import react from "@vitejs/plugin-react-swc";
import { resolve } from "path";
import { defineConfig, loadEnv } from "vite";

// https://vitejs.dev/config/

export default ({ mode } = {}) => {
  // Use '' as a third argument to loadEnv to load env variables that do not
  // start with VITE_
  process.env = { ...process.env, ...loadEnv(mode, process.cwd()) };

  return defineConfig({
    root: resolve(__dirname, `src`),
    publicDir: resolve(__dirname, `public`),
    server: {
      port: globalThis.parseInt(process.env.VITE_PORT),
    },
    plugins: [react()],
    build: {
      outDir: resolve(__dirname, `dist/${process.env.VITE_CLIENT}`),
      // See - https://vitejs.dev/config/build-options.html#build-emptyoutdir
      emptyOutDir: true,
    },
    resolve: {
      alias: {
        "~lib": resolve(__dirname, `src/lib`),
        "@": resolve(__dirname, "./src"),
      },
    },
  });
};
