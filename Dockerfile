FROM --platform=linux/amd64 node:18-alpine as builder
ARG client
# Set the working directory to /app inside the container
WORKDIR /app
# Install dependencies (npm ci makes sure the exact versions in the lockfile gets installed)
# RUN npm ci
COPY package*.json ./
RUN npm install
# Build the app
COPY . .
RUN npm run build:staging -- ${client:-portal}

# Bundle static assets with nginx
FROM  nginx:1.21.0-alpine as production
ARG client
ENV NODE_ENV production
# Copy built assets from `builder` image
COPY --from=builder /app/dist/${client:-provider} /usr/share/nginx/html
# Add your nginx.conf
COPY nginx.conf /etc/nginx/conf.d/default.conf
# Expose port
EXPOSE 80
# Start nginx
CMD ["nginx", "-g", "daemon off;"]
