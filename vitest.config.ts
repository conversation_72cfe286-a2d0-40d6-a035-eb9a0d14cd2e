import { resolve } from "path";
import { defineConfig, mergeConfig } from "vitest/config";
import viteConfig from "./vite.config";

export default mergeConfig(
  viteConfig(),
  defineConfig({
    test: {
      environment: "jsdom",
      globals: true,
      setupFiles: ["./testSetup.ts"],
      coverage: {
        enabled: true,
        reporter: ["html"],
      },
    },
    resolve: {
      alias: {
        "@payer": resolve(__dirname, "src/payer"),
        "~lib": resolve(__dirname, "src/lib"),
      },
    },
  }),
);
