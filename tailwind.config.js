//! ⚠️ WARNING: DO NOT REORDER IMPORTS
//* `@tailwindcss/forms` MUST be the **last plugin imported and listed**.
//* It provides essential default styling for form elements — if it's loaded too early,
//* or moved by auto-import sorters (e.g. <PERSON><PERSON><PERSON>, IDE on-save), inputs/selects/textareas
//* will lose their base styles and look broken globally.
//*
//* Tip: Disable import reordering in this file or lock its order to avoid silent issues.

import colors from "tailwindcss/colors";
import plugin from "tailwindcss/plugin";
// ---
import forms from "@tailwindcss/forms";

export default {
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  safelist: [
    {
      pattern: /(pl|pr)-(12|16|20)/,
      variants: ["first", "last"],
    },
  ],
  theme: {
    extend: {
      colors: {
        customBlue: "#0A315E",
        darkBlue: "#1A2853",
        lightBlue: "#449FDA",
        midBlue: "#11437C",
        btnBlue: "#2563EB",
        faintBlue: "#EFF6FF",
        skyBlue: "#93C5FD",
        txtBlue: "#1D4ED8",
        customRed: "#EF4444",
        darkRed: "#B91C1C",
        lightRed: "#F87171",
        faintRed: "#FEF2F2",
        customGreen: "#22C55E",
        darkGreen: "#15803D",
        faintGreen: "#F0FDF4",
        goldenYellow: "#EAB308",
        faintYellow: "#FEFCE8",
        customGray: "#61758A",
        darkGray: "#304254",
        lightGray: "#6B7280",
        midGray: "#374151",
        goldenBrown: "#A16207",
        gray: colors.slate,
        "light-blue": colors.sky,
        red: colors.rose,
        green: colors.emerald,
        yellow: colors.amber,
        purple: colors.violet,
        blue: {
          550: "#449FDA",
          900: "#172554",
          935: "#003D71",
          950: "#172554",
        },
        // From figma
        "gray-lightest": "#F9FAFB",
        "gray-lighter": "#E5E7EB",
        "gray-light": "#6B7280",
        "gray-default": "#4B5563",
        "gray-dark": "#374151",
        "gray-darkest": "#030712",
        success: "#16A34A",
        error: "#DC2626",
        primary: "#2563EB",
        "primary-lightest": "#EFF6FF",
      },
      outline: {
        blue: "2px solid rgba(0, 112, 244, 0.5)",
      },
      minWidth: {
        36: "9rem",
        44: "11rem",
        56: "14rem",
        60: "15rem",
        72: "18rem",
        80: "20rem",
      },
      maxWidth: {
        "8xl": "88rem",
        "9xl": "96rem",
      },
      zIndex: {
        60: "60",
      },
      boxShadow: {
        custom:
          "0 -5px 6px rgba(175, 175, 175, 0.125), 0 5px 6px rgba(175, 175, 175, 0.125), -5px 0 6px rgba(175, 175, 175, 0.125), 5px 0 6px rgba(175, 175, 175, 0.125)",
      },
    },
  },
  plugins: [
    forms,
    // Add custom variant for expanding sidebar
    plugin(({ addVariant, e }) => {
      addVariant("sidebar-expanded", ({ modifySelectors, separator }) => {
        modifySelectors(
          ({ className }) => `.sidebar-expanded .${e(`sidebar-expanded${separator}${className}`)}`,
        );
      });
      // Add custom variant for checked button
      addVariant("label-checked", ({ modifySelectors, separator }) => {
        modifySelectors(({ className }) => {
          const eClassName = e(`label-checked${separator}${className}`); // escape class
          const yourSelector = 'input[type="radio"]'; // your input selector. Could be any
          return `${yourSelector}:checked ~ .${eClassName}`; // ~ - CSS selector for siblings
        });
      });
      // Add custom variant for checked button background
      addVariant("label-background", ({ modifySelectors, separator }) => {
        modifySelectors(({ className }) => {
          const eClassName = e(`label-background${separator}${className}`); // escape class
          const yourSelector = 'input[type="radio"]'; // your input selector. Could be any
          return `${yourSelector}:checked ~ .${eClassName}`; // ~ - CSS selector for siblings
        });
      });
    }),
  ],
};
