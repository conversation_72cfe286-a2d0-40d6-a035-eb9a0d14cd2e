import {
  setCheckedClaimsIds,
  setIsBatchClaimsModalOpen,
  setIsSelectAllActive,
  setSelectedClaimsSum,
} from "../features/claims/claimsBatchingSlice";
import { useAppDispatch } from "../store/hooks";

export default function useResetBatchingState() {
  const dispatch = useAppDispatch();
  return () => {
    dispatch(setCheckedClaimsIds([]));
    dispatch(setSelectedClaimsSum(0));
    dispatch(setIsBatchClaimsModalOpen(false));
    dispatch(setIsSelectAllActive(false));
  };
}
