import {
  setCatalogIds,
  setSelectedAccount,
  setSelectedAccountName,
  setSelectedAgeBandId,
  setSelectedAgeBandName,
  setSelectedBenefitId,
  setSelectedBenefitName,
  setSelectedRegion,
  setSelectedScheme,
  setSelectedSchemeIds,
  setSelectedSchemeName,
} from "../features/claims/voucheringSlice";
import { useAppDispatch } from "../store/hooks";

export const useClearFilters = () => {
  const dispatch = useAppDispatch();

  return () => {
    dispatch(setSelectedAgeBandId(0));
    dispatch(setSelectedBenefitId(0));
    dispatch(setSelectedScheme(""));
    dispatch(setSelectedAccount(""));
    dispatch(setSelectedSchemeIds([]));
    dispatch(setSelectedRegion(""));
    dispatch(setCatalogIds([]));
    dispatch(setSelectedAccount(""));
    dispatch(setSelectedSchemeName(""));
    dispatch(setSelectedBenefitName(""));
    dispatch(setSelectedAgeBandName(""));
    dispatch(setSelectedAccountName(""));
  };
};
