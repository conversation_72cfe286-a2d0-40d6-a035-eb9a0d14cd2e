import {
  setUserAddedRoles,
  setUserRemovedRoles,
} from "../features/access-control/accessControlSlice";
import { useAppDispatch } from "../store/hooks";

export default function useClearAccessControlState() {
  const dispatch = useAppDispatch();
  const clearAccessControlState = () => {
    dispatch(setUserAddedRoles([]));
    dispatch(setUserRemovedRoles([]));
  };

  return clearAccessControlState;
}
