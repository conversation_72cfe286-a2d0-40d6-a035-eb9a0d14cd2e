import { useMemo } from "react";
import { useUserEffectivePermissions } from "../../utils/user-management-utils";
import { RequestType } from "~lib/api/types";

export function usePreAuthorizationPermissions() {
  const { hasPermission } = useUserEffectivePermissions();

  // Approve permissions
  const hasApproveAllPermission = hasPermission(["CARE_PREAUTH_APPROVE_ALL_ROLE"]);
  const hasApproveDentalPermission = hasPermission(["CARE_PREAUTH_APPROVE_DENTAL_ROLE"]);
  const hasApproveOpticalPermission = hasPermission(["CARE_PREAUTH_APPROVE_OPTICAL_ROLE"]);
  const hasApproveOutpatientPermission = hasPermission(["CARE_PREAUTH_APPROVE_OUTPATIENT_ROLE"]);
  const hasApproveInpatientPermission = hasPermission(["CARE_PREAUTH_APPROVE_INPATIENT_ROLE"]);
  const hasApproveMaternityPermission = hasPermission(["CARE_PREAUTH_APPROVE_MATERNITY_ROLE"]);

  // TopUp permissions
  const hasTopUpAllPermission = hasPermission(["CARE_PREAUTH_TOPUP_ALL_ROLE"]);
  const hasTopUpDentalPermission = hasPermission(["CARE_PREAUTH_TOPUP_DENTAL_ROLE"]);
  const hasTopUpOpticalPermission = hasPermission(["CARE_PREAUTH_TOPUP_OPTICAL_ROLE"]);
  const hasTopUpOutpatientPermission = hasPermission(["CARE_PREAUTH_TOPUP_OUTPATIENT_ROLE"]);
  const hasTopUpInpatientPermission = hasPermission(["CARE_PREAUTH_TOPUP_INPATIENT_ROLE"]);
  const hasTopUpMaternityPermission = hasPermission(["CARE_PREAUTH_TOPUP_MATERNITY_ROLE"]);

  // View permissions
  const hasViewDentalPermission = hasPermission(["CARE_PREAUTH_VIEW_DENTAL_ROLE"]);
  const hasViewOpticalPermission = hasPermission(["CARE_PREAUTH_VIEW_OPTICAL_ROLE"]);
  const hasViewOutpatientPermission = hasPermission(["CARE_PREAUTH_VIEW_OUTPATIENT_ROLE"]);
  const hasViewInpatientPermission = hasPermission(["CARE_PREAUTH_VIEW_INPATIENT_ROLE"]);
  const hasViewMaternityPermission = hasPermission(["CARE_PREAUTH_VIEW_MATERNITY_ROLE"]);

  // Computed view permissions
  const hasViewAllPermission =
    hasViewDentalPermission &&
    hasViewOpticalPermission &&
    hasViewOutpatientPermission &&
    hasViewInpatientPermission &&
    hasViewMaternityPermission;

  const hasViewPermissionForRequestType = (requestType: RequestType | string): boolean => {
    if (hasViewAllPermission) return true;

    switch (requestType) {
      case RequestType.DENTAL:
        return hasViewDentalPermission;
      case RequestType.OPTICAL:
        return hasViewOpticalPermission;
      case RequestType.OUTPATIENT:
        return hasViewOutpatientPermission;
      case RequestType.INPATIENT:
        return hasViewInpatientPermission;
      case RequestType.MATERNITY:
        return hasViewMaternityPermission;
      default:
        return false;
    }
  };

  const hasApprovePermissionForRequestType = (requestType: RequestType | string): boolean => {
    if (hasApproveAllPermission) return true;

    switch (requestType) {
      case RequestType.DENTAL:
        return hasApproveDentalPermission;
      case RequestType.OPTICAL:
        return hasApproveOpticalPermission;
      case RequestType.OUTPATIENT:
        return hasApproveOutpatientPermission;
      case RequestType.INPATIENT:
        return hasApproveInpatientPermission;
      case RequestType.MATERNITY:
        return hasApproveMaternityPermission;
      default:
        return false;
    }
  };

  const hasTopUpPermissionForRequestType = (requestType: RequestType | string): boolean => {
    if (hasTopUpAllPermission) return true;

    switch (requestType) {
      case RequestType.DENTAL:
        return hasTopUpDentalPermission;
      case RequestType.OPTICAL:
        return hasTopUpOpticalPermission;
      case RequestType.OUTPATIENT:
        return hasTopUpOutpatientPermission;
      case RequestType.INPATIENT:
        return hasTopUpInpatientPermission;
      case RequestType.MATERNITY:
        return hasTopUpMaternityPermission;
      default:
        return false;
    }
  };

  // Computed properties for filtering and UI logic
  const allowedBenefitTypes = useMemo(() => {
    const types = [];
    if (hasViewDentalPermission) types.push(RequestType.DENTAL);
    if (hasViewOpticalPermission) types.push(RequestType.OPTICAL);
    if (hasViewOutpatientPermission) types.push(RequestType.OUTPATIENT);
    if (hasViewInpatientPermission) types.push(RequestType.INPATIENT);
    if (hasViewMaternityPermission) types.push(RequestType.MATERNITY);
    return types;
  }, [
    hasViewDentalPermission,
    hasViewOpticalPermission,
    hasViewOutpatientPermission,
    hasViewInpatientPermission,
    hasViewMaternityPermission,
  ]);

  const singleAllowedBenefitType = useMemo(() => {
    if (hasViewAllPermission) return null;
    return allowedBenefitTypes.length === 1 ? allowedBenefitTypes[0] : null;
  }, [hasViewAllPermission, allowedBenefitTypes]);

  return {
    // Individual permissions
    hasApproveAllPermission,
    hasApproveDentalPermission,
    hasApproveOpticalPermission,
    hasApproveOutpatientPermission,
    hasApproveInpatientPermission,
    hasApproveMaternityPermission,
    hasTopUpAllPermission,
    hasTopUpDentalPermission,
    hasTopUpOpticalPermission,
    hasTopUpOutpatientPermission,
    hasTopUpInpatientPermission,
    hasTopUpMaternityPermission,
    hasViewAllPermission,
    hasViewDentalPermission,
    hasViewOpticalPermission,
    hasViewOutpatientPermission,
    hasViewInpatientPermission,
    hasViewMaternityPermission,

    // Permission checker functions
    hasViewPermissionForRequestType,
    hasApprovePermissionForRequestType,
    hasTopUpPermissionForRequestType,

    // Computed properties
    allowedBenefitTypes,
    singleAllowedBenefitType,
  };
}
