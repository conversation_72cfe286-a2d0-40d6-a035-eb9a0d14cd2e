import {
  setSelectedOption,
  setSearchTerm,
  setHighlightedIndex,
} from "../features/filters/filterSelectSlice";
import { useAppDispatch } from "../store/hooks";

export function useHandleFilterSelectClear(id: string) {
  const dispatch = useAppDispatch();

  return () => {
    dispatch(setSelectedOption({ id, selectedOption: null }));
    dispatch(setSearchTerm({ id, searchTerm: "" }));
    dispatch(setHighlightedIndex({ id, index: -1 }));
  };
}
