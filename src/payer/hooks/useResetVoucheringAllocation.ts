import {
  setSelectedAllocationMethod,
  setSelectedAllocationSelectionMethod,
  setIsPartialAllocation,
  setAmountNotAllocated,
} from "../features/claims/voucheringAllocationSlice";
import { setAllocatedAmount } from "../features/claims/voucheringSlice";
import { useAppDispatch } from "../store/hooks";

export default function useResetVoucheringAllocation() {
  const dispatch = useAppDispatch();
  return () => {
    dispatch(setSelectedAllocationMethod(null));
    dispatch(setSelectedAllocationSelectionMethod(null));
    dispatch(setIsPartialAllocation(false));
    dispatch(setAmountNotAllocated(true));
    dispatch(setAllocatedAmount(0));
  };
}
