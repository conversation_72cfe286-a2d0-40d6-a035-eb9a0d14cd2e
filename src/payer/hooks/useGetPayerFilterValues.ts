import { useEffect, useMemo, useState } from "react";
import { useGetAllAgeBandsQuery } from "../api/claims-vetting-api/claimVettingApi";
import {
  useGetMembershipBenefitsCatalogQuery,
  useGetMembershipProvidersQuery,
  useGetMembershipRegionsQuery,
  useGetMembershipSchemesQuery,
} from "../api/features/membershipApi";
import { MembershipBenefitCatalog } from "../lib/types/membership/memberBenefit";
import { MembershipProvider } from "../lib/types/membership/memberProvider";
import { MembershipRegion } from "../lib/types/membership/memberRegion";
import { MembershipScheme } from "../lib/types/membership/memberScheme";
import { AgeBand } from "../pages/claims/claim-vetting/types";
import UserService from "../services/UserService";
import { useAppSelector } from "../store/hooks";

export enum FilterValueType {
  PROVIDER = "provider",
  REGION = "region",
  SCHEME = "scheme",
  BENEFIT = "benefit",
  AGE_BAND = "ageBand",
}

type FilterValues =
  | AgeBand[]
  | MembershipBenefitCatalog[]
  | MembershipProvider[]
  | MembershipRegion[]
  | MembershipScheme[];

type FiltersMap = Record<FilterValueType, FilterValues>;

export default function useGetPayerFilterValues(filterValue: FilterValueType): FilterValues {
  const payerId = useMemo(() => UserService.getPayer()?.tokenParsed?.["payerId"], []);
  const providersSearchQuery = useAppSelector((state) => state.claimsBatching.providersSearchQuery);

  const [filterValues, setFilterValues] = useState<FiltersMap>({
    [FilterValueType.PROVIDER]: [],
    [FilterValueType.REGION]: [],
    [FilterValueType.SCHEME]: [],
    [FilterValueType.BENEFIT]: [],
    [FilterValueType.AGE_BAND]: [],
  });

  const { data: membershipRegionsResponse, isError: isRegionsError } =
    useGetMembershipRegionsQuery(payerId);
  const { data: membershipBenefitsResponse, isError: isBenefitsError } =
    useGetMembershipBenefitsCatalogQuery(payerId);
  const { data: membershipSchemesResponse, isError: isSchemesError } =
    useGetMembershipSchemesQuery(payerId);
  const { data: ageBandsResponse, isError: isAgeBandsError } = useGetAllAgeBandsQuery();
  const providersQuery = useMemo(
    () => ({
      payerId,
      ...(providersSearchQuery.length > 0 && { query: providersSearchQuery }),
    }),
    [payerId, providersSearchQuery],
  );
  const { data: membershipProvidersResponse, isError: isProvidersError } =
    useGetMembershipProvidersQuery(providersQuery);

  useEffect(() => {
    setFilterValues((prev) => ({
      ...prev,
      [FilterValueType.REGION]: membershipRegionsResponse?.data || [],
      [FilterValueType.PROVIDER]: membershipProvidersResponse?.data?.content || [],
      [FilterValueType.BENEFIT]: membershipBenefitsResponse?.data || [],
      [FilterValueType.AGE_BAND]: ageBandsResponse?.data || [],
      [FilterValueType.SCHEME]: membershipSchemesResponse?.data || [],
    }));
  }, [
    membershipRegionsResponse,
    membershipProvidersResponse,
    membershipBenefitsResponse,
    ageBandsResponse,
    membershipSchemesResponse,
  ]);

  useEffect(() => {
    if (
      isRegionsError ||
      isBenefitsError ||
      isSchemesError ||
      isAgeBandsError ||
      isProvidersError
    ) {
      console.error("Failed to fetch some filter values");
    }
  }, [isRegionsError, isBenefitsError, isSchemesError, isAgeBandsError, isProvidersError]);

  return filterValues[filterValue] || [];
}
