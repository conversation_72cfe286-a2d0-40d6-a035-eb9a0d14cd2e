import Auth from "~lib/utils/auth";
import { store } from "../store";
import { configure as updateAxiosToken } from "./HttpService";

const KC_CLIENT_ID = "lctpayerportalclient";

export const auth = new Auth({
  clientId: KC_CLIENT_ID,
  store: store,
  onTokenUpdated: () => {
    updateAxiosToken();
  },
  requiredAttributes: ["payerId"],
});

export const keycloak = auth.keycloak;

const _kc = keycloak;

const doLogin = _kc.login;
const doLogout = _kc.logout;

const kcObject = _kc;
const getToken = () => _kc.token;
const getSubject = () => _kc.subject;
const isLoggedIn = () => !!_kc.token;
const getUsername = () => _kc.tokenParsed?.["preferred_username"] as string;
const getPayer = () => _kc;

const hasRole = (roles: string[]) => roles.some((role) => _kc.hasRealmRole(role));

const UserService = {
  doLogin,
  doLogout,
  isLoggedIn,
  getToken,
  getUsername,
  hasRole,
  getSubject,
  kcObject,
  getPayer,
};

export default UserService;
