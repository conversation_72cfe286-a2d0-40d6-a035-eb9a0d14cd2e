import axios from "axios";
import { store } from "../store";

const HttpMethods = {
  GET: "GET",
  POST: "POST",
  DELETE: "DELETE",
};

const _axios = axios.create();

export const configure = () => {
  _axios.interceptors.request.use((config) => {
    const { isAuthenticated: _isAuthenticated, token: _token } = store.getState().auth

    /** Uncomment to send auth bearer token */
    // if (authenticated) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }

    return config
  });
};

const getAxiosClient = () => _axios;

const HttpService = {
  HttpMethods,
  configure,
  getAxiosClient,
};

export default HttpService;
