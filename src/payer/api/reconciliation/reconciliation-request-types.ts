export type PostClaimsReconciliationRequest = {
  body: {
    file: File;
  };
  queryParams: {
    payerId: number;
    providerId: number;
    reconciledBy: string;
  };
};

export type PostClaimsReconciliationResponse = {
  success: boolean;
  msg: string;
  data: Reconciliation;
};

export type Reconciliation = {
  id: number;
  payerId: number;
  providerId: number;
  reconciledBy: string;
  createdAt: string;
};

export type GetPayerProvidersRequest = {
  pathParams: {
    payerId: number;
  };
};

export type Result<T> = {
  success: boolean;
  msg: string;
  data: Page<T>;
  results: number;
};

type Page<T> = {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
};

export type Provider = {
  id: number;
  name: string;
};

export type GetReconciledInvoicesRequest = {
  queryParams: {
    reconciliationId: number;
    page: number;
    size: number;
  };
};
export type ReconciledInvoice = {
  invoiceNumber: string;
  memberNumber: string;
  memberName?: string;
  providerCode: string;
  providerName: string;
  amount: number;
  invoiceDate: string;
  payableAmount: number;
  invoiceStatus:
    | "REJECTED"
    | "BALANCE_DEDUCTED"
    | "DIAGNOSIS_ADDED"
    | "DOCUMENTS_ADDED"
    | "PENDED"
    | "SENT"
    | "BALANCE_ADDED"
    | "REVERSED"
    | "DISPATCHED"
    | "CANCELLED"
    | "PROFORMA";
  vettingStatus: "PENDING" | "APPROVED" | "DECLINED" | "PARTIAL" | "BATCHED";
  vettingDate: string;
  voucherNumber: string;
  paymentRef: string;
  paymentDate: string;
  paymentStatus: "PAID" | "NOT_PAID";
  narration: string;
};
