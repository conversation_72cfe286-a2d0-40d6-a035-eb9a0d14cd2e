import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { baseUrl } from "../../lib/Utils";
import { createSearchParamsString } from "../../utils/createSearchParamsString";
import {
  GetPayerProvidersRequest,
  GetReconciledInvoicesRequest,
  PostClaimsReconciliationRequest,
  PostClaimsReconciliationResponse,
  Provider,
  ReconciledInvoice,
  Result,
} from "./reconciliation-request-types";

export const reconciliationApi = createApi({
  reducerPath: "reconciliationApi",
  baseQuery: fetchBaseQuery({
    baseUrl: baseUrl,
  }),
  endpoints: (builder) => ({
    postClaimsReconciliation: builder.mutation<
      PostClaimsReconciliationResponse,
      PostClaimsReconciliationRequest
    >({
      query: ({ queryParams, body }) => {
        const formData = new FormData();
        formData.append("file", body.file);
        const searchParams = createSearchParamsString(queryParams);
        return {
          url: `/api/v1/visit/reconcile?${searchParams}`,
          method: "POST",
          body: formData,
        };
      },
    }),

    getReconciledInvoices: builder.query<Result<ReconciledInvoice>, GetReconciledInvoicesRequest>({
      query: ({ queryParams }) => {
        const searchParams = createSearchParamsString(queryParams);
        return `/api/v1/visit/reconciled?${searchParams}`;
      },
    }),

    getPayerProviders: builder.query<Result<Provider>, GetPayerProvidersRequest>({
      query: ({ pathParams }) => `/api/v1/membership/payers/${pathParams.payerId}/providers`,
    }),
  }),
});

export const {
  usePostClaimsReconciliationMutation,
  useGetPayerProvidersQuery,
  useGetReconciledInvoicesQuery,
} = reconciliationApi;
