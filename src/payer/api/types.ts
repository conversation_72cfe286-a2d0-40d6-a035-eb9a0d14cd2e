// TODO: Remove possibly cyclic dependency
import { Fields as ServiceFields } from "~lib/service-fields";

export interface MutationResponse {
  success: boolean;
  msg: string;
  data: boolean;
  results: number;
}

export type ListResponse<T> = {
  success: boolean;
  msg: string | null;
  data: {
    content: Array<T>;
    empty: boolean;
    first: boolean;
    last: boolean;
    number: number;
    numberOfElements: number;
    pageable: {};
    size: number;
    sort: {
      empty: boolean;
      sorted: boolean;
      unsorted: boolean;
    };
    totalElements: number;
    totalPages: number;
  };
  results: any;
};

export interface SimpleListResponse<T> {
  success: boolean;
  msg: any;
  data: T[];
}

export interface SimpleResponse<T> {
  success: boolean;
  msg: string;
  data?: T;
  results?: number;
}

export type MappedProvider = {
  id: number;
  code: string;
  providerId: number;
  providerName: string;
  tier: string;
  region: string;
  country: string;
  mapped: boolean;
};

export interface ChangeLog {
  id: number;
  action: string;
  user: string;
  time: string;
  data: string;
  organisation: string;
  reason: string;
  memberNumber: string;
  type: string;
}

interface Plan {
  id: number;
  name: string;
  type: string;
  accessMode: string;
}

export interface Policy {
  id: number;
  plan: Plan;
  startDate: string;
  endDate: string;
  policyNumber: string;
}

export interface Category {
  id: number;
  name: string;
  description: string;
  agakhanInsuranceCode: string;
  agakhanSchemeCode: string;
  jicSchemeCode: number;
  apaSchemeCode: number;
  policyPayerCode: number;
  policy: Policy;
  status: string;
  allowOtpVerificationFailOver: boolean;
  restrictionType: string;
}

export interface Beneficiary {
  id: number;
  name: string;
  memberNumber: string;
  jicEntityId: number;
  apaEntityId: number;
  nhifNumber: string;
  dob: string;
  gender: string;
  phoneNumber: string;
  email: string;
  beneficiaryType: string;
  category: Category;
  principal: string;
  canUseBiometrics: boolean;
  processed: boolean;
  changeLog: Array<ChangeLog>;
  processedTime: string;
  status: string;
}

export interface BeneficiaryBenefit {
  id: number;
  aggregateId: string;
  benefitId: number;
  beneficiaryId: number;
  memberName: string;
  memberNumber: string;
  benefitName: string;
  status: string;
  balance: number;
  suspensionThreshold: number;
  initialLimit: number;
  categoryId: number;
  payerId: number;
  utilization: number;
  parent?: Omit<BeneficiaryBenefit, "parent">;
  startDate: string;
  endDate: string;
  gender: string;
  memberType: string;
  catalogId: number;
  jicEntityId: any;
  apaEntityId: any;
  benefitType: any;
  capitationType: any;
  capitationPeriod: any;
  capitationMaxVisitCount: number;
  requireBeneficiaryToSelectProvider: any;
  daysOfAdmissionLimit: number;
  amountPerDayLimit: any;
}

export interface Content {
  provider: Provider;
  id: number;
}

export interface Provider {
  id: number;
  name: string;
  latitude: number;
  longitude: number;
  tier: string;
  mainFacility: string;
  region: Region;
  baseUrl: string;
  billingStation: boolean;
  billsOnPortal: boolean;
  billsOnHmis: boolean;
  billsOnDevice: boolean;
  billsOnHmisAutomaticClose: boolean;
  canUseOtpVerificationFailOver: boolean;
  verificationType: string;
  invoiceNumberType: string;
  providerMiddleware: string;
  createdOn: string;
}

export interface Country {
  id: number;
  name: string;
}

export interface Region {
  id: number;
  name: string;
  country: Country;
}

export interface Benefit {
  id: number;
  name: string;
  benefitRef: BenefitRef;
  applicableGender: string;
  applicableMember: string;
  limit: number;
  suspensionThreshold: number;
  preAuthType: string;
  sharing: string;
  coPaymentRequired: boolean;
  coPaymentAmount: number;
  parentBenefit: string;
  waitingPeriod: string;
  processed: boolean;
  processedTime: string;
  payer: Payer;
  benefitType: string;
  capitationType: string;
  capitationPeriod: string;
  capitationMaxVisitCount: number;
  requireBeneficiaryToSelectProvider: boolean;
  daysOfAdmissionLimit: number;
  amountPerDayLimit: number;
  restriction: Restriction;
}

export interface BenefitRef {
  id: number;
  code: string;
  name: string;
  serviceGroup: string;
}

export interface Payer {
  id: number;
  name: string;
  contact: string;
  type: string;
}

export interface Restriction {
  id: number;
  name: string;
  payer: Payer;
  restrictionType: string;
  active: string;
  createDate: string;
}

export interface BeneficiaryBenefitProvider {
  provider: Provider;
  id: number;
  benefit: Benefit;
}

export interface PreAuth {
  id: number;
  time: string;
  status: string;
  requestAmount: number;
  authorizedAmount: number;
  aggregateId: string;
  benefitId: number;
  payerId: number;
  providerId: number;
  requester: string;
  authorizer: string;
  notes: string;
  authorizationNotes: string;
  validity: Validity;
  visitNumber: number;
  memberNumber: string;
  memberName: string;
  benefitName: string;
  service: string;
  requestType: string;
  reference: string;
  diagnosis: string;
  medProcedure: string;
  schemeName: string;
  payerName: string;
  utilization: number;
  createdAt: string;
  updatedAt: string;
}

export interface Validity {
  years: number;
  months: number;
  days: number;
  negative: boolean;
  zero: boolean;
  chronology: Chronology;
  units: Unit[];
}

export interface Chronology {
  calendarType: string;
  id: string;
}

export interface Unit {
  dateBased: boolean;
  timeBased: boolean;
  duration: Duration;
  durationEstimated: boolean;
}

export interface Duration {
  seconds: number;
  nano: number;
  negative: boolean;
  zero: boolean;
}

export interface Visit {
  id: number;
  memberNumber: string;
  memberName: string;
  hospitalProviderId: number; // TODO: Rename to providerId
  staffId: string;
  staffName: string;
  aggregateID: string;
  totalInvoiceAmount: number;
  status: string;
  claimProcessStatus: string;
  invoiceLines: InvoiceLine[];
  diagnosis: Diagnosis[];
  beneficiaryType: string;
  benefitName: string;
  categoryId: string;
  payerId: string;
  payerName: string;
  policyNumber: string;
  beneficiaryId: number;
  benefitId: number;
  visitType: string;
  offSystemReason: string;
  reimbursementProvider: string;
  reimbursementInvoiceDate: string;
  reimbursementReason: string;
  balanceAmount: number;
  invoiceNumber: string;
  aggregateId: string;
  preAuth: VisitPreAuth;
  invoices: Invoice[];
  scheme: Scheme;
  createdAt: string;
}

export interface InvoiceLine {
  id: number;
  lineTotal: number;
  description: string;
  invoiceNumber: string;
  quantity: number;
  unitPrice: number;
  lineType: string;
  claimRef: string;
  lineCategory: string;
  invoice: Invoice;
}

export interface Invoice {
  id: number;
  hospitalProviderId: number;
  invoiceNumber: string;
  service: string;
  totalAmount: number;
  claimRef: string;
  status: string;
  visit: InvoiceVisit;
}

export interface InvoiceVisit {
  id: number;
  memberNumber: string;
  memberName: string;
  hospitalProviderId: number;
  staffId: string;
  staffName: string;
  aggregateId: string;
  categoryId: string;
  benefitName: string;
  beneficiaryId: number;
  benefitId: number;
  payerId: string;
  policyNumber: string;
  balanceAmount: number;
  beneficiaryType: string;
  totalInvoiceAmount: number;
  providerMiddleware: string;
  invoiceNumber: string;
  status: string;
  middlewareStatus: string;
  claimProcessStatus: string;
  cancelReason: string;
  createdAt: string;
  updatedAt: string;
  visitEnd: string;
  diagnosis: Diagnosis[];
  visitType: string;
  offSystemReason: string;
  reimbursementProvider: string;
  reimbursementInvoiceDate: string;
  reimbursementReason: string;
  payerStatus: string;
  facilityType: string;
  providerMapping: string;
  benefitMapping: string;
  payerClaimReference: string;
  invoiceDate: string;
}

export interface Diagnosis {
  id: number;
  code: string;
  title: string;
  invoiceNumber: string;
  claimRef: string;
}

/**
 * TODO: Verify type
 */
export interface VisitPreAuth {
  id: number;
  status: string;
  requestAmount: number;
  authorizedAmount: number;
  memberNumber: string;
  memberName: string;
  benefitName: string;
  service: string;
  reference: string;
}

export interface Scheme {
  id: string;
  name: string;
}

// ICD10
export interface Diagnosis {
  id: number;
  code: string;
  title: string;
}

export interface Procedure {
  id: number;
  procedure_code: string;
  procedure_description: string;
}

export enum Service {
  DENTAL = "DENTAL",
  OPTICAL = "OPTICAL",
  MRI_SCAN = "MRI_SCAN",
  SURGERY = "SURGERY",
}

export enum PreAuthStatus {
  INACTIVE = "INACTIVE",
  AUTHORIZED = "AUTHORIZED",
  DECLINED = "DECLINED",
  CLAIMED = "CLAIMED",
  CANCELLED = "CANCELLED",
  PENDING = "PENDING",
  DRAFT = "DRAFT",
}

export interface PreauthBase extends ServiceFields {
  draft: boolean;
  visitNumber: number;
  service: string;
  requestAmount: number;
  requester: string; // Staff username
  notes: string;
  requestType: string;
  prediagnosisCodes: string[];
  supportingDocuments: string[];
}

/**
 * TODO: Merge with PreAuth type
 */
export interface PreAuthEnhanced extends PreauthBase {
  id: number;
  time: string;
  status: PreAuthStatus;
  schemeName: string;
  payerName: string;
  providerName: string;
  diagnosisInfo: Diagnosis[]; // Derived from prediagnosisCodes
  authorizedAmount: number;
  authorizer: string;
  authorizationNotes: string;
  reference: string;
  validity: Validity;
  utilization: number;
  createdAt: string;
  updatedAt: string;
  visit: Visit;
}

export type GetCoverPeriodsResponse = {
  id: number;
  name: string;
  memberNumber: string;
  jicEntityId: number;
  apaEntityId: number;
  nhifNumber: string;
  dob: string;
  gender: string;
  phoneNumber: string;
  email: string;
  beneficiaryType: string;
  category: Category;
  principal: string;
  canUseBiometrics: boolean;
  processed: boolean;
  changeLog: Array<ChangeLog>;
  processedTime: string;
  status: string;
};

export enum RequestType {
  DENTAL = "DENTAL",
  OPTICAL = "OPTICAL",
  OUTPATIENT = "OUTPATIENT",
  INPATIENT = "INPATIENT",
  MATERNITY = "MATERNITY",
}

export type BenefitProvidersResponse = ListResponse<Content>;
export type BeneficiaryBenefitsResponse = SimpleListResponse<BeneficiaryBenefit>;
export type CountryResponse = SimpleListResponse<Country>;
export type RegionResponse = ListResponse<Region>;
export type BeneficiaryBenefitProviderResponse = SimpleResponse<BeneficiaryBenefitProvider[]>;
export type PreAuthsResponse = ListResponse<PreAuthEnhanced>;
export type SearchPreAuthsResponse = SimpleListResponse<PreAuth>;
export type VisitsResponse = SimpleListResponse<Visit>;
export type PrediagnosisResponse = ListResponse<Diagnosis>;
export type PayerResponse = SimpleListResponse<Payer>;
export type ProcedureResponse = ListResponse<Procedure>;
export type FileUploadResponse = SimpleResponse<string>;
export type NewPreAuthResponse = SimpleResponse<PreAuthEnhanced>;
export type DeletePreAuthResponse = SimpleResponse<undefined>;
export type PreAuthResponse = SimpleResponse<PreAuthEnhanced>;
export type VisitResponse = SimpleResponse<Visit>;
export type DownloadFileResponse = SimpleResponse<string>;
export type ApprovePreAuthResponse = SimpleResponse<PreAuthEnhanced>;
export type DeclinePreAuthResponse = SimpleResponse<PreAuthEnhanced>;
export type PlainPreAuthsResponse = SimpleListResponse<PreAuthEnhanced>;
export type ProvidersResponse = ListResponse<Provider>;
export type CoverResponse = SimpleListResponse<GetCoverPeriodsResponse>;

export type AuditLogsQueryParams = {
  benefitBeneficiaryIds?: number[]; // Array of integers
  preAuthId?: number; // Integer (int64)
  invoiceId?: number; // Integer (int64)
  invoiceBatchId?: number; // Integer (int64)
  action?:
    | "BENEFIT_TOP_UP"
    | "BENEFIT_TRANSFER"
    | "BENEFIT_STATUS_UPDATE"
    | "PRE_AUTHORIZATION_UPDATE"
    | "INVOICE_REVERSAL"
    | "CLAIM_VETTING"
    | "INVOICE_UPDATE"
    | "INVOICE_BATCH_CREATED"
    | "INVOICE_BATCH_ALLOCATED"
    | "CREATED"
    | "UPDATED"
    | "DELETED"; // Enum values
  eventName?: string;
  sortColumn?: string; // Default: "id"
  sortOrder?: "asc" | "desc"; // Default: "desc"
  page?: number; // Integer (int32), Default: 1
  size?: number; // Integer (int32), Default: 50
};

type Sort = {
  empty: boolean;
  unsorted: boolean;
  sorted: boolean;
};

type PageableObject = {
  sort: Sort;
  offset: number;
  paged: boolean;
  unpaged: boolean;
  pageNumber: number;
  pageSize: number;
};

export type AuditLog = {
  id: number;
  action:
    | "BENEFIT_TOP_UP"
    | "BENEFIT_TRANSFER"
    | "BENEFIT_STATUS_UPDATE"
    | "PRE_AUTHORIZATION_UPDATE"
    | "INVOICE_REVERSAL"
    | "CLAIM_VETTING"
    | "INVOICE_UPDATE"
    | "INVOICE_BATCH_CREATED"
    | "INVOICE_BATCH_ALLOCATED"
    | "CREATED"
    | "UPDATED"
    | "DELETED";
  eventName?: string;
  previousValues?: string;
  newValues?: string;
  actionByUser: string;
  reason?: string;
  narration?: string;
  createdOn: string; // Assuming ISO date format (e.g., 2024-03-18T12:00:00Z)
};

type PageAuditLog = {
  totalPages: number;
  totalElements: number;
  number: number;
  sort: Sort;
  size: number;
  content: AuditLog[];
  numberOfElements: number;
  pageable: PageableObject;
  first: boolean;
  last: boolean;
  empty: boolean;
};

export type ResultPageAuditLog = {
  success: boolean;
  msg: string;
  data: PageAuditLog;
};
