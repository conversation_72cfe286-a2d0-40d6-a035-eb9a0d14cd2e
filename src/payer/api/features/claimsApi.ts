import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { baseUrl } from "~lib/constants";
import {
  APIResponseWithContent,
  GeneralResponseBoolean,
  GeneralResponseGeneric,
} from "../../lib/types/apiResponseGeneric";
import { Preauthorization, PreAuthSearchParams } from "../../lib/types/care/preAuth";
import { BatchAuditLogsResponse } from "../../lib/types/claims/auditLogs";
import {
  BatchDeleteResponse,
  BatchUpdateResponse,
  GetBatchResponse,
  UpdateBatchPayload,
} from "../../lib/types/claims/batch";
import {
  BatchedInvoices,
  BatchInvoicesResponse,
  GetBatchedClaimsParams,
} from "../../lib/types/claims/batchedInvoices";
import { BatchedInvoicesListResponse } from "../../lib/types/claims/batchedInvoicesList";
import { ClaimsUser } from "../../lib/types/claims/claimsUser";
import {
  AssignBatchToPayerPayload,
  CalculateInvoicesAmountPayload,
  CalculateInvoicesAmountResponse,
  ClaimsResponse,
  GetAllClaimsParams,
  ReverseInvoicePayload,
  VoucherClaimsPayload,
  VoucherClaimsResponse,
  VoucherEditingPayload,
} from "../../lib/types/claims/invoice";
import {
  DeleteVoucherPayload,
  GetAllVouchersParams,
  VoucherResponse,
  VouchersResponse,
} from "../../lib/types/vouchering/voucher";
import { createSearchParamsString } from "../../utils/createSearchParamsString";
import { isValidBatchAllocationStatus, isValidInvoiceBatchStatus } from "../../utils/utils";
import { AgeBand, AgeBandType } from "../../lib/types/claims/ageBand";
import {
  ClaimAdjudication,
  ClaimAdjudicationSearchParams,
} from "../../lib/types/claims/adjudication";

type MembershipUsersResponse = APIResponseWithContent<ClaimsUser>;

interface AgeBandsResponse {
  success: boolean;
  msg: null | string;
  data: AgeBand[];
}

export const claimsApi = createApi({
  reducerPath: "claimsApi",
  tagTypes: [
    "BatchedInvoices",
    "Claims",
    "BatchedInvoice",
    "Vouchers",
    "PreAuths",
    "AdjudicationClaims",
    "VoucherAmount",
    "InvoiceLines",
    "Reversal",
  ],
  baseQuery: fetchBaseQuery({ baseUrl: `${baseUrl}/api/v1/` }),
  endpoints: (builder) => ({
    getAllClaims: builder.query<ClaimsResponse, GetAllClaimsParams>({
      query: (params) => {
        const searchParams = createSearchParamsString(params);
        return `visit/invoices/search?${searchParams}`;
      },
      providesTags: ["Claims", "VoucherAmount"],
    }),
    getBatchedInvoices: builder.query<BatchedInvoicesListResponse, GetBatchedClaimsParams>({
      query: ({
        payerId,
        batchAllocationStatus,
        invoiceBatchStatus,
        pageNumber = 1,
        pageSize = 10,
        sortOrder = "asc",
      }) => {
        let url = `visit/batches/list/assigned?payerId=${payerId}&page=${pageNumber}&size=${pageSize}&sortOrder=${sortOrder}`;
        if (isValidBatchAllocationStatus(batchAllocationStatus)) {
          url += `&batchAllocationStatus=${batchAllocationStatus}`;
        }
        if (isValidInvoiceBatchStatus(invoiceBatchStatus)) {
          url += `&invoiceBatchStatus=${invoiceBatchStatus}`;
        }
        return url;
      },
      providesTags: ["BatchedInvoices"],
    }),
    getAllAgeBands: builder.query<AgeBandsResponse, { bandType?: AgeBandType }>({
      query: (params) => {
        const searchParams = createSearchParamsString(params);
        return `visit/ageBands?${searchParams}`;
      },
    }),
    batchInvoices: builder.mutation<BatchInvoicesResponse, BatchedInvoices>({
      query: (batchedInvoices) => ({
        url: `visit/batch/invoices`,
        method: "POST",
        body: batchedInvoices,
      }),
    }),
    assignBatchToPayers: builder.mutation({
      query: (payload: AssignBatchToPayerPayload) => ({
        url: `visit/batch/assign`,
        method: "POST",
        body: payload,
      }),
    }),
    getBatchById: builder.query<GetBatchResponse, string | number>({
      query: (batchId) => `visit/batch/${batchId}`,
      providesTags: (result, error, batchId) => [{ type: "BatchedInvoice", id: batchId }],
    }),
    updateBatch: builder.mutation<BatchUpdateResponse, UpdateBatchPayload>({
      query: (payload) => ({
        url: `visit/batch`,
        method: "PUT",
        body: payload,
      }),
      invalidatesTags: ["BatchedInvoice"],
    }),
    deleteBatch: builder.mutation<BatchDeleteResponse, string | number>({
      query: (batchId) => ({
        url: `visit/batch/${batchId}`,
        method: "DELETE",
      }),
      invalidatesTags: ["BatchedInvoices"],
    }),
    getBachAuditLogs: builder.query<BatchAuditLogsResponse, number>({
      query: (invoiceBatchId) => `visit/audit/logs?invoiceBatchId=${invoiceBatchId}`,
    }),
    getClaimsUsers: builder.query<
      MembershipUsersResponse,
      { payerId: string; size?: number; page?: number }
    >({
      query: ({ payerId, size = 20, page = 1 }) =>
        `visit/batch/users/${payerId}?size=${size}&page=${page}`,
    }),
    voucherVettedClaims: builder.mutation<VoucherClaimsResponse, VoucherClaimsPayload>({
      query: (payload) => ({
        url: `visit/payment/voucher`,
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Claims", "VoucherAmount", "Vouchers"],
    }),
    calculateInvoicesAmount: builder.mutation<
      CalculateInvoicesAmountResponse,
      CalculateInvoicesAmountPayload
    >({
      query: (payload) => ({
        url: `visit/batch/invoices/calculate/totalPayable`,
        method: "POST",
        body: payload,
      }),
    }),
    getAllVouchers: builder.query<VouchersResponse, GetAllVouchersParams>({
      query: ({ payerId, interimSave, voucherIds, page = 1, size = 10 }) => {
        let url = `visit/payment/voucher/${payerId}?page=${page}&size=${size}`;
        if (interimSave && interimSave !== null) {
          url += `&interimSave=${interimSave}`;
        }
        if (voucherIds && voucherIds.length > 0) {
          for (const id of voucherIds) {
            url += `&voucherId=${id}`;
          }
        }
        return url;
      },
      providesTags: ["Vouchers"],
    }),
    getVoucherById: builder.query<GeneralResponseGeneric<VoucherResponse>, number>({
      query: (voucherId) => `visit/payment/voucher?voucherId=${voucherId}`,
      providesTags: (result, error, voucherId) => ["Vouchers", { type: "Vouchers", id: voucherId }],
    }),
    removeVoucherInvoices: builder.mutation<GeneralResponseBoolean, VoucherEditingPayload>({
      query: (payload) => ({
        url: `visit/payment/voucher/${payload.voucherId}/removeInvoices`,
        method: "PUT",
        body: payload.body,
      }),
      invalidatesTags: (result, error, payload) => [
        "Vouchers",
        "Claims",
        "VoucherAmount",
        { type: "Vouchers", id: payload.voucherId },
      ],
    }),
    addVoucherInvoices: builder.mutation<GeneralResponseBoolean, VoucherEditingPayload>({
      query: (payload) => ({
        url: `visit/payment/voucher/${payload.voucherId}/addInvoices`,
        method: "PUT",
        body: payload.body,
      }),
      invalidatesTags: (result, error, payload) => [
        "Vouchers",
        "Claims",
        "VoucherAmount",
        {
          type: "Vouchers",
          id: payload.voucherId,
        },
      ],
    }),
    deleteVoucher: builder.mutation<GeneralResponseBoolean, DeleteVoucherPayload>({
      query: (payload) => ({
        url: `visit/payment/voucher/${payload.voucherId}`,
        method: "DELETE",
        body: payload.body,
      }),
      invalidatesTags: ["Vouchers", "Claims", "VoucherAmount"],
    }),
    getPreAuths: builder.query<APIResponseWithContent<Preauthorization>, PreAuthSearchParams>({
      query: (params) => {
        const searchParams = createSearchParamsString(params);
        return `preauthorization/search?${searchParams}`;
      },
      providesTags: ["PreAuths"],
    }),
    getAdjudicationClaims: builder.query<
      APIResponseWithContent<ClaimAdjudication>,
      ClaimAdjudicationSearchParams
    >({
      query: (params) => {
        const searchParams = createSearchParamsString(params);
        return `claim/adjudication/search?${searchParams}`;
      },
      providesTags: ["AdjudicationClaims"],
    }),
    reverseInvoice: builder.mutation<GeneralResponseBoolean, ReverseInvoicePayload>({
      query: (payload) => ({
        url: `visit/invoice/reversal`,
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Claims", "Reversal"],
    }),
  }),
});

export const {
  useGetAllClaimsQuery,
  useLazyGetAllClaimsQuery,
  useGetBatchedInvoicesQuery,
  useGetAllAgeBandsQuery,
  useBatchInvoicesMutation,
  useAssignBatchToPayersMutation,
  useGetBatchByIdQuery,
  useUpdateBatchMutation,
  useDeleteBatchMutation,
  useGetBachAuditLogsQuery,
  useGetClaimsUsersQuery,
  useVoucherVettedClaimsMutation,
  useCalculateInvoicesAmountMutation,
  useGetAllVouchersQuery,
  useRemoveVoucherInvoicesMutation,
  useAddVoucherInvoicesMutation,
  useDeleteVoucherMutation,
  useGetPreAuthsQuery,
  useGetAdjudicationClaimsQuery,
  useLazyGetAdjudicationClaimsQuery,
  useGetVoucherByIdQuery,
  useReverseInvoiceMutation,
} = claimsApi;
