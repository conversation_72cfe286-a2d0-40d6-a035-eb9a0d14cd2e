import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface GroupRoleToRemove {
  id?: number;
  name?: string;
}

interface UserGroupState {
  showRemoveRoleConfirm: boolean;
  roleToRemove: GroupRoleToRemove | null;
  isRemovingPredefinedRole: boolean;
  isRemovingCustomRole: boolean;
}

const initialState: UserGroupState = {
  showRemoveRoleConfirm: false,
  roleToRemove: null,
  isRemovingPredefinedRole: false,
  isRemovingCustomRole: false,
};

export const userGroupsSlice = createSlice({
  name: "userGroups",
  initialState,
  reducers: {
    setShowRemoveRoleConfirm(state, action: PayloadAction<boolean>) {
      state.showRemoveRoleConfirm = action.payload;
    },
    setRoleToRemove(state, action: PayloadAction<GroupRoleToRemove | null>) {
      state.roleToRemove = action.payload;
    },
    setIsRemovingPredefinedRole(state, action: PayloadAction<boolean>) {
      state.isRemovingPredefinedRole = action.payload;
    },
    setIsRemovingCustomRole(state, action: PayloadAction<boolean>) {
      state.isRemovingCustomRole = action.payload;
    },
    resetGroupRolesState(state) {
      state.showRemoveRoleConfirm = false;
      state.roleToRemove = null;
      state.isRemovingPredefinedRole = false;
      state.isRemovingCustomRole = false;
    },
  },
});

export const {
  setShowRemoveRoleConfirm,
  setRoleToRemove,
  setIsRemovingPredefinedRole,
  setIsRemovingCustomRole,
  resetGroupRolesState,
} = userGroupsSlice.actions;

export default userGroupsSlice.reducer;
