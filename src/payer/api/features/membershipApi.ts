import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { baseUrl } from "~lib/constants";
import {
  AddUsersToGroupRequest,
  AssignCustomRoleToUserRequest,
  CreatePayerUserRequest,
  CreateUserGroupRequest,
  CustomRoleRequest,
  CustomRoleResponse,
  CustomRoleUpdateRequest,
  DenyPolicyDto,
  DenyPolicyRequest,
  GetCustomRolesQuery,
  GetCustomRoleUsersQuery,
  GetDenyPolicyUsersQuery,
  GetPayerDenyPoliciesQuery,
  GetUserGroupMembersQuery,
  GetUserGroupsQuery,
  PermissionResponse,
  RemoveCustomRoleFromUserRequest,
  RemoveUsersFromGroupRequest,
  RoleManagementDeletionResponse,
  RolePresentation,
  UpdateDenyPolicyRequest,
  UpdateGroupRolesRequest,
  UpdateUserGroupRequest,
  UserBasicDto,
  UserDirectRolesResponse,
  UserEffectiveRolesResponse,
  UserGroupMemberDto,
  UserGroupResponse,
  UserStandaloneRolesResponse,
} from "../../lib/types/access-control/role";
import {
  AddPayerRequest,
  GetPayerUserAuditLogsQuery,
  GetPayerUsersQuery,
  PasswordResetRequest,
  PasswordResetResponse,
  PayerUserUpdateRequest,
  UpdateUserPayload,
  UserAuditLog,
  UserRepresentation,
  UserStatusUpdateRequest,
} from "../../lib/types/access-control/user";
import {
  APIResponseWithContent,
  APIResponseWithoutContent,
  CustomPageableResponse,
} from "../../lib/types/apiResponseGeneric";
import { MembershipAccountsResponse } from "../../lib/types/membership/memberAccount";
import {
  MembershipBenefit,
  MembershipBenefitCatalog,
} from "../../lib/types/membership/memberBenefit";
import {
  MembershipProvider,
  MembershipProvidersQuery,
} from "../../lib/types/membership/memberProvider";
import { MembershipRegion } from "../../lib/types/membership/memberRegion";
import { MembershipSchemesResponse } from "../../lib/types/membership/memberScheme";
import { createSearchParamsString } from "../../utils/createSearchParamsString";
import { isValidId } from "../../utils/utils";

type MembershipRegionsResponse = APIResponseWithoutContent<MembershipRegion>;

type MembershipProvidersResponse = APIResponseWithContent<MembershipProvider>;

type MembershipBenefitsCatalogResponse = APIResponseWithoutContent<MembershipBenefitCatalog>;

export const membershipApi = createApi({
  reducerPath: "membershipApi",
  tagTypes: [
    "UserRoles",
    "PayerUsers",
    "PayerUsersAuditLogs",
    "UserGroups",
    "UserGroup",
    "CustomRoles",
    "CustomRole",
    "DenyPolicies",
    "DenyPolicy",
  ],
  baseQuery: fetchBaseQuery({ baseUrl: `${baseUrl}/api/v1/` }),
  endpoints: (builder) => ({
    getMembershipRegions: builder.query<MembershipRegionsResponse, string>({
      query: (payerId) => `membership/payer/regions/${payerId}`,
    }),
    getMembershipProviders: builder.query<MembershipProvidersResponse, MembershipProvidersQuery>({
      query: ({
        payerId,
        page = 1,
        size = 50,
        countryId,
        regionId,
        tier,
        mainFacilityId,
        providerType,
        query,
      }) => {
        let url = `membership/payers/${payerId}/providers?page=${page}&size=${size}`;
        if (isValidId(countryId as string)) {
          url += `&countryId=${countryId}`;
        }
        if (isValidId(regionId as string)) {
          url += `&regionId=${regionId}`;
        }
        if (tier) {
          url += `&tier=${tier}`;
        }
        if (isValidId(mainFacilityId as string)) {
          url += `&mainFacilityId=${mainFacilityId}`;
        }
        if (providerType) {
          url += `&providerType=${providerType}`;
        }
        if (query) {
          url += `&query=${query}`;
        }
        return url;
      },
    }),
    getMembershipBenefitsCatalog: builder.query<MembershipBenefitsCatalogResponse, string>({
      query: (payerId) => `membership/payer/${payerId}/benefits/catalog`,
    }),
    getMembershipSchemes: builder.query<MembershipSchemesResponse, string>({
      query: (payerId: string) => `membership/payer/${payerId}/plans`,
    }),
    getMembershipAccounts: builder.query<MembershipAccountsResponse, string>({
      query: (payerId: string) => `membership/payer/${payerId}/providers/accounts`,
    }),
    getMembershipBenefits: builder.query<APIResponseWithoutContent<MembershipBenefit>, string>({
      query: (payerId: string) => `membership/payer/${payerId}/benefits`,
    }),
    updateUserRoles: builder.mutation<void, UpdateUserPayload>({
      query: (payload) => ({
        url: `membership/user/updateRoles?userId=${payload.userId}`,
        method: "POST",
        body: payload.payload,
      }),
      invalidatesTags: (result, error, { userId }) => [
        "UserRoles",
        "CustomRoles",
        "UserGroups",
        { type: "PayerUsers", id: "LIST" },
      ],
    }),
    getRealmRoles: builder.query<Array<RolePresentation>, string>({
      query: () => `membership/users/roles/realm`,
    }),
    getUserById: builder.query<UserRepresentation, string>({
      query: (userId) => `/membership/user/${userId}`,
      providesTags: (result, error, userId) => [{ type: "PayerUsers", id: userId }],
    }),
    getEffectiveUserRealmRoles: builder.query<Array<RolePresentation>, string>({
      query: (userId) => `/membership/user/roles/realm/${userId}`,
      providesTags: ["UserRoles"],
    }),
    getPayerUsers: builder.query<CustomPageableResponse<UserRepresentation>, GetPayerUsersQuery>({
      query: (params) => {
        const searchParams = createSearchParamsString(params);
        return `/membership/users/payer/paginated/${params.payerId}?${searchParams}`;
      },
      providesTags: (result) =>
        result?.content
          ? [
              { type: "PayerUsers", id: "LIST" },
              ...result.content.map(({ id }) => ({ type: "PayerUsers" as const, id })),
            ]
          : [{ type: "PayerUsers", id: "LIST" }],
    }),
    getPayerUsersAuditLogs: builder.query<
      CustomPageableResponse<UserAuditLog>,
      GetPayerUserAuditLogsQuery
    >({
      query: (params) =>
        `/membership/users/roles/auditLogs/user/${params.userId}?page=${params.page}&size=${params.size}`,
      providesTags: ["PayerUsersAuditLogs"],
    }),
    addPayerSuperAdmin: builder.mutation<void, AddPayerRequest>({
      query: (payload) => ({
        url: `/membership/user/addPayerAdmin`,
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["PayerUsers", "UserRoles", { type: "PayerUsers", id: "LIST" }],
    }),
    updateUserStatus: builder.mutation<void, UserStatusUpdateRequest>({
      query: (payload) => ({
        url: `/membership/users/payer/status`,
        method: "PUT",
        body: payload,
      }),
      invalidatesTags: ["PayerUsers", "UserRoles", { type: "PayerUsers", id: "LIST" }],
    }),
    updateUserDetails: builder.mutation<void, PayerUserUpdateRequest>({
      query: (payload) => ({
        url: `/membership/users/payer/update`,
        method: "PUT",
        body: payload,
      }),
      invalidatesTags: ["PayerUsers", "UserRoles", { type: "PayerUsers", id: "LIST" }],
    }),
    addUserPayerWithOptionalRoles: builder.mutation<void, CreatePayerUserRequest>({
      query: (payload) => ({
        url: `/membership/roles/users/payer`,
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["PayerUsers", "UserRoles", { type: "PayerUsers", id: "LIST" }],
    }),
    createUserGroup: builder.mutation<void, CreateUserGroupRequest>({
      query: (payload) => ({
        url: `/membership/roles/groups`,
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["UserGroups", "UserRoles", { type: "UserGroups", id: "LIST" }],
    }),
    addUsersToGroup: builder.mutation<void, AddUsersToGroupRequest>({
      query: (payload) => ({
        url: `/membership/roles/groups/${payload.groupId}/members`,
        method: "POST",
        body: payload.body,
      }),
      invalidatesTags: (result, error, { groupId }) => [
        "UserGroups",
        "UserRoles",
        "PayerUsers",
        { type: "UserGroup", id: groupId },
      ],
    }),
    createDenyPolicy: builder.mutation<void, DenyPolicyRequest>({
      query: (payload) => ({
        url: `/membership/roles/deny-policies`,
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["DenyPolicies", "UserRoles", { type: "DenyPolicies", id: "LIST" }],
    }),
    createCustomRole: builder.mutation<void, CustomRoleRequest>({
      query: (payload) => ({
        url: `/membership/roles/custom`,
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["CustomRoles", "UserRoles", { type: "CustomRoles", id: "LIST" }],
    }),
    updateGroupRoles: builder.mutation<void, UpdateGroupRolesRequest>({
      query: (payload) => ({
        url: `/membership/roles/groups/${payload.id}/roles?payerId=${payload.payerId}`,
        method: "PUT",
        body: payload.body,
      }),
      invalidatesTags: (result, error, { id }) => [
        "UserGroups",
        "UserRoles",
        { type: "UserGroup", id },
      ],
    }),
    updateDenyPolicy: builder.mutation<void, UpdateDenyPolicyRequest>({
      query: (payload) => ({
        url: `/membership/roles/deny-policies/${payload.id}`,
        method: "PUT",
        body: payload.body,
      }),
      invalidatesTags: (_, __, { id, body }) => [
        "DenyPolicies",
        "UserRoles",
        { type: "DenyPolicy", id },
        ...(body?.addUsers?.map((userId) => ({ type: "UserRoles" as const, id: userId })) || []),
        ...(body?.addUsers?.map((userId) => ({ type: "PayerUsers" as const, id: userId })) || []),
        ...(body?.removeUsers?.map((userId) => ({ type: "UserRoles" as const, id: userId })) || []),
        ...(body?.removeUsers?.map((userId) => ({ type: "PayerUsers" as const, id: userId })) ||
          []),
      ],
    }),
    updateCustomRole: builder.mutation<void, CustomRoleUpdateRequest>({
      query: (payload) => ({
        url: `/membership/roles/custom/${payload.id}?payerId=${payload.payerId}`,
        method: "PUT",
        body: payload.body,
      }),
      invalidatesTags: (result, error, { id, body }) => [
        "CustomRoles",
        "UserRoles",
        { type: "CustomRole", id },
        ...(body?.usersToAdd?.map((userId) => ({ type: "UserRoles" as const, id: userId })) || []),
        ...(body?.usersToAdd?.map((userId) => ({ type: "PayerUsers" as const, id: userId })) || []),
        ...(body?.usersToRemove?.map((userId) => ({ type: "UserRoles" as const, id: userId })) ||
          []),
        ...(body?.usersToRemove?.map((userId) => ({ type: "PayerUsers" as const, id: userId })) ||
          []),
      ],
    }),
    deleteDenyPolicy: builder.mutation<
      RoleManagementDeletionResponse,
      { id: number; payerId: number }
    >({
      query: ({ id, payerId }) => ({
        url: `/membership/roles/deny-policies/${id}?payerId=${payerId}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, { id }) => ["DenyPolicies", "UserRoles"],
    }),
    deleteCustomRole: builder.mutation<
      RoleManagementDeletionResponse,
      {
        id: number;
        payerId: number;
        deletedBy: string;
      }
    >({
      query: ({ id, deletedBy, payerId }) => ({
        url: `/membership/roles/custom/${id}?deletedBy=${deletedBy}&payerId=${payerId}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, { id }) => ["CustomRoles", "UserRoles"],
    }),
    removeUsersFromGroup: builder.mutation<
      RoleManagementDeletionResponse,
      RemoveUsersFromGroupRequest
    >({
      query: (payload) => ({
        url: `/membership/roles/groups/${payload.groupId}/members`,
        method: "DELETE",
        body: payload.body,
      }),
      invalidatesTags: (_, __, { groupId, body }) => [
        "UserGroups",
        "UserRoles",
        "PayerUsers",
        { type: "UserGroup", id: groupId },
        ...(body?.userIds?.map((userId) => ({ type: "UserRoles" as const, id: userId })) || []),
        ...(body?.userIds?.map((userId) => ({ type: "PayerUsers" as const, id: userId })) || []),
        // Invalidate specific user groups query
        ...(body?.userIds?.map((userId) => ({ type: "UserGroups" as const, id: userId })) || []),
      ],
    }),
    deleteUserGroup: builder.mutation<
      RoleManagementDeletionResponse,
      {
        id: number;
        deletedBy: string;
        payerId: number;
      }
    >({
      query: ({ id, deletedBy, payerId }) => ({
        url: `/membership/roles/groups/${id}?deletedBy=${deletedBy}&payerId=${payerId}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, { id }) => ["UserGroups", "UserRoles"],
    }),
    getCustomRoleById: builder.query<CustomRoleResponse, { id: number; payerId: number }>({
      query: ({ id, payerId }) => `/membership/roles/custom/${id}?payerId=${payerId}`,
      providesTags: (result, error, { id }) => [{ type: "CustomRole", id }],
    }),
    getUserGroups: builder.query<CustomPageableResponse<UserGroupResponse>, GetUserGroupsQuery>({
      query: (params) => {
        const searchParams = createSearchParamsString(params);
        return `/membership/roles/groups?${searchParams}`;
      },
      providesTags: (result) =>
        result?.content
          ? [
              { type: "UserGroups", id: "LIST" },
              ...result.content.map(({ id }) => ({ type: "UserGroup" as const, id })),
            ]
          : [{ type: "UserGroups", id: "LIST" }],
    }),
    getUserGroupById: builder.query<UserGroupResponse, { id: number; payerId: number }>({
      query: ({ id, payerId }) => `/membership/roles/groups/${id}?payerId=${payerId}`,
      providesTags: (result, error, { id }) => [{ type: "UserGroup", id }],
    }),
    getUserGroupMembers: builder.query<
      CustomPageableResponse<UserGroupMemberDto>,
      GetUserGroupMembersQuery
    >({
      query: ({ groupId, page = 1, size = 10 }) =>
        `/membership/roles/groups/${groupId}/members?page=${page}&size=${size}`,
      providesTags: (result, error, { groupId }) => [
        { type: "UserGroup", id: groupId },
        "UserGroups",
      ],
    }),
    getCustomRoles: builder.query<CustomPageableResponse<CustomRoleResponse>, GetCustomRolesQuery>({
      query: (params) => {
        const searchParams = createSearchParamsString(params);
        return `/membership/roles/custom?${searchParams}`;
      },
      providesTags: (result) =>
        result?.content
          ? [
              { type: "CustomRoles", id: "LIST" },
              ...result.content.map(({ id }) => ({ type: "CustomRole" as const, id })),
            ]
          : [{ type: "CustomRoles", id: "LIST" }],
    }),
    getUserStandaloneRoles: builder.query<UserStandaloneRolesResponse, string>({
      query: (userId) => `/membership/roles/users/${userId}/standalone-roles`,
    }),
    getUserGroupsByUserId: builder.query<Array<UserGroupResponse>, string>({
      query: (userId) => `/membership/roles/users/${userId}/groups`,
      providesTags: (_, __, userId) => [
        { type: "UserGroups", id: userId },
        { type: "UserRoles", id: userId },
      ],
    }),
    getUserEffectiveRoles: builder.query<UserEffectiveRolesResponse, string>({
      query: (userId) => `/membership/roles/users/${userId}/effective-roles`,
    }),
    getAllPermissions: builder.query<Array<PermissionResponse>, void>({
      query: () => `/membership/roles/permissions`,
    }),
    requestPasswordReset: builder.mutation<PasswordResetResponse, PasswordResetRequest>({
      query: (payload) => ({
        url: `/membership/users/password/reset`,
        method: "POST",
        body: payload,
      }),
    }),
    getUserDenyPolicies: builder.query<Array<DenyPolicyDto>, string>({
      query: (userId) => `/membership/roles/deny-policies/user/${userId}`,
      providesTags: (_, __, userId) => [
        { type: "DenyPolicies", id: userId },
        { type: "UserRoles", id: userId },
      ],
    }),
    getDenyPolicyUsers: builder.query<
      CustomPageableResponse<UserBasicDto>,
      GetDenyPolicyUsersQuery
    >({
      query: ({ id, params }) => {
        const searchParams = createSearchParamsString(params);
        return `/membership/roles/deny-policies/${id}/users?${searchParams}`;
      },
      providesTags: (result, error, { id }) => [{ type: "DenyPolicy", id }, "DenyPolicies"],
    }),
    getDenyPoliciesByPayerId: builder.query<
      CustomPageableResponse<DenyPolicyDto>,
      GetPayerDenyPoliciesQuery
    >({
      query: (params) => {
        const searchParams = createSearchParamsString(params);
        return `/membership/roles/deny-policies?${searchParams}`;
      },
      providesTags: (result) =>
        result?.content
          ? [
              { type: "DenyPolicies", id: "LIST" },
              ...result.content.map(({ id }) => ({ type: "DenyPolicy" as const, id })),
            ]
          : [{ type: "DenyPolicies", id: "LIST" }],
    }),
    getDenyPolicyById: builder.query<DenyPolicyDto, { id: number; payerId: number }>({
      query: ({ id, payerId }) => `/membership/roles/deny-policies/${id}?payerId=${payerId}`,
      providesTags: (_, __, { id }) => [{ type: "DenyPolicy", id }],
    }),
    getUserDirectRoles: builder.query<UserDirectRolesResponse, { userId: string; payerId: number }>(
      {
        query: ({ userId, payerId }) =>
          `/membership/roles/users/${userId}/direct-roles?payerId=${payerId}`,
        providesTags: (_, __, { userId }) => [
          { type: "UserRoles", id: userId },
          { type: "PayerUsers", id: userId },
        ],
      },
    ),
    updateUserGroup: builder.mutation<void, UpdateUserGroupRequest>({
      query: (payload) => ({
        url: `/membership/roles/groups/${payload.id}?payerId=${payload.payerId}`,
        method: "PUT",
        body: payload.body,
      }),
      invalidatesTags: (result, error, { id }) => [
        "UserGroups",
        "UserRoles",
        { type: "UserGroup", id },
      ],
    }),
    assignCustomRoleToUser: builder.mutation<void, AssignCustomRoleToUserRequest>({
      query: (payload) => ({
        url: `/membership/roles/users/${payload.userId}/custom-roles/${payload.roleId}?assignedBy=${payload.assignedBy}`,
        method: "POST",
      }),
      invalidatesTags: (result, error, { userId, roleId }) => [
        "UserRoles",
        { type: "PayerUsers", id: userId },
        { type: "CustomRole", id: roleId },
        "CustomRoles",
      ],
    }),
    removeCustomRoleFromUser: builder.mutation<void, RemoveCustomRoleFromUserRequest>({
      query: (payload) => ({
        url: `/membership/roles/users/${payload.userId}/custom-roles/${payload.roleId}?actionedBy=${payload.actionedBy}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, { userId, roleId }) => [
        "UserRoles",
        { type: "PayerUsers", id: userId },
        { type: "CustomRole", id: roleId },
        "CustomRoles",
      ],
    }),
    getUserAssignedCustomRoles: builder.query<Array<CustomRoleResponse>, string>({
      query: (userId) => `/membership/roles/users/${userId}/custom-roles`,
      providesTags: (result, error, userId) => [
        { type: "PayerUsers", id: userId },
        { type: "UserRoles", id: userId },
      ],
    }),
    makeUserPayerSuperAdmin: builder.mutation<void, { userId: string; actionedBy: string }>({
      query: ({ userId, actionedBy }) => ({
        url: `/membership/users/${userId}/makePayerSuperAdmin?actionedBy=${actionedBy}`,
        method: "POST",
      }),
      invalidatesTags: (result, error, { userId }) => [
        "UserRoles",
        "PayerUsers",
        "UserGroups",
        "DenyPolicies",
        "CustomRoles",
        "PayerUsersAuditLogs",
        { type: "PayerUsers", id: userId },
        { type: "PayerUsers", id: "LIST" },
        { type: "UserRoles", id: userId },
        { type: "UserGroups", id: userId },
        { type: "DenyPolicies", id: userId },
      ],
    }),
    getCustomRoleUsers: builder.query<
      CustomPageableResponse<UserBasicDto>,
      GetCustomRoleUsersQuery
    >({
      query: (query) => {
        const searchParams = createSearchParamsString(query.params);
        return `/membership/roles/custom/${query.id}/users?${searchParams}`;
      },
      providesTags: (result, error, { id }) => [{ type: "CustomRole", id }, "CustomRoles"],
    }),
    resetPassword: builder.mutation<void, string>({
      query: (username) => ({
        url: `/membership/user/resetPassword/${username}`,
        method: "POST",
      }),
    }),
  }),
});

export const {
  useGetMembershipRegionsQuery,
  useGetMembershipProvidersQuery,
  useGetMembershipBenefitsCatalogQuery,
  useGetMembershipAccountsQuery,
  useGetMembershipSchemesQuery,
  useGetMembershipBenefitsQuery,
  useUpdateUserRolesMutation,
  useGetRealmRolesQuery,
  useGetUserByIdQuery,
  useGetEffectiveUserRealmRolesQuery,
  useGetPayerUsersQuery,
  useGetPayerUsersAuditLogsQuery,
  useAddPayerSuperAdminMutation,
  useUpdateUserStatusMutation,
  useUpdateUserDetailsMutation,
  useAddUserPayerWithOptionalRolesMutation,
  useAddUsersToGroupMutation,
  useCreateUserGroupMutation,
  useCreateDenyPolicyMutation,
  useCreateCustomRoleMutation,
  useUpdateDenyPolicyMutation,
  useUpdateGroupRolesMutation,
  useUpdateCustomRoleMutation,
  useDeleteDenyPolicyMutation,
  useDeleteCustomRoleMutation,
  useRemoveUsersFromGroupMutation,
  useDeleteUserGroupMutation,
  useGetCustomRoleByIdQuery,
  useGetUserGroupsQuery,
  useGetUserGroupByIdQuery,
  useGetUserGroupMembersQuery,
  useGetCustomRolesQuery,
  useGetUserStandaloneRolesQuery,
  useGetUserGroupsByUserIdQuery,
  useGetUserEffectiveRolesQuery,
  useGetAllPermissionsQuery,
  useRequestPasswordResetMutation,
  useGetUserDenyPoliciesQuery,
  useGetDenyPolicyUsersQuery,
  useGetDenyPoliciesByPayerIdQuery,
  useGetDenyPolicyByIdQuery,
  useGetUserDirectRolesQuery,
  useUpdateUserGroupMutation,
  useAssignCustomRoleToUserMutation,
  useRemoveCustomRoleFromUserMutation,
  useGetUserAssignedCustomRolesQuery,
  useMakeUserPayerSuperAdminMutation,
  useGetCustomRoleUsersQuery,
  useResetPasswordMutation,
} = membershipApi;
