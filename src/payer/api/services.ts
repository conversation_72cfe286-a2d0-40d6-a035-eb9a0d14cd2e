import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { baseUrl } from "../lib/Utils";
import {
  ApprovePreAuthResponse,
  AuditLogsQueryParams,
  Country,
  CountryResponse,
  DeclinePreAuthResponse,
  DeletePreAuthResponse,
  Diagnosis,
  DownloadFileResponse,
  NewPreAuthResponse,
  Payer,
  PayerResponse,
  PlainPreAuthsResponse,
  PreauthBase,
  PreAuthEnhanced,
  PreAuthResponse,
  PreAuthsResponse,
  PreAuthStatus,
  PrediagnosisResponse,
  Procedure,
  ProcedureResponse,
  Provider,
  ProvidersResponse,
  Region,
  RegionResponse,
  RequestType,
  ResultPageAuditLog,
  Visit,
  VisitResponse,
  VisitsResponse,
} from "./types";

import { getInvoicesByVisitId } from "../store/members/actions";
import { createSearchParamsString } from "../utils/createSearchParamsString";

export const api = createApi({
  reducerPath: "api",
  baseQuery: fetchBaseQuery({ baseUrl: baseUrl }),
  tagTypes: ["PreAuths", "PendingPreAuths", "SearchPreAuths"],
  endpoints: (builder) => ({
    // Returns a list of countries
    getCountries: builder.query<Country[], Record<string, never>>({
      query: () => `/api/v1/country/`, // WARN: Trailing slash is required
      transformResponse: (response: CountryResponse) => response.data,
    }),

    // Returns a list of regions
    getRegions: builder.query<Region[], { countryId: number }>({
      query: ({ countryId }) => {
        const url = `/api/v1/country/${countryId}/region/`;

        const urlSearchParams = new URLSearchParams();
        urlSearchParams.append("page", "1");
        urlSearchParams.append("size", "500"); // WARN: Assumes there are less than 500 regions per country

        return url + "?" + urlSearchParams.toString();
      },
      transformResponse: (response: RegionResponse) => response.data.content,
    }),

    // Returns a list of preauths for a given provider
    getPreauths: builder.query<
      PreAuthsResponse["data"],
      {
        providerId: number;
        page?: number;
        size?: number;
      }
    >({
      query: ({ providerId, page = 1, size = 10 }) => {
        return `/api/v1/preauthorization/${providerId}/provider/?page=${page}&size=${size}`;
      },
      transformResponse: (response: PreAuthsResponse) => response.data,
      providesTags: ["PreAuths"],
    }),

    // Returns a list of active visits for a given provider and staff
    getActiveVisits: builder.query<Visit[], { providerId: number; staffId: string }>({
      query: ({ providerId, staffId }) => `/api/v1/visit/main/${providerId}/${staffId}/active`,
      transformResponse: (response: VisitsResponse) => response.data,
    }),

    // Get a visit - GET /api/v1/visit/{id}/visit
    getVisit: builder.query<Visit | undefined, { id: number }>({
      query: ({ id }) => `/api/v1/visit/${id}/visit`,
      transformResponse: (response: VisitResponse) => response.data,
    }),

    // Search for a prediagnosis by title - /visit/searchICD10ByTitle/icd10code?title=ba&page=1&size=50
    searchPrediagnosis: builder.query<Diagnosis[], { query: string }>({
      query: ({ query }) =>
        `/api/v1/visit/searchICD10ByTitle/icd10code?title=${query}&page=1&size=50`,
      transformResponse: (response: PrediagnosisResponse) => response.data.content,
    }),

    // Search for a procedure by title - /visit/searchMedicalProcedure
    searchProcedure: builder.query<Procedure[], { query: string }>({
      query: ({ query }) => `/api/v1/visit/searchMedicalProcedure?title=${query}&page=1&size=50`,
      transformResponse: (response: ProcedureResponse) => response.data.content,
    }),

    // Get payers
    getPayers: builder.query<Payer[], Record<string, never>>({
      query: () => `/api/v1/membership/payers/`, // WARN: Trailing slash is required
      transformResponse: (response: PayerResponse) => response.data,
    }),

    // Adds a new preauthorization - /preauthorization/new
    addPreauth: builder.mutation<NewPreAuthResponse, PreauthBase>({
      query: (body) => ({
        url: `/api/v1/preauthorization/new`,
        method: "POST",
        body,
      }),
      // Fetch the new list of preauths after a delay
      onQueryStarted: async (data, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        setTimeout(() => {
          dispatch(api.util.invalidateTags(["PreAuths"]));
        }, 1000);
      },
      // transformResponse: (response: NewPreAuthResponse) => response.data,
      // invalidatesTags: ["PreAuths"],
    }),

    // Deletes a preauthorization - PUT /api/v1/preauthorization/cancel/{id} - { "reason": "string" }
    deletePreauth: builder.mutation<DeletePreAuthResponse, { id: number; reason: string }>({
      query: ({ id, reason }) => ({
        url: `/api/v1/preauthorization/cancel/${id}`,
        method: "PUT",
        body: { reason },
      }),
      // Fetch the new list of preauths after a delay
      onQueryStarted: async (data, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        setTimeout(() => {
          dispatch(api.util.invalidateTags(["PreAuths"]));
        }, 1000);
      },
    }),

    // Searches a preauthorization - GET /api/v1/preauthorization/search?search=XJDC8024&page=1&size=10
    searchPreauth: builder.query<
      PreAuthsResponse["data"],
      {
        page: number;
        size: number;
        requestType?: RequestType;
        providerId?: number;
        query?: string;
        payerId?: number;
        startDate?: string;
        endDate?: string;
        status?: PreAuthStatus;
      }
    >({
      query: ({
        query,
        providerId,
        payerId,
        requestType,
        status,
        startDate,
        endDate,
        page = 1,
        size = 10,
      }) => {
        const queryParams = new URLSearchParams();

        queryParams.append("page", page.toString());
        queryParams.append("size", size.toString());

        if (providerId) queryParams.append("providerId", providerId.toString());
        if (query) queryParams.append("search", query);
        if (payerId) queryParams.append("payerId", payerId.toString());
        if (startDate) queryParams.append("startDate", startDate);
        if (endDate) queryParams.append("endDate", endDate);
        if (status) queryParams.append("status", status);
        if (requestType) queryParams.append("requestType", requestType);

        return `/api/v1/preauthorization/search?${queryParams.toString()}`;
      },
      transformResponse: (response: PreAuthsResponse) => response.data,
      providesTags: ["SearchPreAuths"],
    }),

    // Counts pending preauthorizations - GET /api/v1/preauthorization/search
    countPendingPreAuths: builder.query<
      number | undefined,
      { payerId?: number; providerId?: number }
    >({
      query: ({ payerId, providerId }) => ({
        url: `/api/v1/preauthorization/search`,
        params: {
          page: 1,
          size: 10,
          ...(providerId && { providerId }),
          ...(payerId && { payerId }),
          status: [PreAuthStatus.PENDING, PreAuthStatus.DRAFT],
        },
      }),
      transformResponse: (response: PreAuthsResponse) => response.data?.totalElements,
      providesTags: ["SearchPreAuths"],
    }),

    // Gets a preauthorization - GET /api/v1/preauthorization/id/{id}/preauth
    getPreauth: builder.query<PreAuthEnhanced, { id: number }>({
      query: ({ id }) => `/api/v1/preauthorization/id/${id}/preauth`,
      transformResponse: (response: PreAuthResponse) => response.data,
      providesTags: (result, error, { id }) => [{ type: "PreAuths", id }],
    }),

    // Get pending preauthorizations - GET /api/v1/preauthorization/{{payerId}}/pending/payer
    getPendingPreauthsByPayer: builder.query<
      PreAuthEnhanced[],
      {
        payerId: number;
      }
    >({
      query: ({ payerId }) => `/api/v1/preauthorization/${payerId}/pending/payer`,
      transformResponse: (response: PlainPreAuthsResponse) => response.data,
      providesTags: ["PendingPreAuths"],
    }),

    // Get preauthorizations by payer - GET /api/v1/preauthorization/{{payerId}}/payer
    getPreauthsByPayer: builder.query<
      PreAuthEnhanced[],
      {
        payerId: number;
      }
    >({
      query: ({ payerId }) => `/api/v1/preauthorization/${payerId}/payer`,
      transformResponse: (response: PlainPreAuthsResponse) => response.data,
    }),

    // Approve a preauthorization - PUT /api/v1/preauthorization/authorize
    approvePreauth: builder.mutation<
      ApprovePreAuthResponse,
      {
        id: number;
        amount: number;
        notes: string;
        authorizer: string;
      }
    >({
      query: ({ id, amount, authorizer, notes = "" }) => ({
        url: `/api/v1/preauthorization/authorize/${id}`,
        method: "PUT",
        body: { amount, notes, authorizer },
      }),
      onQueryStarted: async (data, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        setTimeout(() => {
          dispatch(api.util.invalidateTags(["SearchPreAuths"]));
        }, 1000);
      },
      invalidatesTags: (result, error, { id }) => [{ type: "PreAuths", id }],
    }),

    // Decline a preauthorization - PUT /api/v1/preauthorization/decline
    declinePreauth: builder.mutation<
      DeclinePreAuthResponse,
      {
        id: number;
        reason: string;
        declinedBy: string;
      }
    >({
      query: ({ id, reason = "", declinedBy }) => ({
        url: `/api/v1/preauthorization/decline/${id}`,
        method: "PUT",
        body: { reason, declinedBy },
      }),
      onQueryStarted: async (data, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        setTimeout(() => {
          dispatch(api.util.invalidateTags(["SearchPreAuths"]));
        }, 1000);
      },
      invalidatesTags: (result, error, { id }) => [{ type: "PreAuths", id }],
    }),

    // Download a file /api/file/download?name={name}
    downloadFile: builder.query<DownloadFileResponse, { name: string }>({
      query: ({ name }) => `/api/file/download?name=${name}`,
    }),

    // Search for a provider by name - GET /api/v1/provider/name
    searchProvider: builder.query<Provider[], { name: string; page?: number; size?: number }>({
      query: ({ name, page = 1, size = 10 }) => {
        const queryParams = new URLSearchParams();

        queryParams.append("page", page.toString());
        queryParams.append("size", size.toString());
        queryParams.append("name", name);

        return `/api/v1/provider/name?${queryParams.toString()}`;
      },
      transformResponse: (response: ProvidersResponse) => response.data.content,
    }),

    //PUT update invoice number
    updateInvoice: builder.mutation({
      query: ({ id, invoiceNumber, reason, username }) => ({
        url: `/api/v1/visit/invoice/${id}`,
        method: "PUT",
        body: { invoiceNumber, reason, updatedBy: username },
      }),
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          // Wait for the mutation to succeed
          await queryFulfilled;
          // Fetch updated invoices
          dispatch(getInvoicesByVisitId(arg.id));
        } catch (error) {
          console.log("Refetch error", error);
        }
      },
    }),
    auditLogs: builder.query<ResultPageAuditLog, AuditLogsQueryParams>({
      query: (params) => {
        const queryParams = createSearchParamsString(params);
        return `/api/v1/visit/audit/logs?${queryParams}`;
      },
    }),
  }),
});

export const {
  useAuditLogsQuery,
  useGetCountriesQuery,
  useGetRegionsQuery,
  useGetPreauthsQuery,
  useGetActiveVisitsQuery,
  useSearchPrediagnosisQuery,
  useLazySearchPrediagnosisQuery,
  useGetPayersQuery,
  useSearchProcedureQuery,
  useLazySearchProcedureQuery,
  useAddPreauthMutation,
  useDeletePreauthMutation,
  useSearchPreauthQuery,
  useGetPreauthQuery,
  useGetVisitQuery,
  useGetPendingPreauthsByPayerQuery,
  useApprovePreauthMutation,
  useDeclinePreauthMutation,
  useLazyDownloadFileQuery,
  useLazySearchProviderQuery,
  useCountPendingPreAuthsQuery,
  useUpdateInvoiceMutation,
} = api;
