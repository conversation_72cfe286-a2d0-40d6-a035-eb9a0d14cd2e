import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { baseUrl } from "../../lib/Utils";
import { createSearchParamsString } from "../../utils/createSearchParamsString";
import {
  GetPaymentVouchersRequest,
  GetRemittanceRequest,
  Payment,
  ResultCustomPagingPaymentQueryDto,
  Voucher,
} from "./remittance-api-types";

export const remittanceApi = createApi({
  reducerPath: "remittanceApi",
  baseQuery: fetchBaseQuery({
    baseUrl: baseUrl,
  }),
  endpoints: (builder) => ({
    getRemittance: builder.query<ResultCustomPagingPaymentQueryDto<Payment>, GetRemittanceRequest>({
      query: ({ queryParams }) => {
        const searchParams = createSearchParamsString(queryParams);
        return `/api/v1/visit/payments/search?${searchParams}`;
      },
    }),
    getPaymentVouchers: builder.query<
      ResultCustomPagingPaymentQueryDto<Voucher>,
      GetPaymentVouchersRequest
    >({
      query: ({ pathParams, queryParams }) => {
        const searchParams = createSearchParamsString(queryParams);
        return `/api/v1/visit/payment/voucher/${pathParams.payerId}?${searchParams}`;
      },
    }),
  }),
});

export const { useGetRemittanceQuery, useGetPaymentVouchersQuery } = remittanceApi;
