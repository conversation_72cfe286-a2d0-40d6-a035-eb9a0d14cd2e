export type GetRemittanceRequest = {
  queryParams: {
    payerId: number;
    query: string;
    page: number;
    size: number;
    remittanceSent: true;
  };
};

export type GetPaymentVouchersRequest = {
  pathParams: {
    payerId: number;
  };
  queryParams: {
    paymentIds: number[];
  };
};

export type ResultCustomPagingPaymentQueryDto<T> = {
  success: boolean;
  msg: string;
  data: CustomPagingPaymentQueryDto<T>;
};

export type CustomPagingPaymentQueryDto<T> = {
  content: T[];
  empty: boolean;
  first: boolean;
  last: boolean;
  number: number;
  numberOfElements: number;
  pageable: PageableObject;
  size: number;
  sort: Sort;
  totalElements: number;
  totalPages: number;
};

export type Payment = {
  id: number;
  payerId: number;
  account: PayerProviderBankAccountResolve[];
  paymentReference?: string;
  bankReference?: string;
  chequeNo?: string;
  amount: number;
  modeOfPayment?: "Cheque" | "BankTransfer";
  paid: boolean;
  remittanceSent: boolean;
  bankSchedule?: string;
  paymentDate?: string;
  createdBy: KeycloakUserResolve;
  createdOn: string;
};

export type PayerProviderBankAccountResolve = {
  mappingId: number;
  bankName?: string;
  bankBranch?: string;
  providerId: number;
  payerId: number;
  accountId: number;
  providerName?: string;
  bankCode?: string;
  branchCode?: string;
  accountNumber?: string;
  accountName?: string;
};

export type KeycloakUserResolve = {
  id: string;
  email?: string;
  userName: string;
  firstName?: string;
  lastName?: string;
};

export type PageableObject = {
  sort: Sort;
  offset: number;
  pageNumber: number;
  pageSize: number;
  paged: boolean;
  unpaged: boolean;
};

export type Sort = {
  empty: boolean;
  sorted: boolean;
  unsorted: boolean;
};

export type Voucher = {
  id: number;
  payerId: number;
  provider: ProviderResolve;
  providerAccount: PayerProviderBankAccountResolve;
  voucherNo?: string;
  amount: number;
  batchCriteria?: "Scheme" | "Provider" | "Region" | "Benefit" | "Aging";
  batchCriteriaId?: number;
  batchCriteriaValue?: string;
  discountType?: "Fixed" | "Percentage";
  discount?: number;
  payableAmount?: number;
  createdBy: KeycloakUserResolve;
  createdOn: string;
};

export type ProviderResolve = {
  providerId: number;
  providerName: string;
  usesGlobalBatchInvoice?: boolean;
  email?: string;
  mainFacilityId?: number;
};
