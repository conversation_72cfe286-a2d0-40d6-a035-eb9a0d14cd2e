import { LineItem } from "../../pages/claims/claim-vetting/types";

export type BatchClaimsRequestArgs = {
  payerId: number; // must provide
  batchIds: number; // must provide
  catalogIds?: number;
  query?: string;
  providerId?: number;
  planIds?: number; // for schemes
  ageBandId?: number; // for aging
  benefitIds?: number;
  page: number;
  size: number;
};

export type IndividualBatchClaimRequestArgs = {
  payerId: number; // must provide
  batchIds: number; // must provide
  invoiceIds: number; // must provide
};

export type BatchListAssignedRequestArgs = {
  payerId: number;
  userId?: string | undefined;
  page: number;
  size: number;
  batchAllocationStatus: "Allocated" | "UnAllocated";
  invoiceBatchStatus: "UnVetted" | "InProgress" | "Completed" | "Deleted";
};

export type EditInvoiceNumberRequestArgs = {
  invoiceId: number;
  invoiceNumber: string;
  updatedBy: string;
  reason: string;
};

export type SaveLineItemsRequestArgs = {
  visitId: string | number;
  invoiceNumber: string;
  providerId: number | string;
  lineItems: LineItem[];
};

export type GetMedicalDrugsRequestType = {
  title: string;
  page: number;
  size: number;
};

export type GetLaboratoriesRequestType = {
  title: string;
  page: number;
  size: number;
};

export type SearchICD10ByTitleRequestArgs = {
  title: string;
  page: number;
  size: number;
};

export type AddDiagnosisRequestArgs = {
  visitId: number;
  description: string;
  icd10code: string;
};

export type SearchProcedureRequestArgs = {
  title: string;
  page: number;
  size: number;
};

export type GetProcedureCodesArgs = {
  invoiceNumber: string;
};

export type GetDocumentsArgs = {
  visitNumber: number;
};

export type UploadFileArgs = {
  providerId: number;
  providerName: string;
  type: string;
  file: File;
};

export type SaveDocumentsArgs = {
  providerName: string;
  providerId: number;
  type: string;
  fileUrl: string;
  invoiceNumber: string;
  visitId: number;
};

export type AddProcedureArgs = {
  invoiceNumber: string;
  providerId: number;
  lineItems: {
    itemDescription: string;
    itemAmount: 0;
    invoiceNumber: string;
    lineType: "MEDICALPROCEDURE";
    itemCode: string;
    itemQuantity: 1;
  }[];
};

export type VetClaimArgs = {
  invoiceId: number[];
  vettingStatus: "APPROVED" | "DECLINED" | "PARTIAL";
  declineReason?: string;
  deductibleAmount?: number;
  partialReason?: string;
  actionedBy: string;
};

export type GetAuditLogArgs = {
  benefitBeneficiaryId?: number;
  preAuthId?: number;
  preAuthTopUpId?: number;
  invoiceId?: number;
  invoiceBatchId?: number;
  page?: number;
  size?: number;
};

export type DownloadDocumentRequest = {
  queryParams: {
    id: number;
  };
};
