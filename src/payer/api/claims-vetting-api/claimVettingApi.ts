import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { baseUrl } from "../../lib/Utils";
import { PreAuth } from "../../pages/claims/claim-vetting/preAuthType";
import {
  AgeBand,
  AuditLog,
  Batch,
  Benefit,
  BenefitCatalog,
  Claim,
  DeleteInvoiceDocumentRequest,
  Diagnosis,
  Document,
  EditInvoiceNumberResponse,
  ICD10,
  Laboratory,
  MedicalDrug,
  Plan,
  Procedure,
  ProcedureCode,
  Provider,
  ResultBoolean,
  SuccessfulAddProcedureResponse,
  SuccessfulGenericResponse,
  SuccessfulSaveDocumentResponse,
  SuccessfulServerContentResponse,
  SuccessfulServerDataObjectResponse,
  SuccessfulServerDataResponse,
  SuccessfulUploadResponse,
} from "../../pages/claims/claim-vetting/types";
import { createSearchParamsString } from "../../utils/createSearchParamsString";
import {
  AddDiagnosisRequestArgs,
  AddProcedureArgs,
  BatchClaimsRequestArgs,
  BatchListAssignedRequestArgs,
  DownloadDocumentRequest,
  EditInvoiceNumberRequestArgs,
  GetAuditLogArgs,
  GetDocumentsArgs,
  GetLaboratoriesRequestType,
  GetMedicalDrugsRequestType,
  GetProcedureCodesArgs,
  IndividualBatchClaimRequestArgs,
  SaveDocumentsArgs,
  SaveLineItemsRequestArgs,
  SearchICD10ByTitleRequestArgs,
  SearchProcedureRequestArgs,
  UploadFileArgs,
  VetClaimArgs,
} from "./requestArgsTypes";

export const claimVettingApi = createApi({
  reducerPath: "claimVettingApi",
  baseQuery: fetchBaseQuery({ baseUrl: baseUrl }),
  tagTypes: [
    "BatchClaim",
    "Diagnosis",
    "Documents",
    "Procedures",
    "Claims",
    "AssignedBatches",
    "IndividualBatch",
  ],
  endpoints: (builder) => ({
    getBatchListAssigned: builder.query<
      SuccessfulServerContentResponse<Batch>,
      BatchListAssignedRequestArgs //todo: add assigned to property in the args type
    >({
      query: (args) => {
        const searchParams = createSearchParamsString(args);
        return `/api/v1/visit/batches/list/assigned?${searchParams}`;
      },
      providesTags: ["AssignedBatches"],
    }),

    getBatchClaims: builder.query<SuccessfulServerContentResponse<Claim>, BatchClaimsRequestArgs>({
      query: (args) => {
        const searchParams = createSearchParamsString(args);
        return `/api/v1/visit/invoices/search?${searchParams}`;
      },
      providesTags: ["Claims"],
    }),

    getAllPayerProviders: builder.query<SuccessfulServerContentResponse<Provider>, number>({
      query: (payerId) => {
        return `/api/v1/membership/payers/${payerId}/providers`;
      },
    }),

    //* gets plans / schemes -> might be confusing -> fetched from the backend
    //* as plans, but displayed as schemes in frontend at claims vetting
    getAllPayerSchemes: builder.query<SuccessfulServerDataResponse<Plan>, number>({
      query: (payerId) => {
        return `/api/v1/membership/payer/${payerId}/plans`;
      },
    }),

    getAllAgeBands: builder.query<SuccessfulServerDataResponse<AgeBand>, void>({
      query: () => {
        return `/api/v1/visit/ageBands`;
      },
    }),

    getAllPayerBenefits: builder.query<SuccessfulServerDataResponse<Benefit>, number>({
      query: (payerId) => {
        return `/api/v1/membership/payer/${payerId}/benefits`;
      },
    }),

    getIndividualBatchClaim: builder.query<
      SuccessfulServerContentResponse<Claim>,
      IndividualBatchClaimRequestArgs
    >({
      query: (args) => {
        const searchParams = createSearchParamsString(args);
        return `/api/v1/visit/invoices/search?${searchParams}`;
      },
      providesTags: ["BatchClaim"],
    }),

    editInvoiceNumber: builder.mutation<EditInvoiceNumberResponse, EditInvoiceNumberRequestArgs>({
      query: (args) => ({
        url: `/api/v1/visit/invoice/${args.invoiceId}`,
        method: "PUT",
        body: {
          invoiceNumber: args.invoiceNumber,
          updatedBy: args.updatedBy,
          reason: args.reason,
        },
      }),
      invalidatesTags: ["BatchClaim"],
    }),

    deleteInvoiceDocument: builder.mutation<ResultBoolean, DeleteInvoiceDocumentRequest>({
      query: (req) => ({
        url: `/api/v1/visit/invoice/documents`,
        method: "DELETE",
        body: req.body,
      }),
      invalidatesTags: ["Documents"],
    }),

    getMedicalDrugs: builder.query<
      SuccessfulServerContentResponse<MedicalDrug>,
      GetMedicalDrugsRequestType
    >({
      query: (args) => {
        const searchParams = createSearchParamsString(args);
        return `/api/v1/visit/searchMedicalDrugs?${searchParams}`;
      },
    }),

    getLaboratories: builder.query<
      SuccessfulServerContentResponse<Laboratory>,
      GetLaboratoriesRequestType
    >({
      query: (args) => {
        const searchParams = createSearchParamsString(args);
        return `/api/v1/visit/searchLaboratory?${searchParams}`;
      },
    }),

    saveLineItems: builder.mutation<EditInvoiceNumberResponse, SaveLineItemsRequestArgs>({
      query: (args) => {
        //* I had to modify the line items structure coz the api
        //* only accepted lineItems of this specific shape/schema
        const modifiedLineItems = args.lineItems.map((item) => ({
          itemDescription: item.description,
          itemAmount: item.lineTotal,
          invoiceNumber: args.invoiceNumber,
          lineType: item.lineType,
          visitId: args.visitId,
          providerId: args.providerId,
          // itemCode: "string",
          itemQuantity: item.quantity,
          itemUnitPrice: item.unitPrice,
          // providerName: "string",
          type: "INVOICE",
          // fileUrl: "string"
        }));

        return {
          url: `/api/v1/visit/saveLineItem`,
          method: "POST",
          body: {
            visitId: args.visitId,
            invoiceNumber: args.invoiceNumber,
            providerId: args.providerId,
            lineItems: modifiedLineItems,
          },
        };
      },
      invalidatesTags: ["BatchClaim"],
    }),

    getAllPayerBenefitCatalogs: builder.query<SuccessfulServerDataResponse<BenefitCatalog>, number>(
      {
        query: (payerId) => {
          return `/api/v1/membership/payer/${payerId}/benefits/catalog`;
        },
      },
    ),

    editInvoiceBenefit: builder.mutation<EditInvoiceNumberResponse, EditInvoiceNumberRequestArgs>({
      query: (args) => ({
        url: `/api/v1/visit/invoice/${args.invoiceId}`,
        method: "PUT",
        body: {
          invoiceNumber: args.invoiceNumber,
          updatedBy: args.updatedBy,
          reason: args.reason,
        },
      }),
      invalidatesTags: ["BatchClaim"],
    }),

    getDiagnosis: builder.query<SuccessfulServerDataResponse<Diagnosis>, number>({
      query: (visitId) => {
        return `/api/v1/visit/${visitId}/diagnosis`;
      },
      providesTags: ["Diagnosis"],
    }),

    searchICD10ByTitle: builder.query<
      SuccessfulServerContentResponse<ICD10>,
      SearchICD10ByTitleRequestArgs
    >({
      query: (args) => {
        const searchParams = createSearchParamsString(args);
        return `/api/v1/visit/searchICD10ByTitle/icd10code?${searchParams}`;
      },
    }),

    addDiagnosis: builder.mutation<
      SuccessfulServerDataResponse<Diagnosis>,
      AddDiagnosisRequestArgs
    >({
      query: (args) => {
        return {
          url: `/api/v1/visit/saveDiagnosisItem`,
          method: "POST",
          body: {
            visitId: args.visitId,
            description: args.description,
            icd10code: args.icd10code,
          },
        };
      },
      invalidatesTags: ["Diagnosis"],
    }),

    searchProcedure: builder.query<
      SuccessfulServerContentResponse<Procedure>,
      SearchProcedureRequestArgs
    >({
      query: (args) => {
        const searchParams = createSearchParamsString(args);
        return `/api/v1/visit/searchMedicalProcedure?${searchParams}`;
      },
    }),

    getProcedureCodes: builder.query<SuccessfulServerDataResponse<ProcedureCode>, number>({
      query: (visitId) => {
        return `/api/v1/visit/${visitId}/procedureCodes`;
      },
      providesTags: ["Procedures"],
    }),

    getDocuments: builder.query<SuccessfulServerDataResponse<Document>, GetDocumentsArgs>({
      query: (args) => {
        const searchParams = createSearchParamsString(args);
        return `/api/v1/visit/invoice/documents?${searchParams}`;
      },
      providesTags: ["Documents"],
    }),

    uploadFile: builder.mutation<SuccessfulUploadResponse, UploadFileArgs>({
      query: (args) => {
        const formData = new FormData();
        formData.append("providerId", args.providerId.toString());
        formData.append("providerName", args.providerName);
        formData.append("type", args.type);
        formData.append("file", args.file);

        return {
          url: "/api/file/upload",
          method: "POST",
          body: formData,
        };
      },
    }),

    saveDocument: builder.mutation<SuccessfulSaveDocumentResponse, SaveDocumentsArgs>({
      query: (args) => {
        return {
          url: "/api/v1/document/save",
          method: "POST",
          body: {
            providerName: args.providerName,
            providerId: args.providerId,
            type: args.type,
            fileUrl: args.fileUrl,
            invoiceNumber: args.invoiceNumber,
            visitId: args.visitId,
          },
        };
      },
      invalidatesTags: ["Documents"],
    }),

    addProcedure: builder.mutation<SuccessfulAddProcedureResponse, AddProcedureArgs>({
      query: (args) => {
        return {
          url: "/api/v1/visit/saveLineItem",
          method: "POST",
          body: args,
        };
      },
      invalidatesTags: ["Procedures"],
    }),

    getPreAuthorizations: builder.query<SuccessfulServerDataResponse<PreAuth>, number>({
      query: (visitNumber) => {
        return `/api/v1/preauthorization/${visitNumber}/preauth`;
      },
    }),

    getBatch: builder.query<SuccessfulServerDataObjectResponse<Batch>, number>({
      query: (batchId) => {
        return `/api/v1/visit/batch/${batchId}`;
      },
      providesTags: ["IndividualBatch"],
    }),

    vetClaim: builder.mutation<SuccessfulGenericResponse, VetClaimArgs>({
      query: (args) => {
        return {
          url: "/api/v1/visit/claimsVetting/invoice",
          method: "POST",
          body: args,
        };
      },
      invalidatesTags: ["BatchClaim", "Claims", "AssignedBatches", "IndividualBatch"],
    }),

    getAuditLogs: builder.query<SuccessfulServerContentResponse<AuditLog>, GetAuditLogArgs>({
      query: (params) => {
        const searchParams = createSearchParamsString(params);
        return `/api/v1/visit/audit/logs?${searchParams}`;
      },
    }),

    downloadDocumentById: builder.query<Blob, DownloadDocumentRequest>({
      query: (query) => {
        const searchParams = createSearchParamsString(query.queryParams);
        return `/api/v1/visit/documentDownload?${searchParams}`;
      },
    }),
  }),
});

export const {
  useLazyDownloadDocumentByIdQuery,
  useVetClaimMutation,
  useGetBatchQuery,
  useGetPreAuthorizationsQuery,
  useAddProcedureMutation,
  useSaveDocumentMutation,
  useUploadFileMutation,
  useGetDocumentsQuery,
  useGetBatchListAssignedQuery,
  useGetBatchClaimsQuery,
  useGetAllPayerProvidersQuery,
  useGetAllPayerSchemesQuery,
  useGetAllAgeBandsQuery,
  useGetAllPayerBenefitsQuery,
  useGetIndividualBatchClaimQuery,
  useEditInvoiceNumberMutation,
  useGetMedicalDrugsQuery,
  useGetLaboratoriesQuery,
  useSaveLineItemsMutation,
  useGetAllPayerBenefitCatalogsQuery,
  useGetDiagnosisQuery,
  useSearchICD10ByTitleQuery,
  useAddDiagnosisMutation,
  useSearchProcedureQuery,
  useGetProcedureCodesQuery,
  useGetAuditLogsQuery,
  useDeleteInvoiceDocumentMutation,
} = claimVettingApi;
