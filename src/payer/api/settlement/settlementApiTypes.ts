import { VoucherPaymentStatus } from "../../pages/finance-and-accounting-settlement/settlementTypes";

export type SuccessResponse<T> = {
  success: boolean;
  msg?: string;
  data?: T;
};

export type ContentResponse<T> = {
  success: boolean;
  msg: string;
  data: {
    content: T;
    empty: boolean;
    first: boolean;
    last: boolean;
    number: number;
    numberOfElements: number;
    pageable: {
      sort: {
        empty: boolean;
        unsorted: boolean;
        sorted: boolean;
      };
      offset: number;
      pageNumber: number;
      pageSize: number;
      paged: boolean;
      unpaged: boolean;
    };
    size: number;
    sort: {
      empty: boolean;
      unsorted: boolean;
      sorted: boolean;
    };
    totalElements: number;
    totalPages: number;
  };
};

export type PaymentVoucherParams = {
  payerId: number;
  searchParams: {
    paymentIds?: number[];
    voucherNo?: string;
    providerIds?: number[];
    providerAccountIds?: number[];
    regionIds?: number[];
    voucherPaymentStatus?: VoucherPaymentStatus;
    batchCriteria?: "Scheme" | "Provider" | "Region" | "Benefit" | "Aging";
    startDate?: string;
    endDate?: string;
    page?: number;
    size?: number;
  };
};

export type VoucherInvoiceParams = {
  searchParams: {
    voucherIds: number[];
    page: number;
    size: number;
  };
};

export type DiscountVoucherRequest = {
  parameters: {
    voucherId: number;
  };
  body: {
    discountType: "Fixed" | "Percentage";
    discount: number;
    actionByUser: string;
  };
};

export type ProviderAccountsRequest = {
  parameters: {
    payerId: number;
  };
};

export type PayerRegionsRequest = {
  parameters: {
    payerId: number;
  };
};

export type CreatePaymentRequest = {
  body: {
    payerId: number;
    voucherIds: number[];
    modeOfPayment: "Cheque" | "BankTransfer";
    chequeNo: string;
    chequeDate: string;
    actionedBy: string;
  };
};

export type GetPaymentsRequest = {
  parameters: {
    query: {
      query?: string;
      payerId: number;
      paymentReference?: string;
      bankReference?: string;
      paid?: boolean;
      page?: number;
      size?: number;
    };
  };
};

export type SuccessfulFileUploadResponse = {
  success: boolean;
  msg: string;
  data: string; // url of the file
};

export type PaymentUploadRequest = {
  parameters: {
    query: {
      payerId: number;
      actionedBy: string;
    };
  };
  body: {
    file: File;
  };
};

export type SendRemittanceRequest = {
  body: {
    paymentId: number;
    actionedBy: string;
  };
};
