import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { baseUrl } from "../../lib/Utils";
import { createSearchParamsString } from "../../utils/createSearchParamsString";
import {
  ContentResponse,
  CreatePaymentRequest,
  DiscountVoucherRequest,
  GetPaymentsRequest,
  PayerRegionsRequest,
  PaymentUploadRequest,
  PaymentVoucherParams,
  ProviderAccountsRequest,
  SendRemittanceRequest,
  SuccessResponse,
  VoucherInvoiceParams,
} from "./settlementApiTypes";
import {
  Invoice,
  PayerRegion,
  Payment,
  ProviderBankAccount,
  Voucher,
} from "../../pages/finance-and-accounting-settlement/settlementTypes";

export const settlementApi = createApi({
  reducerPath: "settlementApi",
  baseQuery: fetchBaseQuery({ baseUrl: baseUrl }),
  tagTypes: ["Vouchers", "Payments"],
  endpoints: (builder) => ({
    getPaymentVouchers: builder.query<ContentResponse<Voucher[]>, PaymentVoucherParams>({
      query: (params) => {
        const searchParams = createSearchParamsString(params.searchParams);
        return `/api/v1/visit/payment/voucher/${params.payerId}?${searchParams}`;
      },
      providesTags: ["Vouchers"],
    }),

    getVoucherInvoices: builder.query<ContentResponse<Invoice[]>, VoucherInvoiceParams>({
      query: (params) => {
        const searchParams = createSearchParamsString(params.searchParams);
        return `/api/v1/visit/invoices/search?${searchParams}`;
      },
    }),

    applyVoucherDiscount: builder.mutation<SuccessResponse<true>, DiscountVoucherRequest>({
      query: (request) => ({
        url: `/api/v1/visit/payment/voucher/${request.parameters.voucherId}`,
        method: "PUT",
        body: request.body,
      }),
      invalidatesTags: ["Vouchers"],
    }),

    getProvidersAccounts: builder.query<
      SuccessResponse<ProviderBankAccount[]>,
      ProviderAccountsRequest
    >({
      query: (request) =>
        `/api/v1/membership/payer/${request.parameters.payerId}/providers/accounts`,
    }),

    getPayerRegions: builder.query<SuccessResponse<PayerRegion[]>, PayerRegionsRequest>({
      query: (request) => `/api/v1/membership/payer/regions/${request.parameters.payerId}`,
    }),

    createPayment: builder.mutation<SuccessResponse<true>, CreatePaymentRequest>({
      query: (request) => ({
        url: `/api/v1/visit/payment`,
        method: "POST",
        body: request.body,
      }),
      invalidatesTags: ["Vouchers", "Payments"],
    }),

    getPayments: builder.query<ContentResponse<Payment[]>, GetPaymentsRequest>({
      query: (request) => {
        const queryString = createSearchParamsString(request.parameters.query);
        return `/api/v1/visit/payments/search?${queryString}`;
      },
      providesTags: ["Payments"],
    }),

    paymentsUpload: builder.mutation<SuccessResponse<string>, PaymentUploadRequest>({
      query: (req) => {
        const queryString = createSearchParamsString(req.parameters.query);

        const formData = new FormData();
        formData.append("file", req.body.file);

        return {
          url: `/api/v1/visit/payments/upload?${queryString}`,
          method: "POST",
          body: formData,
        };
      },

      invalidatesTags: ["Payments"],
    }),
    sendRemittance: builder.mutation<SuccessResponse<boolean>, SendRemittanceRequest>({
      query: (req) => {
        return {
          url: `/api/v1/visit/payments/sendRemittance`,
          method: "POST",
          body: req.body,
        };
      },
    }),

    //...
  }),
});

export const {
  useGetPaymentVouchersQuery,
  useGetVoucherInvoicesQuery,
  useApplyVoucherDiscountMutation,
  useGetProvidersAccountsQuery,
  useGetPayerRegionsQuery,
  useCreatePaymentMutation,
  useGetPaymentsQuery,
  usePaymentsUploadMutation,
  useSendRemittanceMutation,
} = settlementApi;
