import { http, HttpResponse } from "msw";
import { setupWorker } from "msw/browser";
import {
  BillVisitV2Request
} from "~lib/api/schema";
import { SaveBillAndCloseVisitResponse } from "~lib/api/types";
import { baseUrl } from "~lib/constants";

const DELAY_MS = 1_000;

export const handlers = [
  http.post<Record<string, never>, BillVisitV2Request, SaveBillAndCloseVisitResponse>(
    `${baseUrl}/api/v1/visit/bill`,
    async ({ request, params: _params, cookies: _cookies }) => {
      const payload = await request.json();

      console.info(`[MSW] Billing visit ${payload.id}`);

      const response: SaveBillAndCloseVisitResponse = {
        success: true,
        msg: "",
      };

      return HttpResponse.json(response);
    },
  ),
];

export const worker = setupWorker(...handlers);
