.drawer {
  width: 100%;
  @apply relative grid;
  grid-auto-columns: max-content auto;
  &-content {
    @apply col-start-2 row-start-1;
  }
  &-side {
    @apply pointer-events-none fixed start-0 top-0 col-start-1 row-start-1 grid w-full grid-cols-1 grid-rows-1 items-start justify-items-start overflow-y-auto overscroll-contain;
    height: 100vh;
    height: 100dvh;
    scrollbar-width: none;
    &::-webkit-scrollbar {
      @apply hidden;
    }
    & > .drawer-overlay {
      @apply sticky top-0 place-self-stretch cursor-pointer bg-transparent transition-colors duration-200 ease-out;
    }
    & > * {
      @apply col-start-1 row-start-1;
    }
    & > *:not(.drawer-overlay) {
      @apply transition-transform duration-300 ease-out will-change-transform;
      transform: translateX(-100%);
      [dir="rtl"] & {
        transform: translateX(100%);
      }
    }
  }
  &-toggle {
    @apply fixed h-0 w-0 appearance-none opacity-0;
    &:checked {
      & ~ .drawer-side {
        @apply pointer-events-auto visible;

        & > .drawer-overlay {
          background-color: #00000066;
        }

        & > *:not(.drawer-overlay) {
          transform: translateX(0%);
        }
      }
    }

    &:focus-visible ~ .drawer-content label.drawer-button {
      @apply outline outline-2 outline-offset-2;
    }
  }
  &-end {
    grid-auto-columns: auto max-content;
    .drawer-toggle {
      & ~ .drawer-content {
        @apply col-start-1;
      }
      & ~ .drawer-side {
        @apply col-start-2 justify-items-end;
      }
      & ~ .drawer-side > *:not(.drawer-overlay) {
        transform: translateX(100%);
        [dir="rtl"] & {
          transform: translateX(-100%);
        }
      }
      &:checked ~ .drawer-side > *:not(.drawer-overlay) {
        transform: translateX(0%);
      }
    }
  }
}

/* Add lg:drawer-open support */
@media (min-width: 1024px) {
  .lg\:drawer-open > .drawer-toggle {
    @apply hidden;
    & ~ .drawer-side {
      @apply pointer-events-auto visible sticky block w-auto overscroll-auto;

      & > .drawer-overlay {
        @apply cursor-default bg-transparent;
      }

      & > *:not(.drawer-overlay) {
        transform: translateX(0%);
        [dir="rtl"] & {
          transform: translateX(0%);
        }
      }
    }
    &:checked ~ .drawer-side {
      @apply pointer-events-auto visible;
    }
  }
}
