import { useEffect, useState } from "react";

import { connect } from "react-redux";

import BenefitCatalogTable from "../partials/pageItems/BenefitCatalogTable";
import { useSelector } from "react-redux";
import { RootState } from "../../store";
import {
  addBenefitToCatalog,
  getBenefitCatalog,
  getBenefitCatalog2,
  getCatalogProviders,
} from "../../store/catalog/actions";
import ModalBasic from "../../components/ModalBasic";
import { TailSpin } from "react-loader-spinner";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

function Catalog({
  getBenefitCatalog,
  getBenefitCatalog2,
  getCatalogProviders,
  addBenefitToCatalog,
}) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedItems, setSelectedItems] = useState([]);
  const [filteredBenefits, setFilteredBenefits] = useState([]);
  const [addBenefitToCatalogModalOpen, setAddBenefitToCatalogModalOpen] = useState(false);

  const [refreshKey, setRefreshKey] = useState(0);

  const [benefitCode, setBenefitCode] = useState("");
  const [benefitName, setBenefitName] = useState("");
  const [benefitserviceGroup, setBenefitserviceGroup] = useState("");
  const [benefitSubmitted, setBenefitSubmitted] = useState(false);

  const benefitCatalog = useSelector((state: RootState) => state.catalog.benefitCatalog);
  const catelogProviders = useSelector((state: RootState) => state.catalog.catalogProviders);
  const loadingCatalog = useSelector((state: RootState) => state.catalog.loading);
  const success = useSelector((state: RootState) => state.catalog.success);

  const handleSelectedItems = (selectedItems) => {
    setSelectedItems([...selectedItems]);
  };
  const handleServiceGroupChange = (e) => {
    setBenefitserviceGroup(e.target.value);
  };
  useEffect(() => {
    getCatalogProviders();
    getBenefitCatalog2().then(() => {
      setRefreshKey((oldKey) => oldKey + 1);
    });
  }, []);

  const handleSearch = (searchTerm) => {
    // update search value
    setSearchTerm(searchTerm);
    getBenefitCatalog(searchTerm).then(() => {
      setRefreshKey((oldKey) => oldKey + 1);
    });
  };
  useEffect(() => {
    if (benefitSubmitted)
      if (success) {
        toast.success("Benefit Added Successfully!", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        setBenefitSubmitted(false);
      } else {
        toast.error("An Error Occurred!", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        setBenefitSubmitted(false);
      }
  }, [benefitSubmitted]);

  const handleSubmit = (event) => {
    event.preventDefault();
    if (!benefitCode || !benefitName || !benefitserviceGroup) {
      toast.error("All Form Fields are Required!", {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      return;
    }

    addBenefitToCatalog({
      code: benefitCode,
      name: benefitName,
      serviceGroup: benefitserviceGroup,
    }).then(() => {
      setBenefitSubmitted(true);
      setAddBenefitToCatalogModalOpen(false);
      ///getBenefitCatalog();
      setRefreshKey((oldKey) => oldKey + 1);
    });
  };

  return (
    <div className="flex h-full overflow-hidden">
      <div className="relative flex flex-1 flex-col overflow-y-auto overflow-x-hidden">
        <main className="mx-4 mb-4 lg:mx-8 lg:mb-8">
          <div className="mx-auto w-full max-w-9xl rounded-md px-4 shadow-lg sm:px-6 lg:px-8">
            {/* Page header */}
            <div className="sm:flex sm:justify-between">
              {/* Left: Title */}
              <div className="sm:mb-0">
                <h1 className="md:text-md text-sm font-bold  text-gray-800 lg:text-lg">
                  Benefit Catalog
                </h1>
                <div className="flex">
                  <h5 className="md:text-md text-sm font-normal  text-gray-400 lg:text-lg">
                    Add, search or select a Benefit to proceed
                  </h5>
                  {loadingCatalog === true ? (
                    <TailSpin height="40" width="60" color="#2193FF" ariaLabel="loading" />
                  ) : (
                    ""
                  )}
                </div>

                <div className="sm:mb-5 sm:mt-3">
                  <label htmlFor="app-search" className="sr-only">
                    Search
                  </label>
                  <input
                    id="app-search"
                    className="form-input w-full focus:border-gray-300  sm:w-96 sm:py-3 sm:pl-5"
                    type="search"
                    placeholder="Search Benefit Catalog"
                    onChange={(e) => {
                      e.stopPropagation();
                      handleSearch(e.target.value);
                    }}
                  />
                </div>
              </div>

              {/* Right: Actions */}

              {/* Add customer button */}

              <button
                className="btn mt-3 bg-blue-500 text-white hover:bg-blue-500 sm:h-10"
                onClick={(e) => {
                  e.stopPropagation();
                  setAddBenefitToCatalogModalOpen(true);
                }}
              >
                <svg className="h-4 w-4 fill-current" viewBox="0 0 16 16">
                  <path d="M15 7H9V1c0-.6-.4-1-1-1S7 .4 7 1v6H1c-.6 0-1 .4-1 1s.4 1 1 1h6v6c0 .6.4 1 1 1s1-.4 1-1V9h6c.6 0 1-.4 1-1s-.4-1-1-1z" />
                </svg>
                <span className="xs:block ml-2 hidden">Add Benefit</span>
              </button>
            </div>

            {/* Table */}
            <BenefitCatalogTable
              benefitsList={benefitCatalog}
              catelogProviders={catelogProviders}
              key={refreshKey}
            />
            {/* Modal */}
            <ModalBasic
              id="feedback-modal"
              modalOpen={addBenefitToCatalogModalOpen}
              setModalOpen={setAddBenefitToCatalogModalOpen}
              title="Add Benefit"
            >
              {/* Modal content */}

              <div className="px-10 pb-1 pt-4">
                <h3 className="mb-5 text-lg text-gray-400">Benefit Details</h3>

                <div className="text-sm">
                  {/* Options */}
                  <div className="sm:flex sm:items-center sm:justify-center">
                    <div className="mr-9 w-64">
                      <label className="mb-1 block text-sm font-medium" htmlFor="email">
                        Benefit Name
                      </label>
                      <input
                        id="email"
                        className="form-input w-full "
                        type="email"
                        placeholder="Benefit Name"
                        value={benefitName}
                        onChange={(e) => setBenefitName(e.target.value)}
                      />
                    </div>
                    <div className="mr-9 w-64">
                      <label className="mb-1 block text-sm font-medium" htmlFor="name">
                        Benefit Code
                      </label>
                      <input
                        id="name"
                        className="form-input w-full"
                        type="text"
                        placeholder="Benefit Code"
                        value={benefitCode}
                        onChange={(e) => setBenefitCode(e.target.value)}
                      />
                    </div>

                    <div className="mr-9 w-64">
                      <label
                        className="md:text-md lg:text-md mb-1 block  text-sm font-medium "
                        htmlFor="name"
                      >
                        Select Service Group
                      </label>
                      <select
                        onChange={handleServiceGroupChange}
                        name=""
                        className="btn md:text-md  lg:text-md  form-input w-full justify-between  text-sm font-normal hover:border-gray-300 "
                      >
                        <option value="Select Service Group" selected disabled>
                          Select Service Group
                        </option>
                        <option value="OUTPATIENT">OUTPATIENT</option>
                        <option value="INPATIENT">INPATIENT</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
              {/* Modal footer */}

              <div className="m-5 mb-10 pr-5 sm:m-5 sm:flex sm:items-center sm:justify-center sm:px-5 sm:py-4">
                <button
                  className="btn m-5 w-full border-gray-200 text-gray-600 hover:border-gray-300 sm:w-44 sm:px-16 sm:py-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    setAddBenefitToCatalogModalOpen(false);
                  }}
                >
                  Clear
                </button>
                <button
                  type="submit"
                  className="btn m-5 w-full bg-blue-500 text-white hover:bg-blue-600  sm:w-44 sm:px-16 sm:py-2"
                  onClick={(e) => {
                    handleSubmit(e);
                  }}
                >
                  Save
                </button>
              </div>
            </ModalBasic>
          </div>
        </main>
      </div>
    </div>
  );
}
const mapDispatchToProps = (dispatch) => ({
  getBenefitCatalog: (searchTerm) => dispatch(getBenefitCatalog(searchTerm)),
  getBenefitCatalog2: () => dispatch(getBenefitCatalog2()),
  getCatalogProviders: () => dispatch(getCatalogProviders()),
  addBenefitToCatalog: (benefitObject) => dispatch(addBenefitToCatalog(benefitObject)),
});
export default connect(null, mapDispatchToProps)(Catalog);
