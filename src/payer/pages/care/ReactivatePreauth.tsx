import { XMarkIcon } from "@heroicons/react/24/outline";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { useReactivatePreauthMutation } from "~lib/api";
import { TextArea } from "~lib/components";
import { Form } from "~lib/components/Form";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { isErrorWithMessage, isFetchBaseQueryError, queryError, responseError } from "~lib/utils";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import Text from "../../components/ui/typography/Text";
import UserService from "../../services/UserService";
import Button from "../../components/ui/Button";

type Props = {
  preAuthId: number;
  onClose: () => void;
  isOpen: boolean;
};

type Inputs = {
  reason: string;
};

export default function ReactivatePreauth({ preAuthId, onClose, isOpen }: Props) {
  const methods = useForm<Inputs>({
    mode: "onBlur",
    defaultValues: {
      reason: "",
    },
  });

  const { reset } = methods;

  const username = UserService.getUsername();

  const [reactivatePreauth, { isLoading }] = useReactivatePreauthMutation();

  async function handleSubmit(form: Inputs) {
    const { reason } = form;

    const request = {
      reason,
      reactivatedBy: username,
      id: preAuthId,
    };

    try {
      const response = await reactivatePreauth(request);
      const message = responseError(response);

      if (message) {
        throw new Error(message);
      }

      toast.success("Preauth reactivated successfully");
      onClose();
    } catch (error) {
      let message = "Something went wrong. Please try again.";
      if (isFetchBaseQueryError(error) || isErrorWithMessage(error)) {
        message = queryError(error) || message;
      }

      toast.error(message);
      console.error(error);
    }
  }

  return (
    <DialogWrapper onClose={onClose} show={isOpen} className="max-w-[900px] px-12 py-6">
      <div className="flex items-center justify-between">
        <Text variant="subheading" className="grow text-center text-2xl">
          Pre-authorization Reactivation
        </Text>
        <button onClick={onClose}>
          <XMarkIcon className="w-5" strokeWidth={2} />
        </button>
      </div>

      <Text variant="description" className="mt-6 italic">
        Please add a reason for pre-authorization reactivation.
      </Text>

      <Form methods={methods} onSubmit={handleSubmit} className="mt-6 ">
        <fieldset className="w-full rounded-lg border px-2">
          <legend className="text-sm">
            <span>Reason </span>
            <span className="text-red-500">*</span>
          </legend>

          <TextArea
            rows={4}
            placeholder="Add a reason for the reactivation..."
            required
            className="w-full resize-none border-none outline-none"
            name="reason"
          />
        </fieldset>

        <div className="mt-12 flex justify-end gap-8">
          <Button
            type="reset"
            className=" border border-gray-200 px-4 py-2 font-medium text-gray-500 enabled:hover:border-gray-300 disabled:opacity-80"
            onClick={onClose}
            disabled={isLoading}
            variant="outlined"
          >
            Cancel
          </Button>

          <Button
            type="submit"
            className="flex items-center gap-2  bg-success px-4 py-2 font-medium text-white enabled:hover:bg-blue-700 disabled:opacity-80"
            disabled={isLoading}
          >
            {isLoading && <LoadingIcon className="h-4 w-4 text-white" />}
            <span>Reactivate</span>
          </Button>
        </div>
      </Form>
    </DialogWrapper>
  );
}
