import { useState } from 'react';
import ModalBasic from '../../components/ModalBasic';
import clsx from '../../utils';
import AddPreAuthField from './AddPreAuthField';

export default function PreAuthFields() {
  const [isAddFieldModalOpen, setIsAddFieldModalOpen] = useState(false);

  return (
    <div className="flex flex-grow h-full overflow-hidden bg-gray-50">
      <div className="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
        <main className="text-gray-600 justify-center bg-white rounded-md shadow-md mx-4 p-4 pl-16 lg:pl-4 mb-4">
          <hgroup>
            <h1 className="font-medium uppercase text-center mb-4">Pre-Authorization Fields</h1>
          </hgroup>

          <header className="flex justify-end">
            <button 
              className={clsx(
                "px-4 py-2 rounded bg-blue-500 text-white hover:bg-blue-600 focus:ring-2 focus:ring-blue-500"
              )}
              onClick={() => setIsAddFieldModalOpen(true)}  
            >Add Field</button>
          </header>
        </main>
      </div>

      <ModalBasic
        id="add-preauth-field"
        modalOpen={isAddFieldModalOpen}
        onClose={() => setIsAddFieldModalOpen(false)}
        title="Add Field"
        setModalOpen={setIsAddFieldModalOpen}
      >
        <AddPreAuthField />
      </ModalBasic>
    </div>
  )
}
