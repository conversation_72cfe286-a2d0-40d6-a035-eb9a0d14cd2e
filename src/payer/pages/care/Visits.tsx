import { skipToken } from "@reduxjs/toolkit/query";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import {
  useActivateVisitMutation,
  useLazySearchProvidersByTypeQuery,
  useSearchVisitsQuery,
} from "~lib/api";
import { SearchProviderFilter, Visit, VisitStatus } from "~lib/api/types";
import {
  AsyncSelect,
  DateInput,
  Empty,
  FieldWrapper,
  Input,
  Modal,
  Pagination,
} from "~lib/components";
import ErrorMessage from "~lib/components/Error";
import { Form } from "~lib/components/Form";
import { PAGE_SIZES } from "~lib/constants";
import { useDebounce } from "~lib/hooks/useDebounce";
import { Option } from "~lib/types";
import { formatDateTime, responseError, truncate } from "~lib/utils";
import UserService from "../../services/UserService";

interface Inputs {
  provider?: Option<string> | undefined;
  query?: string;
  startDate?: string;
  endDate?: string;
}

const PROVIDERS_SIZE = 10; // Max items to fetch from the API
const PROVIDERS_PAGE = 1; // Page is always 1 for now
const DEBOUNCE_DELAY = 300;

export const Visits = () => {
  const [page, setPage] = useState(1);
  const [size, setSize] = useState<number>(PAGE_SIZES[0]);
  const [showActivateModal, setShowActivateModal] = useState(true);
  const [selectedVisit, setSelectedVisit] = useState<number | undefined>();

  const methods = useForm<Inputs>({
    mode: "onBlur",
    defaultValues: {
      provider: undefined,
      query: "",
      startDate: "",
      endDate: "",
    },
  });

  const { watch } = methods;
  const form = watch();
  const queryDebounced = useDebounce(form.query, 200);

  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];

  const {
    data: visitsResponse,
    error: visitsError,
    isLoading: isVisitsLoading,
    isFetching: isVisitsFetching,
  } = useSearchVisitsQuery(
    !payerId
      ? skipToken
      : {
          page,
          size,
          statuses: [VisitStatus.PENDING],
          payerIds: [Number(payerId)],
          query: queryDebounced,
          startDate: form.startDate,
          endDate: form.endDate,
          providerIds: form.provider ? [Number(form.provider.value)] : undefined,
        },
    {
      refetchOnFocus: true,
      /**
       * payerId should always be defined because of initial auth check.
       * In case it isn't, we shouldn't fetch visits for other payers.
       */
      skip: !payerId,
    }
  );

  const visits = visitsResponse?.data.content;
  const isVisitsFetchingOnly = isVisitsFetching && !isVisitsFetching;

  const [getProviders] = useLazySearchProvidersByTypeQuery();
  const [activateVisit, { isLoading: isActivateVisitLoading }] = useActivateVisitMutation();

  const getProviderOptions = async (query: string) => {
    const response = await getProviders({
      // TODO: Debounce query?
      ...(query && { query }),
      page: PROVIDERS_PAGE,
      size: PROVIDERS_SIZE,
      type: SearchProviderFilter.ALL,
    }).unwrap();

    const providers = response?.data?.content || [];

    return providers.map((provider) => ({
      label: provider.name,
      value: provider.id.toString(),
    }));
  };

  type GetProviderOptionsReturnType = Awaited<ReturnType<typeof getProviderOptions>>;

  const getProviderOptionsDebounced = (delay: number) => {
    let timeoutId: ReturnType<typeof setTimeout>;

    return (query: string) =>
      new Promise<GetProviderOptionsReturnType>((resolve, reject) => {
        clearTimeout(timeoutId);

        timeoutId = setTimeout(async () => {
          try {
            const options = await getProviderOptions(query);
            resolve(options);
          } catch (error) {
            reject(error);
          }
        }, delay);
      });
  };

  const NA = <span className="text-gray-400 uppercase">N/A</span>;

  function handleActivateVisit(id: number) {
    setSelectedVisit(id);
    setShowActivateModal(true);
  }

  function handleCloseActivateModal() {
    setShowActivateModal(false);
    setSelectedVisit(undefined);
  }

  async function handleConfirmActivate() {
    try {
      if (!selectedVisit) {
        throw new Error("No visit selected");
      }

      const response = await activateVisit({
        visitId: selectedVisit,
      });

      const message = responseError(response);
      if (message) {
        throw new Error(message);
      }

      toast.success("Visit re-activated successfully");
      handleCloseActivateModal();
    } catch (error) {
      toast.error((error as Error)?.message || "Something went wrong");
    }
  }

  return (
    <div className="flex flex-grow h-full overflow-hidden bg-gray-50">
      <div className="flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
        <main>
          <div className="w-full text-gray-600 pb-8">
            <div className="bg-white shadow-md rounded-md max-w-full">
              <div className="px-8 py-4 z-10 bg-gray-50">
                <Form className="flex justify-start flex-wrap gap-2 pt-2" methods={methods}>
                  <FieldWrapper
                    name="provider"
                    label="Provider"
                    labelClassName="font-semibold text-gray-500"
                  >
                    <div className="mb-2">
                      <label htmlFor="provider" className="flex gap-2 py-1 items-center">
                        <AsyncSelect
                          name="provider"
                          getOptions={getProviderOptionsDebounced(DEBOUNCE_DELAY)}
                          placeholder="Search provider..."
                          className="flex-grow max-w-full w-64"
                        />
                      </label>
                    </div>
                  </FieldWrapper>

                  <FieldWrapper
                    name="query"
                    label="Search"
                    labelClassName="font-semibold text-gray-500"
                  >
                    <Input
                      type="text"
                      placeholder="Member number or invoice number..."
                      className="max-w-xs"
                    />
                  </FieldWrapper>

                  <FieldWrapper
                    name="startDate"
                    label="Start Date"
                    labelClassName="font-semibold text-gray-500"
                  >
                    <DateInput
                      placeholder={new Date().toISOString().split("T")[0]}
                      maxDate="today"
                      className="w-36"
                    />
                  </FieldWrapper>

                  <FieldWrapper
                    name="endDate"
                    label="End Date"
                    labelClassName="font-semibold text-gray-500"
                  >
                    <DateInput
                      placeholder={new Date().toISOString().split("T")[0]}
                      maxDate="today"
                      className="w-36"
                    />
                  </FieldWrapper>
                </Form>
              </div>

              <div className="mb-4">
                {isVisitsLoading ? (
                  <div className="flex justify-center items-center py-8">
                    {/* prettier-ignore */}
                    <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-blue-400">
                <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
              </svg>
                  </div>
                ) : visitsError ? (
                  <ErrorMessage title="Error fetching visits" message="Refresh to retry" />
                ) : !visits?.length ? (
                  <Empty message="No visits found" />
                ) : (
                  <div className="bg-white text-gray-600 overflow-x-auto">
                    <table className="w-full">
                      <thead className="text-left">
                        <tr className="bg-gray-100">
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Visit Number</th>
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Member Number</th>
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Member Name</th>
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Provider</th>
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Scheme</th>
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Benefit</th>
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Date</th>
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Action</th>
                        </tr>
                      </thead>

                      <tbody>
                        {visits.map((visit: Visit) => (
                          <tr className="cursor-pointer" key={visit.id}>
                            <td className="px-2 py-4 first:pl-8 last:pr-8">{visit.id}</td>
                            <td className="px-2 py-4 first:pl-8 last:pr-8">{visit.memberNumber}</td>
                            <td className="px-2 py-4 first:pl-8 last:pr-8">{visit.memberName}</td>
                            <td
                              className="px-2 py-4 first:pl-8 last:pr-8"
                              title={visit.providerName}
                            >
                              {truncate(visit.providerName, 30)}
                            </td>
                            <td
                              className="px-2 py-4 first:pl-8 last:pr-8"
                              title={visit.schemeName || visit?.scheme?.name}
                            >
                              {truncate(visit.schemeName || visit?.scheme?.name, 30) || NA}
                            </td>
                            <td
                              className="px-2 py-4 first:pl-8 last:pr-8"
                              title={visit.benefitName}
                            >
                              {truncate(visit.benefitName, 30) || NA}
                            </td>
                            <td className="px-2 py-4 first:pl-8 last:pr-8">
                              {visit.createdAt
                                ? formatDateTime(new Date(visit.createdAt + "Z"))
                                : ""}
                            </td>
                            <td className="px-2 py-4 first:pl-8 last:pr-8">
                              <button
                                className="flex items-center gap-2 py-2 px-4 text-white text-sm font-medium rounded bg-blue-500 enabled:hover:bg-blue-700 disabled:opacity-80 disabled:cursor-not-allowed"
                                onClick={() => {
                                  handleActivateVisit(visit.id);
                                }}
                              >
                                Reactivate
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>

              {visitsResponse && (
                <div className="pb-4">
                  <Pagination
                    totalElements={visitsResponse?.data.totalElements}
                    totalPages={visitsResponse?.data.totalPages}
                    setPage={setPage}
                    setSize={setSize}
                    page={page}
                    size={size}
                    isLoading={isVisitsFetchingOnly}
                  />
                </div>
              )}
            </div>
          </div>
        </main>
      </div>

      <Modal
        id="confirm-modal"
        modalOpen={showActivateModal && Boolean(selectedVisit)}
        onClose={handleCloseActivateModal}
        title="Confirm"
        size="sm"
      >
        <div className="p-4">
          <p className="mb-4 text-gray-600">
            Are you sure you want to re-activate visit{" "}
            <span className="text-gray-700 font-medium">{selectedVisit}</span>?
          </p>

          <div className="flex gap-4 justify-end">
            <button
              onClick={handleCloseActivateModal}
              className="flex gap-2 py-2 px-4 font-medium rounded border border-red-500 text-red-500 enabled:hover:text-red-600 enabled:hover:border-red-600"
            >
              Cancel
            </button>

            <button
              onClick={handleConfirmActivate}
              className="flex gap-2 py-2 px-4 font-medium rounded border border-blue-500 text-blue-500 enabled:hover:text-blue-600 enabled:hover:border-blue-600"
              disabled={isActivateVisitLoading}
            >
              {isActivateVisitLoading && (
                <>
                  {/* prettier-ignore */}
                  <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-blue-500">
                    <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z">
                      <animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite" />
                    </path>
                  </svg>
                </>
              )}
              OK
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Visits;
