import { XMarkIcon } from "@heroicons/react/24/outline";
import Button from "../../components/ui/Button";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import Text from "../../components/ui/typography/Text";

type WithdrawPreAuthPromptProps = {
  onClose(): void;
  onProceed(): void;
  isOpen: boolean;
};

export default function WithdrawPreAuthPrompt({
  onClose,
  onProceed,
  isOpen,
}: WithdrawPreAuthPromptProps) {
  return (
    <DialogWrapper maxWidth="max-w-[800px]" onClose={onClose} show={isOpen}>
      <div className=" flex justify-between border-b px-6 py-4">
        <Text variant="subheading" className="text-xl">
          Pre-authorization Withdrawal
        </Text>

        <button onClick={onClose}>
          <XMarkIcon width={20} strokeWidth={2} />
        </button>
      </div>

      <div className="px-12 py-4">
        <Text variant="subheading" className="text-[#374151]">
          Review Before You Withdraw
        </Text>
        <Text variant="description" className="mt-2 text-[#374151]">
          Before withdrawing this preauthorization request, please review the approved admission
          details to proceed.
        </Text>
      </div>

      <div className="mb-6 mt-4 flex justify-end gap-8 pr-12">
        <Button onClick={onClose} variant="outlined">
          Cancel
        </Button>
        <Button onClick={onProceed}>Proceed</Button>
      </div>
    </DialogWrapper>
  );
}
