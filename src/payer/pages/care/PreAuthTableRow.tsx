import { Menu, Transition } from "@headlessui/react";
import {
  ArrowPathIcon,
  ArrowUturnLeftIcon,
  BanknotesIcon,
  EllipsisVerticalIcon,
  NewspaperIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { Fragment, SetStateAction } from "react";
import { PreAuth, PreAuthStatus, RequestType } from "~lib/api/types";
import { headlessUiMenuTransitionAttrs } from "~lib/constants";
import { formatDateTime, truncate } from "~lib/utils";
import Badge from "../../components/ui/Badge";
import TableDataItem from "../../components/ui/table/TableDataItem";
import clsx from "../../utils";
import { getPreAuthBadgeLabelAndColor } from "./getPreAuthBadgeLabelAndColor";
import { PageModal, PreAuthJourney } from "./PreAuthorizations";
import { Preauthorization } from "../../lib/types/care/preAuth";
import { usePreAuthorizationPermissions } from "../../hooks/care/usePreAuthorizationPermissions";

type Props = {
  preauth: Preauthorization;
  handleViewPreAuthApprovalForm(id: number): void;
  disableReviewUnactionablePreauth: boolean;
  disableReviewActionableStatuses: PreAuthStatus[];
  preAuthLogs(preAuthId: number): void;
  handleOpenWithdrawPreAuthPrompt(preauth: PreAuth): void;
  handleOpenRevokePreAuthModal(id: number): void;
  setActiveModal: (value: SetStateAction<PageModal | undefined>) => void;
  setPreAuthJourney: (value: SetStateAction<PreAuthJourney | null>) => void;
  setSelectedPreauthId: (value: SetStateAction<number | undefined>) => void;
  checkIsPerformTopUpAllowed: (preAuth: Preauthorization) => boolean;
};

export default function PreAuthTableRow({
  preauth,
  handleViewPreAuthApprovalForm,
  disableReviewUnactionablePreauth,
  disableReviewActionableStatuses,
  handleOpenWithdrawPreAuthPrompt,
  handleOpenRevokePreAuthModal,
  preAuthLogs,
  setActiveModal,
  setPreAuthJourney,
  setSelectedPreauthId,
  checkIsPerformTopUpAllowed,
}: Props) {
  const {
    hasTopUpPermissionForRequestType,
    hasViewPermissionForRequestType,
    hasApprovePermissionForRequestType,
  } = usePreAuthorizationPermissions();
  function handleProbableTopUpJourney(preAuth: Preauthorization) {
    if (preAuth?.topUps.some((topUp) => topUp.status === "PENDING")) {
      setPreAuthJourney(PreAuthJourney.ReviewTopUp);
    }
  }

  const requestType = preauth.requestType as RequestType;
  const hasViewPermission = hasViewPermissionForRequestType(requestType);
  const canApprove = hasApprovePermissionForRequestType(requestType);
  const canTopUp = hasTopUpPermissionForRequestType(requestType);

  const canPerformActions = canApprove || canTopUp;

  const requiresAction = disableReviewActionableStatuses.includes(preauth.status);
  // Users should only be able to take action if they have the appropriate permissions
  // regardless of whether the preauth requires action or not
  const canTakeAction = canPerformActions;

  return (
    <>
      <tr>
        <TableDataItem className="max-w-[120px] px-1" item={preauth.visit?.id || "NA"} />
        <TableDataItem className="max-w-[120px] px-1" item={preauth.visit?.memberNumber} />
        <TableDataItem className="max-w-[120px] px-1" item={preauth.visit?.memberName} />
        <TableDataItem className="max-w-[120px] px-1" item={preauth.providerName} />
        <TableDataItem className="max-w-[120px] px-1" item={preauth.reference} />
        <TableDataItem className="max-w-[120px] px-1" item={preauth.schemeName || "NA"} />
        <TableDataItem
          className="max-w-[120px] px-1 "
          item={truncate(preauth.visit?.benefitName, 30) || "NA"}
          title={preauth.visit?.benefitName}
        />
        <TableDataItem
          className="max-w-[120px] px-1 "
          item={preauth.createdAt ? formatDateTime(new Date(preauth.createdAt + "Z")) : ""}
        />
        <td className="border-b border-[#EAECF0] px-1 py-4">
          <Badge
            hasDot
            className="text-sm"
            textClassName="max-w-[10ch] whitespace-nowrap text-ellipsis overflow-hidden"
            color={getPreAuthBadgeLabelAndColor(preauth).color}
            text={getPreAuthBadgeLabelAndColor(preauth).label}
          />
        </td>
        <td className="border-b border-[#EAECF0]">
          <button
            type="button"
            className="w-full rounded-lg bg-blue-500 px-1 py-2 text-sm text-white focus:ring-2 focus:ring-blue-500 enabled:hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-80"
            title={
              !hasViewPermission
                ? "You don't have permission to view this benefit type"
                : !canTakeAction
                  ? "You only have view permissions for this benefit type"
                  : "Review"
            }
            onClick={() => {
              handleViewPreAuthApprovalForm(preauth.id);
              handleProbableTopUpJourney(preauth);
            }}
            disabled={
              !hasViewPermission ||
              (!canTakeAction && requiresAction) ||
              (disableReviewUnactionablePreauth &&
                !disableReviewActionableStatuses.includes(preauth.status))
            }
          >
            {canTakeAction ? "Review" : "View"}
          </button>
        </td>
        <td className="border-b border-[#EAECF0]">
          <Menu as="div" className="relative inline-block w-full text-left">
            <div className="flex text-sm">
              <Menu.Button className="flex items-center gap-1 rounded-r py-2 font-medium disabled:cursor-not-allowed disabled:opacity-60">
                <EllipsisVerticalIcon strokeWidth={2} className="w-8 text-[#1E40AF]" />
              </Menu.Button>
            </div>
            <Transition as={Fragment} {...headlessUiMenuTransitionAttrs}>
              <Menu.Items className="absolute right-0 z-10 mt-2 flex origin-top-right flex-col gap-2 rounded-md border bg-white py-2 shadow-lg focus:outline-none">
                <Menu.Item>
                  {({ active }) => (
                    <button
                      className={clsx(
                        "group flex w-full items-center gap-4 rounded-md px-6 py-2 text-sm text-[#374151] enabled:hover:text-gray-600 disabled:cursor-not-allowed disabled:opacity-60",
                        active && "bg-gray-100",
                      )}
                      onClick={() => {
                        preAuthLogs(preauth.id);
                      }}
                      // disabled={preauth.status != PreAuthStatus.AUTHORIZED}
                    >
                      <NewspaperIcon className="w-4" strokeWidth={2} />

                      <span className="whitespace-nowrap">Pre-authorization logs</span>
                    </button>
                  )}
                </Menu.Item>
                <Menu.Item>
                  {({ active }) => (
                    <button
                      className={clsx(
                        "group flex w-full items-center gap-4 whitespace-nowrap rounded-md px-6 py-2 text-sm text-[#374151] enabled:hover:text-gray-600 disabled:cursor-not-allowed disabled:opacity-60",
                        active && "bg-gray-100",
                      )}
                      onClick={() => {
                        handleOpenWithdrawPreAuthPrompt(preauth);
                      }}
                      disabled={!canApprove || preauth.status != PreAuthStatus.AUTHORIZED}
                    >
                      <ArrowUturnLeftIcon className="w-4" strokeWidth={2} />{" "}
                      <span>Withdraw pre-authorization</span>
                    </button>
                  )}
                </Menu.Item>
                <Menu.Item>
                  {({ active }) => (
                    <button
                      className={clsx(
                        "group flex w-full items-center gap-4 rounded-md px-6 py-2 text-sm text-[#374151] enabled:hover:text-gray-600 disabled:cursor-not-allowed disabled:opacity-60",
                        active && "bg-gray-100",
                      )}
                      onClick={() => handleOpenRevokePreAuthModal(preauth.id)}
                      disabled={
                        !canApprove ||
                        !(preauth?.markAsIncomplete || preauth.status === PreAuthStatus.ACTIVE)
                      }
                    >
                      <XMarkIcon className="w-4" strokeWidth={2} /> <span>Revoke</span>
                    </button>
                  )}
                </Menu.Item>

                <Menu.Item>
                  {({ active }) => (
                    <button
                      className={clsx(
                        "group flex w-full items-center gap-4 rounded-md px-6 py-2 text-sm text-[#374151] enabled:hover:text-gray-600 disabled:cursor-not-allowed disabled:opacity-60",
                        active && "bg-gray-100",
                      )}
                      onClick={() => {
                        setActiveModal(PageModal.REACTIVATION_REVIEW_PROMPT);
                        setPreAuthJourney(PreAuthJourney.ReactivatePreauth);
                        setSelectedPreauthId(preauth.id);
                      }}
                      disabled={!canApprove || preauth.status !== PreAuthStatus.EXPIRED}
                    >
                      <ArrowPathIcon className="w-4" strokeWidth={2} /> <span>Reactivate</span>
                    </button>
                  )}
                </Menu.Item>

                <Menu.Item>
                  {({ active }) => (
                    <button
                      className={clsx(
                        "group flex w-full items-center gap-4 rounded-md px-6 py-2 text-sm text-[#374151] enabled:hover:text-gray-600 disabled:cursor-not-allowed disabled:opacity-60",
                        active && "bg-gray-100",
                      )}
                      onClick={() => {
                        setActiveModal(PageModal.TOP_UP_PROMPT);
                        setPreAuthJourney(PreAuthJourney.TopUp);
                        setSelectedPreauthId(preauth.id);
                      }}
                      disabled={!canTopUp || !checkIsPerformTopUpAllowed(preauth)}
                      title={
                        !canTopUp
                          ? "You don't have permission to top up this benefit type"
                          : "Top Up"
                      }
                    >
                      <BanknotesIcon className="w-4" strokeWidth={2} /> <span>Top Up</span>
                    </button>
                  )}
                </Menu.Item>
              </Menu.Items>
            </Transition>
          </Menu>
        </td>
      </tr>
    </>
  );
}
