import { XMarkIcon } from "@heroicons/react/24/outline";
import Button from "../../components/ui/Button";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import Text from "../../components/ui/typography/Text";

type TopUpPrompt = {
  onClose(): void;
  onProceed(): void;
  isOpen: boolean;
};
export default function TopUpPrompt({ onClose, isOpen, onProceed }: TopUpPrompt) {
  return (
    <DialogWrapper className="max-w-[900px]" onClose={onClose} show={isOpen}>
      <div className="flex justify-between border-b border-gray-300 px-8 py-4">
        <Text className="text-2xl" variant="subheading">
          Pre-authorization Top Up
        </Text>

        <button onClick={onClose}>
          <XMarkIcon className="w-5" strokeWidth={2} />
        </button>
      </div>

      <div className="flex flex-col gap-2 px-12 py-6">
        <Text className="text-lg font-medium" variant="paragraph">
          Review Before You Top UP
        </Text>
        <Text className="" variant="description">
          Kindly review the details of the expired pre-authorization request before proceeding with
          top up.
        </Text>
      </div>

      <div className="flex justify-end gap-6 px-8 py-8">
        <Button onClick={onClose} variant="outlined">
          Cancel
        </Button>
        <Button onClick={onProceed}>Proceed</Button>
      </div>
    </DialogWrapper>
  );
}
