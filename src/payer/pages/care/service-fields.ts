export enum ComplexPrimitive {
  Array = "ARRAY",
  Options = "OPTIONS",
}

export const ComputedPrimitive = "COMPUTED";

/**
 * WARN: Update renderField, and baseDefaultValues when adding new network
 * primitives.
 */
export enum NetworkPrimitive {
  Disease = "DISEASE",
  Drug = "DRUG",
  LabTest = "LAB_TEST",
  Procedure = "PROCEDURE",
}

export enum Primitive {
  Boolean = "BOOLEAN",
  Date = "DATE",
  Number = "NUMBER",
  Text = "TEXT",
  Textbox = "TEXTBOX",
}

interface PrimitiveTypes {
  [Primitive.Boolean]: boolean;
  [Primitive.Date]: string;
  [Primitive.Number]: number;
  [Primitive.Text]: string;
  [Primitive.Textbox]: string;
}

export const baseDefaultValues = {
  [Primitive.Boolean]: false,
  [Primitive.Date]: "",
  [Primitive.Number]: 0,
  [Primitive.Text]: "",
  [Primitive.Textbox]: "",
  [ComplexPrimitive.Options]: "",
  [NetworkPrimitive.Disease]: "",
  [NetworkPrimitive.Drug]: "",
  [NetworkPrimitive.LabTest]: "",
  [NetworkPrimitive.Procedure]: "",
  [ComputedPrimitive]: 0,
};

export const TypeDescription = {
  [Primitive.Boolean]: "A true or false value",
  [Primitive.Date]: "A date",
  [Primitive.Number]: "A number",
  [Primitive.Text]: "A short text",
  [Primitive.Textbox]: "A long text",
  [ComplexPrimitive.Options]: "A list of options",
};

/* ----- */

/**
 * T - The type of the field value
 * S - The type of the field that will trigger the showIf condition
 */
export interface FieldProperties<
  T extends string | number | boolean = string,
  S extends string | number | boolean = string,
> {
  name: string;
  label?: string;
  description?: string;
  service: service;
  showIf?: {
    field: string;
    value: S;
  };
  placeholder?: T;
  hidden?: boolean;
}

type NestedFieldProperties = Omit<FieldProperties, "service" | "showIf">;

export type ComputedPrimitive = typeof ComputedPrimitive;

/**
 * A computed field has a string value calculated from other fields using
 * JavaScript expressions, with the context of the current form or the current
 * array field item. Global functions:
 * - formatMoney: format number as KES 1,000.00
 * - formatDate: format date string as DD/MM/YYYY
 */
interface BaseComputedField {
  /**
   * The expression to evaluate
   * @example
   * 'numberOfSessions*amountPerSession'
   */
  expression: string;
  type: ComputedPrimitive;
  defaultValue?: string;
}

export interface ComputedField extends FieldProperties, BaseComputedField {}

export interface ComputedNestedField extends BaseComputedField, NestedFieldProperties {}

interface BaseField<T extends Primitive = Primitive> {
  type: T;
  defaultValue?: PrimitiveTypes[T];
  rules?: NumberRules | TextRules | DateRules;
}

export interface BasicField<
  T extends Primitive = Primitive,
  S extends string | number | boolean = string,
> extends BaseField<T>,
    FieldProperties<PrimitiveTypes[T], S> {}

export interface BasicNestedField<T extends Primitive = Primitive>
  extends BaseField<T>,
    NestedFieldProperties {}

interface BaseNetworkField<T extends NetworkPrimitive = NetworkPrimitive> {
  type: T;
  defaultValue?: string;
  rules?: {
    required: boolean;
  };
}

export interface NetworkField<T extends NetworkPrimitive = NetworkPrimitive>
  extends BaseNetworkField<T>,
    FieldProperties {}

export interface NetworkNestedField<T extends NetworkPrimitive = NetworkPrimitive>
  extends BaseNetworkField<T>,
    NestedFieldProperties {}

export enum OptionWidget {
  Select = "SELECT",
  RadioGroup = "RADIO_GROUP",
  CheckboxGroup = "CHECKBOX_GROUP",
}

interface BaseOptionField<T extends string | number = string> {
  type: ComplexPrimitive.Options;
  options: Array<{
    label: string;
    value: T;
  }>;
  rules?: OptionRules;
  defaultValue?: T | Array<T>;
  /**
   * The widget to use to render the options
   * @default OptionWidget.Select
   */
  widget?: OptionWidget;
}

export interface OptionField<T extends string | number = string>
  extends BaseOptionField<T>,
    FieldProperties {}
export interface OptionNestedField<T extends string | number = string>
  extends BaseOptionField<T>,
    NestedFieldProperties {}

export enum Aggregation {
  Sum = "SUM",
  Average = "AVERAGE",
  Count = "COUNT",
  Min = "MIN",
  Max = "MAX",
}

/**
 * An array of object fields.
 */
export interface ArrayField extends FieldProperties {
  type: ComplexPrimitive.Array;
  /**
   * The fields that will be repeated
   */
  fields: Array<BasicNestedField | NetworkNestedField | OptionNestedField | ComputedNestedField>;
  rules?: ArrayRules;
  aggregate?: {
    /**
     * The field to aggregate.
     * WARN: Must be a number field.
     */
    label: string;
    field: string;
    aggregation: Aggregation;
  };
}

/* ----- */

export interface Rules {
  required?: boolean;
}

export interface NumberRules extends Rules {
  min?: number;
  max?: number;
}

export interface TextRules extends Rules {
  minLength?: number;
  maxLength?: number;
}

/**
 * duration strings - d
 * @example
 *
 * const rules = {
 *  min: 'today'
 * }
 */
export interface DateRules extends Rules {
  min?: string | "today";
  max?: string | "today";
}

export interface OptionRules extends Rules {
  minLength?: number;
  maxLength?: number;
  allowsMultiple?: boolean;
  allowsOther?: boolean;
}

export interface ArrayRules extends Rules {
  minLength?: number;
  maxLength?: number;
}

/* ----- */

export const requestTypes = [
  {
    name: "Admission",
    label: "Admission",
  },
  {
    name: "Maternity",
    label: "Maternity",
  },
  {
    name: "Day Case",
    label: "Day Case",
  },
  {
    name: "Outpatient",
    label: "Outpatient",
  },
  {
    name: "Dental",
    label: "Dental",
  },
  {
    name: "Optical",
    label: "Optical",
  },
  {
    name: "Other",
    label: "Other",
  },
] as const;

/**
 * NOTE: Service names must be unique
 */
export const services = [
  {
    name: "admissionIllness",
    label: "Illness",
    requestType: "Admission",
  },
  {
    name: "admissionAccident",
    label: "Accident",
    requestType: "Admission",
  },
  {
    name: "admissionSurgery",
    label: "Surgery",
    requestType: "Admission",
  },
  {
    name: "maternityNormal",
    label: "Normal",
    requestType: "Maternity",
  },
  {
    name: "maternityCesarean",
    label: "Cesarean",
    requestType: "Maternity",
  },
  {
    name: "dayCaseMedical",
    label: "Medical",
    requestType: "Day Case",
  },
  {
    name: "dayCaseSurgery",
    label: "Surgery",
    requestType: "Day Case",
  },
  {
    name: "outpatientPharmacy",
    label: "Pharmacy",
    requestType: "Outpatient",
  },
  {
    name: "outpatientLaboratory",
    label: "Laboratory",
    requestType: "Outpatient",
  },
  {
    name: "outpatientRadiology",
    label: "Radiology",
    requestType: "Outpatient",
  },
  {
    name: "outpatientPhysiotherapy",
    label: "Physiotherapy",
    requestType: "Outpatient",
  },
  {
    name: "optical",
    label: "Optical",
    requestType: "Optical",
  },
  {
    name: "dental",
    label: "Dental",
    requestType: "Dental",
  },
  {
    name: "other",
    label: "Other",
    requestType: "Other",
  },
] as const;

export type service = (typeof services)[number]["name"];

/**
 * WARN: If fields across different services have the same name,
 * the field must refer to the same data type and entity.
 * WARN: There should be at most one array field per service
 * called line, type Line[].
 * TODO: Refactor common fields.
 * TODO: Extract base type of showIf field.
 * When updating the fields, update the following:
 * - [FieldsBase]
 */
export const serviceFields: Array<
  | BasicField<Primitive.Boolean>
  | BasicField<Primitive.Text>
  | BasicField<Primitive.Number>
  | BasicField<Primitive.Text, boolean>
  | BasicField<Primitive.Textbox>
  | BasicField<Primitive.Date>
  | OptionField
  | NetworkField
  | ArrayField
  | ComputedField
> = [
  /* ----- */
  // Optical
  {
    name: "prescriptionType",
    label: "Type of Prescription",
    type: ComplexPrimitive.Options,
    service: "optical",
    options: [
      {
        label: "First Time",
        value: "First Time",
      },
      {
        label: "Subsequent",
        value: "Subsequent",
      },
    ],
    rules: {
      required: true,
    },
  },
  {
    name: "doctorName",
    label: "Name of Doctor",
    type: Primitive.Text,
    rules: {
      required: true,
    },
    service: "optical",
  },
  {
    name: "frameMake",
    label: "Frame Make",
    type: Primitive.Text,
    service: "optical",
  },
  {
    name: "typeOfLens",
    label: "Type of Lens",
    type: Primitive.Text,
    service: "optical",
  },
  {
    name: "prescriptionReason",
    label: "Reason for Prescription",
    type: ComplexPrimitive.Options,
    service: "optical",
    options: [
      {
        label: "First-time Vision Correction",
        value: "First-time Vision Correction",
      },
      {
        label: "Prescription Change",
        value: "Prescription Change",
      },
      {
        label: "Frames/Lenses Breakage Beyond Repair",
        value: "Frames/Lenses Breakage Beyond Repair",
      },
      {
        label: "Loss of Spectacles",
        value: "Loss of Spectacles",
      },
      {
        label: "Frames Wear and Tear",
        value: "Frames Wear and Tear",
      },
    ],
    rules: {
      allowsOther: true,
      required: true,
    },
    placeholder: "Type or select a reason",
  },
  {
    name: "dateOfFirstDiagnosis",
    label: "Date of First Diagnosis",
    type: Primitive.Date,
    service: "optical",
    rules: {
      max: "today",
    },
  },
  {
    name: "consultationFee",
    label: "Consultation Fee",
    type: Primitive.Number,
    service: "optical",
    defaultValue: 0,
    rules: {
      min: 0,
    },
  },
  {
    name: "framesCost",
    label: "Cost of Frames",
    type: Primitive.Number,
    service: "optical",
    defaultValue: 0,
    rules: {
      min: 0,
    },
  },
  {
    name: "lensesCost",
    label: "Cost of Lenses",
    type: Primitive.Number,
    service: "optical",
    defaultValue: 0,
    rules: {
      min: 0,
    },
  },
  {
    name: "totalCost",
    label: "Total Cost",
    type: ComputedPrimitive,
    service: "optical",
    expression: "formatMoney(framesCost + lensesCost + consultationFee)",
  },
  /* ----- */
  // Dental
  {
    name: "dentalConditionType",
    label: "Type of Condition",
    type: ComplexPrimitive.Options,
    service: "dental",
    options: [
      {
        label: "Final Diagnosis",
        value: "Final Diagnosis",
      },
      {
        label: "First Diagnosis",
        value: "First Diagnosis",
      },
    ],
    rules: {
      required: true,
    },
  },
  {
    name: "doctorName",
    label: "Name of Doctor",
    type: Primitive.Text,
    rules: {
      required: true,
    },
    service: "dental",
  },
  {
    name: "numberOfSessions",
    label: "Sessions",
    type: Primitive.Number,
    service: "dental",
    rules: {
      min: 1,
    },
  },
  {
    name: "underlyingCondition",
    label: "Underlying Condition",
    type: Primitive.Text,
    service: "dental",
  },
  {
    name: "natureOfTreatment",
    label: "Nature of Treatment",
    type: Primitive.Text,
    service: "dental",
    rules: {
      required: true,
    },
  },
  {
    name: "dateOfFirstDiagnosis",
    label: "Date of First Diagnosis",
    type: Primitive.Date,
    service: "dental",
    rules: {
      max: "today",
    },
  },
  {
    name: "surgeryRequired",
    label: "Surgery Required",
    type: Primitive.Boolean,
    service: "dental",
  },
  {
    name: "surgeryDetails",
    label: "Surgery Details",
    type: Primitive.Text,
    service: "dental",
    showIf: {
      field: "surgeryRequired",
      value: true,
    },
    rules: {
      required: true,
    },
  },
  {
    name: "dateOfProcedure",
    label: "Date of Procedure",
    type: Primitive.Date,
    service: "dental",
    rules: {
      required: true,
    },
  },
  {
    name: "lines",
    label: "Dental Items",
    type: ComplexPrimitive.Array,
    service: "dental",
    rules: {
      required: true,
    },
    fields: [
      {
        name: "name",
        label: "Procedure",
        type: ComplexPrimitive.Options,
        options: [
          {
            label: "Extraction",
            value: "Extraction",
          },
          {
            label: "Filling",
            value: "Filling",
          },
          {
            label: "Scaling and Polishing",
            value: "Scaling and Polishing",
          },
          {
            label: "Root Canal Treatment",
            value: "Root Canal Treatment",
          },
        ],
        rules: {
          required: true,
          allowsOther: true,
        },
        placeholder: "Type or select a procedure",
      },
      {
        name: "numberOfTeeth",
        label: "No. of Teeth",
        type: Primitive.Number,
        defaultValue: 1,
        rules: {
          required: true,
        },
      },
      {
        name: "cost",
        label: "Cost",
        type: Primitive.Number,
        defaultValue: 0,
        rules: {
          required: true,
        },
      },
      {
        name: "tag",
        type: Primitive.Text,
        defaultValue: "dentalCosting",
        hidden: true,
      },
    ],
    aggregate: {
      label: "Aggregate Cost",
      field: "cost",
      aggregation: Aggregation.Sum,
    },
  },
  /* ----- */
  // Day Case - Medical
  {
    name: "doctorName",
    label: "Name of Doctor",
    type: Primitive.Text,
    rules: {
      required: true,
    },
    service: "dayCaseMedical",
  },
  {
    name: "conditionRecurrent",
    label: "Condition Reccurent",
    type: Primitive.Boolean,
    service: "dayCaseMedical",
  },
  {
    name: "natureOfTreatment",
    label: "Treatment Plan",
    type: Primitive.Textbox,
    service: "dayCaseMedical",
    rules: {
      required: true,
    },
  },
  {
    name: "conditionCongenital",
    label: "Condition Congenital",
    type: Primitive.Boolean,
    service: "dayCaseMedical",
  },
  {
    name: "underlyingCondition",
    label: "Underlying Condition",
    type: Primitive.Text,
    service: "dayCaseMedical",
  },
  {
    name: "causeOfIllness",
    label: "Cause of Illness",
    type: Primitive.Text,
    service: "dayCaseMedical",
  },
  {
    name: "procedureType",
    label: "Procedure",
    type: NetworkPrimitive.Procedure,
    service: "dayCaseMedical",
    rules: {
      required: true,
    },
  },
  /* ----- */
  // Day Case - Surgery
  {
    name: "doctorName",
    label: "Name of Doctor",
    type: Primitive.Text,
    rules: {
      required: true,
    },
    service: "dayCaseSurgery",
  },
  {
    name: "expedited",
    label: "Expedited",
    type: Primitive.Boolean,
    service: "dayCaseSurgery",
  },
  {
    name: "procedureType",
    label: "Procedure",
    type: NetworkPrimitive.Procedure,
    service: "dayCaseSurgery",
    rules: {
      required: true,
    },
  },
  {
    name: "typeOfSurgery",
    label: "Type of Surgery",
    type: Primitive.Text,
    rules: {
      required: true,
    },
    service: "dayCaseSurgery",
  },
  {
    name: "underlyingCondition",
    label: "Underlying Condition",
    type: Primitive.Text,
    service: "dayCaseSurgery",
  },
  {
    name: "dateOfProcedure",
    label: "Date of Procedure",
    type: Primitive.Date,
    service: "dayCaseSurgery",
    rules: {
      required: true,
    },
  },
  /* ----- */
  // Admission - Illness
  {
    name: "doctorName",
    label: "Name of Doctor",
    type: Primitive.Text,
    rules: {
      required: true,
    },
    service: "admissionIllness",
  },
  {
    name: "conditionRecurrent",
    label: "Condition Reccurent",
    type: Primitive.Boolean,
    service: "admissionIllness",
  },
  {
    name: "natureOfTreatment",
    label: "Treatment Plan",
    type: Primitive.Textbox,
    service: "admissionIllness",
    rules: {
      required: true,
    },
  },
  {
    name: "conditionCongenital",
    label: "Condition Congenital",
    type: Primitive.Boolean,
    service: "admissionIllness",
  },
  {
    name: "dateOfAdmission",
    label: "Date of Admission",
    type: Primitive.Date,
    service: "admissionIllness",
    rules: {
      required: true,
    },
  },
  {
    name: "underlyingCondition",
    label: "Underlying Condition",
    type: Primitive.Text,
    service: "admissionIllness",
  },
  {
    name: "causeOfIllness",
    label: "Cause of Illness",
    type: Primitive.Text,
    service: "admissionIllness",
  },
  {
    name: "procedureType",
    label: "Procedure",
    type: NetworkPrimitive.Procedure,
    service: "admissionIllness",
    rules: {
      required: true,
    },
  },
  /* ----- */
  // Admission - Accident
  {
    name: "doctorName",
    label: "Name of Doctor",
    type: Primitive.Text,
    service: "admissionAccident",
    rules: {
      required: true,
    },
  },
  {
    name: "natureOfAccident",
    label: "Nature of Accident",
    type: Primitive.Text,
    service: "admissionAccident",
    rules: {
      required: true,
    },
  },
  {
    name: "natureOfTreatment",
    label: "Treatment Plan",
    type: Primitive.Textbox,
    service: "admissionAccident",
    rules: {
      required: true,
    },
  },
  {
    name: "causeOfAccident",
    label: "Cause of Accident",
    type: Primitive.Text,
    service: "admissionAccident",
    rules: {
      required: true,
    },
  },
  {
    name: "dateOfAccident",
    label: "Date of Accident",
    type: Primitive.Date,
    service: "admissionAccident",
    rules: {
      required: true,
      max: "today",
    },
  },
  {
    name: "underlyingCondition",
    label: "Underlying Condition",
    type: Primitive.Text,
    service: "admissionAccident",
  },
  /* ----- */
  // Admission - Surgery
  {
    name: "doctorName",
    label: "Name of Doctor",
    type: Primitive.Text,
    rules: {
      required: true,
    },
    service: "admissionSurgery",
  },
  {
    name: "expedited",
    label: "Surgery Expedited",
    type: Primitive.Boolean,
    service: "admissionSurgery",
  },
  {
    name: "procedureType",
    label: "Procedure",
    type: NetworkPrimitive.Procedure,
    service: "admissionSurgery",
    rules: {
      required: true,
    },
  },
  {
    name: "typeOfSurgery",
    label: "Type of Surgery",
    type: Primitive.Text,
    rules: {
      required: true,
    },
    service: "admissionSurgery",
  },
  {
    name: "underlyingCondition",
    label: "Underlying Condition",
    type: Primitive.Text,
    service: "admissionSurgery",
  },
  {
    name: "dateOfProcedure",
    label: "Date of Procedure",
    type: Primitive.Date,
    service: "admissionSurgery",
    rules: {
      required: true,
    },
  },
  /* ----- */
  // Maternity - Normal
  {
    name: "dueDate",
    label: "Due Date",
    type: Primitive.Date,
    service: "maternityNormal",
    rules: {
      required: true,
    },
  },
  {
    name: "underlyingCondition",
    label: "Underlying Condition",
    type: Primitive.Text,
    service: "maternityNormal",
  },
  {
    name: "previousComplication",
    label: "Previous Complication",
    type: Primitive.Textbox,
    service: "maternityNormal",
  },
  /* ----- */
  // Maternity - Caesarean
  {
    name: "caesareanType",
    label: "Type of Delivery",
    type: ComplexPrimitive.Options,
    service: "maternityCesarean",
    options: [
      {
        label: "Elective",
        value: "Elective",
      },
      {
        label: "Emergency",
        value: "Emergency",
      },
    ],
    rules: {
      required: true,
    },
  },
  {
    name: "priorCaesarean",
    label: "Prior Caesarean",
    type: ComplexPrimitive.Options,
    service: "maternityCesarean",
    options: [
      {
        label: "First",
        value: "First",
      },
      {
        label: "Subsequent",
        value: "Subsequent",
      },
    ],
    rules: {
      required: true,
    },
  },
  {
    name: "dueDate",
    label: "Due Date",
    type: Primitive.Date,
    service: "maternityCesarean",
    rules: {
      required: true,
    },
  },
  {
    name: "underlyingCondition",
    label: "Underlying Condition",
    type: Primitive.Text,
    service: "maternityCesarean",
  },
  {
    name: "previousComplication",
    label: "Previous Complication",
    type: Primitive.Textbox,
    service: "maternityCesarean",
  },
  /* ----- */
  // Outpatient - Laboratory
  {
    name: "lines",
    label: "Tests",
    type: ComplexPrimitive.Array,
    service: "outpatientLaboratory",
    fields: [
      {
        name: "name",
        label: "Name",
        type: NetworkPrimitive.LabTest,
        rules: {
          required: true,
        },
      },
      {
        name: "quantity",
        label: "Quantity",
        type: Primitive.Number,
        defaultValue: 1,
        rules: {
          required: true,
        },
      },
      {
        name: "cost",
        label: "Unit Cost",
        type: Primitive.Number,
        rules: {
          required: true,
        },
      },
      {
        name: "total",
        label: "Total Cost",
        type: ComputedPrimitive,
        expression: "quantity * cost",
      },
      {
        name: "tag",
        type: Primitive.Text,
        defaultValue: "tests",
        hidden: true,
      },
    ],
    aggregate: {
      label: "Aggregate Cost",
      aggregation: Aggregation.Sum,
      field: "total",
    },
  },
  /* ----- */
  // Outpatient - Pharmacy
  {
    name: "lines",
    label: "Drugs",
    type: ComplexPrimitive.Array,
    service: "outpatientPharmacy",
    fields: [
      {
        name: "name",
        label: "Name",
        type: NetworkPrimitive.Drug,
        rules: {
          required: true,
        },
      },
      {
        name: "quantity",
        label: "Quantity",
        type: Primitive.Number,
        defaultValue: 1,
        rules: {
          required: true,
        },
      },
      {
        name: "cost",
        label: "Unit Cost",
        type: Primitive.Number,
        rules: {
          required: true,
        },
      },
      {
        name: "total",
        label: "Total Cost",
        type: ComputedPrimitive,
        expression: "quantity * cost",
      },
      {
        name: "tag",
        type: Primitive.Text,
        defaultValue: "drugs",
        hidden: true,
      },
    ],
    aggregate: {
      label: "Aggregate Cost",
      aggregation: Aggregation.Sum,
      field: "total",
    },
  },
  /* ----- */
  // Outpatient - Physiotherapy
  {
    name: "doctorName",
    label: "Name of Doctor",
    type: Primitive.Text,
    rules: {
      required: true,
    },
    service: "outpatientPhysiotherapy",
  },
  {
    name: "specialistReferral",
    label: "Specialist Referred From",
    type: Primitive.Text,
    rules: {
      required: true,
    },
    service: "outpatientPhysiotherapy",
  },
  {
    name: "numberOfSessions",
    label: "Sessions",
    type: Primitive.Number,
    service: "outpatientPhysiotherapy",
    rules: {
      min: 1,
      required: true,
    },
  },
  {
    name: "amountPerSession",
    label: "Amount per Session",
    type: Primitive.Number,
    service: "outpatientPhysiotherapy",
    rules: {
      min: 1,
    },
  },
  {
    name: "totalCost",
    label: "Total Cost",
    type: ComputedPrimitive,
    expression: "formatMoney(numberOfSessions * amountPerSession)",
    service: "outpatientPhysiotherapy",
  },
  /* ----- */
  // Outpatient - Radiology
  {
    name: "doctorName",
    label: "Name of Doctor",
    type: Primitive.Text,
    rules: {
      required: true,
    },
    service: "outpatientRadiology",
  },
  {
    name: "lines",
    label: "Tests",
    type: ComplexPrimitive.Array,
    service: "outpatientRadiology",
    fields: [
      {
        name: "name",
        label: "Name",
        type: ComplexPrimitive.Options,
        options: [
          {
            label: "X-Ray",
            value: "X-Ray",
          },
          {
            label: "MRI",
            value: "MRI",
          },
          {
            label: "CT Scan",
            value: "CT Scan",
          },
          {
            label: "Ultrasound",
            value: "Ultrasound",
          },
          {
            label: "Mammogram",
            value: "Mammogram",
          },
          {
            label: "PET Scan",
            value: "PET Scan",
          },
        ],
        placeholder: "Type of Radiology",
      },
      {
        name: "quantity",
        label: "Quantity",
        type: Primitive.Number,
        defaultValue: 1,
        rules: {
          required: true,
        },
      },
      {
        name: "cost",
        label: "Unit Cost",
        type: Primitive.Number,
        rules: {
          required: true,
        },
      },
      {
        name: "total",
        label: "Total Cost",
        type: ComputedPrimitive,
        expression: "quantity * cost",
      },
      {
        name: "tag",
        type: Primitive.Text,
        defaultValue: "radiologyCosting",
        hidden: true,
      },
    ],
    aggregate: {
      label: "Aggregate Cost",
      aggregation: Aggregation.Sum,
      field: "total",
    },
  },

  // NOTE: Assume each service has a field called "Notes"
];

export interface Line {
  name: string;
  numberOfTeeth?: number;
  cost: number;
  quantity?: number;
}

// type FieldKeys = typeof serviceFields[number]["name"]

/**
 * WARN: Update this when adding new fields.
 * TODO: Extract from serviceFields.
 * Fields maybe be undefined depending on the service
 * or if showIf hides the field.
 */
export interface FieldsBase {
  amountPerSession: number;
  caesareanType: string;
  causeOfIllness: string;
  conditionCongenital: boolean;
  conditionRecurrent: boolean;
  dateOfAdmission: string;
  dateOfFirstDiagnosis: string;
  dateOfProcedure: string;
  dentalConditionType: string;
  dentalProcedure: string;
  doctorName: string;
  dueDate: string;
  expedited: boolean;
  frameMake: string;
  natureOfTreatment: string;
  prescriptionReason: string;
  prescriptionType: string;
  previousComplication: string;
  priorCaesarean: string;
  procedureType: string;
  radiologyType: string;
  sessions: number;
  specialistReferral: string;
  surgeryDetails: string;
  surgeryRequired: boolean;
  typeOfSurgery: string;
  typeOfLens: string;
  underlyingCondition: string;
  lines: Line[];
  consultationFee: number;
  framesCost: number;
  lensesCost: number;
}

export type fieldsTyped = keyof Fields;
export type Fields = Partial<FieldsBase>;
