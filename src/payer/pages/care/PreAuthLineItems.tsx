import { PreAuth } from "~lib/api/types";
import NoData from "../../components/illustrations/NoData";
import EmptyState from "../../components/ui/EmptyState";
import TableDataItem from "../../components/ui/table/TableDataItem";
import TableHeaderItem from "../../components/ui/table/TableHeaderItem";

type PreAuthLineItemsProps = {
  preAuth: PreAuth;
};

export default function PreAuthLineItems({ preAuth }: PreAuthLineItemsProps) {
  const lineItems = preAuth.lines;

  const title = preAuth.service === "dental" ? "Dental Items" : "Line Items";

  return (
    <div>
      <p className="text-xl font-bold text-[#030712]">{title}</p>

      {lineItems.length === 0 ? (
        <div className="flex items-center justify-center ">
          <EmptyState
            illustration={<NoData size={200} />}
            message={{ title: "Service has no line items" }}
          />
        </div>
      ) : (
        <table className="mt-2 w-full">
          <thead className=" border-b">
            <tr>
              <TableHeaderItem item="Procedure" />

              <TableHeaderItem
                item={preAuth.service === "dental" ? "Number of Teeth" : "Quantity"}
              />
              <TableHeaderItem item="Total Cost" />
            </tr>
          </thead>
          <tbody>
            {lineItems.map((item, i) => (
              <tr key={i}>
                <TableDataItem item={item.name} />
                <TableDataItem
                  item={preAuth.service === "dental" ? item.numberOfTeeth : item.quantity}
                />
                <TableDataItem item={item.cost} />
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
}
