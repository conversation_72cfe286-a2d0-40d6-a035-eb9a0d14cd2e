import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router";
import ModalBasic from "../../components/ModalBasic";
import UserService from "../../services/UserService";
import { RootState } from "../../store";

function PendingPreAuthItem(props: any) {
  const [payerEditModalOpen, setEditPayerModalOpen] = useState(false);
  const navigate = useNavigate();
  const [open, setOpen] = useState(false);
  const [emailPresent, setEmailPresent] = useState(false);
  const dispatch = useDispatch();

  return (
    <tbody
      className="divide-y-0 even:bg-light-blue-50 
    hover:bg-light-blue-100 hover:text-sky-700 scale-10 delay-150 "
    >
      <tr className="">
        <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
          <div className=" font-medium rounded-full text-left px-2.5 py-0.5 uppercase">
            {props.memberNumber}
          </div>
        </td>
        <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
          <div className=" font-medium rounded-full text-left px-2.5 py-0.5 uppercase">
            {props.memberName}
          </div>
        </td>
        <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
          <div
            className=" font-medium rounded-full text-left px-2.5 py-0.5 sm:w-64 uppercase truncate  ..."
            title={props.benefitName}
          >
            {props.benefitName}
          </div>
        </td>

        <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap w-px">
          <div className="flex items-center justify-evenly">
            <button
              className={`bg-transparent hover:text-white  transform flex rounded border border-red-700 ml-1
               hover:bg-red-600  text-red-700  py-1 px-2`}
              aria-expanded={open}
              aria-controls={`description-${props.id}`}
              title="Cancel Visit"
              // onClick={() => setIsCancelModalOpen(true)}
            >
              Cancel
            </button>{" "}
          </div>
        </td>
      </tr>

      {/* <tr
        id={`description-${props.id}`}
        role="region"
        className={`${!open && "hidden"} border-none`}
      >
        <td colSpan={10} className="mx-5 px-2 first:pl-5 last:pr-5 py-2">
          <div className="flex items-center bg-transparent p-3 -mt-1 border-b-2">
            <div className="flex">
              <button
                className="text-light-blue-700 rounded px-3 text-sm font-bold flex cursor-pointer"
                onClick={() => setIsLineItemModalOpen(true)}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M4 6h16M4 12h16M4 18h7"
                  />
                </svg>
                <div id="text">Add Line Item</div>
              </button>
              <button
                className="text-light-blue-700 rounded px-3 text-sm font-bold flex cursor-pointer ml-2"
                onClick={() => setIsCancelModalOpen(true)}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                <div id="text">Add Diagnosis</div>
              </button>

              <button
                className="text-light-blue-700 rounded px-3 text-sm font-bold flex cursor-pointer ml-2"
                onClick={() => props.openInvoiceModal(true)}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                  />
                </svg>
                <div id="text">Add Notes</div>
              </button>
            </div>
          </div>
        </td>
        <ModalBasic
          id="bill-item"
          modalOpen={isLineItemModalOpen}
          onClose={() => setIsLineItemModalOpen(false)}
          title="Bill Item"
        >
          <BillingForm
            open={isLineItemModalOpen}
            onClose={() => setIsLineItemModalOpen(false)}
          />
        </ModalBasic>

        <ModalBasic
          id="Diagnosis item"
          modalOpen={isCancelModalOpen}
          onClose={() => setIsCancelModalOpen(false)}
          title="Diagnosis Item"
        >
          <DiagnosisForm
            open={isCancelModalOpen}
            onClose={() => setIsCancelModalOpen(false)}
          />
        </ModalBasic>
      </tr> */}

      {/* <ModalBasic
        id="Diagnosis item"
        modalOpen={isCancelModalOpen}
        onClose={() => setIsCancelModalOpen(false)}
        title="Cancel Visit"
      >
        <CancelVisitForm
          open={isCancelModalOpen}
          onClose={() => setIsCancelModalOpen(false)}
          visitNumber={props.visitNumber}
        />
      </ModalBasic> */}
    </tbody>
  );
}
export default PendingPreAuthItem;
