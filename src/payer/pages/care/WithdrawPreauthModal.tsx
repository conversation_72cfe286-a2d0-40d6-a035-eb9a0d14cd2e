import { XMarkIcon } from "@heroicons/react/24/outline";
import { FormEvent, useState } from "react";
import { toast } from "react-toastify";
import { useWithdrawPreauthMutation } from "~lib/api";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { isErrorWithMessage, isFetchBaseQueryError, queryError, responseError } from "~lib/utils";
import Button from "../../components/ui/Button";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import Text from "../../components/ui/typography/Text";
import UserService from "../../services/UserService";

type Props = {
  preAuthId: number | undefined;
  onClose: () => void;
  isOpen: boolean;
};

export default function WithdrawPreauthModal({ preAuthId, onClose, isOpen }: Props) {
  const [reason, setReason] = useState("");

  const username = UserService.getUsername();

  const [withdrawPreauth, { isLoading }] = useWithdrawPreauthMutation();

  async function handleSubmit(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();

    if (!reason.trim()) {
      toast.error("Please provide a reason");
      return;
    }

    const request = {
      reason,
      actionBy: username,
      id: preAuthId as number,
    };

    try {
      const response = await withdrawPreauth(request);
      const message = responseError(response);

      if (message) {
        throw new Error(message);
      }

      toast.success("Preauth withdrawn successfully");
      setReason("");
      onClose();
    } catch (error) {
      let message = "Something went wrong. Please try again.";
      if (isFetchBaseQueryError(error) || isErrorWithMessage(error)) {
        message = queryError(error) || message;
      }

      toast.error(message);
      console.error(error);
    }
  }

  return (
    <DialogWrapper maxWidth="max-w-[1000px] py-6 px-12" onClose={onClose} show={isOpen}>
      <div className=" flex justify-between  ">
        <Text variant="subheading" className="grow text-center text-2xl">
          Pre-authorization Withdrawal
        </Text>

        <button onClick={onClose}>
          <XMarkIcon width={20} strokeWidth={2} />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="mt-6">
        <Text variant="description" className="italic">
          Please add a reason for pre-authorization withdrawal.
        </Text>

        <fieldset className="mb-8 mt-6 rounded-lg border border-[#D1D5DB] px-2">
          <legend className="text-sm">
            <span>Reason</span> <span className="text-error">*</span>
          </legend>

          <textarea
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            name="reason"
            rows={4}
            className="h-full w-full resize-none border-none outline-none"
            placeholder="Add a reason for the withdrawal..."
          />
        </fieldset>

        <div className="flex justify-end gap-8 ">
          <Button onClick={onClose} type="button" variant="outlined">
            Cancel
          </Button>
          <Button variant="destructive" type="submit" disabled={isLoading}>
            {isLoading && <LoadingIcon className="h-4 w-4 text-white" />}
            <span>Withdraw</span>
          </Button>
        </div>
      </form>
    </DialogWrapper>
  );
}
