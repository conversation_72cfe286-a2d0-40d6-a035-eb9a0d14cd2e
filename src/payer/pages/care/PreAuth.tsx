import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import UserService from "../../services/UserService";
import { RootState } from "../../store";
import { setRefreshHistory } from "../../store/dashboard/actions";
import {
  getAllPreAuths,
  getClosedReiumbursement,
  getMemberBenefits,
  getPendingPreAuths,
  searchMember,
  setInputInvoiceDateReimbursement,
  setInputProviderReimbursementName,
  setSelectedMember,
} from "../../store/members/actions";
import VisitPreAuthPageItem from "./VisitPreAuthPageItem";
import AllPreAuthItem from "./AllPreAuthItem";
import PreAuthTransactionPanel from "../../components/PreAuthTransactionPanel";

function PreAuth({ getAllPreAuths }) {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [openTab, setOpenTab] = React.useState(1);
  const [color, setColor] = React.useState("blue");
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [preSearch, setPreSearch] = useState("");
  const [providerInput, setProviderInput] = useState("");
  const [searchloading, setSearchLoading] = useState(false);
  const [searchStatus, setSearchStatus] = useState(false);
  const [search, setSearch] = useState("");
  const [searchResultVisible, setSearchResultVisible] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [reason, setReason] = useState("");
  const [offSetValue, setOffsetValue] = useState(0);
  const [provider, setProvider] = useState(false);
  const [providerValue, setProviderValue] = useState(0);
  const [refreshTableKey, setRefreshTableKey] = useState(0);
  const [invoiceDate, setInvoiceDate] = useState(null);
  const [isPreauthOpen, setIsPreauthOpen] = useState(false);
  const [preauthtransactionPanelOpen, setPreauthTransactionPanelOpen] = useState(false);

  const location = useLocation();

  const memberSearchResult = useSelector((state: RootState) => state.memberInfo.memberSearchResult);
  console.log(providerValue);

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    setSearchStatus(true);
    if (e.key === "Enter") {
      setSearchLoading(true);
      setSearch(preSearch);
      dispatch(searchMember(payerId, preSearch));
      setSearchResultVisible(true);
    } else {
      if (e.keyCode === 27) {
        setSearch("");
        setSearchResultVisible(true);
      }
    }
  };
  useEffect(() => {
    if (preSearch.length > 2) {
      setSearchLoading(true);
      setSearch(preSearch);
      searchMember(payerId, preSearch);
      setSearchResultVisible(true);
    }
  }, [preSearch]);

  const handleMemberClick = (e: React.MouseEvent, member: any) => {
    dispatch(setInputProviderReimbursementName(providerInput));
    dispatch(setInputInvoiceDateReimbursement(invoiceDate));
    dispatch(setSelectedMember(member));
    dispatch(getMemberBenefits(member.id));
    setIsOpen(true);
  };

  useEffect(() => {
    dispatch(getClosedReiumbursement({ payerId, offSetValue }));
    setRefreshTableKey((oldKey) => oldKey + 1);
  }, [offSetValue]);

  const activeVisits = useSelector((state: RootState) => state.memberInfo.activeVisits);
  const handlePaginationChange = (e) => {
    console.log(e);
    setOffsetValue(e);
  };

  const closedVisits = useSelector((state: RootState) => state.memberInfo.closedVisits);

  const closedVisitsTotalElements = useSelector(
    (state: RootState) => state.memberInfo.closedVisitsTotalElements,
  );
  const closedVisitsTotalPages = useSelector(
    (state: RootState) => state.memberInfo.closedVisitsTotalPages,
  );
  const closedVisitsTotalPageNumber = useSelector(
    (state: RootState) => state.memberInfo.closedVisitsTotalPageNumber,
  );
  useEffect(() => {
    setRefreshTableKey((oldKey) => oldKey + 1);
  }, [closedVisits]);

  const payerName = useSelector((state: RootState) => state.payers.payer.name);
  const options = {
    mode: "single",
    static: true,
    monthSelectorType: "static",
    dateFormat: "Y-m-d",
    defaultDate: null,
    enableTime: false,
    maxDate: "today",

    // altInput: true,
    prevArrow:
      '<svg class="fill-current" width="7" height="11" viewBox="0 0 7 11"><path d="M5.4 10.8l1.4-1.4-4-4 4-4L5.4 0 0 5.4z" /></svg>',
    nextArrow:
      '<svg class="fill-current" width="7" height="11" viewBox="0 0 7 11"><path d="M1.4 10.8L0 9.4l4-4-4-4L1.4 0l5.4 5.4z" /></svg>',
  };
  console.log(invoiceDate);

  const pendingPreAuths = useSelector((state: RootState) => state.memberInfo.pendingPreAuths);

  const allPreAuths = useSelector((state: RootState) => state.memberInfo.allPreAuths);
  const payerId = UserService.getPayer().tokenParsed.payerId;
  useEffect(() => {
    if (openTab === 1) {
      dispatch(getPendingPreAuths({ payerId }));
    }

    if (openTab === 2) {
      getAllPreAuths({ payerId }).then(() => {
        dispatch(setRefreshHistory(Math.random));
      });
    }
  }, [location.state, openTab]);

  const refreshKey = useSelector((state: RootState) => state.memberInfo.refreshKey);

  return (
    <div className="flex h-full flex-grow overflow-hidden bg-gray-50">
      <div className="relative flex flex-1 flex-col overflow-y-auto overflow-x-hidden">
        <main className="mx-10 justify-center rounded-md bg-white pt-10 shadow-md">
          <ul className="mb-0 flex w-10/12 list-none flex-row flex-wrap  pt-2 " role="tablist">
            <li className=" w-64 flex-initial  "></li>
            <li
              className={
                "-mb-px mr-0 flex-auto last:mr-0 " +
                (openTab === 1 ? " border-  border-b-4" + color + "-600" : " border-b-gray-600")
              }
            >
              <a
                className={
                  "block rounded px-1 py-3  text-lg font-bold leading-normal " +
                  (openTab === 1
                    ? "text-" + color + "-600" + " border-b-" + color + "-600"
                    : "text-gray-700")
                }
                onClick={(e) => {
                  e.preventDefault();
                  setOpenTab(1);
                }}
                data-toggle="tab"
                href="#link1"
                role="tablist"
              >
                <i className="fas fa-space-shuttle mr-1 text-base"></i> Pre-Auth Request(s)
              </a>
            </li>
            <li
              className={
                "-mb-px mr-0 flex-auto last:mr-0 " +
                (openTab === 2 ? " border-  border-b-4" + color + "-600" : " border-b-gray-600")
              }
            >
              <a
                className={
                  "flex rounded px-1  py-3 text-lg  font-bold leading-normal " +
                  (openTab === 2
                    ? "text-" + color + "-600" + " border-b-" + color + "-600"
                    : "text-gray-700")
                }
                onClick={(e) => {
                  e.preventDefault();
                  setOpenTab(2);
                }}
                data-toggle="tab"
                href="#link2"
                role="tablist"
              >
                <i className="fas fa-cog mr-1 text-base"></i> Pre-Auth History
              </a>
            </li>
          </ul>
          <div className="relative flex w-full min-w-0 flex-col break-words rounded-md   bg-white ">
            <div className="flex-auto px-4">
              <div className="tab-content tab-space  mb-10  ">
                <div className="mx-auto w-full px-4 py-0 sm:px-6 lg:px-8">
                  <div className={openTab === 1 ? "block" : "hidden"} id="link1">
                    <div className="mx-auto flex items-center justify-center "></div>
                    <div className="grid grid-cols-10 gap-2  rounded-md   px-4 py-4">
                      <div className="col-span-1"></div>
                      <div className="col-span-10">
                        {/* Table */}
                        <div className="overflow-x-auto">
                          <table className="w-full table-auto ">
                            {/* Table header */}
                            <thead className="border-b border-sky-700  text-xs   font-semibold text-gray-500">
                              <tr>
                                <th className="mx-5 whitespace-nowrap border-r-2 border-gray-50 px-2 py-3 first:pl-5 last:pr-5">
                                  <div className="flex text-left  font-semibold uppercase">
                                    Visit Number
                                  </div>
                                </th>
                                <th className="justify-items-center  whitespace-nowrap border-r-2 border-gray-50 px-2 py-3">
                                  <div className="flex text-left  font-semibold  uppercase">
                                    REFERENCE No.
                                  </div>
                                </th>
                                <th className="justify-items-center  whitespace-nowrap border-r-2 border-gray-50 px-2 py-3">
                                  <div className="flex text-left  font-semibold  uppercase">
                                    Scheme
                                  </div>
                                </th>
                                <th className="justify-items-center  whitespace-nowrap border-r-2 border-gray-50 px-2 py-3">
                                  <div className="flex text-left  font-semibold  uppercase">
                                    Request Type
                                  </div>
                                </th>
                                <th className="justify-items-center  whitespace-nowrap border-r-2 border-gray-50 px-2 py-3">
                                  <div className="flex text-left  font-semibold  uppercase">
                                    Service
                                  </div>
                                </th>

                                <th className="justify-items-center  whitespace-nowrap border-r-2 border-gray-50 px-2 py-3">
                                  <div className="flex text-left  font-semibold  uppercase">
                                    Member Number
                                  </div>
                                </th>
                                <th className="justify-items-center  whitespace-nowrap border-r-2 border-gray-50 px-2 py-3">
                                  <div className="flex text-left  font-semibold  uppercase">
                                    Member Name
                                  </div>
                                </th>
                                <th className="justify-items-center  whitespace-nowrap border-r-2 border-gray-50 px-2 py-3">
                                  <div className="flex text-left  font-semibold  uppercase">
                                    Benefit Name
                                  </div>
                                </th>
                                <th className="justify-items-center  whitespace-nowrap border-r-2 border-gray-50 px-2 py-3">
                                  <div className="flex text-left  font-semibold  uppercase">
                                    Status
                                  </div>
                                </th>
                              </tr>
                            </thead>
                            {/* Table body */}
                            {pendingPreAuths &&
                              pendingPreAuths.map((visitsList, index) => {
                                return (
                                  <VisitPreAuthPageItem
                                    key={index}
                                    visit={visitsList}
                                    setIsPreauthOpen={setIsPreauthOpen}
                                  />
                                );
                              })}
                          </table>
                          {pendingPreAuths?.length < 1 ? (
                            <p className="mt-2 flex justify-center">No Pending Pre-auth(s)</p>
                          ) : (
                            ""
                          )}
                        </div>
                      </div>
                      <div className="col-span-1"></div>
                    </div>
                  </div>
                </div>
                {/* All Preauths */}
                <div className=" mx-auto w-full px-4 py-0 sm:px-6 lg:px-8">
                  <div className={openTab === 2 ? "block" : "hidden"} id="link2">
                    <div className="mx-auto flex items-center justify-center "></div>
                    <div className="grid grid-cols-10 gap-2  rounded-md   px-4 py-4">
                      <div className="col-span-1"></div>
                      <div className="col-span-10">
                        {/* Table */}
                        <div className="overflow-x-auto">
                          <table className="w-full table-auto ">
                            {/* Table header */}
                            <thead className="border-b border-sky-700  text-xs   font-semibold text-gray-500">
                              <tr>
                                <th className="mx-5 whitespace-nowrap border-r-2 border-gray-50 px-2 py-3 first:pl-5 last:pr-5">
                                  <div className="flex text-left  font-semibold uppercase">
                                    Visit Number
                                  </div>
                                </th>
                                <th className="justify-items-center  whitespace-nowrap border-r-2 border-gray-50 px-2 py-3">
                                  <div className="flex text-left  font-semibold  uppercase">
                                    REFERENCE No.
                                  </div>
                                </th>
                                <th className="justify-items-center  whitespace-nowrap border-r-2 border-gray-50 px-2 py-3">
                                  <div className="flex text-left  font-semibold  uppercase">
                                    Scheme
                                  </div>
                                </th>
                                <th className="justify-items-center  whitespace-nowrap border-r-2 border-gray-50 px-2 py-3">
                                  <div className="flex text-left  font-semibold  uppercase">
                                    Request Type
                                  </div>
                                </th>
                                <th className="justify-items-center  whitespace-nowrap border-r-2 border-gray-50 px-2 py-3">
                                  <div className="flex text-left  font-semibold  uppercase">
                                    Service
                                  </div>
                                </th>

                                <th className="justify-items-center  whitespace-nowrap border-r-2 border-gray-50 px-2 py-3">
                                  <div className="flex text-left  font-semibold  uppercase">
                                    Member Number
                                  </div>
                                </th>
                                <th className="justify-items-center  whitespace-nowrap border-r-2 border-gray-50 px-2 py-3">
                                  <div className="flex text-left  font-semibold  uppercase">
                                    Member Name
                                  </div>
                                </th>
                                <th className="justify-items-center  whitespace-nowrap border-r-2 border-gray-50 px-2 py-3">
                                  <div className="flex text-left  font-semibold  uppercase">
                                    Benefit Name
                                  </div>
                                </th>
                                <th className="justify-items-center  whitespace-nowrap border-r-2 border-gray-50 px-2 py-3">
                                  <div className="flex text-left  font-semibold  uppercase">
                                    Status
                                  </div>
                                </th>
                              </tr>
                            </thead>
                            {/* Table body */}

                            {allPreAuths &&
                              allPreAuths.map((visitsList, index) => {
                                return (
                                  <AllPreAuthItem
                                    id={visitsList.id}
                                    visitNumber={visitsList.id}
                                    visit={visitsList}
                                    preauthtransactionPanelOpen={preauthtransactionPanelOpen}
                                    setPreauthTransactionPanelOpen={setPreauthTransactionPanelOpen}
                                  />
                                );
                              })}
                          </table>
                          {allPreAuths?.length < 1 ? (
                            <p className="mt-2 flex justify-center">No PreAuths</p>
                          ) : (
                            ""
                          )}
                        </div>
                      </div>
                      <div className="col-span-1"></div>
                    </div>

                    <div className="mt-8"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* <div className="mx-auto flex justify-center items-center ">{}</div>
            <div className="grid grid-cols-12 gap-1 bg-white  rounded-sm px-4 py-4">
              <div className=" px-5">
                
              </div>
            </div> */}

            <div className=""></div>
          </div>
        </main>
      </div>
      <PreAuthTransactionPanel
        preauthtransactionPanelOpen={preauthtransactionPanelOpen}
        setPreauthTransactionPanelOpen={setPreauthTransactionPanelOpen}
      />
    </div>
  );
}

const mapDispatchToProps = (dispatch) => ({
  getAllPreAuths: (page) => dispatch(getAllPreAuths(page)),
});
export default connect(null, mapDispatchToProps)(PreAuth);
