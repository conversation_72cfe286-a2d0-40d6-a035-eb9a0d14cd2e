import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router";
import ModalBasic from "../../components/ModalBasic";
import UserService from "../../services/UserService";
import { RootState } from "../../store";
import { setSelectedPreAuthItem } from "../../store/members/actions";

interface Props {
  visit?: any;
  id?: any;
  visitNumber?: any;
  setPreauthTransactionPanelOpen?: any;
  preauthtransactionPanelOpen?: any;
}
export const AllPreAuthItem: React.FC<Props> = (props) => {
  const [payerEditModalOpen, setEditPayerModalOpen] = useState(false);
  const navigate = useNavigate();
  const [open, setOpen] = useState(false);
  const [emailPresent, setEmailPresent] = useState(false);
  const dispatch = useDispatch();

  const [isLineItemModalOpen, setIsLineItemModalOpen] = useState(false);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);

  const handlePreAuthItemClick = (e) => {
    e.stopPropagation();
    props.setPreauthTransactionPanelOpen(true);
    console.log("first");
    dispatch(setSelectedPreAuthItem(props));
  };

  return (
    <tbody
      className="divide-y-0 even:bg-light-blue-50 
    hover:bg-light-blue-100 hover:text-sky-700 scale-10 delay-150 "
    >
      <tr className="cursor-pointer" onClick={(e) => handlePreAuthItemClick(e)}>
        <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
          <div className="flex items-center text-slate-800">
            {" "}
            <div className="font-medium text-slate-800 text-center items-center px-2.5 ">
              {props.visit.visitNumber}
            </div>
          </div>
        </td>

        <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
          <div className="flex items-center text-slate-800">
            {" "}
            <div className="font-medium text-slate-800 text-center items-center px-2.5 ">
              {props.visit.reference}
            </div>
          </div>
        </td>
        <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
          <div className="flex items-center text-slate-800">
            {" "}
            <div className="font-medium text-slate-800 text-center items-center px-2.5 truncate  ... ">
              {props.visit.schemeName}
            </div>
          </div>
        </td>
        <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
          <div className="flex items-center text-slate-800">
            {" "}
            <div className="font-medium text-slate-800 text-center items-center px-2.5 ">
              {props.visit.requestType}
            </div>
          </div>
        </td>
        <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
          <div className="flex items-center text-slate-800">
            {" "}
            <div className="font-medium text-slate-800 text-center items-center px-2.5 ">
              {props.visit.service}
            </div>
          </div>
        </td>
        <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
          <div className=" font-medium rounded-full text-left px-2.5 py-0.5 uppercase">
            {props.visit.memberNumber}
          </div>
        </td>
        <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
          <div className=" font-medium rounded-full text-left px-2.5 py-0.5 uppercase">
            {props.visit.memberName}
          </div>
        </td>
        <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
          <div
            className=" font-medium rounded-full text-left px-2.5 py-0.5  uppercase truncate  ..."
            title={props.visit.benefitName}
          >
            {props.visit.benefitName}
          </div>
        </td>
        <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
          <div
            className=" font-medium rounded-full text-left px-2.5 py-0.5 sm:w-64 uppercase truncate  ..."
            title={props.visit.status}
          >
            {props.visit.status}
          </div>
        </td>
      </tr>
    </tbody>
  );
};
export default AllPreAuthItem;
