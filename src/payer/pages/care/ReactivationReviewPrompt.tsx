import { XMarkIcon } from "@heroicons/react/24/outline";
import Button from "../../components/ui/Button";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import Text from "../../components/ui/typography/Text";

type ReactivatePromptProps = {
  onClose(): void;
  onProceed(): void;
  isOpen: boolean;
};
export default function ReactivationReviewPrompt({
  onClose,
  isOpen,
  onProceed,
}: ReactivatePromptProps) {
  return (
    <DialogWrapper className="max-w-[900px]" onClose={onClose} show={isOpen}>
      <div className="flex justify-between border-b border-gray-300 px-8 py-4">
        <Text className="text-2xl" variant="subheading">
          Pre-authorization Reactivation
        </Text>

        <button onClick={onClose}>
          <XMarkIcon className="w-5" strokeWidth={2} />
        </button>
      </div>

      <div className="flex flex-col gap-2 px-12 py-6">
        <Text className="text-lg font-medium" variant="paragraph">
          Review Before You Reactivate
        </Text>
        <Text className="text-lg" variant="description">
          Kindly review the details of the expired pre-authorization request before proceeding with
          reactivation.
        </Text>
      </div>

      <div className="flex justify-end gap-6 px-8 py-8">
        <Button onClick={onClose} variant="outlined">
          Cancel
        </Button>
        <Button onClick={onProceed}>Proceed</Button>
      </div>
    </DialogWrapper>
  );
}
