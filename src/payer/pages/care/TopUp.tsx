import { XMarkIcon } from "@heroicons/react/24/outline";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { z } from "zod";
import { usePreAuthorizationTopUpByPayerMutation } from "~lib/api";
import Button from "../../components/ui/Button";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import Text from "../../components/ui/typography/Text";
import { AUTHENTICATED_USER } from "../../lib/payer-constants";

type Props = {
  preAuthId: number;
  onClose: () => void;
  isOpen: boolean;
};

const topUpSchema = z.object({
  amount: z.coerce.number().min(1, "Amount must be more than 0"),
  reason: z.string(),
});

type TopUpSchema = z.infer<typeof topUpSchema>;

export default function TopUp({ isOpen, onClose, preAuthId }: Props) {
  const [topUpPreAuth] = usePreAuthorizationTopUpByPayerMutation();

  const {
    handleSubmit,
    formState: { isSubmitting, errors },
    register,
  } = useForm<TopUpSchema>({
    resolver: zodResolver(topUpSchema),
  });

  const username = AUTHENTICATED_USER.getUserName();

  async function onValid({ amount, reason }: TopUpSchema) {
    try {
      const payload = await topUpPreAuth({
        pathParameters: { preAuthId },
        body: { amount, reason, actionBy: username },
      }).unwrap();

      toast.success(payload.msg);
      onClose();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Top Up Failed";

      toast.error(errorMessage);
      console.error(errorMessage, error);
    }
  }

  return (
    <DialogWrapper onClose={onClose} show={isOpen} className="max-w-[900px] px-12 py-4">
      <div className="flex items-center justify-between">
        <Text variant="subheading" className="grow text-center text-2xl">
          Top Up Request
        </Text>
        <button onClick={onClose}>
          <XMarkIcon className="w-6" strokeWidth={2} />
        </button>
      </div>

      <Text variant="description" className="mt-6 italic">
        Please enter the top-up amount and specify the reason for your request.
      </Text>

      <form onSubmit={handleSubmit(onValid)} className="mt-4 flex flex-col">
        <div className="flex flex-col">
          <label className="text-sm">
            <span>Top Up Amount </span>
            <span className="font-medium text-red-500">*</span>
          </label>
          <input
            {...register("amount")}
            required
            className="mt-2 w-1/3 rounded-lg border-gray-300"
            type="number"
            min={1}
          />
          {errors.amount && <p className="text-sm text-red-500">{errors.amount.message}</p>}
        </div>

        <div>
          <fieldset className="mt-6 rounded-lg border border-gray-300 px-2">
            <legend className="text-sm">
              <span>Reason </span>
              <span className="font-medium text-red-500">*</span>
            </legend>
            <textarea
              {...register("reason")}
              required
              rows={4}
              className="w-full resize-none rounded-lg border-none text-sm outline-none"
              placeholder="Add a reason for the top up request..."
            />
          </fieldset>
          {errors.reason && <p className="text-sm text-red-500">{errors.reason.message}</p>}
        </div>

        <div className="mb-6 mt-8 flex justify-end gap-8">
          <Button
            type="reset"
            className=" border border-gray-200 px-4 py-2 font-medium text-gray-500 enabled:hover:border-gray-300 disabled:opacity-80"
            onClick={onClose}
            variant="outlined"
          >
            Cancel
          </Button>
          <Button
            disabled={isSubmitting}
            type="submit"
            className="flex items-center gap-2  px-4 py-2 font-medium text-white enabled:hover:bg-blue-700 disabled:opacity-80"
          >
            {isSubmitting ? "Loading..." : "Confirm"}
          </Button>
        </div>
      </form>
    </DialogWrapper>
  );
}
