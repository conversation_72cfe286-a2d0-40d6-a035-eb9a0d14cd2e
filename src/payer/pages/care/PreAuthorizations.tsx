import React, { useEffect, useMemo, useState } from "react";
import DateInput from "react-flatpickr";
import { toast } from "react-toastify";
import { useSearchPreauthQuery } from "~lib/api";
import {
  PreAuth,
  PreAuthStatus,
  preauthStatusLabels,
  RequestType,
  requestTypeLabels,
  UserNotificationCategory,
} from "~lib/api/types";
import { Empty } from "~lib/components";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { baseUrl } from "~lib/constants";
import {
  useGetMembershipProvidersQuery,
  useGetMembershipSchemesQuery,
} from "../../api/features/membershipApi";
import NoData from "../../components/illustrations/NoData";
import { requestTypeIcon } from "../../components/RequestTypeIcon";
import EmptyState from "../../components/ui/EmptyState";
import AutoSuggestSelect from "../../components/ui/input/AutoSuggestSelect";
import { Select } from "../../components/ui/input/Select";
import MainWrapper from "../../components/ui/MainWrapper";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import PrimaryPagination from "../../components/ui/pagination/PrimaryPagination";
import TableHeaderItem from "../../components/ui/table/TableHeaderItem";
import { useNotifications } from "../../context/NotificationsContext";
import useGetPayerFilterValues, { FilterValueType } from "../../hooks/useGetPayerFilterValues";
import { APIResponseWithContent } from "../../lib/types/apiResponseGeneric";
import {
  PreAuthCriteriaSelect,
  PreAuthFilterCriteria,
  Preauthorization,
} from "../../lib/types/care/preAuth";
import {
  MembershipProvider,
  MembershipProvidersQuery,
} from "../../lib/types/membership/memberProvider";
import { MembershipScheme } from "../../lib/types/membership/memberScheme";
import UserService from "../../services/UserService";
import { useAppSelector } from "../../store/hooks";
import clsx, { dateFlatPickrOptions } from "../../utils";
import { handleTryCatchError } from "../../utils/handleTryCatchError";
import useDebounce from "../../utils/useDebounce";
import { usePreAuthorizationPermissions } from "../../hooks/care/usePreAuthorizationPermissions";
import PreAuthApprovalForm from "./pre-auth-approval-form/PreAuthApprovalForm";
import PreAuthChangeLog from "./PreAuthChangeLog";
import PreAuthTableRow from "./PreAuthTableRow";
import ReactivatePreauth from "./ReactivatePreauth";
import ReactivationReviewPrompt from "./ReactivationReviewPrompt";
import RevokePreauthModal from "./RevokePreauthModal";
import TopUp from "./TopUp";
import TopUpPrompt from "./TopUpPrompt";
import WithdrawPreauthModal from "./WithdrawPreauthModal";
import WithdrawPreAuthPrompt from "./WithdrawPreAuthPrompt";

export interface Inputs {
  query: string;
  status: PreAuthStatus | typeof PENDING_AND_DRAFT;
  payerId: string;
  startDate: string;
  endDate: string;
}

const PENDING_AND_DRAFT = "PENDING,DRAFT";
const DAY_CASE = "DAY CASE";
const ADMISSION = "ADMISSION";

export enum PageModal {
  REVIEW_PREAUTH,
  REACTIVATE_PREAUTH,
  WITHDRAW_PREAUTH,
  WITHDRAW_PREAUTH_PROMPT,
  PREAUTH_LOGS,
  REACTIVATION_REVIEW_PROMPT,
  TOP_UP_PROMPT,
  TOP_UP,
}

export enum PreAuthJourney {
  ReactivatePreauth,
  TopUp,
  ReviewTopUp,
  WithdrawPreauth,
}

export default function PreAuthorizations() {
  const { activeNotification, setActiveNotification } = useNotifications();
  const {
    hasViewPermissionForRequestType,
    hasApprovePermissionForRequestType,
    hasTopUpPermissionForRequestType,
    allowedBenefitTypes
  } = usePreAuthorizationPermissions();

  const [idOfPreAuthToBeCanceled, setIdOfPreAuthToBeCanceled] = useState<number | null>(null);

  const [activeModal, setActiveModal] = useState<PageModal | undefined>();
  const [selectedPreauthId, setSelectedPreauthId] = useState<number | undefined>();
  const [preAuthJourney, setPreAuthJourney] = useState<PreAuthJourney | null>(null);

  // handle active notification
  useEffect(() => {
    if (!activeNotification) return;

    async function handleActiveTopUpNotification() {
      try {
        const response = await fetch(
          `${baseUrl}/api/v1/preauthorization/search?topUpId=${activeNotification?.refId}&page=1&size=1`,
        );

        if (!response.ok) {
          const errorMessage =
            `Request failed with status ${response.status} ${response.statusText || ""}`.trim();
          throw new Error(errorMessage);
        }

        const payload = (await response.json()) as APIResponseWithContent<Preauthorization>;

        const preauthId = payload.data.content[0]?.id;

        if (!preauthId) throw new Error("Something went wrong");

        setSelectedPreauthId(preauthId);
        setPreAuthJourney(PreAuthJourney.ReviewTopUp);
        setActiveModal(PageModal.REVIEW_PREAUTH);
      } catch (error) {
        handleTryCatchError(error);
      }
    }

    async function handleActivePreAuthRequestNotification() {
      setSelectedPreauthId(activeNotification?.refId);
      setActiveModal(PageModal.REVIEW_PREAUTH);
    }

    switch (activeNotification.category) {
      case UserNotificationCategory.TOP_UP:
        handleActiveTopUpNotification();
        break;
      case UserNotificationCategory.PRE_AUTH_REQUEST:
        handleActivePreAuthRequestNotification();
        break;
    }
  }, [activeNotification]);

  // Form states
  const [selectedProviderId, setSelectedProviderId] = useState("");
  const [providerSearchQuery, setProviderSearchQuery] = useState("");
  const [selectedSchemeId, setSelectedSchemeId] = useState("");
  const [schemeSearchQuery, setSchemeSearchQuery] = useState("");

  const [page, setPage] = useState(1);
  const [size, setSize] = useState<number>(10);
  const [payerProviders, setPayerProviders] = useState<MembershipProvider[]>([]);
  const selectedPreAuthScheme = useAppSelector((state) => state.preAuth.selectedPreAuthScheme);
  const selectedPreAuthProvider = useAppSelector((state) => state.preAuth.selectedPreAuthProvider);

  const initialFilterValues = useMemo(
    () => ({
      query: "",
      status: PENDING_AND_DRAFT,
      providerId: "",
      startDate: "",
      endDate: "",
      planId: selectedPreAuthScheme?.id,
    }),
    [selectedPreAuthScheme?.id],
  );

  const [filters, setFilters] = useState(initialFilterValues);

  const [selectedBenefitTypes, setSelectedBenefitTypes] = useState<RequestType[]>([]);

  useEffect(() => {
    if (allowedBenefitTypes.length > 0) {
      setSelectedBenefitTypes(allowedBenefitTypes);
    }
  }, [allowedBenefitTypes]);

  function handleSearchQueryChange(e: React.ChangeEvent<HTMLInputElement>) {
    setFilters({ ...initialFilterValues, query: e.target.value, status: "" });
    setSelectedProviderId("");
    setSelectedSchemeId("");
    setPage(1);
    setSelectedBenefitTypes(allowedBenefitTypes);
  }

  useEffect(() => {
    setPage(1);
  }, [filters, selectedProviderId, selectedSchemeId]);

  function handleContinueJourneyClick() {
    switch (preAuthJourney) {
      case PreAuthJourney.ReactivatePreauth:
        reactivatePreAuth(selectedPreauthId as number);
        break;
      case PreAuthJourney.TopUp:
        topUpPreAuth(selectedPreauthId as number);
        break;
      case PreAuthJourney.WithdrawPreauth:
        setActiveModal(PageModal.WITHDRAW_PREAUTH);
        break;
    }
  }

  function handleCloseRevokePreAuthModal() {
    setIdOfPreAuthToBeCanceled(null);
  }

  function handleOpenRevokePreAuthModal(id: number) {
    const preauth = preauths.find(p => p.id === id);
    if (preauth) {
      const requestType = preauth.requestType as RequestType;
      const hasApprovePermission = hasApprovePermissionForRequestType(requestType);

      if (!hasApprovePermission) {
        toast.error("You don't have permission to revoke this benefit type");
        return;
      }
    }

    setIdOfPreAuthToBeCanceled(id);
  }

  function handleOpenWithdrawPreAuthPrompt(preauth: PreAuth) {
    const requestType = preauth.requestType as RequestType;
    const hasApprovePermission = hasApprovePermissionForRequestType(requestType);

    if (!hasApprovePermission) {
      toast.error("You don't have permission to withdraw this benefit type");
      return;
    }

    setSelectedPreauthId(preauth.id);
    setActiveModal(PageModal.WITHDRAW_PREAUTH_PROMPT);
  }

  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];

  const queryDebounced = useDebounce(filters.query, 200);

  /** providers filtering */
  const providersQuery: MembershipProvidersQuery = {
    payerId,
    ...(providerSearchQuery.length > 0 && { query: providerSearchQuery }),
  };

  const { data: membershipProvidersResponse, isFetching: isFetchingProviders } =
    useGetMembershipProvidersQuery(providersQuery);

  const providers = membershipProvidersResponse?.data.content || [];

  const {
    data: preauthsData,
    error: searchPreauthsError,
    isLoading: isSearchPreauthsLoading,
  } = useSearchPreauthQuery(
    {
      page,
      size,
      payerId: payerId,
      requestTypes: selectedBenefitTypes.includes(RequestType.INPATIENT)
        ? [
            ...selectedBenefitTypes.filter((type) => type !== RequestType.INPATIENT),
            ADMISSION,
            DAY_CASE,
          ]
        : selectedBenefitTypes.length > 0
          ? selectedBenefitTypes
          : ["NONE"], // If no benefit types are selected, pass a dummy value to get empty results
      ...(filters.query
        ? { search: queryDebounced } // Ignore status if query is present
        : filters.status && {
            status:
              filters.status === PENDING_AND_DRAFT
                ? [PreAuthStatus.PENDING, PreAuthStatus.DRAFT]
                : [filters.status as PreAuthStatus],
          }),
      ...(selectedProviderId && { providerId: Number(selectedProviderId) }),
      ...(filters.startDate && { startDate: filters.startDate }),
      ...(filters.endDate && { endDate: filters.endDate }),
      ...(selectedSchemeId && { planId: Number(selectedSchemeId) }),
    },
    {
      skip: !payerId,
      refetchOnFocus: true,
      refetchOnMountOrArgChange: true,
    },
  );

  const preauths = preauthsData?.content || [];

  function handleViewPreAuthApprovalForm(id: number) {
    const preauth = preauths.find(p => p.id === id);
    if (preauth) {
      const requestType = preauth.requestType as RequestType;
      const hasViewPermission = hasViewPermissionForRequestType(requestType);

      if (!hasViewPermission) {
        toast.error("You don't have permission to view this benefit type");
        return;
      }
    }

    setSelectedPreauthId(id);
    setActiveModal(PageModal.REVIEW_PREAUTH);
  }

  function reactivatePreAuth(preAuthId: number) {
    const preauth = preauths.find(p => p.id === preAuthId);
    if (preauth) {
      const requestType = preauth.requestType as RequestType;
      const hasApprovePermission = hasApprovePermissionForRequestType(requestType);

      if (!hasApprovePermission) {
        toast.error("You don't have permission to reactivate this benefit type");
        return;
      }
    }

    setSelectedPreauthId(preAuthId);
    setActiveModal(PageModal.REACTIVATE_PREAUTH);
  }

  function topUpPreAuth(preAuthId: number) {
    const preauth = preauths.find(p => p.id === preAuthId);
    if (preauth) {
      const requestType = preauth.requestType as RequestType;
      const hasTopUpPermission = hasTopUpPermissionForRequestType(requestType);

      if (!hasTopUpPermission) {
        toast.error("You don't have permission to top up this benefit type");
        return;
      }
    }

    setSelectedPreauthId(preAuthId);
    setActiveModal(PageModal.TOP_UP);
  }

  function preAuthLogs(preAuthId: number) {
    setSelectedPreauthId(preAuthId);
    setActiveModal(PageModal.PREAUTH_LOGS);
  }

  function handleCloseModal() {
    setActiveModal(undefined);
    setSelectedPreauthId(undefined);
    setPreAuthJourney(null);
    setActiveNotification(null);
  }

  const { static: _, ...restFlatPickrOptions } = dateFlatPickrOptions;

  const defaultStatusOptions = Object.values(PreAuthStatus)
    .filter((status) => ![PreAuthStatus.INCOMPLETE].includes(status))
    .map((value: PreAuthStatus) => ({
      label: preauthStatusLabels.get(value) || "Unknown",
      value: value,
    }));

  const pendingAndDraftStatusOption = {
    value: PENDING_AND_DRAFT,
    label: "Pending and Notification",
  };

  const allStatusesOption = {
    value: "",
    label: "All Statuses",
  };

  const disableReviewUnactionablePreauth = payerId.toString() === import.meta.env.VITE_KRA_PAYER_ID;
  const disableReviewActionableStatuses = [PreAuthStatus.DRAFT, PreAuthStatus.PENDING];

  const statusOptions = [allStatusesOption, pendingAndDraftStatusOption, ...defaultStatusOptions];

  const payerSchemes = useGetPayerFilterValues(FilterValueType.SCHEME);

  const { data: membershipSchemesResponse } = useGetMembershipSchemesQuery(payerId);

  const schemes = membershipSchemesResponse?.data || [];

  const schemeFilterCriteria: PreAuthCriteriaSelect<MembershipScheme, string> = {
    name: PreAuthFilterCriteria.SCHEME,
    options: ["Search scheme", ...(payerSchemes as MembershipScheme[])],
    value: selectedPreAuthScheme?.name || "",
  };

  const providersFilterCriteria: PreAuthCriteriaSelect<MembershipProvider, string> = {
    name: PreAuthFilterCriteria.PROVIDER,
    options: ["Select the provider", ...payerProviders],
    value: selectedPreAuthProvider?.id || "",
  };

  const handlePageNumberClick = (page: number) => {
    setPage(page);
  };

  const handleSizeChange = (size: number) => {
    setSize(size);
    setPage(1);
  };

  useEffect(() => {
    if (membershipProvidersResponse?.data?.content) {
      setPayerProviders(membershipProvidersResponse.data.content);
    }
  }, [membershipProvidersResponse]);

  function checkIsPerformTopUpAllowed(preAuth: PreAuth) {
    return (
      [PreAuthStatus.AUTHORIZED].includes(preAuth.status) && // must be an authorized preauth
      !preAuth.topUps.some((topUp) => topUp.status === "PENDING") // preauth must not have a pending top up request
    );
  }

  return (
    <MainWrapper>
      <div className="mb-8 flex flex-wrap justify-around gap-8">
        {Object.values(RequestType).map((requestType) => {
          const hasPermission = hasViewPermissionForRequestType(requestType);
          const isActive = selectedBenefitTypes.includes(requestType as RequestType);

          return (
            <button
              className="flex flex-col items-center justify-center focus:outline-customBlue disabled:cursor-not-allowed"
              onClick={() => {
                if (!hasPermission) return;

                if (isActive) {
                  if (selectedBenefitTypes.length > 1) {
                    setSelectedBenefitTypes((prev) => prev.filter((type) => type !== requestType));
                  }
                } else {
                  setSelectedBenefitTypes((prev) => [...prev, requestType as RequestType]);
                }
              }}
              key={requestType}
              disabled={!hasPermission}
            >
              <div
                className={clsx(
                  "mb-2 flex h-28 w-28 items-center justify-center rounded-lg border",
                  isActive
                    ? "border-blue-900 bg-blue-900 text-white"
                    : "border border-gray-200 bg-gray-50 text-blue-900",
                  !hasPermission && "cursor-not-allowed opacity-50",
                  hasPermission && "cursor-pointer",
                )}
              >
                {requestTypeIcon(requestType)}
              </div>

              <p className={clsx("text-sm font-medium", !hasPermission && "text-gray-400")}>
                {requestTypeLabels.get(requestType)} Visit
              </p>
            </button>
          );
        })}
      </div>

      <form
        className="mb-2 grid grid-cols-6 items-center gap-4"
        onSubmit={(e) => {
          e.preventDefault();
        }}
      >
        <div className="flex flex-col space-y-2">
          {/* Show search fields */}
          {/* WARN: Search exclusivity is implemented server side */}
          <label htmlFor="query" className="flex gap-1 text-sm font-semibold text-midGray">
            Search
          </label>
          <input
            type="text"
            id="query"
            name="query"
            title="Search by name, member number, visit, or reference"
            placeholder="Search by name, member number, visit, or reference"
            className={clsx("rounded-md border border-gray-300 p-2 text-sm placeholder:text-sm")}
            value={filters.query}
            onChange={handleSearchQueryChange}
          />
        </div>

        <div className="flex flex-col gap-2">
          <label htmlFor="status" className="text-sm font-semibold text-midGray">
            Status
          </label>

          <Select
            value={filters.status}
            onChange={(value) => setFilters({ ...filters, status: value as PreAuthStatus })}
            options={statusOptions.map(({ label, value }) => ({
              key: value,
              label,
              value,
            }))}
          />
        </div>

        <div className="flex flex-col space-y-2">
          <label
            htmlFor={providersFilterCriteria.name}
            className="text-sm font-semibold text-midGray"
          >
            {providersFilterCriteria.name}
          </label>

          <AutoSuggestSelect
            placeholder="Search for provider"
            value={selectedProviderId}
            onChange={(value) => setSelectedProviderId(value as string)}
            options={providers?.map((provider) => ({
              key: provider.id,
              label: provider.name,
              value: provider.id,
            }))}
            query={providerSearchQuery}
            onQueryChange={setProviderSearchQuery}
            isFetchingOptions={isFetchingProviders}
          />
        </div>

        <div className="flex flex-col space-y-2">
          <label htmlFor={schemeFilterCriteria.name} className="text-sm font-semibold text-midGray">
            {schemeFilterCriteria.name}
          </label>
          <AutoSuggestSelect
            options={schemes.map((scheme) => ({
              key: scheme.id,
              value: scheme.id,
              label: scheme.name,
            }))}
            onChange={(value) => setSelectedSchemeId(value as string)}
            query={schemeSearchQuery}
            onQueryChange={setSchemeSearchQuery}
            value={selectedSchemeId}
            placeholder="Search for scheme"
          />
        </div>

        <div className="flex flex-col space-y-2">
          <label className="flex gap-1 text-sm font-semibold text-midGray" htmlFor="dateFrom">
            Start Date
          </label>
          <DateInput
            name="startDate"
            placeholder={new Date().toISOString().split("T")[0]}
            className={clsx("w-full rounded-md border border-gray-300 p-2 px-4 text-sm")}
            options={dateFlatPickrOptions}
            value={filters.startDate}
            onChange={(_, dateStr) => {
              setFilters({ ...filters, startDate: dateStr });
            }}
          />
        </div>

        <div className="flex flex-col space-y-2">
          <label className="text-sm font-semibold text-midGray" htmlFor="dateTo">
            End Date
          </label>

          <DateInput
            name="endDate"
            placeholder={new Date().toISOString().split("T")[0]}
            className={clsx("w-full rounded-md border border-gray-300 p-2 px-4 text-sm")}
            options={{
              position: "auto left",
              ...restFlatPickrOptions,
            }}
            value={filters.endDate}
            onChange={(_, dateStr) => {
              setFilters({ ...filters, endDate: dateStr });
            }}
          />
        </div>
      </form>

      <div>
        {isSearchPreauthsLoading ? (
          <div className="flex items-center justify-center space-x-2 self-center py-44">
            <LoadingIcon className="h-6 w-6 text-blue-400" />
          </div>
        ) : searchPreauthsError ? (
          <div className="px-2 py-8 text-red-500">
            Something went wrong. Please refresh the page to retry.
          </div>
        ) : !preauths.length ? (
          <EmptyState
            illustration={<NoData size={350} />}
            message={{
              title: "No Pre-Authorization found",
              description: "No pre-authorizations found",
            }}
          />
        ) : (
          <div className="max-w-full overflow-x-auto">
            <table className="mt-4 w-full ">
              <thead>
                <tr className="bg-gray-50">
                  <TableHeaderItem className="px-2" item="Visit Number" />
                  <TableHeaderItem className="px-2" item="Member Number" />
                  <TableHeaderItem className="px-2" item="Member Name" />
                  <TableHeaderItem className="px-2" item="Provider" />
                  <TableHeaderItem className="px-2" item="Reference No" />
                  <TableHeaderItem className="px-2" item="Scheme" />
                  <TableHeaderItem className="px-2" item="Benefit" />
                  <TableHeaderItem className="px-2" item="Date/Time" />
                  <TableHeaderItem className="px-2" item="Status" />
                  <TableHeaderItem className="px-2" item="Action" />
                  <TableHeaderItem className="px-2" item="" />
                </tr>
              </thead>
              <tbody>
                {preauths.map((preauth) => (
                  <PreAuthTableRow
                    key={preauth.id}
                    preauth={preauth}
                    handleViewPreAuthApprovalForm={handleViewPreAuthApprovalForm}
                    disableReviewUnactionablePreauth={disableReviewUnactionablePreauth}
                    disableReviewActionableStatuses={disableReviewActionableStatuses}
                    preAuthLogs={preAuthLogs}
                    handleOpenWithdrawPreAuthPrompt={handleOpenWithdrawPreAuthPrompt}
                    handleOpenRevokePreAuthModal={handleOpenRevokePreAuthModal}
                    setActiveModal={setActiveModal}
                    setPreAuthJourney={setPreAuthJourney}
                    setSelectedPreauthId={setSelectedPreauthId}
                    checkIsPerformTopUpAllowed={checkIsPerformTopUpAllowed}
                  />
                ))}
              </tbody>
            </table>

            <div className="self-start py-4">
              <PrimaryPagination
                pageSize={size}
                totalElements={preauthsData?.totalElements ?? 0}
                totalPages={preauthsData?.totalPages ?? 1}
                pageNumber={page}
                onPageNumberClick={handlePageNumberClick}
                onSizeChange={handleSizeChange}
              />
            </div>
          </div>
        )}
      </div>

      <DialogWrapper
        onClose={handleCloseModal}
        maxWidth="max-w-[1150px]"
        show={activeModal == PageModal.REVIEW_PREAUTH}
      >
        {selectedPreauthId ? (
          <PreAuthApprovalForm
            id={selectedPreauthId}
            closeModal={handleCloseModal}
            key={selectedPreauthId}
            preAuthJourney={preAuthJourney}
            onContinueJourneyClick={handleContinueJourneyClick}
          />
        ) : (
          <p className="px-2 py-8 text-center text-gray-400">No preauth selected</p>
        )}
      </DialogWrapper>

      <WithdrawPreAuthPrompt
        onClose={handleCloseModal}
        onProceed={() => {
          handleViewPreAuthApprovalForm(selectedPreauthId as number);
          setPreAuthJourney(PreAuthJourney.WithdrawPreauth);
        }}
        isOpen={activeModal === PageModal.WITHDRAW_PREAUTH_PROMPT}
      />

      <WithdrawPreauthModal
        preAuthId={selectedPreauthId}
        onClose={handleCloseModal}
        isOpen={activeModal == PageModal.WITHDRAW_PREAUTH}
      />

      <DialogWrapper
        maxWidth="max-w-[1000px]"
        show={activeModal === PageModal.PREAUTH_LOGS}
        onClose={() => handleCloseModal()}
      >
        {selectedPreauthId ? (
          <PreAuthChangeLog id={selectedPreauthId} closeModal={handleCloseModal} />
        ) : (
          <div>
            <Empty message="No preauth selected" />
          </div>
        )}
      </DialogWrapper>

      <RevokePreauthModal
        onClose={handleCloseRevokePreAuthModal}
        preAuthId={idOfPreAuthToBeCanceled as number}
        show={!!idOfPreAuthToBeCanceled}
      />

      <ReactivationReviewPrompt
        isOpen={activeModal === PageModal.REACTIVATION_REVIEW_PROMPT}
        onClose={() => {
          setActiveModal(undefined);
          setPreAuthJourney(null);
        }}
        onProceed={() => {
          setActiveModal(PageModal.REVIEW_PREAUTH);
        }}
      />

      <ReactivatePreauth
        isOpen={activeModal === PageModal.REACTIVATE_PREAUTH}
        preAuthId={selectedPreauthId as number}
        onClose={handleCloseModal}
      />

      <TopUpPrompt
        isOpen={activeModal === PageModal.TOP_UP_PROMPT}
        onClose={() => {
          setActiveModal(undefined);
          setPreAuthJourney(null);
        }}
        onProceed={() => {
          setActiveModal(PageModal.REVIEW_PREAUTH);
        }}
      />

      <TopUp
        isOpen={activeModal === PageModal.TOP_UP}
        preAuthId={selectedPreauthId as number}
        onClose={handleCloseModal}
      />
    </MainWrapper>
  );
}
