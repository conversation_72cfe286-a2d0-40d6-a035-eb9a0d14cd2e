import React, { FormEvent, useEffect } from "react"
import FieldWrapper from "../../rhf-components/FieldWrapper"
import Input from "../../rhf-components/Input"
import Select from "../../rhf-components/Select"
import DateInput from "../../rhf-components/DateInput"
import { useFieldArray, useForm } from "react-hook-form"
import UserService from "../../services/UserService"
import { NumberRules, TextRules, DateRules, OptionRules, TypeDescription, Primitive, ComplexPrimitive, FieldProperties } from "~lib/service-fields"
import { Form } from "../../rhf-components/Form"
import TextArea from "../../rhf-components/TextArea"
import Switch from "../../rhf-components/Switch"
import { capitalize } from "../../utils"
import { services } from "./service-fields"
import { min } from "moment"

/**
 * Notes:
 * - Treat pharmacy and laboratory as a special case? Handle them manually for now.
 */

// TODO: Add fields props
// Use fields prop to fill showIfField options and showIfValue type
interface Props {}

interface Inputs extends FieldProperties {
  type: Primitive | ComplexPrimitive
  // TODO: Use field interface type
  defaultValue?: string | number | boolean
  rules?: NumberRules | TextRules | DateRules | OptionRules | OptionRules
  options?: Array<{ label: string, value: string | number }>
  /* ----- */
  isConditional?: boolean
  isCustomMinDate?: boolean
  isCustomMaxDate?: boolean
}

export default function AddPreAuthField(props: Props) {
  const methods = useForm<Inputs>({
    mode: "onBlur",
    defaultValues: {
      name: "",
      label: "",
      type: Primitive.Text,
      description: "",
      isConditional: false,
      rules: {
        required: false
      },
      isCustomMinDate: false,
      isCustomMaxDate: false,
    },
    /**
     * Unregister conditional fields on unmount
     */
    shouldUnregister: true
  })

  const { register, trigger, watch, reset, setValue, control, formState } = methods;
  const username = UserService.getUsername();

  const { fields, append, remove, move, swap } = useFieldArray({
    control,
    name: "options",
    rules: {
      required: true,
      minLength: 1
    }
  });

  const optionsError = formState.errors?.['options'];
  const optionsMessage =
    optionsError?.type === "required"
      ? "This field is required"
      : optionsError?.type === "minLength"
      ? "At least one option is required"
      : optionsError?.message ?? "Invalid input";

  // TODO: Add options

  const fieldType = watch("type")
  const form = watch()

  useEffect(() => {
    // Reset dynamic fields when the field type changes
    setValue("defaultValue", undefined)
    setValue("rules", {
      required: false
    })
  }, [fieldType])


  // TODO: Reset showIf.field and showIf.value when services change

  const handleSubmit = async (data: Inputs, e?: FormEvent<HTMLFormElement>) => {
    console.log(data)
  }

  return (
    <div className="py-4 px-8">
      {/* <div>Value: <code>{ JSON.stringify(form, null, 2) }</code></div> */}

      <Form className="grid gap-4 lg:grid-cols-2" methods={methods} onSubmit={handleSubmit}>
        <FieldWrapper 
          label="Label"
          description="The label for the field"
          name="label"
          className="mb-2"
          required
        >
          <Input 
            placeholder="Bed Capacity"
          />
        </FieldWrapper>
        
        <FieldWrapper 
          label="Name"
          description="A unique identifier for the field. Accepts alphanumeric characters only."
          name="name"
          className="mb-2"
          required
        >
          {/* TODO: Fix pattern validation */}
          <Input 
            placeholder="bedCapacity"
            options={{
              pattern: {
                value: /^[a-zA-Z0-9]+$/,
                message: "Alphanumeric characters only"
              }
            }}
          />
        </FieldWrapper>

        <FieldWrapper
          label="Type"
          description="The type of data the field will accept"
          name="type"
          className="mb-2"
          required
        >
          <Select
            options={[
              ...Object.entries(Primitive).map(([_, value]) => ({ label: `${capitalize(value.toLowerCase().replace(/_/g, " "))} - ${ TypeDescription[value] ?? "" }`, value: value })),
              ...Object.entries(ComplexPrimitive).map(([_, value]) => ({ label: `${capitalize(value.toLowerCase().replace(/_/g, " "))} - ${ TypeDescription[value] ?? "" }`, value: value })),
            ]}
          />
        </FieldWrapper>

        
          <FieldWrapper 
            label="Default Value"
            description="The default value for the field."
            name="defaultValue"
            className="mb-2"
          >
            {fieldType === Primitive.Text ? (
              <Input />
            ) : fieldType === Primitive.Number ? (
              <Input type="number" />
            ) : fieldType === Primitive.Boolean ? (
              <Switch />
            ) : fieldType === Primitive.Date ? (
              <DateInput />
            ) : fieldType === Primitive.Textbox ? (
              <TextArea />
            ) : fieldType === ComplexPrimitive.Options ? (
              // TODO: Show defaultValue after choosing options
              <Select
                options={form.options ?? []}
              />
            ) : (
              <></>
            )}
          </FieldWrapper>
        
        <FieldWrapper
          label="Description"
          description="A helpful description of the field"
          name="description"
          className="mb-2 col-span-2"
        >
          <TextArea />
        </FieldWrapper>

        {/* TODO: Load services from network */}
        <FieldWrapper
          label="Service"
          description="The service the field applies to"
          name="service"
          className="mb-2"
          required
        >
          <Select
            options={services.map(service => ({ label: service.label, value: service.name }))}
          />
        </FieldWrapper>

        <FieldWrapper
          label="Conditional"
          name="isConditional"
          description="Show the field only if a certain value is selected for another field"
          className="mb-2"
        >
          <Switch />
        </FieldWrapper>

        {form.type === ComplexPrimitive.Options && (
          <fieldset className="border border-gray-300 flex flex-col lg:col-span-2 gap-2 flex-wrap px-4 py-2 rounded" name="condition">
            <legend className="text-sm px-2 text-gray-400">Options</legend>

            <div>
              {/* TODO: Verify error type */}
              {optionsError && (
                <div className="text-red-500 text-xs mb-2">
                  {optionsMessage}
                </div>
              )}
            </div>

            {fields.map((field, index) => (
              <div className="flex gap-4 flex-wrap" key={index}>
                <FieldWrapper 
                  label="Label"
                  // description="The label for the option"
                  name={`options.${index}.label`}
                  className="mb-2"
                  required
                >
                  <Input 
                    placeholder="Option"
                    key={field.id+'label'}
                  />
                </FieldWrapper>
                
                {/* TODO: Validate alphanumeric value */}
                <FieldWrapper 
                  label="Value"
                  // description="A unique value for the option. Should be alphanumeric."
                  name={`options.${index}.value`}
                  className="mb-2"
                  required
                >
                  <Input 
                    placeholder="Value"
                    key={field.id+'value'}
                  />
                </FieldWrapper>

                <div className="flex items-center">
                  <button
                    type="button"
                    onClick={() => remove(index)}
                    className="text-red-500 hover:text-red-600 p-4 rounded"
                    title="Remove"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                    </svg>
                  </button>
                </div>
              </div>
            ))}

            <div className="mb-4">
              <button
                type="button"
                onClick={() => append({ value: "", label: "" })}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
              >
                Add Option
              </button>
            </div>
          </fieldset>
        )}

        {form.isConditional && (
          <fieldset className="border border-gray-300 flex lg:col-span-2 gap-2 flex-wrap px-4 py-2 rounded" name="condition">
            <legend className="text-sm px-2 text-gray-400">Condition</legend>

            <FieldWrapper 
              label="Field"
              name="showIf.field"
              className="mb-2"
              required
            >
              <Input />
            </FieldWrapper>
            
            <FieldWrapper 
              label="Value"
              name="showIf.value"
              className="mb-2"
              required
            >
              <Input />
            </FieldWrapper>

          </fieldset>
        )}

        <fieldset className="border border-gray-300 flex gap-2  justify-between flex-wrap col-span-2 px-4 py-2 rounded">
          <legend className="text-sm px-2 text-gray-400">Rules</legend>

          <FieldWrapper
            label="Required"
            name="rules.required"
            className="mb-2"
          >
            <Switch />
          </FieldWrapper>


          {fieldType === Primitive.Text ? (
            <>
              <FieldWrapper
                label="Min Length"
                name="rules.minLength"
                className="mb-2"
              >
                <Input type="number" />
              </FieldWrapper>

              <FieldWrapper
                label="Max Length"
                name="rules.maxLength"
                className="mb-2"
              >
                <Input type="number" />
              </FieldWrapper>
            </>
          ) : fieldType === Primitive.Number ? (
            <>
              <FieldWrapper

                label="Min"
                name="rules.min"
                className="mb-2"
              >
                <Input type="number" />
              </FieldWrapper>

              <FieldWrapper
                label="Max"
                name="rules.max"
                className="mb-2"
              >
                <Input type="number" />
              </FieldWrapper>
            </>
          ) : fieldType === Primitive.Date ? (
            <>
              <FieldWrapper
                label="Custom Format"
                name="isCustomMinDate"
                className="mb-2"
              >
                <Switch />
              </FieldWrapper>

              <FieldWrapper
                label="Min"
                name="rules.min"
                className="mb-2"
                >
                {form.isCustomMinDate ? (
                  <Input />
                  ) : (
                  <DateInput />
                )}
              </FieldWrapper>

              <FieldWrapper
                label="Custom Format"
                name="isCustomMaxDate"
                className="mb-2"
              >
                <Switch />
              </FieldWrapper>

              <FieldWrapper
                label="Max"
                name="rules.max"
                className="mb-2"
                >
                {form.isCustomMaxDate ? (
                  <Input />
                  ) : (
                  <DateInput />
                )}
              </FieldWrapper>
            </>
          ) : (
            <></>
          )}
        </fieldset>

        <div className="flex col-span-2 gap-2 flex-wrap justify-end">
          <input 
            type="button" 
            value="Reset" 
            onClick={() => {
              reset()
            }}
            className="bg-gray-400 hover:bg-gray-500 rounded font-medium text-white px-4 py-2 focus:outline-blue"
          />
          
          <input 
            type="submit" 
            value="Submit" 
            className="bg-blue-500 hover:bg-blue-600 rounded font-medium text-white px-4 py-2 focus:outline-blue"
          />
        </div>
      </Form>
    </div>
  )
}
