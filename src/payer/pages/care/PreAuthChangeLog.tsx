import { ExclamationCircleIcon, XMarkIcon } from "@heroicons/react/24/outline";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { useAuditLogsQuery } from "../../api/services";
import { AuditLog } from "../../api/types";
import Text from "../../components/ui/typography/Text";

interface Props {
  closeModal: () => void;
  id?: number;
}

const providerEventNames = ["Pre-auth edited", "Pre-auth Initiated", "Provider Cancelled Pre-auth"];

export default function PreAuthChangeLog({ closeModal, id }: Props) {
  const { data: result, isLoading } = useAuditLogsQuery({
    preAuthId: id as number,
  });

  const logs = result?.data.content || [];
  const logGroups = getLogsGroupedByDate(logs);

  if (isLoading)
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingIcon />
      </div>
    );

  return (
    <section className="pb-4">
      <div className="flex items-center justify-between  px-6 pb-3 pt-4">
        <Text className="grow text-center text-xl font-semibold" variant="heading">
          Pre-authorization Logs
        </Text>

        <button onClick={closeModal}>
          <XMarkIcon className="w-5" strokeWidth={2} />
        </button>
      </div>

      {logs.length === 0 && (
        <p className="p-4 text-center font-semibold text-[#6B7280]">
          No logs available for this pre-authorization
        </p>
      )}

      {logGroups.map((group, groupIndex) => (
        <div key={group.dateString} className="ml-16 mt-2">
          <p className="font-semibold text-[#6B7280]">{group.dateString}</p>

          <div className="mt-4">
            {group.logs.map((log, logIndex) => (
              <div
                key={log.id || `${groupIndex}-${logIndex}`}
                className="ml-8 mt-1 grid min-h-[50px] grid-cols-[auto,1fr] gap-4 "
              >
                <div className="flex h-full flex-col items-center">
                  <ExclamationCircleIcon className="w-6 text-[#6B7280]" />
                  {logIndex < group.logs.length - 1 && (
                    <span className="grow border border-[#6B7280]"></span>
                  )}
                </div>

                <div className="flex flex-col gap-2">
                  <div className="flex items-center gap-4">
                    <span className="text-sm font-medium text-[#6B7280]">
                      {new Date(log.createdOn).toLocaleTimeString("en-US")}
                    </span>{" "}
                    <span className="font-semibold">{log.actionByUser}</span>{" "}
                    <span className="text-sm font-medium text-[#6B7280]">
                      {log.eventName || log.action}
                    </span>{" "}
                  </div>

                  <div className="ml-8">
                    {log.reason?.trim() && (
                      <fieldset className="mb-4 w-4/5 max-w-[800px] overflow-x-auto rounded-md border border-[#D1D5DB] px-2 pb-4 pt-2">
                        <legend className="text-sm font-medium text-red-500">Reason</legend>
                        <p className="text-sm font-medium text-[#6B7280]">{log.reason?.trim()}</p>
                      </fieldset>
                    )}
                    {getMarkAsIncompleteNotes(log?.newValues) && (
                      <fieldset className="mb-4 w-4/5 max-w-[800px] overflow-x-auto rounded-md border border-[#D1D5DB] px-2 pb-4 pt-2">
                        <legend className="text-sm font-medium text-[#2563EB]">Notes</legend>
                        <p className="text-sm font-medium text-[#6B7280]">
                          {getMarkAsIncompleteNotes(log?.newValues) || "NA"}
                        </p>
                      </fieldset>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </section>
  );
}

type GroupedLogs = {
  [date: string]: AuditLog[];
};

type LogGroups = {
  dateString: string;
  logs: AuditLog[];
};

function getLogsGroupedByDate(logs: AuditLog[]): LogGroups[] {
  const grouped = logs
    .filter((log) => !providerEventNames.includes(log?.eventName || ""))
    .reduce((acc: GroupedLogs, log: AuditLog) => {
      const dateString = new Date(log.createdOn).toLocaleDateString("en-US", {
        month: "long",
        day: "numeric",
        year: "numeric",
      });

      if (!acc[dateString]) {
        acc[dateString] = [];
      }

      acc[dateString].push(log);
      return acc;
    }, {} as GroupedLogs);

  return Object.entries(grouped).map(([dateString, logs]) => ({
    dateString,
    logs,
  }));
}

function getMarkAsIncompleteNotes(newValues?: string | null): string | null {
  if (!newValues) return null;
  try {
    const parsed = JSON.parse(newValues);

    return parsed.notes?.trim() || null;
  } catch {
    return null;
  }
}
