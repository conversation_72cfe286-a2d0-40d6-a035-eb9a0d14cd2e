import { ChevronDownIcon, CloudArrowDownIcon } from "@heroicons/react/24/outline";
import { useState } from "react";
import { toast } from "react-toastify";
import { PreAuthRePortRequest } from "~lib/api/types";
import { baseUrl } from "~lib/constants";
import Dropdown, { DropDownItem } from "../../../components/ui/Dropdown";
import { AUTHENTICATED_USER } from "../../../lib/payer-constants";
import { Report, usePreAuthReports } from "./pre-auth-reports-context";

type ReportFileType = "XLSX" | "CSV";

export default function DownloadReportButton() {
  const { activeReport, selectedFilters, isReportEmpty } = usePreAuthReports();
  const [isDownloading, setIsDownloading] = useState(false);

  const payerId = AUTHENTICATED_USER.getPayerId();

  const downloadOptions: DropDownItem[] = [
    {
      label: "XLSX",
      onClick: () => handleDownloadReport("XLSX"),
    },
    {
      label: "CSV",
      onClick: () => handleDownloadReport("CSV"),
    },
  ];

  async function handleDownloadReport(fileType: ReportFileType) {
    try {
      setIsDownloading(true);
      const baseDownloadUrl = `${baseUrl}/api/v1/preauthorization`;
      let downloadUrl: string;

      const { catalogId, endDate, planId, providerId, startDate } = selectedFilters;

      const body: PreAuthRePortRequest["body"] = {
        catalogId: Number(catalogId),
        endDate: endDate.split("T").at(0) as string,
        startDate: startDate.split("T").at(0) as string,
        providerId: Number(providerId),
        planId: Number(planId),
        payerId,
      };

      const method = "POST";

      switch (activeReport) {
        case Report.TAT:
          downloadUrl = baseDownloadUrl.concat(`/turnAroundTime/${fileType}/report`);
          break;
        case Report.Admission:
          downloadUrl = baseDownloadUrl.concat(`/admission/${fileType}/report`);
          body.statuses = ["AUTHORIZED"];
          body.preAuthTypes = ["EMERGENCY_ADMISSION", "SCHEDULED_ADMISSION"];
          break;
        case Report.Declined:
          downloadUrl = baseDownloadUrl.concat(`/decline/${fileType}/report`);
          body.statuses = ["DECLINED"];
          break;
        case Report.Withdrawn:
          downloadUrl = baseDownloadUrl.concat(`/withdrawn/${fileType}/report`);
          body.statuses = ["WITHDRAWN"];
          break;
        default:
          downloadUrl = baseDownloadUrl.concat(`/perBenefit/${fileType}/report`);
          break;
      }

      const cleanBody = Object.fromEntries(
        Object.entries(body).filter(
          ([_key, value]) => value !== null && value !== undefined && value !== "" && value !== 0,
        ),
      );

      const response = await fetch(downloadUrl, {
        method,
        body: JSON.stringify(cleanBody),
        headers: { "Content-Type": "application/json" },
      });

      if (!response.ok) {
        toast.error("Failed to download document!");
        return;
      }

      const blob = await response.blob();

      const objUrl = window.URL.createObjectURL(blob);

      const a = document.createElement("a");
      a.href = objUrl;
      a.download = `${activeReport} Report.${fileType.toLowerCase()}`;
      document.body.appendChild(a);

      a.click();
      a.remove();

      window.URL.revokeObjectURL(objUrl);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Something went wrong";
      toast.error(errorMessage);
      console.error(errorMessage, error);
    } finally {
      setIsDownloading(false);
    }
  }

  return (
    <Dropdown
      disabled={!activeReport || isDownloading || isReportEmpty}
      buttonLabel={`Download${isDownloading ? "ing report ..." : " report"}`}
      items={downloadOptions}
      LeftIcon={<CloudArrowDownIcon className="w-5" />}
      RightIcon={<ChevronDownIcon className="w-4" strokeWidth={2} />}
    />
  );
}
