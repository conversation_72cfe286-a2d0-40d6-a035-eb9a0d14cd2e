import illustrationUrl from "../../../assets/svg/report-illustration.svg";
import EmptyState from "../../../components/ui/EmptyState";
import { Select, SelectOption } from "../../../components/ui/input/Select";
import { reportTypeLabelMap } from "./pre-auth-reports-constants";
import { Report, usePreAuthReports } from "./pre-auth-reports-context";

const reportTypeOptions: SelectOption[] = Array.from(reportTypeLabelMap).map(
  ([reportType, label]) => ({ key: reportType, label, value: reportType }),
);

export default function ReportTypeSelection() {
  const { activeReport: activeReportType, setActiveReport: setActiveReportType } =
    usePreAuthReports();
  function handleReportTypeChange(value: Report) {
    setActiveReportType(value);
  }

  return (
    <section className="flex flex-col gap-4">
      <div className=" mx-auto mt-8  flex w-[75%] items-center gap-4">
        <p className="text-lg text-[#304254] ">Start by Selecting a Preauthorization Report Type</p>
        <Select
          containerClassName="grow"
          options={reportTypeOptions}
          onChange={handleReportTypeChange}
          value={activeReportType}
          placeholder="Select report type"
        />
      </div>

      <EmptyState
        illustration={<img className="block h-[300px]" src={illustrationUrl} alt="Illustration" />}
        message={{
          title: "No Preauthorization Reports",
          description:
            "Start by choosing a preauthorization report type. Then, apply filters to see the relevant data.",
        }}
      />
    </section>
  );
}
