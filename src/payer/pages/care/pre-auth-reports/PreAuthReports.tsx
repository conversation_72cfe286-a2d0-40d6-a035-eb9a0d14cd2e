import { useState } from "react";
import {
  useGetMembershipBenefitsCatalogQuery,
  useGetMembershipProvidersQuery,
  useGetMembershipSchemesQuery,
} from "../../../api/features/membershipApi";
import BackNavigationButton from "../../../components/ui/BackNavigationButton";
import MainWrapper from "../../../components/ui/MainWrapper";
import Text from "../../../components/ui/typography/Text";
import { AUTHENTICATED_USER } from "../../../lib/payer-constants";
import ActiveDisplay from "./ActiveDisplay";
import DownloadReportButton from "./DownloadReportButton";
import {
  DEFAULT_FILTER_QUERIES,
  DEFAULT_FILTER_VALUES,
  FilterQueries,
  ReportsFilterValues,
  reportTypeLabelMap,
} from "./pre-auth-reports-constants";
import { PreAuthReportsContext, Report } from "./pre-auth-reports-context";

export default function PreAuthReports() {
  const [activeReport, setActiveReport] = useState<Report>();
  const [selectedFilters, setSelectedFilters] =
    useState<ReportsFilterValues>(DEFAULT_FILTER_VALUES);
  const [filterQueries, setFilterQueries] = useState<FilterQueries>(DEFAULT_FILTER_QUERIES);
  const [isReportEmpty, setIsReportEmpty] = useState(true);

  const payerId = AUTHENTICATED_USER.getPayerId();

  const { data: schemesPayload } = useGetMembershipSchemesQuery(String(payerId));
  const { data: catalogsPayload } = useGetMembershipBenefitsCatalogQuery(String(payerId));
  const { data: providersPayload } = useGetMembershipProvidersQuery({
    payerId: String(payerId),
    query: filterQueries.provider,
  });

  const schemes = schemesPayload?.data || [];
  const catalogs = catalogsPayload?.data || [];
  const providers = providersPayload?.data.content || [];

  function handleBackClick() {
    setActiveReport(undefined);
    handleClearFilters();
  }

  function handleClearFilters() {
    setSelectedFilters(DEFAULT_FILTER_VALUES);
    setFilterQueries(DEFAULT_FILTER_QUERIES);
  }

  return (
    <PreAuthReportsContext.Provider
      value={{
        activeReport,
        setActiveReport,
        catalogs,
        providers,
        schemes,
        selectedFilters,
        setSelectedFilters,
        handleClearFilters,
        filterQueries,
        setFilterQueries,
        isReportEmpty,
        setIsReportEmpty,
      }}
    >
      <MainWrapper>
        <section className=" flex justify-between">
          <div className="flex items-center gap-6">
            {activeReport !== undefined && <BackNavigationButton onClick={handleBackClick} />}
            <Text variant="heading">
              {activeReport ? reportTypeLabelMap.get(activeReport) : "Reports"}
            </Text>
          </div>
          {activeReport && <DownloadReportButton />}
        </section>

        <ActiveDisplay />
      </MainWrapper>
    </PreAuthReportsContext.Provider>
  );
}
