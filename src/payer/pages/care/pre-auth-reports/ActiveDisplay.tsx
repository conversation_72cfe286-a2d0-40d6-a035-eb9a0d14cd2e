import AdmissionReport from "./AdmissionReport";
import DeclinedReport from "./DeclinedReport";
import PerBenefitReport from "./PerBenefitReport";
import { Report, usePreAuthReports } from "./pre-auth-reports-context";
import ReportTypeSelection from "./ReportTypeSelection";
import TATReport from "./TATReport";
import WithdrawnReport from "./WithdrawnReport";

export default function ActiveDisplay() {
  const { activeReport } = usePreAuthReports();

  switch (activeReport) {
    case Report.TAT:
      return <TATReport />;
    case Report.Admission:
      return <AdmissionReport />;
    case Report.Declined:
      return <DeclinedReport />;
    case Report.Withdrawn:
      return <WithdrawnReport />;
    case Report.PerBenefit:
      return <PerBenefitReport />;
    default:
      return <ReportTypeSelection />;
  }
}
