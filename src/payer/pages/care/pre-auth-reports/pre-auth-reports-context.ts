import { createContext, useContext } from "react";
import { MembershipBenefitCatalog } from "../../../lib/types/membership/memberBenefit";
import { MembershipProvider } from "../../../lib/types/membership/memberProvider";
import { MembershipScheme } from "../../../lib/types/membership/memberScheme";
import { FilterQueries, ReportsFilterValues } from "./pre-auth-reports-constants";

export enum Report {
  TAT = "TAT",
  Admission = "Admission",
  Declined = "Declined",
  Withdrawn = "Withdrawn",
  PerBenefit = "PerBenefit",
}

export type PreAutReportsState = {
  activeReport: Report | undefined;
  setActiveReport: React.Dispatch<React.SetStateAction<Report | undefined>>;
  schemes: MembershipScheme[];
  catalogs: MembershipBenefitCatalog[];
  providers: MembershipProvider[];
  selectedFilters: ReportsFilterValues;
  setSelectedFilters: React.Dispatch<React.SetStateAction<ReportsFilterValues>>;
  handleClearFilters(): void;
  filterQueries: FilterQueries;
  setFilterQueries: React.Dispatch<React.SetStateAction<FilterQueries>>;
  isReportEmpty: boolean;
  setIsReportEmpty: React.Dispatch<React.SetStateAction<boolean>>;
};

export const PreAuthReportsContext = createContext<PreAutReportsState | null>(null);

export function usePreAuthReports() {
  const context = useContext(PreAuthReportsContext);

  if (!context)
    throw new Error("usePreAuhReports can only be used in a PreAuthReportsContext provider");

  return context;
}
