import { Report } from "./pre-auth-reports-context";

export const DEFAULT_FILTER_VALUES = {
  planId: "",
  catalogId: "",
  providerId: "",
  startDate: "",
  endDate: "",
  currentPage: 1,
  currentSize: 10,
};

export type ReportsFilterValues = typeof DEFAULT_FILTER_VALUES;
export const reportTypeLabelMap = new Map([
  [Report.TAT, "Pre-auth Turnaround Time (TAT) Report"],
  [Report.Admission, "Admission Report (Pre-Discharge Cases)"],
  [Report.Declined, "Declined Pre-auths Report"],
  [Report.Withdrawn, "Withdrawn Pre-auths Report"],
  [Report.PerBenefit, "Per Benefit Report"],
]);

export const DEFAULT_FILTER_QUERIES = {
  scheme: "",
  benefit: "",
  provider: "",
};

export type FilterQueries = typeof DEFAULT_FILTER_QUERIES;
