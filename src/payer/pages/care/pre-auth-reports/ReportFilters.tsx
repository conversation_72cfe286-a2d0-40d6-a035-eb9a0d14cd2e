import Button from "../../../components/ui/Button";
import AutoSuggestSelect from "../../../components/ui/input/AutoSuggestSelect";
import DateInput from "../../../components/ui/input/DateInput";
import { Select, SelectOption } from "../../../components/ui/input/Select";
import { usePreAuthReports } from "./pre-auth-reports-context";

export default function ReportFilters() {
  const {
    schemes,
    catalogs,
    providers,
    selectedFilters,
    setSelectedFilters,
    handleClearFilters,
    filterQueries,
    setFilterQueries,
  } = usePreAuthReports();

  const { catalogId, endDate, startDate, planId, providerId } = selectedFilters;

  const schemesOptions: SelectOption[] = schemes?.map(({ id, name }) => ({
    key: id,
    label: name,
    value: id,
  }));

  const catalogsOptions: SelectOption[] = catalogs?.map(({ id, name }) => ({
    key: id,
    label: name,
    value: id,
  }));

  const providersOptions: SelectOption[] = providers?.map(({ id, name }) => ({
    key: id,
    label: name,
    value: id,
  }));

  const ignoredFilterKeys = ["currentPage", "currentSize"];

  const canClearFilters = Object.entries(selectedFilters).some(
    ([key, value]) => !ignoredFilterKeys.includes(key) && value != null && value !== "",
  );

  return (
    <div className="mt-4 flex gap-2">
      <div className="grid grow grid-cols-5 gap-2">
        <div>
          <p className="mb-1 text-sm font-medium text-gray-700">Scheme</p>
          <Select
            placeholder="Select a scheme"
            options={schemesOptions}
            onChange={(value) =>
              setSelectedFilters((prev) => ({ ...prev, planId: value as string }))
            }
            value={planId}
          />
        </div>
        <div>
          <p className="mb-1 text-sm font-medium text-gray-700">Benefit</p>
          <Select
            placeholder="Select a benefit"
            options={catalogsOptions}
            onChange={(value) =>
              setSelectedFilters((prev) => ({ ...prev, catalogId: value as string }))
            }
            value={catalogId}
          />
        </div>
        <div>
          <p className="mb-1 text-sm font-medium text-gray-700">Provider</p>
          <AutoSuggestSelect
            query={filterQueries.provider}
            onQueryChange={(value) => setFilterQueries((prev) => ({ ...prev, provider: value }))}
            placeholder="Search for provider"
            options={providersOptions}
            onChange={(value) =>
              setSelectedFilters((prev) => ({ ...prev, providerId: value as string }))
            }
            value={providerId}
          />
        </div>
        <div>
          <p className="mb-1 text-sm font-medium text-gray-700">Start Date</p>
          <DateInput
            value={startDate}
            onChange={(value) =>
              setSelectedFilters((prev) => ({
                ...prev,
                startDate: value,
              }))
            }
            placeholder="Select the date"
          />
        </div>
        <div>
          <p className="mb-1 text-sm font-medium text-gray-700">End Date</p>
          <DateInput
            value={endDate}
            onChange={(value) => setSelectedFilters((prev) => ({ ...prev, endDate: value }))}
            placeholder="Select the date"
          />
        </div>
      </div>

      <div className="flex items-end ">
        <Button
          variant="filled"
          className="w-full whitespace-nowrap py-2"
          disabled={!canClearFilters}
          onClick={handleClearFilters}
        >
          Clear filters
        </Button>
      </div>
    </div>
  );
}
