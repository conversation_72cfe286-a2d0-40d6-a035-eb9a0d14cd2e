import axios from "axios";
import { useEffect, useLayoutEffect, useState } from "react";
import { toast } from "react-toastify";
import { PreAuthRePortRequest, ReportVisit } from "~lib/api/types";
import { ListResponse } from "../../../api/types";
import LoadingAnimation from "../../../components/animations/LoadingAnimation/LoadingAnimation";
import NoData from "../../../components/illustrations/NoData";
import EmptyState from "../../../components/ui/EmptyState";
import PrimaryPagination from "../../../components/ui/pagination/PrimaryPagination";
import TableDataItem from "../../../components/ui/table/TableDataItem";
import TableHeaderItem from "../../../components/ui/table/TableHeaderItem";
import { AUTHENTICATED_USER } from "../../../lib/payer-constants";
import { baseUrl } from "../../../lib/Utils";
import { usePreAuthReports } from "./pre-auth-reports-context";
import ReportFilters from "./ReportFilters";

type Column = {
  label: string;
  key: keyof ReportVisit;
};

const columns: Column[] = [
  { label: "Visit ID", key: "id" },
  {
    label: "Member Number",
    key: "memberNumber",
  },
  {
    label: "Member Name",
    key: "memberName",
  },
  {
    label: "Request Time",
    key: "requestTime",
  },
  {
    label: "Approval Time",
    key: "approvalTime",
  },
  {
    label: "Provider Name",
    key: "providerName",
  },
  {
    label: "Benefit Name",
    key: "benefitName",
  },
  {
    label: "Reference Number",
    key: "reference",
  },

  {
    label: "Request Amount",
    key: "requestAmount",
  },
  {
    label: "Approved Amount",
    key: "approvedAmount",
  },
];

function formatNumber(number: number) {
  return new Intl.NumberFormat().format(number);
}

function getDataItem(visit: ReportVisit, key: keyof ReportVisit) {
  const value = visit[key];
  const isAmountKey = key === "requestAmount" || key === "approvedAmount";
  const shouldFormatNumber = isAmountKey && typeof value === "number";
  return shouldFormatNumber ? formatNumber(value) : value;
}

export default function PerBenefitReport() {
  const [visits, setVisits] = useState<ReportVisit[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalElements, setTotalElements] = useState<number>();
  const [totalPages, setTotalPages] = useState<number>();

  const {
    selectedFilters: {
      catalogId,
      endDate,
      planId,
      providerId,
      startDate,
      currentPage,
      currentSize,
    },
    setSelectedFilters,
    setIsReportEmpty,
  } = usePreAuthReports();

  const payerId = AUTHENTICATED_USER.getPayerId();

  useLayoutEffect(() => {
    (async () => {
      try {
        const body: PreAuthRePortRequest["body"] = {
          catalogId: Number(catalogId),
          endDate: endDate.split("T").at(0) as string,
          startDate: startDate.split("T").at(0) as string,
          providerId: Number(providerId),
          planId: Number(planId),
          payerId,
          size: currentSize,
          page: currentPage,
        };

        const cleanBody = Object.fromEntries(
          Object.entries(body).filter(
            ([_key, value]) => value !== null && value !== undefined && value !== "" && value !== 0,
          ),
        );

        const { data } = await axios.post<ListResponse<ReportVisit>>(
          `${baseUrl}/api/v1/preauthorization/time/search`,
          cleanBody,
        );
        setVisits(data?.data.content);
        setTotalElements(data?.data.totalElements);
        setTotalPages(data?.data.totalPages);

        if (isLoading) setIsLoading(false);
      } catch (error) {
        toast.error((error as any).response.data.error || "Something went wrong");
        console.error(error);
      }
    })();
  }, [
    catalogId,
    currentPage,
    currentSize,
    endDate,
    isLoading,
    payerId,
    planId,
    providerId,
    startDate,
  ]);

  useEffect(() => {
    setIsReportEmpty(visits.length === 0);
  }, [setIsReportEmpty, visits.length]);

  return (
    <section>
      <ReportFilters />

      {isLoading ? (
        <div className=" mt-32 flex justify-center">
          <LoadingAnimation size={30} />
        </div>
      ) : visits.length === 0 ? (
        <EmptyState
          illustration={<NoData size={250} />}
          message={{ title: "No reports found", description: "No reports available" }}
        />
      ) : (
        <>
          <table className="mt-8">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column) => (
                  <TableHeaderItem
                    className="px-1 text-sm"
                    key={column.label}
                    item={column.label}
                  />
                ))}
              </tr>
            </thead>
            <tbody>
              {visits.map((visit) => (
                <tr key={visit.id}>
                  {columns.map((column) => (
                    <TableDataItem
                      className="px-1 text-sm"
                      key={column.key}
                      item={getDataItem(visit, column.key) || "-"}
                    />
                  ))}
                </tr>
              ))}
            </tbody>
          </table>

          <PrimaryPagination
            totalElements={Number(totalElements)}
            totalPages={Number(totalPages)}
            pageNumber={currentPage}
            onPageNumberClick={(page: number) => {
              setSelectedFilters((prev) => ({ ...prev, currentPage: page }));
            }}
            onSizeChange={(size: number) => {
              setSelectedFilters((prev) => ({ ...prev, currentSize: size }));
            }}
            pageSize={currentSize}
          />
        </>
      )}
    </section>
  );
}
