import { XMarkIcon } from "@heroicons/react/24/outline";
import { ChangeEvent, FormEvent, useState } from "react";
import { toast } from "react-toastify";
import { useDeletePreauthMutation } from "~lib/api";
import Button from "../../components/ui/Button";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import Text from "../../components/ui/typography/Text";
import { AUTHENTICATED_USER } from "../../lib/payer-constants";

type CancelPreauthModalProps = {
  show: boolean;
  onClose: () => void;
  preAuthId: number;
};

export default function RevokePreauthModal({ onClose, preAuthId, show }: CancelPreauthModalProps) {
  const [cancelPreauth] = useDeletePreauthMutation();
  const [reason, setReason] = useState("");

  function handleReasonChange(e: ChangeEvent<HTMLTextAreaElement>) {
    setReason(e.target.value);
  }

  function handleClose() {
    setReason("");
    onClose();
  }

  async function handleCancelPreAuth(e: FormEvent<HTMLFormElement>) {
    try {
      e.preventDefault();
      const toastId = toast.loading("Cancelling pre-authorization");

      await cancelPreauth({
        id: preAuthId,
        cancelledBy: AUTHENTICATED_USER.getUserName(),
        reason,
      }).unwrap();

      toast.update(toastId, {
        render: "Pre-authorization canceled successfully!",
        type: "success",
        isLoading: false,
        autoClose: 300,
      });

      onClose();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error?.message : "Error occurred. Please try again!";

      console.error(errorMessage);
      toast.error(errorMessage);
    }
  }

  return (
    <DialogWrapper className="px-12 py-6" maxWidth="max-w-[1000px]" show={show} onClose={onClose}>
      <form onSubmit={handleCancelPreAuth}>
        <div className="mb-8 flex justify-between">
          <Text className="grow text-center text-2xl" variant="subheading">
            Revoke Pre-authorization
          </Text>

          <button type="button" onClick={onClose}>
            <XMarkIcon className="w-6" strokeWidth={2} />
          </button>
        </div>

        <Text variant="description" className="mb-8 italic">
          Please add a reason for revoking pre-authorization
        </Text>

        <fieldset className="rounded-md border  border-gray-300 px-2 py-1">
          <legend className="text-sm text-midGray">
            Reason <span className="text-darkRed">*</span>
          </legend>

          <textarea
            required
            rows={3}
            className="w-full resize-none rounded border-none outline-none placeholder:text-gray-400 "
            placeholder="Add a reason for the cancel..."
            value={reason}
            onChange={handleReasonChange}
          />
        </fieldset>

        <div className="mt-12 flex justify-end gap-12">
          <Button type="button" onClick={handleClose} variant="outlined">
            Close
          </Button>
          <Button className="bg-[#DC2626]">Revoke</Button>
        </div>
      </form>
    </DialogWrapper>
  );
}
