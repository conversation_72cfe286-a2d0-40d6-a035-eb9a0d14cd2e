import { PreAuth, PreAuthStatus, preauthStatusLabels } from "~lib/api/types";
import { BadgeColor } from "../../components/ui/Badge";

type LabelAndColor = {
  label: string;
  color: BadgeColor;
};

export function getPreAuthBadgeLabelAndColor(preAuth: PreAuth): LabelAndColor {
  // handle display of incomplete status
  const shouldOverrideDisplayingIncompleteStatus = [
    PreAuthStatus.CANCELLED,
    PreAuthStatus.REVOKED,
  ].includes(preAuth.status);

  if (preAuth?.markAsIncomplete && !shouldOverrideDisplayingIncompleteStatus) {
    return { label: "Incomplete", color: "blue" };
  }

  // handle display of other statuses
  const statusColorMap: Record<PreAuthStatus, BadgeColor> = {
    [PreAuthStatus.PENDING]: "yellow",
    [PreAuthStatus.DRAFT]: "yellow",
    [PreAuthStatus.ACTIVE]: "green",
    [PreAuthStatus.AUTHORIZED]: "green",
    [PreAuthStatus.CLAIMED]: "green",
    [PreAuthStatus.DECLINED]: "red",
    [PreAuthStatus.CANCELLED]: "red",
    [PreAuthStatus.REVOKED]: "red",
    [PreAuthStatus.INCOMPLETE]: "blue",
    [PreAuthStatus.INACTIVE]: "gray",
    [PreAuthStatus.EXPIRED]: "gray",
    [PreAuthStatus.WITHDRAWN]: "gray",
  };

  const color = statusColorMap[preAuth.status] || "gray";
  const label = preauthStatusLabels.get(preAuth.status) || preAuth.status;

  return { label, color };
}
