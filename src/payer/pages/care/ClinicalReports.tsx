import { <PERSON>u, Popover, Transition } from "@headlessui/react";
import { Fragment, ReactNode, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import {
  useExportClaimsMutation,
  useGetPayerPoliciesQuery,
  useGetPoliciesCategoriesQuery,
  useLazyGetClaimsQuery,
  useExportInvoicesMutation,
} from "~lib/api";
import {
  ClaimStatus,
  ClaimsReportType,
  ExportFileType,
  FilterVisits,
  PayerStatus,
  PolicyStatus,
  SearchProviderFilter,
  ServiceGroup,
  VisitType,
  claimReportsStatuses,
  claimStatusLabels,
  payerStatusLabels,
  serviceGroupLabels,
  visitTypeLabels,
} from "~lib/api/types";
import doctorSvg from "~lib/assets/doctor.svg";
import AuthenticationError from "~lib/components/AuthenticationError";
import CheckboxGroup from "~lib/components/CheckboxGroup";
import DateRangePicker from "~lib/components/DateRangePicker";
import Empty from "~lib/components/Empty";
import { Form } from "~lib/components/Form";
import Pagination from "~lib/components/Pagination";
import RadioGroup from "~lib/components/RadioGroup";
import Select from "~lib/components/Select";
import SelectProvider from "~lib/components/SelectProvider";
import SelectProviders from "~lib/components/SelectProviders";
import { dropdownTransitions } from "~lib/constants";
import usePrevious from "~lib/hooks/usePrevious";
import { Entries, Option } from "~lib/types";
import {
  arrayJoin,
  arrayOrUndefined,
  clsx,
  formValueTruthy,
  formatDateISO,
  formatDateStringGB,
  formatMoney,
  numberArrayOrUndefined,
  optionNumberArrayOrUndefined,
} from "~lib/utils";
import { getToday } from "~lib/utils/dates";
import UserService from "../../services/UserService";

enum FiltersType {
  SCHEME_WISE = "SCHEME_WISE",
  PROVIDER_WISE = "PROVIDER_WISE",
}

enum FilterType {
  CLAIM_STATUS = "CLAIM_STATUS",
  PAYER_STATUS = "PAYER_STATUS",
  SCHEMES = "SCHEMES",
  SCHEME = "SCHEME",
  VISIT_TYPES = "VISIT_TYPES",
  MAIN_FACILITY = "MAIN_FACILITY",
  SERVICE_GROUPS = "SERVICE_GROUPS",
  BRANCHES = "BRANCHES",
  CATEGORIES = "CATEGORIES",
}

type Filters = Omit<FilterVisits, "payerId">;

type Filter = {
  render: ReactNode;
  mandatory?: boolean;
  input: keyof Inputs;
  filter: keyof Filters;
};

interface Inputs {
  policyId: string;
  policyIds?: string[];
  categoryIds?: string[];
  serviceGroups?: ServiceGroup[];
  statuses?: ClaimStatus[];
  visitTypes?: VisitType[];
  payerStatus?: PayerStatus;
  mainFacility?: Option<string>;
  branches?: Option<string>[];
}

const DEFAULT_SIZE = 10;
const DEFAULT_PAGE = 1;
const NULL_DIAGNOSIS_CODE = "null"; // Some visits have visits with diagnosis code "null"

const filterLabels = new Map<keyof Inputs, string>([
  ["policyId", "Scheme"],
  ["policyIds", "Schemes"],
  ["categoryIds", "Category"],
  ["branches", "Branch"],
  ["mainFacility", "Facility"],
  ["serviceGroups", "Benefit"],
  ["statuses", "Status"],
  ["visitTypes", "Visit Type"],
  ["payerStatus", "Delivery Status"],
]);

const filterTypeLabels = new Map<FiltersType, string>([
  [FiltersType.SCHEME_WISE, "Scheme-wise"],
  [FiltersType.PROVIDER_WISE, "Provider-wise"],
]);

const defaultValues: Inputs = {
  policyId: undefined,
  policyIds: [],
  categoryIds: [],
  branches: [],
  serviceGroups: [],
  statuses: [],
  visitTypes: [],
  payerStatus: undefined,
  mainFacility: undefined,
};

const ClinicalReports = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [filtersType, setFiltersType] = useState(FiltersType.SCHEME_WISE);
  const [page, setPage] = useState(DEFAULT_PAGE);
  const [size, setSize] = useState(DEFAULT_SIZE);
  const [dateRange, setDateRange] = useState<[Date?, Date?]>([undefined, undefined]);
  const [filters, setFilters] = useState<Filters | undefined>();

  const methods = useForm<Inputs>({
    defaultValues,
  });

  const payerId = UserService.getPayer().tokenParsed.payerId;

  const { watch, reset, resetField, handleSubmit } = methods;
  const form = watch();

  const { policyIds, mainFacility, policyId } = form;

  const previousPolicyIds = usePrevious(policyIds);
  const previousPolicyId = usePrevious(policyId);

  const activeFilterEntries = Object.entries(form).filter(([, value]) =>
    Array.isArray(value) ? value.length > 0 : Boolean(value),
  ) as Entries<Inputs>;

  const mainFacilityId = Number(mainFacility?.value) || 0;

  const formActive = (key: keyof Inputs) => formValueTruthy(form[key]);
  const filterActive = (key: keyof Filters) => formValueTruthy(filters?.[key]);

  const {
    data: policies,
    isLoading: isPoliciesLoading,
    error: policiesError,
  } = useGetPayerPoliciesQuery({ payerId });

  const {
    data: categories,
    isLoading: isCategoriesLoading,
    error: categoriesError,
  } = useGetPoliciesCategoriesQuery(
    { policyIds: policyId ? [Number(policyId)] : policyIds?.map(Number) },
    { skip: !policyId && !policyIds?.length },
  );

  const [
    getClaims,
    {
      data: claimsResponse,
      isLoading: isClaimsLoading,
      isFetching: isClaimsFetching,
      error: claimsError,
      isUninitialized: isClaimsUninitialized,
    },
  ] = useLazyGetClaimsQuery();

  const [exportClaims, { isLoading: isExportClaimsLoading }] = useExportClaimsMutation();

  const claims = claimsResponse?.data?.content || [];
  const isClaimsFetchingOnly = isClaimsFetching && !isClaimsLoading;

  /**
   * Active policy on scheme utilization.
   * Used to set the default date range.
   */
  const activePolicy =
    filtersType === FiltersType.SCHEME_WISE && policyId
      ? policies?.find((p) => p.id === Number(policyId))
      : undefined;

  const isFormValid =
    filtersType == FiltersType.SCHEME_WISE ? formActive("policyId") : formActive("mainFacility");

  const isFiltersValid =
    filtersType == FiltersType.SCHEME_WISE
      ? filterActive("policyIds")
      : filterActive("mainFacilities");

  function handleChangeFiltersType(e: React.ChangeEvent<HTMLSelectElement>) {
    setFiltersType(e.target.value as FiltersType);
  }

  async function handleExport(fileType: ExportFileType) {
    try {
      await exportClaims({
        ...filters,
        fileType,
        page,
        size,
        payerId,
        reportType: ClaimsReportType.SCHEME_UTILIZATION_CLINICAL,
      }).unwrap();
    } catch (error) {
      toast.error("Something went wrong. Please try again.");

      if (import.meta.env.DEV) {
        console.error(error);
      }
    }
  }
  /**
   * Expects the following values in scope
   * page, size, payerId, mainFacilityId, dateRange, reportType
   */
  function handleSubmitFilters({
    categoryIds,
    branches,
    mainFacility,
    serviceGroups,
    statuses,
    visitTypes,
    payerStatus,
    policyIds,
    policyId,
  }: Inputs) {
    const filters: Filters = {
      policyIds: policyId ? [Number(policyId)] : numberArrayOrUndefined(policyIds),
      categoryIds: numberArrayOrUndefined(categoryIds),
      ...(mainFacility && { mainFacilities: [Number(mainFacility?.value)] }),
      ...(mainFacility && {
        providerIds: optionNumberArrayOrUndefined(branches),
      }),
      serviceGroups: arrayOrUndefined(serviceGroups),
      payerStatuses: payerStatus ? [payerStatus] : undefined,
      statuses: arrayOrUndefined(statuses),
      visitTypes: arrayOrUndefined(visitTypes),
      fromDate: formatDateISO(dateRange[0]) || undefined,
      toDate: formatDateISO(dateRange[1]) || undefined,
    };

    getClaims({
      ...filters,
      payerId,
      page,
      size,
    });

    setFilters(filters);
  }

  const getFilterValueLabel = (key: keyof Inputs) => {
    switch (key) {
      case "policyId":
        return policies?.find((p) => p.id === Number(policyId))?.plan.name || "1 selected";
      case "policyIds":
      case "categoryIds":
      case "branches":
        return `${form[key]?.length} selected`;
      case "mainFacility":
        return form[key]?.label;
      case "serviceGroups":
        return arrayJoin(form[key], (serviceGroup) => serviceGroupLabels.get(serviceGroup));
      case "statuses":
        return arrayJoin(form[key], (status) => claimStatusLabels.get(status));
      case "visitTypes":
        return arrayJoin(form[key], (visitType) => visitTypeLabels.get(visitType));
      case "payerStatus":
        return form[key] ? payerStatusLabels.get(form[key] as PayerStatus) : "All";
      default:
        return (form[key] as Inputs[keyof Inputs])?.toString();
    }
  };

  const arrayEllipsis = (array: unknown[], max = 3) =>
    array.length > 3 ? `${array.slice(0, max).join(", ")}...` : array.join(", ");

  /**
   * TODO: Reset page/size before submitting filters?
   */

  /**
   * Reset filters when filters, page, and size when the filters type changes
   */
  useEffect(() => {
    reset();
    setFilters(undefined);
    setPage(DEFAULT_PAGE);
    setSize(DEFAULT_SIZE);

    switch (filtersType) {
      case FiltersType.SCHEME_WISE:
        // Set to undefined until user selects a scheme
        setDateRange([undefined, undefined]);
        break;
      case FiltersType.PROVIDER_WISE:
        // Today
        setDateRange(getToday());
        break;
    }
  }, [filtersType, reset]);

  /**
   * Reset branches when main facility changes
   */
  useEffect(() => {
    resetField("branches");
  }, [mainFacilityId, resetField]);

  /**
   * Reset categories when schemes change
   */
  useEffect(() => {
    const policyIdsChanged = policyIds?.some((policyId) => !previousPolicyIds?.includes(policyId));

    if (policyId !== previousPolicyId || policyIdsChanged) {
      resetField("categoryIds");
    }
  }, [policyIds, policyId, previousPolicyId, previousPolicyIds, resetField]);

  /**
   * Set the date range to the active policy's start and end dates
   */
  useEffect(() => {
    if (activePolicy) {
      setDateRange([new Date(activePolicy.startDate), new Date(activePolicy.endDate)]);
    }
  }, [activePolicy]);

  const filterElements = new Map<FilterType, Filter>([
    [
      FilterType.CLAIM_STATUS,
      {
        input: "statuses",
        filter: "statuses",
        render: (
          <details>
            <summary className="mb-2">Claim Status</summary>

            <CheckboxGroup
              name="statuses"
              options={claimReportsStatuses.map((status) => ({
                label: claimStatusLabels.get(status),
                value: status,
              }))}
              showFilter={false}
            />
          </details>
        ),
      },
    ],
    [
      FilterType.SCHEMES,
      {
        input: "policyIds",
        filter: "policyIds",
        render: (
          <details>
            <summary className="mb-2">Schemes</summary>

            <div>
              {isPoliciesLoading ? (
                <div className="flex items-center justify-center py-8">
                  {/* prettier-ignore */}
                  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-blue-400 w-8 h-8">
                    <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
                  </svg>
                </div>
              ) : policiesError ? (
                <div className="px-2 py-8 text-red-500">
                  Something went wrong while fetching schemes. Please refresh the page to retry.
                </div>
              ) : !policies?.length ? (
                <Empty message="No schemes found" />
              ) : (
                <div>
                  <CheckboxGroup
                    name="policyIds"
                    label="Schemes"
                    options={policies
                      .filter((policy) => policy.status == PolicyStatus.ACTIVE)
                      .map((policy) => ({
                        label: `${policy.plan.name}${
                          policy.status == PolicyStatus.EXPIRED ? " (Inactive)" : ""
                        }`,
                        value: policy.id.toString(),
                      }))}
                    fieldSetClassName="max-h-72 overflow-y-auto"
                  />
                </div>
              )}
            </div>
          </details>
        ),
      },
    ],
    [
      FilterType.SCHEME,
      {
        input: "policyId",
        filter: "policyIds",
        render: (
          <details>
            <summary className="mb-2">Scheme</summary>

            <div>
              {isPoliciesLoading ? (
                <div className="flex items-center justify-center py-8">
                  {/* prettier-ignore */}
                  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-blue-400 w-8 h-8">
                    <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
                  </svg>
                </div>
              ) : policiesError ? (
                <div className="px-2 py-8 text-red-500">
                  Something went wrong while fetching schemes. Please refresh the page to retry.
                </div>
              ) : !policies?.length ? (
                <Empty message="No schemes found" />
              ) : (
                <div>
                  <Select
                    name="policyId"
                    options={policies
                      .filter((policy) => policy.status === PolicyStatus.ACTIVE)
                      .map((policy) => ({
                        label: policy.plan.name,
                        value: policy.id.toString(),
                      }))}
                    placeholder="Search scheme"
                    styles={{
                      menuList: (provided) => ({
                        ...provided,
                        height: "120px",
                      }),
                    }}
                    required={true}
                  />
                </div>
              )}
            </div>
          </details>
        ),
      },
    ],
    [
      FilterType.VISIT_TYPES,
      {
        input: "visitTypes",
        filter: "visitTypes",
        render: (
          <details>
            <summary className="mb-2">Visit Type</summary>

            <CheckboxGroup
              name="visitTypes"
              options={Object.values(VisitType).map((visitType) => ({
                label: visitTypeLabels.get(visitType),
                value: visitType,
              }))}
              showFilter={false}
            />
          </details>
        ),
      },
    ],
    [
      FilterType.PAYER_STATUS,
      {
        input: "payerStatus",
        filter: "payerStatuses",
        render: (
          <details>
            <summary className="mb-2">Delivery Status</summary>

            <RadioGroup
              name="payerStatus"
              options={Object.values(PayerStatus).map((status) => ({
                label: payerStatusLabels.get(status),
                value: status,
              }))}
            />
          </details>
        ),
      },
    ],
    [
      FilterType.MAIN_FACILITY,
      {
        input: "mainFacility",
        filter: "mainFacilities",
        render: (
          <details>
            <summary className="mb-2">Main Facility</summary>

            <SelectProvider
              name="mainFacility"
              type={SearchProviderFilter.MAIN}
              placeholder="Search provider..."
            />
          </details>
        ),
      },
    ],
    [
      FilterType.SERVICE_GROUPS,
      {
        input: "serviceGroups",
        filter: "serviceGroups",
        render: (
          <details>
            <summary className="mb-2">Benefits</summary>

            <CheckboxGroup
              name="serviceGroups"
              options={Object.values(ServiceGroup).map((serviceGroup) => ({
                label: serviceGroupLabels.get(serviceGroup),
                value: serviceGroup,
              }))}
              showFilter={false}
            />
          </details>
        ),
      },
    ],
    [
      FilterType.BRANCHES,
      {
        input: "branches",
        filter: "providerIds",
        render: (
          <details>
            <summary className="mb-2">Branch</summary>

            {!mainFacilityId ? (
              <p className="px-2 py-4 text-sm text-gray-400">
                Select a main facility to select branches
              </p>
            ) : (
              <SelectProviders
                name="branches"
                fieldsetClassName="max-h-72 overflow-y-auto"
                mainProvider={mainFacilityId}
                type={SearchProviderFilter.BRANCH}
              />
            )}
          </details>
        ),
      },
    ],
    [
      FilterType.CATEGORIES,
      {
        input: "categoryIds",
        filter: "categoryIds",
        render: (
          <details>
            <summary className="mb-2">Categories</summary>

            <div>
              {!(policyId || policyIds?.length) ? (
                <p className="px-2 py-4 text-sm text-gray-400">
                  Select one or more schemes to view categories
                </p>
              ) : isCategoriesLoading ? (
                <div className="flex items-center justify-center py-8">
                  {/* prettier-ignore */}
                  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-blue-400 w-8 h-8">
                    <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
                  </svg>
                </div>
              ) : categoriesError ? (
                <div className="px-2 py-8 text-red-500">
                  Something went wrong while fetching categories. Please refresh the page to retry.
                </div>
              ) : !categories?.length ? (
                <Empty message="No categories found" />
              ) : (
                <div>
                  <CheckboxGroup
                    name="categoryIds"
                    label="Categories"
                    options={categories.map((category) => ({
                      label: `${category.policy.plan.name} - ${category.name}`,
                      value: category.id.toString(),
                    }))}
                    fieldSetClassName="max-h-72 overflow-y-auto"
                  />
                </div>
              )}
            </div>
          </details>
        ),
      },
    ],
  ]);

  const filterTypesFilters = new Map<FiltersType, Filter[]>([
    [
      FiltersType.SCHEME_WISE,
      [
        {
          mandatory: true,
          ...filterElements.get(FilterType.SCHEME),
        },
        filterElements.get(FilterType.CATEGORIES),
        filterElements.get(FilterType.VISIT_TYPES),
        filterElements.get(FilterType.CLAIM_STATUS),
      ],
    ],
    [
      FiltersType.PROVIDER_WISE,
      [
        {
          mandatory: true,
          ...filterElements.get(FilterType.MAIN_FACILITY),
        },
        filterElements.get(FilterType.BRANCHES),
        filterElements.get(FilterType.SCHEMES),
        filterElements.get(FilterType.VISIT_TYPES),
        filterElements.get(FilterType.SERVICE_GROUPS),
        filterElements.get(FilterType.CLAIM_STATUS),
      ],
    ],
  ]);

  const SubmitFilters = (
    <Popover.Button
      type="button"
      className={clsx(
        "flex items-center justify-center gap-2 rounded-md bg-blue-500 px-8 py-2 text-center font-medium text-white focus:ring-2 focus:ring-blue-700",
        !isFormValid || isClaimsLoading ? "cursor-not-allowed opacity-40" : "hover:bg-blue-600",
      )}
      title={!isFormValid ? "Select all required filters" : "Search"}
      disabled={isClaimsLoading || !isFormValid}
      onClick={() => {
        handleSubmit(handleSubmitFilters)();
      }}
    >
      Search
    </Popover.Button>
  );

  useEffect(() => {
    if (isFormValid && !isClaimsFetching && !isClaimsLoading) {
      // Fetch claims when page and size change
      handleSubmit(handleSubmitFilters)();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, size]);

  return (
    <div className="flex h-full overflow-hidden">
      <div className="relative flex max-h-screen flex-1 flex-col overflow-y-auto overflow-x-hidden ">
        <main className="px-10 pb-10 text-gray-600">
          {!payerId && <AuthenticationError />}

          {/* Filters */}
          <div className="mb-4 flex flex-wrap items-end justify-between gap-4 py-2">
            <div className="flex items-center gap-2">
              <div>
                <label htmlFor="report-type" className="text-gray-500" title="Filters Type">
                  Filters Type:{" "}
                </label>

                <select
                  id="report-type"
                  value={filtersType}
                  onChange={handleChangeFiltersType}
                  className="rounded border border-gray-300 "
                >
                  {Object.values(FiltersType).map((type) => (
                    <option value={type} key={type}>
                      {filterTypeLabels.get(type)}
                    </option>
                  ))}
                </select>
              </div>

              <Popover className="relative">
                <Popover.Button
                  className={clsx(
                    "inline-flex w-full items-center justify-center gap-2 rounded-md border border-gray-300 px-4 py-2 text-sm font-medium hover:border-gray-400",
                  )}
                >
                  {/* prettier-ignore */}
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 01-.659 1.591l-5.432 5.432a2.25 2.25 0 00-.659 1.591v2.927a2.25 2.25 0 01-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 00-.659-1.591L3.659 7.409A2.25 2.25 0 013 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0112 3z" />
                  </svg>
                  Filter
                  {/* prettier-ignore */}
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="-mr-1 h-5 w-5">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                  </svg>
                </Popover.Button>

                <Transition as={Fragment} {...dropdownTransitions}>
                  <Popover.Panel className="absolute z-10 mt-2 w-96 origin-top-right overflow-y-auto rounded-lg border border-gray-300 bg-gray-50 p-4 shadow focus:outline-none">
                    <div>
                      <div className="flex justify-between gap-2">
                        <p className="uppercase text-gray-400">Filtered By</p>

                        <button
                          onClick={() => reset()}
                          className={clsx(
                            "text-sm",
                            !activeFilterEntries.length
                              ? "cursor-not-allowed text-gray-300"
                              : "text-red-500",
                          )}
                          disabled={!activeFilterEntries.length}
                        >
                          Clear All
                        </button>
                      </div>

                      {!activeFilterEntries.length ? (
                        <p className="py-4 text-center text-gray-400">No filters applied</p>
                      ) : (
                        <div className="flex flex-col gap-1 py-2 text-sm">
                          {activeFilterEntries.map(([key]) => (
                            <div key={key} className="flex justify-between gap-2">
                              <p>
                                <span className="text-gray-300">{filterLabels.get(key)}: </span>
                                <span className="text-gray-400">
                                  {getFilterValueLabel(key as keyof Inputs)}
                                </span>
                              </p>

                              <button
                                className="text-red-400"
                                title="Remove filter"
                                onClick={() => {
                                  resetField(key as keyof Inputs);
                                }}
                              >
                                {/* prettier-ignore */}
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-4 h-4">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                              </button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    <Form
                      className="flex flex-col gap-2"
                      methods={methods}
                      onSubmit={handleSubmitFilters}
                    >
                      {filterTypesFilters.get(filtersType).map((filter) => filter.render)}

                      {SubmitFilters}
                    </Form>
                  </Popover.Panel>
                </Transition>
              </Popover>

              <DateRangePicker
                value={dateRange}
                onChange={setDateRange}
                options={
                  activePolicy
                    ? {
                        minDate: activePolicy.startDate,
                        maxDate: activePolicy.endDate,
                      }
                    : {}
                }
                footer={<div className="flex justify-end py-1">{SubmitFilters}</div>}
              />

              <div>
                {isClaimsFetchingOnly && (
                  <>
                    {/* prettier-ignore */}
                    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-gray-400 w-6 h-6">
                      <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z">
                        <animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/>
                      </path>
                    </svg>
                  </>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Menu as="div" className="relative inline-block text-left">
                <div>
                  <Menu.Button
                    className={clsx(
                      "inline-flex w-full items-center justify-center gap-2 rounded-md bg-blue-500 px-4 py-2 text-sm font-medium text-white",
                      isExportClaimsLoading || !isFiltersValid
                        ? "cursor-not-allowed opacity-60"
                        : "hover:bg-blue-600",
                    )}
                    disabled={isExportClaimsLoading || !isFiltersValid}
                  >
                    {isExportClaimsLoading && (
                      <>
                        {/* prettier-ignore */}
                        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="h-5 w-5">
                          <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
                        </svg>
                      </>
                    )}
                    {/* prettier-ignore */}
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="h-5 w-5">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"/>
                    </svg>
                    Export
                    {/* prettier-ignore */}
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="-mr-1 h-5 w-5">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                    </svg>
                  </Menu.Button>
                </div>

                <Transition as={Fragment} {...dropdownTransitions}>
                  <Menu.Items className="absolute right-0 mt-2 w-36 origin-top-right rounded-md border border-gray-200 bg-white focus:outline-none">
                    {Object.values(ExportFileType)
                      .filter((fileType) => fileType != ExportFileType.PDF)
                      .map((fileType) => (
                        <Menu.Item key={fileType}>
                          {({ active }) => (
                            <button
                              className={`${
                                active ? "bg-gray-50" : ""
                              } group flex w-full items-center rounded-md px-6 py-4 text-sm`}
                              onClick={() => handleExport(ExportFileType[fileType])}
                            >
                              {fileType}
                            </button>
                          )}
                        </Menu.Item>
                      ))}
                  </Menu.Items>
                </Transition>
              </Menu>
            </div>
          </div>

          {/* Content */}
          <div className="flex-grow pb-4">
            {!isFiltersValid || isClaimsUninitialized ? (
              <div className="flex items-center justify-center p-4">
                <div className="flex flex-col items-center gap-1">
                  <img src={doctorSvg} alt="Image of a doctor" className="h-auto w-48" />

                  <h2 className="text-center text-2xl font-medium">No reports</h2>

                  <p className="text-center text-gray-300">
                    Click on the filter button to select the reports you want to view...
                  </p>
                </div>
              </div>
            ) : isClaimsLoading ? (
              <div className="flex items-center justify-center py-8">
                {/* prettier-ignore */}
                <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-blue-400">
                  <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
                </svg>
              </div>
            ) : claimsError ? (
              <div className="px-2 py-8 text-red-500">
                Something went wrong. Please refresh the page to retry.
              </div>
            ) : !claims.length ? (
              <div className="flex items-center justify-center p-8">
                <div className="flex flex-col items-center">
                  <Empty message="No claims matching filters" />
                </div>
              </div>
            ) : (
              <div>
                <div className="mb-4 max-w-full overflow-x-auto">
                  <table className="w-full table-auto">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="px-1 py-4 pl-8 text-left">Member Number</th>
                        <th className="px-1 py-4 text-left">Member Name</th>
                        <th className="px-1 py-4 text-left">Visit Number</th>
                        <th className="px-1 py-4 text-left">Service Group</th>
                        <th className="px-1 py-4 text-left">ICD10 Codes</th>
                        <th className="px-1 py-4 text-left">Diagnosis Desc.</th>
                        <th className="px-1 py-4 text-left">Provider</th>
                        <th className="px-1 py-4 text-left">Name</th>
                        <th className="px-1 py-4 text-left">Scheme</th>
                        <th className="px-1 py-4 text-left">Benefit</th>
                        <th className="px-1 py-4 text-left">Invoice Numbers</th>
                        <th className="px-1 py-4 text-left">Total Amount</th>
                        <th className="px-1 py-4 pr-8 text-left">Date</th>
                      </tr>
                    </thead>

                    <tbody>
                      {claims?.map((claim) => (
                        <Fragment key={claim.id}>
                          <tr>
                            <td className="px-1 py-4 pl-8">{claim.memberNumber}</td>
                            <td className="px-1 py-4">{claim.memberName}</td>
                            <td className="px-1 py-4">{claim.id}</td>
                            <td className="px-1 py-4">
                              {serviceGroupLabels.get(claim.serviceGroup)}
                            </td>
                            <td className="px-1 py-4">
                              {arrayEllipsis(
                                claim.diagnosis
                                  ?.filter((d) => d.code !== NULL_DIAGNOSIS_CODE)
                                  .map((d) => d.code),
                              ) || "-"}
                            </td>
                            <td className="px-1 py-4">
                              {arrayEllipsis(
                                claim.diagnosis
                                  ?.filter((d) => d.code !== NULL_DIAGNOSIS_CODE)
                                  .map((d) => d.title),
                                1,
                              ) || "-"}
                            </td>
                            <td className="px-1 py-4" colSpan={2}>
                              {claim.providerName}
                            </td>
                            <td className="px-1 py-4">{claim.schemeName}</td>
                            <td className="px-1 py-4">{claim.benefitName}</td>
                            <td className="px-1 py-4">
                              {arrayEllipsis(
                                claim.invoices?.map((invoice) => invoice.invoiceNumber),
                              )}
                            </td>
                            <td className="px-1 py-4">
                              {formatMoney(
                                claim?.invoices?.reduce(
                                  (acc, invoice) => acc + invoice?.totalAmount || 0,
                                  0,
                                ),
                              )}
                            </td>
                            <td className="px-1 py-4 pr-8">
                              {formatDateStringGB(claim?.invoiceDate)}
                            </td>
                          </tr>
                        </Fragment>
                      ))}
                    </tbody>
                  </table>
                </div>

                <Pagination
                  totalElements={claimsResponse.data.totalElements}
                  totalPages={claimsResponse.data.totalPages}
                  page={page}
                  size={size}
                  setPage={setPage}
                  setSize={setSize}
                  isLoading={isClaimsFetchingOnly}
                />
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
};

export default ClinicalReports;
