import { Menu, Transition } from "@headlessui/react";
import {
  ArrowDownTrayIcon,
  ArrowPathIcon,
  ChevronDownIcon,
  ChevronRightIcon,
} from "@heroicons/react/24/outline";
import { skipToken } from "@reduxjs/toolkit/query";
import { Fragment, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import {
  api,
  useActivateVisitMutation,
  useExportClaimsMutation,
  useSearchVisitsQuery,
} from "~lib/api";
import {
  Pagination as APIPagination,
  ClaimsReportType,
  ExportFileType,
  FilterVisits,
  Visit,
  VisitStatus,
  VisitType,
  visitStatusLabels,
} from "~lib/api/types";
import { AsyncSelect, DateInput, Empty, FieldWrapper, Input, Modal, Select } from "~lib/components";
import ErrorMessage from "~lib/components/Error";
import { Form } from "~lib/components/Form";
import StatusBadge from "~lib/components/StatusBadge";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { MAX_INVOICES_PER_VISIT, PAGE_SIZES, dropdownTransitions } from "~lib/constants";
import { useDebounce } from "~lib/hooks/useDebounce";
import useSearchProviderOptions from "~lib/hooks/useSearchProviderOptions";
import { Option } from "~lib/types";
import { clsx, formatDateTime, getVisitStatusBadge, responseError, truncate } from "~lib/utils";
import MainWrapper from "../../components/ui/MainWrapper";
import PrimaryPagination from "../../components/ui/pagination/PrimaryPagination";
import TableDataItem from "../../components/ui/table/TableDataItem";
import TableHeaderItem from "../../components/ui/table/TableHeaderItem";
import UserService from "../../services/UserService";
import { useAppDispatch } from "../../store/hooks";

interface Inputs {
  provider?: Option<string> | undefined;
  status: VisitStatus | undefined;
  query?: string;
  startDate?: string;
  endDate?: string;
}

enum PageModal {
  ACTIVATE_VISIT,
}

const VISIT_TYPES = [VisitType.ONLINE];
const VISIT_STATUSES = [VisitStatus.PENDING, VisitStatus.INACTIVE, VisitStatus.CANCELLED];

/**
 * TODO: Add visit reactivation reason
 */
export const VisitReactivation = () => {
  const [page, setPage] = useState(1);
  const [size, setSize] = useState<number>(PAGE_SIZES[0]);
  const [activeVisitId, setActiveVisitId] = useState<number | undefined>();
  const [activeModal, setActiveModal] = useState<PageModal | undefined>();

  const dispatch = useAppDispatch();

  const methods = useForm<Inputs>({
    mode: "onBlur",
    defaultValues: {
      provider: undefined,
      query: "",
      status: undefined,
      startDate: "",
      endDate: "",
    },
  });

  const { watch, setValue } = methods;

  const form = watch();
  const queryDebounced = useDebounce(form.query, 200);

  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];

  const params = {
    statuses: form.status ? [form.status] : VISIT_STATUSES, // fetch filtered visit statuses, or all permissible statuses
    visitTypes: VISIT_TYPES, // fetch online visits
    payerIds: [Number(payerId)],
    query: queryDebounced,
    startDate: form.startDate,
    endDate: form.endDate,
    providerIds: form.provider ? [Number(form.provider.value)] : undefined,
  };

  const exportParams: FilterVisits = {
    ...params,
    ...(queryDebounced ? { query: queryDebounced } : undefined),
  };

  const {
    data: visitsResponse,
    error: visitsError,
    isLoading: isVisitsLoading,
    isFetching: isVisitsFetching,
  } = useSearchVisitsQuery(!payerId ? skipToken : { page, size, ...params }, {
    refetchOnFocus: true,
    /**
     * payerId should always be defined because of initial auth check.
     * In case it isn't, we shouldn't fetch visits for other payers.
     */
    skip: !payerId,
  });

  const [exportClaims, { isLoading: isExportLoading }] = useExportClaimsMutation();
  const [activateVisit, { isLoading: isActivateVisitLoading }] = useActivateVisitMutation();

  const visits = visitsResponse?.data.content;
  const isVisitsFetchingOnly = isVisitsFetching && !isVisitsLoading;

  const { getProviderOptions } = useSearchProviderOptions();

  const isExportFiltersValid = Boolean(form.startDate && form.endDate);
  const isExportDisabled = isExportLoading || !isExportFiltersValid;

  const refresh = () => dispatch(api.util.invalidateTags(["Visits"]));

  function handleActivateVisit(id: number) {
    setActiveVisitId(id);
    setActiveModal(PageModal.ACTIVATE_VISIT);
  }

  function handleCloseActivateModal() {
    setActiveVisitId(undefined);
    setActiveModal(undefined);
  }

  async function handleConfirmActivate() {
    try {
      if (!activeVisitId) {
        throw new Error("No visit selected");
      }

      const response = await activateVisit({
        visitId: activeVisitId,
      });

      const message = responseError(response);
      if (message) {
        throw new Error(message);
      }

      toast.success("Visit re-activated successfully");
      handleCloseActivateModal();
    } catch (error) {
      toast.error((error as Error)?.message || "Something went wrong");
    }
  }

  async function handleExport(fileType: ExportFileType, visitId?: number) {
    setActiveVisitId(visitId);

    const { payerIds } = exportParams;

    type ExportClaimsReportParams = {
      fileType: ExportFileType;
      reportType: ClaimsReportType;
    };

    const exportVisitParams: FilterVisits & APIPagination & ExportClaimsReportParams = {
      visitNumbers: visitId ? [visitId] : [],
      payerIds,
      page: 1,
      size: MAX_INVOICES_PER_VISIT,
      reportType: ClaimsReportType.SCHEME_UTILIZATION_CLINICAL,
      fileType,
    };

    const exportVisitsParams = {
      ...exportParams,
      fileType,
      page,
      size,
      reportType: ClaimsReportType.SCHEME_UTILIZATION_CLINICAL,
    };

    try {
      await exportClaims(visitId ? exportVisitParams : exportVisitsParams).unwrap();
    } catch (error) {
      toast.error("Something went wrong. Please try again.");

      if (import.meta.env.DEV) {
        console.error(error);
      }
    }
  }

  useEffect(() => {
    // "Default" status
    setValue("status", VisitStatus.PENDING);
  }, [setValue]);

  const handlePageNumberClick = (page: number) => {
    setPage(page);
  };

  const handleSizeChange = (size: number) => {
    setSize(size);
    setPage(1);
  };

  return (
    <MainWrapper>
      <div className="flex flex-1 flex-col overflow-y-auto overflow-x-hidden">
        <main>
          <div className="w-full pb-8 text-gray-600">
            <div className="max-w-full rounded-md bg-white">
              <div className="z-10">
                <Form
                  className="pt-2 text-sm"
                  methods={methods}
                  onSubmit={(_data, e) => {
                    e?.preventDefault();
                  }}
                >
                  <div className="flex items-center justify-center pb-2">
                    <hgroup className="flex items-center gap-2">
                      <h2 className="text-lg font-medium capitalize">Visit Reactivation</h2>
                      {isVisitsFetchingOnly ? (
                        <LoadingIcon className="h-5 w-5 text-gray-400" />
                      ) : (
                        <button
                          title="Refresh"
                          className="flex gap-2 rounded-full bg-transparent font-medium text-gray-400 enabled:hover:text-gray-500 disabled:cursor-not-allowed disabled:opacity-60"
                          type="button"
                          onClick={() => {
                            refresh();
                          }}
                        >
                          <ArrowPathIcon className="h-5 w-5" />
                        </button>
                      )}
                    </hgroup>
                  </div>

                  <details className="[&>summary_svg.chevron]:open:rotate-90" open>
                    <summary className="mb-2 flex justify-between gap-4 font-medium">
                      <span className="flex gap-2 py-2">
                        <ChevronRightIcon className="chevron h-5 w-5 shrink-0 rotate-0 transform transition-all duration-300" />
                        Filter Visits
                      </span>

                      <Menu as="div" className="relative inline-block text-left text-sm">
                        <div>
                          <Menu.Button
                            className={clsx(
                              "flex gap-2 rounded border border-blue-500 px-4 py-2 font-medium text-blue-500",
                              isExportDisabled
                                ? "cursor-not-allowed opacity-60"
                                : "hover:border-blue-600 hover:text-blue-600",
                            )}
                            disabled={isExportDisabled}
                          >
                            {isExportLoading && <LoadingIcon className="h-5 w-5" />}
                            <ArrowDownTrayIcon strokeWidth={1.5} className="h-5 w-5" />
                            <span>Export</span>
                            <ChevronDownIcon strokeWidth={1.5} className="-mr-1 h-5 w-5" />
                          </Menu.Button>
                        </div>

                        <Transition as={Fragment} {...dropdownTransitions}>
                          <Menu.Items className="absolute right-0 mt-2 w-36 origin-top-right rounded-md border border-gray-200 bg-white focus:outline-none">
                            {Object.values(ExportFileType).map((fileType) => (
                              <Menu.Item key={fileType}>
                                {({ active }) => (
                                  <button
                                    className={`${
                                      active ? "bg-gray-50" : ""
                                    } group flex w-full items-center rounded-md px-6 py-4 text-sm`}
                                    onClick={() => {
                                      handleExport(fileType);
                                    }}
                                    disabled={isExportLoading}
                                  >
                                    {fileType}
                                  </button>
                                )}
                              </Menu.Item>
                            ))}
                          </Menu.Items>
                        </Transition>
                      </Menu>
                    </summary>

                    <div className="flex flex-wrap justify-start gap-2">
                      <FieldWrapper
                        name="provider"
                        label="Provider"
                        className="max-w-xs flex-shrink flex-grow"
                        labelClassName="font-semibold text-gray-500"
                      >
                        <AsyncSelect
                          name="provider"
                          getOptions={getProviderOptions}
                          placeholder="Search provider..."
                          className="min-w-56"
                        />
                      </FieldWrapper>

                      <FieldWrapper name="status" label="Status">
                        <Select
                          options={VISIT_STATUSES.map((value) => ({
                            label: visitStatusLabels.get(value) || "Unknown",
                            value,
                          }))}
                          placeholder="Select..."
                          className="min-w-44 max-w-xs"
                          isClearable
                        />
                      </FieldWrapper>

                      <FieldWrapper
                        name="query"
                        label="Search"
                        className="flex-grow"
                        labelClassName="font-semibold text-gray-500"
                      >
                        <Input
                          type="text"
                          placeholder="Member number or invoice number..."
                          className="max-w-full text-sm"
                        />
                      </FieldWrapper>

                      {/* TODO: Fix date font size */}
                      <FieldWrapper
                        name="startDate"
                        label="Start Date"
                        labelClassName="font-semibold text-gray-500"
                      >
                        <DateInput
                          placeholder={new Date().toISOString().split("T")[0]}
                          maxDate="today"
                          className="w-36 text-sm"
                        />
                      </FieldWrapper>

                      <FieldWrapper
                        name="endDate"
                        label="End Date"
                        labelClassName="font-semibold text-gray-500"
                      >
                        <DateInput
                          placeholder={new Date().toISOString().split("T")[0]}
                          maxDate="today"
                          className="w-36 text-sm"
                        />
                      </FieldWrapper>
                    </div>
                  </details>
                </Form>
              </div>

              <div className="mb-4">
                {isVisitsLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <LoadingIcon className="h-6 w-6 text-blue-400" />
                  </div>
                ) : visitsError ? (
                  <ErrorMessage title="Error fetching visits" message="Refresh to retry" />
                ) : !visits?.length ? (
                  <Empty message={`No visits found`} />
                ) : (
                  <div className="overflow-x-auto bg-white text-gray-600">
                    <table className="w-full">
                      <thead className="text-left">
                        <tr className="bg-gray-100">
                          <TableHeaderItem item="Visit Number" />
                          <TableHeaderItem item="Member Number" />
                          <TableHeaderItem item="Member Name" />
                          <TableHeaderItem item="Provider" />
                          <TableHeaderItem item="Scheme" />
                          <TableHeaderItem item="Benefit" />
                          <TableHeaderItem item="Added By" />
                          <TableHeaderItem item="Status" />
                          <TableHeaderItem item="Date" />
                          <TableHeaderItem item="Action" />
                        </tr>
                      </thead>

                      <tbody>
                        {visits.map((visit: Visit) => (
                          <tr key={visit.id}>
                            <TableDataItem item={visit.id} />
                            <TableDataItem item={visit.memberNumber} />
                            <TableDataItem item={visit.memberName} />
                            <TableDataItem
                              item={
                                truncate(visit.providerName || visit.reimbursementProvider, 30) ||
                                "NA"
                              }
                              title={visit.providerName || visit.reimbursementProvider}
                            />
                            <TableDataItem
                              item={truncate(visit.schemeName || visit?.scheme?.name, 30) || "NA"}
                              title={visit.schemeName || visit?.scheme?.name}
                            />
                            <TableDataItem
                              item={truncate(visit.benefitName, 30) || "NA"}
                              title={visit.benefitName}
                            />
                            <TableDataItem
                              item={truncate(visit.staffName, 12) || "NA"}
                              title={visit.staffName}
                            />
                            <td className="border-b border-[#EAECF0] px-2 py-4 first:pl-8 last:pr-8">
                              <StatusBadge status={getVisitStatusBadge(visit.status)}>
                                {visitStatusLabels.get(visit.status)}
                              </StatusBadge>
                            </td>
                            <TableDataItem
                              item={
                                visit.createdAt
                                  ? formatDateTime(new Date(visit.createdAt + "Z"))
                                  : ""
                              }
                            />
                            <td className="border-b border-[#EAECF0] px-2 py-4 first:pl-8 last:pr-8">
                              <div>
                                <button
                                  className="flex items-center gap-2 rounded bg-blue-500 px-4 py-2 text-sm font-medium text-white enabled:hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-80"
                                  title="Export"
                                  onClick={() => {
                                    handleActivateVisit(visit.id);
                                  }}
                                  disabled={visit.status == VisitStatus.CANCELLED}
                                >
                                  Reactivate
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>

              {visitsResponse && (
                <div className="self-start py-4">
                  <PrimaryPagination
                    pageSize={size}
                    totalElements={visitsResponse?.data.totalElements ?? 0}
                    totalPages={visitsResponse?.data.totalPages ?? 1}
                    pageNumber={page}
                    onPageNumberClick={handlePageNumberClick}
                    onSizeChange={handleSizeChange}
                  />
                </div>
              )}
            </div>
          </div>
        </main>
      </div>

      <Modal
        id="confirm-modal"
        modalOpen={activeModal == PageModal.ACTIVATE_VISIT && Boolean(activeVisitId)}
        onClose={handleCloseActivateModal}
        title="Confirm"
        size="sm"
      >
        <div className="p-4">
          <p className="mb-4 text-gray-600">
            Are you sure you want to re-activate visit{" "}
            <span className="font-medium text-gray-700">{activeVisitId}</span>?
          </p>

          <div className="flex justify-end gap-4">
            <button
              onClick={handleCloseActivateModal}
              className="flex gap-2 rounded border border-red-500 px-4 py-2 font-medium text-red-500 enabled:hover:border-red-600 enabled:hover:text-red-600"
            >
              Cancel
            </button>

            <button
              onClick={handleConfirmActivate}
              className="flex gap-2 rounded border border-blue-500 px-4 py-2 font-medium text-blue-500 enabled:hover:border-blue-600 enabled:hover:text-blue-600"
              disabled={isActivateVisitLoading}
            >
              {isActivateVisitLoading && (
                <>
                  {/* prettier-ignore */}
                  <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-blue-500">
                    <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z">
                      <animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite" />
                    </path>
                  </svg>
                </>
              )}
              OK
            </button>
          </div>
        </div>
      </Modal>
    </MainWrapper>
  );
};

export default VisitReactivation;
