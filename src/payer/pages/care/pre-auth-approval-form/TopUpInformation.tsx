import { useState } from "react";
import { PreAuthorizationTopUp } from "~lib/api/types";
import Badge, { BadgeColor } from "../../../components/ui/Badge";
import Button from "../../../components/ui/Button";
import TableDataItem from "../../../components/ui/table/TableDataItem";
import TableHeaderItem from "../../../components/ui/table/TableHeaderItem";
import { usePreAuthApprovalForm } from "./pre-auth-approval-form-context";
import PreAuthTopUpApprovalForm from "./pre-auth-top-up-approval-form/PreAuthTopUpApprovalForm";
import { usePreAuthorizationPermissions } from "../../../hooks/care/usePreAuthorizationPermissions";

export default function TopUpInformation() {
  const { preauth, hasTopUpPermissionForRequestType, hasViewPermissionForRequestType } =
    usePreAuthApprovalForm();
  const [selectedTopUp, setSelectedTopUp] = useState<PreAuthorizationTopUp | null>(null);

  const requestType = preauth.requestType as string;
  const canView = hasViewPermissionForRequestType(requestType);
  const canTopUp = hasTopUpPermissionForRequestType(requestType);

  function handleCloseTopUpApprovalModal() {
    setSelectedTopUp(null);
  }

  function openTopUpApprovalModal(topUp: PreAuthorizationTopUp) {
    setSelectedTopUp(topUp);
  }

  return (
    <div className="">
      <h3 className="mb-6 text-2xl text-[#61758A]">Top Up(s)</h3>

      <table>
        <thead>
          <tr>
            <TableHeaderItem item="Requested Amount" />
            <TableHeaderItem item="Approved Amount" />
            <TableHeaderItem item="Date" />
            <TableHeaderItem item="Time" />
            <TableHeaderItem item="Status" />
            <TableHeaderItem item={canView ? "Action" : ""} />
          </tr>
        </thead>
        <tbody>
          {preauth.topUps.map((topUp) => (
            <tr key={topUp.id}>
              <TableDataItem item={`KES: ${topUp.requestedAmount}`} />
              <TableDataItem item={`KES: ${topUp.authorizedAmount || "-"}`} />
              <TableDataItem item={"30/10/2024"} />
              <TableDataItem item={"12:00pm"} />
              <TableDataItem>
                <Badge hasDot {...getBadgeColorAndText(topUp.status)} />
              </TableDataItem>
              <TableDataItem>
                {canView && (
                  <Button
                    onClick={() => openTopUpApprovalModal(topUp)}
                    className="rounded-lg px-4 py-2"
                    type="button"
                    disabled={!canTopUp && topUp.status === "PENDING"}
                    title={
                      !canTopUp && topUp.status === "PENDING"
                        ? "You don't have permission to top up this benefit type"
                        : "Review"
                    }
                  >
                    {canTopUp || topUp.status !== "PENDING" ? "Review" : "View"}
                  </Button>
                )}
              </TableDataItem>
            </tr>
          ))}
        </tbody>
      </table>

      <PreAuthTopUpApprovalForm
        selectedTopUp={selectedTopUp}
        onClose={handleCloseTopUpApprovalModal}
      />
    </div>
  );
}

function getBadgeColorAndText(status: PreAuthorizationTopUp["status"]): {
  text: string;
  color: BadgeColor;
} {
  switch (status) {
    case "AUTHORIZED":
      return {
        text: "Approved",
        color: "green",
      };
    case "PENDING":
      return {
        color: "yellow",
        text: "Pending",
      };
    case "REJECTED":
      return {
        color: "red",
        text: "Rejected",
      };
    default:
      return {
        color: "gray",
        text: status,
      };
  }
}
