import { usePreAuthApprovalForm } from "./pre-auth-approval-form-context";

export default function MarkedAsIncompleteReview() {
  const { lastMarkAsIncompleteLog, preauth } = usePreAuthApprovalForm();

  return (
    <div>
      <section className="col-span-2  grid grid-cols-2 items-center">
        <div className="mb-8 flex flex-col gap-1">
          <p className="text-2xl font-black text-[#030712]">
            <span className="">Ksh </span>
            <span className="">{preauth?.requestAmount || 0}</span>
          </p>
          <p className="mb-2 ml-8 text-sm text-[#61758A]">Requested Amount</p>
        </div>

        <div className="mb-8 flex flex-col gap-1">
          <p className="text-2xl font-black text-[#16A34A]">
            <span className="">Ksh </span>
            <span className="">{preauth?.authorizedAmount || 0}</span>
          </p>
          <p className="mb-2 ml-8 text-sm text-[#61758A]">Approved Amount</p>
        </div>
      </section>

      <div className="grid-col mb-8 grid grid-cols-2 gap-8">
        <div className="mb-8 flex flex-col gap-1">
          <p className="mb-2 text-sm text-[#61758A]">First Actioned By</p>
          <div className="border-b border-gray-200 pb-2">
            {lastMarkAsIncompleteLog?.actionByUser}
          </div>
        </div>
        <div className="mb-8 flex flex-col gap-1">
          <p className="mb-2 text-sm text-[#61758A]">Action</p>
          <div className="border-b border-gray-200 pb-2">Marked as incomplete</div>
        </div>
      </div>
    </div>
  );
}
