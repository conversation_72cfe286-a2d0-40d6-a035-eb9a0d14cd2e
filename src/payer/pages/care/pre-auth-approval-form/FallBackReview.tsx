import { usePreAuthApprovalForm } from "./pre-auth-approval-form-context";

export default function FallBackReview() {
  const { preauth } = usePreAuthApprovalForm();

  return (
    <div>
      <section className="grid grid-cols-2">
        <div className="mb-8 flex flex-col gap-1">
          <p className="text-2xl font-black text-[#030712]">
            <span className="">Ksh </span>
            <span className="">{preauth?.requestAmount || 0}</span>
          </p>
          <p className="mb-2 ml-8 text-sm text-[#61758A]">Requested Amount</p>
        </div>
      </section>
    </div>
  );
}
