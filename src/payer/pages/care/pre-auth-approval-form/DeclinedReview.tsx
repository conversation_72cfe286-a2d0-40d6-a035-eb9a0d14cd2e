import { usePreAuthApprovalForm } from "./pre-auth-approval-form-context";

export default function DeclinedReview() {
  const { preauth } = usePreAuthApprovalForm();

  return (
    <div>
      <section className="grid grid-cols-2">
        <div className="mb-8 flex flex-col gap-1">
          <p className="text-2xl font-black text-[#030712]">
            <span className="">Ksh </span>
            <span className="">{preauth?.requestAmount || 0}</span>
          </p>
          <p className="mb-2 ml-8 text-sm text-[#61758A]">Requested Amount</p>
        </div>
      </section>

      <section>
        <div>
          <div className="mb-8 flex flex-col gap-1">
            <p className="mb-2 text-sm text-[#61758A]">Declined By</p>
            <div className="border-b border-gray-200 pb-2">{preauth?.rejectBy || "N/A"}</div>
          </div>
        </div>

        <div>
          <div className="mb-8 flex flex-col gap-1">
            <p className="mb-2 text-sm text-[#61758A]">Reason</p>
            <div className="border-b border-gray-200 pb-2">{preauth?.rejectNotes || "N/A"}</div>
          </div>
        </div>
      </section>
    </div>
  );
}
