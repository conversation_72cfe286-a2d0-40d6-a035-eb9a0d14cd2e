import { useEffect } from "react";
import FieldWrapper from "../../../rhf-components/FieldWrapper";
import Input from "../../../rhf-components/Input";
import RadioGroup from "../../../rhf-components/RadioGroup";
import TextArea from "../../../rhf-components/TextArea";
import { usePreAuthApprovalForm } from "./pre-auth-approval-form-context";
import { ApprovalStatus, approvalStatusLabels } from "./pre-auth-approval-form.misc";
import { useFormContext } from "react-hook-form";
import { usePreAuthorizationPermissions } from "../../../hooks/care/usePreAuthorizationPermissions";

export default function PendingTopUpReview() {
  const {
    preauth,
    lastMarkAsIncompleteLog,
    form,
    approvalStatusOptions,
    shouldShowTopUpApprovalFields,
    pendingTopUp,
    topUpRequestLog,
  } = usePreAuthApprovalForm();

  const { hasTopUpPermissionForRequestType, hasViewPermissionForRequestType } =
    usePreAuthorizationPermissions();

  const { clearErrors, setValue } = useFormContext();

  const requestType = preauth.requestType as string;
  const hasViewPermission = hasViewPermissionForRequestType(requestType);
  const canTopUp = hasTopUpPermissionForRequestType(requestType);

  // reset approval if user lacks permission to avoid form submission
  useEffect(() => {
    if (form.approval === ApprovalStatus.APPROVE && !canTopUp) {
      setValue("approval", undefined);
      clearErrors("approval");
    }
  }, [form.approval, canTopUp, setValue, clearErrors]);

  return (
    <div>
      <section className="mb-6 grid grid-cols-2 gap-4">
        <p>
          <span className="text-sm text-[#4B5563]">Initial Requested Amount : </span>
          <span className="font-medium text-[#030712]">Ksh {preauth?.requestAmount}</span>
        </p>

        <p>
          <span className="text-sm text-[#4B5563]">Initial Approved Amount : </span>
          <span className="font-medium text-success">Ksh {preauth?.authorizedAmount}</span>
        </p>

        <p>
          <span className="text-sm text-[#4B5563]">Total Ambiguous Amount : </span>
          <span className="font-medium text-[#030712]">
            Ksh {(preauth?.authorizedAmount as number) + (pendingTopUp?.requestedAmount as number)}
          </span>
        </p>
      </section>

      <section className="mb-4 mt-6 grid grid-cols-2">
        <div className=" flex flex-col gap-1">
          <p className="text-2xl font-black text-[#030712]">
            <span className="">Ksh </span>
            <span className="">{pendingTopUp?.requestedAmount || 0}</span>
          </p>
          <p className="mb-2 ml-8 text-sm text-[#61758A]">Top Up Amount</p>
        </div>
      </section>

      <p>
        <span className="text-sm text-[#4B5563]">Top Up Requested Reason : </span>
        <span className="font-medium text-[#374151]">{topUpRequestLog?.reason || "N/A"}</span>
      </p>

      {shouldShowTopUpApprovalFields ? (
        <>
          <fieldset className="mb-4 mt-6 grid gap-4 rounded border border-gray-300 px-4 py-1 text-sm lg:grid-cols-2">
            <legend className="px-2 text-xs text-gray-400">Approve/Decline</legend>

            <FieldWrapper label="Decision" name="approval" className="mb-2 text-black" required>
              <RadioGroup
                options={approvalStatusOptions.map((status) => ({
                  label: approvalStatusLabels[status] || status,
                  value: status,
                  disabled: status === ApprovalStatus.APPROVE && !canTopUp,
                  title:
                    status === ApprovalStatus.APPROVE && !canTopUp
                      ? "You don't have permission to top up this benefit type"
                      : undefined,
                }))}
                orientation="vertical"
                solid
              />
            </FieldWrapper>

            <FieldWrapper
              label="Reason"
              name="reason"
              className="mb-2"
              required={
                form.amount < (preauth?.requestAmount || 0) ||
                form.approval === ApprovalStatus.DECLINE
              }
            >
              <TextArea
                className="resize-none placeholder:text-sm"
                placeholder="Please provide any additional information or notes here...."
                disabled={form.approval === ApprovalStatus.APPROVE && !canTopUp}
              />
            </FieldWrapper>
          </fieldset>

          {/* Show preauth/admission request approval terms */}
          {form.approval === ApprovalStatus.APPROVE && (
            <>
              <fieldset className="mb-4 grid gap-4 rounded border border-gray-300 px-4 py-2 lg:grid-cols-3">
                <legend className="px-2 text-sm text-gray-400">Terms</legend>

                <FieldWrapper
                  label="Amount"
                  name="topUpAmount"
                  className="mb-2"
                  required={form.approval === ApprovalStatus.APPROVE}
                >
                  <Input
                    type="number"
                    disabled={!canTopUp || form.approval !== ApprovalStatus.APPROVE}
                    options={{
                      min: {
                        value: 1,
                        message: "Amount must be greater than 0",
                      },
                      max: {
                        value: pendingTopUp?.requestedAmount as number,
                        message: `Amount must be less than ${pendingTopUp?.requestedAmount}`,
                      },
                    }}
                    step="0.01"
                  />
                </FieldWrapper>
              </fieldset>
            </>
          )}

          {form.approval === ApprovalStatus.INCOMPLETE && canTopUp && (
            <fieldset className="mb-4 grid gap-4 rounded border border-gray-300 px-4 py-1 text-sm lg:grid-cols-2">
              <legend className="px-2 text-xs text-gray-400">Terms</legend>
              <FieldWrapper label="Reason" name="reason" className="mb-2 mt-4" required>
                <TextArea
                  rows={4}
                  className="resize-none placeholder:text-sm"
                  placeholder="Please provide a reason for marking this pre-authorization as incomplete..."
                  disabled={!canTopUp}
                />
              </FieldWrapper>
            </fieldset>
          )}
        </>
      ) : (
        <section className="mt-6 grid grid-cols-2 gap-6">
          {lastMarkAsIncompleteLog && (
            <>
              <div className="mb-2 flex flex-col gap-1">
                <p className="mb-2 text-sm text-[#61758A]">Actioned By</p>
                <div className="border-b border-gray-200 pb-2">
                  {lastMarkAsIncompleteLog?.actionByUser || "N/A"}
                </div>
              </div>

              <div className="mb-2 flex flex-col gap-1">
                <p className="mb-2 text-sm text-[#61758A]">Action</p>
                <div className="border-b border-gray-200 pb-2">Marked as incomplete</div>
              </div>
            </>
          )}

          <div className="mb-2 flex flex-col gap-1">
            <p className="mb-2 text-sm text-[#61758A]">Actioned By</p>
            <div className="border-b border-gray-200 pb-2">{preauth?.authorizer || "NA"}</div>
          </div>

          <div className="mb-2 flex flex-col gap-1">
            <p className="mb-2 text-sm text-[#61758A]">Action</p>
            <div className="border-b border-gray-200 pb-2">Approved</div>
          </div>
        </section>
      )}
    </div>
  );
}
