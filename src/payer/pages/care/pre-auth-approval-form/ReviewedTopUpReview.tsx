import { usePreAuthApprovalForm } from "./pre-auth-approval-form-context";

export default function ReviewedTopUpReview() {
  const {
    preauth,
    lastMarkAsIncompleteLog,
    latestTopUp,
    topUpAuthorizedLog,
    topUpRejectedLog,
    topUpRequestLog,
  } = usePreAuthApprovalForm();

  return (
    <div>
      <section className="mb-6 grid grid-cols-2 gap-4">
        <p>
          <span className="text-sm text-[#4B5563]">Initial Requested Amount : </span>
          <span className="font-medium text-[#030712]">Ksh {preauth?.requestAmount}</span>
        </p>

        <p>
          <span className="text-sm text-[#4B5563]">Initial Approved Amount : </span>
          <span className="font-medium text-success">
            Ksh {(preauth?.authorizedAmount as number) - (latestTopUp?.authorizedAmount as number)}
          </span>
        </p>

        <p>
          <span className="text-sm text-[#4B5563]">Top Up Requested Amount : </span>
          <span className="font-medium text-[#030712]">
            Ksh {latestTopUp?.requestedAmount as number}
          </span>
        </p>

        {latestTopUp?.authorizedAmount !== undefined && (
          <p>
            <span className="text-sm text-[#4B5563]">Top Up Approved Amount : </span>
            <span className="font-medium text-success">Ksh {latestTopUp?.authorizedAmount}</span>
          </p>
        )}
      </section>
      <p>
        <span className="text-sm text-[#4B5563]">Top Up Requested Reason : </span>
        <span className="font-medium text-[#374151]">{topUpRequestLog?.reason || "N/A"}</span>
      </p>

      <section className="mt-6 grid grid-cols-2">
        <div className="mb-8 flex flex-col gap-1">
          <p className="text-2xl font-black text-[#030712]">
            <span className="">Ksh </span>
            <span className="">
              {(latestTopUp?.requestedAmount as number) + (preauth.requestAmount as number)}
            </span>
          </p>
          <p className="mb-2 ml-8 text-sm text-[#61758A]">Total Request Amount</p>
        </div>

        <div className="mb-8 flex flex-col gap-1">
          <p className="text-2xl font-black text-success">
            <span className="">Ksh </span>
            <span className="">{preauth.authorizedAmount as number}</span>
          </p>
          <p className="mb-2 ml-8 text-sm text-[#61758A]">Total Approved Amount</p>
        </div>
      </section>

      <section className="mt-6 grid grid-cols-2 gap-6">
        {lastMarkAsIncompleteLog && (
          <>
            <div className="mb-2 flex flex-col gap-1">
              <p className="mb-2 text-sm text-[#61758A]">Actioned By</p>
              <div className="border-b border-gray-200 pb-2">
                {lastMarkAsIncompleteLog?.actionByUser || "N/A"}
              </div>
            </div>

            <div className="mb-2 flex flex-col gap-1">
              <p className="mb-2 text-sm text-[#61758A]">Action</p>
              <div className="border-b border-gray-200 pb-2">Marked as incomplete</div>
            </div>
          </>
        )}

        <div className="mb-2 flex flex-col gap-1">
          <p className="mb-2 text-sm text-[#61758A]">Actioned By</p>
          <div className="border-b border-gray-200 pb-2">{preauth?.authorizer || "NA"}</div>
        </div>

        <div className="mb-2 flex flex-col gap-1">
          <p className="mb-2 text-sm text-[#61758A]">Action</p>
          <div className="border-b border-gray-200 pb-2">Approved</div>
        </div>

        {topUpAuthorizedLog && (
          <>
            <div className="mb-2 flex flex-col gap-1">
              <p className="mb-2 text-sm text-[#61758A]">Actioned By</p>
              <div className="border-b border-gray-200 pb-2">
                {topUpAuthorizedLog.actionByUser || "NA"}
              </div>
            </div>

            <div className="mb-2 flex flex-col gap-1">
              <p className="mb-2 text-sm text-[#61758A]">Action</p>
              <div className="border-b border-gray-200 pb-2">Top Up Approved</div>
            </div>

            <div className="mb-2 flex flex-col gap-1">
              <p className="mb-2 text-sm text-[#61758A]">Top up Approval reason</p>
              <div className="border-b border-gray-200 pb-2">
                {topUpAuthorizedLog?.reason || "NA"}
              </div>
            </div>
          </>
        )}
        {topUpRejectedLog && (
          <>
            <div className="mb-2 flex flex-col gap-1">
              <p className="mb-2 text-sm text-[#61758A]">Actioned By</p>
              <div className="border-b border-gray-200 pb-2">
                {topUpRejectedLog.actionByUser || "NA"}
              </div>
            </div>

            <div className="mb-2 flex flex-col gap-1">
              <p className="mb-2 text-sm text-[#61758A]">Action</p>
              <div className="border-b border-gray-200 pb-2">Top Up Declined</div>
            </div>

            <div className="mb-2 flex flex-col gap-1">
              <p className="mb-2 text-sm text-[#61758A]">Top up Decline reason</p>
              <div className="border-b border-gray-200 pb-2">
                {topUpRejectedLog?.reason || "NA"}
              </div>
            </div>
          </>
        )}
      </section>
    </div>
  );
}
