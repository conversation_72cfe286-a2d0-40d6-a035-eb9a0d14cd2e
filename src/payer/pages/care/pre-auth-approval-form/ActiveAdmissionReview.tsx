import { DocumentTextIcon } from "@heroicons/react/24/outline";
import { Fragment } from "react";
import { usePreAuthApprovalForm } from "./pre-auth-approval-form-context";
export default function ActiveAdmissionReview() {
  const { preauth, lastMarkAsIncompleteLog } = usePreAuthApprovalForm();

  return (
    <div>
      <section className="grid grid-cols-2">
        <div className="mb-8 flex flex-col gap-1">
          <p className="text-2xl font-black text-[#030712]">
            <span className="">Ksh </span>
            <span className="">{preauth?.initialRequestAmount || 0}</span>
          </p>
          <p className="mb-2 ml-8 text-sm text-[#61758A]">Requested Amount</p>
        </div>

        <div className="mb-8 flex flex-col gap-1">
          <p className="text-2xl font-black text-[#CA8A04]">
            <span className="">Ksh </span>
            <span className="">{preauth?.authorizedAmount || 0}</span>
          </p>
          <p className="mb-2 ml-8 text-sm text-[#61758A]">Approved Amount</p>
        </div>
      </section>

      <section>
        {lastMarkAsIncompleteLog?.actionByUser && (
          <div className="grid grid-cols-2 gap-8">
            <div className="mb-8 flex flex-col gap-1">
              <p className="mb-2 text-sm text-[#61758A]">Actioned By</p>
              <div className="border-b border-gray-200 pb-2">
                {lastMarkAsIncompleteLog?.actionByUser || "N/A"}
              </div>
            </div>
            <div className="mb-8 flex flex-col gap-1">
              <p className="mb-2 text-sm text-[#61758A]">Action</p>
              <div className="border-b border-gray-200 pb-2">Marked as incomplete</div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-2 gap-8">
          <div className="mb-8 flex flex-col gap-1">
            <p className="mb-2 text-sm text-[#61758A]">Actioned By</p>
            <div className="border-b border-gray-200 pb-2">{preauth?.authorizer || "NA"}</div>
          </div>
          <div className="mb-8 flex flex-col gap-1">
            <p className="mb-2 text-sm text-[#61758A]">Action</p>
            <div className="border-b border-gray-200 pb-2">Approved</div>
          </div>
        </div>
      </section>

      {preauth.authorizationNotes && (
        <section>
          <p className="mb-4 flex gap-2 font-medium">
            <span>Notes</span>
          </p>
          <p className="text-sm text-[#4B5563]">{preauth.authorizationNotes}</p>
        </section>
      )}

      {preauth?.validForDays && (
        <section className="mt-4">
          <p className="mb-4 flex gap-2 font-medium">
            <DocumentTextIcon className="w-6" /> <span>Review</span>
          </p>
          <div className="flex flex-col gap-6">
            {preauth?.validForDays && (
              <p className="text-sm">
                <span className="text-[#6B7280]">Number of days valid: </span>
                <span className="text-[#374151]">{preauth?.validForDays}</span>
              </p>
            )}
          </div>
        </section>
      )}

      <section className="mt-12">
        <p className="mb-4 flex gap-2 font-medium">
          <DocumentTextIcon className="w-6" /> <span>Terms</span>
        </p>
        <div className="grid grid-cols-2 gap-4">
          <p>Description</p>
          <p>Cost</p>
          {preauth?.guidelines?.map((guideline) => (
            <Fragment key={guideline.id}>
              <p className="text-sm text-[#4B5563]">
                <span className="">{guideline.description}</span>
              </p>

              <p className="text-sm text-[#4B5563]">
                <span className="">Ksh </span>
                <span className="">{guideline.cost || 0}</span>
              </p>
            </Fragment>
          ))}
        </div>
      </section>
    </div>
  );
}
