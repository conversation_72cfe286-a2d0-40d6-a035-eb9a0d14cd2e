import { SerializedError } from "@reduxjs/toolkit";
import { FetchBaseQueryError } from "@reduxjs/toolkit/query";
import { AuthorizePreauthResponse, CreateGuideline, DeclinePreAuthResponse } from "~lib/api/types";
import CsvFileIcon from "../../../components/icons/CsvFileIcon";
import JpgFileIcon from "../../../components/icons/JpgFileIcon";
import PdfFileIcon from "../../../components/icons/PdfFileIcon";
import PngFileIcon from "../../../components/icons/PngFileIcon";
import UnknownFileTypeIcon from "../../../components/icons/UnknownFileTypeIcon";

export function fileExtensionIcons(extension: string) {
  switch (extension) {
    case "csv":
    case "xls":
    case "xlsx":
      return <CsvFileIcon />;
    case "jpg":
    case "jpeg":
      return <JpgFileIcon />;
    case "png":
      return <PngFileIcon />;
    case "pdf":
    case "doc":
    case "docx":
      return <PdfFileIcon />;
    default:
      return <UnknownFileTypeIcon />;
  }
}
export interface Inputs {
  amount: number;
  notes: string;
  numberOfDaysValid: number;
  allowedDaysOfAdmission: number;
  netOfNHIFLimit: number;
  doctorFees: number;
  /* ----- */
  approval: ApprovalStatus;
  benefitId: number;
  limit: number;
  guidelines?: CreateGuideline[];
  reason: string;
  topUpAmount?: number;
}
export enum ApprovalStatus {
  APPROVE = "APPROVE",
  DECLINE = "DECLINE",
  INCOMPLETE = "INCOMPLETE",
}
export const approvalStatusLabels: Record<ApprovalStatus, string> = {
  APPROVE: "Approve",
  DECLINE: "Decline",
  INCOMPLETE: "Mark Incomplete",
};
export const approvalStatusDescription: Record<ApprovalStatus, string> = {
  APPROVE: "approved",
  DECLINE: "declined",
  INCOMPLETE: "marked as incomplete",
};
export const EMPTY_LINE = "N/A";

export const ADMISSION_REQUEST_TYPE = "Admission";
export type Id = {
  id: number;
};

export type Response =
  | { data: AuthorizePreauthResponse | DeclinePreAuthResponse }
  | { error: FetchBaseQueryError | SerializedError };
