import { DocumentTextIcon } from "@heroicons/react/24/outline";
import { Fragment } from "react";
import { PreAuthStatus } from "~lib/api/types";
import { usePreAuthApprovalForm } from "./pre-auth-approval-form-context";

export default function ApprovedReview() {
  const { preauth, lastMarkAsIncompleteLog } = usePreAuthApprovalForm();

  const hasReviewsToDisplay = Boolean(
    preauth?.doctorFees ||
      preauth?.netOfNHIFLimit ||
      preauth?.allowedDaysOfAdmission ||
      preauth?.validForDays,
  );

  return (
    <div>
      <section className="grid grid-cols-2">
        <div className="mb-8 flex flex-col gap-1">
          <p className="text-2xl font-black text-[#030712]">
            <span className="">Ksh </span>
            <span className="">{preauth?.requestAmount || 0}</span>
          </p>
          <p className="mb-2 ml-8 text-sm text-[#61758A]">Requested Amount</p>
        </div>

        <div className="mb-8 flex flex-col gap-1">
          <p className="text-2xl font-black text-[#16A34A]">
            <span className="">Ksh </span>
            <span className="">{preauth?.authorizedAmount || 0}</span>
          </p>
          <p className="mb-2 ml-8 text-sm text-[#61758A]">Approved Amount</p>
        </div>
      </section>

      <section className="grid grid-cols-2 gap-6">
        <div className="mb-2 flex flex-col gap-1">
          <p className="mb-2 text-sm text-[#61758A]">Actioned By</p>
          <div className="border-b border-gray-200 pb-2">
            {lastMarkAsIncompleteLog?.actionByUser || "N/A"}
          </div>
        </div>

        <div className="mb-2 flex flex-col gap-1">
          <p className="mb-2 text-sm text-[#61758A]">Action</p>
          <div className="border-b border-gray-200 pb-2">Marked as incomplete</div>
        </div>

        <div className="mb-2 flex flex-col gap-1">
          <p className="mb-2 text-sm text-[#61758A]">Actioned By</p>
          <div className="border-b border-gray-200 pb-2">{preauth?.authorizer || "NA"}</div>
        </div>

        <div className="mb-2 flex flex-col gap-1">
          <p className="mb-2 text-sm text-[#61758A]">Action</p>
          <div className="border-b border-gray-200 pb-2">Approved</div>
        </div>
      </section>

      {hasReviewsToDisplay && (
        <section className="mt-8">
          <p className="mb-4 flex gap-2 font-medium">
            <DocumentTextIcon className="w-6" /> <span>Review</span>
          </p>
          <div className="flex flex-col gap-6">
            {(preauth?.status === PreAuthStatus.AUTHORIZED ||
              preauth?.status === PreAuthStatus.CLAIMED ||
              preauth?.status === PreAuthStatus.ACTIVE) &&
              preauth?.requestType === "Admission" && (
                <>
                  {preauth?.doctorFees && (
                    <p className="text-sm">
                      <span className="text-[#6B7280]">Net of NHIF Limit: </span>
                      <span className="text-[#374151]">{preauth?.doctorFees}</span>
                    </p>
                  )}

                  {preauth?.validForDays && (
                    <p className="text-sm">
                      <span className="text-[#6B7280]">Number of days valid: </span>
                      <span className="text-[#374151]">{preauth?.validForDays}</span>
                    </p>
                  )}

                  {preauth?.allowedDaysOfAdmission && (
                    <p className="text-sm">
                      <span className="text-[#6B7280]">Allowed days of admission: </span>
                      <span className="text-[#374151]">{preauth?.allowedDaysOfAdmission}</span>
                    </p>
                  )}

                  {preauth?.netOfNHIFLimit && (
                    <p className="text-sm">
                      <span className="text-[#6B7280]">Net of NHIF Limit: </span>
                      <span className="text-[#374151]">{preauth?.netOfNHIFLimit}</span>
                    </p>
                  )}
                </>
              )}

            {(preauth?.status === PreAuthStatus.AUTHORIZED ||
              preauth?.status === PreAuthStatus.CLAIMED) &&
            preauth?.authorizationNotes ? (
              <p className="text-sm">
                <span className="text-[#6B7280]">Notes: </span>
                <span className="text-[#374151]">{preauth?.authorizationNotes}</span>
              </p>
            ) : preauth?.status === PreAuthStatus.CANCELLED && preauth?.rejectNotes ? (
              <p className="text-sm">
                <span className="text-[#6B7280]">Cancellation reason: </span>
                <span className="text-[#374151]">{preauth?.rejectNotes}</span>
              </p>
            ) : preauth?.status === PreAuthStatus.DECLINED && preauth?.rejectNotes ? (
              <p className="text-sm">
                <span className="text-[#6B7280]">Decline reason: </span>
                <span className="text-[#374151]">{preauth?.rejectNotes}</span>
              </p>
            ) : preauth?.markAsIncomplete ? (
              <p className="text-sm">
                <span className="text-[#6B7280]">Marked incomplete reason: </span>
                <span className="text-[#374151]">{preauth?.markAsIncompleteReason}</span>
              </p>
            ) : (
              <></>
            )}
          </div>
        </section>
      )}

      {preauth?.guidelines && preauth?.guidelines?.length > 0 && (
        <section className="mt-8">
          <p className="mb-4 flex gap-2 font-medium">
            <DocumentTextIcon className="w-6" /> <span>Terms</span>
          </p>
          <div className="grid grid-cols-2 gap-4">
            <p className="font-medium">Description</p>
            <p className="font-medium">Cost</p>
            {preauth?.guidelines?.map((guideline) => (
              <Fragment key={guideline.id}>
                <p className="text-sm text-[#4B5563]">
                  <span className="">{guideline.description}</span>
                </p>

                <p className="text-sm text-[#4B5563]">
                  <span className="">Ksh </span>
                  <span className="">{guideline.cost || 0}</span>
                </p>
              </Fragment>
            ))}
          </div>
        </section>
      )}
    </div>
  );
}
