import { Fragment } from "react";
import { PreAuthType } from "~lib/api/types";
import { Empty, Select } from "~lib/components";
import ErrorMessage from "~lib/components/Error";
import { formatMoney, truncate } from "~lib/utils";
import LoadingAnimation from "../../../components/animations/LoadingAnimation/LoadingAnimation";
import FieldWrapper from "../../../rhf-components/FieldWrapper";
import Input from "../../../rhf-components/Input";
import RadioGroup from "../../../rhf-components/RadioGroup";
import TextArea from "../../../rhf-components/TextArea";
import { usePreAuthApprovalForm } from "./pre-auth-approval-form-context";
import { ApprovalStatus, approvalStatusLabels } from "./pre-auth-approval-form.misc";

export default function NotificationReview() {
  const {
    preauth,
    approvalStatusOptions,
    form,
    benefitsError,
    isBenefitsLoading,
    filteredBenefits,
    activeBenefit,
    guidelinesError,
    guidelinesItemsError,
    fields,
    remove,
    append,
    undertakingTotalCost,
    shouldShowUpdateDisplay,
    lastMarkAsIncompleteLog,
  } = usePreAuthApprovalForm();

  return (
    <div>
      <section className="grid grid-cols-2">
        <div className="mb-8 flex flex-col gap-1">
          <p className="text-2xl font-black text-[#030712]">
            <span className="">Ksh </span>
            <span className="">{preauth?.requestAmount || preauth?.initialRequestAmount || 0}</span>
          </p>
          <p className="mb-2 ml-8 text-sm text-[#61758A]">Requested Amount</p>
        </div>

        {shouldShowUpdateDisplay && (
          <div className="mb-8 flex flex-col gap-1">
            <p className="text-2xl font-black text-[#CA8A04]">
              <span className="">Ksh </span>
              <span className="">{preauth?.authorizedAmount || 0}</span>
            </p>
            <p className="mb-2 ml-8 text-sm text-[#61758A]">Approved Amount</p>
          </div>
        )}
      </section>

      {shouldShowUpdateDisplay ? (
        <div className="grid-col mb-8 grid grid-cols-2 gap-8">
          <div className="mb-8 flex flex-col gap-1">
            <p className="mb-2 text-sm text-[#61758A]">First Actioned By</p>
            <div className="border-b border-gray-200 pb-2">
              {lastMarkAsIncompleteLog?.actionByUser}
            </div>
          </div>
          <div className="mb-8 flex flex-col gap-1">
            <p className="mb-2 text-sm text-[#61758A]">Action</p>
            <div className="border-b border-gray-200 pb-2">Marked as incomplete</div>
          </div>
        </div>
      ) : (
        <fieldset className="mb-4 grid gap-4 rounded border border-gray-300 px-4 py-1 text-sm lg:grid-cols-2">
          <legend className="px-2 text-xs text-gray-400">Approve/Decline</legend>

          <FieldWrapper label="Decision" name="approval" className="mb-2 text-black" required>
            <RadioGroup
              options={approvalStatusOptions.map((status) => ({
                label: approvalStatusLabels[status] || status,
                value: status,
              }))}
              orientation="vertical"
              solid
            />
          </FieldWrapper>

          <FieldWrapper
            label="Notes"
            name="notes"
            className="mb-2"
            required={
              (preauth?.requestAmount && form.amount < preauth?.requestAmount) ||
              form.approval === ApprovalStatus.DECLINE
            }
          >
            <TextArea
              className="resize-none placeholder:text-sm"
              placeholder="Please provide any additional information or notes here...."
            />
          </FieldWrapper>
        </fieldset>
      )}

      {form.approval === ApprovalStatus.APPROVE && !preauth?.draft && (
        <>
          {isBenefitsLoading ? (
            <div className="flex items-center justify-center py-8">
              <LoadingAnimation size={24} />
            </div>
          ) : benefitsError ? (
            <div className="flex flex-col items-center">
              <ErrorMessage
                title="Error loading member benefits"
                message="Refresh the page to retry"
              />
            </div>
          ) : !filteredBenefits?.length ? (
            <div className="flex items-center justify-center p-8">
              <div className="flex flex-col items-center">
                <Empty message="No valid benefits found." />
              </div>
            </div>
          ) : (
            <fieldset className="mt-8 grid grid-cols-3 gap-4 rounded border border-gray-300 px-4 py-2">
              <legend className="px-1 text-sm text-gray-400">Terms</legend>

              <FieldWrapper
                label="Benefit"
                name="benefitId"
                className="mb-0 text-sm font-medium"
                descriptionClassName="text-xs"
                description="Assign a benefit"
                required
              >
                <Select
                  options={filteredBenefits.map((benefit) => ({
                    label: `${benefit.benefitName} - Ksh ${benefit.balance}`,
                    value: benefit.benefitId,
                  }))}
                  className="text-sm"
                  styles={{
                    menuList: (provided) => ({
                      ...provided,
                      // height: "144px",
                    }),
                  }}
                  placeholder="Search or select..."
                />
              </FieldWrapper>

              {preauth?.preauthType != PreAuthType.SCHEDULED_ADMISSION && (
                <FieldWrapper
                  label="Number of days valid"
                  name="numberOfDaysValid"
                  className="text-sm"
                  descriptionClassName="text-xs"
                  description="Number of days within which the discharge request must be submitted"
                >
                  <Input
                    type="number"
                    options={{
                      min: {
                        value: 1,
                        message: "Minimum validity period is one day",
                      },
                    }}
                  />
                </FieldWrapper>
              )}

              {activeBenefit && (
                <div className="flex flex-col justify-center gap-4">
                  <p>
                    <span className="text-gray-light">Benefit: </span>
                    <span className="text-gray-dark">
                      {truncate(activeBenefit.benefitName, 20)}
                    </span>
                  </p>
                  <p>
                    <span className="text-gray-light">Balance: </span>
                    <span className="text-gray-dark">{formatMoney(activeBenefit.balance)}</span>
                  </p>
                </div>
              )}
            </fieldset>
          )}

          <fieldset className="mb-4 mt-8 gap-4 rounded border border-gray-300 px-4 py-2">
            <legend className="px-2 text-sm text-gray-400">Initial Undertaking</legend>

            {(guidelinesError || guidelinesItemsError) && (
              <div className="mb-2 text-xs text-red-500">
                {guidelinesError || guidelinesItemsError}
              </div>
            )}

            <div className="grid grid-cols-4 gap-x-2">
              <div className="col-span-2 mb-2 text-sm text-gray-400">Description</div>
              <div className="mb-2 text-sm text-gray-400">Cost</div>
              <div className="mb-2 text-sm text-gray-400">Action</div>

              {fields.map((field, index) => (
                <Fragment key={field.id}>
                  <FieldWrapper
                    name={`guidelines.${index}.description`}
                    className="col-span-2"
                    required
                  >
                    <Input
                      options={{
                        minLength: {
                          value: 1,
                          message: "Description must be longer than one character",
                        },
                      }}
                    />
                  </FieldWrapper>

                  <FieldWrapper name={`guidelines.${index}.cost`} required>
                    <Input
                      key={field.id}
                      type="number"
                      options={{
                        min: {
                          value: 1,
                          message: "Amount must be greater than 0",
                        },
                      }}
                    />
                  </FieldWrapper>

                  <div className="items-center pt-2">
                    <button
                      type="button"
                      onClick={() => remove(index)}
                      className="text-red-500 hover:text-red-600"
                      title="Remove"
                    >
                      {/* prettier-ignore */}
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                                           <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                                         </svg>
                    </button>
                  </div>
                </Fragment>
              ))}

              <div className="col-span-full mb-4 flex items-center justify-between">
                <button
                  type="button"
                  onClick={() => append({ description: "", cost: 0 })}
                  className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
                >
                  Add
                </button>

                <div className="flex flex-col gap-1">
                  <label>Total</label>

                  <span className="text-sm text-gray-400">{formatMoney(undertakingTotalCost)}</span>
                </div>
              </div>
            </div>
          </fieldset>
        </>
      )}

      {form.approval === ApprovalStatus.INCOMPLETE && (
        <fieldset className="mb-4 grid gap-4 rounded border border-gray-300 px-4 py-1 text-sm lg:grid-cols-2">
          <legend className="px-2 text-xs text-gray-400">Terms</legend>
          <FieldWrapper label="Reason" name="reason" className="mb-2 mt-4" required>
            <TextArea
              className="resize-none placeholder:text-sm"
              placeholder="Please provide a reason for marking this pre-authorization as incomplete..."
            />
          </FieldWrapper>
        </fieldset>
      )}
    </div>
  );
}
