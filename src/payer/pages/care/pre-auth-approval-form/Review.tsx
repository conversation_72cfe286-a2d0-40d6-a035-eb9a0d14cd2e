import { PreAuthStatus } from "~lib/api/types";
import ActiveAdmissionReview from "./ActiveAdmissionReview";
import ApprovedReview from "./ApprovedReview";
import DeclinedReview from "./DeclinedReview";
import FallBackReview from "./FallBackReview";
import MarkedAsIncompleteReview from "./MarkedAsIncompleteReview";
import NotificationReview from "./NotificationReview";
import PendingReview from "./PendingReview";
import PendingTopUpReview from "./PendingTopUpReview";
import { usePreAuthApprovalForm } from "./pre-auth-approval-form-context";
import ReviewedTopUpReview from "./ReviewedTopUpReview";

export default function Review() {
  const { preauth, pendingTopUp, latestTopUp } = usePreAuthApprovalForm();

  return (
    <div>
      {preauth?.markAsIncomplete ? (
        <MarkedAsIncompleteReview />
      ) : pendingTopUp ? (
        <PendingTopUpReview />
      ) : latestTopUp?.status === "AUTHORIZED" || latestTopUp?.status === "REJECTED" ? (
        <ReviewedTopUpReview />
      ) : preauth?.status === PreAuthStatus.PENDING ? (
        <PendingReview />
      ) : preauth?.status === PreAuthStatus.DRAFT ? ( // DRAFT === NOTIFICATION status
        <NotificationReview />
      ) : preauth?.status === PreAuthStatus.AUTHORIZED ||
        preauth?.status === PreAuthStatus.CLAIMED ? (
        <ApprovedReview />
      ) : preauth?.status === PreAuthStatus.DECLINED ? (
        <DeclinedReview />
      ) : preauth?.status === PreAuthStatus.ACTIVE ? (
        <ActiveAdmissionReview />
      ) : (
        <FallBackReview />
      )}
    </div>
  );
}
