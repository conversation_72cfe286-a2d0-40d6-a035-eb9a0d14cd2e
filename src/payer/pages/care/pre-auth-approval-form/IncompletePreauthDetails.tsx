import { XMarkIcon } from "@heroicons/react/24/outline";
import { PreAuth } from "~lib/api/types";
import Text from "../../../components/ui/typography/Text";
import Button from "../../../components/ui/Button";

type PreAuthDetailsProps = {
  onContinueClick: () => void;
  onCancelClick: () => void;
  preAuth: PreAuth | undefined;
};

export default function IncompletePreAuthDetails({
  onContinueClick,
  preAuth,
  onCancelClick,
}: PreAuthDetailsProps) {
  return (
    <section>
      <div className="flex items-center justify-between border-b-2 px-6 pb-3 pt-4">
        <Text variant="heading">Incomplete Pre-authorization Details</Text>

        <button onClick={onCancelClick}>
          <XMarkIcon className="w-5" strokeWidth={2} />
        </button>
      </div>

      <div className=" mx-8 my-6 flex flex-col gap-2">
        <p className="font-semibold text-[#374151]">Reason for marking as incomplete</p>
        <p className="text-sm font-medium text-[#6B7280]">
          {preAuth?.markAsIncompleteReason || "No reason provided"}
        </p>
      </div>

      <div className=" mx-8 my-6 flex flex-col gap-2">
        <p className="font-semibold text-[#374151]">Notes</p>
        <p className="text-sm font-medium text-[#6B7280]">
          {preAuth?.notes || "No notes provided"}
        </p>
      </div>

      <div className="mx-12 mb-8 mt-12 flex justify-end gap-8">
        <Button variant="outlined" className="border-none font-medium" onClick={onCancelClick}>
          Cancel
        </Button>
        <Button onClick={onContinueClick}>Continue</Button>
      </div>
    </section>
  );
}
