import TableDataItem from "../../../../components/ui/table/TableDataItem";
import TableHeaderItem from "../../../../components/ui/table/TableHeaderItem";
import Text from "../../../../components/ui/typography/Text";
import { useTopUpApproval } from "./TopUpApprovalContext";

export default function LineItems() {
  const { topUp } = useTopUpApproval();

  return (
    <>
      <Text variant="subheading" className="text-2xl">
        Line Items
      </Text>

      <table className="mt-6 w-full">
        <thead>
          <tr className="border-b">
            <TableHeaderItem item="Item" />
            <TableHeaderItem item="Quantity" />
            <TableHeaderItem item="Cost" />
          </tr>
        </thead>
        <tbody>
          {topUp?.lineItems.map((item) => (
            <tr key={item.id}>
              <TableDataItem item={`${item.name}`} />
              <TableDataItem item={`${item.quantity || "-"}`} />
              <TableDataItem item={`KES: ${item.cost}`} />
            </tr>
          ))}
        </tbody>
      </table>
    </>
  );
}
