import { ChangeEvent } from "react";
import { useTopUpApproval } from "./TopUpApprovalContext";

export default function ActionSection() {
  const { topUp, approvalAmount, isApproved, reason, setApprovalAmount, setIsApproved, setReason } =
    useTopUpApproval();

  const requestedAmount = topUp?.requestedAmount;
  const isReasonRequired = Number(requestedAmount) > Number(approvalAmount);

  function handleApprove(e: ChangeEvent<HTMLInputElement>) {
    setIsApproved(e.target.checked);
  }

  function handleDecline(e: ChangeEvent<HTMLInputElement>) {
    setIsApproved(!e.target.checked);
  }

  function handleApprovalAmountChange(e: ChangeEvent<HTMLInputElement>) {
    setApprovalAmount(e.target.value);
  }
  return (
    <>
      <fieldset className="mb-10 flex min-h-[200px] rounded-lg border border-gray-300 px-6 py-4">
        <legend className="text-xs  text-gray-400">Approve/Decline</legend>

        <section className="grow">
          <p className="mb-4 text-gray-700">
            <span>Decision </span>
            <span className="text-red-500">*</span>
          </p>

          <div className="mb-4 flex items-center gap-2">
            <input onChange={handleApprove} type="radio" name="approval" id="approve" />
            <label htmlFor="approve">Approve</label>
          </div>

          <div className="flex items-center gap-2">
            <input onChange={handleDecline} type="radio" name="approval" id="decline" />
            <label htmlFor="decline">Decline</label>
          </div>
        </section>

        <section className="flex grow flex-col">
          <label htmlFor="reason" className="mb-1 text-gray-700">
            <span>Reason </span>
            {isReasonRequired && <span className="text-red-500">*</span>}
          </label>
          <textarea
            placeholder="Please provide any additional information or notes here...."
            className="h-full resize-none rounded-lg border px-3 py-2"
            id="reason"
            rows={4}
            value={reason}
            onChange={(e) => setReason(e.target.value)}
          />
        </section>
      </fieldset>

      {isApproved && (
        <fieldset className="flex h-[150px] rounded-lg border border-gray-300 px-6 py-4">
          <legend className="text-xs  text-gray-400">Terms</legend>

          <section className="grow">
            <p className="mb-4 text-gray-700">
              <span>Amount </span>
              <span className="text-red-500">*</span>
            </p>

            <input
              type="number"
              className="rounded-lg border px-3 py-2"
              min={1}
              step={0.01}
              data-nonscientific-number-input
              data-unsigned-number-input
              value={approvalAmount}
              onChange={handleApprovalAmountChange}
            />
          </section>
        </fieldset>
      )}
    </>
  );
}
