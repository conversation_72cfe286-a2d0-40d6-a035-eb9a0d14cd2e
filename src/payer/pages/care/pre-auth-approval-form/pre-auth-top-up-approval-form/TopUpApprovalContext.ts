import { createContext, useContext } from "react";
import { PreAuthorizationTopUp } from "~lib/api/types";

type TopUpApprovalState = {
  topUp: PreAuthorizationTopUp | null;
  isApproved: boolean | undefined;
  setIsApproved: React.Dispatch<React.SetStateAction<boolean | undefined>>;
  approvalAmount: string;
  setApprovalAmount: React.Dispatch<React.SetStateAction<string>>;
  reason: string;
  setReason: React.Dispatch<React.SetStateAction<string>>;
};

export const TopUpApprovalContext = createContext<TopUpApprovalState | null>(null);

export function useTopUpApproval() {
  const context = useContext(TopUpApprovalContext);

  if (!context)
    throw new Error("useTopUpApproval can only be used in a TopUpApprovalContext provider");

  return context;
}
