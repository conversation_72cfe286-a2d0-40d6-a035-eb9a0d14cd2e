import { useGetAuditLogsQuery } from "../../../../api/claims-vetting-api/claimVettingApi";
import ActionSection from "./ActionSection";
import { useTopUpApproval } from "./TopUpApprovalContext";

export default function Review() {
  const { topUp } = useTopUpApproval();

  const { data: logsData } = useGetAuditLogsQuery(
    { preAuthTopUpId: topUp?.id as number },
    { skip: topUp?.status === "PENDING" },
  );

  const topUpLog = logsData?.data.content[0];

  const requestedAmount = topUp?.requestedAmount;
  const authorizedAmount = topUp?.authorizedAmount;

  return (
    <>
      <section className="grid grid-cols-2">
        <div className="mb-8 flex flex-col gap-1">
          <p className="text-3xl font-black text-[#030712]">
            <span className="">Ksh </span>
            <span className="">{requestedAmount}</span>
          </p>
          <p className="mb-2 ml-8 text-lg text-[#61758A]">Requested Amount</p>
        </div>

        {topUp?.status === "AUTHORIZED" && (
          <div className="mb-8 flex flex-col gap-1">
            <p className="text-3xl font-black text-success">
              <span className="">Ksh </span>
              <span className="">{authorizedAmount}</span>
            </p>
            <p className="mb-2 ml-8 text-lg text-[#61758A]">Approved Amount</p>
          </div>
        )}
      </section>

      <p className="mb-8 text-xl">
        <span className="text-gray-light">Top Up Request Reason: </span>
        <span className="text-gray-dark">Additional line Items</span>
      </p>

      {topUp?.status === "PENDING" ? (
        <ActionSection />
      ) : (
        <div className="grid grid-cols-2">
          <div className="mb-2 flex flex-col gap-1">
            <p className="mb-2 text-sm text-[#61758A]">Actioned by</p>
            <div className="border-b border-gray-200 pb-2">{topUpLog?.actionByUser || "NA"}</div>
          </div>
          <div className="mb-2 flex flex-col gap-1">
            <p className="mb-2 text-sm text-[#61758A]">Action</p>
            <div className="border-b border-gray-200 pb-2">{topUpLog?.eventName || "NA"}</div>
          </div>
        </div>
      )}
    </>
  );
}
