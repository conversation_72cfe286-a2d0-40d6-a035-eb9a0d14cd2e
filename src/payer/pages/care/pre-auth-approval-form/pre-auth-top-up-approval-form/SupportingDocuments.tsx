import { toast } from "react-toastify";
import EmptyStateIcon from "../../../../components/icons/EmptyStateIcon";
import PdfFileIcon from "../../../../components/icons/PdfFileIcon";
import EmptyState from "../../../../components/ui/EmptyState";
import { useTopUpApproval } from "./TopUpApprovalContext";

export default function SupportingDocuments() {
  const { topUp } = useTopUpApproval();

  async function handleDownloadFile(url: string, index: number) {
    const name = url.split("/").pop();

    try {
      if (!name) throw new Error("File not found");

      const response = await fetch(`https://api.lctafrica.net/api/file/download?name=${name}`);

      const res = await response.json();

      const a = document.createElement("a");

      a.href = res.data;

      a.download =
        decodeURIComponent(url || "")
          .split("/")
          .pop()
          ?.slice(27) || `File ${index + 1}`;

      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    } catch (error) {
      toast.error((error as Error)?.message || "Something went wrong");
      console.error("Error: ", error);
    }
  }
  return (
    <>
      <h3 className="mb-6 text-2xl text-[#61758A]">Supporting Documents</h3>

      {topUp?.documents.length === 0 && (
        <div className="col-span-4 mx-auto h-[50px] w-[300px]">
          <EmptyState
            message={{ title: "No supporting documents available" }}
            illustration={<EmptyStateIcon size={200} />}
          />
        </div>
      )}

      <div className="grid grid-cols-4 place-items-center gap-4">
        {topUp?.documents.map((document, i) => (
          <button
            key={document.id}
            className="flex flex-col items-center gap-2 rounded-lg bg-[#F3F4F5] p-5"
            onClick={() => handleDownloadFile(document.fileUrl, i)}
          >
            <PdfFileIcon />
            {decodeURIComponent(document.fileUrl || "")
              .split("/")
              .pop()
              ?.slice(27) || `File ${i + 1}`}
          </button>
        ))}
      </div>
    </>
  );
}
