import { XMarkIcon } from "@heroicons/react/24/outline";
import { ReactNode, useState } from "react";
import { toast } from "react-toastify";
import { useApprovePreAuthTopUpMutationMutation, useRejectPreAuthTopUpMutation } from "~lib/api";
import { PreAuthorizationTopUp, SimpleResponse } from "~lib/api/types";
import { VerticalStepper } from "~lib/components";
import Button from "../../../../components/ui/Button";
import DialogWrapper from "../../../../components/ui/modal/DialogWrapper";
import Text from "../../../../components/ui/typography/Text";
import { useNotifications } from "../../../../context/NotificationsContext";
import { AUTHENTICATED_USER } from "../../../../lib/payer-constants";
import { handleTryCatchError } from "../../../../utils/handleTryCatchError";
import LineItems from "./LineItems";
import Review from "./Review";
import SupportingDocuments from "./SupportingDocuments";
import { TopUpApprovalContext } from "./TopUpApprovalContext";
import { usePreAuthApprovalForm } from "../pre-auth-approval-form-context";
import { usePreAuthorizationPermissions } from "../../../../hooks/care/usePreAuthorizationPermissions";

type Props = {
  selectedTopUp: PreAuthorizationTopUp | null;
  onClose(): void;
};

type TopUpSteps = {
  title: string;
  description: string;
  Component: ReactNode;
};

const steps: TopUpSteps[] = [
  {
    title: "Line Items",
    description: "This section contains information the line items for the procedure . ",
    Component: <LineItems />,
  },
  {
    title: "Supporting Documents",
    description: "This section contains the necessary supporting documents for the procedure.",
    Component: <SupportingDocuments />,
  },
  {
    title: "Review",
    description:
      "Review the pre-authorization request and proceed with the appropriate action, such as approving or declining.",
    Component: <Review />,
  },
];
export default function PreAuthTopUpApprovalForm({ selectedTopUp, onClose }: Props) {
  const [activeStep, setActiveStep] = useState(0);

  const [isApproved, setIsApproved] = useState<boolean>();
  const [approvalAmount, setApprovalAmount] = useState("");
  const [reason, setReason] = useState("");

  const { activeNotification, setActiveNotification, handleMarkNotificationsAsRead } =
    useNotifications();

  const { preauth, hasTopUpPermissionForRequestType } = usePreAuthApprovalForm();
  const benefitType = preauth.requestType as string;
  const canTopUp = hasTopUpPermissionForRequestType(benefitType);

  const [approvePreAuthToUp, { isLoading: isApprovingTopUp }] =
    useApprovePreAuthTopUpMutationMutation();
  const [rejectPreAuthToUp, { isLoading: isRejectingTopUp }] = useRejectPreAuthTopUpMutation();

  const isPendingReview = isApprovingTopUp || isRejectingTopUp;
  const canSubmit =
    ((isApproved && Number(approvalAmount) > 0) || !isApproved) &&
    !isPendingReview &&
    isApproved !== undefined &&
    (isApproved ? canTopUp : true) &&
    reason.trim().length > 0;

  function handlePreviousClick() {
    if (activeStep === 0) return;

    setActiveStep((prev) => prev - 1);
  }

  function handleNextClick() {
    if (activeStep === steps.length - 1) return;

    setActiveStep((prev) => prev + 1);
  }

  async function handleReviewTopUp() {
    try {
      if (Number(approvalAmount) > Number(selectedTopUp?.requestedAmount))
        throw new Error(`Amount must be less than or equal to ${selectedTopUp?.requestedAmount}`);

      if (Number(approvalAmount) <= 0 && isApproved)
        throw new Error(`Approval amount should be more than 0`);

      if (
        Number(approvalAmount) < Number(selectedTopUp?.requestedAmount) &&
        reason.trim().length === 0 &&
        isApproved
      )
        throw new Error(
          `Please provide a reason for approving a top up of less than the requested ${selectedTopUp?.requestedAmount}`,
        );

      if (reason.trim().length === 0 && !isApproved)
        throw new Error(`Please provide a reason for rejecting the Top Up`);

      let res: SimpleResponse<boolean>;

      if (isApproved) {
        res = await approvePreAuthToUp({
          pathParameters: { preAuthTopUpId: selectedTopUp?.id as number },
          body: {
            actionBy: AUTHENTICATED_USER.getUserName(),
            amount: Number(approvalAmount) as number,
            reason,
          },
        }).unwrap();
      } else {
        res = await rejectPreAuthToUp({
          pathParameters: { preAuthTopUpId: selectedTopUp?.id as number },
          body: {
            actionBy: AUTHENTICATED_USER.getUserName(),
            reason,
          },
        }).unwrap();
      }

      toast.success(res.msg);

      if (activeNotification)
        await handleMarkNotificationsAsRead([activeNotification?.id as number]);

      setActiveNotification(null);
    } catch (error) {
      handleTryCatchError(error);
    }
  }

  function handleClose() {
    setActiveStep(0);
    onClose();
  }

  return (
    <DialogWrapper
      show={Boolean(selectedTopUp)}
      onClose={onClose}
      className="relative flex min-h-[700px] max-w-[1100px] flex-col px-7 py-7"
    >
      <TopUpApprovalContext.Provider
        value={{
          topUp: selectedTopUp,
          approvalAmount,
          isApproved,
          reason,
          setApprovalAmount,
          setIsApproved,
          setReason,
        }}
      >
        <button type="button" onClick={handleClose} className=" absolute right-4 top-4">
          <XMarkIcon className="h-6 w-6" />
        </button>

        <Text variant="subheading" className="mb-10 ml-12 text-center text-[24px] font-medium">
          Pre-Authorization Top Up Approval Form
        </Text>

        <div className="flex grow gap-8">
          <VerticalStepper containerClassName="w-1/4" active={activeStep} sections={steps} />
          <section className="grow">{steps[activeStep]?.Component}</section>
        </div>

        <div className="mt-6 flex items-center justify-end gap-6 ">
          {activeStep > 0 && (
            <button
              onClick={handlePreviousClick}
              className="rounded-lg px-4 py-2 font-medium text-gray-700"
              type="button"
            >
              Previous
            </button>
          )}
          {activeStep < steps.length - 1 && (
            <Button onClick={handleNextClick} className="rounded-lg px-4 py-2" type="button">
              Next
            </Button>
          )}
          {activeStep === steps.length - 1 && selectedTopUp?.status === "PENDING" && (
            <Button
              onClick={handleReviewTopUp}
              className="rounded-lg px-4 py-2"
              type="button"
              disabled={!canSubmit}
              title={
                isApproved && !canTopUp
                  ? "You don't have permission to top up this benefit type"
                  : "Submit"
              }
            >
              {isPendingReview ? "Submitting..." : "Submit"}
            </Button>
          )}

          {activeStep === steps.length - 1 && selectedTopUp?.status !== "PENDING" && (
            <Button onClick={handleClose} className="rounded-lg px-4 py-2" type="button">
              Close
            </Button>
          )}
        </div>
      </TopUpApprovalContext.Provider>
    </DialogWrapper>
  );
}
