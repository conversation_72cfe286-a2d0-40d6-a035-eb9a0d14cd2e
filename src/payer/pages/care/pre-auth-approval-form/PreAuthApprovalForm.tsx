import { XMarkIcon } from "@heroicons/react/24/outline";
import React, { useEffect, useState } from "react";
import { useFieldArray, useForm, useWatch } from "react-hook-form";
import { toast } from "react-toastify";
import {
  useApprovePreauthMutation,
  useDeclinePreauthMutation,
  useEditPreauthMutation,
  useGetBeneficiaryProviderBenefitsQuery,
  useGetBeneficiaryQuery,
  useGetPreauthQuery,
} from "~lib/api";
import {
  AuthorizePreauthRequest,
  DeclinePreauthRequest,
  EditPreauthRequest,
  genderLabels,
  PreAuth,
  PreauthEditor,
  PreAuthStatus,
  PreAuthType,
  preauthTypeLabels,
  RequestType,
  ServiceGroup,
} from "~lib/api/types";
import VerticalStepper from "~lib/components/VerticalStepper";
import {
  ComplexPrimitive,
  NetworkPrimitive,
  Primitive,
  serviceFields,
  services,
} from "~lib/service-fields";
import { formatCalculation, formatDateStringGB, formatMoney, responseError } from "~lib/utils";
import { calculateAgeYearsMonth } from "~lib/utils/dates";
import { getPreauthStage, Stage } from "~lib/utils/preauth";
import { useAuditLogsQuery } from "../../../api/services";
import NoData from "../../../components/illustrations/NoData";
import ModalSmall from "../../../components/ModalSmall";
import EmptyState from "../../../components/ui/EmptyState";
import Text from "../../../components/ui/typography/Text";
import { useNotifications } from "../../../context/NotificationsContext";
import { Form } from "../../../rhf-components/Form";
import UserService from "../../../services/UserService";
import { unCamelCase } from "../../../utils";
import PreAuthLineItems from "../PreAuthLineItems";
import { PreAuthJourney } from "../PreAuthorizations";
import IncompletePreAuthDetails from "./IncompletePreauthDetails";
import { PreAuthApprovalFormContext } from "./pre-auth-approval-form-context";
import {
  ApprovalStatus,
  approvalStatusDescription,
  EMPTY_LINE,
  fileExtensionIcons,
  Id,
  Inputs,
  Response,
} from "./pre-auth-approval-form.misc";
import Review from "./Review";
import TopUpInformation from "./TopUpInformation";
import { usePreAuthorizationPermissions } from "../../../hooks/care/usePreAuthorizationPermissions";

interface Props {
  id: number;
  closeModal: () => void;
  preAuthJourney: PreAuthJourney | null;
  onContinueJourneyClick: () => void;
}

type Section = {
  title: string;
  description: string;
  hideSection?: boolean;
  component: JSX.Element;
};

export default function PreAuthApprovalForm({
  id,
  closeModal,
  preAuthJourney,
  onContinueJourneyClick,
  hasViewPermissionForRequestType,
  hasApprovePermissionForRequestType,
  hasTopUpPermissionForRequestType,
}: Props) {
  const { activeNotification, handleMarkNotificationsAsRead, setActiveNotification } =
    useNotifications();

  const [active, setActive] = useState(0);
  const [downloadIndex, setDownloadIndex] = useState<undefined | number>();
  const [showConfirmModal, setConfirmModal] = React.useState(false);
  const [formData, setFormData] = React.useState({});
  const [shouldShowTopUpApprovalFields, setShouldShowTopUpApprovalFields] = useState(false);
  const [shouldShowIncompleteStatusDetails, setShouldShowIncompleteStatusDetails] = useState(false);

  const {
    data: preauth,
    isLoading: isPreAuthLoading,
    isFetching: isPreauthFetching,
    error: preauthError,
  } = useGetPreauthQuery(
    { id: id },
    {
      refetchOnFocus: import.meta.env.PROD,
    },
  );

  const latestTopUp = preauth?.topUps
    .toSorted((prevTopUp, nextTopUp) => nextTopUp.id - prevTopUp.id)
    .at(0);

  const pendingTopUp = preauth?.topUps.find((topUp) => topUp.status === "PENDING");

  const methods = useForm<Inputs>({
    defaultValues: {
      amount: 0,
      notes: "",
      // Reuse admission validity period
      numberOfDaysValid: preauth?.validForDays || 1,
      allowedDaysOfAdmission: 1,
      netOfNHIFLimit: 0,
      doctorFees: 0,
      guidelines: [],
    },
    shouldUnregister: true,
    reValidateMode: "onBlur",
  });

  const { watch, unregister, control, formState } = methods;
  const form = watch();

  const guidelinesError = formState.errors?.["guidelines"]?.root?.message;
  const guidelinesItemsError = formState.errors?.["guidelines"]?.length ? "Invalid input" : null;

  const { fields, append, remove } = useFieldArray({
    control,
    name: "guidelines",
  });

  const guidelines = useWatch({
    name: "guidelines",
    control,
  });

  const undertakingTotalCost = guidelines?.reduce((acc, curr) => acc + curr.cost, 0);

  const { benefitId } = form;

  /**
   * Regular preauthorization is fully filled
   * Scheduled preauthorization has all sections
   * Unfilled emergency preauth has no procedure information or supporting documents
   * Filled emergency preauth has no amount
   */

  const isAdmissionRequest =
    preauth?.preauthType === PreAuthType.EMERGENCY_ADMISSION ||
    preauth?.preauthType === PreAuthType.SCHEDULED_ADMISSION;

  const isUnfilledEmergencyPreauth =
    preauth?.preauthType === PreAuthType.EMERGENCY_ADMISSION && preauth?.draft;

  const stage = getPreauthStage(preauth);

  /**
   * Hide review section for filled and approved emergency admission
   */
  const hideReview = stage == Stage.EMERGENCY_FILLED_APPROVED;

  /**
   * Disable preauth edits for emergency notifications
   */
  const disableMarkIncomplete = stage == Stage.EMERGENCY_NOTIFICATION;

  const allowMarkIncomplete =
    preauth &&
    [PreAuthStatus.PENDING, PreAuthStatus.DRAFT].includes(preauth.status) &&
    !disableMarkIncomplete;

  const visit = preauth?.visit;

  const {
    data: beneficiary,
    isLoading: isBeneficiaryLoading,
    isFetching: isBeneficiaryFetching,
    error: beneficiaryError,
  } = useGetBeneficiaryQuery(
    { beneficiaryId: visit?.beneficiaryId ?? 0 },
    { skip: !visit?.beneficiaryId },
  );

  const {
    data: benefitsResponse,
    isLoading: isBenefitsLoading,
    isFetching: _isBenefitsFetching,
    error: benefitsError,
  } = useGetBeneficiaryProviderBenefitsQuery(
    {
      providerId: visit?.hospitalProviderId ?? 0,
      beneficiaryId: visit?.beneficiaryId ?? 0,
    },
    {
      skip: !isAdmissionRequest || !visit?.hospitalProviderId || !visit?.beneficiaryId,
    },
  );

  const benefits = benefitsResponse?.data;

  const activeBenefit =
    benefits?.length && benefitId
      ? benefits.find((benefit) => benefit.benefitId == benefitId)
      : undefined;

  const [approvePreauth, { isLoading: isApprovePreauthLoading }] = useApprovePreauthMutation();
  const [declinePreauth, { isLoading: isDeclinePreauthLoading }] = useDeclinePreauthMutation();
  const [editPreauth, { isLoading: isEditPreauthLoading }] = useEditPreauthMutation();

  const { data, isLoading: isLoadingLogs } = useAuditLogsQuery({
    preAuthId: id as number,
  });

  const isFormLoading = isApprovePreauthLoading || isDeclinePreauthLoading || isEditPreauthLoading;

  const username = UserService.getUsername();

  // Skip to first step when a different preauth is selected
  // You probably don't want this in development since it will jump to the first section on hot reload
  useEffect(() => {
    if (import.meta.env.PROD) {
      setActive(0);
    }
  }, [id]);

  async function handleSubmit(data: Inputs) {
    const requestType = preauth?.requestType as RequestType;
    const canApprove = hasApprovePermissionForRequestType(requestType);
    const canTopUp = hasTopUpPermissionForRequestType(requestType);

    if (data.approval === ApprovalStatus.APPROVE) {
      if (pendingTopUp && !canTopUp) {
        toast.error("You don't have permission to top up this benefit type");
        return;
      }
      if (!pendingTopUp && !canApprove) {
        toast.error("You don't have permission to approve this benefit type");
        return;
      }
    }

    setFormData(data);

    const {
      approval,
      amount,
      notes,
      guidelines,
      benefitId,
      allowedDaysOfAdmission,
      numberOfDaysValid,
      reason,
      topUpAmount,
      ...rest
    } = data;

    let response: Response | undefined;

    try {
      if (!preauth?.id) {
        throw new Error("Preauth id not found");
      }

      if (approval == ApprovalStatus.APPROVE) {
        let payload: AuthorizePreauthRequest & Id = {
          id: preauth?.id,
          authorizer: username,
        };

        const isStatusDraft = preauth?.status === PreAuthStatus.DRAFT;
        const isTypeScheduled = preauth?.preauthType === PreAuthType.SCHEDULED_ADMISSION;
        const isTypeEmergency = preauth?.preauthType === PreAuthType.EMERGENCY_ADMISSION;

        if (isStatusDraft && isTypeScheduled) {
          payload = { ...payload, guidelines, benefitId, notes };
        } else if (isStatusDraft && isTypeEmergency) {
          payload = preauth?.draft
            ? payload
            : { ...payload, guidelines, benefitId, numberOfDaysValid, notes };
        } else if (benefitId) {
          payload = {
            ...payload,
            amount,
            notes,
            allowedDaysOfAdmission,
            numberOfDaysValid,
            reason,
            benefitId,
            ...rest,
          };
        } else {
          payload = {
            ...payload,
            amount,
            notes,
            allowedDaysOfAdmission,
            numberOfDaysValid,
            reason,
            ...rest,
          };
        }

        response = await approvePreauth(payload);
        //check dailyLimit
        const responseError = response?.error?.data?.error;
        if (
          responseError != undefined &&
          responseError.includes("Amount exceeds the set benefit visit limit of KES")
        ) {
          setConfirmModal(true);
        }
        //End check dailyLimit
      } else if (approval == ApprovalStatus.DECLINE) {
        const payload: DeclinePreauthRequest & Id = {
          id: preauth?.id,
          declinedBy: username,
          reason: notes,
        };
        response = await declinePreauth(payload);
      } else if (approval == ApprovalStatus.INCOMPLETE) {
        if (!allowMarkIncomplete || !canApprove) {
          throw new Error("Cannot mark this preauth as incomplete");
        }

        const payload: EditPreauthRequest = {
          id: preauth?.id,
          body: {
            editor: PreauthEditor.PAYER,
            editedByUser: username,
            markAsIncomplete: true,
            notes: notes,
            markAsIncompleteReason: reason,
          },
        };

        response = await editPreauth(payload);
      } else {
        throw new Error(`Unknown approval status: ${approval}`);
      }

      if (!response) {
        throw new Error("No response received");
      }

      const errorMessage = responseError(response);
      if (errorMessage) {
        throw new Error(errorMessage);
      }

      toast.success(
        `Preauth ${approvalStatusDescription[approval] || approval + "ed"} successfully`,
      );
      closeModal();
    } catch (error) {
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error("Something went wrong");
      }
    } finally {
      if (activeNotification) {
        await handleMarkNotificationsAsRead([activeNotification?.id as number]);
        setActiveNotification(null);
      }
    }
  }

  const [shouldShowUpdateDisplay, setShouldShowUpdateDisplay] = useState(false);

  const preAuthLogs = data?.data?.content || [];
  const markAsIncompleteLogs = preAuthLogs.filter(
    (log) => log.eventName === "Pre-auth marked as incomplete",
  );

  const topUpAuthorizedLog = preAuthLogs.find((log) => log.eventName === "Top up authorized");
  const topUpRejectedLog = preAuthLogs.find((log) => log.eventName === "Top up rejected");
  const topUpRequestLog = preAuthLogs.find((log) => log.eventName === "Top up request");

  const lastMarkAsIncompleteLog = markAsIncompleteLogs[0];

  const isMarkAsIncompleteReasonAvailable =
    Number(preauth?.markAsIncompleteReason?.trim()?.length) > 0;

  useEffect(() => {
    if (lastMarkAsIncompleteLog) {
      setShouldShowUpdateDisplay(true);
    }
    setShouldShowIncompleteStatusDetails(isMarkAsIncompleteReasonAvailable);
  }, [isMarkAsIncompleteReasonAvailable, lastMarkAsIncompleteLog]);

  const approvalStatusOptions = [
    ApprovalStatus.APPROVE,
    ApprovalStatus.DECLINE,
    ...(allowMarkIncomplete ? [ApprovalStatus.INCOMPLETE] : []),
  ];

  /**
   * TODO: Check benefit restriction?
   */
  const filteredBenefits =
    benefits?.filter(
      (benefitBeneficiary) =>
        ![ServiceGroup.DENTAL, ServiceGroup.OPTICAL, ServiceGroup.OUTPATIENT].includes(
          benefitBeneficiary.benefit.benefitRef.serviceGroup,
        ) && benefitBeneficiary.billable !== false,
    ) || [];

  const sections: Section[] = [
    {
      title: "Patient Information",
      description: "This section contains information about the patient and the insurance/payer.",
      component: (
        <>
          <div className="grid gap-12 lg:grid-cols-2">
            <div>
              <div className="mb-8 flex flex-col gap-1">
                <p className="mb-2 text-sm text-[#61758A]">Name of Patient</p>
                <div className="border-b border-[#C4C4C4] text-[#304254]">
                  {visit?.memberName || preauth?.visit?.memberName || EMPTY_LINE}
                </div>
              </div>

              <div className="mb-8 flex flex-col gap-1">
                <p className="mb-2 text-sm text-[#61758A]">Member Number</p>
                <div className="border-b border-[#C4C4C4] text-[#304254]">
                  {visit?.memberNumber || preauth?.visit?.memberNumber || EMPTY_LINE}
                </div>
              </div>

              <div className="mb-8 flex-col gap-1">
                <p className="mb-2 text-sm text-[#61758A]">Gender</p>
                <div className="border-b border-[#C4C4C4] text-[#304254]">
                  {beneficiary?.gender ? genderLabels.get(beneficiary?.gender) : EMPTY_LINE}
                </div>
              </div>

              <div className="mb-8 flex-col gap-1">
                <p className="mb-2 text-sm text-[#61758A]">Age</p>
                <div className="border-b border-[#C4C4C4] text-[#304254]">
                  {beneficiary?.dob
                    ? `${formatCalculation(calculateAgeYearsMonth(new Date(beneficiary?.dob)))}`
                    : EMPTY_LINE}
                </div>
              </div>
            </div>

            <div>
              <div className="mb-8 flex flex-col gap-1">
                <p className="mb-2 text-sm text-[#61758A]">Scheme</p>
                <div className="line-clamp-1 border-b border-[#C4C4C4] text-[#304254]">
                  {preauth?.schemeName ||
                    visit?.scheme?.name ||
                    preauth?.visit?.scheme?.name ||
                    beneficiary?.category?.policy?.plan?.name ||
                    EMPTY_LINE}
                </div>
              </div>

              <div className="mb-8 flex flex-col gap-1">
                <p className="mb-2 text-sm text-[#61758A]">Payer</p>
                <div className="line-clamp-1 border-b border-[#C4C4C4] text-[#304254]">
                  {preauth?.payerName ||
                    preauth?.visit?.payerName ||
                    visit?.payerName ||
                    EMPTY_LINE}
                </div>
              </div>

              <div className="mb-8 flex flex-col gap-1">
                <p className="mb-2 text-sm text-[#61758A]">Category</p>
                <div className="border-b border-[#C4C4C4] text-[#304254]">
                  {beneficiary?.category.name || EMPTY_LINE}
                </div>
              </div>

              {!preauth?.draft && (
                <div className="mb-8 flex-col gap-1">
                  <p className="mb-2 text-sm text-[#61758A]">Balance</p>
                  <div className="border-b border-[#C4C4C4] text-[#304254]">
                    {formatMoney(preauth?.visit?.balanceAmount) || EMPTY_LINE}
                  </div>
                </div>
              )}
            </div>
          </div>
        </>
      ),
    },
    {
      title: "Diagnosis Information",
      description: "This section contains information about the patient’s diagnosis.",
      component: (
        <>
          <div className="grid gap-8 lg:grid-cols-2">
            <div>
              {!isAdmissionRequest && !preauth?.draft && (
                <div className="mb-8 flex flex-col gap-1">
                  <p className="mb-2 text-sm text-[#61758A]">Benefit Name</p>
                  <div className="border-b border-[#C4C4C4] text-[#304254]">
                    {preauth?.visit?.benefitName || EMPTY_LINE}
                  </div>
                </div>
              )}

              <div className="mb-8 flex flex-col gap-1">
                <p className="mb-2 text-sm text-[#61758A]">Request Type</p>
                <div className="border-b border-[#C4C4C4] text-[#304254]">
                  {preauth?.requestType || EMPTY_LINE}
                </div>
              </div>

              {preauth?.preauthType && (
                <div className="mb-8 flex flex-col gap-1">
                  <p className="mb-2 text-sm text-[#61758A]">Preauth Type</p>
                  <div className="border-b border-[#C4C4C4] text-[#304254]">
                    {preauthTypeLabels.get(preauth?.preauthType) || "Unknown Preauth Type"}
                  </div>
                </div>
              )}

              {preauth?.initialNotes && (
                <div className="mb-8 flex flex-col gap-1">
                  <p className="mb-2 text-sm text-[#61758A]">Reason</p>
                  <div className="border-b border-[#C4C4C4] text-[#304254]">
                    {preauth.initialNotes}
                  </div>
                </div>
              )}
            </div>

            <div>
              <div className="mb-8 flex flex-col gap-1">
                <p className="mb-2 text-sm text-[#61758A]">Service</p>
                <div className="border-b border-[#C4C4C4] text-[#304254]">
                  {services.find((service) => service.name == preauth?.service)?.label ||
                    EMPTY_LINE}
                </div>
              </div>
              <div className="mb-8 flex flex-col gap-1">
                <p className="mb-2 text-sm text-[#61758A]">Prediagnosis</p>
                <div className="border-b border-[#C4C4C4] text-[#304254]">
                  {preauth?.diagnosisInfo
                    ?.map((diagnosis) => diagnosis.title)
                    .filter(Boolean)
                    .join(", ") ||
                    preauth?.prediagnosisCodes?.join(", ") ||
                    EMPTY_LINE}
                </div>
              </div>
            </div>
          </div>
        </>
      ),
    },
    {
      title: "Procedure Information",
      description: "This section contains information about the patient’s procedure.",
      hideSection: isUnfilledEmergencyPreauth,
      component: (
        <div className="grid gap-4 lg:grid-cols-2">
          {/* TODO: Render text box */}
          {serviceFields
            .filter((field) => field.service === preauth?.service)
            .map((field) => (
              <React.Fragment key={field.name}>
                {(Object.values(Primitive).includes(field.type as Primitive) ||
                  Object.values(NetworkPrimitive).includes(field.type as NetworkPrimitive) ||
                  field.type === ComplexPrimitive.Options) && (
                  <div className="flex flex-col gap-1" key={field.service + field.name}>
                    <p className="mb-2 text-sm text-[#61758A]">{field.label}</p>

                    <div className="border-b border-[#C4C4C4] text-[#304254]">
                      {field.type === Primitive.Boolean ? (
                        preauth?.[field.name as keyof PreAuth] == null ? (
                          "N/A"
                        ) : preauth?.[field.name as keyof PreAuth] ? (
                          "True"
                        ) : (
                          "False"
                        )
                      ) : field.type == Primitive.Date ? (
                        formatDateStringGB(
                          (preauth?.[field.name as keyof PreAuth] as string) ?? "",
                        ) || "N/A"
                      ) : Object.values(Primitive).includes(field.type as Primitive) ||
                        Object.values(NetworkPrimitive).includes(field.type as NetworkPrimitive) ? (
                        preauth?.[field.name] || "N/A"
                      ) : field.type === ComplexPrimitive.Options ? (
                        unCamelCase((preauth?.[field.name as keyof PreAuth] as string) ?? "") ||
                        "N/A"
                      ) : (
                        <></>
                      )}
                    </div>
                  </div>
                )}
              </React.Fragment>
            ))}

          {preauth?.notes && (
            <div className="mb-8 flex flex-col gap-1" key="notes">
              <p className="mb-2 text-sm text-[#61758A]">Notes</p>
              <div className="border-b border-[#C4C4C4] text-[#304254]">
                {preauth?.notes || EMPTY_LINE}
              </div>
            </div>
          )}
        </div>
      ),
    },
    {
      title: "Line Items",
      description: "This section contains the line items for the procedure.",
      hideSection: isUnfilledEmergencyPreauth,
      component: <PreAuthLineItems preAuth={preauth} />,
    },
    {
      title: "Supporting Documents",
      description: "This section contains the necessary supporting documents for the procedure.",
      hideSection: isUnfilledEmergencyPreauth,
      component: (
        <>
          <div>
            {!username && (
              <div
                className="mb-4 rounded border border-red-400 bg-red-100 px-4 py-3 text-sm text-red-700"
                role="alert"
              >
                <strong className="mr-2 font-bold">Authentication Error!</strong>
                <span className="block sm:inline">
                  Your login session has expired or is invalid. Please log out and login again, or
                  use an account with the payer role.
                </span>
              </div>
            )}

            <p className="mb-6 text-xl text-[#61758A]">Supporting documents</p>

            <div className="mb-4 px-4">
              {!preauth?.supportingDocuments || preauth?.supportingDocuments?.length === 0 ? (
                <div className="flex items-center justify-center ">
                  <EmptyState
                    illustration={<NoData size={200} />}
                    message={{ title: "Service has no line items" }}
                  />
                </div>
              ) : (
                <div className="grid items-baseline justify-start gap-8 py-2 lg:grid-cols-4">
                  {preauth?.supportingDocuments?.map((url, i) => (
                    <button
                      key={i}
                      onClick={() => {
                        handleDownloadFile(url, i);
                      }}
                      type="button"
                    >
                      <div className="flex w-[120px] flex-col items-center gap-4 rounded-lg bg-[#F3F4F5] p-2">
                        {fileExtensionIcons(url.split(".").pop()?.toLowerCase() || "")}
                        <div className="flex gap-2">
                          {downloadIndex === i && (
                            <span>
                              {/* prettier-ignore */}
                              <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"
                                   className="text-blue-400">
                              <path fill="currentColor"
                                    d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform
                                attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12"
                                repeatCount="indefinite" /></path>
                            </svg>
                            </span>
                          )}

                          {/* TODO: Break on special characters */}
                          <p className="line-clamp-1 break-all text-sm">
                            {decodeURIComponent(url || "")
                              .split("/")
                              .pop()
                              ?.slice(27) || `File ${i + 1}`}
                          </p>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </>
      ),
    },
    {
      title: "Top Up Information",
      description: "This section contains information about the top up request.",
      component: <TopUpInformation />,
      hideSection: preauth?.topUps.length === 0,
    },
    {
      title: "Review",
      description:
        "Review the pre-authorization request and proceed with the appropriate action, such as approving, declining, or marking it as incomplete.",
      component: <Review />,
    },
  ];

  async function handleOverride() {
    const { notes, amount, allowedDaysOfAdmission, numberOfDaysValid, ...rest } = formData;
    try {
      if (!preauth?.id) {
        throw new Error("Preauth id not found");
      }

      let payload: AuthorizePreauthRequest & Id = {
        id: preauth?.id,
        authorizer: username,
        ignoreErrors: true,
      };

      const isStatusDraft = preauth?.status === PreAuthStatus.DRAFT;
      const isTypeScheduled = preauth?.preauthType === PreAuthType.SCHEDULED_ADMISSION;
      const isTypeEmergency = preauth?.preauthType === PreAuthType.EMERGENCY_ADMISSION;

      if (isStatusDraft && isTypeScheduled) {
        payload = { ...payload, guidelines, benefitId, notes };
      } else if (isStatusDraft && isTypeEmergency) {
        payload = preauth?.draft
          ? payload
          : { ...payload, guidelines, benefitId, numberOfDaysValid, notes };
      } else {
        payload = { ...payload, amount, notes, allowedDaysOfAdmission, numberOfDaysValid, ...rest };
      }

      const response = await approvePreauth(payload);

      if (!response) {
        throw new Error("No response received");
      }

      const errorMessage = responseError(response);
      if (errorMessage) {
        throw new Error(errorMessage);
      }

      toast.success("Preauth approved with override successfully");
      setConfirmModal(false); // Close the modal after successful override
      closeModal(); // Close the main modal or do any other post-success actions
    } catch (error) {
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error("Something went wrong during the override process");
      }
    }
  }

  async function handleDownloadFile(url: string, index: number) {
    setDownloadIndex(index);
    const name = url.split("/").pop();

    try {
      if (!name) throw new Error("File not found");

      const response = await fetch(`https://api.lctafrica.net/api/file/download?name=${name}`);

      const res = await response.json();

      const a = document.createElement("a");

      a.href = res.data;

      a.download =
        decodeURIComponent(url || "")
          .split("/")
          .pop()
          ?.slice(27) || `File ${index + 1}`;

      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    } catch (error) {
      toast.error((error as Error)?.message || "Something went wrong");
      console.error("Error: ", error);
    } finally {
      setDownloadIndex(undefined);
    }
  }

  useEffect(() => {
    // Unregister notes field if approval is APPROVE and amount is equal to request amount
    // Fixes an issue where the notes field is required when it shouldn't be
    if (
      form.approval === ApprovalStatus.APPROVE &&
      (form.amount === preauth?.requestAmount || preauth?.status === PreAuthStatus.DRAFT)
    ) {
      unregister("notes");
    }
  }, [form.approval, form.amount, preauth?.requestAmount, unregister, preauth?.status]);

  const filteredSections = sections.filter((section) => !section.hideSection);

  return (
    <PreAuthApprovalFormContext.Provider
      value={{
        activeBenefit,
        append,
        approvalStatusOptions,
        benefitsError,
        fields,
        filteredBenefits,
        form,
        guidelinesError,
        guidelinesItemsError,
        isBenefitsLoading,
        preauth,
        remove,
        undertakingTotalCost,
        lastMarkAsIncompleteLog,
        shouldShowUpdateDisplay,
        isLoadingLogs,
        preAuthLogs,
        shouldShowTopUpApprovalFields,
        latestTopUp,
        pendingTopUp,
        topUpAuthorizedLog,
        topUpRejectedLog,
        topUpRequestLog,
        hasViewPermissionForRequestType,
        hasApprovePermissionForRequestType,
        hasTopUpPermissionForRequestType,
      }}
    >
      {shouldShowIncompleteStatusDetails && preauth.status === PreAuthStatus.PENDING ? (
        <IncompletePreAuthDetails
          preAuth={preauth}
          onCancelClick={closeModal}
          onContinueClick={() => setShouldShowIncompleteStatusDetails(false)}
        />
      ) : (
        <div className="p-6">
          {isPreAuthLoading ||
          isBeneficiaryLoading ||
          isPreauthFetching ||
          isBeneficiaryFetching ||
          isLoadingLogs ? (
            <div className="flex items-center justify-center py-8">
              {/* prettier-ignore */}
              <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"
                   className="text-blue-400">
                <path fill="currentColor"
                      d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z">
                  <animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12"
                                    repeatCount="indefinite" />
                </path>
              </svg>
            </div>
          ) : preauthError || beneficiaryError ? (
            <div className="px-2 py-8 text-red-500">
              Something went wrong. Please refresh the page to retry.
            </div>
          ) : !preauth || !visit ? (
            <div className="flex items-center justify-center p-8">
              <div className="flex flex-col items-center">
                {/* prettier-ignore */}
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5}
                     stroke="currentColor" className="w-12 h-12 text-gray-200">
                  <path strokeLinecap="round" strokeLinejoin="round"
                        d="M2.25 13.5h3.86a2.25 2.25 0 012.012 1.244l.256.512a2.25 2.25 0 002.013 1.244h3.218a2.25 2.25 0 002.013-1.244l.256-.512a2.25 2.25 0 012.013-1.244h3.859m-19.5.338V18a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18v-4.162c0-.224-.034-.447-.1-.661L19.24 5.338a2.25 2.25 0 00-2.15-1.588H6.911a2.25 2.25 0 00-2.15 1.588L2.35 13.177a2.25 2.25 0 00-.1.661z" />
                </svg>

                <h3 className="mt-4 text-center text-xl font-bold text-gray-400">
                  PreAuth {!visit ? "visit" : ""} not found.
                </h3>
              </div>
            </div>
          ) : (
            <div>
              <div className="grid grid-cols-4 gap-6 px-2">
                <VerticalStepper sections={filteredSections} active={active} />

                <Form
                  methods={methods}
                  onSubmit={handleSubmit}
                  className="col-span-3 flex min-h-[600px] flex-col"
                >
                  <div className="mb-8 flex items-center justify-between">
                    <Text
                      variant="subheading"
                      className="ml-12 text-center text-[24px] font-medium"
                    >
                      Pre-Authorization Approval Form
                    </Text>

                    <button type="button" onClick={closeModal}>
                      <XMarkIcon className="w-5" strokeWidth={2} />
                    </button>
                  </div>

                  <div className="mb-4 flex-grow">{filteredSections[active]?.component}</div>

                  <div className="flex justify-end gap-4">
                    {active == 0 &&
                      isMarkAsIncompleteReasonAvailable &&
                      preauth.status === PreAuthStatus.PENDING && (
                        <button
                          type="button"
                          className="rounded px-4 py-2 font-medium text-gray-500 hover:bg-gray-100"
                          onClick={() => setShouldShowIncompleteStatusDetails(true)}
                        >
                          Back
                        </button>
                      )}
                    {active > 0 && (
                      <button
                        type="button"
                        className="rounded px-4 py-2 font-medium text-gray-500 hover:bg-gray-100"
                        onClick={() => setActive(active - 1)}
                      >
                        Previous
                      </button>
                    )}

                    {active < filteredSections.length - 1 && (
                      <button
                        type="button"
                        className="rounded-lg bg-[#2563EB] px-4 py-2 font-medium text-white hover:bg-blue-700"
                        onClick={() => setActive(active + 1)}
                      >
                        Next
                      </button>
                    )}

                    {active === filteredSections.length - 1 &&
                      (shouldShowUpdateDisplay &&
                      (preauth?.status === PreAuthStatus.DRAFT ||
                        preauth?.status === PreAuthStatus.PENDING) &&
                      !preauth.markAsIncomplete ? (
                        <div
                          className="flex cursor-pointer gap-2 rounded-lg bg-[#2563EB] px-4 py-2 font-medium text-white hover:bg-blue-700"
                          onClick={() => setShouldShowUpdateDisplay(false)}
                        >
                          <span>Update</span>
                        </div>
                      ) : (preauth?.status === PreAuthStatus.DRAFT ||
                          preauth?.status === PreAuthStatus.PENDING) &&
                        !preauth.markAsIncomplete ? (
                        <button
                          type="submit"
                          className="flex gap-2 rounded-lg bg-[#2563EB] px-4 py-2 font-medium text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-80"
                          disabled={
                            isFormLoading ||
                            (form.approval === ApprovalStatus.APPROVE &&
                              ((!hasApprovePermissionForRequestType(
                                preauth.requestType as RequestType,
                              ) &&
                                !pendingTopUp) ||
                                (!hasTopUpPermissionForRequestType(
                                  preauth.requestType as RequestType,
                                ) &&
                                  pendingTopUp)))
                          }
                          title={
                            form.approval === ApprovalStatus.APPROVE &&
                            ((!hasApprovePermissionForRequestType(
                              preauth.requestType as RequestType,
                            ) &&
                              !pendingTopUp) ||
                              (!hasTopUpPermissionForRequestType(
                                preauth.requestType as RequestType,
                              ) &&
                                pendingTopUp))
                              ? pendingTopUp
                                ? "You don't have permission to top up this benefit type"
                                : "You don't have permission to approve this benefit type"
                              : "Submit"
                          }
                        >
                          {isFormLoading && (
                            <svg
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              xmlns="http://www.w3.org/2000/svg"
                              className="text-white"
                            >
                              <path
                                fill="currentColor"
                                d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"
                              >
                                <animateTransform
                                  attributeName="transform"
                                  type="rotate"
                                  dur="0.75s"
                                  values="0 12 12;360 12 12"
                                  repeatCount="indefinite"
                                />
                              </path>
                            </svg>
                          )}
                          <span>Submit</span>
                        </button>
                      ) : ![null, PreAuthJourney.ReviewTopUp].includes(preAuthJourney) ? (
                        <button
                          type="button"
                          className="flex gap-2 rounded-lg bg-[#2563EB] px-4 py-2 font-medium text-white hover:bg-blue-700"
                          onClick={onContinueJourneyClick}
                        >
                          <span>Continue</span>
                        </button>
                      ) : (
                        <button
                          type="button"
                          className="flex gap-2 rounded-lg bg-[#2563EB] px-4 py-2 font-medium text-white hover:bg-blue-700"
                          onClick={closeModal}
                        >
                          <span>Close</span>
                        </button>
                      ))}
                  </div>
                </Form>
              </div>
            </div>
          )}

          {showConfirmModal && (
            <ModalSmall
              id={confirm}
              title={confirm}
              modalOpen={showConfirmModal}
              setModalOpen={setConfirmModal}
            >
              <div className="w-full bg-white p-6">
                <p className="mb-4 text-gray-800">
                  The preauth amount exceeds the daily limit. Do you want to proceed and override
                  the limit?
                </p>
                <div className="flex justify-end space-x-4">
                  <button
                    onClick={handleOverride}
                    className="rounded-lg bg-[#2563EB] px-4 py-2 font-semibold text-white hover:bg-blue-700"
                  >
                    Yes, override
                  </button>
                  <button
                    onClick={() => setConfirmModal(false)}
                    className="rounded-lg border border-gray-300 px-4 py-2 font-semibold text-gray-700 hover:bg-gray-100"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </ModalSmall>
          )}
        </div>
      )}
    </PreAuthApprovalFormContext.Provider>
  );
}
