import { Fragment, useEffect, useState } from "react";
import { useGetBeneficiaryProviderBenefitsQuery } from "~lib/api";
import { PreAuthType } from "~lib/api/types";
import { Empty, Select } from "~lib/components";
import ErrorMessage from "~lib/components/Error";
import LoadingAnimation from "../../../components/animations/LoadingAnimation/LoadingAnimation";
import Toggle from "../../../components/ui/Toggle";
import FieldWrapper from "../../../rhf-components/FieldWrapper";
import Input from "../../../rhf-components/Input";
import RadioGroup from "../../../rhf-components/RadioGroup";
import TextArea from "../../../rhf-components/TextArea";
import { usePreAuthApprovalForm } from "./pre-auth-approval-form-context";
import {
  ADMISSION_REQUEST_TYPE,
  ApprovalStatus,
  approvalStatusLabels,
  Inputs,
} from "./pre-auth-approval-form.misc";
import { useFormContext } from "react-hook-form";
import { RequestType } from "../../../api/types";
import { usePreAuthorizationPermissions } from "../../../hooks/care/usePreAuthorizationPermissions";

export default function PendingReview() {
  const [isChangBenefitEnabled, setIsChangBenefitEnabled] = useState(false);
  const { preauth, form, approvalStatusOptions, shouldShowUpdateDisplay, lastMarkAsIncompleteLog } =
    usePreAuthApprovalForm();

  const { hasApprovePermissionForRequestType, hasViewPermissionForRequestType } =
    usePreAuthorizationPermissions();

  const { clearErrors, unregister, setValue } = useFormContext<Inputs>();

  const {
    data: benefitsResponse,
    isLoading: isBenefitsLoading,
    error: benefitsError,
  } = useGetBeneficiaryProviderBenefitsQuery({
    providerId: preauth?.visit?.hospitalProviderId ?? 0,
    beneficiaryId: preauth?.visit?.beneficiaryId ?? 0,
  });

  const benefits = benefitsResponse?.data || [];

  const requestType = preauth?.requestType as RequestType;
  const hasViewPermission = hasViewPermissionForRequestType(requestType);
  const canApprove = hasApprovePermissionForRequestType(requestType);

  // reset approval if user lacks permission to avoid form submission
  useEffect(() => {
    if (!hasViewPermission || (form.approval === ApprovalStatus.APPROVE && !canApprove)) {
      setValue("approval", undefined);
      clearErrors("approval");
    }
  }, [form.approval, canApprove, hasViewPermission, setValue, clearErrors]);

  // only show approval options if user has view permission
  const filteredApprovalOptions = hasViewPermission
    ? approvalStatusOptions.map((status) => ({
        label: approvalStatusLabels[status] || status,
        value: status,
        disabled:
          (status === ApprovalStatus.APPROVE && !canApprove) ||
          (status === ApprovalStatus.INCOMPLETE && !canApprove),
        title:
          status === ApprovalStatus.APPROVE && !canApprove
            ? "You don't have permission to approve this benefit type"
            : status === ApprovalStatus.INCOMPLETE && !canApprove
            ? "You don't have permission to mark this preauth as incomplete"
            : undefined,
      }))
    : [];

  useEffect(() => {
    unregister("notes");
    clearErrors("notes");
    setValue("notes", "");
  }, [clearErrors, form.approval, setValue, unregister]);

  return (
    <div className="">
      <section className="grid grid-cols-2">
        <div className="mb-8 flex flex-col gap-1">
          <p className="text-2xl font-black text-[#030712]">
            <span className="">Ksh </span>
            <span className="">{preauth?.requestAmount || 0}</span>
          </p>
          <p className="mb-2 ml-8 mt-1 text-sm text-[#61758A]">Requested Amount</p>
        </div>

        {shouldShowUpdateDisplay && (
          <div className="mb-8 flex flex-col gap-1">
            <p className="text-2xl font-black text-[#CA8A04]">
              <span className="">Ksh </span>
              <span className="">{preauth?.authorizedAmount || 0}</span>
            </p>
            <p className="mb-2 ml-8 mt-1 text-sm text-[#61758A]">Approved Amount</p>
          </div>
        )}
      </section>

      {shouldShowUpdateDisplay ? (
        <section className="grid-col mb-8 grid grid-cols-2 gap-8">
          <div className="mb-8 flex flex-col gap-1">
            <p className="mb-2 text-sm text-[#61758A]">First Actioned By</p>
            <div className="border-b border-gray-200 pb-2">
              {lastMarkAsIncompleteLog?.actionByUser}
            </div>
          </div>
          <div className="mb-8 flex flex-col gap-1">
            <p className="mb-2 text-sm text-[#61758A]">Action</p>
            <div className="border-b border-gray-200 pb-2">Marked as incomplete</div>
          </div>
        </section>
      ) : hasViewPermission ? (
        <fieldset className="mb-4 grid gap-4 rounded border border-gray-300 px-4 py-1 text-sm lg:grid-cols-2">
          <legend className="px-2 text-xs text-gray-400">Approve/Decline</legend>

          <FieldWrapper label="Decision" name="approval" className="mb-2 text-black" required>
            <RadioGroup
              options={filteredApprovalOptions}
              orientation="vertical"
              solid
              disabled={!hasViewPermission}
            />
          </FieldWrapper>

          <FieldWrapper
            label="Notes"
            name="notes"
            className="mb-2"
            required={form.approval === ApprovalStatus.DECLINE}
          >
            <TextArea
              className="resize-none placeholder:text-sm"
              placeholder="Please provide any additional information or notes here...."
              disabled={
                !hasViewPermission || (form.approval === ApprovalStatus.APPROVE && !canApprove)
              }
            />
          </FieldWrapper>
        </fieldset>
      ) : (
        <div className="mb-4 rounded-lg border border-yellow-200 bg-yellow-50 p-4 text-sm text-yellow-700">
          You don't have permission to view this benefit type.
        </div>
      )}

      {form.approval === ApprovalStatus.APPROVE && hasViewPermission && (
        <>
          <fieldset className="mb-4 grid gap-4 rounded border border-gray-300 px-4 py-2 lg:grid-cols-2">
            <legend className="px-2 text-sm text-gray-400">Terms</legend>
            {preauth?.requestType === ADMISSION_REQUEST_TYPE &&
              preauth.preauthType !== PreAuthType.EMERGENCY_ADMISSION && (
                <>
                  <FieldWrapper
                    label="Number of days valid"
                    name="numberOfDaysValid"
                    className="mb-2"
                  >
                    <Input
                      type="number"
                      disabled={!canApprove}
                      options={{
                        min: {
                          value: 1,
                          message: "Minimum validity period is one day",
                        },
                      }}
                    />
                  </FieldWrapper>
                  <FieldWrapper
                    label="Allowed admission days"
                    name="allowedDaysOfAdmission"
                    className="mb-2"
                  >
                    <Input
                      type="number"
                      disabled={!canApprove}
                      options={{
                        min: {
                          value: 1,
                          message: "Minimum admission period is one day",
                        },
                      }}
                    />
                  </FieldWrapper>
                </>
              )}
            <FieldWrapper
              label="Amount"
              name="amount"
              className="mb-2"
              required={form.approval === ApprovalStatus.APPROVE}
            >
              <Input
                type="number"
                disabled={!canApprove || form.approval !== ApprovalStatus.APPROVE}
                options={{
                  min: {
                    value: 1,
                    message: "Amount must be greater than 0",
                  },
                  max: {
                    value: preauth?.requestAmount as number,
                    message: `Amount must be less than ${preauth?.requestAmount}`,
                  },
                }}
                step="0.01"
              />
            </FieldWrapper>
            {form.amount < (preauth?.requestAmount || 0) && (
              <FieldWrapper label="Reason" name="reason" className="mb-2" required>
                <TextArea
                  rows={4}
                  className="resize-none placeholder:text-sm"
                  placeholder="Please provide a reason for marking this pre-authorization as incomplete..."
                  disabled={!canApprove}
                />
              </FieldWrapper>
            )}
          </fieldset>

          <section className="mt-8 flex w-1/2 items-center gap-8">
            <div className="w-2/3">
              <p className="font-medium text-gray-700">
                Change benefit <em className="font-light">(If applicable)</em>
              </p>
              <p className="text-gray-500">Enable if you need to change the benefit</p>
            </div>

            <Toggle
              isOn={isChangBenefitEnabled}
              onChange={(isOn) => setIsChangBenefitEnabled(isOn)}
              disabled={!canApprove}
            />
          </section>

          {isChangBenefitEnabled && (
            <>
              {isBenefitsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingAnimation size={24} />
                </div>
              ) : benefitsError ? (
                <div className="flex flex-col items-center">
                  <ErrorMessage
                    title="Error loading member benefits"
                    message="Refresh the page to retry"
                  />
                </div>
              ) : !benefits?.length ? (
                <div className="flex items-center justify-center p-8">
                  <div className="flex flex-col items-center">
                    <Empty message="No valid benefits found." />
                  </div>
                </div>
              ) : (
                <fieldset className="mb-6 mt-8 grid grid-cols-2 gap-4 rounded border border-gray-300 px-4 py-2">
                  <legend className="px-1 text-sm text-gray-400">Terms</legend>

                  <FieldWrapper
                    label="Benefit"
                    name="benefitId"
                    className=" mb-16 text-sm font-medium"
                    descriptionClassName="text-xs my-1"
                    description="Change the benefit"
                    required
                  >
                    <Select
                      options={benefits.map((benefit) => ({
                        label: `${benefit.benefitName} - Ksh ${benefit.balance}`,
                        value: benefit.benefitId,
                      }))}
                      className="text-sm"
                      styles={{
                        menuList: (provided) => ({
                          ...provided,
                        }),
                      }}
                      placeholder="Search or select..."
                      isDisabled={!canApprove}
                    />
                  </FieldWrapper>

                  <FieldWrapper
                    label="Reason"
                    name="changeBenefitReason"
                    required
                    className="mb-0 text-sm font-medium"
                  >
                    <TextArea
                      rows={4}
                      className="resize-none placeholder:text-sm"
                      placeholder="Please provide a reason for the benefit change....."
                      disabled={!canApprove}
                    />
                  </FieldWrapper>
                </fieldset>
              )}
            </>
          )}
        </>
      )}

      {form.approval === ApprovalStatus.INCOMPLETE && (
        <fieldset className="mb-4 grid gap-4 rounded border border-gray-300 px-4 py-1 text-sm lg:grid-cols-2">
          <legend className="px-2 text-xs text-gray-400">Terms</legend>
          <FieldWrapper label="Reason" name="reason" className="mb-2 mt-4" required>
            <TextArea
              rows={4}
              className="resize-none placeholder:text-sm"
              placeholder="Please provide a reason for marking this pre-authorization as incomplete..."
              disabled={!canApprove}
            />
          </FieldWrapper>
        </fieldset>
      )}

      {!form.approval && (
        <section>
          <p className="mb-4 flex gap-2 font-medium">
            <span>Terms</span>
          </p>
          {preauth?.guidelines?.length ? (
            <div className="grid grid-cols-2 gap-4">
              <p className="">Description</p>
              <p className="">Cost</p>
              {preauth?.guidelines?.map((guideline) => (
                <Fragment key={guideline.id}>
                  <p className="text-[#6B7280]">{guideline.description}</p>
                  <p className="text-[#6B7280]">
                    <span className="text-[#6B7280]">KES </span>
                    <span className="">{guideline.cost || 0}</span>
                  </p>
                </Fragment>
              ))}
            </div>
          ) : (
            <p className="text-sm italic text-[#4B5563]">No guidelines available</p>
          )}

          {preauth.authorizationNotes ? (
            <section>
              <p className="mb-4 flex gap-2 ">
                <span>Notes</span>
              </p>
              <p className="text-sm text-[#4B5563]">{preauth.authorizationNotes}</p>
            </section>
          ) : (
            <p className="mt-4 text-sm italic text-[#4B5563]">No notes available</p>
          )}
        </section>
      )}
    </div>
  );
}
