import { SerializedError } from "@reduxjs/toolkit";
import { FetchBaseQueryError } from "@reduxjs/toolkit/query";
import { createContext, useContext } from "react";
import { FieldArrayWithId, UseFieldArrayAppend, UseFieldArrayRemove } from "react-hook-form";
import { BeneficiaryProviderBenefit, PreAuthorizationTopUp } from "~lib/api/types";
import { AuditLog } from "../../../api/types";
import { ApprovalStatus, Inputs } from "./pre-auth-approval-form.misc";
import { Preauthorization } from "../../../lib/types/care/preAuth";

type PreAuthApprovalFormState = {
  preauth: Preauthorization;
  preAuthLogs: AuditLog[];
  approvalStatusOptions: ApprovalStatus[];
  form: Inputs;
  isBenefitsLoading: boolean;
  benefitsError: FetchBaseQueryError | SerializedError | undefined;
  filteredBenefits: BeneficiaryProviderBenefit[];
  activeBenefit: BeneficiaryProviderBenefit | undefined;
  guidelinesError: string | undefined;
  guidelinesItemsError: "Invalid input" | null;
  fields: FieldArrayWithId<Inputs, "guidelines", "id">[];
  remove: UseFieldArrayRemove;
  append: UseFieldArrayAppend<Inputs, "guidelines">;
  undertakingTotalCost: number | undefined;
  shouldShowUpdateDisplay: boolean;
  lastMarkAsIncompleteLog: AuditLog | undefined;
  isLoadingLogs: boolean;
  shouldShowTopUpApprovalFields: boolean;
  latestTopUp: PreAuthorizationTopUp | undefined;
  pendingTopUp: PreAuthorizationTopUp | undefined;
  topUpAuthorizedLog: AuditLog | undefined;
  topUpRejectedLog: AuditLog | undefined;
  topUpRequestLog: AuditLog | undefined;
};

export const PreAuthApprovalFormContext = createContext<PreAuthApprovalFormState | null>(null);

export function usePreAuthApprovalForm() {
  const context = useContext(PreAuthApprovalFormContext);

  if (!context)
    throw new Error(
      "usePreAuthApprovalForm can only be used in a PreAuthApprovalFormContext provider ",
    );

  return context;
}
