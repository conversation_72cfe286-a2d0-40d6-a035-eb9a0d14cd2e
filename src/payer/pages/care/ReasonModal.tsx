import React, { useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

import { isError } from "util";
import { RootState } from "../../store";

interface Props {
  submitReason: (reason: string) => void;
}

export const ReasonModal: React.FC<Props> = (props) => {
  const setOtp = useState(0);
  const navigate = useNavigate();
  const [reason, setReason] = useState("");

  const [error, setError] = useState(false);
  const [touched, setTouched] = useState<any>({});

  const handleReasonChange = (e) => {
    setReason(e.target.value);
  };

  const submitReason = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e.preventDefault();
    props.submitReason(reason);
  };

  //const otpSent = useSelector((state: RootState) => state.dashboard.otpSent);

  return (
    <div className="min-w-2 bg-white p-4">
      <form className={`m-10 mb-0 flex flex-col items-center justify-center`}>
        <div className={` items center mx-auto mt-8 space-x-4`}>
          <label className="mb-2 flex items-center p-5">
            <span className="text-md ml-2 font-medium">Reason For Reimbursement:</span>
          </label>
          <textarea
            id="feedback"
            className="form-textarea w-full px-2 py-1 "
            rows={5}
            cols={70}
            required
            value={reason}
            onChange={(e) => setReason(e.target.value)}
          ></textarea>
        </div>
        <div className={`mt-10 justify-items-end `}>
          <button
            className={`mb-2  rounded px-10 py-3 text-gray-50 ${
              reason === "" ? "bg-gray-500" : "bg-light-blue-900"
            } `}
            onClick={(e) => {
              e.stopPropagation();
              submitReason(e);
            }}
            disabled={reason === "" ? true : false}
          >
            Submit
          </button>
        </div>
      </form>
    </div>
  );
};
