import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router";
import ModalBasic from "../../components/ModalBasic";
import ModalMedium from "../../components/ModalMedium";
import { PreAuthPage } from "../../components/PreAuthPage";
import UserService from "../../services/UserService";
import { RootState } from "../../store";

interface Props {
  visit?: any;
  setIsPreauthOpen?: any;
}

export const VisitPreAuthPageItem: React.FC<Props> = (props) => {
  const [payerEditModalOpen, setEditPayerModalOpen] = useState(false);
  const navigate = useNavigate();

  const [open, setOpen] = useState(false);
  const [emailPresent, setEmailPresent] = useState(false);
  const dispatch = useDispatch();
  const [isPreauthOpen, setIsPreauthOpen] = useState(false);
  const [visitState, setVisitState] = useState({});
  const [notes, setNotes] = useState("");
  const [amount, setAmount] = useState(0);

  const userObjState: any = useSelector(
    (state: RootState) => state.dashboard.userObj
  );

  const handleMemberClick = (e: React.MouseEvent, visit: any) => {
    const hospitalProviderId = userObjState?.hospitalProviderId;
    setIsPreauthOpen(true);
    setVisitState(visit);
  };

  const handleSubmitPreAuth = () => {
    /// dispatch(startPreAuth(preAuthObj));
  };

  return (
    <>
      <tbody
        className="divide-y-0 even:bg-light-blue-50 
    hover:bg-light-blue-100 hover:text-sky-700 scale-10 delay-150 "
      >
        <tr className="">
          <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
            <div className="flex items-center text-slate-800">
              {" "}
              <div className="font-medium text-slate-800 text-center items-center px-2.5 ">
                {props.visit.visitNumber}
              </div>
            </div>
          </td>

          <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
            <div className="flex items-center text-slate-800">
              {" "}
              <div className="font-medium text-slate-800 text-center items-center px-2.5 ">
                {props.visit.reference}
              </div>
            </div>
          </td>
          <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
            <div className="flex items-center text-slate-800">
              {" "}
              <div className="font-medium text-slate-800 text-center items-center px-2.5 ">
                {props.visit.schemeName}
              </div>
            </div>
          </td>
          <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
            <div className="flex items-center text-slate-800">
              {" "}
              <div className="font-medium text-slate-800 text-center items-center px-2.5 ">
                {props.visit.requestType}
              </div>
            </div>
          </td>
          <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
            <div className="flex items-center text-slate-800">
              {" "}
              <div className="font-medium text-slate-800 text-center items-center px-2.5 ">
                {props.visit.service}
              </div>
            </div>
          </td>
          <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
            <div className=" font-medium rounded-full text-left px-2.5 py-0.5 uppercase">
              {props.visit.memberNumber}
            </div>
          </td>
          <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
            <div className=" font-medium rounded-full text-left px-2.5 py-0.5 uppercase">
              {props.visit.memberName}
            </div>
          </td>
          <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
            <div
              className=" font-medium rounded-full text-left px-2.5 py-0.5 sm:w-64 uppercase truncate  ..."
              title={props.visit.benefitName}
            >
              {props.visit.benefitName}
            </div>
          </td>
          <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
            <div
              className=" font-medium rounded-full text-left px-2.5 py-0.5 sm:w-64 uppercase truncate  ..."
              title={props.visit.status}
            >
              {props.visit.status}
            </div>
          </td>
          <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap w-px">
            <div className="flex items-center justify-evenly">
              <button
                onClick={(e) => {
                  handleMemberClick(e, props.visit);
                }}
                className="mr-1 bg-transparent hover:bg-blue-700 text-blue-700 
              font-semibold hover:text-white py-1 px-2 border border-blue-500 hover:border-transparent rounded "
                title="Proceed To Bill Client"
              >
                Review
              </button>
            </div>
          </td>
        </tr>
      </tbody>
      <ModalBasic
        id="preauth-modal"
        modalOpen={isPreauthOpen}
        setModalOpen={setIsPreauthOpen}
        onClose={() => setIsPreauthOpen(false)}
        title="Pre-Authorization Details"
      >
        <PreAuthPage
          visit={visitState}
          submitPreAuth={() => handleSubmitPreAuth()}
          setModalOpen={() => setIsPreauthOpen(false)}
        />
      </ModalBasic>
    </>
  );
};
export default VisitPreAuthPageItem;
