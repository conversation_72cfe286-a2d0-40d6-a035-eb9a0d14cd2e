import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store";
import { setSelectedBenefit } from "../../store/payers/actions";

export default function BenefitResult({ family, value }) {
  console.log(family);
  const dispatch = useDispatch();
  const [showItems, setShowItems] = useState(true);

  // const benefitItem = useSelector(
  //   (state: RootState) => state.payers.selectedBenefit
  // );
  const setBenefit = (e) => {
    console.log(e);
    dispatch(setSelectedBenefit(e));
    setShowItems(false);
  };
  useEffect(() => {
    if (value.length < 1) {
      setShowItems(true);
    }
  });
  return (
    <div className="absolute">
      <ul>
        {family.map((el, i) => (
          <li
            key={i}
            className="p-3 border-2 bg-gray-100 cursor-pointer w-52"
            style={{ display: showItems ? "block" : "none" }}
            onClick={(e) => {
              e.stopPropagation();
              setBenefit(el);
            }}
          >
            {el.name}
          </li>
        ))}
      </ul>
    </div>
  );
}
