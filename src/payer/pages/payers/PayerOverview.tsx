import React, { useEffect, useState } from "react";

import Pagination from "../../components/Pagination";
import ModalBasic from "../../components/ModalBasic";
import PayersTableOverview from "../../pages/partials/pageItems/PayersOverviewTable";
import MappingTableOverview from "../../pages/partials/pageItems/MappingTable";
import { useSelector } from "react-redux";
import { RootState } from "../../store";
import BenefitResult from "./BenefitResult";
import useFetch from "../../lib/useFetch";
import {
  getPayerMappedBenefits,
  getPayerMappedProviders,
  mapBenefit,
  mapProvider,
} from "../../store/payers/actions";
import { connect, useDispatch } from "react-redux";
import MappingTableProviders from "../partials/pageItems/MappingTableProviders";
import { getCatalogProviders } from "../../store/catalog/actions";
import ModalMedium from "../../components/ModalMedium";
import { AsyncPaginate } from "react-select-async-paginate";
import { loadProviderOptions } from "../../lib/loadOptions";

function PayerOverview({
  mapBenefit,
  mapProvider,
  getPayerMappedBenefits,
  getPayerMappedProviders,
}) {
  const [payerEditModalOpen, setEditPayerModalOpen] = useState(false);
  const [providerMappingModalOpen, setProviderMappingModalOpen] = useState(false);
  const [benefitMappingModalOpen, setBenefitMappingModalOpen] = useState(false);

  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [openTab, setOpenTab] = React.useState(1);
  const [color, setColor] = React.useState("blue");

  const [payerType, setPayerType] = useState("");
  const [payerName, setPayerName] = useState("");

  const [underwriter, setUnderwriter] = useState(false);
  const [corporate, setCorporate] = useState(false);
  const [administrator, setAdministrator] = useState(false);
  const [searchloading, setSearchLoading] = useState(false);

  const [mobile, setMobile] = useState("");
  const [email, setEmail] = useState("");

  const [selectedItems, setSelectedItems] = useState([]);
  const [payerModalOpen, setPayerModalOpen] = useState(false);
  const [inputCode, setInputCode] = useState("");
  const [inputCodePayer, setInputCodePayer] = useState("");
  const { data, setData } = useFetch();
  const [refreshKey, setRefreshKey] = useState(0);
  const [refreshProviderMappingKey, setRefreshProviderMappingKey] = useState(0);
  const [providerSelected, setProviderSelected] = useState("");

  const [provider, setProvider] = useState(false);
  const [providerValue, setProviderValue] = useState({});

  const handleSelectedItems = (selectedItems) => {
    setSelectedItems([...selectedItems]);
  };
  const selectedBenefit = useSelector((state: RootState) => state.payers.selectedBenefit);
  const payerItem = useSelector((state: RootState) => state.payers.payerItem);
  const payerMappedBenefits = useSelector((state: RootState) => state.payers.payerMappedBenefits);
  const payerMappedProviders = useSelector((state: RootState) => state.payers.payerMappedProviders);
  const totalElements = useSelector((state: RootState) => state.payers.totalElements);
  const totalPages = useSelector((state: RootState) => state.payers.totalPages);
  const pageNumber = useSelector((state: RootState) => state.payers.pageNumber);

  // provider mapping pagination
  const totalProvMappingElements = useSelector(
    (state: RootState) => state.payers.totalPayerMappedElements,
  );
  const totalProvMappingPages = useSelector(
    (state: RootState) => state.payers.totalPayerMappedPages,
  );
  const pageProvMappingNumber = useSelector(
    (state: RootState) => state.payers.pagePayerMappedNumber,
  );
  const userObjState: any = useSelector((state: RootState) => state.payers.userObj);

  console.log(totalProvMappingElements);
  //

  const searchBenefit = (e) => {
    setSearchLoading(true);
    setData({ ...data, slug: e.target.value });
  };

  useEffect(() => {
    getPayerMappedBenefits(userObjState.payerId).then(() => {
      setRefreshKey((oldKey) => oldKey + 1);
    });
    getPayerMappedProviders(userObjState.payerId).then(() => {
      setRefreshProviderMappingKey((oldKey) => oldKey + 1);
    });
  }, [payerItem.id]);

  useEffect(() => {
    setSearchLoading(false);
    setData({ ...data, slug: selectedBenefit.name });
    if (payerItem) {
      if (payerItem?.type?.toLocaleLowerCase().toString() == "underwriter") {
        setUnderwriter(true);
      } else if (payerItem?.type?.toLocaleLowerCase().toString() == "corporate") {
        setCorporate(true);
      } else if (payerItem?.type?.toLocaleLowerCase().toString() == "intermediary") {
        setAdministrator(true);
      }
    }
  }, [selectedBenefit]);

  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(getCatalogProviders());
  }, []);
  const catelogProviders = useSelector((state: RootState) => state.catalog.catalogProviders);
  const handleFacilityChange = (e) => {
    setProviderSelected(e.target.value);
  };

  const handleBenefitMappingSubmit = () => {
    mapBenefit({
      payerId: Number(payerItem.id),
      benefitCatalogId: selectedBenefit.id,
      code: inputCode,
    }).then(() => {
      getPayerMappedBenefits(payerItem.id).then(() => {
        setRefreshKey((oldKey) => oldKey + 1);
        setBenefitMappingModalOpen(false);
      });
    });
  };
  const handleProviderMappingSubmit = () => {
    setProviderMappingModalOpen(false);
    mapProvider({
      payerId: Number(payerItem.id),
      providerId: Number(providerValue),
      code: inputCodePayer,
    }).then(() => {
      getPayerMappedProviders(payerItem.id).then(() => {
        setRefreshProviderMappingKey((oldKey) => oldKey + 1);
      });
    });
  };

  const handlePaginationChangeProvider = (e) => {
    console.log(e);
    getPayerMappedProviders(payerItem.id, e).then(() => {
      setRefreshProviderMappingKey((oldKey) => oldKey + 1);
    });
  };

  const changeProviderName = (e) => {
    if (e) {
      setProvider(e);
      console.log(e);
      console.log(e["value"]);
      setProviderValue(e["value"]);
    } else {
      setProvider(e);
    }
  };

  return (
    <div className="flex h-full overflow-hidden">
      {/* Content area */}
      <div className="relative flex flex-1 flex-col overflow-y-auto overflow-x-hidden">
        <main className="mx-4 mb-4 lg:mx-8 lg:mb-8">
          <div className="mx-auto w-full max-w-9xl  rounded-md px-4 shadow-lg sm:px-6 lg:px-8">
            {/* Page header */}
            <div className="mb-5 sm:flex sm:items-center sm:justify-between">
              {/* Left: Title */}
              <div className="mb-4 sm:mb-0">
                <h1 className="text-2xl font-bold text-gray-800 md:text-3xl"></h1>
                <h3 className="mt-1 text-xl font-bold text-gray-800 md:text-2xl">Payer Overview</h3>
              </div>
            </div>

            {/* More actions */}
            <div className="mb-2 sm:flex sm:items-center sm:justify-between"></div>
            {/* Modal */}

            {/* Table */}
            <PayersTableOverview selectedItems={handleSelectedItems} payerItem={payerItem} />
            {/* buttons
             */}
            {/* <button
              className="btn w-full sm:w-40  border-blue-500 hover:border-gray-300 text-gray-600"
              onClick={(e) => {
                e.stopPropagation();
                setEditPayerModalOpen(true);
              }}
            >
              <span className="ml-2">Edit Profile</span>
            </button> */}

            <div className="">
              <div className="mt-15 flex flex-wrap">
                <div className="w-full">
                  <ul className="mb-0 flex list-none flex-row flex-wrap pb-4 pt-3" role="tablist">
                    <li
                      className={
                        "-mb-px mr-2 flex-auto text-center last:mr-0" +
                        (openTab === 1
                          ? " border-  border-b-4" + color + "-600"
                          : " border-b-gray-600")
                      }
                    >
                      <a
                        className={
                          "md:text-md block rounded px-5 py-3 text-sm font-bold leading-normal shadow-lg lg:text-lg " +
                          (openTab === 1
                            ? "text-" + color + "-600" + " border-b-" + color + "-600"
                            : "text-gray-700")
                        }
                        onClick={(e) => {
                          e.preventDefault();
                          setOpenTab(1);
                        }}
                        data-toggle="tab"
                        href="#link1"
                        role="tablist"
                      >
                        <i className="fas fa-space-shuttle mr-1 text-sm md:text-base lg:text-lg"></i>{" "}
                        Benefit Mapping
                      </a>
                    </li>
                    <li
                      className={
                        "-mb-px mr-2 flex-auto text-center last:mr-0" +
                        (openTab === 2
                          ? " border-  border-b-4" + color + "-600"
                          : " border-b-gray-600")
                      }
                    >
                      <a
                        className={
                          "md:text-md block rounded px-5  py-3 text-sm font-bold leading-normal shadow-lg lg:text-lg " +
                          (openTab === 2
                            ? "text-" + color + "-600" + " border-b-" + color + "-600"
                            : "text-gray-700")
                        }
                        onClick={(e) => {
                          e.preventDefault();
                          setOpenTab(2);
                        }}
                        data-toggle="tab"
                        href="#link2"
                        role="tablist"
                      >
                        <i className="fas fa-cog mr-1 text-base"></i> Provider Mapping
                      </a>
                    </li>
                  </ul>
                  <div className="relative mb-6 flex w-full min-w-0 flex-col break-words rounded  bg-white">
                    <div className="flex-auto px-4 py-1">
                      <div className="tab-content tab-space">
                        <div className={openTab === 1 ? "block" : "hidden"} id="link1">
                          <div className="flex justify-end ">
                            {/* <button
                              className="btn ml-5 bg-blue-700 border-gray-200 hover:border-gray-300 "
                              onClick={(e) => {
                                e.stopPropagation();
                                setBenefitMappingModalOpen(true);
                              }}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-4 w-6 text-white"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                                />
                              </svg>

                              <span className="ml-2 text-white ">
                                {" "}
                                New Benefit Mapping
                              </span>
                            </button> */}
                          </div>
                          {/* Table */}
                          <MappingTableOverview
                            selectedItems={handleSelectedItems}
                            payerMappedBenefits={payerMappedBenefits}
                            key={refreshKey}
                          />
                          <div className="mt-8">
                            <Pagination
                              totalElements={totalElements}
                              totalPages={totalPages}
                              pageNumber={pageNumber}
                              OnPageNumberClick=""
                            />
                          </div>
                        </div>
                        <div className={openTab === 2 ? "block" : "hidden"} id="link2">
                          <div className="flex justify-end ">
                            {/* <button
                              className="btn ml-5 bg-blue-700 border-gray-200 hover:border-gray-300 "
                              onClick={(e) => {
                                e.stopPropagation();
                                setProviderMappingModalOpen(true);
                              }}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-4 w-6 text-white"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                                />
                              </svg>
                              <span className="ml-2 text-white">
                                New Provider Mapping
                              </span>
                            </button> */}
                          </div>
                          {/* Table */}
                          <MappingTableProviders
                            selectedItems={handleSelectedItems}
                            payerMappedProviders={payerMappedProviders}
                            key={refreshProviderMappingKey}
                          />
                          <div className="mt-8">
                            <Pagination
                              totalElements={totalProvMappingElements}
                              totalPages={totalProvMappingPages}
                              pageNumber={pageProvMappingNumber}
                              OnPageNumberClick={handlePaginationChangeProvider}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="space-x-1">
              <div>
                <ModalBasic
                  id="feedback-modal"
                  modalOpen={payerEditModalOpen}
                  setModalOpen={setEditPayerModalOpen}
                  title="Edit Payer"
                >
                  {/* Modal content */}
                  <div className="px-10 pb-1 pt-4">
                    <h3 className="md:text-md mb-5 text-sm text-gray-400 lg:text-lg">
                      EDIT PAYER DETAILS
                    </h3>

                    <div className="text-sm">
                      {/* Options */}
                      <div className="sm:flex sm:items-center sm:justify-center">
                        <div className="mr-9 w-64">
                          <label className="mb-1 block text-sm font-medium" htmlFor="name">
                            Payer Name
                          </label>
                          <input
                            id="name"
                            className="form-input w-full"
                            type="text"
                            placeholder="Payer Name"
                            value={payerItem.name}
                            onChange={(e) => setPayerName(e.target.value)}
                          />
                        </div>
                        <div className="mr-9 w-64">
                          <label className="mb-1 block text-sm font-medium" htmlFor="email">
                            Email Address
                          </label>
                          <input
                            id="email"
                            className="form-input w-full "
                            type="email"
                            placeholder="Email Address"
                            value={payerItem.email}
                            onChange={(e) => setEmail(e.target.value)}
                          />
                        </div>
                        <div className="mr-9 w-64">
                          <label className="mb-1 block text-sm font-medium" htmlFor="name">
                            Mobile
                          </label>
                          <input
                            id="name"
                            className="form-input w-full"
                            type="phone"
                            placeholder="Mobile"
                            value={payerItem.contact}
                            onChange={(e) => setMobile(e.target.value)}
                          />
                        </div>
                      </div>
                      <div className="mt-10">
                        <h3 className="md:text-md mb-5 text-sm text-gray-400 lg:text-lg">
                          PAYER TYPE
                        </h3>
                      </div>

                      <div className="mt-5 sm:flex sm:items-center sm:justify-center">
                        <div className="mt-3 sm:mr-5  sm:w-48">
                          <input
                            type="radio"
                            name="option"
                            id="option44"
                            className="hidden p-20"
                            value="Underwriter"
                            onChange={(e) => setPayerType(e.target.value)}
                          />
                          <label
                            htmlFor="option44"
                            className="inline-block cursor-pointer  rounded-md border-2 p-5 px-16 py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                          >
                            Underwriter
                          </label>
                        </div>
                        <div className="mt-3 sm:mr-5  sm:w-48">
                          <input
                            type="radio"
                            name="option"
                            id="option55"
                            className="hidden"
                            value="Corporate"
                            onChange={(e) => setPayerType(e.target.value)}
                          />
                          <label
                            htmlFor="option55"
                            className="inline-block cursor-pointer rounded-md border-2 px-16  py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                          >
                            Corporate
                          </label>
                        </div>
                        <div className="mt-3 sm:mr-5  sm:w-48">
                          <input
                            type="radio"
                            name="option"
                            id="option66"
                            className="hidden"
                            value="Intermediary"
                            onChange={(e) => setPayerType(e.target.value)}
                          />
                          <label
                            htmlFor="option66"
                            className="inline-block cursor-pointer rounded-md border-2 px-16  py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                          >
                            Administrator
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* Modal footer */}
                  <div className="mt-5 px-5 py-4">
                    <div className="flex flex-wrap justify-center space-x-2">
                      <button
                        className="btn m-5 w-full border-gray-200 text-gray-600 hover:border-gray-300 sm:w-44 sm:px-16 sm:py-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          setEditPayerModalOpen(false);
                        }}
                      >
                        Clear
                      </button>
                      <button className="btn m-5 w-full bg-blue-500 text-white hover:bg-blue-600 sm:w-44 sm:px-16 sm:py-2">
                        Save
                      </button>
                    </div>
                  </div>
                </ModalBasic>
              </div>
            </div>

            {/* provider mapping modal */}
            <div className="space-x-1">
              <div>
                <ModalBasic
                  id="feedback-modal"
                  modalOpen={providerMappingModalOpen}
                  setModalOpen={setProviderMappingModalOpen}
                  title="Payer Mapping"
                >
                  {/* Modal content */}
                  <div className="justify-center px-10 pb-1 pt-4">
                    <h3 className="uppercase text-gray-400 sm:flex">Provider Mapping</h3>

                    <div className="text-sm">
                      {/* Options */}
                      <div className="sm:flex sm:items-center sm:justify-center">
                        <div className="mr-9 w-full">
                          <label className="mb-1 block text-sm font-medium" htmlFor="name">
                            Input Code
                          </label>
                          <input
                            id="name"
                            className="form-input w-full"
                            type="text"
                            placeholder="Input Code"
                            value={inputCodePayer}
                            onChange={(e) => setInputCodePayer(e.target.value)}
                          />
                        </div>
                        <div className="mr-9 w-full">
                          <label className="mb-1 block text-sm font-medium" htmlFor="providername">
                            Select Provider
                          </label>
                          <AsyncPaginate
                            className="react-select"
                            classNamePrefix="react-select"
                            name="providername"
                            value={provider}
                            loadOptions={loadProviderOptions}
                            onChange={(value) => changeProviderName(value)}
                            placeholder="Select Provider Name"
                            isClearable={true}
                          />
                          {/* <select
                            name="Providers"
                            id="Providers"
                            onChange={handleFacilityChange}
                            className="btn w-full justify-between bg-white  border-gray-200 hover:border-gray-300 text-gray-500 hover:text-gray-600"
                          >
                            {catelogProviders.map((provider) => (
                              <option value={provider.id}>
                                {provider.name}
                              </option>
                            ))}
                          </select> */}
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* Modal footer */}
                  <div className="mt-10 px-5 py-4">
                    <div className="flex flex-wrap justify-center space-x-2">
                      <button
                        className="btn m-5 w-full border-gray-200 text-gray-600 hover:border-gray-300 sm:w-44 sm:px-16 sm:py-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          setProviderMappingModalOpen(false);
                        }}
                      >
                        Clear
                      </button>
                      <button
                        className="btn m-5 w-full bg-blue-500 text-white hover:bg-blue-600 sm:w-44 sm:px-16 sm:py-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleProviderMappingSubmit();
                        }}
                      >
                        Save
                      </button>
                    </div>
                  </div>
                </ModalBasic>
              </div>
            </div>

            {/* Benefit mapping modal */}
            <div className="space-x-1">
              <div>
                <ModalMedium
                  id="feedback-modal"
                  modalOpen={benefitMappingModalOpen}
                  setModalOpen={setBenefitMappingModalOpen}
                  title="Payer Benefit Mapping"
                >
                  {/* Modal content */}
                  <div className="px-10 pb-1 pt-4">
                    <div className="md:text-md mb-5 flex justify-center text-sm uppercase text-gray-400 lg:text-lg">
                      <h3>Benefit Mapping</h3>
                    </div>

                    <div className="text-sm">
                      {/* Options */}
                      <div className="sm:items-center sm:justify-center">
                        <div className="w-full">
                          <label className="mb-1 block text-sm font-medium" htmlFor="country">
                            Search & Select Benefit
                            {searchloading ? (
                              <svg
                                role="status"
                                className="mx-5 mr-2 inline h-6 w-6 animate-spin fill-blue-600 text-gray-200 dark:text-gray-600"
                                viewBox="0 0 100 101"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                  fill="currentColor"
                                />
                                <path
                                  d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                  fill="blue"
                                />
                              </svg>
                            ) : (
                              ""
                            )}
                          </label>
                          <input
                            type="text"
                            className="form-input w-full"
                            placeholder="Search Benefit"
                            value={data.slug}
                            onChange={(e) => searchBenefit(e)}
                          ></input>
                          <br />
                          {data.results.length > 0 ? (
                            <BenefitResult family={data.results} value={data.slug} />
                          ) : null}
                        </div>
                      </div>
                      <div className="flex">
                        <div className="mt-10 w-full ">
                          <label className="mb-1 block text-sm font-medium" htmlFor="name">
                            Payer Benefit Code
                          </label>
                          <input
                            id="name"
                            className="form-input w-full"
                            type="text"
                            value={inputCode}
                            placeholder="Benfit Code"
                            onChange={(e) => setInputCode(e.target.value)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* Modal footer */}
                  <div className="mt-10 px-5 py-4">
                    <div className="flex flex-wrap justify-center space-x-2">
                      <button
                        className="btn m-5 w-full border-gray-200 text-gray-600 hover:border-gray-300 sm:w-44 sm:px-16 sm:py-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          setBenefitMappingModalOpen(false);
                        }}
                      >
                        Clear
                      </button>
                      <button
                        className="btn m-5 w-full bg-blue-500  text-white hover:bg-blue-600 sm:w-44 sm:px-16 sm:py-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleBenefitMappingSubmit();
                        }}
                      >
                        Save
                      </button>
                    </div>
                  </div>
                </ModalMedium>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
const mapStateToProps = (state) => ({
  success: state.payers.success,
  payers: state.payers.payers,
  loading: state.payers.loading,
});
const mapDispatchToProps = (dispatch) => ({
  mapBenefit: (benefitObject) => dispatch(mapBenefit(benefitObject)),
  mapProvider: (providerObject) => dispatch(mapProvider(providerObject)),
  getPayerMappedBenefits: (payerId) => dispatch(getPayerMappedBenefits(payerId)),
  getPayerMappedProviders: (payerId, page) => dispatch(getPayerMappedProviders(payerId, page)),
});
export default connect(mapStateToProps, mapDispatchToProps)(PayerOverview);
