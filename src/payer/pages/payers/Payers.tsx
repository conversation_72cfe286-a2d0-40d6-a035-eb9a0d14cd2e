import { useState, useEffect } from "react";
import { connect, useSelector } from "react-redux";
import ModalBasic from "../../components/ModalBasic";
import PayersTable from "../../pages/partials/pageItems/PayersTable";
import { addPayer, getPayers, getPayersByType } from "../../store/payers/actions";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { RootState } from "../../store";
import { TailSpin } from "react-loader-spinner";
import * as notifications from "../../lib/notifications.js";

const Payers = ({ addPayer, getPayers, getPayersByType, payers }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [payerModalOpen, setPayerModalOpen] = useState(false);
  const [payerType, setPayerType] = useState("");
  const [payerName, setPayerName] = useState("");
  const [mobile, setMobile] = useState("");
  const [email, setEmail] = useState("");
  const [payerSubmitted, setPayerSubmitted] = useState(false);
  const [selectPayerType, setSelectPayerType] = useState("");

  const [refreshKey, setRefreshKey] = useState(0);

  const success = useSelector((state: RootState) => state.payers.success);
  const msg = useSelector((state: RootState) => state.payers.msg);
  const loadingPayers = useSelector((state: RootState) => state.payers.loading);

  useEffect(() => {
    getPayers().then(() => {
      setRefreshKey((oldKey) => oldKey + 1);
    });
  }, []);

  const totalElements = useSelector((state: RootState) => state.payers.totalElements);
  const totalPages = useSelector((state: RootState) => state.payers.totalPages);
  const pageNumber = useSelector((state: RootState) => state.payers.pageNumber);

  const criteria = ["UNDERWRITER", "CORPORATE", "INTERMEDIARY"];

  const handlePayerTypeChange = (e) => {
    const index = e.nativeEvent.target.selectedIndex;
    const text = e.nativeEvent.target[index].text;
    const searchText = text === "ADMINISTRATOR" ? "INTERMEDIARY" : text;
    const stringPayerType = JSON.stringify(text).toLocaleLowerCase();

    getPayersByType(searchText).then(() => {
      setRefreshKey((oldKey) => oldKey + 1);
    });
  };

  const handlePaginationChange = (e) => {
    console.log(e);
    getPayers().then(() => {
      setRefreshKey((oldKey) => oldKey + 1);
    });
  };

  useEffect(() => {
    if (payerSubmitted)
      if (success) {
        setPayerSubmitted(false);
        toast.success("Payer Added Successfully!", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
      } else {
        setPayerSubmitted(false);
        toast.error("An Error Occurred!", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        notifications.Error({
          title: msg,
        });
      }
  }, [payerSubmitted]);

  const handleSubmit = (event) => {
    event.preventDefault();

    if (!mobile || !payerName || !payerType) {
      toast.error("All Form Fields are Required!", {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      return;
    }

    addPayer({
      contact: mobile,
      name: payerName,
      type: payerType.toUpperCase(),
    }).then(() => {
      setPayerSubmitted(true);
      setPayerModalOpen(false);
      getPayers().then(() => {
        setRefreshKey((oldKey) => oldKey + 1);
      });
      clearFormFields();
    });
  };

  const clearFormFields = () => {
    setMobile("");
    setPayerName("");
    setPayerType("");
  };

  return (
    <div className="flex h-full overflow-hidden">
      {/* Content area */}
      <div className="relative flex flex-1 flex-col overflow-y-auto overflow-x-hidden ">
        <main className="mx-4 mb-4 lg:mx-8 lg:mb-8">
          <div className="mx-auto rounded-md px-4 py-2 pb-10 shadow-lg sm:px-6 lg:px-8">
            {/* Page header */}
            <div className="mb-5 sm:flex sm:items-center sm:justify-between">
              {/* Left: Title */}
              <div className="mb-0 sm:mb-0">
                <h1 className="md:text-md text-sm font-bold text-gray-800 lg:text-lg">Payers</h1>
                <h5 className="lg:taxt-lg text-sm font-normal text-gray-400 md:text-base">
                  Add, search or select a payer to proceed
                </h5>
              </div>
            </div>

            {/* More actions */}
            <div className="sm:flex sm:items-center sm:justify-between">
              {/* Left side */}
              <div className="mb-4 flex sm:mb-0">
                <div className=" mb-5">
                  <select
                    onChange={handlePayerTypeChange}
                    name="country"
                    className="btn md:text-md min-w-44 justify-between border-gray-200 bg-white text-sm text-gray-500 hover:border-gray-300 hover:text-gray-600"
                  >
                    <option value="Select Country" selected disabled>
                      Select Payer Type
                    </option>
                    {criteria.map((criterion) => (
                      <option
                        className="md:text-md flex w-full cursor-pointer items-center px-3 py-1 text-sm font-medium text-gray-600  hover:bg-gray-50"
                        value={criterion}
                      >
                        {criterion === "INTERMEDIARY" ? "ADMINISTRATOR" : criterion}
                      </option>
                    ))}
                  </select>
                </div>
                <div></div>
              </div>

              {/* Right side */}
              <div className="grid grid-flow-col justify-start gap-2 sm:auto-cols-max sm:justify-end">
                <button
                  className="btn bg-blue-500 text-white hover:bg-blue-600"
                  onClick={(e) => {
                    e.stopPropagation();
                    setPayerModalOpen(true);
                  }}
                >
                  <svg className="h-4 w-4 shrink-0 fill-current" viewBox="0 0 16 16">
                    <path d="M15 7H9V1c0-.6-.4-1-1-1S7 .4 7 1v6H1c-.6 0-1 .4-1 1s.4 1 1 1h6v6c0 .6.4 1 1 1s1-.4 1-1V9h6c.6 0 1-.4 1-1s-.4-1-1-1z" />
                  </svg>
                  <span className="xs:block ml-2 hidden">Add Payer</span>
                </button>
              </div>
            </div>
            {/* Modal */}
            <ModalBasic
              id="feedback-modal"
              modalOpen={payerModalOpen}
              setModalOpen={setPayerModalOpen}
              title="Add Payer"
            >
              {/* Modal content */}

              <div className="px-10 pb-1 pt-4">
                <h3 className="md:text-md mb-5 text-sm text-gray-400 lg:text-lg">PAYER DETAILS</h3>

                <div className="text-sm">
                  {/* Options */}
                  <div className="sm:flex sm:items-center sm:justify-center">
                    <div className="mr-9 w-64">
                      <label className="mb-1 block text-sm font-medium" htmlFor="name">
                        Payer Name
                      </label>
                      <input
                        id="name"
                        className="form-input w-full"
                        type="text"
                        placeholder="Payer Name"
                        value={payerName}
                        onChange={(e) => setPayerName(e.target.value)}
                      />
                    </div>
                    {/* <div className="w-64 mr-9">
                      <label
                        className="block text-sm font-medium mb-1"
                        htmlFor="email"
                      >
                        Email Address
                      </label>
                      <input
                        id="email"
                        className="form-input w-full "
                        type="email"
                        placeholder="Email Address"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                      />
                    </div> */}
                    <div className="mr-9 w-64">
                      <label className="mb-1 block text-sm font-medium" htmlFor="name">
                        Mobile
                      </label>
                      <input
                        id="name"
                        className="form-input w-full"
                        type="number"
                        placeholder="Phone number e.g 07##"
                        value={mobile}
                        onChange={(e) => setMobile(e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="mt-10">
                    <h3 className="md:text-md mb-5 text-sm text-gray-400 lg:text-lg">PAYER TYPE</h3>
                  </div>

                  <div className="sm:flex sm:items-center sm:justify-center">
                    <div className="mt-3 w-full sm:mr-5 sm:w-48">
                      <input
                        type="radio"
                        name="option"
                        id="option1"
                        className="hidden"
                        value="Underwriter"
                        onChange={(e) => setPayerType(e.target.value)}
                      />
                      <label
                        htmlFor="option1"
                        className="rounded-md border-2 px-16 py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                      >
                        Underwriter
                      </label>
                    </div>
                    <div className="mt-3 w-full sm:mr-5  sm:w-48">
                      <input
                        type="radio"
                        name="option"
                        id="option2"
                        className="hidden"
                        value="Corporate"
                        onChange={(e) => setPayerType(e.target.value)}
                      />
                      <label
                        htmlFor="option2"
                        className="rounded-md border-2 px-16  py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                      >
                        Corporate
                      </label>
                    </div>
                    <div className="mt-3 w-full sm:mr-5 sm:w-48">
                      <input
                        type="radio"
                        name="option"
                        id="option3"
                        className="hidden"
                        value="Intermediary"
                        onChange={(e) => setPayerType(e.target.value)}
                      />
                      <label
                        htmlFor="option3"
                        className="rounded-md border-2 px-16  py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                      >
                        Administrator
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              {/* Modal footer */}

              <div className="m-5 mb-10 sm:m-5 sm:flex sm:items-center sm:justify-center sm:px-5 sm:py-4">
                <button
                  className="btn m-5 w-full border-gray-200 text-gray-600 hover:border-gray-300 sm:w-44 sm:px-16 sm:py-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    setPayerModalOpen(false);
                  }}
                >
                  Clear
                </button>
                <button
                  type="submit"
                  className=" btn m-5 w-full bg-blue-500 text-white hover:bg-blue-600  sm:w-44 sm:px-16 sm:py-2"
                  onClick={(e) => {
                    handleSubmit(e);
                  }}
                >
                  Save
                </button>
              </div>
            </ModalBasic>

            {/* Table */}
            {loadingPayers === true ? (
              <TailSpin height="30" width="50" color="#2193FF" ariaLabel="loading" />
            ) : (
              <PayersTable payerslist={payers} key={refreshKey} />
            )}

            {/* No elements exist */}
            {/* <div className="flex justify-center font-sans text-sm md:text-base lg:text-lg mr-6 pt-5 font-normal">
              {totalElements < 1 ? <div>No Records Exist</div> : ""}
            </div> */}
            {/* Pagination */}
            {/* <div className="mt-8">
              <Pagination
                totalElements={totalElements}
                totalPages={totalPages}
                pageNumber={pageNumber}
                OnPageNumberClick={handlePaginationChange}
              />
            </div> */}
          </div>
        </main>
      </div>
    </div>
  );
};
const mapStateToProps = (state) => ({
  payers: state.payers.payers,
  loading: state.payers.loading,
});
const mapDispatchToProps = (dispatch) => ({
  addPayer: (payerObject) => dispatch(addPayer(payerObject)),
  getPayers: () => dispatch(getPayers()),
  getPayersByType: (payerType) => dispatch(getPayersByType(payerType)),
});
export default connect(mapStateToProps, mapDispatchToProps)(Payers);
