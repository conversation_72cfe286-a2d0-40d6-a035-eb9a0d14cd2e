import { Menu, Transition } from "@headlessui/react";
import { ArrowDownTrayIcon, ArrowPathIcon, ChevronDownIcon } from "@heroicons/react/24/outline";
import { skipToken } from "@reduxjs/toolkit/query";
import { Fragment, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import {
  api,
  useFilterPayerProvidersQuery,
  useGetCountriesQuery,
  useGetRegionsQuery,
} from "~lib/api";
import { FilterPayerProviders } from "~lib/api/schema";
import {
  ExportFileType,
  ProviderTier,
  SearchProviderFilter,
  providerTierLabels,
  searchProviderFilterLabels,
} from "~lib/api/types";
import { Empty, FieldWrapper, Input, Pagination, Select } from "~lib/components";
import ErrorMessage from "~lib/components/Error";
import { Form } from "~lib/components/Form";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { PAGE_SIZES, baseUrl, dropdownTransitions } from "~lib/constants";
import { useDebounce } from "~lib/hooks/useDebounce";
import usePrevious from "~lib/hooks/usePrevious";
import { clsx, truncate } from "~lib/utils";
import UserService from "../../services/UserService";
import { useAppDispatch } from "../../store/hooks";

interface Inputs {
  query: string;
  country: string | undefined;
  region: string | undefined;
  type: SearchProviderFilter;
  tier: ProviderTier | undefined;
}

const exportTypes = [ExportFileType.PDF, ExportFileType.XLSX];

export const Providers = () => {
  const [page, setPage] = useState(1);
  const [size, setSize] = useState<number>(PAGE_SIZES[0]);

  const dispatch = useAppDispatch();

  const methods = useForm<Inputs>({
    mode: "onBlur",
    defaultValues: {
      query: "",
      country: undefined,
      region: undefined,
      type: SearchProviderFilter.ALL,
      tier: undefined,
    },
  });

  const { watch, setValue } = methods;

  const form = watch();
  const _queryDebounced = useDebounce(form.query, 200);

  const countryId = form.country ? parseInt(form.country) : undefined;
  const previousCountryId = usePrevious(countryId);

  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];

  const {
    data: countries,
    error: countriesError,
    isLoading: isCountriesLoading,
    isFetching: isCountriesFetching,
  } = useGetCountriesQuery({});

  const {
    data: regions,
    error: regionsError,
    isLoading: isRegionsLoading,
    isFetching: isRegionsFetching,
  } = useGetRegionsQuery(countryId ? { countryId } : skipToken);

  const params: Omit<FilterPayerProviders, "payerId"> = {
    query: _queryDebounced,
    countryId: form.country ? parseInt(form.country) : undefined,
    regionId: form.region ? parseInt(form.region) : undefined,
    providerType: form.type === SearchProviderFilter.ALL ? undefined : form.type,
    tier: form.tier,
  };

  const {
    data: providersResponse,
    error: providersError,
    isLoading: isProvidersLoading,
    isFetching: isProvidersFetching,
  } = useFilterPayerProvidersQuery(!payerId ? skipToken : { payerId, ...params, page, size }, {
    skip: !payerId,
  });

  const providers = providersResponse?.data.content;
  const isProviderMappingFetchingOnly = isProvidersFetching && !isProvidersLoading;

  const NA = <span className="uppercase text-gray-400">N/A</span>;

  const isExportLoading = false;
  const isExportDisabled = false;

  const refresh = () => dispatch(api.util.invalidateTags(["PayerProviders"]));

  const generateExportUrl = (fileType: ExportFileType) => {
    const entries = Object.fromEntries(
      (Object.keys(params) as Array<keyof typeof params>)
        .filter((key) => params[key] != undefined)
        .map((key) => [key, params[key]?.toString() ?? ""]),
    );

    const urlParams = new URLSearchParams({
      fileType,
      ...entries,
    });

    return `${baseUrl}/api/v1/membership/payers/${payerId}/providers/export?${urlParams.toString()}`;
  };

  useEffect(() => {
    // Reset region when country changes
    setValue("region", undefined);
  }, [countryId]);

  return (
    <div className="flex h-full flex-grow overflow-hidden bg-gray-50">
      <div className="flex flex-1 flex-col overflow-y-auto overflow-x-hidden">
        <main>
          <div className="w-full pb-8 text-gray-600">
            <div className="max-w-full rounded-md bg-white shadow-md">
              <div className="z-10 bg-gray-50 px-8 pt-4">
                <Form
                  className="pt-2 text-sm"
                  methods={methods}
                  onSubmit={(_data, e) => {
                    e?.preventDefault();
                  }}
                >
                  <div className="flex items-center justify-center pb-2">
                    <hgroup className="flex items-center gap-2">
                      <h2 className="text-lg font-medium capitalize">Providers</h2>
                      {isProviderMappingFetchingOnly ? (
                        <LoadingIcon className="h-5 w-5 text-gray-400" />
                      ) : (
                        <button
                          title="Refresh"
                          className="flex gap-2 rounded-full bg-transparent font-medium text-gray-400 enabled:hover:text-gray-500 disabled:cursor-not-allowed disabled:opacity-60"
                          type="button"
                          onClick={() => {
                            refresh();
                          }}
                        >
                          <ArrowPathIcon className="h-5 w-5" />
                        </button>
                      )}
                    </hgroup>
                  </div>

                  <div className="flex items-end justify-between gap-4">
                    <FieldWrapper
                      name="country"
                      label="Country"
                      labelClassName="font-semibold text-gray-500"
                    >
                      <Select
                        options={(countries || []).map((country) => ({
                          label: country.name,
                          value: country.id.toString(),
                        }))}
                        isLoading={isCountriesLoading}
                        className="min-w-44 max-w-xs"
                      />
                    </FieldWrapper>

                    <FieldWrapper
                      name="region"
                      label="Region"
                      labelClassName="font-semibold text-gray-500"
                    >
                      <Select
                        options={(regions || []).map((region) => ({
                          label: region.name,
                          value: region.id.toString(),
                        }))}
                        isLoading={isRegionsLoading || isCountriesLoading}
                        className="min-w-44 max-w-xs"
                      />
                    </FieldWrapper>

                    <FieldWrapper
                      name="type"
                      label="Provider Type"
                      labelClassName="font-semibold text-gray-500"
                    >
                      <Select
                        options={Object.values(SearchProviderFilter).map((providerType) => ({
                          label: searchProviderFilterLabels[providerType] || "Unknown",
                          value: providerType,
                        }))}
                        className="min-w-44 max-w-xs"
                      />
                    </FieldWrapper>

                    <FieldWrapper
                      name="tier"
                      label="Tier"
                      labelClassName="font-semibold text-gray-500"
                    >
                      <Select
                        options={Object.values(ProviderTier).map((tier) => ({
                          label: providerTierLabels[tier] || "Unknown",
                          value: tier,
                        }))}
                        className="min-w-44 max-w-xs"
                      />
                    </FieldWrapper>

                    {/* Add filter by country and region */}

                    <FieldWrapper
                      name="query"
                      label="Search"
                      className="flex-grow"
                      labelClassName="font-semibold text-gray-500"
                    >
                      <Input
                        type="text"
                        placeholder="Provider name..."
                        className="max-w-full text-sm"
                      />
                    </FieldWrapper>

                    <div className="flex flex-col gap-1">
                      <span>&#8203;</span>
                      <Menu as="div" className="relative inline-block text-left text-sm">
                        <div>
                          <Menu.Button
                            className={clsx(
                              "flex gap-2 rounded border border-blue-500 px-4 py-2 font-medium text-blue-500",
                              isExportDisabled
                                ? "cursor-not-allowed opacity-60"
                                : "hover:border-blue-600 hover:text-blue-600",
                            )}
                            disabled={isExportDisabled}
                          >
                            {isExportLoading && <LoadingIcon className="h-5 w-5" />}
                            <ArrowDownTrayIcon strokeWidth={1.5} className="h-5 w-5" />
                            <span>Export</span>
                            <ChevronDownIcon strokeWidth={1.5} className="-mr-1 h-5 w-5" />
                          </Menu.Button>
                        </div>

                        <Transition as={Fragment} {...dropdownTransitions}>
                          <Menu.Items className="absolute right-0 mt-2 w-36 origin-top-right rounded-md border border-gray-200 bg-white focus:outline-none">
                            {exportTypes.map((fileType) => (
                              <Menu.Item key={fileType}>
                                {({ active }) => (
                                  <a
                                    className={`${
                                      active ? "bg-gray-50" : ""
                                    } group flex w-full items-center rounded-md px-6 py-4 text-sm`}
                                    href={generateExportUrl(fileType)}
                                    target="_blank"
                                  >
                                    {fileType}
                                  </a>
                                )}
                              </Menu.Item>
                            ))}
                          </Menu.Items>
                        </Transition>
                      </Menu>
                      <span className="text-xs">&#8203;</span>
                    </div>
                  </div>
                </Form>
              </div>

              <div className="mb-4">
                {isProvidersLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <LoadingIcon className="h-6 w-6 text-blue-400" />
                  </div>
                ) : providersError ? (
                  <ErrorMessage title="Error fetching providers" message="Refresh to retry" />
                ) : !providers?.length ? (
                  <Empty message="No providers found" />
                ) : (
                  <div className="overflow-x-auto bg-white text-sm text-gray-600">
                    <table className="w-full">
                      <thead className="text-left">
                        <tr className="bg-gray-100">
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Name</th>
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Tier</th>
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Region</th>
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Main Facility</th>
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Coordinates</th>
                        </tr>
                      </thead>

                      <tbody>
                        {providers?.map((provider) => (
                          <tr key={provider.id}>
                            <td className="px-2 py-4 first:pl-8 last:pr-8">{provider.name}</td>
                            <td className="px-2 py-4 first:pl-8 last:pr-8">
                              {providerTierLabels[provider.tier]}
                            </td>
                            <td className="px-2 py-4 first:pl-8 last:pr-8">
                              {provider.region.name}
                            </td>
                            <td
                              className="px-2 py-4 first:pl-8 last:pr-8"
                              title={provider.mainFacility?.name}
                            >
                              {provider.mainFacility
                                ? truncate(provider.mainFacility.name, 30)
                                : NA}
                            </td>
                            <td className="px-2 py-4 first:pl-8 last:pr-8">
                              {provider?.longitude?.toFixed(3) || "NA"},{" "}
                              {provider?.latitude?.toFixed(3) || "NA"}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>

              {providersResponse && (
                <div className="pb-4">
                  <Pagination
                    totalElements={providersResponse?.data.totalElements}
                    totalPages={providersResponse?.data.totalPages}
                    setPage={setPage}
                    setSize={setSize}
                    page={page}
                    size={size}
                    isLoading={isProviderMappingFetchingOnly}
                  />
                </div>
              )}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Providers;
