import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import {
  useAddRestrictionProvidersMutation,
  useGetRestrictionProvidersQuery,
  useRemoveRestrictionProvidersMutation,
  useSearchProviderQuery,
  useGetProvidersQuery,
} from "~lib/api";
import { useDebounce } from "~lib/hooks/useDebounce";
import usePrevious from "~lib/hooks/usePrevious";
import { Provider, ProviderTier } from "~lib/api/types";
import {
  capitalize,
  clsx,
  difference,
  exclude,
  include,
  intersection,
  isSubset,
  pluralize,
  union,
} from "~lib/utils";
import Pagination from "~lib/components/Pagination";
import Empty from "~lib/components/Empty";

interface Props {
  restrictionId: number;
}

const PAGE_SIZES = [10, 20, 50, 100, 200];

const EditRestrictionSet = ({ restrictionId }: Props) => {
  const [filterTier, setFilterTier] = useState<ProviderTier | "">("");
  const [filterRegionId, setFilterRegionId] = useState<number | undefined>();
  const [query, setQuery] = useState("");

  const [filterMapped, setFilterMapped] = useState(false);
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(PAGE_SIZES[0]);
  const [added, setAdded] = useState([] as number[]);
  const [dropped, setDropped] = useState([] as number[]);

  const debouncedQuery = useDebounce(query, 500);
  const previousDebouncedQuery = usePrevious(debouncedQuery);
  const selectAllRef = React.useRef<HTMLInputElement | null>(null);

  const { data: providersResponse, isLoading: isGetProvidersLoading } =
    useGetProvidersQuery(
      {
        page,
        size,
      },
      {
        skip: Boolean(debouncedQuery),
      }
    );

  const { data: searchProviderResponse, isLoading: isSearchProviderLoading } =
    useSearchProviderQuery(
      {
        name: debouncedQuery,
        page,
        size,
      },
      {
        skip: !debouncedQuery,
      }
    );

  const {
    data: restrictionProvidersResponse,
    isLoading: isGetRestrictionProvidersLoading,
  } = useGetRestrictionProvidersQuery({
    restrictionId,
    page,
    size,
  });

  const [
    addRestrictionProviders,
    { isLoading: isAddRestrictionProvidersLoading },
  ] = useAddRestrictionProvidersMutation();

  const [
    removeRestrictionProviders,
    { isLoading: isRemoveRestrictionProvidersLoading },
  ] = useRemoveRestrictionProvidersMutation();

  const isProvidersLoading =
    isGetProvidersLoading ||
    isSearchProviderLoading ||
    isGetRestrictionProvidersLoading;

  const getActiveResponse = () => {
    if (debouncedQuery) {
      return searchProviderResponse;
    } else if (filterMapped) {
      return restrictionProvidersResponse;
    } else {
      return providersResponse;
    }
  };

  const restrictionProviders =
    restrictionProvidersResponse?.data?.content.map(
      (restrictionProvider) => restrictionProvider.provider
    ) || [];

  const activeResponse = getActiveResponse();
  const totalPages = activeResponse?.data?.totalPages || 1;
  const totalElements = activeResponse?.data?.totalElements || 0;
  const providers =
    (filterMapped
      ? restrictionProviders
      : (activeResponse?.data?.content as Provider[])) || [];

  const mapped = restrictionProviders.map((provider) => provider.id);

  // Mapped providerIds that are not marked for deletion
  const mappedChecked = difference(mapped, dropped);

  // Mapped providerIds that are not marked for deletion
  // and providerIds to be added
  const mappedOrAdded = union(mappedChecked, added);

  // Filter providers by all active filters
  const filteredProviders = providers.filter(
    (provider) =>
      (provider.region.id === filterRegionId || !filterRegionId) &&
      (provider.tier === filterTier || !filterTier)
  );
  const filtered = filteredProviders.map((provider) => provider.id);

  const isAllSelected = isSubset(mappedOrAdded, filtered);
  const isNoneSelected = intersection(mappedOrAdded, filtered).length === 0;

  // Get unique regions by id
  const regions = [
    ...new Map(
      providers
        .map((provider) => provider.region)
        .map((region) => [region["id"], region])
    ).values(),
  ];

  const isFormLoading =
    isAddRestrictionProvidersLoading || isRemoveRestrictionProvidersLoading;

  useEffect(() => {
    // Set the indeterminate state of the select all checkbox
    // is (strictly) some of the providers in the page are selected
    if (selectAllRef.current) {
      selectAllRef.current.indeterminate = !isNoneSelected && !isAllSelected;
    }
  }, [isAllSelected, isNoneSelected]);

  useEffect(() => {
    if ((!previousDebouncedQuery && debouncedQuery) || !debouncedQuery) {
      // Started a new search or clearing search - reset pagination
      setPage(1);
      setSize(PAGE_SIZES[0]);
    }
  }, [debouncedQuery, previousDebouncedQuery]);

  useEffect(() => {
    // Reset pagination when toggling between mapped and unmapped providers
    setPage(1);
    setSize(PAGE_SIZES[0]);
  }, [filterMapped]);

  useEffect(() => {
    // Searching for a provider precludes filtering by status (mapped/unmapped)
    if (debouncedQuery) {
      setFilterMapped(false);
    }
  }, [debouncedQuery]);

  function handleProviderSelectChange(
    event: React.ChangeEvent<HTMLInputElement>
  ) {
    const id = Number(event.target.value);
    if (event.target.checked) {
      // Checkbox checked
      if (dropped.includes(id)) {
        // Remove mapped providerId from values that will be dropped
        setDropped(exclude(dropped, id));
      } else {
        // Add providerId to values that will be added
        setAdded(include(added, id));
      }
    } else {
      // Checkbox unchecked
      if (added.includes(id)) {
        // Remove providerId from values that will be added
        setAdded(exclude(added, id));
      } else {
        // Include mapped providerId to values that will be dropped
        setDropped(include(dropped, id));
      }
    }
  }

  const handleSelectAllChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (!restrictionProvidersResponse) {
      return;
    }

    // Filtered providers that are mapped
    const filteredMapped = intersection(mapped, filtered);

    // Filtered providers that are not mapped
    const unmappedFiltered = difference(filtered, mapped);

    if (event.target.checked) {
      // Remove all mapped providers in page from values to be dropped
      setDropped(difference(dropped, filteredMapped));

      // Add all unmapped providers in page to the list of selected providers
      setAdded(union(added, unmappedFiltered));
    } else {
      // Remove all providers in page from the list of selected providers
      setAdded(difference(added, filtered));
      setDropped(union(dropped, filteredMapped));
    }
  };

  const handleRegionChange = (e: React.FormEvent<HTMLSelectElement>) => {
    const value = e.currentTarget.value;
    setFilterRegionId(value ? Number(value) : undefined);
  };

  const handleTierChange = (e: React.FormEvent<HTMLSelectElement>) => {
    setFilterTier(e.currentTarget.value as ProviderTier | "");
  };

  const handleFilterMappedChange = (e: React.FormEvent<HTMLSelectElement>) => {
    const value = e.currentTarget.value;

    if (value) {
      setFilterMapped(true);
    } else {
      setFilterMapped(undefined);
    }
  };

  const handleKeyUp = (e: React.KeyboardEvent<HTMLInputElement>) => {
    setQuery(e.currentTarget.value);
  };

  const handleReset = () => {
    setQuery("");
    setFilterRegionId(undefined);
    setFilterTier("");
    setAdded([]);
    setDropped([]);
  };

  const handleSave = async (
    e: React.MouseEvent<HTMLButtonElement, MouseEvent>
  ): Promise<void> => {
    e.stopPropagation();

    if (added.length > 0) {
      try {
        await addRestrictionProviders({
          providerIds: added,
          restrictionId,
        }).unwrap();
        setAdded([]);
        toast.success("Providers added successfully");
      } catch (error) {
        toast.error("An error occurred");
      }
    }

    if (dropped.length > 0) {
      try {
        await removeRestrictionProviders({
          restrictionId,
          providerIds: dropped,
        }).unwrap();
        setDropped([]);
        toast.success("Providers removed successfully");
      } catch (error) {
        toast.error("An error occurred");
      }
    }
  };

  const message = capitalize(
    [
      added.length ? `add ${added.length}` : "",
      dropped.length ? `remove ${dropped.length}` : "",
    ]
      .filter(Boolean)
      .join(", ")
  );

  const suffix = pluralize(
    [dropped.length, added.length].find(Boolean),
    "provider"
  );

  return (
    <div className="px-4 py-4">
      <div>
        <div className="grid gap-4 lg:grid-cols-2 mb-4">
          <div>
            <p className="mb-2 text-gray-400">Filter in current page:</p>

            <div className="flex flex-wrap gap-4">
              <select
                name="region"
                onChange={handleRegionChange}
                className="rounded text-sm border-gray-200 text-gray-500"
                defaultValue=""
              >
                <option value="">Select Region</option>
                {regions.map((region) => (
                  <option
                    className="flex items-center w-full text-sm hover:bg-gray-50 py-1 px-3 cursor-pointer font-medium text-gray-600"
                    value={region.id}
                    key={region.id}
                  >
                    {region.name}
                  </option>
                ))}
              </select>

              <select
                name="tier"
                onChange={handleTierChange}
                className="rounded text-sm border-gray-200 text-gray-500"
                defaultValue=""
              >
                <option
                  className="flex items-center w-full text-sm  hover:bg-gray-50 py-1 px-3 cursor-pointer font-medium text-gray-600"
                  value=""
                >
                  Select Tier
                </option>
                <option value="TIER_ONE">TIER ONE</option>
                <option value="TIER_TWO">TIER TWO</option>
                <option value="TIER_THREE">TIER THREE</option>
              </select>
            </div>
          </div>

          <div>
            <p className="mb-2 text-gray-400">Filter in all pages:</p>

            <div className="flex flex-wrap gap-4">
              <select
                name="mapped"
                onChange={handleFilterMappedChange}
                className="rounded text-sm border-gray-200 text-gray-500"
                defaultValue=""
                value={filterMapped ? "true" : ""}
              >
                <option
                  className="flex items-center w-full text-sm  hover:bg-gray-50 py-1 px-3 cursor-pointer font-medium text-gray-600"
                  value=""
                >
                  All providers
                </option>
                <option value="true">In Restriction</option>
              </select>

              <input
                className="rounded text-sm border-gray-200 text-gray-500 capitalize"
                type="search"
                placeholder="Search Provider"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyUp={handleKeyUp}
                autoComplete="off"
              />
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between gap-4 pl-2 pr-4">
          <div>
            {(added.length || dropped.length) ? `${message} ${suffix}` : ""}
          </div>

          <button
            className="text-red-500 hover:text-red-600"
            onClick={() => {
              setAdded([]);
              setDropped(mapped);
            }}
          >
            Remove All
          </button>
        </div>

        {isProvidersLoading ? (
          <div className="py-8 flex justify-center">
            {/* prettier-ignore */}
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-blue-400 w-6 h-6">
              <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
            </svg>
          </div>
        ) : (
          <div className="pt-5">
            <div className="overflow-x-auto max-h-96 overflow-y-scroll">
              <table className="table-auto w-full">
                <thead className="text-md font-medium border-b border-gray-200">
                  <tr>
                    <th className="px-2 first:pl-5 last:pr-5 py-1 whitespace-nowrap">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          name="select-all"
                          checked={isAllSelected}
                          ref={selectAllRef}
                          onChange={handleSelectAllChange}
                          className="rounded-sm border-gray-300"
                          title={
                            isAllSelected
                              ? "Unselect All in Page"
                              : "Select All in Page"
                          }
                        />
                      </div>
                    </th>
                    <th className="px-2 first:pl-5 last:pr-5 py-1 whitespace-nowrap">
                      <div className="font-medium text-left">Name</div>
                    </th>
                    <th className="px-2 first:pl-5 last:pr-5 py-1 whitespace-nowrap">
                      <div className="font-semibold text-left">Tier</div>
                    </th>
                    <th className="px-2 first:pl-5 last:pr-5 py-1 whitespace-nowrap">
                      <div className="font-medium text-left">Region</div>
                    </th>
                  </tr>
                </thead>

                <tbody className="text-sm">
                  {filteredProviders.map((provider) => (
                    <tr className="even:bg-light-blue-50" key={provider.id}>
                      <td className="px-2 first:pl-5 last:pr-5 py-1 whitespace-nowrap w-px">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            name="providers"
                            value={provider.id}
                            checked={mappedOrAdded.includes(provider.id)}
                            onChange={handleProviderSelectChange}
                            className="rounded-sm border-gray-300"
                            id={`provider-${provider.id}`}
                          />
                        </div>
                      </td>
                      <td className="px-2 first:pl-5 last:pr-5 py-1 whitespace-nowrap">
                        <label
                          className="font-medium"
                          htmlFor={`provider-${provider.id}`}
                        >
                          {provider.name}
                        </label>
                      </td>
                      <td className="px-2 first:pl-5 last:pr-5 py-1 whitespace-nowrap">
                        <div className={`font-medium`}>
                          {provider.tier.replace("_", " ")}
                        </div>
                      </td>
                      <td className="px-2 first:pl-5 last:pr-5 py-1 whitespace-nowrap">
                        <div className={`font-medium`}>
                          {provider.region.name}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* No elements exist */}
        <div className="flex justify-center font-sans text-sm md:text-base lg:text-lg mb-4 font-normal">
          {!filteredProviders.length && (
            <Empty
              message={
                providers.length
                  ? "No matching providers in current page"
                  : "No providers found"
              }
            />
          )}
        </div>

        {/* Pagination */}
        <Pagination
          {...{ totalPages, totalElements, page, size, setPage, setSize }}
        />
      </div>

      <div className="px-5 py-4 mt-5">
        <div className="flex flex-wrap justify-center space-x-2">
          <button
            type="button"
            className="btn-sm border-gray-200 hover:border-gray-300 text-gray-600 w-44 px-16 py-2"
            onClick={handleReset}
          >
            Reset
          </button>

          <button
            type="button"
            className={clsx(
              "bg-blue-500 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded flex gap-2",
              isFormLoading && "opacity-50 cursor-not-allowed"
            )}
            key="submit"
            title={isFormLoading ? "Loading..." : "Submit"}
            disabled={isFormLoading}
            onClick={handleSave}
          >
            {isFormLoading && ( // prettier-ignore
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
                className="text-white"
              >
                <path
                  fill="currentColor"
                  d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"
                >
                  <animateTransform
                    attributeName="transform"
                    type="rotate"
                    dur="0.75s"
                    values="0 12 12;360 12 12"
                    repeatCount="indefinite"
                  />
                </path>
              </svg>
            )}
            <span>Submit</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default EditRestrictionSet;
