import { useState } from "react";
import { toast } from "react-toastify";
import {
  useDeleteRestrictionMutation,
  useGetRestrictionsQuery,
} from "~lib/api";
import { RestrictionType } from "~lib/api/types";
import AuthenticationError from "~lib/components/AuthenticationError";
import Empty from "~lib/components/Empty";
import { clsx } from "~lib/utils";
import UserService from "../../services/UserService";
import Modal from "~lib/components/Modal";
import AddRestrictionSet from "./AddRestrictionSet";
import EditRestrictionSet from "./EditRestrictionSet";

export default function Restrictions() {
  const [isAddRestrictionSetModalOpen, setIsAddRestrictionSetModalOpen] =
    useState(false);
  const [isEditRestrictionSetModalOpen, setIsEditRestrictionSetModalOpen] = useState(false)
  const [selectedRestriction, setSelectedRestriction] = useState<number|undefined>()

  const payerId = UserService.getPayer().tokenParsed.payerId;
  const {
    data: restrictions,
    isLoading: isRestrictionsLoading,
    error: restrictionsError,
  } = useGetRestrictionsQuery({
    payerId,
  });

  const [deleteRestriction, { isLoading: isDeleteRestrictionLoading }] =
    useDeleteRestrictionMutation();

  async function handleDeleteRestriction(id: number) {
    if (!payerId) {
      console.warn("Delete restriction: No payerId from UserService");
      return;
    }

    try {
      await deleteRestriction({ id });
      toast.success("Restriction deleted successfully");
    } catch (error) {
      toast.error("Something went wrong. Please try again.");
    }
  }

  function handleEditRestriction(id: number) {
    if (!payerId) {
      console.warn("Edit restriction: No payerId from UserService");
      return;
    }

    setSelectedRestriction(id)
    setIsEditRestrictionSetModalOpen(true)
  }

  return (
    <div className="flex h-full overflow-hidden">
      {/* Content area */}
      <div className="relative flex flex-col flex-1 max-h-screen overflow-y-auto overflow-x-hidden ">
        {/*  Site header */}
        <main className="mx-4 lg:mx-8 mb-4 lg:mb-8 ">
          {!payerId && <AuthenticationError />}

          <div className="flex justify-between gap-4 mb-4 items-center">
            <h1 className="text-xl font-medium">Restrictions Sets</h1>

            <button
              type="button"
              className="bg-blue-500 hover:bg-blue-600 font-medium text-white px-4 py-2 rounded-md"
              onClick={() => {
                setIsAddRestrictionSetModalOpen(true);
              }}
            >
              Add Restriction Set
            </button>
          </div>

          <div className="pb-4">
            {isRestrictionsLoading ? (
              <div className="flex justify-center items-center py-8">
                {/* prettier-ignore */}
                <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-blue-400">
                  <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
                </svg>
              </div>
            ) : restrictionsError ? (
              <div className="py-8 px-2 text-red-500">
                Something went wrong. Please refresh the page to retry.
              </div>
            ) : !restrictions.length ? (
              <Empty message="No Restrictions" />
            ) : (
              <div className="flex flex-col gap-4 max-w-full overflow-x-auto p-4">
                {restrictions?.map((restriction) => (
                  <div
                    className="flex justify-between items-center rounded shadow px-8 py-4"
                    key={restriction.id}
                  >
                    <div className="flex gap-2">
                      <span>{restriction.name}</span>

                      <p>
                        <span
                          className={clsx(
                            "text-white text-xxs align-middle font-bold rounded-full px-2 py-1 uppercase flex-shrink tracking-wider",
                            restriction.restrictionType ===
                              RestrictionType.INCLUSIVE && "bg-green-500",
                            restriction.restrictionType ===
                              RestrictionType.EXCLUSIVE && "bg-red-500"
                          )}
                          title="Restriction Type"
                        >
                          {restriction.restrictionType}
                        </span>
                      </p>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2">
                      <button
                        type="button"
                        className={clsx(
                          "text-blue-500 hover:text-blue-600",
                        )}
                        title="Edit"
                        onClick={() => {
                          handleEditRestriction(restriction.id);
                        }}
                      >
                        {/* prettier-ignore */}
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125" />
                        </svg>
                      </button>

                      <button
                        type="button"
                        className={clsx(
                          "text-red-500 hover:text-red-600",
                          isDeleteRestrictionLoading &&
                            "cursor-not-allowed opacity-60"
                        )}
                        title={
                          isDeleteRestrictionLoading ? "Loading..." : "Delete"
                        }
                        onClick={() => {
                          handleDeleteRestriction(restriction.id);
                        }}
                        disabled={isDeleteRestrictionLoading}
                      >
                        {/* prettier-ignore */}
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                        </svg>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <Modal
            id="add-restriction-set"
            modalOpen={isAddRestrictionSetModalOpen}
            onClose={() => {
              setIsAddRestrictionSetModalOpen(false);
            }}
            title="Add Restriction Set"
          >
            <AddRestrictionSet />
          </Modal>
          
          <Modal
            id="edit-restriction-set"
            modalOpen={isEditRestrictionSetModalOpen}
            onClose={() => {
              setIsEditRestrictionSetModalOpen(false);
            }}
            title="Edit Restriction Set"
          >
            {selectedRestriction ? (
              <EditRestrictionSet restrictionId={selectedRestriction} />
            ) : (
              <p className="py-8 px-4 text-red-400">No restriction selected.</p>
            )}
          </Modal>
        </main>
      </div>
    </div>
  );
}
