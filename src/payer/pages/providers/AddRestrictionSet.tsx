import { useForm } from "react-hook-form"
import { useAddRestrictionMutation } from "~lib/api"
import { RestrictionType } from "~lib/api/types"
import AuthenticationError from "~lib/components/AuthenticationError"
import FieldWrapper from "~lib/components/FieldWrapper"
import { Form } from "~lib/components/Form"
import Input from "~lib/components/Input"
import Select from "~lib/components/Select"
import { capitalize, clsx } from "~lib/utils"
import UserService from "../../services/UserService"
import { toast } from "react-toastify"

interface Inputs {
  name: string
  restrictionType: RestrictionType
}

export default function AddRestrictionSet() {
  const methods = useForm<Inputs>({
    mode: "onBlur",
    defaultValues: {
      name: "",
      restrictionType: RestrictionType.INCLUSIVE,
    },
  })

  const payerId = UserService.getPayer().tokenParsed.payerId
  const { reset } = methods;

  const [addRestriction, { isLoading }] = useAddRestrictionMutation()

  const handleSubmit = async (data: Inputs) => {
    try {
      const response = await addRestriction({ 
        ...data,
        payerId, 
      }).unwrap()

      if (response.success === false) {
        toast.error(response.msg || "Something went wrong. Please try again.");
        return;
      }

      toast.success("Restriction set added successfully");
      reset()
    } catch (error) {
      console.error(error)
    }
  }

  return (
    <div className="py-4 px-8">
      {!payerId && <AuthenticationError />}

      <Form className="grid gap-4 lg:grid-cols-2" methods={methods} onSubmit={handleSubmit}>
        <FieldWrapper 
            label="Name"
            description="The name of the restriction set."
            name="name"
            className="mb-2"
          >
            <Input required minLength={1} />
          </FieldWrapper>

        <FieldWrapper
          label="Type"
          description="Allow only selected providers or exclude selected providers."
          name="restrictionType"
          className="mb-2"
          required
        >
          <Select
            options={
              Object.entries(RestrictionType).map(([, value]) => ({ label: `${capitalize(value.toLowerCase().replace(/_/g, " "))}`, value: value }))
            }
          />
        </FieldWrapper>

        <div className="flex col-span-2 gap-2 flex-wrap justify-end">
          <input 
            type="button"
            value="Reset" 
            onClick={() => {
              reset()
            }}
            className={clsx(
              "bg-gray-400 hover:bg-gray-500 rounded font-medium text-white px-4 py-2 focus:outline-blue",
              isLoading && "opacity-50 cursor-not-allowed"
            )}
            title={isLoading ? "Loading..." : "Reset"}
            disabled={isLoading}
          />
          
          <button
            type="submit"
            className={clsx(
              "bg-blue-500 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded flex gap-2",
              (isLoading || !payerId) && "opacity-50 cursor-not-allowed"
            )}
            key="submit"
            title={isLoading ? "Loading..." : "Submit"}
            disabled={isLoading || !payerId}
          >
            {isLoading && (
              // prettier-ignore
              <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-white">
                <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
              </svg>
            )}
            <span>Submit</span>
          </button>
        </div>
      </Form>
    </div>
  )
}
