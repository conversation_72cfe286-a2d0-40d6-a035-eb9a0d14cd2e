import React, { useEffect, useState } from "react";

import Pagination from "../../components/Pagination";
import ModalBasic from "../../components/ModalBasic";
import { useSelector } from "react-redux";
import { RootState } from "../../store";
import useFetch from "../../lib/useFetch";
import { mapBenefit } from "../../store/payers/actions";
import { connect } from "react-redux";
import ProviderExclusionsTable from "../partials/pageItems/ProviderExclusionsTable";
import ProviderCopaysTable from "../partials/pageItems/ProviderCopaysTable";
import ProviderWhitelistTable from "../partials/pageItems/ProviderWhitelistTable";
import {
  getProviderCopays,
  getProviderExclusions,
  getProviderWhitelist,
  whitelistMultipleProviderBenefit,
} from "../../store/providers/actions";
import makeAnimated from "react-select/animated";
import { getBenefitCatalog2 } from "../../store/catalog/actions";
import { toast } from "react-toastify";
import * as notifications from "../../lib/notifications.js";

const animatedComponents = makeAnimated();
function ProviderOverview({
  mapBenefit,
  getProviderExclusions,
  getProviderCopays,
  getProviderWhitelist,
  getBenefitCatalog2,
  whitelistMultipleProviderBenefit,
}) {
  const [payerEditModalOpen, setEditPayerModalOpen] = useState(false);
  const [providerMappingModalOpen, setProviderMappingModalOpen] = useState(false);
  const [benefitMappingModalOpen, setBenefitMappingModalOpen] = useState(false);

  const [openTab, setOpenTab] = React.useState(1);
  const [color, setColor] = React.useState("blue");

  const [providerType, setProviderType] = useState("");
  const [providerName, setProviderName] = useState("");
  const [providerId, setProviderId] = useState("");

  const [underwriter, setUnderwriter] = useState(false);
  const [corporate, setCorporate] = useState(false);
  const [administrator, setAdministrator] = useState(false);
  const [whitelistModalOpen, setWhitelistModalOpen] = useState(false);

  const [mobile, setMobile] = useState("");
  const [email, setEmail] = useState("");

  const [selectedItems, setSelectedItems] = useState([]);
  const [benefitsListIds, setBenefitsListIds] = useState([]);
  const [benefitsListNames, setBenefitsListNames] = useState([]);

  const [benefitsSelectedIds, setBenefitSelectedIds] = useState([]);
  const [benefitsSelectedNames, setBenefitsSelectedNames] = useState([]);

  const [payerModalOpen, setPayerModalOpen] = useState(false);
  const [inputCode, setInputCode] = useState("");
  const { data, setData } = useFetch();
  const [refreshExclusions, setRefreshEx] = useState(0);
  const [refreshCopay, setRefreshCopay] = useState(0);

  const [refreshWhitelist, setRefreshWhitelist] = useState(0);
  const providerUrlId = window.location.pathname.split("/provider/overview/")[1];
  const [benefitsSubmitted, setBenefitsSubmitted] = useState(false);

  const handleSelectedItems = (selectedItems) => {
    setSelectedItems([...selectedItems]);
  };
  const providerItem = useSelector((state: RootState) => state.providers.providerItem);
  const exclusions = useSelector((state: RootState) => state.providers.exclusions);

  const copays = useSelector((state: RootState) => state.providers.copays);

  const whitelist = useSelector((state: RootState) => state.providers.whitelist);
  const benefitCatalog = useSelector((state: RootState) => state.catalog.benefitCatalog);
  const success = useSelector((state: RootState) => state.providers.success);

  const [checkedState, setCheckedState] = useState(new Array(benefitCatalog.length).fill(false));

  const totalWhiteListElements = useSelector(
    (state: RootState) => state.providers.totalWhiteListElements,
  );
  const totalWhiteListPages = useSelector(
    (state: RootState) => state.providers.totalWhiteListPages,
  );
  const pageWhiteListNumber = useSelector(
    (state: RootState) => state.providers.pageWhiteListNumber,
  );

  const searchBenefit = (e) => {
    setData({ ...data, slug: e.target.value });
  };

  useEffect(() => {
    getBenefitCatalog2();
    // setData({ ...data, slug: selectedBenefit.name });
    if (providerItem != null) {
      setProviderName(providerItem.name);
      setProviderType(providerItem.type);
      setProviderId(providerItem.id);
    }

    getProviderExclusions(providerUrlId).then(() => {
      setRefreshEx((oldKey) => oldKey + 1);
    });
    getProviderCopays(providerUrlId).then(() => {
      setRefreshCopay((oldKey) => oldKey + 1);
    });
    getProviderWhitelist(providerUrlId).then(() => {
      setRefreshWhitelist((oldKey) => oldKey + 1);
    });
  }, []);

  const handleBenefitMappingSubmit = () => {
    mapBenefit({
      name: data.slug,
      code: inputCode,
      serviceGroup: "OUTPATIENT",
    });
  };

  if (benefitCatalog != null) {
    console.log(benefitCatalog);
  }

  const handleChange = (id, name, position) => {
    const updatedCheckedState = checkedState.map((item, index) =>
      index === position ? !item : item,
    );

    setCheckedState(updatedCheckedState);

    setBenefitsListIds((prevState) => ({
      ...prevState,
      [id]: !prevState[Number(id)],
    }));
    setBenefitsListNames((prevState) => ({
      ...prevState,
      [name]: !prevState[name],
    }));
  };

  const resultIds = Object.keys(benefitsListIds).reduce((o, key) => {
    benefitsListIds[key] === true && (o[key] = benefitsListIds[key]);

    return o;
  }, {});

  const resultNames = Object.keys(benefitsListNames).reduce((o, key) => {
    benefitsListNames[key] === true && (o[key] = benefitsListNames[key]);

    return o;
  }, {});
  const ids = Object.keys(resultIds);
  const names = Object.keys(resultNames) + ",";

  useEffect(() => {
    if (benefitsSubmitted)
      if (success) {
        setBenefitsSubmitted(false);
        toast.success("Benfit(s) Whitelist Successfull!", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        setWhitelistModalOpen(false);
      } else {
        setBenefitsSubmitted(false);
        toast.error("An Error Occurred!", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        notifications.Error({
          title: "Error Occurred",
        });
      }
  });

  const handlePaginationChange = (e) => {
    getProviderWhitelist(providerUrlId, e).then(() => {
      setRefreshWhitelist((oldKey) => oldKey + 1);
    });
  };
  const handleSubmitBenefits = (event) => {
    event.preventDefault();
    const benefit = "benefitIds";
    const provObj = {
      provider_id: providerUrlId,
      [benefit]: ids,
    };

    whitelistMultipleProviderBenefit(provObj).then(() => {
      setBenefitsSubmitted(true);
      getProviderWhitelist(providerUrlId).then(() => {
        setRefreshWhitelist((oldKey) => oldKey + 1);
      });
    });
  };

  return (
    <div className="flex h-full overflow-hidden">
      {/* Content area */}
      <div className="relative flex flex-1 flex-col overflow-y-auto overflow-x-hidden">
        <main className="ml-10 mr-10">
          <div className="mx-auto w-full max-w-9xl  rounded-md px-4 shadow-lg sm:px-6 lg:px-8 ">
            {/* Page header */}
            <div className="sm:flex sm:items-center sm:justify-between">
              {/* Left: Title */}
              <div className="mb-4 font-bold sm:mb-0">
                <h3 className="mb-2 text-lg font-semibold leading-8 tracking-wide text-gray-800">
                  Provider Overview
                </h3>
                <div className="mb-1 mt-2 text-lg  font-semibold leading-7 tracking-wide text-gray-400">
                  <h3>{providerName}</h3>
                </div>
                <div className="mb-1 mt-2 text-lg  font-semibold leading-7 tracking-wide  text-gray-400">
                  <h3>{providerType}</h3>
                </div>
              </div>
              {/* Right */}
              <div className="mb-4 mr-16 font-bold sm:mb-0">
                <div className="mb-1 mt-2 text-lg  font-semibold leading-7 tracking-wide  text-gray-400"></div>
              </div>
            </div>
            {/* More actions */}
            <div className="mb-2 sm:flex sm:items-center sm:justify-between"></div>
            {/* Modal */}
            <ModalBasic
              id="feedback-modal"
              modalOpen={payerModalOpen}
              setModalOpen={setPayerModalOpen}
              title="Add Payer"
            >
              {/* Modal content */}
              <div className="px-10 pb-1 pt-4">
                <h3 className="mb-5 text-lg text-gray-400">PAYER DETAILS</h3>

                <div className="text-sm">
                  {/* Options */}
                  <div className="flex">
                    <div className="mr-9 w-64">
                      <label className="mb-1 block text-sm font-medium" htmlFor="name">
                        Payer Name
                      </label>
                      <input
                        id="name"
                        className="form-input w-full"
                        type="text"
                        placeholder="Payer Name"
                      />
                    </div>
                    <div className="mr-9 w-64">
                      <label className="mb-1 block text-sm font-medium" htmlFor="email">
                        Email Address
                      </label>
                      <input
                        id="email"
                        className="form-input w-full "
                        type="email"
                        placeholder="Email Address"
                      />
                    </div>
                    <div className="mr-9 w-64">
                      <label className="mb-1 block text-sm font-medium" htmlFor="name">
                        Mobile
                      </label>
                      <input
                        id="name"
                        className="form-input w-full"
                        type="phone"
                        placeholder="Mobile"
                      />
                    </div>
                  </div>
                  <div className="mt-10">
                    <h3 className="mb-5 text-lg text-gray-400">PAYER TYPE</h3>
                  </div>

                  <div className="mt-5 flex">
                    <div className="mr-9  flex ">
                      <div className="mr-5  w-48">
                        <input type="radio" name="option" id="option1" className="hidden " />
                        <label
                          htmlFor="option1"
                          className="rounded-md border-2 px-16 py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                        >
                          Underwriter
                        </label>
                      </div>
                      <div className="mr-5 w-48">
                        <input type="radio" name="option" id="option2" className="hidden" />
                        <label
                          htmlFor="option2"
                          className="rounded-md border-2 px-16  py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                        >
                          Corporate
                        </label>
                      </div>
                      <div className="mr-5 w-48">
                        <input type="radio" name="option" id="option3" className="hidden" />
                        <label
                          htmlFor="option3"
                          className="rounded-md border-2 px-16  py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                        >
                          Intermediary
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {/* Modal footer */}
              <div className="mt-5 px-5 py-4">
                <div className="flex flex-wrap justify-center space-x-2">
                  <button
                    className="btn-sm w-44 border-gray-200 px-16 py-2 text-gray-600 hover:border-gray-300"
                    onClick={(e) => {
                      e.stopPropagation();
                      setPayerModalOpen(false);
                    }}
                  >
                    Clear
                  </button>
                  <button className="btn-sm w-44 bg-blue-500 px-16 py-2 text-white hover:bg-blue-600">
                    Save
                  </button>
                </div>
              </div>
            </ModalBasic>

            <div className="">
              <div className="mt-1 flex flex-wrap">
                <div className="w-full">
                  <ul className="mb-0 flex list-none flex-row flex-wrap pb-4" role="tablist">
                    <li
                      className={
                        "-mb-px mr-2 flex-auto text-center last:mr-0" +
                        (openTab === 1
                          ? " border-  border-b-4" + color + "-600"
                          : " border-b-gray-600")
                      }
                    >
                      <a
                        className={
                          "block rounded px-5 py-3 text-lg font-bold leading-normal shadow-lg " +
                          (openTab === 1
                            ? "text-" + color + "-600" + " border-b-" + color + "-600"
                            : "text-gray-700")
                        }
                        onClick={(e) => {
                          e.preventDefault();
                          setOpenTab(1);
                        }}
                        data-toggle="tab"
                        href="#link1"
                        role="tablist"
                      >
                        <i className="fas fa-space-shuttle mr-1 text-base"></i> Exclusions
                      </a>
                    </li>
                    <li
                      className={
                        "-mb-px mr-2 flex-auto text-center last:mr-0" +
                        (openTab === 2
                          ? " border-  border-b-4" + color + "-600"
                          : " border-b-gray-600")
                      }
                    >
                      <a
                        className={
                          "block rounded  px-5 py-3 text-lg font-bold leading-normal shadow-lg " +
                          (openTab === 2
                            ? "text-" + color + "-600" + " border-b-" + color + "-600"
                            : "text-gray-700")
                        }
                        onClick={(e) => {
                          e.preventDefault();
                          setOpenTab(2);
                        }}
                        data-toggle="tab"
                        href="#link2"
                        role="tablist"
                      >
                        <i className="fas fa-cog mr-1 text-base"></i> CoPays
                      </a>
                    </li>
                    <li
                      className={
                        "-mb-px mr-2 flex-auto text-center last:mr-0" +
                        (openTab === 3
                          ? " border-  border-b-4" + color + "-600"
                          : " border-b-gray-600")
                      }
                    >
                      <a
                        className={
                          "block rounded  px-5 py-3 text-lg font-bold leading-normal shadow-lg " +
                          (openTab === 3
                            ? "text-" + color + "-600" + " border-b-" + color + "-600"
                            : "text-gray-700")
                        }
                        onClick={(e) => {
                          e.preventDefault();
                          setOpenTab(3);
                        }}
                        data-toggle="tab"
                        href="#link3"
                        role="tablist"
                      >
                        <i className="fas fa-cog mr-1 text-base"></i> WhiteList
                      </a>
                    </li>
                  </ul>
                  <div className="relative mb-6 flex w-full min-w-0 flex-col break-words rounded  bg-white">
                    <div className="flex-auto px-4 py-5">
                      <div className="tab-content tab-space">
                        <div className={openTab === 1 ? "block" : "hidden"} id="link1">
                          {/* Table */}
                          <ProviderExclusionsTable
                            selectedItems={handleSelectedItems}
                            exclusions={exclusions}
                            key={refreshExclusions}
                          />
                          <div className="mt-8">{/* <Pagination /> */}</div>
                        </div>
                        <div className={openTab === 2 ? "block" : "hidden"} id="link2">
                          {/* Table */}
                          <ProviderCopaysTable
                            selectedItems={handleSelectedItems}
                            copays={copays}
                            key={refreshCopay}
                          />
                          <div className="mt-8">{/* <Pagination /> */}</div>
                        </div>
                        <div className={openTab === 3 ? "block" : "hidden"} id="link3">
                          <div className="sm:flex sm:items-center sm:justify-between">
                            {/* Left: Title */}
                            <div className="font-bold sm:mb-0"></div>
                            {/* Right */}
                            <div className="mb-1 mr-16 font-bold sm:mb-0">
                              <div className="mb-1 mt-0 text-lg  font-semibold leading-7 tracking-wide  text-gray-400">
                                {/* <button
                                  className="btn bg-blue-500 hover:bg-blue-600 text-white"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setWhitelistModalOpen(true);
                                  }}
                                >
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-6 w-6"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="2"
                                      d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"
                                    />
                                  </svg>
                                  <span className="hidden xs:block ml-2 text-base">
                                    Whitelist Benefit(s)
                                  </span>
                                </button> */}
                              </div>
                            </div>
                          </div>

                          {/* Table */}
                          <ProviderWhitelistTable
                            selectedItems={handleSelectedItems}
                            whitelist={whitelist}
                            key={refreshWhitelist}
                          />
                          {/* No elements exist */}
                          <div className="mr-6 flex justify-center pt-5 font-sans text-sm font-normal md:text-base lg:text-lg">
                            {totalWhiteListElements < 1 ? <div>No Records Exist</div> : ""}
                          </div>
                          {/* Pagination */}
                          <div className="mt-8">
                            <Pagination
                              totalElements={totalWhiteListElements}
                              totalPages={totalWhiteListPages}
                              pageNumber={pageWhiteListNumber}
                              OnPageNumberClick={handlePaginationChange}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="space-x-1"></div>

            {/* whitelist Multiple */}
            {/* Modal */}
            <ModalBasic
              id="feedback-modal"
              modalOpen={whitelistModalOpen}
              setModalOpen={setWhitelistModalOpen}
              title={` Whitelist Benefit(s) to provider - ${providerName} `}
            >
              {/* Modal content */}

              <div className="center mx-auto flex justify-center px-10 pb-1  pt-4">
                <h3 className="text-wrap mb-2 text-lg underline ">
                  Select Benefit(s) from List Below
                </h3>
              </div>
              <div className="center mx-auto mb-2 flex justify-center  px-10 pb-1 ">
                <h2
                  className="inline-flex rounded-full 
                          px-2.5 py-1 text-center
                           font-medium capitalize leading-9 text-gray-600"
                >
                  {names.length > 1 ? `Benefits Selected:` : ""}
                </h2>
                <h2
                  className="inline-flex rounded-full 
                          px-2.5 py-1 text-center
                           font-medium capitalize leading-9 text-blue-600"
                >
                  {names.length > 1 ? `${names}` : ""}
                </h2>
              </div>

              <div className="form-group mx-5 border-b-2 border-t-2 border-gray-100 px-10 ">
                <div
                  style={{
                    height: "350px",
                    overflow: "auto",
                    listStyleType: "none",
                    alignItems: "center",
                    scrollbarColor: "rebeccapurple",
                    paddingBottom: "5px",
                  }}
                  className="my-3 mb-10 rounded-md border-none"
                >
                  {benefitCatalog.map(({ name, id }, index) => (
                    <li key={index} className="mt-2 flex ">
                      <div className="left-section ml-10 justify-between">
                        <input
                          className="mr-2 "
                          type="checkbox"
                          id={`custom-checkbox-${index}`}
                          name={name}
                          value={name}
                          checked={checkedState[index]}
                          onChange={() => handleChange(id, name, index)}
                        />

                        <label
                          htmlFor={`custom-checkbox-${index}`}
                          className="inline-flex rounded-full bg-indigo-100 px-1.5 
                          text-center font-sans font-medium font-normal
                           capitalize leading-9 text-blue-600"
                        >
                          {name}
                        </label>
                      </div>
                    </li>
                  ))}
                </div>
              </div>

              {/* Modal footer */}
              <div className="mt-36 px-5 py-4">
                <div className="flex flex-wrap justify-center space-x-2">
                  <button
                    className="btn-sm w-44 border-gray-200 px-16 py-2 text-gray-600 hover:border-gray-300"
                    onClick={(e) => {
                      e.stopPropagation();
                      setWhitelistModalOpen(false);
                    }}
                  >
                    Clear
                  </button>
                  <button
                    type="submit"
                    className="btn-sm w-44 bg-blue-500 px-16 py-2 text-white hover:bg-blue-600"
                    onClick={(e) => {
                      handleSubmitBenefits(e);
                    }}
                  >
                    Save
                  </button>
                </div>
              </div>
            </ModalBasic>
          </div>
        </main>
      </div>
    </div>
  );
}
const mapStateToProps = (state) => ({
  success: state.payers.success,
  payers: state.payers.payers,
  loading: state.payers.loading,
});
const mapDispatchToProps = (dispatch) => ({
  mapBenefit: (benefitObject) => dispatch(mapBenefit(benefitObject)),
  getProviderExclusions: (policyId) => dispatch(getProviderExclusions(policyId)),
  getProviderCopays: (providerUrlId) => dispatch(getProviderCopays(providerUrlId)),

  getProviderWhitelist: (providerUrlId, page) =>
    dispatch(getProviderWhitelist(providerUrlId, page)),

  getBenefitCatalog2: () => dispatch(getBenefitCatalog2()),

  whitelistMultipleProviderBenefit: (provider) =>
    dispatch(whitelistMultipleProviderBenefit(provider)),
});
export default connect(mapStateToProps, mapDispatchToProps)(ProviderOverview);
