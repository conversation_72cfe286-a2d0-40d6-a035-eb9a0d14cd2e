// ReconciliationReport.test.tsx
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import ReconciliationReport from "./ReconciliationReport";
import { baseUrl } from "../../lib/Utils";
import { toast } from "react-toastify";

// Mock react-router-dom hooks
const mockNavigate = vi.fn();
vi.mock("react-router-dom", () => ({
  useNavigate: () => mockNavigate,
  useParams: () => ({ reconciliationId: "123" }),
}));

// Mock the API hook so we can control its output
import { useGetReconciledInvoicesQuery } from "../../api/reconciliation/reconciliationAPI";
vi.mock("../../api/reconciliation/reconciliationAPI", () => ({
  useGetReconciledInvoicesQuery: vi.fn(),
}));

// Mock child components to isolate ReconciliationReport
vi.mock("../../components/ui/MainWrapper", () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="main-wrapper">{children}</div>
  ),
}));

vi.mock("../../components/ui/typography/Text", () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="text">{children}</div>
  ),
}));

vi.mock("./ReconciliationTable", () => ({
  default: (props: any) => <div data-testid="reconciliation-table">ReconciliationTable</div>,
}));

vi.mock("../../components/ui/EmptyState", () => ({
  default: (props: any) => <div data-testid="empty-state">{props.message.title}</div>,
}));

vi.mock("../../assets/svg/NoClaimsVouchering", () => ({
  default: () => <div data-testid="no-claims-vouchering" />,
}));

// Mock toast from react-toastify
vi.mock("react-toastify", () => ({
  toast: {
    error: vi.fn(),
  },
}));

describe("ReconciliationReport", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders header with back button, title, and download link", () => {
    // Setup API hook with invoices (non-empty content)
    (useGetReconciledInvoicesQuery as vi.Mock).mockReturnValue({
      data: { data: { content: [{ id: 1 }], totalPages: 1, totalElements: 1 } },
      error: null,
      isFetching: false,
    });

    render(<ReconciliationReport />);

    // Verify back button exists
    const backButton = screen.getByRole("button");
    expect(backButton).toBeInTheDocument();

    // Verify title text is rendered
    expect(screen.getByText("Claim Reconciliation Process")).toBeInTheDocument();

    // Verify download link with correct href (using reconciliationId from useParams)
    const downloadLink = screen.getByRole("link", {
      name: /download report/i,
    });
    expect(downloadLink).toHaveAttribute(
      "href",
      `${baseUrl}/api/v1/visit/reconciled/report?reconciliationId=123`,
    );
  });

  it("calls navigate(-1) when back button is clicked", () => {
    (useGetReconciledInvoicesQuery as vi.Mock).mockReturnValue({
      data: { data: { content: [{ id: 1 }], totalPages: 1, totalElements: 1 } },
      error: null,
      isFetching: false,
    });

    render(<ReconciliationReport />);

    const backButton = screen.getByRole("button");
    fireEvent.click(backButton);
    expect(mockNavigate).toHaveBeenCalledWith(-1);
  });

  it("renders ReconciliationTable when invoices exist", () => {
    (useGetReconciledInvoicesQuery as vi.Mock).mockReturnValue({
      data: {
        data: { content: [{ id: 1 }, { id: 2 }], totalPages: 2, totalElements: 2 },
      },
      error: null,
      isFetching: false,
    });

    render(<ReconciliationReport />);
    expect(screen.getByTestId("reconciliation-table")).toBeInTheDocument();
  });

  it("renders EmptyState when there are no invoices", () => {
    (useGetReconciledInvoicesQuery as vi.Mock).mockReturnValue({
      data: { data: { content: [], totalPages: 0, totalElements: 0 } },
      error: null,
      isFetching: false,
    });

    render(<ReconciliationReport />);
    expect(screen.getByTestId("empty-state")).toBeInTheDocument();
    expect(screen.getByTestId("empty-state")).toHaveTextContent("No Reconciled Invoices");
  });

  it("calls toast.error when an error occurs", () => {
    const errorObject = new Error("Test error");
    (useGetReconciledInvoicesQuery as vi.Mock).mockReturnValue({
      data: undefined,
      error: errorObject,
      isFetching: false,
    });

    render(<ReconciliationReport />);
    expect(toast.error).toHaveBeenCalledWith("Error occurred while getting reconciled invoices");
  });
});
