import { XMarkIcon } from "@heroicons/react/24/outline";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { useGetMembershipProvidersQuery } from "../../api/features/membershipApi";
import { usePostClaimsReconciliationMutation } from "../../api/reconciliation/reconciliationAPI";
import Button from "../../components/ui/Button";
import AutoSuggestSelect from "../../components/ui/input/AutoSuggestSelect";
import SuccessModal from "../../components/ui/modal/SuccessModal";
import { AUTHENTICATED_USER } from "../../lib/payer-constants";
import { handleTryCatchError } from "../../utils/handleTryCatchError";
import FileTarget from "./FileTarget";
import ReconcilingDataModal from "./ReconcilingDataModal";

type Props = {
  onClose: () => void;
};

export default function ProviderStatementUpload({ onClose }: Props) {
  const [shouldShowSuccessModal, setShouldShowSuccessModal] = useState(false);
  const [selectedProviderId, setSelectedProviderId] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | undefined>();
  const [query, setQuery] = useState("");
  const navigate = useNavigate();

  const [uploadProviderStatement, { isLoading: isUploadingProviderStatement }] =
    usePostClaimsReconciliationMutation();

  const { data: providersData } = useGetMembershipProvidersQuery({
    payerId: String(AUTHENTICATED_USER.getPayerId()),
    query,
  });

  const providers = providersData?.data.content || [];
  const isFormValid = selectedFile && selectedProviderId;

  async function handleFileUpload(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    try {
      if (!isFormValid) {
        toast.error("File not selected!");
        return;
      }

      const result = await uploadProviderStatement({
        body: {
          file: selectedFile,
        },
        queryParams: {
          payerId: AUTHENTICATED_USER.getPayerId(),
          providerId: Number(selectedProviderId),
          reconciledBy: AUTHENTICATED_USER.getUserName(),
        },
      });

      if (result.error) {
        let errorMessage;

        if (
          "data" in result.error &&
          result.error.data instanceof Object &&
          "error" in result.error.data &&
          result.error.data.error instanceof String
        ) {
          errorMessage = result.error.data.error;
        } else {
          errorMessage = "Error uploading file!";
        }

        toast.error(errorMessage);
        console.error(result);
        return;
      }

      setShouldShowSuccessModal(true);

      const reconciliationId = result.data.data.id;

      setTimeout(() => {
        navigate(`/finance-and-accounting/reconciliation/report/${reconciliationId}`);
      }, 2500);
    } catch (error) {
      handleTryCatchError(error);
    }
  }

  return (
    <form className="relative p-6" onSubmit={handleFileUpload}>
      <button className="absolute right-6 top-4" onClick={onClose} type="button">
        <XMarkIcon className="w-5" strokeWidth={2} />
      </button>

      <h2 className="text-lg font-semibold text-[#030712]">Provider Statement Upload</h2>

      <section className="mt-4 rounded-lg p-4 shadow-md">
        <p className="font-medium text-[#1F2937]">Upload and attach files</p>
        <p className="mt-2 text-sm text-[#6B7280]">Upload and attach files for reconciliation.</p>

        <FileTarget file={selectedFile} setFile={setSelectedFile} />
      </section>

      <section>
        <p className="mt-6 text-sm font-medium text-[#374151]">
          <span>Select Provider Name</span>
        </p>
        <p className="mb-1 mt-2 text-xs font-medium text-[#374151]">
          <span>Provider</span> <span className="text-[#DC2626]">*</span>{" "}
        </p>
        <AutoSuggestSelect
          query={query}
          onQueryChange={setQuery}
          placeholder="Select a provider"
          options={providers.map((provider) => ({
            key: provider.id,
            label: provider.name,
            value: provider.id,
          }))}
          value={selectedProviderId}
          onChange={(value) => setSelectedProviderId(value as string)}
        />
      </section>

      <div className="mt-8 flex justify-end gap-8 ">
        <Button variant="outlined" onClick={onClose} type="button">
          Cancel
        </Button>
        <Button disabled={!isFormValid}>Submit</Button>
      </div>

      <ReconcilingDataModal isUploadingProviderStatement={isUploadingProviderStatement} />

      <SuccessModal
        title="Claims Reconciled Successfully!"
        description="All records have been successfully reconciled. Your data is now up-to-date and any discrepancies have been resolved. You can now proceed with the next steps."
        onClose={() => setShouldShowSuccessModal(false)}
        isSuccessModalOpen={shouldShowSuccessModal}
      />
    </form>
  );
}
