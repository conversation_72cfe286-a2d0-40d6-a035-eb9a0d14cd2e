import LoadingAnimation from "../../components/animations/LoadingAnimation/LoadingAnimation";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";

type Props = {
  isUploadingProviderStatement: boolean;
};
export default function ReconcilingDataModal({ isUploadingProviderStatement }: Props) {
  return (
    <DialogWrapper
      maxWidth="max-w-[650px]"
      show={isUploadingProviderStatement}
      onClose={() => null}
      className="overflow-y-visible"
    >
      <section className="flex flex-col items-center p-8">
        <LoadingAnimation size={80} />
        <h2 className="mt-8 text-2xl font-medium text-[#2563EB]">Reconciling Data...</h2>
        <p className="mt-5 text-center text-sm text-[#6B7280]">
          Please wait while we match your records. This process may take a few moments as we ensure
          everything is in order. Do not refresh or navigate away from this page until the
          reconciliation is complete.
        </p>
      </section>
    </DialogWrapper>
  );
}
