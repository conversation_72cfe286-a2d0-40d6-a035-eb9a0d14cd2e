import { CloudArrowUpIcon, XCircleIcon } from "@heroicons/react/24/outline";
import React, { useState } from "react";
import { toast } from "react-toastify";

type Props = {
  file: File | undefined;
  setFile: React.Dispatch<React.SetStateAction<File | undefined>>;
};

export default function FileTarget({ file, setFile }: Props) {
  const [isFileOverTarget, setIsFileOverTarget] = useState(false);

  function handleFileSelection(e: React.ChangeEvent<HTMLInputElement>) {
    const selectedFile = e.target.files?.[0];

    if (!selectedFile) {
      toast.error("File not selected!");
      return;
    }

    setFile(selectedFile);
  }

  function handleFileDragOver(event: React.DragEvent<HTMLDivElement>) {
    event.preventDefault();
    setIsFileOverTarget(true);
  }

  function handleFileDragLeave() {
    setIsFileOverTarget(false);
  }

  function handleFileDrop(event: React.DragEvent<HTMLDivElement>) {
    event.preventDefault();
    const droppedFile = event.dataTransfer.files?.[0];

    if (!droppedFile) {
      toast.error("File not selected!");
      return;
    }

    setIsFileOverTarget(false);
    setFile(droppedFile);
  }

  function clearFile() {
    setFile(undefined);
  }

  return (
    <div
      onDragOver={handleFileDragOver}
      onDragLeave={handleFileDragLeave}
      onDrop={handleFileDrop}
      className={`mt-4 rounded-lg border p-2 text-center ${isFileOverTarget && "border-txtBlue bg-faintBlue shadow-lg"}`}
    >
      {file && (
        <div className="flex items-center justify-center p-8">
          <span>{file.name}</span>
          <button className="ml-8 text-sm text-red-500" type="button" onClick={clearFile}>
            <XCircleIcon className="w-5" />
          </button>
        </div>
      )}

      {!file && (
        <>
          <div
            className={`mx-auto mb-4 w-fit rounded-3xl p-4 ${!isFileOverTarget && "bg-[#F5F5F5]"}`}
          >
            <CloudArrowUpIcon strokeWidth={0.3} className="mx-auto w-16 text-[#374151]" />
          </div>

          <label htmlFor="file-input" className=" cursor-pointer text-xs text-[#6B7280]">
            <span className="text-[#1D4ED8]">Click to select file</span> or drag and drop
          </label>

          <input
            className="hidden"
            id="file-input"
            type="file"
            accept=".xls, .xlsx"
            value={file}
            onChange={handleFileSelection}
          />

          <p className="mt-1 text-xs text-[#6B7280]"> .xls, .xlsx</p>
        </>
      )}
    </div>
  );
}
