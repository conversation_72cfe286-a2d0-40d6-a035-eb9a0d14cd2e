import { ChevronDownIcon, CloudArrowDownIcon, CloudArrowUpIcon } from "@heroicons/react/24/outline";
import { useState } from "react";
import NoClaimsVouchering from "../../components/illustrations/NoClaimsVouchering";
import MenuDropdown from "../../components/MenuDropdown";
import EmptyState from "../../components/ui/EmptyState";
import MainWrapper from "../../components/ui/MainWrapper";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import Text from "../../components/ui/typography/Text";
import { baseUrl } from "../../lib/Utils";
import ProviderStatementUpload from "./ProviderStatementUpload";

export default function Reconciliation() {
  const [shouldShowUploadModal, setShouldShowUploadModal] = useState(false);

  function openModal() {
    setShouldShowUploadModal(true);
  }

  function closeModal() {
    setShouldShowUploadModal(false);
  }

  const reconTemplateDownloadUrl = `${baseUrl}/api/v1/claim/template/download?templateType=RECONCILIATION`;

  return (
    <MainWrapper>
      <header className="flex justify-between">
        <Text variant="heading" className="text-lg">
          Claim Reconciliation Process
        </Text>

        <MenuDropdown
          buttonChild={
            <div className="flex w-fit items-center justify-center gap-2 rounded-md bg-btnBlue px-3 py-2 text-xs font-medium text-white">
              <span>Select action</span>
              <ChevronDownIcon className="h-4 w-4" />
            </div>
          }
          options={[
            <a href={reconTemplateDownloadUrl} className="flex items-center gap-4">
              <CloudArrowDownIcon className="w-5" /> <span>Download reconciliation template</span>
            </a>,
            <button className="flex items-center gap-4" onClick={openModal}>
              <CloudArrowUpIcon className="w-5" /> <span>Upload reconciliation template</span>
            </button>,
          ]}
        />
      </header>

      <DialogWrapper maxWidth="max-w-[650px]" onClose={closeModal} show={shouldShowUploadModal}>
        <ProviderStatementUpload onClose={() => setShouldShowUploadModal(false)} />
      </DialogWrapper>

      <EmptyState
        illustration={<NoClaimsVouchering />}
        message={{
          title: "No claims to reconcile",
          description:
            "There are currently no claims available for reconciliation. To get started, please upload the provider's statement, and the system will generate a detailed reconciliation report that you can download for review.",
        }}
      />
    </MainWrapper>
  );
}
