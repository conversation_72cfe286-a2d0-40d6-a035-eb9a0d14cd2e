// ReconciliationTable.test.tsx
import React from "react";
import { render, screen } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import ReconciliationTable from "./ReconciliationTable";

// --- Mocks ---
// Mock LoadingAnimation so we can verify the loading state.
vi.mock("../../components/animations/LoadingAnimation/LoadingAnimation", () => ({
  default: ({ size }: { size: number }) => (
    <div data-testid="loading-animation">Loading {size}</div>
  ),
}));

// Mock Badge to simply render its text and color.
vi.mock("../../components/ui/Badge", () => ({
  default: ({ text, color }: { text: string; color: string; hasDot: boolean }) => (
    <span data-testid="badge" data-color={color}>
      {text}
    </span>
  ),
}));

// Mock PrimaryPagination to render a placeholder.
vi.mock("../../components/ui/pagination/PrimaryPagination", () => ({
  default: (props: any) => <div data-testid="primary-pagination">Pagination</div>,
}));

// Mock TableDataItem to render a table cell.
vi.mock("../../components/ui/table/TableDataItem", () => ({
  default: ({ item }: { item: string }) => <td data-testid="table-data-item">{item}</td>,
}));

// Mock TableHeaderItem to render a table header cell.
vi.mock("../../components/ui/table/TableHeaderItem", () => ({
  default: ({ item }: { item: string }) => <th data-testid="table-header-item">{item}</th>,
}));

// Mock utility functions to return predictable values.
vi.mock("../../utils/formatCurrency", () => ({
  formatNumberToKes: (num: number) => `KES ${num}`,
}));

vi.mock("../../utils/convertDateString", () => ({
  convertDateString: (date: string) => `Converted ${date}`,
}));

// --- Sample Data ---
const sampleInvoices = [
  {
    memberNumber: "001",
    memberName: "John Doe",
    invoiceNumber: "INV-123",
    amount: 1000,
    paymentDate: "2022-01-01",
    payableAmount: 950,
    paymentStatus: "PAID",
  },
  {
    memberNumber: "002",
    memberName: "Jane Smith",
    invoiceNumber: "INV-124",
    amount: 2000,
    // Payment date is falsy so it should render "-"
    paymentDate: "",
    payableAmount: 1900,
    paymentStatus: "NOT_PAID",
  },
];

describe("ReconciliationTable", () => {
  const onSizeChangeMock = vi.fn();
  const onPageNumberClickMock = vi.fn();

  it("renders loading animation when invoices are being fetched", () => {
    render(
      <ReconciliationTable
        invoices={[]}
        totalElements={0}
        totalPages={0}
        pageNumber={1}
        pageSize={10}
        onSizeChange={onSizeChangeMock}
        onPageNumberClick={onPageNumberClickMock}
        isFetchingInvoices={true}
      />,
    );
    // Verify the loading animation is shown.
    expect(screen.getByTestId("loading-animation")).toBeInTheDocument();
    // Table header items should not be rendered.
    expect(screen.queryByTestId("table-header-item")).not.toBeInTheDocument();
    // PrimaryPagination should still be rendered.
    expect(screen.getByTestId("primary-pagination")).toBeInTheDocument();
  });

  it("renders table with invoice data when not fetching", () => {
    render(
      <ReconciliationTable
        invoices={sampleInvoices}
        totalElements={2}
        totalPages={1}
        pageNumber={1}
        pageSize={10}
        onSizeChange={onSizeChangeMock}
        onPageNumberClick={onPageNumberClickMock}
        isFetchingInvoices={false}
      />,
    );

    // --- Verify table header items ---
    const headerItems = screen.getAllByTestId("table-header-item");
    expect(headerItems).toHaveLength(7);
    expect(headerItems[0]).toHaveTextContent("MEMBER NUMBER");
    expect(headerItems[1]).toHaveTextContent("MEMBER NAME");
    expect(headerItems[2]).toHaveTextContent("INVOICE NUMBER");
    expect(headerItems[3]).toHaveTextContent("INVOICE AMOUNT");
    expect(headerItems[4]).toHaveTextContent("PAYMENT DATE");
    expect(headerItems[5]).toHaveTextContent("PAYABLE AMOUNT");
    expect(headerItems[6]).toHaveTextContent("STATUS");

    // --- Verify table data cells ---
    // There are 6 data cells per invoice (the badge is rendered separately).
    const dataItems = screen.getAllByTestId("table-data-item");

    // For the first invoice
    expect(dataItems[0]).toHaveTextContent("001");
    expect(dataItems[1]).toHaveTextContent("John Doe");
    expect(dataItems[2]).toHaveTextContent("INV-123");
    expect(dataItems[3]).toHaveTextContent("KES 1000");
    expect(dataItems[4]).toHaveTextContent("Converted 2022-01-01");
    expect(dataItems[5]).toHaveTextContent("KES 950");

    // For the second invoice
    expect(dataItems[6]).toHaveTextContent("002");
    expect(dataItems[7]).toHaveTextContent("Jane Smith");
    expect(dataItems[8]).toHaveTextContent("INV-124");
    expect(dataItems[9]).toHaveTextContent("KES 2000");
    // When paymentDate is falsy, it should display "-"
    expect(dataItems[10]).toHaveTextContent("-");
    expect(dataItems[11]).toHaveTextContent("KES 1900");

    // --- Verify Badge rendering ---
    const badges = screen.getAllByTestId("badge");
    // For first invoice (PAID)
    expect(badges[0]).toHaveTextContent("Paid");
    expect(badges[0]).toHaveAttribute("data-color", "green");
    // For second invoice (NOT_PAID)
    expect(badges[1]).toHaveTextContent("Not paid");
    expect(badges[1]).toHaveAttribute("data-color", "red");

    // Loading animation should not be rendered.
    expect(screen.queryByTestId("loading-animation")).not.toBeInTheDocument();

    // PrimaryPagination should be rendered.
    expect(screen.getByTestId("primary-pagination")).toBeInTheDocument();
  });
});
