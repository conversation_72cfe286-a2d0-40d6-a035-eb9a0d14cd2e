// Reconciliation.test.tsx
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import Reconciliation from "./Reconciliation";
import { baseUrl } from "../../lib/Utils";

// --- Mocks ---

// MainWrapper simply renders its children.
vi.mock("../../components/ui/MainWrapper", () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="main-wrapper">{children}</div>
  ),
}));

// Text renders its children.
vi.mock("../../components/ui/typography/Text", () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="text">{children}</div>
  ),
}));

// MenuDropdown renders the buttonChild and the options.
vi.mock("../../components/MenuDropdown", () => ({
  default: ({
    buttonChild,
    options,
  }: {
    buttonChild: React.ReactNode;
    options: React.ReactNode[];
  }) => (
    <div data-testid="menu-dropdown">
      {buttonChild}
      <div data-testid="menu-options">{options}</div>
    </div>
  ),
}));

// DialogWrapper renders its children only when "show" is true and provides a close button.
vi.mock("../../components/ui/modal/DialogWrapper", () => ({
  default: ({
    children,
    show,
    onClose,
  }: {
    children: React.ReactNode;
    show: boolean;
    onClose: () => void;
  }) =>
    show ? (
      <div data-testid="dialog-wrapper">
        {children}
        <button onClick={onClose} data-testid="dialog-close">
          Close
        </button>
      </div>
    ) : null,
}));

// ProviderStatementUpload renders a dummy element with a close button.
vi.mock("./ProviderStatementUpload", () => ({
  default: ({ onClose }: { onClose: () => void }) => (
    <div data-testid="provider-statement-upload">
      ProviderStatementUpload
      <button onClick={onClose} data-testid="upload-close">
        Upload Close
      </button>
    </div>
  ),
}));

// EmptyState renders its message.
vi.mock("../../components/ui/EmptyState", () => ({
  default: ({
    illustration,
    message,
  }: {
    illustration: React.ReactNode;
    message: { title: string; description: string };
  }) => (
    <div data-testid="empty-state">
      {message.title}
      {message.description}
    </div>
  ),
}));

// NoClaimsVouchering renders a dummy illustration.
vi.mock("../../assets/svg/NoClaimsVouchering", () => ({
  default: () => <div data-testid="no-claims-vouchering">NoClaimsVouchering</div>,
}));

describe("Reconciliation Component", () => {
  it("renders header with title and download link", () => {
    render(<Reconciliation />);
    // Verify header title text.
    expect(screen.getByTestId("text")).toHaveTextContent("Claim Reconciliation Process");

    // Verify the MenuDropdown renders the download link with the correct href.
    const downloadLink = screen.getByRole("link", {
      name: /Download reconciliation template/i,
    });
    expect(downloadLink).toHaveAttribute(
      "href",
      `${baseUrl}/api/v1/claim/template/download?templateType=RECONCILIATION`,
    );
  });

  it("opens the upload modal when 'Upload reconciliation template' is clicked", () => {
    render(<Reconciliation />);
    // Initially, the DialogWrapper (upload modal) should not be rendered.
    expect(screen.queryByTestId("dialog-wrapper")).not.toBeInTheDocument();

    // Find the upload button inside the MenuDropdown options.
    const uploadButton = screen.getByRole("button", {
      name: /Upload reconciliation template/i,
    });
    fireEvent.click(uploadButton);

    // After clicking, the DialogWrapper and ProviderStatementUpload should be rendered.
    expect(screen.getByTestId("dialog-wrapper")).toBeInTheDocument();
    expect(screen.getByTestId("provider-statement-upload")).toBeInTheDocument();
  });

  it("closes the upload modal when the close button is clicked", () => {
    render(<Reconciliation />);
    // Open the modal first.
    const uploadButton = screen.getByRole("button", {
      name: /Upload reconciliation template/i,
    });
    fireEvent.click(uploadButton);
    expect(screen.getByTestId("dialog-wrapper")).toBeInTheDocument();

    // Click the close button in the DialogWrapper.
    const closeButton = screen.getByTestId("dialog-close");
    fireEvent.click(closeButton);
    expect(screen.queryByTestId("dialog-wrapper")).not.toBeInTheDocument();
  });

  it("renders EmptyState with the correct message", () => {
    render(<Reconciliation />);
    // Verify that EmptyState is rendered with the expected title and description.
    const emptyState = screen.getByTestId("empty-state");
    expect(emptyState).toHaveTextContent("No claims to reconcile");
    expect(emptyState).toHaveTextContent(
      "There are currently no claims available for reconciliation. To get started, please upload the provider's statement, and the system will generate a detailed reconciliation report that you can download for review.",
    );
  });
});
