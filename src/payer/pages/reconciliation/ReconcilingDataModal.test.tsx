// ReconcilingDataModal.test.tsx
import React from "react";
import { render, screen } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import ReconcilingDataModal from "./ReconcilingDataModal";

// Mock the LoadingAnimation component to verify that it is rendered with the correct props.
vi.mock("../../components/animations/LoadingAnimation/LoadingAnimation", () => ({
  default: ({ size }: { size: number }) => (
    <div data-testid="loading-animation">LoadingAnimation {size}</div>
  ),
}));

// Mock the DialogWrapper component to conditionally render children based on the "show" prop.
vi.mock("../../components/ui/modal/DialogWrapper", () => ({
  default: ({
    children,
    show,
    maxWidthRem,
    onClose,
    className,
  }: {
    children: React.ReactNode;
    show: boolean;
    maxWidthRem: string;
    onClose: () => void;
    className: string;
  }) =>
    show ? (
      <div data-testid="dialog-wrapper" className={`${maxWidthRem} ${className}`}>
        {children}
      </div>
    ) : null,
}));

describe("ReconcilingDataModal", () => {
  it("renders modal content when isUploadingProviderStatement is true", () => {
    render(<ReconcilingDataModal isUploadingProviderStatement={true} />);
    // Verify that the DialogWrapper renders its children.
    expect(screen.getByTestId("dialog-wrapper")).toBeInTheDocument();
    // Verify that the LoadingAnimation is rendered with the expected size.
    expect(screen.getByTestId("loading-animation")).toHaveTextContent("LoadingAnimation 80");
    // Check for the modal title and instructions.
    expect(screen.getByText("Reconciling Data...")).toBeInTheDocument();
    expect(screen.getByText(/Please wait while we match your records/i)).toBeInTheDocument();
  });

  it("does not render modal content when isUploadingProviderStatement is false", () => {
    render(<ReconcilingDataModal isUploadingProviderStatement={false} />);
    // The DialogWrapper should render null when show is false.
    expect(screen.queryByTestId("dialog-wrapper")).not.toBeInTheDocument();
    expect(screen.queryByText("Reconciling Data...")).not.toBeInTheDocument();
  });
});
