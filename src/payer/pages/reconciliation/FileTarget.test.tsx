// FileTarget.test.tsx
import React from "react";
import { render, fireEvent, screen } from "@testing-library/react";
import { describe, it, expect, beforeEach, vi, Mock } from "vitest";
import FileTarget from "./FileTarget";
import { toast } from "react-toastify";

// Mock the toast so we can verify error messages
vi.mock("react-toastify", () => ({
  toast: {
    error: vi.fn(),
  },
}));

describe("FileTarget Component", () => {
  const mockSetFile = vi.fn();

  beforeEach(() => {
    mockSetFile.mockClear();
    (toast.error as Mock).mockClear();
  });

  it("renders file selection instructions when no file is provided", () => {
    render(<FileTarget file={undefined} setFile={mockSetFile} />);
    expect(screen.getByText(/click to select file/i)).toBeInTheDocument();
    expect(screen.getByText(/drag and drop/i)).toBeInTheDocument();
  });

  it("calls setFile on valid file selection via input", () => {
    render(<FileTarget file={undefined} setFile={mockSetFile} />);
    // Get the hidden input by its id
    const input = document.getElementById("file-input") as HTMLInputElement;
    // Create a fake file
    const file = new File(["dummy content"], "dummy.xlsx", {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    fireEvent.change(input, { target: { files: [file] } });
    expect(mockSetFile).toHaveBeenCalledWith(file);
  });

  it("shows a toast error if no file is selected via input", () => {
    render(<FileTarget file={undefined} setFile={mockSetFile} />);
    const input = document.getElementById("file-input") as HTMLInputElement;
    // Simulate a change event with no file selected
    fireEvent.change(input, { target: { files: [] } });
    expect(toast.error).toHaveBeenCalledWith("File not selected!");
    expect(mockSetFile).not.toHaveBeenCalled();
  });

  it("handles file drop with a valid file", () => {
    render(<FileTarget file={undefined} setFile={mockSetFile} />);
    // Get the drop zone (the outer container)
    const dropZone = document.querySelector("div.mt-4") as HTMLElement;
    const file = new File(["dummy content"], "dummy.xlsx", {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    const dataTransfer = { files: [file], getData: vi.fn() };
    fireEvent.drop(dropZone, { dataTransfer });
    expect(mockSetFile).toHaveBeenCalledWith(file);
  });

  it("shows a toast error if no file is dropped", () => {
    render(<FileTarget file={undefined} setFile={mockSetFile} />);
    const dropZone = document.querySelector("div.mt-4") as HTMLElement;
    fireEvent.drop(dropZone, { dataTransfer: { files: [] } });
    expect(toast.error).toHaveBeenCalledWith("File not selected!");
    expect(mockSetFile).not.toHaveBeenCalled();
  });

  it("clears the file when the clear button is clicked", () => {
    // Render with a file already provided
    const file = new File(["dummy content"], "dummy.xlsx", {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    render(<FileTarget file={file} setFile={mockSetFile} />);
    // The clear button is rendered as a button element
    const clearButton = screen.getByRole("button");
    fireEvent.click(clearButton);
    expect(mockSetFile).toHaveBeenCalledWith(undefined);
  });

  it("adds and removes drag-over styling on drag events", () => {
    const { container } = render(<FileTarget file={undefined} setFile={mockSetFile} />);
    // The outer container is the first child
    const outerDiv = container.firstChild as HTMLElement;
    // Simulate drag over
    fireEvent.dragOver(outerDiv);
    expect(outerDiv.className).toMatch(/border-txtBlue/);
    // Simulate drag leave
    fireEvent.dragLeave(outerDiv);
    expect(outerDiv.className).not.toMatch(/border-txtBlue/);
  });
});
