import { ReconciledInvoice } from "../../api/reconciliation/reconciliation-request-types";
import LoadingAnimation from "../../components/animations/LoadingAnimation/LoadingAnimation";
import Badge, { BadgeColor } from "../../components/ui/Badge";
import PrimaryPagination from "../../components/ui/pagination/PrimaryPagination";
import TableDataItem from "../../components/ui/table/TableDataItem";
import TableHeaderItem from "../../components/ui/table/TableHeaderItem";
import { convertDateString } from "../../utils/convertDateString";
import { formatNumberToKes } from "../../utils/formatCurrency";

type Props = {
  invoices: ReconciledInvoice[];
  totalPages: number;
  totalElements: number;
  pageSize: number;
  pageNumber: number;
  onSizeChange: (size: number) => void;
  onPageNumberClick: (page: number) => void;
  isFetchingInvoices: boolean;
};

type PaymentStatus = "PAID" | "NOT_PAID";

export default function ReconciliationTable({
  invoices,
  totalElements,
  totalPages,
  pageNumber,
  pageSize,
  onPageNumberClick,
  onSizeChange,
  isFetchingInvoices,
}: Props) {
  function getBadgeColor(status: PaymentStatus): BadgeColor {
    if (status === "PAID") return "green";
    if (status === "NOT_PAID") return "red";
    return "gray";
  }

  function getBadgeText(status: PaymentStatus): string {
    if (status === "PAID") return "Paid";
    if (status === "NOT_PAID") return "Not paid";
    return status;
  }
  return (
    <>
      {!isFetchingInvoices ? (
        <table>
          <thead>
            <tr className="bg-[#F9FAFB]">
              <TableHeaderItem item="MEMBER NUMBER" />
              <TableHeaderItem item="MEMBER NAME" />
              <TableHeaderItem item="INVOICE NUMBER" />
              <TableHeaderItem item="INVOICE AMOUNT" />
              <TableHeaderItem item="PAYMENT DATE" />
              <TableHeaderItem item="PAYABLE AMOUNT" />
              <TableHeaderItem item="STATUS" />
            </tr>
          </thead>

          <tbody>
            {invoices.map((invoice, i) => (
              <tr key={i}>
                <TableDataItem item={invoice.memberNumber} />
                <TableDataItem item={invoice.memberName || "-"} />
                <TableDataItem item={invoice.invoiceNumber} />
                <TableDataItem item={formatNumberToKes(invoice.amount)} />
                <TableDataItem
                  item={invoice?.paymentDate ? convertDateString(invoice?.paymentDate) : "-"}
                />
                <TableDataItem item={formatNumberToKes(invoice.payableAmount)} />
                <td className="border-b border-[#EAECF0] px-4 py-2">
                  <Badge
                    hasDot
                    text={getBadgeText(invoice.paymentStatus) || "-"}
                    color={getBadgeColor(invoice.paymentStatus as PaymentStatus)}
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      ) : (
        <div className="flex items-center justify-center w-full h-full">
          <LoadingAnimation size={80} />
        </div>
      )}

      <PrimaryPagination
        onPageNumberClick={onPageNumberClick}
        onSizeChange={onSizeChange}
        pageNumber={pageNumber}
        pageSize={pageSize}
        totalElements={totalElements}
        totalPages={totalPages}
      />
    </>
  );
}
