import { ArrowLeftIcon, CloudArrowDownIcon } from "@heroicons/react/24/outline";
import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import { useGetReconciledInvoicesQuery } from "../../api/reconciliation/reconciliationAPI";
import NoClaimsVouchering from "../../components/illustrations/NoClaimsVouchering";
import EmptyState from "../../components/ui/EmptyState";
import MainWrapper from "../../components/ui/MainWrapper";
import Text from "../../components/ui/typography/Text";
import { baseUrl } from "../../lib/Utils";
import ReconciliationTable from "./ReconciliationTable";

export default function ReconciliationReport() {
  const navigate = useNavigate();
  const { reconciliationId } = useParams();

  const [page, setPage] = useState(1);
  const [size, setSize] = useState(10);

  const {
    data: invoicesResult,
    error,
    isFetching: isFetchingInvoices,
  } = useGetReconciledInvoicesQuery({
    queryParams: {
      reconciliationId: Number(reconciliationId),
      page,
      size,
    },
  });

  function handleBackButtonClick() {
    navigate(-1);
  }

  if (error) {
    toast.error("Error occurred while getting reconciled invoices");
    console.error(error);
  }

  const invoices = invoicesResult?.data?.content || [];
  const totalPages = invoicesResult?.data.totalPages;
  const totalElements = invoicesResult?.data.totalElements;

  const downloadUrl = `${baseUrl}/api/v1/visit/reconciled/report?reconciliationId=${reconciliationId}`;

  return (
    <MainWrapper>
      <header className="mb-4 flex items-center justify-between">
        <div className="flex items-center gap-8">
          <button className="rounded-lg bg-[#E5E7EB] p-2" onClick={handleBackButtonClick}>
            <ArrowLeftIcon className="w-4" strokeWidth={2} />
          </button>
          <Text variant="heading">Claim Reconciliation Process</Text>
        </div>

        <a
          className={`flex w-fit items-center justify-center gap-2 rounded-md bg-btnBlue px-3 py-[6px] text-xs font-medium text-white`}
          href={downloadUrl}
        >
          <CloudArrowDownIcon className="w-5" /> <span>Download report</span>
        </a>
      </header>

      {invoices.length > 0 ? (
        <ReconciliationTable
          invoices={invoices}
          totalElements={totalElements as number}
          totalPages={totalPages as number}
          pageSize={size}
          pageNumber={page}
          onPageNumberClick={(value) => setPage(value)}
          onSizeChange={(value) => setSize(value)}
          isFetchingInvoices={isFetchingInvoices}
        />
      ) : (
        <EmptyState
          illustration={<NoClaimsVouchering />}
          message={{
            title: "No Reconciled Invoices",
            description: "There are currently no reconciled invoices. ",
          }}
        />
      )}
    </MainWrapper>
  );
}
