import React from "react";

export const ContactUs: React.FC = () => {
  return (
    <div className="flex flex-grow h-full overflow-hidden bg-gray-50">
      {/* Sidebar */}
      <div className="flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
        {/*  Site header */}
        <main className="justify-center p-4 lg:p-8">
          <div className="flex flex-col min-w-0 break-words w-full bg-white rounded-md shadow-md pt-10">
            <article className="px-8 pb-8">
              <hgroup className="mb-8">
                <h1 className="text-2xl text-center font-bold mb-2 text-gray-600">Contact Us</h1>
              </hgroup>

              <section>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 pb-4">
                  <div className="flex flex-col items-center gap-2">
                    <div className="bg-blue-50 rounded-lg p-4 mb-2">
                      {/* prettier-ignore */}
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-6 h-6 text-blue-500">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                      </svg>
                    </div>

                    <div className="text-center">
                      <h2 className="text-xl font-bold mb-4">Email Us</h2>

                      <p className="text-gray-500 mb-2">
                        Email us for general enquiries, pre-auth requests and reimbursement
                        requests.
                      </p>

                      <p>
                        <a
                          href="mailto:<EMAIL>"
                          className="font-bold text-blue-500"
                          target="_blank"
                          rel="noreferrer"
                        >
                          <EMAIL>
                        </a>
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-col items-center gap-2">
                    <div className="bg-blue-50 rounded-lg p-4 mb-2">
                      {/* prettier-ignore */}
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-6 h-6 text-blue-500">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
                      </svg>
                    </div>

                    <div className="text-center">
                      <h2 className="text-xl font-bold mb-4">Call Us</h2>

                      <p className="text-gray-500 mb-2">
                        Call us to speak to a member of our contact center.
                      </p>

                      <p>
                        <a
                          href="tel:+254703071333"
                          className="font-bold text-blue-500"
                          target="_blank"
                          rel="noreferrer"
                        >
                          +254 703 071 333
                        </a>
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-col items-center gap-2">
                    <div className="bg-blue-50 rounded-lg p-4 mb-2">
                      {/* prettier-ignore */}
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-6 h-6 text-blue-500">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
                      </svg>
                    </div>

                    <div className="text-center">
                      <h2 className="text-xl font-bold mb-4">Visit Us</h2>

                      <p className="text-gray-500 mb-2">
                        Visit our offices for one-on-one chat. We are open from 9 am to 5 pm.
                      </p>

                      <p>
                        <a
                          href="https://www.google.com/maps/place/Liason+House,+State+House+Avenue,+Nairobi/@-1.2869939,36.8058074,17z/data=!3m1!4b1!4m6!3m5!1s0x182f10c8b0fdc5d5:0xa462f20c35ffe969!8m2!3d-1.2869993!4d36.8083877!16s%2Fg%2F12hncbjms?entry=ttu"
                          className="font-bold text-blue-500"
                          target="_blank"
                          rel="noreferrer"
                        >
                          3<sup>rd</sup> Floor, Liaison House <br />
                          State House Avenue, Nairobi
                        </a>
                      </p>
                    </div>
                  </div>
                </div>
              </section>
            </article>
          </div>
        </main>
      </div>
    </div>
  );
};

export default ContactUs;
