import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, it, expect, vi, beforeEach } from "vitest";
import VoucherDetailsRow from "./VoucherDetailsRow";

// Dummy voucher object.
const dummyVoucher = {
  id: "123",
  voucherNo: "VOUCHER-001",
  amount: 5000,
  discount: 500,
  payableAmount: 4500,
  createdOn: "2023-01-01T00:00:00Z",
  createdBy: { userName: "<PERSON> Doe" },
};

// --- Mocks ---
// Mock DialogModal to render its children when open.
vi.mock("../../components/DialogModal", () => ({
  default: (props: { isOpen: boolean; setIsOpen: any; children: any }) =>
    props.isOpen ? <div data-testid="dialog-modal">{props.children}</div> : null,
}));

// Stub TableDataItem to simply render its item.
vi.mock("../../components/ui/table/TableDataItem", () => ({
  default: (props: { item: any }) => <td data-testid="table-data-item">{props.item}</td>,
}));

// Stub EyeIcon.
vi.mock("../../components/icons/EyeIcon", () => ({
  default: (props: any) => <span data-testid="eye-icon">EyeIcon</span>,
}));

// Stub InvoiceDetails to indicate it was rendered.
vi.mock("./InvoiceDetails", () => ({
  default: (props: { voucherId: string }) => (
    <div data-testid="invoice-details">InvoiceDetails for voucher {props.voucherId}</div>
  ),
}));

// Stub formatNumberToKes to simply prepend "KES ".
vi.mock("../../utils/formatCurrency", () => ({
  formatNumberToKes: (num: number) => `KES ${num}`,
}));

describe("VoucherDetailsRow Component", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders voucher details in a table row", () => {
    // Render inside a table since VoucherDetailsRow returns a <tr>.
    render(
      <table>
        <tbody>
          <VoucherDetailsRow voucher={dummyVoucher} />
        </tbody>
      </table>,
    );

    // Check that the voucher number is rendered.
    expect(screen.getByText("VOUCHER-001")).toBeInTheDocument();
    // Check that the amounts are formatted.
    expect(screen.getByText("KES 5000")).toBeInTheDocument();
    expect(screen.getByText("KES 500")).toBeInTheDocument();
    expect(screen.getByText("KES 4500")).toBeInTheDocument();
    // Check the formatted date. In en-GB, new Date("2023-01-01T00:00:00Z") formats to "1 January 2023".
    expect(screen.getByText("1 January 2023")).toBeInTheDocument();
    // Check the creator's user name.
    expect(screen.getByText("John Doe")).toBeInTheDocument();
  });

  it("opens InvoiceDetails modal when the EyeIcon button is clicked", async () => {
    render(
      <table>
        <tbody>
          <VoucherDetailsRow voucher={dummyVoucher} />
        </tbody>
      </table>,
    );

    // Find the button wrapping the EyeIcon.
    const eyeIcon = screen.getByTestId("eye-icon");
    const button = eyeIcon.closest("button");
    expect(button).toBeInTheDocument();

    // Click the button to open the modal.
    if (button) {
      await userEvent.click(button);
    }

    // Wait for the DialogModal to be rendered and verify that InvoiceDetails is shown.
    await waitFor(() => {
      expect(screen.getByTestId("dialog-modal")).toBeInTheDocument();
      expect(screen.getByTestId("invoice-details")).toHaveTextContent(
        "InvoiceDetails for voucher 123",
      );
    });
  });
});
