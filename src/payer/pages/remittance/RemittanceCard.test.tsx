import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, it, expect, vi, beforeAll, beforeEach, afterEach } from "vitest";
import RemittanceCard from "./RemittanceCard";
import { baseUrl } from "../../lib/Utils";
import { toast } from "react-toastify";

// Polyfill for URL.createObjectURL in jsdom
beforeAll(() => {
  if (!window.URL.createObjectURL) {
    window.URL.createObjectURL = vi.fn(() => "blob:url");
  }
  if (!window.URL.revokeObjectURL) {
    window.URL.revokeObjectURL = vi.fn();
  }
});

// --- Mocks for child components ---
vi.mock("../../components/DialogModal", () => ({
  default: (props: { isOpen: boolean; setIsOpen: any; children: any }) =>
    props.isOpen ? <div data-testid="dialog-modal">{props.children}</div> : null,
}));

vi.mock("../../components/MenuDropdown", () => ({
  default: (props: { options: React.ReactNode[] }) => <div>{props.options}</div>,
}));

vi.mock("../../components/ui/CardWrapper", () => ({
  default: (props: { children: any }) => <div>{props.children}</div>,
}));

vi.mock("../../components/icons/DotIcon", () => ({
  default: () => <span data-testid="dot-icon">.</span>,
}));

vi.mock("../../components/icons/EyeIcon", () => ({
  default: (props: { size: number }) => <span data-testid="eye-icon">EyeIcon</span>,
}));

vi.mock("../../components/icons/DownloadIcon", () => ({
  default: (props: { fill: string; size: number }) => (
    <span data-testid="download-icon">DownloadIcon</span>
  ),
}));

vi.mock("./VoucherDetails", () => ({
  default: ({ remittance }: { remittance: any }) => (
    <div data-testid="voucher-details">Voucher Details for {remittance.paymentReference}</div>
  ),
}));

// --- Sample prop ---
const sampleRemittance = {
  id: "12345",
  paymentReference: "REF12345",
  account: [{ accountName: "John Doe" }],
  amount: 5000,
  createdOn: "2025-02-26T10:00:00",
  createdBy: { userName: "Tester" },
};

describe("RemittanceCard", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Ensure global.fetch is available and mocked
    global.fetch = vi.fn();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("renders remittance information correctly", () => {
    render(<RemittanceCard remittance={sampleRemittance} />);
    expect(screen.getByText(sampleRemittance.paymentReference)).toBeInTheDocument();
    expect(screen.getByText(/Account Name:/i)).toBeInTheDocument();
    expect(screen.getByText("John Doe")).toBeInTheDocument();
    expect(screen.getByText(/Amount Paid:/i)).toBeInTheDocument();
    // Assuming formatNumberToKes formats 5000 to a string containing "5,000"
    expect(screen.getByText(/5,000/)).toBeInTheDocument();
    expect(screen.getByText(/Paid by:/i)).toBeInTheDocument();
    expect(screen.getByText("Tester")).toBeInTheDocument();
  });

  it("opens voucher details modal when 'View voucher details' is clicked", async () => {
    render(<RemittanceCard remittance={sampleRemittance} />);
    // The MenuDropdown mock always renders its options.
    const viewVoucherBtn = screen.getByText(/View voucher details/i);
    userEvent.click(viewVoucherBtn);

    await waitFor(() => {
      expect(screen.getByTestId("dialog-modal")).toBeInTheDocument();
      expect(screen.getByTestId("voucher-details")).toHaveTextContent(
        `Voucher Details for ${sampleRemittance.paymentReference}`,
      );
    });
  });

  it("handles successful download of remittance", async () => {
    // Set up a successful fetch response
    const mockBlob = new Blob(["dummy content"], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    (global.fetch as any).mockResolvedValue({
      ok: true,
      blob: async () => mockBlob,
    });

    // Spy on URL.createObjectURL and document body manipulation
    const createObjectURLSpy = vi.spyOn(window.URL, "createObjectURL").mockReturnValue("blob:url");
    const appendChildSpy = vi.spyOn(document.body, "appendChild");
    const removeChildSpy = vi.spyOn(document.body, "removeChild");

    render(<RemittanceCard remittance={sampleRemittance} />);
    const downloadBtn = screen.getByText(/Download remittance/i);
    userEvent.click(downloadBtn);

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        `${baseUrl}/api/v1/visit/payments/downloadRemittance/${sampleRemittance.id}`,
        { method: "POST" },
      );
    });

    expect(createObjectURLSpy).toHaveBeenCalled();
    expect(appendChildSpy).toHaveBeenCalled();
    expect(removeChildSpy).toHaveBeenCalled();
  });

  it("handles download error by showing a toast error", async () => {
    // Set up a fetch response with an error
    (global.fetch as any).mockResolvedValue({
      ok: false,
      statusText: "Download error",
    });

    const toastErrorSpy = vi.spyOn(toast, "error");
    render(<RemittanceCard remittance={sampleRemittance} />);
    const downloadBtn = screen.getByText(/Download remittance/i);
    userEvent.click(downloadBtn);

    await waitFor(() => {
      expect(toastErrorSpy).toHaveBeenCalledWith("Download error");
    });
  });
});
