import { EllipsisHorizontalIcon } from "@heroicons/react/24/outline";
import { useState } from "react";
import { toast } from "react-toastify";
import { Payment } from "../../api/remittance/remittance-api-types";
import DialogModal from "../../components/DialogModal";
import DotIcon from "../../components/icons/DotIcon";
import DownloadIcon from "../../components/icons/DownloadIcon";
import EyeIcon from "../../components/icons/EyeIcon";
import MenuDropdown from "../../components/MenuDropdown";
import CardWrapper from "../../components/ui/CardWrapper";
import { baseUrl } from "../../lib/Utils";
import { formatNumberToKes } from "../../utils/formatCurrency";
import getTimeBetweenAsString from "../../utils/getTimeBetweenAsString";
import { handleTryCatchError } from "../../utils/handleTryCatchError";
import VoucherDetails from "./VoucherDetails";

type Props = {
  remittance: Payment;
};

export default function RemittanceCard({ remittance }: Props) {
  const [canShowVoucherDetailsModal, setCanShowVoucherDetailsModal] = useState(false);

  const timeZoneString = remittance.createdOn + "Z";

  const downloadUrl = `${baseUrl}/api/v1/visit/payments/downloadRemittance/${remittance.id}`;

  async function handleDownloadRemittance() {
    try {
      const res = await fetch(downloadUrl, {
        method: "POST",
      });

      if (!res.ok) {
        toast.error(res.statusText);
        return;
      }

      const blob = await res.blob();
      const url = window.URL.createObjectURL(blob);

      const linkTag = document.createElement("a");
      linkTag.href = url;
      linkTag.download = "Remittance Document.xlsx";

      document.body.appendChild(linkTag);
      linkTag.click();
      document.body.removeChild(linkTag);

      window.URL.revokeObjectURL(url);
    } catch (error) {
      handleTryCatchError(error);
    }
  }

  return (
    <CardWrapper>
      <div className="relative">
        <p className="text-base font-[500] text-[#304254]">
          {remittance?.paymentReference || "NA"}
        </p>
        <p className="mt-2 text-sm">
          <span className="text-[#6B7280]">Account Name: </span>
          <span className="text-[#374151]">{remittance?.account[0]?.accountName || "NA"}</span>
        </p>
        <p className="mt-2 text-sm">
          <span className="text-[#6B7280]">Amount Paid: </span>
          <span className="text-[#374151]">{formatNumberToKes(remittance.amount)}</span>
        </p>
        <p className="mt-2 flex gap-1 text-sm">
          <span className="text-[#6B7280]">Date Paid: </span>
          <span className="flex items-center gap-2 text-[#374151]">
            <span>
              {new Intl.DateTimeFormat("en-GB", {
                dateStyle: "long",
                timeZone: "Africa/Nairobi",
              }).format(new Date(remittance.createdOn))}
            </span>
            <DotIcon />
            <span>{getTimeBetweenAsString(timeZoneString)}</span>
          </span>
        </p>
        <p className="mt-2 text-sm">
          <span className="text-[#6B7280]">Paid by: </span>
          <span className="text-[#374151]">{remittance?.createdBy?.userName || "NA"}</span>
        </p>

        <MenuDropdown
          containerStyle={{
            position: "absolute",
            top: 0,
            right: 0,
            display: "flex",
            alignItems: "center",
          }}
          buttonChild={<EllipsisHorizontalIcon className="h-5 w-5" />}
          options={[
            <button
              className="flex items-center gap-2"
              onClick={() => setCanShowVoucherDetailsModal(true)}
            >
              <EyeIcon size={18} /> <span>View voucher details</span>
            </button>,
            <button onClick={handleDownloadRemittance} className="flex items-center gap-2">
              <DownloadIcon fill="#304254" size={18} /> <span>Download remittance</span>
            </button>,
          ]}
        />

        <DialogModal isOpen={canShowVoucherDetailsModal} setIsOpen={setCanShowVoucherDetailsModal}>
          <VoucherDetails remittance={remittance} />
        </DialogModal>
      </div>
    </CardWrapper>
  );
}
