import { useGetPaymentVouchersQuery } from "../../api/remittance/remittanceApi";
import TableHeaderItem from "../../components/ui/table/TableHeaderItem";
import { AUTHENTICATED_USER } from "../../lib/payer-constants";
import VoucherDetailsRow from "./VoucherDetailsRow";

type Props = {
  paymentId: number;
};

export default function VoucherDetailsTable({ paymentId }: Props) {
  const { data: vouchersData } = useGetPaymentVouchersQuery({
    pathParams: {
      payerId: AUTHENTICATED_USER.getPayerId(),
    },
    queryParams: {
      paymentIds: [paymentId],
    },
  });

  const vouchers = vouchersData?.data.content || [];
  return vouchers.length === 0 ? (
    <p>No Vouchers Available</p>
  ) : (
    <table className="mt-2">
      <thead>
        <tr>
          <TableHeaderItem item="VOUCHER NUMBER" />
          <TableHeaderItem item="VOUCHER AMOUNT" />
          <TableHeaderItem item="DISCOUNT" />
          <TableHeaderItem item="PAYABLE AMOUNT" />
          <TableHeaderItem item="DATE CREATED" />
          <TableHeaderItem item="CREATED BY" />
          <TableHeaderItem item="ACTION" />
        </tr>
      </thead>

      <tbody>
        {vouchers.map((voucher) => (
          <VoucherDetailsRow voucher={voucher} key={voucher.id} />
        ))}
      </tbody>
    </table>
  );
}
