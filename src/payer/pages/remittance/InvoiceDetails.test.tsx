import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import InvoiceDetails from "./InvoiceDetails";

// Dummy invoice data
const dummyInvoice = {
  id: 1,
  invoiceNumber: "INV001",
  providerName: "ACC001",
  totalAmount: 5000,
  createdAt: "2025-02-26T10:00:00Z",
};

// Adjust dummyQueryResult so that when destructured the component finds:
// invoicesResult?.data.content  →  dummyQueryResult.data.data.content
const dummyQueryResult = {
  data: {
    data: {
      content: [dummyInvoice],
      totalPages: 2,
      totalElements: 15,
    },
  },
};

// --- Mocks ---

// Mock the API hook to return our dummy query result.
vi.mock("../../api/settlement/settlementApi", () => ({
  useGetVoucherInvoicesQuery: vi.fn(() => dummyQueryResult),
}));

// Mock the DialogModal context hook to supply a closeModal function.
const mockCloseModal = vi.fn();
vi.mock("../../components/DialogModal", () => ({
  useDialogModalContext: () => ({
    closeModal: mockCloseModal,
  }),
}));

// Stub XIcon to render a simple element.
vi.mock("../../components/icons/XIcon", () => ({
  default: (props: { size: number }) => <span data-testid="x-icon">XIcon</span>,
}));

// Stub the pagination component to render a button that sets page to 2.
vi.mock("../../components/ui/pagination/SecondaryPagination", () => ({
  default: (props: any) => (
    <button data-testid="pagination-button" onClick={() => props.setCurrentPage(2)}>
      Next Page
    </button>
  ),
}));

// Stub TableDataItem to simply render its item in a table cell.
vi.mock("../../components/ui/table/TableDataItem", () => ({
  default: (props: { item: any }) => <td>{props.item}</td>,
}));

// Stub TableHeaderItem to simply render the header text.
vi.mock("../../components/ui/table/TableHeaderItem", () => ({
  default: (props: { item: any }) => <th>{props.item}</th>,
}));

// Stub utility functions.
vi.mock("../../utils/convertDateString", () => ({
  convertDateString: (dateString: string) => `converted ${dateString}`,
}));

vi.mock("../../utils/formatCurrency", () => ({
  formatNumberToKes: (amount: number) => `KES ${amount.toLocaleString()}`,
}));

// --- Tests ---
describe("InvoiceDetails", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("renders invoice details with table headers and invoice row", () => {
    render(<InvoiceDetails voucherId={123} />);

    // Check that the title is rendered.
    expect(screen.getByText("Invoice Details")).toBeInTheDocument();

    // Check that table headers are rendered.
    expect(screen.getByText("INVOICE NUMBER")).toBeInTheDocument();
    expect(screen.getByText("ACCOUNT NUMBER")).toBeInTheDocument();
    expect(screen.getByText("INVOICE AMOUNT")).toBeInTheDocument();
    expect(screen.getByText("INVOICE DATE")).toBeInTheDocument();

    // Check that the invoice row is rendered.
    expect(screen.getByText(dummyInvoice.invoiceNumber)).toBeInTheDocument();
    expect(screen.getByText(dummyInvoice.providerName)).toBeInTheDocument();
    expect(
      screen.getByText(`KES ${dummyInvoice.totalAmount.toLocaleString()}`),
    ).toBeInTheDocument();
    expect(screen.getByText(`converted ${dummyInvoice.createdAt}`)).toBeInTheDocument();
  });

  it("calls closeModal when the close button is clicked", async () => {
    render(<InvoiceDetails voucherId={123} />);
    const closeButton = screen.getByTestId("close-button");
    expect(closeButton).toBeInTheDocument();

    if (closeButton) {
      await userEvent.click(closeButton);
    }
    expect(mockCloseModal).toHaveBeenCalled();
  });

  it("updates page when pagination button is clicked", async () => {
    // Re-import the hook so we can inspect its calls.
    const { useGetVoucherInvoicesQuery } = await import("../../api/settlement/settlementApi");
    const mockQuery = useGetVoucherInvoicesQuery as unknown as vi.Mock;

    render(<InvoiceDetails voucherId={123} />);

    // Initially, the hook should be called with page = 1.
    expect(mockQuery).toHaveBeenCalledWith({
      searchParams: { voucherIds: [123], page: 1, size: 10 },
    });

    // Click the pagination button, which our mock sets to page 2.
    const paginationButton = screen.getByTestId("pagination-button");
    userEvent.click(paginationButton);

    // Wait for re-render; the hook should be re-called with page = 2.
    await waitFor(() => {
      expect(mockQuery).toHaveBeenCalledWith({
        searchParams: { voucherIds: [123], page: 2, size: 10 },
      });
    });
  });
});
