import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import React from "react";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import * as remittanceApi from "../../api/remittance/remittanceApi";
import Remittance from "./Remittance";

// --- <PERSON>cks ---

// Mock the API hook.
vi.mock("../../api/remittance/remittanceApi", () => ({
  useGetRemittanceQuery: vi.fn(),
}));

// Stub MainWrapper.
vi.mock("../../components/ui/MainWrapper", () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="main-wrapper">{children}</div>
  ),
}));

// Stub Text.
vi.mock("../../components/ui/typography/Text", () => ({
  default: (props: any) => <div data-testid="text">{props.children}</div>,
}));

// Stub SearchInput.
vi.mock("../../components/ui/input/SearchInput", () => ({
  default: (props: any) => <input data-testid="search-input" {...props} />,
}));

// Stub LoadingAnimation.
vi.mock("../../components/animations/LoadingAnimation/LoadingAnimation", () => ({
  default: (props: any) => <div data-testid="loading-animation">Loading Animation</div>,
}));

// Stub EmptyState.
vi.mock("../../components/ui/EmptyState", () => ({
  default: (props: any) => <div data-testid="empty-state">{props.message.title}</div>,
}));

// Stub NoClaimsVouchering.
vi.mock("../../assets/svg/NoClaimsVouchering", () => ({
  default: () => <svg data-testid="no-claims">NoClaims</svg>,
}));

// Stub RemittanceCard.
vi.mock("./RemittanceCard", () => ({
  default: (props: any) => (
    <div data-testid="remittance-card">{props.remittance.paymentReference}</div>
  ),
}));

// Stub PrimaryPagination to render a button that simulates a page change.
vi.mock("../../components/ui/pagination/PrimaryPagination", () => ({
  default: (props: any) => (
    <button data-testid="pagination" onClick={() => props.onPageNumberClick(2)}>
      Change Page
    </button>
  ),
}));

// Stub CURRENT_USER constant.
vi.mock("../../lib/payer-constants", () => ({
  AUTHENTICATED_USER: {
    getPayerId: () => "payer123",
  },
}));

describe("Remittance Component", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("renders loading state when remittance data is fetching", () => {
    // When fetching, data is undefined and isFetching is true.
    (remittanceApi.useGetRemittanceQuery as any).mockReturnValue({
      data: undefined,
      isFetching: true,
    });

    render(<Remittance />);

    expect(screen.getByTestId("loading-animation")).toBeInTheDocument();
  });

  it("renders remittance cards and pagination when data is available", () => {
    // Dummy non-empty remittance data.
    const dummyData = {
      data: {
        content: [
          {
            id: "1",
            paymentReference: "REF1",
            account: [{ accountName: "John Doe" }],
            amount: 1000,
            createdOn: "2023-01-01T00:00:00",
            createdBy: { userName: "User1" },
          },
          {
            id: "2",
            paymentReference: "REF2",
            account: [{ accountName: "Jane Doe" }],
            amount: 2000,
            createdOn: "2023-01-02T00:00:00",
            createdBy: { userName: "User2" },
          },
        ],
        totalPages: 1,
        totalElements: 2,
      },
    };

    (remittanceApi.useGetRemittanceQuery as any).mockReturnValue({
      data: dummyData,
      isFetching: false,
    });

    render(<Remittance />);

    // Check that the heading is rendered.
    expect(screen.getByTestId("text")).toHaveTextContent("Remittance List");

    // Check that the search input is rendered.
    expect(screen.getByTestId("search-input")).toBeInTheDocument();

    // Verify that remittance cards are rendered.
    const cards = screen.getAllByTestId("remittance-card");
    expect(cards.length).toBe(2);

    // Verify that the pagination button is rendered.
    expect(screen.getByTestId("pagination")).toBeInTheDocument();
  });

  it("renders empty state when no remittance data is available", () => {
    // Dummy empty remittance data.
    const emptyData = {
      data: {
        content: [],
        totalPages: 0,
        totalElements: 0,
      },
    };

    (remittanceApi.useGetRemittanceQuery as any).mockReturnValue({
      data: emptyData,
      isFetching: false,
    });

    render(<Remittance />);

    // Check that the EmptyState is rendered with the expected message.
    expect(screen.getByTestId("empty-state")).toHaveTextContent("No Remittance available");
  });

  it("updates search query when the user types in the search input", async () => {
    // Use an empty data set.
    const dummyData = {
      data: {
        content: [],
        totalPages: 0,
        totalElements: 0,
      },
    };

    (remittanceApi.useGetRemittanceQuery as any).mockReturnValue({
      data: dummyData,
      isFetching: false,
    });

    render(<Remittance />);

    const searchInput = screen.getByTestId("search-input");
    expect(searchInput).toHaveValue("");

    await userEvent.type(searchInput, "test query");

    expect(searchInput).toHaveValue("test query");
  });

  it("updates page number when the pagination button is clicked", async () => {
    // Dummy data with at least one remittance.
    const dummyData = {
      data: {
        content: [
          {
            id: "1",
            paymentReference: "REF1",
            account: [{ accountName: "John Doe" }],
            amount: 1000,
            createdOn: "2023-01-01T00:00:00",
            createdBy: { userName: "User1" },
          },
        ],
        totalPages: 2,
        totalElements: 1,
      },
    };

    (remittanceApi.useGetRemittanceQuery as any).mockReturnValue({
      data: dummyData,
      isFetching: false,
    });

    render(<Remittance />);

    // Click the pagination button (our PrimaryPagination stub calls onPageNumberClick(2)).
    const paginationButton = screen.getByTestId("pagination");
    userEvent.click(paginationButton);

    // Because Remittance re-calls useGetRemittanceQuery on state change,
    // we wait and check that it was called with page = 2.
    await waitFor(() => {
      expect(remittanceApi.useGetRemittanceQuery).toHaveBeenCalledWith({
        queryParams: {
          payerId: "payer123",
          query: "",
          page: 2,
          size: 3,
          remittanceSent: true,
        },
      });
    });
  });
});
