import { useState } from "react";
import { TailSpin } from "react-loader-spinner";
import { toast } from "react-toastify";
import { Payment } from "../../api/remittance/remittance-api-types";
import { useDialogModalContext } from "../../components/DialogModal";
import PdfFileIcon from "../../components/icons/PdfFileIcon";
import XIcon from "../../components/icons/XIcon";
import { baseUrl } from "../../lib/Utils";
import { downloadFile } from "../../utils/downloadFile";
import VoucherDetailsTable from "./VoucherDetailsTable";

type Props = {
  remittance: Payment;
};

type ResultUrl = {
  data: string;
  msg: string;
  success: boolean;
};

export default function VoucherDetails({ remittance }: Props) {
  const { closeModal } = useDialogModalContext();

  const [isDownloadingFile, setIsDownloadingFile] = useState(false);

  async function handleDownloadFile() {
    try {
      setIsDownloadingFile(true);

      //get the download url
      const response = await fetch(`${baseUrl}/api/file/download?name=${remittance.bankSchedule}`);
      const result = (await response.json()) as ResultUrl;

      downloadFile(result.data, remittance.paymentReference as string);
    } catch (error) {
      if (error instanceof Error) toast.error(error.message);
      console.error(error);
    } finally {
      setIsDownloadingFile(false);
    }
  }

  return (
    <div className="relative min-w-[650px]">
      <button
        data-testid="close-button"
        className={`absolute right-0 top-0`}
        onClick={() => closeModal()}
      >
        <XIcon size={24} />
      </button>

      <section className="pt-4">
        <h3 className=" text-xl font-[500] text-[#1A2853]">Payment Details</h3>

        <div className="mt-3 grid grid-cols-4 gap-4">
          <div className="">
            <p className="text-sm text-[#61758A]">Payment Mode</p>
            <p className="">{remittance.modeOfPayment}</p>
          </div>

          {remittance.modeOfPayment === "Cheque" && (
            <>
              <div className="">
                <p className="text-sm text-[#61758A]">Cheque Date</p>
                <p className="">
                  {" "}
                  {new Intl.DateTimeFormat("en-GB", { dateStyle: "long" }).format(
                    new Date(remittance.createdOn),
                  )}
                </p>
              </div>
              <div className="">
                <p className="text-sm text-[#61758A]">Cheque Number</p>
                <p className="">{remittance.chequeNo || "NA"}</p>
              </div>
            </>
          )}

          {remittance.modeOfPayment === "BankTransfer" && (
            <>
              <div className="">
                <p className="text-sm text-[#61758A]">Account Number</p>
                <p className="">{remittance.account[0]?.accountNumber || "NA"}</p>
              </div>

              <div className="">
                <p className="text-sm text-[#61758A]">Bank Code</p>
                <p className="">{remittance.account[0]?.bankCode || "NA"}</p>
              </div>

              <div className="">
                <p className="text-sm text-[#61758A]">Branch Code</p>
                <p className="">{remittance.account[0]?.branchCode || "NA"}</p>
              </div>

              <div className="">
                <p className="text-sm text-[#61758A]">Account Name</p>
                <p className="">{remittance.account[0]?.accountName || "NA"}</p>
              </div>

              <div className="">
                <p className="text-sm text-[#61758A]">Bank Name</p>
                <p className="">{remittance.account[0]?.bankName || "NA"}</p>
              </div>

              <div className="">
                <p className="text-sm text-[#61758A]">Branch Name</p>
                <p className="">{remittance.account[0]?.bankBranch || "NA"}</p>
              </div>
            </>
          )}
        </div>

        {remittance.modeOfPayment === "BankTransfer" && remittance.bankSchedule && (
          <>
            <div className="mt-4">
              <p className="font-medium text-[#374151]">Bank Schedule</p>
              <button
                data-testid="download-button"
                className="mt-1 w-fit rounded bg-[#F3F4F5] p-2"
                onClick={handleDownloadFile}
              >
                <PdfFileIcon size={40} />
              </button>

              {isDownloadingFile && (
                <div
                  data-testid="tailspin"
                  className="absolute inset-0 flex items-center justify-center "
                >
                  <TailSpin color="blue" height={40} />
                </div>
              )}
            </div>
          </>
        )}
      </section>

      <section className="mt-6">
        <h3 className="text-xl font-[500] text-[#1A2853]">Voucher Details</h3>

        <VoucherDetailsTable paymentId={remittance.id} />
      </section>
    </div>
  );
}
