// VoucherDetailsTable.test.tsx
import React from "react";
import { render, screen } from "@testing-library/react";
import { vi } from "vitest";
import VoucherDetailsTable from "./VoucherDetailsTable";

// Partially mock the remittance API module using vi.importActual
vi.mock("../../api/remittance/remittanceApi", async () => {
  const actual = await vi.importActual("../../api/remittance/remittanceApi");
  return {
    ...actual,
    useGetPaymentVouchersQuery: vi.fn(),
  };
});

// Optionally, mock the VoucherDetailsRow component so we can focus on VoucherDetailsTable
vi.mock("./VoucherDetailsRow", () => ({
  default: ({ voucher }: { voucher: any }) => (
    <tr data-testid="voucher-row">
      <td>{voucher.id}</td>
    </tr>
  ),
}));

// Import the mocked hook to set its return values in tests.
import { useGetPaymentVouchersQuery } from "../../api/remittance/remittanceApi";

describe("VoucherDetailsTable", () => {
  const paymentId = 1;

  it('should display "No Vouchers Available" when there are no vouchers', () => {
    (useGetPaymentVouchersQuery as any).mockReturnValue({
      data: { data: { content: [] } },
    });

    render(<VoucherDetailsTable paymentId={paymentId} />);
    expect(screen.getByText("No Vouchers Available")).toBeInTheDocument();
  });

  it("should render a table with headers and voucher rows when vouchers are available", () => {
    const fakeVouchers = [
      { id: 1, voucherNumber: "V001" },
      { id: 2, voucherNumber: "V002" },
    ];
    (useGetPaymentVouchersQuery as any).mockReturnValue({
      data: { data: { content: fakeVouchers } },
    });

    render(<VoucherDetailsTable paymentId={paymentId} />);

    // Verify table headers are rendered
    expect(screen.getByText("VOUCHER NUMBER")).toBeInTheDocument();
    expect(screen.getByText("VOUCHER AMOUNT")).toBeInTheDocument();
    expect(screen.getByText("DISCOUNT")).toBeInTheDocument();
    expect(screen.getByText("PAYABLE AMOUNT")).toBeInTheDocument();
    expect(screen.getByText("DATE CREATED")).toBeInTheDocument();
    expect(screen.getByText("CREATED BY")).toBeInTheDocument();
    expect(screen.getByText("ACTION")).toBeInTheDocument();

    // Verify that the correct number of voucher rows are rendered
    const rows = screen.getAllByTestId("voucher-row");
    expect(rows).toHaveLength(fakeVouchers.length);
  });
});
