import { useState } from "react";
import { useGetVoucherInvoicesQuery } from "../../api/settlement/settlementApi";
import { useDialogModalContext } from "../../components/DialogModal";
import XIcon from "../../components/icons/XIcon";
import SecondaryPagination from "../../components/ui/pagination/SecondaryPagination";
import TableDataItem from "../../components/ui/table/TableDataItem";
import TableHeaderItem from "../../components/ui/table/TableHeaderItem";
import { convertDateString } from "../../utils/convertDateString";
import { formatNumberToKes } from "../../utils/formatCurrency";

type Props = {
  voucherId: number;
};
export default function InvoiceDetails({ voucherId }: Props) {
  const { closeModal } = useDialogModalContext();
  const [page, setPage] = useState(1);
  const size = 10;

  const { data: invoicesResult } = useGetVoucherInvoicesQuery({
    searchParams: {
      voucherIds: [voucherId],
      page,
      size,
    },
  });

  const invoices = invoicesResult?.data.content || [];
  const totalPages = invoicesResult?.data.totalPages;
  const totalElements = invoicesResult?.data.totalElements;

  return (
    <div className="relative">
      <button
        data-testid="close-button"
        className={`absolute -right-2 -top-2 `}
        onClick={() => closeModal()}
      >
        <XIcon size={20} />
      </button>

      <section className="flex items-center justify-between pr-4  pt-6">
        <h3 className=" text-xl font-[500] text-[#1A2853]">Invoice Details</h3>

        <SecondaryPagination
          currentPage={page}
          setCurrentPage={(value) => setPage(value)}
          size={size}
          totalElements={totalElements as number}
          totalPages={totalPages as number}
        />
      </section>

      <table className="mt-4">
        <thead>
          <tr>
            <TableHeaderItem item="INVOICE NUMBER" />
            <TableHeaderItem item="ACCOUNT NUMBER" />
            <TableHeaderItem item="INVOICE AMOUNT" />
            <TableHeaderItem item="INVOICE DATE" />
          </tr>
        </thead>

        <tbody>
          {invoices.map((invoice) => (
            <tr key={invoice.id}>
              <TableDataItem item={invoice.invoiceNumber} />
              <TableDataItem item={invoice.providerName} />
              <TableDataItem item={formatNumberToKes(invoice.totalAmount)} />
              <TableDataItem item={convertDateString(invoice.createdAt)} />
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
