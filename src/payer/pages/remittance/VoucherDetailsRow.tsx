import { useState } from "react";
import { Voucher } from "../../api/remittance/remittance-api-types";
import DialogModal from "../../components/DialogModal";
import EyeIcon from "../../components/icons/EyeIcon";
import TableDataItem from "../../components/ui/table/TableDataItem";
import { formatNumberToKes } from "../../utils/formatCurrency";
import InvoiceDetails from "./InvoiceDetails";

type Props = {
  voucher: Voucher;
};

export default function VoucherDetailsRow({ voucher }: Props) {
  const [canShowInvoiceDetails, setCanShowInvoiceDetails] = useState(false);

  return (
    <>
      <tr>
        <TableDataItem item={voucher.voucherNo as string} />
        <TableDataItem item={formatNumberToKes(voucher.amount) as string} />
        <TableDataItem item={formatNumberToKes(voucher.discount as number) as string} />
        <TableDataItem item={formatNumberToKes(voucher.payableAmount as number) as string} />
        <TableDataItem
          item={
            new Intl.DateTimeFormat("en-GB", { dateStyle: "long" }).format(
              new Date(voucher.createdOn),
            ) as string
          }
        />
        <TableDataItem item={(voucher.createdBy?.userName as string) || "NA"} />
        <td className="flex justify-center">
          <button onClick={() => setCanShowInvoiceDetails(true)}>
            <EyeIcon fill="#3B82F6" size={20} />
          </button>
        </td>
      </tr>

      <DialogModal isOpen={canShowInvoiceDetails} setIsOpen={setCanShowInvoiceDetails}>
        <InvoiceDetails voucherId={voucher.id} />
      </DialogModal>
    </>
  );
}
