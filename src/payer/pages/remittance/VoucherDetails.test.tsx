import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { toast } from "react-toastify";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { downloadFile } from "../../utils/downloadFile";
import VoucherDetails from "./VoucherDetails";

// Dummy remittance object for BankTransfer (so the download button is rendered)
const dummyRemittance = {
  id: "1",
  paymentReference: "REF001",
  modeOfPayment: "BankTransfer",
  bankSchedule: "schedule.pdf",
  createdOn: "2023-01-01T00:00:00Z",
  chequeNo: "123",
  account: [
    {
      accountNumber: "*********",
      bankCode: "001",
      branchCode: "002",
      accountName: "Test Account",
      bankName: "Test Bank",
      bankBranch: "Test Branch",
    },
  ],
};

// --- Mocks ---
// Mock the dialog modal context.
const mockCloseModal = vi.fn();
vi.mock("../../components/DialogModal", () => ({
  useDialogModalContext: () => ({
    closeModal: mockCloseModal,
  }),
}));

// Stub the XIcon so we can locate the close button.
vi.mock("../../components/icons/XIcon", () => ({
  default: (props: any) => <span data-testid="x-icon">XIcon</span>,
}));

// Stub the PdfFileIcon used in the download button.
vi.mock("../../components/icons/PdfFileIcon", () => ({
  default: (props: any) => <span data-testid="pdf-icon">PdfIcon</span>,
}));

// Stub the VoucherDetailsTable.
vi.mock("./VoucherDetailsTable", () => ({
  default: (props: any) => (
    <div data-testid="voucher-details-table">VoucherDetailsTable for payment {props.paymentId}</div>
  ),
}));

// Stub the TailSpin spinner from react-loader-spinner.
vi.mock("react-loader-spinner", () => ({
  TailSpin: (props: any) => <div data-testid="tailspin">TailSpin Spinner</div>,
}));

// Stub baseUrl.
vi.mock("../../lib/Utils", () => ({
  baseUrl: "http://example.com",
}));

// Mock downloadFile helper.
vi.mock("../../utils/downloadFile", () => ({
  downloadFile: vi.fn(),
}));

describe("VoucherDetails Component", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Setup global.fetch as a mock function.
    global.fetch = vi.fn();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("calls closeModal when the close button is clicked", async () => {
    render(<VoucherDetails remittance={dummyRemittance} />);

    // Locate the close button using its test id.
    const closeButton = screen.getByTestId("close-button");
    expect(closeButton).toBeInTheDocument();

    await userEvent.click(closeButton);
    expect(mockCloseModal).toHaveBeenCalled();
  });

  it("shows toast error when fetch fails during file download", async () => {
    const errorMessage = "Network error";
    (global.fetch as any).mockRejectedValue(new Error(errorMessage));
    const toastErrorSpy = vi.spyOn(toast, "error");

    render(<VoucherDetails remittance={dummyRemittance} />);

    const pdfIcon = screen.getByTestId("pdf-icon");
    const downloadButton = pdfIcon.closest("button");
    expect(downloadButton).toBeInTheDocument();

    if (downloadButton) {
      await userEvent.click(downloadButton);
    }

    // Wait for the error to be handled and toast.error to be called.
    await waitFor(() => {
      expect(toastErrorSpy).toHaveBeenCalledWith(errorMessage);
    });
  });
});
