import { useState } from "react";
import { useGetRemittanceQuery } from "../../api/remittance/remittanceApi";
import LoadingAnimation from "../../components/animations/LoadingAnimation/LoadingAnimation";
import NoClaimsVouchering from "../../components/illustrations/NoClaimsVouchering";
import EmptyState from "../../components/ui/EmptyState";
import SearchInput from "../../components/ui/input/SearchInput";
import MainWrapper from "../../components/ui/MainWrapper";
import PrimaryPagination from "../../components/ui/pagination/PrimaryPagination";
import Text from "../../components/ui/typography/Text";
import { AUTHENTICATED_USER } from "../../lib/payer-constants";
import RemittanceCard from "./RemittanceCard";

export default function Remittance() {
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(3);

  const { data: remittanceData, isFetching: isFetchingRemittance } = useGetRemittanceQuery({
    queryParams: {
      payerId: AUTHENTICATED_USER.getPayerId(),
      query: searchQuery,
      page,
      size,
      remittanceSent: true,
    },
  });

  const remittanceList = remittanceData?.data.content || [];
  const totalPages = remittanceData?.data.totalPages;
  const totalElements = remittanceData?.data.totalElements;

  return (
    <MainWrapper>
      <section className="flex items-center justify-between">
        <Text variant="heading" className="text-lg">
          Remittance List
        </Text>
        <form className="relative max-w-[320px] grow">
          <SearchInput
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full"
            placeholder="Search"
          />
        </form>
      </section>

      {isFetchingRemittance ? (
        <>
          <div className="flex h-full items-center justify-center">
            <LoadingAnimation size={50} />
          </div>
        </>
      ) : (
        <>
          <section className="mt-8 flex flex-col gap-4">
            {remittanceList.map((remittance) => (
              <RemittanceCard remittance={remittance} key={remittance.id} />
            ))}

            {remittanceList.length === 0 && (
              <EmptyState
                illustration={<NoClaimsVouchering />}
                message={{
                  title: "No Remittance available",
                  description: "Send remittance to populate remittance list",
                }}
              />
            )}
          </section>
        </>
      )}

      {remittanceList.length > 0 && (
        <PrimaryPagination
          onPageNumberClick={(value) => setPage(value)}
          onSizeChange={(value) => setSize(value)}
          pageNumber={page}
          pageSize={size}
          totalElements={totalElements as number}
          totalPages={totalPages as number}
        />
      )}
    </MainWrapper>
  );
}
