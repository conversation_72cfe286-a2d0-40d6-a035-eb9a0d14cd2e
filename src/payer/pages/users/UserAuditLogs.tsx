import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Empty } from "~lib/components";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { useGetPayerUsersQuery } from "../../api/features/membershipApi";
import Badge from "../../components/ui/Badge";
import Button from "../../components/ui/Button";
import MainWrapper from "../../components/ui/MainWrapper";
import TableDataItem from "../../components/ui/table/TableDataItem";
import TableHeaderItem from "../../components/ui/table/TableHeaderItem";
import Text from "../../components/ui/typography/Text";
import { setSelectedAuditLogsUser } from "../../features/access-control/accessControlSlice";
import { GetPayerUsersQuery, UserRepresentation } from "../../lib/types/access-control/user";
import UserService from "../../services/UserService";
import { useAppDispatch } from "../../store/hooks";
import PrimaryPagination from "../../components/ui/pagination/PrimaryPagination";
import SearchInput from "../../components/ui/input/SearchInput";

export default function UserAuditLogs() {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [users, setUsers] = useState<UserRepresentation[]>([]);
  const payerId: string = UserService.getPayer()?.tokenParsed?.["payerId"];
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");

  const payerUsersParams: GetPayerUsersQuery = {
    payerId,
    page,
    size,
    search: searchTerm,
  };

  const { data, isLoading, isFetching, error } = useGetPayerUsersQuery(payerUsersParams);
  const totalElements = data?.totalElements as number;
  const totalPages = data?.totalPages as number;

  const handlePageNumberClick = (page: number) => {
    setPage(page);
  };

  const handleSizeChange = (size: number) => {
    setSize(size);
    setPage(1);
  };

  useEffect(() => {
    if (data?.content) {
      setUsers(data.content);
    }
  }, [data]);

  const shouldDisplayTable = !isLoading && !isFetching && !error && users.length > 0;
  const shouldDisplayPagination = !isLoading && !isFetching && !error && totalElements > 0;

  const handleViewLogs = (user: UserRepresentation) => {
    dispatch(setSelectedAuditLogsUser(user));
    navigate("/users/audit-logs/view");
  };

  return (
    <MainWrapper className="flex flex-col gap-6">
      <section className="flex justify-between gap-6">
        <div className="flex flex-col gap-3">
          <Text variant="heading">User Audit Logs</Text>
          <Text
            variant="description"
            className="italic"
          >{`Please click on the "view logs" button to access the user's logs.`}</Text>
        </div>
        <div className="flex items-center gap-6">
          <SearchInput
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search by username, email or full name"
            title="Search by username, email or full name"
            className="h-fit w-[37ch]"
          />
        </div>
      </section>
      {shouldDisplayTable && (
        <table>
          <thead>
            <tr className="bg-gray-50">
              <TableHeaderItem item="name" />
              <TableHeaderItem item="username" />
              <TableHeaderItem item="email address" />
              <TableHeaderItem item="status" />
              <TableHeaderItem item="action" />
            </tr>
          </thead>
          <tbody>
            {users.map((user) => (
              <tr key={user.id}>
                <TableDataItem>
                  {user.firstName && user.lastName ? (
                    `${user.firstName} ${user.lastName}`
                  ) : (
                    <div className="text-center">-</div>
                  )}
                </TableDataItem>
                <TableDataItem>{user.username}</TableDataItem>
                <TableDataItem>{user.email || <div className="text-center">-</div>}</TableDataItem>
                <TableDataItem>
                  {user.enabled ? (
                    <Badge color="green" text="Enabled" hasDot />
                  ) : (
                    <Badge color="yellow" text="Disabled" hasDot />
                  )}
                </TableDataItem>
                <TableDataItem>
                  <Button onClick={() => handleViewLogs(user)}>View Logs</Button>
                </TableDataItem>
              </tr>
            ))}
          </tbody>
        </table>
      )}
      {shouldDisplayPagination && (
        <div className="self-start py-4">
          <PrimaryPagination
            pageSize={size}
            totalElements={totalElements ?? 0}
            totalPages={totalPages ?? 1}
            pageNumber={page}
            onPageNumberClick={handlePageNumberClick}
            onSizeChange={handleSizeChange}
          />
        </div>
      )}
      {isLoading || isFetching ? (
        <div className="flex items-center justify-center space-x-2 self-center py-44">
          <LoadingIcon className="h-6 w-6 text-blue-400" />
          <p className="text-base text-blue-700">Loading Users...</p>
        </div>
      ) : error ? (
        <div className="flex items-center justify-center self-center py-44">
          <p className="text-red-700">Error loading users. Try again later.</p>
        </div>
      ) : (
        !shouldDisplayTable &&
        totalElements === 0 && (
          <div className="self-center py-44">
            <Empty message="No users found!" />
          </div>
        )
      )}
    </MainWrapper>
  );
}
