import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  accessModuleDetails,
  PredefinedRole,
  predefinedRoles,
} from "../../components/access-management/data";
import MainWrapper from "../../components/ui/MainWrapper";
import Text from "../../components/ui/typography/Text";
import TopBackButton from "../../components/ui/TopBackButton";
import { Empty } from "~lib/components";
import { AccessModuleDetail, RealmRole } from "../../components/access-management/AccessModuleCard";

export default function RoleDetail() {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const [role, setRole] = useState<PredefinedRole | null>(null);
  const [moduleData, setModuleData] = useState<AccessModuleDetail | null>(null);

  useEffect(() => {
    if (slug) {
      const foundRole = predefinedRoles.find((r) => r.slug === slug);
      if (foundRole) {
        setRole(foundRole);
        const foundModule = accessModuleDetails.find((m) => m.name === foundRole.moduleName);
        setModuleData(foundModule ?? null);
      }
    }
  }, [slug]);

  if (!role || !moduleData) {
    return (
      <MainWrapper>
        <TopBackButton onClick={() => navigate("/users/roles")} />
        <div className="py-20">
          <Empty message="Role not found" />
        </div>
      </MainWrapper>
    );
  }

  return (
    <MainWrapper className="flex flex-col gap-6">
      <section className="flex space-x-6">
        <TopBackButton onClick={() => navigate("/users/roles/predefined")} className="mt-1" />
        <div className="flex flex-col space-y-1">
          <Text variant="heading">{role.name}</Text>
          <Text variant="description" className="italic">
            {role.description}
          </Text>
        </div>
      </section>

      <div className="rounded-lg border p-6 shadow-sm">
        <Text variant="subheading" className="mb-4">
          Permissions Assigned
        </Text>

        <div className="space-y-6">
          {/* No Display for the "All X roles" entry (first entry) except for Scheme Management */}
          {(slug === "scheme-management"
            ? moduleData.realmRoles
            : moduleData.realmRoles.slice(1)
          ).map((realmRole: RealmRole) => (
            <div key={realmRole.name} className="border-t pt-4">
              <Text variant="subheading" className="font-medium">
                {realmRole.name}
              </Text>
              <Text variant="description" className="mt-1 text-gray-600">
                {realmRole.description}
              </Text>
            </div>
          ))}
        </div>
      </div>
    </MainWrapper>
  );
}
