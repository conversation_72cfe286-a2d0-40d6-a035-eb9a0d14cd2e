import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import * as z from "zod";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import Button from "../../components/ui/Button";
import CharacterCount from "../../components/ui/character-count";
import RequiredTag from "../../components/ui/input/required-tag";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import Text from "../../components/ui/typography/Text";
import { UserStatusUpdateRequest } from "../../lib/types/access-control/user";
import { XMarkIcon } from "@heroicons/react/24/outline";

interface UpdateUserStatusModalProps {
  show: boolean;
  onClose: () => void;
  userId: string;
  currentStatus: boolean;
  actionedBy: string;
  onSubmit: (payload: UserStatusUpdateRequest) => Promise<void>;
  isLoading: boolean;
}

const createReasonSchema = () =>
  z.object({
    reason: z
      .string()
      .trim()
      .min(5, "Reason must be at least 5 characters")
      .max(500, `Reason must be at most 500 characters`),
  });

type FormValues = z.infer<ReturnType<typeof createReasonSchema>>;

export default function UpdateUserStatusModal({
  show,
  onClose,
  userId,
  currentStatus,
  actionedBy,
  onSubmit,
  isLoading,
}: UpdateUserStatusModalProps) {
  const maxCharacters = 500;

  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors, isValid },
  } = useForm<FormValues>({
    resolver: zodResolver(createReasonSchema()),
    mode: "onChange",
    defaultValues: {
      reason: "",
    },
  });

  const reasonValue = watch("reason") || "";

  const onFormSubmit = async (data: FormValues) => {
    try {
      const payload: UserStatusUpdateRequest = {
        userId,
        enabled: !currentStatus,
        updatedBy: actionedBy,
        reason: data.reason,
      };

      await onSubmit(payload);
      reset();
    } catch (error) {
      console.error("Error updating user status:", error);
      toast.error("Failed to update user status. Please try again.");
    }
  };

  const handleClose = () => {
    reset({
      reason: "",
    });
    onClose();
  };

  const actionText = currentStatus ? "Deactivate" : "Activate";

  return (
    <DialogWrapper show={show} onClose={handleClose} maxWidth="max-w-[500px]" className="p-6">
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <Text variant="heading">{actionText} User</Text>
          <button>
            <XMarkIcon className="h-5 w-5" onClick={onClose} />
          </button>
        </div>
        <p className="text-sm text-gray-500">
          {currentStatus
            ? "Deactivating this user will prevent them from logging in. Please provide a reason for this action."
            : "Activating this user will allow them to log in. Please provide a reason for this action."}
        </p>

        <form onSubmit={handleSubmit(onFormSubmit)} className="flex flex-col gap-4">
          <div className="flex flex-col gap-2">
            <label htmlFor="reason" className="text-sm font-medium">
              Reason for {currentStatus ? "Deactivation" : "Activation"} <RequiredTag />
            </label>
            <div className="relative">
              <textarea
                id="reason"
                className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                placeholder={`Enter reason for ${actionText.toLowerCase()}`}
                rows={3}
                maxLength={maxCharacters}
                {...register("reason")}
              />
              <div className="mt-1 flex items-center justify-between">
                {errors.reason && <p className="text-xs text-red-600">{errors.reason.message}</p>}
                <CharacterCount
                  current={reasonValue.length}
                  max={maxCharacters}
                  className="ml-auto"
                />
              </div>
            </div>
          </div>

          <div className="mt-4 flex justify-end gap-3">
            <Button type="button" variant="outlined" onClick={handleClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !isValid}
              variant={currentStatus ? "destructive" : "filled"}
            >
              {isLoading ? (
                <>
                  <LoadingIcon className="mr-2 h-5 w-5" />
                  {currentStatus ? "Deactivating..." : "Activating..."}
                </>
              ) : (
                `${actionText} User`
              )}
            </Button>
          </div>
        </form>
      </div>
    </DialogWrapper>
  );
}
