import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { useGetCustomRolesQuery } from "../../api/features/membershipApi";
import Button from "../../components/ui/Button";
import MainWrapper from "../../components/ui/MainWrapper";
import PrimaryPagination from "../../components/ui/pagination/PrimaryPagination";
import SearchInput from "../../components/ui/input/SearchInput";
import Text from "../../components/ui/typography/Text";
import { CustomRoleResponse, GetCustomRolesQuery } from "../../lib/types/access-control/role";
import UserService from "../../services/UserService";
import { PlusIcon } from "@heroicons/react/24/solid";
import EmptyState from "../../components/ui/EmptyState";
import NoCustomRoles from "../../components/illustrations/no-custom-roles";
import CustomRoleCard from "../../components/custom-role/custom-role-card";

export default function CustomRoles() {
  const navigate = useNavigate();
  const [customRoles, setCustomRoles] = useState<CustomRoleResponse[]>([]);
  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");

  const customRolesParams: GetCustomRolesQuery = {
    payerId,
    page,
    size,
    name: searchTerm,
  };

  const { data, isLoading, isFetching, error } = useGetCustomRolesQuery(customRolesParams);
  const totalElements = data?.totalElements as number;
  const totalPages = data?.totalPages as number;

  const handlePageNumberClick = (page: number) => {
    setPage(page);
  };

  const handleSizeChange = (size: number) => {
    setSize(size);
    setPage(1);
  };

  useEffect(() => {
    if (data?.content) {
      setCustomRoles(data.content);
    }
  }, [data]);

  const filteredRoles = searchTerm
    ? customRoles.filter(
        (role) =>
          role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          role.description?.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    : customRoles;

  const shouldDisplayData = !isLoading && !isFetching && !error && filteredRoles.length > 0;

  return (
    <MainWrapper className="flex flex-col gap-6">
      <section className="flex justify-between gap-6">
        <div className="flex flex-col gap-3">
          <Text variant="heading">Role Management</Text>
          <Text variant="description" className="italic">
            Create customized roles with specific permissions.
          </Text>
        </div>
        <div className="flex items-center gap-6">
          <SearchInput
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search custom role by name"
            title="Search by name or description"
            className="h-fit w-[37ch]"
          />
          <Button
            className="whitespace-nowrap"
            onClick={() => navigate("/users/roles/custom/create")}
          >
            <PlusIcon className="h-5 w-5" /> Create Custom Role
          </Button>
        </div>
      </section>
      {shouldDisplayData && (
        <>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredRoles.map((role) => (
              <CustomRoleCard key={role.id} role={role} />
            ))}
          </div>
          <div className="self-start py-4">
            <PrimaryPagination
              pageSize={size}
              totalElements={totalElements ?? 0}
              totalPages={totalPages ?? 1}
              pageNumber={page}
              onPageNumberClick={handlePageNumberClick}
              onSizeChange={handleSizeChange}
            />
          </div>
        </>
      )}
      {isLoading || isFetching ? (
        <div className="flex items-center justify-center space-x-2 self-center py-44">
          <LoadingIcon className="h-6 w-6 text-blue-400" />
          <p className="text-blue-700">Loading Custom Roles...</p>
        </div>
      ) : error ? (
        <div className="flex items-center justify-center self-center py-44">
          <p className="text-red-700">Error loading custom roles. Try again later.</p>
        </div>
      ) : (
        filteredRoles?.length === 0 && (
          <div className="self-center py-44">
            <EmptyState
              illustration={<NoCustomRoles />}
              message={{
                title: "No Custom Roles",
                description: "Create a custom role to define specific permissions for your users.",
              }}
            />
          </div>
        )
      )}
    </MainWrapper>
  );
}
