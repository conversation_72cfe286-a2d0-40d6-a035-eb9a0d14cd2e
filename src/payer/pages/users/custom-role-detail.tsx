import { TrashIcon } from "@heroicons/react/24/outline";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import {
  useDeleteCustomRoleMutation,
  useGetCustomRoleByIdQuery,
  useUpdateCustomRoleMutation,
} from "../../api/features/membershipApi";
import CustomRoleDeleteDialog from "../../components/custom-role/custom-role-delete-dialog";
import CustomRoleInfo from "../../components/custom-role/custom-role-info";
import CustomRolePermissions from "../../components/custom-role/custom-role-permissions";
import CustomRoleUsers from "../../components/custom-role/custom-role-users";
import Badge from "../../components/ui/Badge";
import Button from "../../components/ui/Button";
import MainWrapper from "../../components/ui/MainWrapper";
import ProgressModal from "../../components/ui/modal/ProgressModal";
import TabGroup from "../../components/ui/tab-group";
import TopBackButton from "../../components/ui/TopBackButton";
import Text from "../../components/ui/typography/Text";
import {
  CustomRoleUpdateRequest,
  RoleManagementDeletionResponse,
} from "../../lib/types/access-control/role";
import UserService from "../../services/UserService";
import AddCustomRolePermissionsModal from "./add-custom-role-permissions-modal";

export default function CustomRoleDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isEditingName, setIsEditingName] = useState(false);
  const [isEditingDescription, setIsEditingDescription] = useState(false);
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showAddPermissionsModal, setShowAddPermissionsModal] = useState(false);
  const [selectedPermissionNames, setSelectedPermissionNames] = useState<Set<string>>(new Set());
  const [isUpdatingField, setIsUpdatingField] = useState(false);
  const [showProgressModal, setShowProgressModal] = useState(false);
  const [progressModalTitle, setProgressModalTitle] = useState("");

  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const username = UserService.getUsername() || "";

  const {
    data: roleData,
    isLoading,
    error,
    refetch,
  } = useGetCustomRoleByIdQuery({
    id: Number(id),
    payerId,
  });

  const [updateCustomRole] = useUpdateCustomRoleMutation();
  const [deleteCustomRole, { isLoading: isDeleting }] = useDeleteCustomRoleMutation();

  useEffect(() => {
    if (roleData) {
      setName(roleData.name);
      setDescription(roleData.description || "");
      setSelectedPermissionNames(new Set(roleData.permissions.map((p) => p.name)));
    }
  }, [roleData]);

  const handleSaveName = async () => {
    if (!roleData) return;

    try {
      setIsUpdatingField(true);
      setProgressModalTitle("Updating role name...");
      setShowProgressModal(true);

      const payload: CustomRoleUpdateRequest = {
        id: roleData.id,
        payerId,
        body: {
          name: name !== roleData.name ? name : undefined,
          updatedBy: username,
        },
      };

      await updateCustomRole(payload).unwrap();
      toast.success("Role name updated successfully");
      setIsEditingName(false);
      refetch();
    } catch (error) {
      toast.error("Failed to update role name");
      console.error("Error updating role name:", error);
    } finally {
      setIsUpdatingField(false);
      setShowProgressModal(false);
    }
  };

  const handleSaveDescription = async () => {
    if (!roleData) return;

    try {
      setIsUpdatingField(true);
      setProgressModalTitle("Updating role description...");
      setShowProgressModal(true);

      const payload: CustomRoleUpdateRequest = {
        id: roleData.id,
        payerId,
        body: {
          description: description !== roleData.description ? description : undefined,
          updatedBy: username,
        },
      };

      await updateCustomRole(payload).unwrap();
      toast.success("Role description updated successfully");
      setIsEditingDescription(false);
      refetch();
    } catch (error) {
      toast.error("Failed to update role description");
      console.error("Error updating role description:", error);
    } finally {
      setIsUpdatingField(false);
      setShowProgressModal(false);
    }
  };

  const handleDelete = async () => {
    if (!roleData) return;

    try {
      setProgressModalTitle("Deleting custom role...");
      setShowProgressModal(true);

      await deleteCustomRole({
        id: roleData.id,
        payerId,
        deletedBy: username,
      }).unwrap();

      toast.success("Custom role deleted successfully");
      navigate("/users/roles/custom");
    } catch (error: unknown) {
      const errorObj = error as { data?: RoleManagementDeletionResponse };
      const errorMessage = errorObj.data?.message || "Failed to delete custom role";
      toast.error(errorMessage);
      console.error("Error deleting custom role:", error);
      setShowProgressModal(false);
    }
  };

  const handleCloseAddPermissionsModal = () => {
    setShowAddPermissionsModal(false);
  };

  const handleAddPermissions = async (permissions: string[]) => {
    if (!roleData) return;

    try {
      setProgressModalTitle("Adding permissions...");
      setShowProgressModal(true);

      const payload: CustomRoleUpdateRequest = {
        id: roleData.id,
        payerId,
        body: {
          permissionsToAdd: permissions,
          updatedBy: username,
        },
      };

      await updateCustomRole(payload).unwrap();

      setSelectedPermissionNames((prev) => {
        const newSet = new Set(prev);
        permissions.forEach((permission) => newSet.add(permission));
        return newSet;
      });

      toast.success("Permissions added to role successfully");
      refetch();
    } catch (error) {
      toast.error("Failed to add permissions to role");
      console.error("Error adding permissions to role:", error);
    } finally {
      setShowProgressModal(false);
      setShowAddPermissionsModal(false);
    }
  };

  const handleRemovePermission = async (permission: string) => {
    if (!roleData) return;

    try {
      setProgressModalTitle("Removing permission...");
      setShowProgressModal(true);

      const payload: CustomRoleUpdateRequest = {
        id: roleData.id,
        payerId,
        body: {
          permissionsToRemove: [permission],
          updatedBy: username,
        },
      };

      await updateCustomRole(payload).unwrap();

      setSelectedPermissionNames((prev) => {
        const newSet = new Set(prev);
        newSet.delete(permission);
        return newSet;
      });

      toast.success("Permission removed from role successfully");
      refetch();
    } catch (error) {
      toast.error("Failed to remove permission from role");
      console.error("Error removing permission from role:", error);
    } finally {
      setShowProgressModal(false);
    }
  };

  if (isLoading) {
    return (
      <MainWrapper className="flex flex-col gap-6">
        <div className="flex items-center justify-center py-44">
          <LoadingIcon className="h-6 w-6 text-blue-400" />
          <p className="ml-2 text-blue-700">Loading custom role...</p>
        </div>
      </MainWrapper>
    );
  }

  if (error) {
    return (
      <MainWrapper className="flex flex-col gap-6">
        <div className="flex items-center justify-center py-44">
          <p className="text-red-700">Error loading custom role. Try again later.</p>
        </div>
      </MainWrapper>
    );
  }

  if (!roleData) {
    return (
      <MainWrapper className="flex flex-col gap-6">
        <div className="flex items-center justify-center py-44">
          <p className="text-red-700">Custom role not found.</p>
        </div>
      </MainWrapper>
    );
  }

  return (
    <MainWrapper className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <TopBackButton onClick={() => navigate("/users/roles/custom")} className="mt-1" />
          <Text variant="heading">{roleData.name}</Text>
          {roleData.isPredefined && <Badge color="yellow" text="Predefined" />}
        </div>
        <div className="flex items-center gap-2">
          {!roleData.isPredefined && (
            <Button
              variant="destructive"
              className="flex items-center gap-2"
              onClick={() => setShowDeleteConfirm(true)}
            >
              <TrashIcon className="h-4 w-4" />
              Delete
            </Button>
          )}
        </div>
      </div>

      <TabGroup
        tabs={[
          {
            title: "Details",
            content: (
              <CustomRoleInfo
                role={roleData}
                isEditingName={isEditingName}
                setIsEditingName={setIsEditingName}
                isEditingDescription={isEditingDescription}
                setIsEditingDescription={setIsEditingDescription}
                name={name}
                setName={setName}
                description={description}
                setDescription={setDescription}
                onSaveName={handleSaveName}
                onSaveDescription={handleSaveDescription}
                isUpdating={isUpdatingField}
              />
            ),
          },
          {
            title: "Permissions",
            content: (
              <CustomRolePermissions
                role={roleData}
                onAddPermissions={() => setShowAddPermissionsModal(true)}
                onRemovePermission={handleRemovePermission}
              />
            ),
          },
          {
            title: "Users",
            content: <CustomRoleUsers role={roleData} payerId={payerId} />,
          },
        ]}
      />

      <CustomRoleDeleteDialog
        show={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onDelete={handleDelete}
        roleName={roleData.name}
        isDeleting={isDeleting}
      />

      <AddCustomRolePermissionsModal
        show={showAddPermissionsModal}
        onClose={handleCloseAddPermissionsModal}
        selectedPermissionNames={selectedPermissionNames}
        onAddPermissions={handleAddPermissions}
      />

      <ProgressModal
        isProgressModalOpen={showProgressModal}
        onClose={() => setShowProgressModal(false)}
        title={progressModalTitle}
        description="Please wait while we process your request..."
      />
    </MainWrapper>
  );
}
