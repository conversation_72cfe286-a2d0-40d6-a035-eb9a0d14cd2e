import { useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Submit<PERSON>and<PERSON>, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import MainWrapper from "../../components/ui/MainWrapper";
import Text from "../../components/ui/typography/Text";
import TopBackButton from "../../components/ui/TopBackButton";
import Button from "../../components/ui/Button";
import UserService from "../../services/UserService";
import { PlusIcon } from "@heroicons/react/24/outline";
import AddCustomRolePermissionsModal from "./add-custom-role-permissions-modal";
import { XMarkIcon } from "@heroicons/react/24/solid";
import { toast } from "react-toastify";
import { useCreateCustomRoleMutation } from "../../api/features/membershipApi";
import { mapRoleNameToDescriptiveName } from "../../utils/user-management-utils";
import BackButtonWarningOnEditModal from "../../components/ui/modal/back-button-warning-on-edit-modal";
import CharacterCount from "../../components/ui/character-count";
import {
  CustomRoleInput,
  customRoleSchema,
  DESCRIPTION_MAX_LENGTH,
  NAME_MAX_LENGTH,
} from "../../utils/validation-schemas";

export default function CreateCustomRole() {
  const navigate = useNavigate();
  const [selectedPermissions, setSelectedPermissions] = useState<Set<string>>(new Set());
  const [showAddPermissionsModal, setShowAddPermissionsModal] = useState<boolean>(false);
  const [showSummary, setShowSummary] = useState<boolean>(false);

  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const username = UserService.getUsername();

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<CustomRoleInput>({
    resolver: zodResolver(customRoleSchema),
    mode: "onChange",
  });

  const [createCustomRole, { isLoading: isCreating }] = useCreateCustomRoleMutation();

  const watchedName = watch("name") || "";
  const watchedDescription = watch("description") || "";

  const onSubmit: SubmitHandler<CustomRoleInput> = async (data) => {
    if (selectedPermissions.size === 0) {
      toast.error("Please select at least one permission");
      return;
    }

    try {
      await createCustomRole({
        name: data.name,
        description: data.description,
        permissions: Array.from(selectedPermissions),
        createdBy: username || "",
        payerId: payerId,
      }).unwrap();

      toast.success("Custom role created successfully");
      navigate("/users/roles/custom");
    } catch (error: unknown) {
      const errorObj = error as { data?: { error?: string } };
      const errorMessage = errorObj.data?.error || "Failed to create custom role.";
      toast.error(errorMessage);
      console.error("Error creating custom role:", error);
    }
  };

  const handlePermissionToggle = (roleName: string) => {
    setSelectedPermissions((prev) => {
      const newPermissions = new Set(prev);
      if (newPermissions.has(roleName)) {
        newPermissions.delete(roleName);
      } else {
        newPermissions.add(roleName);
      }
      return newPermissions;
    });
  };

  const handleClearAllPermissions = () => {
    setSelectedPermissions(new Set());
  };

  const handleAddPermissions = (permissions: string[]) => {
    setSelectedPermissions(new Set([...selectedPermissions, ...permissions]));
  };

  const handleReviewClick = () => {
    setShowSummary(true);
  };

  const selectedPermissionsArray = useMemo(() => {
    return Array.from(selectedPermissions);
  }, [selectedPermissions]);

  const [isBackButtonWarningModalOpen, setIsBackButtonWarningModalOpen] = useState(false);
  const handleBackClick = () => {
    setIsBackButtonWarningModalOpen(true);
  };

  const handleBackButtonConfirm = () => {
    navigate("/users/roles/custom");
  };

  return (
    <MainWrapper className="flex flex-col gap-6">
      <section className="flex space-x-6">
        <TopBackButton onClick={handleBackClick} className="mt-1" />
        <div className="flex flex-col space-y-1">
          <Text variant="heading">Create New Custom Role</Text>
          <Text variant="description" className="italic">
            Create a new custom role with specific permissions.
          </Text>
        </div>
      </section>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {!showSummary ? (
          <>
            <div className="rounded-lg border p-6 shadow-sm">
              <Text variant="subheading" className="mb-4">
                Role Information
              </Text>

              <div className="space-y-4">
                <div>
                  <label htmlFor="name" className="mb-1 block text-sm font-medium text-gray-700">
                    Role Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    id="name"
                    type="text"
                    className="w-full rounded-md border border-gray-300 p-2"
                    maxLength={NAME_MAX_LENGTH}
                    {...register("name")}
                  />
                  <div className="mt-1 flex items-center justify-between">
                    {errors.name && <p className="text-xs text-red-600">{errors.name.message}</p>}
                    <CharacterCount
                      current={watchedName.length}
                      max={NAME_MAX_LENGTH}
                      className="ml-auto"
                    />
                  </div>
                </div>

                <div>
                  <label
                    htmlFor="description"
                    className="mb-1 block text-sm font-medium text-gray-700"
                  >
                    Description <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    id="description"
                    className="h-20 w-full resize-none rounded-md border border-gray-300 p-2"
                    maxLength={DESCRIPTION_MAX_LENGTH}
                    {...register("description")}
                  />
                  <div className="mt-1 flex items-center justify-between">
                    {errors.description && (
                      <p className="text-xs text-red-600">{errors.description.message}</p>
                    )}
                    <CharacterCount
                      current={watchedDescription.length}
                      max={DESCRIPTION_MAX_LENGTH}
                      className="ml-auto"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="rounded-lg border p-6 shadow-sm">
              <div className="mb-4 flex items-center justify-between">
                <Text variant="subheading">Assign Permissions</Text>
                <div className="flex gap-2">
                  <Button
                    variant="outlined"
                    type="button"
                    className="flex items-center gap-1 text-sm"
                    onClick={() => setShowAddPermissionsModal(true)}
                  >
                    <PlusIcon className="h-4 w-4" /> Add Permissions
                  </Button>
                  {selectedPermissions.size > 0 && (
                    <Button
                      variant="destructive"
                      type="button"
                      className="flex items-center gap-1 text-sm"
                      onClick={handleClearAllPermissions}
                    >
                      <XMarkIcon className="h-4 w-4" /> Clear All
                    </Button>
                  )}
                </div>
              </div>

              {selectedPermissions.size > 0 ? (
                <div className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    {Array.from(selectedPermissions).map((permission) => (
                      <div key={permission} className="flex items-center gap-1">
                        <p>{mapRoleNameToDescriptiveName(permission)}</p>
                        <button
                          type="button"
                          onClick={() => handlePermissionToggle(permission)}
                          className="ml-1 rounded-full p-0.5 hover:bg-blue-200"
                          title="Remove Permission"
                          aria-label="Remove Permission"
                        >
                          <XMarkIcon className="h-3 w-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                  <Text variant="description">
                    {selectedPermissions.size} permission(s) selected
                  </Text>
                </div>
              ) : (
                <div className="py-8 text-center text-gray-500">
                  No permissions selected. Click "Add Permissions" to select permissions for this
                  role.
                </div>
              )}
            </div>

            <div className="flex justify-end space-x-4">
              <Button
                variant="outlined"
                onClick={() => navigate("/users/roles/custom")}
                type="button"
              >
                Cancel
              </Button>
              <Button
                type="button"
                disabled={selectedPermissions.size === 0 || !watch("name") || !watch("description")}
                onClick={handleReviewClick}
              >
                Review Selections
              </Button>
            </div>
          </>
        ) : (
          <>
            <div className="rounded-lg border p-6 shadow-sm">
              <Text variant="subheading" className="mb-4">
                Review Role
              </Text>

              <div className="space-y-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Role Name</p>
                  <p className="text-gray-900">{watch("name")}</p>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-500">Description</p>
                  <p className="text-gray-900">{watch("description")}</p>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Permissions ({selectedPermissions.size})
                  </p>
                  <ol className="mt-2 list-inside list-disc">
                    {selectedPermissionsArray.map((permission) => (
                      <li key={permission}>{mapRoleNameToDescriptiveName(permission)}</li>
                    ))}
                  </ol>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-4">
              <Button variant="outlined" onClick={() => setShowSummary(false)} type="button">
                Back to Edit
              </Button>
              <Button variant="filled" type="submit" disabled={isCreating}>
                {isCreating ? "Creating..." : "Create Custom Role"}
              </Button>
            </div>
          </>
        )}
      </form>

      <AddCustomRolePermissionsModal
        show={showAddPermissionsModal}
        onClose={() => setShowAddPermissionsModal(false)}
        selectedPermissionNames={selectedPermissions}
        onAddPermissions={handleAddPermissions}
      />

      <BackButtonWarningOnEditModal
        onClose={() => setIsBackButtonWarningModalOpen(false)}
        isBackButtonWarningModalOpen={isBackButtonWarningModalOpen}
        onConfirm={handleBackButtonConfirm}
      />
    </MainWrapper>
  );
}
