import { useNavigate, useParams } from "react-router";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { useGetUserByIdQuery } from "../../api/features/membershipApi";
import MainWrapper from "../../components/ui/MainWrapper";
import TopBackButton from "../../components/ui/TopBackButton";
import Text from "../../components/ui/typography/Text";
import Badge from "../../components/ui/Badge";
import { ShieldCheckIcon } from "@heroicons/react/24/solid";

export default function SuperAdminUserProfile() {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();

  const { data: user, isLoading, error } = useGetUserByIdQuery(userId || "", { skip: !userId });

  const handleBackClick = () => {
    navigate("/users");
  };

  if (isLoading) {
    return (
      <MainWrapper>
        <div className="flex h-full w-full items-center justify-center">
          <LoadingIcon className="h-8 w-8 text-primary" />
        </div>
      </MainWrapper>
    );
  }

  if (error) {
    return (
      <MainWrapper>
        <div className="flex h-full w-full flex-col items-center justify-center">
          <Text variant="heading" className="text-red-600">
            Error
          </Text>
          <Text>Failed to load user data. Please try again later.</Text>
        </div>
      </MainWrapper>
    );
  }

  if (!user) {
    return (
      <MainWrapper>
        <div className="flex h-full w-full flex-col items-center justify-center">
          <Text variant="heading">No User Found</Text>
          <Text>The requested user could not be found.</Text>
        </div>
      </MainWrapper>
    );
  }

  return (
    <MainWrapper>
      <div className="mb-6 flex items-center justify-between">
        <div className="flex gap-4">
          <TopBackButton onClick={handleBackClick} className="mt-1" />
          <div>
            <Text variant="heading">Super Admin Profile</Text>
            <Text className="text-gray-500">View super admin user details</Text>
          </div>
        </div>
      </div>

      {/* User Information Card */}
      <section className="mb-6 rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
        <div className="mb-4 flex items-center justify-between">
          <Text variant="subheading" className="flex items-center gap-2">
            <ShieldCheckIcon className="h-6 w-6 text-blue-600" />
            Super Admin Information
          </Text>
          <Badge color="blue" text="Full System Access" hasDot />
        </div>

        <div className="mt-6 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          <div>
            <Text className="text-sm font-medium text-gray-500">Name</Text>
            <Text>
              {user.firstName} {user.lastName}
            </Text>
          </div>
          <div>
            <Text className="text-sm font-medium text-gray-500">Username</Text>
            <Text>{user.username}</Text>
          </div>
          <div>
            <Text className="text-sm font-medium text-gray-500">Email</Text>
            <Text>{user.email || "-"}</Text>
          </div>
          <div>
            <Text className="text-sm font-medium text-gray-500">Status</Text>
            <Text>
              {user.enabled ? (
                <span className="text-green-600">Active</span>
              ) : (
                <span className="text-yellow-600">Inactive</span>
              )}
            </Text>
          </div>
        </div>
      </section>

      {/* Super Admin Description */}
      <section className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
        <Text variant="subheading" className="mb-4">
          Super Admin Privileges
        </Text>
        <div className="rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <ShieldCheckIcon className="h-5 w-5 text-blue-600" aria-hidden="true" />
            </div>
            <div className="ml-3">
              <Text className="text-sm font-medium">
                This user has full administrative access to the system
              </Text>
              <div className="mt-2 text-sm">
                <ul className="list-disc space-y-1 pl-5">
                  <li>Can manage all users and their permissions</li>
                  <li>Has access to all modules and features</li>
                  <li>Can perform all administrative functions</li>
                  <li>Cannot be modified through the regular user interface</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>
    </MainWrapper>
  );
}
