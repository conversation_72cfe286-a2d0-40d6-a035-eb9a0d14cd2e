import { useEffect, useMemo, useState } from "react";
import { useGetAllPermissionsQuery } from "../../api/features/membershipApi";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import Text from "../../components/ui/typography/Text";
import Button from "../../components/ui/Button";
import SearchInput from "../../components/ui/input/SearchInput";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { PermissionResponse } from "../../lib/types/access-control/role";
import { toast } from "react-toastify";
import {
  DenyPolicyPermission,
  mapPermissionsToDenyPolicyPermissions,
} from "../../utils/user-management-utils";
import { XMarkIcon } from "@heroicons/react/24/outline";

interface AddDenyPolicyPermissionsModalProps {
  show: boolean;
  onClose: () => void;
  selectedPermissionNames: Set<string>;
  onAddPermissions: (permissions: PermissionResponse[]) => void;
}

export default function AddDenyPolicyPermissionsModal({
  show,
  onClose,
  selectedPermissionNames,
  onAddPermissions,
}: AddDenyPolicyPermissionsModalProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [localSelectedPermissionNames, setLocalSelectedPermissionNames] = useState<Set<string>>(
    new Set(),
  );
  const [localSelectedPermissions, setLocalSelectedPermissions] = useState<PermissionResponse[]>(
    [],
  );
  const [mappedPermissions, setMappedPermissions] = useState<DenyPolicyPermission[]>([]);

  const { data: permissionsData, isLoading } = useGetAllPermissionsQuery();

  useEffect(() => {
    if (show) {
      setLocalSelectedPermissionNames(new Set());
      setLocalSelectedPermissions([]);
    }
  }, [show]);

  useEffect(() => {
    if (permissionsData) {
      const mapped = mapPermissionsToDenyPolicyPermissions(permissionsData);
      setMappedPermissions(mapped);
    }
  }, [permissionsData]);

  const handleTogglePermission = (permission: DenyPolicyPermission) => {
    const originalPermission = permissionsData?.find((p) => p.name === permission.roleName);
    if (!originalPermission) {
      console.error("Could not find original permission for", permission.roleName);
      return;
    }

    setLocalSelectedPermissionNames((prev) => {
      const newNames = new Set(prev);
      if (newNames.has(permission.roleName)) {
        newNames.delete(permission.roleName);
        setLocalSelectedPermissions(
          localSelectedPermissions.filter((p) => p.name !== permission.roleName),
        );
      } else {
        newNames.add(permission.roleName);
        setLocalSelectedPermissions([...localSelectedPermissions, originalPermission]);
      }
      return newNames;
    });
  };

  const handleAddPermissions = () => {
    if (localSelectedPermissions.length === 0) {
      toast.error("Please select at least one permission to add");
      return;
    }

    const newPermissions = localSelectedPermissions.filter(
      (permission) => !selectedPermissionNames.has(permission.name),
    );

    if (newPermissions.length === 0) {
      toast.error("All selected permissions are already in the deny policy");
      return;
    }

    onAddPermissions(newPermissions);
    onClose();
  };

  const handleCancel = () => {
    setLocalSelectedPermissionNames(new Set());
    setLocalSelectedPermissions([]);
    setSearchTerm("");
  };

  const handleClose = () => {
    setLocalSelectedPermissionNames(new Set());
    setLocalSelectedPermissions([]);
    setSearchTerm("");
    onClose();
  };

  const availablePermissions = useMemo(() => {
    return mappedPermissions.filter(
      (permission) => !selectedPermissionNames.has(permission.roleName),
    );
  }, [mappedPermissions, selectedPermissionNames]);

  const filteredPermissions = useMemo(() => {
    return availablePermissions.filter(
      (permission) =>
        permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (permission.description &&
          permission.description.toLowerCase().includes(searchTerm.toLowerCase())),
    );
  }, [availablePermissions, searchTerm]);

  const newlySelectedCount = localSelectedPermissionNames.size;

  return (
    <DialogWrapper show={show} onClose={handleClose} maxWidth="max-w-5xl" className="h-[660px]">
      <div>
        <div className="relative border-b border-gray-200 px-6 py-4">
          <Text variant="subheading">Add Permissions to Deny Policy</Text>
          <button
            onClick={handleClose}
            className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
            aria-label="Close modal"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
        <div className="p-6">
          <div className="mb-4 flex justify-between">
            <Text variant="description">Select permissions to deny the selected user(s).</Text>
            <SearchInput
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search permissions..."
              className="w-64"
            />
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <LoadingIcon className="h-6 w-6 text-blue-400" />
              <span className="ml-2 text-gray-600">Loading permissions...</span>
            </div>
          ) : (
            <>
              <div className="mb-4 max-h-96 overflow-y-auto rounded-md border border-gray-200 p-4">
                <div className="space-y-4">
                  {filteredPermissions.length > 0 ? (
                    filteredPermissions.map((permission) => (
                      <div
                        key={permission.roleName}
                        className="rounded-md border border-gray-200 p-3 hover:bg-gray-50"
                      >
                        <div className="flex items-start">
                          <div className="mr-2 mt-0.5">
                            <input
                              type="checkbox"
                              id={`permission-${permission.roleName}`}
                              checked={localSelectedPermissionNames.has(permission.roleName)}
                              onChange={() => handleTogglePermission(permission)}
                              className="h-4 w-4 rounded border-gray-300 text-blue-600"
                            />
                          </div>
                          <div>
                            <label
                              htmlFor={`permission-${permission.roleName}`}
                              className="block font-medium text-gray-700"
                            >
                              {permission.name}
                            </label>
                            {permission.description && (
                              <p className="mt-1 text-sm text-gray-500">{permission.description}</p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="py-8 text-center text-gray-500">No permissions found</div>
                  )}
                </div>
              </div>

              <div className="mb-4">
                <Text variant="description">{newlySelectedCount} permission(s) selected</Text>
              </div>
            </>
          )}

          <div className="mt-6 flex justify-end space-x-3">
            <Button variant="outlined" onClick={handleCancel} disabled={newlySelectedCount === 0}>
              Clear Selection
            </Button>
            <Button
              variant="filled"
              onClick={handleAddPermissions}
              disabled={newlySelectedCount === 0}
            >
              Add Permissions
            </Button>
          </div>
        </div>
      </div>
    </DialogWrapper>
  );
}
