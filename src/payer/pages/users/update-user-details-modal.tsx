import { useForm } from "react-hook-form";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import Button from "../../components/ui/Button";
import Text from "../../components/ui/typography/Text";
import RequiredTag from "../../components/ui/input/required-tag";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { PayerUserUpdateRequest } from "../../lib/types/access-control/user";
import { toast } from "react-toastify";
import { XMarkIcon } from "@heroicons/react/24/outline";

interface UpdateUserDetailsModalProps {
  show: boolean;
  onClose: () => void;
  userId: string;
  initialFirstName: string;
  initialLastName: string;
  initialEmail?: string;
  actionedBy: string;
  onSubmit: (payload: PayerUserUpdateRequest) => Promise<void>;
  isLoading: boolean;
}

type FormValues = {
  firstName: string;
  lastName: string;
  email: string;
  reason: string;
};

export default function UpdateUserDetailsModal({
  show,
  onClose,
  userId,
  initialFirstName,
  initialLastName,
  initialEmail = "",
  actionedBy,
  onSubmit,
  isLoading,
}: UpdateUserDetailsModalProps) {
  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors },
  } = useForm<FormValues>({
    defaultValues: {
      firstName: initialFirstName,
      lastName: initialLastName,
      email: initialEmail,
      reason: "",
    },
  });

  const onFormSubmit = async (data: FormValues) => {
    try {
      const payload: PayerUserUpdateRequest = {
        userId,
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        updatedBy: actionedBy,
        reason: data.reason,
      };

      await onSubmit(payload);
      reset();
    } catch (error) {
      console.error("Error updating user details:", error);
      toast.error("Failed to update user details. Please try again.");
    }
  };

  const handleClose = () => {
    reset({
      firstName: initialFirstName,
      lastName: initialLastName,
      email: initialEmail,
      reason: "",
    });
    onClose();
  };

  return (
    <DialogWrapper show={show} onClose={handleClose} maxWidth="max-w-3xl" className="p-6">
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <Text variant="heading">Update User Details</Text>
          <button>
            <XMarkIcon className="h-5 w-5" onClick={onClose} />
          </button>
        </div>
        <p className="text-sm text-gray-500">
          Update the user's first name, last name, and email. Please provide a reason for this
          update.
        </p>

        <form onSubmit={handleSubmit(onFormSubmit)} className="flex flex-col gap-4">
          <div className="flex flex-col gap-2">
            <label htmlFor="firstName" className="text-sm font-medium">
              First Name <RequiredTag />
            </label>
            <input
              id="firstName"
              type="text"
              className="rounded-md border border-gray-300 px-3 py-2 text-sm"
              placeholder="Enter first name"
              {...register("firstName", { required: "First name is required" })}
            />
            {errors.firstName && (
              <span className="text-xs text-red-500">{errors.firstName.message}</span>
            )}
          </div>

          <div className="flex flex-col gap-2">
            <label htmlFor="lastName" className="text-sm font-medium">
              Last Name <RequiredTag />
            </label>
            <input
              id="lastName"
              type="text"
              className="rounded-md border border-gray-300 px-3 py-2 text-sm"
              placeholder="Enter last name"
              {...register("lastName", { required: "Last name is required" })}
            />
            {errors.lastName && (
              <span className="text-xs text-red-500">{errors.lastName.message}</span>
            )}
          </div>

          <div className="flex flex-col gap-2">
            <label htmlFor="email" className="text-sm font-medium">
              Email <RequiredTag />
            </label>
            <input
              id="email"
              type="email"
              className="rounded-md border border-gray-300 px-3 py-2 text-sm"
              placeholder="Enter email address"
              {...register("email", {
                required: "Email is required",
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: "Invalid email address",
                },
              })}
            />
            {errors.email && <span className="text-xs text-red-500">{errors.email.message}</span>}
          </div>

          <div className="flex flex-col gap-2">
            <label htmlFor="reason" className="text-sm font-medium">
              Reason for Update <RequiredTag />
            </label>
            <textarea
              id="reason"
              className="rounded-md border border-gray-300 px-3 py-2 text-sm"
              placeholder="Enter reason for update"
              rows={3}
              {...register("reason", { required: "Reason is required" })}
            />
            {errors.reason && <span className="text-xs text-red-500">{errors.reason.message}</span>}
          </div>

          <div className="mt-4 flex justify-end gap-3">
            <Button type="button" variant="outlined" onClick={handleClose} disabled={isLoading}>
              Cancel
            </Button>
            {(() => {
              const currentFirstName = watch("firstName");
              const currentLastName = watch("lastName");
              const currentEmail = watch("email");
              const hasChanges =
                currentFirstName !== initialFirstName ||
                currentLastName !== initialLastName ||
                currentEmail !== initialEmail;
              const hasReason = !!watch("reason");
              const isFormValid = !Object.keys(errors).length;

              return (
                <Button
                  type="submit"
                  disabled={isLoading || !hasChanges || !hasReason || !isFormValid}
                >
                  {isLoading ? (
                    <>
                      <LoadingIcon className="mr-2 h-5 w-5" />
                      Updating...
                    </>
                  ) : (
                    "Update Details"
                  )}
                </Button>
              );
            })()}
          </div>
        </form>
      </div>
    </DialogWrapper>
  );
}
