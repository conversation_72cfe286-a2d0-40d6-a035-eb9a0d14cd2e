import React from "react";
import MainWrapper from "../../../components/ui/MainWrapper";
import LoadingIcon from "~lib/components/icons/LoadingIcon";

interface LoadingStateProps {
  message?: string;
}

export default function LoadingState({
  message = "Loading User Information...",
}: LoadingStateProps) {
  return (
    <MainWrapper>
      <div className="flex items-center justify-center space-x-2 self-center py-44">
        <LoadingIcon className="h-6 w-6 text-blue-400" />
        <p className="text-blue-700">{message}</p>
      </div>
    </MainWrapper>
  );
}
