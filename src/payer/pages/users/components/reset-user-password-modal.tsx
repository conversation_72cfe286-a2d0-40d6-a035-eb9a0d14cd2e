import { ExclamationTriangleIcon, XMarkIcon } from "@heroicons/react/24/outline";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import Button from "../../../components/ui/Button";
import DialogWrapper from "../../../components/ui/modal/DialogWrapper";
import Text from "../../../components/ui/typography/Text";

interface ResetUserPasswordModalProps {
  show: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading: boolean;
  userName?: string;
}

export default function ResetUserPasswordModal({
  show,
  onClose,
  onConfirm,
  isLoading,
  userName,
}: ResetUserPasswordModalProps) {
  return (
    <DialogWrapper show={show} onClose={onClose} maxWidth="max-w-2xl">
      <div className="p-6">
        <section className="flex items-center justify-between pb-2">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <ExclamationTriangleIcon className="h-6 w-6 text-amber-500" />
            </div>
            <Text variant="subheading">Reset User Password</Text>
          </div>
          <button>
            <XMarkIcon className="h-5 w-5" onClick={onClose} />
          </button>
        </section>

        <div className="mb-6">
          <Text variant="paragraph" className="mb-3 text-gray-600">
            Are you sure you want to reset the password for{" "}
            <span className="font-medium text-gray-900">{userName}</span>?
          </Text>

          <div className="rounded-md border bg-blue-50 p-3">
            <Text variant="description" className="text-gray-700">
              <strong>Note:</strong> The user's password will be reset to the default password{" "}
              <code className="text-sm font-bold">1234</code>. The user will need to change this
              password on their next login.
            </Text>
          </div>
        </div>

        <div className="flex justify-end gap-3">
          <Button variant="outlined" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={onConfirm} disabled={isLoading} className="flex items-center gap-2">
            {isLoading && <LoadingIcon className="h-4 w-4 text-white" />}
            Reset Password
          </Button>
        </div>
      </div>
    </DialogWrapper>
  );
}
