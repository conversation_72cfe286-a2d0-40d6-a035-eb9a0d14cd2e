import { Dialog } from "@headlessui/react";
import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import Button from "../../../components/ui/Button";
import DialogWrapper from "../../../components/ui/modal/DialogWrapper";

interface RemoveCustomRoleFromUserModalProps {
  show: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  isLoading: boolean;
  roleName: string;
  userName?: string | undefined;
}

export default function RemoveCustomRoleFromUserModal({
  show,
  onClose,
  onConfirm,
  isLoading,
  roleName,
  userName,
}: RemoveCustomRoleFromUserModalProps) {
  return (
    <DialogWrapper show={show} onClose={onClose} maxWidth="max-w-xl" className="p-6">
      <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
        Remove Custom Role
      </Dialog.Title>
      <div className="mt-2">
        <div className="flex items-center gap-3 text-sm text-gray-500">
          <p>
            Are you sure you want to remove the custom role{" "}
            <span className="font-semibold text-black">{roleName}</span> from{" "}
            {userName ? <span className="font-semibold text-black">{userName}</span> : "this user"}?
          </p>
        </div>
      </div>

      <div className="mt-6 flex justify-end gap-3">
        <Button onClick={onClose} disabled={isLoading} variant="outlined">
          Cancel
        </Button>
        <Button
          variant="destructive"
          onClick={onConfirm}
          disabled={isLoading}
          className="flex items-center gap-2"
        >
          {isLoading ? (
            <>
              <LoadingIcon className="h-4 w-4" />
              Removing...
            </>
          ) : (
            "Remove Role"
          )}
        </Button>
      </div>
    </DialogWrapper>
  );
}
