import { CheckIcon } from "@heroicons/react/24/solid";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import Button from "../../../components/ui/Button";
import Text from "../../../components/ui/typography/Text";
import { CustomRoleResponse } from "../../../lib/types/access-control/role";
import RemoveCustomRoleFromUserModal from "./remove-custom-role-from-user-modal";
import ProgressModal from "../../../components/ui/modal/ProgressModal";
import UserService from "../../../services/UserService";
import { toast } from "react-toastify";
import { useState } from "react";
import { useRemoveCustomRoleFromUserMutation } from "../../../api/features/membershipApi";

interface CustomRolesPanelProps {
  availableCustomRoles: CustomRoleResponse[];
  selectedCustomRoles: CustomRoleResponse[];
  toggleCustomRoleSelection: (role: CustomRoleResponse) => void;
  handleAssignCustomRoles: () => void;
  isAssigningCustomRoles: boolean;
  assignedCustomRoles: CustomRoleResponse[] | undefined;
  userId: string | undefined;
  username: string;
}

export default function UserInfoCustomRolesPanel({
  availableCustomRoles,
  selectedCustomRoles,
  toggleCustomRoleSelection,
  handleAssignCustomRoles,
  isAssigningCustomRoles,
  assignedCustomRoles,
  userId,
  username,
}: CustomRolesPanelProps) {
  const handleOpenRemoveCustomRoleConfirm = (role: CustomRoleResponse) => {
    setSelectedCustomRole(role);
    setShowRemoveCustomRoleConfirm(true);
  };
  const [showRemoveCustomRoleConfirm, setShowRemoveCustomRoleConfirm] = useState(false);
  const [selectedCustomRole, setSelectedCustomRole] = useState<CustomRoleResponse | null>(null);
  const [showRemoveCustomRoleProgress, setShowRemoveCustomRoleProgress] = useState(false);

  const [removeCustomRoleFromUser, { isLoading: isRemovingCustomRole }] =
    useRemoveCustomRoleFromUserMutation();

  const handleRemoveCustomRole = async (): Promise<void> => {
    if (!userId || !selectedCustomRole) {
      return Promise.resolve();
    }

    try {
      setShowRemoveCustomRoleProgress(true);

      const username = UserService.getUsername() || "";

      await removeCustomRoleFromUser({
        userId,
        roleId: selectedCustomRole.id,
        actionedBy: username,
      }).unwrap();

      toast.success(`Custom role "${selectedCustomRole.name}" removed successfully`);
      setShowRemoveCustomRoleConfirm(false);
      setSelectedCustomRole(null);
    } catch (error) {
      console.error("Error removing custom role:", error);
      toast.error("Failed to remove custom role");
    } finally {
      setShowRemoveCustomRoleProgress(false);
    }
  };

  return (
    <>
      <div className="mb-6">
        {assignedCustomRoles && assignedCustomRoles.length > 0 ? (
          <div className="mt-2 flex flex-col gap-2 border-b py-4">
            <Text variant="subheading">Assigned Custom Roles</Text>
            <div className="mt-1 grid grid-cols-1 gap-3 md:grid-cols-2">
              {assignedCustomRoles.map((role) => (
                <div
                  key={role.id}
                  className="flex flex-col gap-3 rounded-md border border-gray-200 p-4"
                >
                  <div className="flex items-start justify-between">
                    <div>
                      <Text className="font-medium">{role.name}</Text>
                      <Text className="text-sm text-gray-500">{role.description}</Text>
                    </div>
                  </div>
                  <div className="self-end">
                    <Button
                      onClick={() => handleOpenRemoveCustomRoleConfirm(role)}
                      className="text-xs"
                      title="Remove Custom Role"
                    >
                      Remove
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="mt-2 flex flex-col gap-2 border-b p-4">
            <Text variant="subheading">Custom Roles</Text>
            <Text className="text-gray-500">No custom roles assigned directly to this user.</Text>
          </div>
        )}
      </div>
      <div className="mb-4">
        <Text variant="subheading">Available Custom Roles</Text>
        <Text className="text-sm text-gray-500">Select custom roles to assign to this user</Text>
      </div>

      {availableCustomRoles.length === 0 ? (
        <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 text-center">
          <Text>No available custom roles found or user already has all custom roles.</Text>
        </div>
      ) : (
        <>
          <div className="mb-4 grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3">
            {availableCustomRoles.map((role) => (
              <div
                key={role.id}
                className={`cursor-pointer rounded-lg border p-3 transition-colors
                  ${
                    selectedCustomRoles.some((r) => r.id === role.id)
                      ? "border-primary bg-primary/10"
                      : "border-gray-200 bg-white hover:bg-gray-50"
                  }`}
                onClick={() => toggleCustomRoleSelection(role)}
              >
                <div className="flex items-start justify-between">
                  <div>
                    <Text className="font-medium">{role.name}</Text>
                    <Text className="text-sm text-gray-500">{role.description}</Text>
                  </div>
                  {selectedCustomRoles.some((r) => r.id === role.id) && (
                    <CheckIcon className="h-5 w-5 text-primary" />
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className="flex justify-end">
            <Button
              variant="filled"
              onClick={handleAssignCustomRoles}
              disabled={selectedCustomRoles.length === 0 || isAssigningCustomRoles}
              className="flex items-center gap-2"
            >
              {isAssigningCustomRoles ? (
                <>
                  <LoadingIcon className="h-4 w-4" />
                  Assigning...
                </>
              ) : (
                `Assign ${selectedCustomRoles.length} Role${selectedCustomRoles.length !== 1 ? "s" : ""}`
              )}
            </Button>
          </div>
        </>
      )}
      <RemoveCustomRoleFromUserModal
        show={showRemoveCustomRoleConfirm}
        onClose={() => setShowRemoveCustomRoleConfirm(false)}
        onConfirm={handleRemoveCustomRole}
        isLoading={isRemovingCustomRole}
        roleName={selectedCustomRole?.name || ""}
        userName={username}
      />

      <ProgressModal
        isProgressModalOpen={showRemoveCustomRoleProgress}
        onClose={() => null}
        title="Removing Custom Role"
        description="Please wait while we remove the custom role from the user..."
      />
    </>
  );
}
