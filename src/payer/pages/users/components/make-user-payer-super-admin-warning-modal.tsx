import React from "react";
import Button from "../../../components/ui/Button";
import Text from "../../../components/ui/typography/Text";
import DialogWrapper from "../../../components/ui/modal/DialogWrapper";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { XMarkIcon } from "@heroicons/react/24/outline";

interface MakeSuperAdminModalProps {
  show: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  isLoading: boolean;
}

export default function MakeUserPayerSuperAdminWarningModal({
  show,
  onClose,
  onConfirm,
  isLoading,
}: MakeSuperAdminModalProps) {
  const handleConfirm = async () => {
    await onConfirm();
  };

  return (
    <DialogWrapper show={show} onClose={onClose} maxWidth="max-w-lg" className="p-6">
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <Text variant="heading">Make User Super Admin</Text>
          <button>
            <XMarkIcon className="h-5 w-5" onClick={onClose} />
          </button>
        </div>
        <div className="rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <Text className="text-sm font-medium">Warning</Text>
              <div className="mt-2 text-sm">
                <p>Making this user a Super Admin will:</p>
                <ul className="mt-1 list-disc space-y-1 pl-5">
                  <li>Remove them from all deny policies</li>
                  <li>Grant them all available permissions</li>
                  <li>Allow them to manage all aspects of the system</li>
                </ul>
                <p className="mt-2">
                  This action cannot be reversed and should be used with caution.
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-4 flex justify-end gap-3">
          <Button type="button" variant="outlined" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            type="button"
            variant="filled"
            onClick={handleConfirm}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            {isLoading ? (
              <>
                <LoadingIcon className="h-4 w-4" />
                Processing...
              </>
            ) : (
              "Confirm"
            )}
          </Button>
        </div>
      </div>
    </DialogWrapper>
  );
}
