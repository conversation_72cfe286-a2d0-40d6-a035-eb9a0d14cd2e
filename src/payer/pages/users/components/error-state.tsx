import React from "react";
import MainWrapper from "../../../components/ui/MainWrapper";
import { Empty } from "~lib/components";

interface ErrorStateProps {
  message?: string;
  isEmpty?: boolean;
}

export default function ErrorState({
  message = "Error loading user. Try again later.",
  isEmpty = false,
}: ErrorStateProps) {
  return (
    <MainWrapper>
      <div className="flex items-center justify-center self-center py-44">
        {isEmpty ? <Empty message="User not found!" /> : <p className="text-red-700">{message}</p>}
      </div>
    </MainWrapper>
  );
}
