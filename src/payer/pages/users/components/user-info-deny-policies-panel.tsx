import { CheckIcon } from "@heroicons/react/24/solid";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import Button from "../../../components/ui/Button";
import Text from "../../../components/ui/typography/Text";
import { DenyPolicyDto, UpdateDenyPolicyRequest } from "../../../lib/types/access-control/role";
import { useUpdateDenyPolicyMutation } from "../../../api/features/membershipApi";
import { useState } from "react";
import UserService from "../../../services/UserService";
import { toast } from "react-toastify";
import { mapRoleNameToDescriptiveName } from "../../../utils/user-management-utils";
import RemoveUserFromDenyPolicyModal from "./remove-user-from-deny-policy-modal";
import ProgressModal from "../../../components/ui/modal/ProgressModal";

interface DenyPoliciesPanelProps {
  availableDenyPolicies: DenyPolicyDto[];
  selectedDenyPolicies: DenyPolicyDto[];
  toggleDenyPolicySelection: (policy: DenyPolicyDto) => void;
  handleAddToDenyPolicies: () => void;
  isAddingToDenyPolicies: boolean;
  userId: string | undefined;
  userDenyPolicies: DenyPolicyDto[] | undefined;
  username: string;
}

export default function UserInfoDenyPoliciesPanel({
  availableDenyPolicies,
  selectedDenyPolicies,
  toggleDenyPolicySelection,
  handleAddToDenyPolicies,
  isAddingToDenyPolicies,
  userId,
  userDenyPolicies,
  username,
}: DenyPoliciesPanelProps) {
  const [updateDenyPolicy, { isLoading: isRemovingFromDenyPolicy }] = useUpdateDenyPolicyMutation();

  const [showRemoveFromDenyPolicyConfirm, setShowRemoveFromDenyPolicyConfirm] = useState(false);
  const [selectedDenyPolicy, setSelectedDenyPolicy] = useState<DenyPolicyDto | null>(null);

  const [showRemoveFromDenyPolicyProgress, setShowRemoveFromDenyPolicyProgress] = useState(false);

  const handleRemoveFromDenyPolicy = async (): Promise<void> => {
    if (!userId || !selectedDenyPolicy) {
      return Promise.resolve();
    }

    try {
      setShowRemoveFromDenyPolicyProgress(true);

      const username = UserService.getUsername() || "";

      const payload: UpdateDenyPolicyRequest = {
        id: selectedDenyPolicy.id,
        body: {
          removeUsers: [userId],
          updatedBy: username,
        },
      };

      await updateDenyPolicy(payload).unwrap();
      toast.success(`User removed from deny policy "${selectedDenyPolicy.name}" successfully`);
      setShowRemoveFromDenyPolicyConfirm(false);
      setSelectedDenyPolicy(null);
    } catch (error) {
      console.error("Error removing user from deny policy:", error);
      toast.error("Failed to remove user from deny policy");
    } finally {
      setShowRemoveFromDenyPolicyProgress(false);
    }
  };

  const handleOpenRemoveFromDenyPolicyConfirm = (policy: DenyPolicyDto) => {
    setSelectedDenyPolicy(policy);
    setShowRemoveFromDenyPolicyConfirm(true);
  };

  return (
    <>
      <div className="mb-6 flex flex-col gap-2 border-b py-4">
        <Text variant="subheading">User Deny Policies</Text>
        {userDenyPolicies && userDenyPolicies.length > 0 ? (
          <div className="mt-1 grid grid-cols-1 gap-3 md:grid-cols-2">
            {userDenyPolicies.map((policy) => (
              <div
                key={policy.id}
                className="flex flex-col gap-3 rounded-md border border-gray-200 p-4"
              >
                <div className="flex items-start justify-between">
                  <div>
                    <Text className="font-medium">{policy.name}</Text>
                    {policy.reason && (
                      <Text className="mt-1 text-sm text-gray-700">Reason: {policy.reason}</Text>
                    )}
                  </div>
                </div>

                <div className="mt-3">
                  <Text className="pb-2 text-xs font-medium text-gray-500">
                    Denied Permissions:
                  </Text>
                  <div className="flex flex-wrap items-center gap-2">
                    {policy.deniedPermissions.map((permission, index) => (
                      <span
                        key={index}
                        className="w-fit rounded-full bg-red-100 px-2 py-1 text-xs text-red-800"
                      >
                        {mapRoleNameToDescriptiveName(permission)}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="self-end pt-4">
                  <Button
                    onClick={() => handleOpenRemoveFromDenyPolicyConfirm(policy)}
                    className="text-xs"
                    title="Remove from Deny Policy"
                  >
                    Remove
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <Text className="text-gray-500">No deny policies applied to this user.</Text>
        )}
      </div>

      <div className="mb-4">
        <Text variant="subheading">Available Deny Policies</Text>
        <Text className="text-sm text-gray-500">Select deny policies to add this user to</Text>
      </div>

      {availableDenyPolicies.length === 0 ? (
        <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 text-center">
          <Text>
            No available deny policies found or user is already assigned to all deny policies.
          </Text>
        </div>
      ) : (
        <>
          <div className="mb-4 grid grid-cols-1 gap-3 md:grid-cols-2">
            {availableDenyPolicies.map((policy) => (
              <div
                key={policy.id}
                className={`cursor-pointer rounded-lg border p-3 transition-colors
                  ${
                    selectedDenyPolicies.some((p) => p.id === policy.id)
                      ? "border-primary bg-primary/10"
                      : "border-gray-200 bg-white hover:bg-gray-50"
                  }`}
                onClick={() => toggleDenyPolicySelection(policy)}
              >
                <div className="flex items-start justify-between">
                  <div>
                    <Text className="font-medium">{policy.name}</Text>
                    {policy.reason && (
                      <Text className="text-sm text-gray-500">Reason: {policy.reason}</Text>
                    )}
                  </div>
                  {selectedDenyPolicies.some((p) => p.id === policy.id) && (
                    <CheckIcon className="h-5 w-5 text-primary" />
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className="flex justify-end">
            <Button
              variant="filled"
              onClick={handleAddToDenyPolicies}
              disabled={selectedDenyPolicies.length === 0 || isAddingToDenyPolicies}
              className="flex items-center gap-2"
            >
              {isAddingToDenyPolicies ? (
                <>
                  <LoadingIcon className="h-4 w-4" />
                  Adding...
                </>
              ) : (
                `Add to ${selectedDenyPolicies.length} Deny ${selectedDenyPolicies.length !== 1 ? "Policies" : "Policy"}`
              )}
            </Button>
          </div>
        </>
      )}

      <RemoveUserFromDenyPolicyModal
        show={showRemoveFromDenyPolicyConfirm}
        onClose={() => setShowRemoveFromDenyPolicyConfirm(false)}
        onConfirm={handleRemoveFromDenyPolicy}
        isLoading={isRemovingFromDenyPolicy}
        policyName={selectedDenyPolicy?.name || ""}
        userName={username}
      />

      <ProgressModal
        isProgressModalOpen={showRemoveFromDenyPolicyProgress}
        onClose={() => null}
        title="Removing User from Deny Policy"
        description="Please wait while we remove the user from the deny policy..."
      />
    </>
  );
}
