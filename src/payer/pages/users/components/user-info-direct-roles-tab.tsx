import { UserDirectRolesResponse } from "../../../lib/types/access-control/role";
import {
  mapPermissionsToValidDisplayPermissions,
  mapRoleNameToDescriptiveName,
} from "../../../utils/user-management-utils";
import Button from "../../../components/ui/Button";
import { useState } from "react";
import DeleteDirectRolesModal from "./delete-direct-roles-modal";
import DeleteSingleRoleModal from "./delete-single-role-modal";
import { toast } from "react-toastify";
import UserService from "../../../services/UserService";
import { UpdateUserPayload } from "../../../lib/types/access-control/user";
import { useUpdateUserRolesMutation } from "../../../api/features/membershipApi";
import Text from "../../../components/ui/typography/Text";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { XMarkIcon } from "@heroicons/react/24/outline";

interface UserInfoDirectRolesTabProps {
  directRolesResponse: UserDirectRolesResponse | undefined;
}

export default function UserInfoDirectRolesTab({
  directRolesResponse,
}: UserInfoDirectRolesTabProps) {
  const [showSingleRoleDeleteConfirm, setShowSingleRoleDeleteConfirm] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [selectedRole, setSelectedRole] = useState<{ name: string; displayName: string } | null>(
    null,
  );

  const [updateUserRoles, { isLoading: isUpdatingRoles }] = useUpdateUserRolesMutation();

  const handleDeleteAllDirectRoles = async (): Promise<void> => {
    if (!directRolesResponse?.directRoles || !directRolesResponse.userId) {
      return Promise.resolve();
    }

    try {
      const validRoles = mapPermissionsToValidDisplayPermissions(directRolesResponse.directRoles);

      const rolesToRemove = directRolesResponse.directRoles
        .filter((role) => {
          return validRoles.some(
            (validRole) => mapRoleNameToDescriptiveName(role.name) === validRole.name,
          );
        })
        .map((role) => role.name);

      if (rolesToRemove.length === 0) {
        toast.info("No direct roles to remove");
        return Promise.resolve();
      }

      const username = UserService.getUsername() || "";

      const payload: UpdateUserPayload = {
        userId: directRolesResponse.userId,
        payload: {
          addedRoles: [],
          removedRoles: rolesToRemove,
          actionedBy: username,
        },
      };

      await updateUserRoles(payload).unwrap();
      toast.success("All direct roles removed successfully");
      setShowDeleteConfirm(false);
      return Promise.resolve();
    } catch (error) {
      console.error("Error removing direct roles:", error);
      toast.error("Failed to remove direct roles");
      return Promise.reject(error);
    }
  };

  const handleDeleteSingleRole = async (): Promise<void> => {
    if (!directRolesResponse?.directRoles || !directRolesResponse.userId || !selectedRole) {
      return Promise.resolve();
    }

    try {
      const username = UserService.getUsername() || "";

      const payload: UpdateUserPayload = {
        userId: directRolesResponse.userId,
        payload: {
          addedRoles: [],
          removedRoles: [selectedRole.name],
          actionedBy: username,
        },
      };

      await updateUserRoles(payload).unwrap();
      toast.success(`Role "${selectedRole.displayName}" removed successfully`);
      setShowSingleRoleDeleteConfirm(false);
      setSelectedRole(null);
      return Promise.resolve();
    } catch (error) {
      console.error("Error removing role:", error);
      toast.error("Failed to remove role");
      return Promise.reject(error);
    }
  };

  const handleOpenDeleteConfirm = () => {
    if (!directRolesResponse?.directRoles) return;

    const validRoles = mapPermissionsToValidDisplayPermissions(directRolesResponse.directRoles);

    const rolesToRemove = directRolesResponse.directRoles.filter((role) => {
      return validRoles.some(
        (validRole) => mapRoleNameToDescriptiveName(role.name) === validRole.name,
      );
    });

    if (rolesToRemove.length === 0) {
      toast.info("No direct roles to remove");
      return;
    }

    setShowDeleteConfirm(true);
  };

  const handleOpenSingleRoleDeleteConfirm = (roleName: string, displayName: string) => {
    setSelectedRole({ name: roleName, displayName });
    setShowSingleRoleDeleteConfirm(true);
  };
  return (
    <div>
      {directRolesResponse?.directRoles &&
        mapPermissionsToValidDisplayPermissions(directRolesResponse.directRoles).length > 0 && (
          <section className="mt-4 flex flex-col gap-2 border-b p-4">
            <div className="flex justify-between gap-6">
              <Text variant="subheading">Direct Roles</Text>
              <Button
                variant="destructive"
                onClick={handleOpenDeleteConfirm}
                disabled={isUpdatingRoles}
              >
                {isUpdatingRoles ? (
                  <>
                    <LoadingIcon className="mr-2 h-4 w-4" />
                    Removing...
                  </>
                ) : (
                  "Remove All Direct Roles"
                )}
              </Button>
            </div>
            <Text variant="description">
              Direct roles are roles that are assigned directly to the user. For proper role
              management, prefer assigning roles through groups.
            </Text>
            <div className="mt-1 grid grid-cols-1 gap-2 md:grid-cols-2 md:gap-4 lg:grid-cols-3">
              {mapPermissionsToValidDisplayPermissions(directRolesResponse.directRoles).map(
                (role, index) => {
                  const originalRole = directRolesResponse.directRoles.find(
                    (r) => mapRoleNameToDescriptiveName(r.name) === role.name,
                  );
                  const originalRoleName = originalRole?.name || "";

                  return (
                    <div key={index} className="rounded-md border border-gray-200 bg-gray-50 p-3">
                      <div className="flex items-start justify-between">
                        <div>
                          <Text className="font-medium">{role.name}</Text>
                          {role.description && (
                            <Text className="text-sm text-gray-500">{role.description}</Text>
                          )}
                        </div>
                        <button
                          onClick={() =>
                            handleOpenSingleRoleDeleteConfirm(originalRoleName, role.name)
                          }
                          className="ml-2 rounded-full p-1 text-gray-400 hover:bg-red-100 hover:text-red-500"
                          title="Remove Role"
                        >
                          <XMarkIcon className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                  );
                },
              )}
            </div>
          </section>
        )}
      <DeleteDirectRolesModal
        show={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={handleDeleteAllDirectRoles}
        isLoading={isUpdatingRoles}
      />
      <DeleteSingleRoleModal
        show={showSingleRoleDeleteConfirm}
        onClose={() => setShowSingleRoleDeleteConfirm(false)}
        onConfirm={handleDeleteSingleRole}
        isLoading={isUpdatingRoles}
        roleName={selectedRole?.displayName || ""}
      />
    </div>
  );
}
