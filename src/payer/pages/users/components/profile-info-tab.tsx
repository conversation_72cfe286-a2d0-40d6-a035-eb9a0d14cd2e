import React from "react";
import Text from "../../../components/ui/typography/Text";
import Button from "../../../components/ui/Button";
import { UserRepresentation } from "../../../lib/types/access-control/user";

interface ProfileInfoTabProps {
  user: UserRepresentation;
  onUpdateDetails: () => void;
  onToggleStatus: () => void;
}

export default function ProfileInfoTab({
  user,
  onUpdateDetails,
  onToggleStatus,
}: ProfileInfoTabProps) {
  return (
    <section>
      <section className="flex justify-between gap-6">
        <div className="mb-4">
          <Text variant="subheading">User Information</Text>
          <p className="mt-1 text-sm text-gray-500">View and update user profile information</p>
        </div>
        <div className="flex items-center gap-6">
          <Button onClick={onUpdateDetails} variant="filled">
            Update Details
          </Button>
          <Button onClick={onToggleStatus} variant={user.enabled ? "destructive" : "filled"}>
            {user.enabled ? "Deactivate User" : "Activate User"}
          </Button>
        </div>
      </section>
      <div className="flex flex-col gap-4 rounded-sm border p-4">
        <div>
          <Text variant="label">Username</Text>
          <Text>{user.username}</Text>
        </div>
        <div>
          <Text variant="label">Email</Text>
          <Text>{user.email || "-"}</Text>
        </div>
        <div>
          <Text variant="label">First Name</Text>
          <Text>{user.firstName || "-"}</Text>
        </div>
        <div>
          <Text variant="label">Last Name</Text>
          <Text>{user.lastName || "-"}</Text>
        </div>
        {user.groups && user.groups.length > 0 && (
          <div className="col-span-2">
            <Text variant="label">Groups</Text>
            <Text>{user.groups.join(", ")}</Text>
          </div>
        )}
      </div>
    </section>
  );
}
