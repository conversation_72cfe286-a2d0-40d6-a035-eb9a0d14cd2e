import { Fragment, useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router";
import { toast } from "react-toastify";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import {
  useAddUsersToGroupMutation,
  useAssignCustomRoleToUserMutation,
  useGetCustomRolesQuery,
  useGetDenyPoliciesByPayerIdQuery,
  useGetUserAssignedCustomRolesQuery,
  useGetUserByIdQuery,
  useGetUserDenyPoliciesQuery,
  useGetUserDirectRolesQuery,
  useGetUserEffectiveRolesQuery,
  useGetUserGroupsByUserIdQuery,
  useGetUserGroupsQuery,
  useMakeUserPayerSuperAdminMutation,
  useResetPasswordMutation,
  useUpdateDenyPolicyMutation,
  useUpdateUserDetailsMutation,
  useUpdateUserStatusMutation,
} from "../../../api/features/membershipApi";
import MainWrapper from "../../../components/ui/MainWrapper";
import ProgressModal from "../../../components/ui/modal/ProgressModal";
import TabGroup from "../../../components/ui/tab-group";
import TopBackButton from "../../../components/ui/TopBackButton";
import Text from "../../../components/ui/typography/Text";
import {
  CustomRoleResponse,
  DenyPolicyDto,
  UpdateDenyPolicyRequest,
  UserGroupResponse,
} from "../../../lib/types/access-control/role";
import {
  PayerUserUpdateRequest,
  UserStatusUpdateRequest,
} from "../../../lib/types/access-control/user";
import UserService from "../../../services/UserService";
import UserInfoCustomRolesPanel from "./user-info-custom-roles-panel";
import UserInfoDenyPoliciesPanel from "./user-info-deny-policies-panel";
import UserInfoUserGroupsPanel from "./user-info-user-groups-panel";
import { EllipsisHorizontalIcon } from "@heroicons/react/24/outline";
import { PAYER_SUPER_ADMIN_ASSIGNMENT_ROLE } from "../../../components/access-management/data";
import UserInfoDirectRolesTab from "./user-info-direct-roles-tab";
import { Menu, Transition } from "@headlessui/react";
import UpdateUserDetailsModal from "../update-user-details-modal";
import UpdateUserStatusModal from "../update-user-status-modal";
import MakeUserPayerSuperAdminWarningModal from "./make-user-payer-super-admin-warning-modal";
import ResetUserPasswordModal from "./reset-user-password-modal";
import { mapPermissionsToValidDisplayPermissions } from "../../../utils/user-management-utils";

export default function UserProfileInfo() {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();
  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const [redirectChecked, setRedirectChecked] = useState(false);

  const [selectedGroups, setSelectedGroups] = useState<UserGroupResponse[]>([]);
  const [selectedDenyPolicies, setSelectedDenyPolicies] = useState<DenyPolicyDto[]>([]);
  const [selectedCustomRoles, setSelectedCustomRoles] = useState<CustomRoleResponse[]>([]);

  const [isAddingToGroups, setIsAddingToGroups] = useState(false);
  const [isAddingToDenyPolicies, setIsAddingToDenyPolicies] = useState(false);
  const [isAssigningCustomRoles, setIsAssigningCustomRoles] = useState(false);

  // user details update states
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [showUpdateDetailsModal, setShowUpdateDetailsModal] = useState(false);
  const [showUpdateStatusModal, setShowUpdateStatusModal] = useState(false);
  const [showSuperAdminConfirmModal, setShowSuperAdminConfirmModal] = useState(false);
  const [showResetPasswordModal, setShowResetPasswordModal] = useState(false);

  const { data: userEffectiveRoles, isLoading: isLoadingUserEffectiveRoles } =
    useGetUserEffectiveRolesQuery(userId || "", {
      skip: !userId,
    });

  const effectivePermissionNames =
    userEffectiveRoles?.effectivePermissions?.map((permission) => permission.name) || [];

  const hasPermission = (roles: string[]) => {
    if (!userEffectiveRoles || isLoadingUserEffectiveRoles) {
      return roles.some((role) => UserService.hasRole([role]));
    }

    return roles.some((role) => effectivePermissionNames.includes(role));
  };

  const SELECTED_USER_HAS_PAYER_SUPER_ADMIN_ROLE = hasPermission([
    PAYER_SUPER_ADMIN_ASSIGNMENT_ROLE,
  ]);

  const {
    data: user,
    isLoading: isLoadingUser,
    error: userError,
  } = useGetUserByIdQuery(userId || "");

  useEffect(() => {
    if (!isLoadingUserEffectiveRoles && !isLoadingUser && !redirectChecked) {
      setRedirectChecked(true);
      if (SELECTED_USER_HAS_PAYER_SUPER_ADMIN_ROLE) {
        navigate(`/users/super-admin/profile/${userId}`);
      }
    }
  }, [
    isLoadingUserEffectiveRoles,
    isLoadingUser,
    SELECTED_USER_HAS_PAYER_SUPER_ADMIN_ROLE,
    userId,
    navigate,
    redirectChecked,
  ]);

  const { data: userGroups, isLoading: isLoadingUserGroups } = useGetUserGroupsByUserIdQuery(
    userId || "",
    { skip: !userId },
  );

  const { data: userDenyPolicies, isLoading: isLoadingUserDenyPolicies } =
    useGetUserDenyPoliciesQuery(userId || "", { skip: !userId });

  const { data: assignedCustomRoles, isLoading: isLoadingUserCustomRoles } =
    useGetUserAssignedCustomRolesQuery(userId || "", { skip: !userId });

  const { data: allGroupsData, isLoading: isLoadingAllGroups } = useGetUserGroupsQuery(
    {
      payerId: Number(payerId),
      page: 1,
      size: 100,
    },
    { skip: !payerId },
  );

  const { data: directRolesResponse, isLoading: isLoadingDirectRoles } = useGetUserDirectRolesQuery(
    { userId: userId || "", payerId },
    { skip: !userId },
  );

  const selectedUserUsername = (directRolesResponse && directRolesResponse?.userName) || "";
  const loggedInUserUsername = UserService.getUsername() || "";

  const { data: allDenyPoliciesData, isLoading: isLoadingAllDenyPolicies } =
    useGetDenyPoliciesByPayerIdQuery(
      {
        payerId: Number(payerId),
        page: 1,
        size: 100,
      },
      { skip: !payerId },
    );

  const { data: allCustomRolesData, isLoading: isLoadingAllCustomRoles } = useGetCustomRolesQuery(
    {
      payerId: Number(payerId),
      page: 1,
      size: 100,
    },
    { skip: !payerId },
  );

  const [addUsersToGroup] = useAddUsersToGroupMutation();
  const [updateDenyPolicy] = useUpdateDenyPolicyMutation();
  const [assignCustomRoleToUser] = useAssignCustomRoleToUserMutation();
  const [updateUserDetails, { isLoading: isUpdatingUserDetails }] = useUpdateUserDetailsMutation();
  const [updateUserStatus, { isLoading: isUpdatingStatus }] = useUpdateUserStatusMutation();
  const [makeUserPayerSuperAdmin, { isLoading: isMakingSuperAdmin }] =
    useMakeUserPayerSuperAdminMutation();
  const [requestPasswordReset, { isLoading: isResettingPassword }] = useResetPasswordMutation();

  const availableGroups =
    allGroupsData?.content?.filter(
      (group) => !userGroups?.some((userGroup) => userGroup.id === group.id),
    ) || [];

  const availableDenyPolicies =
    allDenyPoliciesData?.content?.filter(
      (policy) => !userDenyPolicies?.some((userPolicy) => userPolicy.id === policy.id),
    ) || [];

  const availableCustomRoles =
    allCustomRolesData?.content?.filter(
      (role) => !assignedCustomRoles?.some((userRole) => userRole.name === role.name),
    ) || [];

  const handleBackClick = () => {
    navigate(`/users`);
  };

  const toggleGroupSelection = (group: UserGroupResponse) => {
    if (selectedGroups.some((g) => g.id === group.id)) {
      setSelectedGroups(selectedGroups.filter((g) => g.id !== group.id));
    } else {
      setSelectedGroups([...selectedGroups, group]);
    }
  };

  const toggleDenyPolicySelection = (policy: DenyPolicyDto) => {
    if (selectedDenyPolicies.some((p) => p.id === policy.id)) {
      setSelectedDenyPolicies(selectedDenyPolicies.filter((p) => p.id !== policy.id));
    } else {
      setSelectedDenyPolicies([...selectedDenyPolicies, policy]);
    }
  };

  const toggleCustomRoleSelection = (role: CustomRoleResponse) => {
    if (selectedCustomRoles.some((r) => r.id === role.id)) {
      setSelectedCustomRoles(selectedCustomRoles.filter((r) => r.id !== role.id));
    } else {
      setSelectedCustomRoles([...selectedCustomRoles, role]);
    }
  };

  const handleAddToGroups = async () => {
    if (!userId || selectedGroups.length === 0) return;

    try {
      setIsAddingToGroups(true);

      for (const group of selectedGroups) {
        await addUsersToGroup({
          groupId: group.id,
          body: {
            userIds: [userId],
            addedBy: loggedInUserUsername,
          },
        }).unwrap();
      }

      toast.success(`User added to ${selectedGroups.length} group(s) successfully`);
      setSelectedGroups([]);
    } catch (error) {
      console.error("Error adding user to groups:", error);
      toast.error("Failed to add user to groups");
    } finally {
      setIsAddingToGroups(false);
    }
  };

  const handleAddToDenyPolicies = async () => {
    if (!userId || selectedDenyPolicies.length === 0) return;

    try {
      setIsAddingToDenyPolicies(true);

      for (const policy of selectedDenyPolicies) {
        const payload: UpdateDenyPolicyRequest = {
          id: policy.id,
          body: {
            addUsers: [userId],
            updatedBy: loggedInUserUsername,
          },
        };

        await updateDenyPolicy(payload).unwrap();
      }

      toast.success(
        `User added to ${selectedDenyPolicies.length} deny policy/policies successfully`,
      );
      setSelectedDenyPolicies([]);
    } catch (error) {
      console.error("Error adding user to deny policies:", error);
      toast.error("Failed to add user to deny policies");
    } finally {
      setIsAddingToDenyPolicies(false);
    }
  };

  const handleAssignCustomRoles = async () => {
    if (!userId || selectedCustomRoles.length === 0) return;

    try {
      setIsAssigningCustomRoles(true);

      for (const role of selectedCustomRoles) {
        await assignCustomRoleToUser({
          userId,
          roleId: role.id,
          assignedBy: loggedInUserUsername,
        }).unwrap();
      }

      toast.success(`${selectedCustomRoles.length} custom role(s) assigned successfully`);
      setSelectedCustomRoles([]);
    } catch (error) {
      console.error("Error assigning custom roles:", error);
      toast.error("Failed to assign custom roles");
    } finally {
      setIsAssigningCustomRoles(false);
    }
  };

  useEffect(() => {
    if (user) {
      setFirstName(user.firstName || "");
      setLastName(user.lastName || "");
      setEmail(user.email || "");
    }
  }, [user]);

  const handleUpdateUserDetails = () => {
    setShowUpdateDetailsModal(true);
  };

  const handleSubmitUserDetails = async (payload: PayerUserUpdateRequest) => {
    try {
      await updateUserDetails(payload).unwrap();
      setShowUpdateDetailsModal(false);
      toast.success("User details updated successfully");
    } catch (error) {
      console.error("Error updating user details:", error);
      toast.error("Failed to update user details. Please try again.");
    }
  };

  const handleToggleUserStatus = () => {
    setShowUpdateStatusModal(true);
  };

  const handleSubmitUserStatus = async (payload: UserStatusUpdateRequest) => {
    try {
      await updateUserStatus(payload).unwrap();
      setShowUpdateStatusModal(false);
      toast.success("User status updated successfully");
    } catch (error) {
      console.error("Error updating user status:", error);
      toast.error("Failed to update user status. Please try again.");
    }
  };

  const handleShowSuperAdminConfirm = () => {
    setShowSuperAdminConfirmModal(true);
  };

  const handleMakeUserSuperAdmin = async () => {
    if (!userId) return;

    try {
      await makeUserPayerSuperAdmin({
        userId,
        actionedBy: loggedInUserUsername,
      }).unwrap();
      toast.success("User has been made a Super Admin successfully", {
        onClose: () => navigate(`/users/super-admin/profile/${userId}`),
      });
      setShowSuperAdminConfirmModal(false);
    } catch (error) {
      console.error("Error making user super admin:", error);
      toast.error("Failed to make user a Super Admin. Please try again.");
    }
  };

  const handleShowResetPasswordModal = () => {
    setShowResetPasswordModal(true);
  };

  const handleResetPassword = async () => {
    if (!user?.username) return;

    try {
      await requestPasswordReset(user.username).unwrap();
      toast.success(
        `Password reset successfully for ${user.username}. New temporary password: 1234`,
      );
      setShowResetPasswordModal(false);
    } catch (error) {
      console.error("Error resetting password:", error);
      toast.error("Failed to reset password. Please try again.");
    }
  };

  const isLoading =
    isLoadingUser ||
    isLoadingUserGroups ||
    isLoadingUserDenyPolicies ||
    isLoadingUserCustomRoles ||
    isLoadingAllGroups ||
    isLoadingAllDenyPolicies ||
    isLoadingAllCustomRoles ||
    isLoadingUserEffectiveRoles ||
    isLoadingDirectRoles;

  if (isLoading) {
    return (
      <MainWrapper>
        <div className="flex h-full w-full items-center justify-center">
          <LoadingIcon className="h-8 w-8 text-primary" />
        </div>
      </MainWrapper>
    );
  }

  if (userError) {
    return (
      <MainWrapper>
        <div className="flex h-full w-full flex-col items-center justify-center">
          <Text variant="heading" className="text-red-600">
            Error
          </Text>
          <Text>Failed to load user data. Please try again later.</Text>
        </div>
      </MainWrapper>
    );
  }

  if (!user) {
    return (
      <MainWrapper>
        <div className="flex h-full w-full flex-col items-center justify-center">
          <Text variant="heading">No User Found</Text>
          <Text>The requested user could not be found.</Text>
        </div>
      </MainWrapper>
    );
  }

  return (
    <MainWrapper>
      <div className="mb-6 flex items-center justify-between">
        <div className="flex gap-4">
          <TopBackButton onClick={handleBackClick} className="mt-1" />
          <div>
            <Text variant="heading">User Profile</Text>
            <Text className="text-gray-500">Edit user details and manage user roles</Text>
          </div>
        </div>
      </div>

      {/* User Information Card */}
      <section className="mb-6 rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
        <div className="flex items-center justify-between">
          <Text variant="subheading" className="mb-2">
            User Information
          </Text>
          <Menu as="div" className="relative">
            <Menu.Button className="rounded-full p-1 hover:bg-gray-100">
              <EllipsisHorizontalIcon className="h-5 w-5" />
            </Menu.Button>
            <Transition
              as={Fragment}
              enter="transition ease-out duration-100"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-75"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <Menu.Items className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                <div className="py-1">
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        onClick={handleUpdateUserDetails}
                        className={`${active ? "bg-gray-100" : ""} flex w-full items-center px-4 py-2 text-left text-sm text-gray-700`}
                      >
                        Update Details
                      </button>
                    )}
                  </Menu.Item>
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        onClick={handleToggleUserStatus}
                        className={`${active ? "bg-gray-100" : ""} flex w-full items-center px-4 py-2 text-left text-sm ${user?.enabled ? "text-red-600" : "text-green-600"}`}
                      >
                        {user?.enabled ? "Deactivate User" : "Activate User"}
                      </button>
                    )}
                  </Menu.Item>
                  {!SELECTED_USER_HAS_PAYER_SUPER_ADMIN_ROLE && (
                    <Menu.Item>
                      {({ active }) => (
                        <button
                          onClick={handleShowSuperAdminConfirm}
                          className={`${active ? "bg-gray-100" : ""} flex w-full items-center px-4 py-2 text-left text-sm text-blue-600`}
                        >
                          Make Super Admin
                        </button>
                      )}
                    </Menu.Item>
                  )}
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        onClick={handleShowResetPasswordModal}
                        className={`${active ? "bg-gray-100" : ""} flex w-full items-center px-4 py-2 text-left text-sm text-orange-600`}
                      >
                        Reset Password
                      </button>
                    )}
                  </Menu.Item>
                </div>
              </Menu.Items>
            </Transition>
          </Menu>
        </div>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          <div>
            <Text className="text-sm font-medium text-gray-500">Name</Text>
            <Text>
              {user.firstName} {user.lastName}
            </Text>
          </div>
          <div>
            <Text className="text-sm font-medium text-gray-500">Username</Text>
            <Text>{user.username}</Text>
          </div>
          <div>
            <Text className="text-sm font-medium text-gray-500">Email</Text>
            <Text>{user.email}</Text>
          </div>
        </div>
      </section>

      <TabGroup
        tabs={[
          ...(!SELECTED_USER_HAS_PAYER_SUPER_ADMIN_ROLE &&
          directRolesResponse &&
          mapPermissionsToValidDisplayPermissions(directRolesResponse?.directRoles).length > 0
            ? [
                {
                  title: "Direct Roles",
                  content: <UserInfoDirectRolesTab directRolesResponse={directRolesResponse} />,
                },
              ]
            : []),
          {
            title: "User Groups",
            content: (
              <UserInfoUserGroupsPanel
                availableGroups={availableGroups}
                selectedGroups={selectedGroups}
                toggleGroupSelection={toggleGroupSelection}
                handleAddToGroups={handleAddToGroups}
                isAddingToGroups={isAddingToGroups}
                userId={userId}
                userGroups={userGroups}
                username={selectedUserUsername}
              />
            ),
          },
          {
            title: "Deny Policies",
            content: (
              <UserInfoDenyPoliciesPanel
                availableDenyPolicies={availableDenyPolicies}
                selectedDenyPolicies={selectedDenyPolicies}
                toggleDenyPolicySelection={toggleDenyPolicySelection}
                handleAddToDenyPolicies={handleAddToDenyPolicies}
                isAddingToDenyPolicies={isAddingToDenyPolicies}
                userId={userId}
                username={selectedUserUsername}
                userDenyPolicies={userDenyPolicies}
              />
            ),
          },
          {
            title: "Custom Roles",
            content: (
              <UserInfoCustomRolesPanel
                availableCustomRoles={availableCustomRoles}
                selectedCustomRoles={selectedCustomRoles}
                toggleCustomRoleSelection={toggleCustomRoleSelection}
                handleAssignCustomRoles={handleAssignCustomRoles}
                isAssigningCustomRoles={isAssigningCustomRoles}
                assignedCustomRoles={assignedCustomRoles}
                userId={userId}
                username={directRolesResponse?.userName || ""}
              />
            ),
          },
        ]}
      />

      {/* Progress Modals */}
      <ProgressModal
        isProgressModalOpen={isAddingToGroups}
        onClose={() => null}
        title="Adding User to Groups"
        description="Please wait while we add the user to the selected groups..."
      />
      <ProgressModal
        isProgressModalOpen={isAddingToDenyPolicies}
        onClose={() => null}
        title="Adding User to Deny Policies"
        description="Please wait while we add the user to the selected deny policies..."
      />
      <ProgressModal
        isProgressModalOpen={isAssigningCustomRoles}
        onClose={() => null}
        title="Assigning Custom Roles"
        description="Please wait while we assign the selected custom roles to the user..."
      />

      {/* User Action Modals */}
      {user && (
        <>
          <UpdateUserDetailsModal
            show={showUpdateDetailsModal}
            onClose={() => setShowUpdateDetailsModal(false)}
            userId={user.id}
            initialFirstName={firstName}
            initialLastName={lastName}
            initialEmail={email}
            actionedBy={loggedInUserUsername}
            onSubmit={handleSubmitUserDetails}
            isLoading={isUpdatingUserDetails}
          />
          <UpdateUserStatusModal
            show={showUpdateStatusModal}
            onClose={() => setShowUpdateStatusModal(false)}
            userId={user.id}
            currentStatus={user.enabled}
            actionedBy={loggedInUserUsername}
            onSubmit={handleSubmitUserStatus}
            isLoading={isUpdatingStatus}
          />
          <MakeUserPayerSuperAdminWarningModal
            show={showSuperAdminConfirmModal}
            onClose={() => setShowSuperAdminConfirmModal(false)}
            onConfirm={handleMakeUserSuperAdmin}
            isLoading={isMakingSuperAdmin}
          />
          <ResetUserPasswordModal
            show={showResetPasswordModal}
            onClose={() => setShowResetPasswordModal(false)}
            onConfirm={handleResetPassword}
            isLoading={isResettingPassword}
            userName={user.username}
          />
        </>
      )}
    </MainWrapper>
  );
}
