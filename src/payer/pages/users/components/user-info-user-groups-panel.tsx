import { CheckIcon } from "@heroicons/react/24/solid";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import Button from "../../../components/ui/Button";
import Text from "../../../components/ui/typography/Text";
import {
  RemoveUsersFromGroupRequest,
  UserGroupResponse,
} from "../../../lib/types/access-control/role";
import { mapRoleNameToDescriptiveName } from "../../../utils/user-management-utils";
import ProgressModal from "../../../components/ui/modal/ProgressModal";
import RemoveUserFromGroupModal from "./remove-user-from-group-modal";
import UserService from "../../../services/UserService";
import { toast } from "react-toastify";
import { useRemoveUsersFromGroupMutation } from "../../../api/features/membershipApi";
import { useState } from "react";

interface UserGroupsPanelProps {
  availableGroups: UserGroupResponse[];
  selectedGroups: UserGroupResponse[];
  toggleGroupSelection: (group: UserGroupResponse) => void;
  handleAddToGroups: () => void;
  isAddingToGroups: boolean;
  userId: string | undefined;
  userGroups: UserGroupResponse[] | undefined;
  username: string;
}

export default function UserInfoUserGroupsPanel({
  availableGroups,
  selectedGroups,
  toggleGroupSelection,
  handleAddToGroups,
  isAddingToGroups,
  userId,
  userGroups,
  username,
}: UserGroupsPanelProps) {
  const [showRemoveFromGroupProgress, setShowRemoveFromGroupProgress] = useState(false);
  const [showRemoveFromGroupConfirm, setShowRemoveFromGroupConfirm] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<UserGroupResponse | null>(null);

  const [removeUsersFromGroup, { isLoading: isRemovingFromGroup }] =
    useRemoveUsersFromGroupMutation();
  const handleOpenRemoveFromGroupConfirm = (group: UserGroupResponse) => {
    setSelectedGroup(group);
    setShowRemoveFromGroupConfirm(true);
  };

  const handleRemoveFromGroup = async (): Promise<void> => {
    if (!userId || !selectedGroup) {
      return Promise.resolve();
    }

    try {
      setShowRemoveFromGroupProgress(true);

      const username = UserService.getUsername() || "";

      const payload: RemoveUsersFromGroupRequest = {
        groupId: selectedGroup.id,
        body: {
          userIds: [userId],
          actionedBy: username,
        },
      };

      await removeUsersFromGroup(payload).unwrap();
      toast.success(`User removed from group "${selectedGroup.name}" successfully`);
      setShowRemoveFromGroupConfirm(false);
      setSelectedGroup(null);
    } catch (error) {
      console.error("Error removing user from group:", error);
      toast.error("Failed to remove user from group");
    } finally {
      setShowRemoveFromGroupProgress(false);
    }
  };

  return (
    <>
      <div className="mb-6 flex flex-col gap-2 border-b py-4">
        <Text variant="subheading">Assigned User Groups</Text>
        {userGroups && userGroups.length > 0 ? (
          <div className="mt-1 grid grid-cols-1 gap-3 md:grid-cols-2">
            {userGroups.map((group) => (
              <div
                key={group.id}
                className="flex flex-col gap-3 rounded-md border border-gray-200 p-4"
              >
                <div className="flex items-start">
                  <div>
                    <Text className="font-medium">{group.name}</Text>
                    <Text className="text-sm text-gray-500">{group.description}</Text>
                  </div>
                </div>

                {group.customRoles && group.customRoles.length > 0 && (
                  <div className="mt-2">
                    <Text className="text-xs font-medium text-gray-500">Custom Roles:</Text>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {group.customRoles.map((role) => (
                        <span
                          key={role.id}
                          className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800"
                        >
                          {role.name}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {group.predefinedRoles && group.predefinedRoles.length > 0 && (
                  <div className="mt-2">
                    <Text className="text-xs font-medium text-gray-500">Predefined Roles:</Text>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {group.predefinedRoles.map((role, index) => (
                        <span
                          key={index}
                          className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-800"
                        >
                          {mapRoleNameToDescriptiveName(role)}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                <div className="self-end pt-4">
                  <Button
                    onClick={() => handleOpenRemoveFromGroupConfirm(group)}
                    className="text-xs"
                    title="Remove from Group"
                  >
                    Remove
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <Text className="text-gray-500">User is not a member of any groups.</Text>
        )}
      </div>

      <div className="mb-4">
        <Text variant="subheading">Available Groups</Text>
        <Text className="text-sm text-gray-500">Select groups to add this user to</Text>
      </div>

      {availableGroups.length === 0 ? (
        <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 text-center">
          <Text>No available groups found or user is already a member of all groups.</Text>
        </div>
      ) : (
        <>
          <div className="mb-4 grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3">
            {availableGroups.map((group) => (
              <div
                key={group.id}
                className={`cursor-pointer rounded-lg border p-3 transition-colors
                  ${
                    selectedGroups.some((g) => g.id === group.id)
                      ? "border-primary bg-primary/10"
                      : "border-gray-200 bg-white hover:bg-gray-50"
                  }`}
                onClick={() => toggleGroupSelection(group)}
              >
                <div className="flex items-start justify-between">
                  <div>
                    <Text className="font-medium">{group.name}</Text>
                    <Text className="text-sm text-gray-500">{group.description}</Text>
                  </div>
                  {selectedGroups.some((g) => g.id === group.id) && (
                    <CheckIcon className="h-5 w-5 text-primary" />
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className="flex justify-end">
            <Button
              variant="filled"
              onClick={handleAddToGroups}
              disabled={selectedGroups.length === 0 || isAddingToGroups}
              className="flex items-center gap-2"
            >
              {isAddingToGroups ? (
                <>
                  <LoadingIcon className="h-4 w-4" />
                  Adding...
                </>
              ) : (
                `Add to ${selectedGroups.length} Group${selectedGroups.length !== 1 ? "s" : ""}`
              )}
            </Button>
          </div>
        </>
      )}

      <RemoveUserFromGroupModal
        show={showRemoveFromGroupConfirm}
        onClose={() => setShowRemoveFromGroupConfirm(false)}
        onConfirm={handleRemoveFromGroup}
        isLoading={isRemovingFromGroup}
        groupName={selectedGroup?.name || ""}
        userName={username}
      />

      <ProgressModal
        isProgressModalOpen={showRemoveFromGroupProgress}
        onClose={() => null}
        title="Removing User from Group"
        description="Please wait while we remove the user from the group..."
      />
    </>
  );
}
