import { Dialog } from "@headlessui/react";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import Button from "../../../components/ui/Button";
import DialogWrapper from "../../../components/ui/modal/DialogWrapper";

interface RemoveUserFromDenyPolicyModalProps {
  show: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  isLoading: boolean;
  policyName: string;
  userName?: string | undefined;
}

export default function RemoveUserFromDenyPolicyModal({
  show,
  onClose,
  onConfirm,
  isLoading,
  policyName,
  userName,
}: RemoveUserFromDenyPolicyModalProps) {
  return (
    <DialogWrapper show={show} onClose={onClose} maxWidth="max-w-xl">
      <div>
        <div className="border-b border-gray-200 px-6 py-4">
          <Dialog.Title as="h3" className="text-lg font-medium text-gray-900">
            Remove User from Deny Policy
          </Dialog.Title>
        </div>
        <div className="p-6">
          <div className="mb-4">
            <p className="text-sm text-gray-500">
              Are you sure you want to remove{" "}
              {userName ? <span className="font-medium text-black">{userName}</span> : "this user"}{" "}
              from the deny policy <span className="font-medium text-black">{policyName}</span>?
              This action cannot be undone. The user will regain access to any permissions that were
              restricted by this policy.
            </p>
          </div>
          <div className="mt-6 flex justify-end space-x-3">
            <Button variant="outlined" onClick={onClose}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={onConfirm}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              {isLoading ? (
                <>
                  <LoadingIcon className="h-4 w-4" />
                  Removing...
                </>
              ) : (
                "Remove from Policy"
              )}
            </Button>
          </div>
        </div>
      </div>
    </DialogWrapper>
  );
}
