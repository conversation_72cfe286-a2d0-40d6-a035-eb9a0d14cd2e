import { useEffect, useMemo, useState } from "react";
import { useGetAllPermissionsQuery } from "../../api/features/membershipApi";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import Text from "../../components/ui/typography/Text";
import Button from "../../components/ui/Button";
import SearchInput from "../../components/ui/input/SearchInput";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { toast } from "react-toastify";
import {
  CustomRolePermission,
  mapCompositeRoleToConstituentRoles,
  mapPermissionsToCustomRolePermissions,
} from "../../utils/user-management-utils";
import { CompositeRole, FULL_ACCESS_ROLES } from "../../components/access-management/data";
import { XMarkIcon } from "@heroicons/react/24/outline";

interface AddRolePermissionsModalProps {
  show: boolean;
  onClose: () => void;
  selectedPermissionNames: Set<string>;
  onAddPermissions: (permissions: string[]) => void;
}

export default function AddCustomRolePermissionsModal({
  show,
  onClose,
  selectedPermissionNames,
  onAddPermissions,
}: AddRolePermissionsModalProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [localSelectedPermissionNames, setLocalSelectedPermissionNames] = useState<Set<string>>(
    new Set(),
  );

  const [mappedPermissions, setMappedPermissions] = useState<CustomRolePermission[]>([]);

  const { data: permissionsData, isLoading } = useGetAllPermissionsQuery();

  useEffect(() => {
    if (show) {
      setLocalSelectedPermissionNames(new Set());
      setSearchTerm("");
    } else {
      setLocalSelectedPermissionNames(new Set());
      setSearchTerm("");
    }
  }, [show]);

  useEffect(() => {
    if (permissionsData) {
      const mapped = mapPermissionsToCustomRolePermissions(permissionsData);
      setMappedPermissions(mapped);
    }
  }, [permissionsData]);

  const isCompositeRole = (roleName: string): roleName is CompositeRole => {
    return FULL_ACCESS_ROLES.includes(roleName as CompositeRole);
  };

  const handleTogglePermission = (permission: CustomRolePermission) => {
    setLocalSelectedPermissionNames((prev) => {
      const newNames = new Set(prev);
      if (isCompositeRole(permission.roleName)) {
        if (newNames.has(permission.roleName)) {
          newNames.delete(permission.roleName);
          const constituentRoles = mapCompositeRoleToConstituentRoles(permission.roleName);
          constituentRoles.forEach((role) => newNames.delete(role));
        } else {
          const constituentRoles = mapCompositeRoleToConstituentRoles(permission.roleName);
          constituentRoles.forEach((role) => newNames.add(role));
          newNames.add(permission.roleName);
        }
      } else {
        if (newNames.has(permission.roleName)) {
          newNames.delete(permission.roleName);
        } else {
          newNames.add(permission.roleName);
        }
      }

      return newNames;
    });
  };

  const handleAddPermissions = () => {
    const selectedPermissions = Array.from(localSelectedPermissionNames).filter(
      (roleName) => !isCompositeRole(roleName) && !selectedPermissionNames.has(roleName),
    );

    if (selectedPermissions.length === 0) {
      toast.error(
        "No new permissions selected or all selected permissions are already in the role",
      );
      return;
    }

    onAddPermissions(selectedPermissions);
    onClose();
  };

  const handleCancel = () => {
    setLocalSelectedPermissionNames(new Set());
    setSearchTerm("");
  };

  const handleClose = () => {
    setLocalSelectedPermissionNames(new Set());
    setSearchTerm("");
    onClose();
  };

  const availablePermissions = useMemo(() => {
    return mappedPermissions.filter(
      (permission) => !selectedPermissionNames.has(permission.roleName),
    );
  }, [mappedPermissions, selectedPermissionNames]);

  const filteredPermissions = useMemo(() => {
    return availablePermissions.filter(
      (permission) =>
        permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (permission.description &&
          permission.description.toLowerCase().includes(searchTerm.toLowerCase())),
    );
  }, [availablePermissions, searchTerm]);

  const newlySelectedCount = localSelectedPermissionNames.size;

  return (
    <DialogWrapper show={show} onClose={handleClose} maxWidth="max-w-5xl" className="h-[660px]">
      <div>
        <div className="relative border-b border-gray-200 px-6 py-4">
          <Text variant="subheading">Add Permissions to Role</Text>
          <button
            onClick={handleClose}
            className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
            aria-label="Close modal"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
        <div className="p-6">
          <div className="mb-4 flex justify-between">
            <Text variant="description">
              Select permissions to add to this role. Users assigned this role will have these
              permissions.
            </Text>
            <SearchInput
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search permissions..."
              className="w-64"
            />
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <LoadingIcon className="h-6 w-6 text-blue-400" />
              <span className="ml-2 text-gray-600">Loading permissions...</span>
            </div>
          ) : (
            <>
              <div className="mb-4 max-h-96 overflow-y-auto rounded-md border border-gray-200 p-4">
                <div className="space-y-4">
                  {filteredPermissions.length > 0 ? (
                    filteredPermissions.map((permission) => (
                      <div
                        key={permission.roleName}
                        className="rounded-md border border-gray-200 p-3 hover:bg-gray-50"
                      >
                        <div className="flex items-start">
                          <div className="mr-2 mt-0.5">
                            <input
                              type="checkbox"
                              id={`permission-${permission.roleName}`}
                              checked={
                                isCompositeRole(permission.roleName)
                                  ? Array.from(
                                      mapCompositeRoleToConstituentRoles(permission.roleName),
                                    ).every((role) => localSelectedPermissionNames.has(role))
                                  : localSelectedPermissionNames.has(permission.roleName)
                              }
                              onChange={() => handleTogglePermission(permission)}
                              className="h-4 w-4 rounded border-gray-300 text-blue-600"
                            />
                          </div>
                          <div>
                            <label
                              htmlFor={`permission-${permission.roleName}`}
                              className="block font-medium text-gray-700"
                            >
                              {permission.name}
                            </label>
                            {permission.description && (
                              <p className="mt-1 text-sm text-gray-500">{permission.description}</p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="py-8 text-center text-gray-500">No permissions found</div>
                  )}
                </div>
              </div>

              <div className="mb-4">
                <Text variant="description">{newlySelectedCount} permission(s) selected</Text>
              </div>
            </>
          )}

          <div className="mt-6 flex justify-end space-x-3">
            <Button variant="outlined" onClick={handleCancel} disabled={newlySelectedCount === 0}>
              Clear Selection
            </Button>
            <Button
              type="button"
              variant="filled"
              onClick={handleAddPermissions}
              disabled={newlySelectedCount === 0}
            >
              Add Permissions
            </Button>
          </div>
        </div>
      </div>
    </DialogWrapper>
  );
}
