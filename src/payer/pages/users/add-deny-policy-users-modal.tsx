import { XMarkIcon } from "@heroicons/react/24/outline";
import { useEffect, useState } from "react";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { useGetPayerUsersQuery } from "../../api/features/membershipApi";
import Button from "../../components/ui/Button";
import SearchInput from "../../components/ui/input/SearchInput";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import SecondaryPagination from "../../components/ui/pagination/SecondaryPagination";
import Text from "../../components/ui/typography/Text";
import { UserRepresentation } from "../../lib/types/access-control/user";

interface AddDenyPolicyUsersModalProps {
  show: boolean;
  onClose: () => void;
  selectedUserIds: Set<string>;
  onAddUsers: (users: UserRepresentation[]) => void;
  payerId: string;
  existingUserIds?: Set<string>;
}

export default function AddDenyPolicyUsersModal({
  show,
  onClose,
  selectedUserIds,
  onAddUsers,
  payerId,
  existingUserIds = new Set(),
}: AddDenyPolicyUsersModalProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [localSelectedUserIds, setLocalSelectedUserIds] = useState<Set<string>>(
    new Set(selectedUserIds),
  );
  const [localSelectedUsers, setLocalSelectedUsers] = useState<UserRepresentation[]>([]);
  const pageSize = 10;

  const { data: userData, isLoading } = useGetPayerUsersQuery({
    payerId,
    page: currentPage,
    size: pageSize,
    search: searchTerm,
  });

  useEffect(() => {
    setLocalSelectedUserIds(new Set(selectedUserIds));
    if (selectedUserIds.size === 0) {
      setLocalSelectedUsers([]);
    }
  }, [selectedUserIds]);

  useEffect(() => {
    if (show) {
      setLocalSelectedUserIds(new Set(selectedUserIds));
      setLocalSelectedUsers([]);
    }
  }, [show, selectedUserIds]);

  const handleToggleUser = (user: UserRepresentation) => {
    setLocalSelectedUserIds((prev) => {
      const newIds = new Set(prev);
      if (newIds.has(user.id)) {
        newIds.delete(user.id);
        setLocalSelectedUsers(localSelectedUsers.filter((u) => u.id !== user.id));
      } else {
        newIds.add(user.id);
        setLocalSelectedUsers([...localSelectedUsers, user]);
      }
      return newIds;
    });
  };

  const handleAddUsers = () => {
    onAddUsers(localSelectedUsers);
    onClose();
  };

  const handleCancel = () => {
    setLocalSelectedUserIds(new Set());
    setLocalSelectedUsers([]);
  };

  const handleClose = () => {
    setLocalSelectedUserIds(new Set());
    setLocalSelectedUsers([]);
    onClose();
  };

  const totalPages = userData?.totalPages || 1;
  const totalElements = userData?.totalElements || 0;

  const isAllNonExistingUsersSelected =
    userData &&
    userData?.content?.filter((user) => !existingUserIds.has(user.id)).length > 0 &&
    userData?.content
      ?.filter((user) => !existingUserIds.has(user.id))
      .every((user) => localSelectedUserIds.has(user.id));

  const handleSelectAllToggle = () => {
    if (isAllNonExistingUsersSelected) {
      const newIds = new Set(localSelectedUserIds);
      userData?.content
        ?.filter((user) => !existingUserIds.has(user.id))
        .forEach((user) => {
          newIds.delete(user.id);
        });
      setLocalSelectedUserIds(newIds);
      setLocalSelectedUsers(
        localSelectedUsers.filter(
          (u) =>
            !userData?.content
              ?.filter((user) => !existingUserIds.has(user.id))
              .some((user) => user.id === u.id),
        ),
      );
    } else {
      const newIds = new Set(localSelectedUserIds);
      const newUsers = [...localSelectedUsers];
      userData?.content
        ?.filter((user) => !existingUserIds.has(user.id))
        .forEach((user) => {
          if (!newIds.has(user.id)) {
            newIds.add(user.id);
            newUsers.push(user);
          }
        });
      setLocalSelectedUserIds(newIds);
      setLocalSelectedUsers(newUsers);
    }
  };

  const getUserCheckboxProps = (user: UserRepresentation) => {
    const isExistingUser = existingUserIds.has(user.id);
    return {
      checked: localSelectedUserIds.has(user.id) || isExistingUser,
      onChange: () => !isExistingUser && handleToggleUser(user),
      disabled: isExistingUser,
      className: `h-4 w-4 rounded border-gray-300 text-blue-600 ${isExistingUser ? "cursor-not-allowed opacity-60" : ""}`,
    };
  };

  return (
    <DialogWrapper show={show} onClose={handleClose} maxWidth="max-w-5xl" className="h-[660px]">
      <div className="relative border-b border-gray-200 px-6 py-4">
        <Text variant="subheading">Add Users to Deny Policy</Text>
        <button
          onClick={handleClose}
          className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
          aria-label="Close modal"
        >
          <XMarkIcon className="h-5 w-5" />
        </button>
      </div>
      <div className="flex flex-col p-6">
        <div className="mb-4 flex justify-between">
          <Text variant="description">Select users to add to this deny policy.</Text>
          <SearchInput
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setCurrentPage(1);
            }}
            placeholder="Search users..."
            className="w-64"
          />
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <LoadingIcon className="h-6 w-6 text-blue-400" />
            <span className="ml-2 text-gray-600">Loading users...</span>
          </div>
        ) : (
          <>
            <div className="mb-4 max-h-96 overflow-y-auto rounded-md border border-gray-200">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200 bg-gray-50">
                    <th className="px-4 py-2 text-left text-xs font-medium uppercase text-gray-500">
                      <input
                        type="checkbox"
                        className="h-4 w-4 rounded border-gray-300 text-blue-600"
                        checked={isAllNonExistingUsersSelected}
                        onChange={handleSelectAllToggle}
                      />
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium uppercase text-gray-500">
                      Name
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium uppercase text-gray-500">
                      Username
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium uppercase text-gray-500">
                      Email
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {userData?.content?.map((user) => {
                    const isExistingUser = existingUserIds.has(user.id);
                    return (
                      <tr
                        key={user.id}
                        className={`border-b border-gray-200 hover:bg-gray-50 ${isExistingUser ? "bg-gray-50" : ""}`}
                      >
                        <td className="px-4 py-2">
                          <input type="checkbox" {...getUserCheckboxProps(user)} />
                        </td>
                        <td className="px-4 py-2 text-xs">
                          <div className="flex items-center">
                            {user.firstName && user.lastName
                              ? `${user.firstName} ${user.lastName}`
                              : "-"}
                            {isExistingUser && (
                              <span className="ml-2 rounded-full bg-blue-100 px-2 py-0.5 text-xs text-blue-800">
                                Already in policy
                              </span>
                            )}
                          </div>
                        </td>
                        <td className="px-4 py-2 text-xs">{user.username}</td>
                        <td className="px-4 py-2 text-xs">{user.email || "-"}</td>
                      </tr>
                    );
                  })}
                  {userData?.content?.length === 0 && (
                    <tr>
                      <td colSpan={4} className="px-4 py-8 text-center text-gray-500">
                        No users found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            <div className="mb-4 flex justify-between">
              <div>
                <Text variant="description">{localSelectedUserIds.size} user(s) selected</Text>
              </div>
              <SecondaryPagination
                currentPage={currentPage}
                totalPages={totalPages || 1}
                size={pageSize}
                totalElements={totalElements || 0}
                setCurrentPage={setCurrentPage}
              />
            </div>
          </>
        )}

        <div className="mt-6 flex justify-end space-x-3 self-end">
          <Button
            variant="outlined"
            onClick={handleCancel}
            disabled={localSelectedUserIds.size === 0}
          >
            Clear Selection
          </Button>
          <Button
            variant="filled"
            onClick={handleAddUsers}
            disabled={localSelectedUserIds.size === 0}
          >
            Add Users
          </Button>
        </div>
      </div>
    </DialogWrapper>
  );
}
