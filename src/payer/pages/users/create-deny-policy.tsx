import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { XMarkIcon } from "@heroicons/react/24/solid";
import { useNavigate } from "react-router-dom";
import { useCreateDenyPolicyMutation } from "../../api/features/membershipApi";
import Button from "../../components/ui/Button";
import MainWrapper from "../../components/ui/MainWrapper";
import Text from "../../components/ui/typography/Text";
import UserService from "../../services/UserService";
import { DenyPolicyRequest, PermissionResponse } from "../../lib/types/access-control/role";
import { UserRepresentation } from "../../lib/types/access-control/user";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { PlusIcon } from "@heroicons/react/24/outline";
import AddDenyPolicyUsersModal from "./add-deny-policy-users-modal";
import AddDenyPolicyPermissionsModal from "./add-deny-policy-permissions-modal";
import TopBackButton from "../../components/ui/TopBackButton";
import RequiredTag from "../../components/ui/input/required-tag";
import { mapRoleNameToDescriptiveName } from "../../utils/user-management-utils";
import { toast } from "react-toastify";
import BackButtonWarningOnEditModal from "../../components/ui/modal/back-button-warning-on-edit-modal";
import CharacterCount from "../../components/ui/character-count";
import {
  NAME_MAX_LENGTH,
  DESCRIPTION_MAX_LENGTH,
  denyPolicySchema,
  DenyPolicyInput,
} from "../../utils/validation-schemas";

export default function CreateDenyPolicy() {
  const navigate = useNavigate();
  const [selectedUsers, setSelectedUsers] = useState<UserRepresentation[]>([]);
  const [selectedPermissions, setSelectedPermissions] = useState<PermissionResponse[]>([]);
  const [showAddUsersModal, setShowAddUsersModal] = useState(false);
  const [showAddPermissionsModal, setShowAddPermissionsModal] = useState(false);
  const [selectedUserIds, setSelectedUserIds] = useState<Set<string>>(new Set());
  const [selectedPermissionNames, setSelectedPermissionNames] = useState<Set<string>>(new Set());

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isValid },
  } = useForm<DenyPolicyInput>({
    resolver: zodResolver(denyPolicySchema),
    mode: "onChange",
  });

  const watchedName = watch("name") || "";
  const watchedReason = watch("reason") || "";

  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const username = UserService.getUsername();

  const [createDenyPolicy, { isLoading: isCreating, isSuccess, error }] =
    useCreateDenyPolicyMutation();

  useEffect(() => {
    if (isSuccess) {
      navigate("/users/deny-policies");
    }
  }, [isSuccess, navigate]);

  const handleCreateDenyPolicy = (data: DenyPolicyInput) => {
    try {
      if (selectedUsers.length === 0 || selectedPermissions.length === 0) {
        return;
      }

      const userIdsArray = Array.from(selectedUsers.map((user) => user.id));
      const permissionsArray = Array.from(selectedPermissions.map((permission) => permission.name));

      const payload: DenyPolicyRequest = {
        name: data.name,
        payerId,
        userIds: userIdsArray,
        deniedPermissions: permissionsArray,
        reason: data.reason,
        createdBy: username,
      };

      createDenyPolicy(payload).unwrap();
      toast.success("Deny policy created successfully");
    } catch (error: unknown) {
      const errorObj = error as { data?: { error?: string } };
      const errorMessage = errorObj.data?.error || "Failed to create deny policy.";
      toast.error(errorMessage);
    }
  };

  const handleAddUsers = (users: UserRepresentation[]) => {
    setSelectedUsers(users);
    setSelectedUserIds(new Set(users.map((user) => user.id)));
  };

  const handleAddPermissions = (permissions: PermissionResponse[]) => {
    const newPermissions = [...selectedPermissions];
    const newPermissionNames = new Set(selectedPermissionNames);

    permissions.forEach((permission) => {
      if (!newPermissionNames.has(permission.name)) {
        newPermissions.push(permission);
        newPermissionNames.add(permission.name);
      }
    });

    setSelectedPermissions(newPermissions);
    setSelectedPermissionNames(newPermissionNames);
  };

  const removeUser = (user: UserRepresentation) => {
    setSelectedUsers(selectedUsers.filter((u) => u.id !== user.id));
    setSelectedUserIds(new Set([...selectedUserIds].filter((id) => id !== user.id)));
  };

  const clearAllUsers = () => {
    setSelectedUsers([]);
    setSelectedUserIds(new Set());
    if (showAddUsersModal) {
      setShowAddUsersModal(false);
      setTimeout(() => setShowAddUsersModal(true), 50);
    }
  };

  const removePermission = (permission: PermissionResponse) => {
    setSelectedPermissions(selectedPermissions.filter((p) => p.name !== permission.name));
    setSelectedPermissionNames(
      new Set([...selectedPermissionNames].filter((name) => name !== permission.name)),
    );
  };

  const clearAllPermissions = () => {
    setSelectedPermissions([]);
    setSelectedPermissionNames(new Set());
    if (showAddPermissionsModal) {
      setShowAddPermissionsModal(false);
      setTimeout(() => setShowAddPermissionsModal(true), 50);
    }
  };

  const [isBackButtonWarningModalOpen, setIsBackButtonWarningModalOpen] = useState(false);
  const handleBackClick = () => {
    setIsBackButtonWarningModalOpen(true);
  };

  const handleBackButtonConfirm = () => {
    navigate("/users/deny-policies");
  };

  return (
    <MainWrapper className="flex flex-col gap-6">
      <div className="flex items-center gap-4">
        <TopBackButton onClick={handleBackClick} className="mt-1" />
        <Text variant="heading">Create A Deny Policy</Text>
      </div>

      <div className="rounded-md border border-gray-200 bg-white p-6">
        <form onSubmit={handleSubmit(handleCreateDenyPolicy)} className="space-y-6">
          <div className="">
            <div className="flex flex-col gap-2">
              <label className="mb-1 block text-sm font-medium text-gray-700">
                Policy Name <RequiredTag />
              </label>
              <input
                type="text"
                className="w-full rounded-md border border-gray-300 p-2 text-sm"
                placeholder="Enter policy name"
                maxLength={NAME_MAX_LENGTH}
                {...register("name")}
              />
              <div className="mt-1 flex items-center justify-between">
                {errors.name && <p className="text-xs text-red-600">{errors.name.message}</p>}
                <CharacterCount
                  current={watchedName.length}
                  max={NAME_MAX_LENGTH}
                  className="ml-auto"
                />
              </div>
            </div>
          </div>

          <div className="flex flex-col gap-2">
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Reason <RequiredTag />
            </label>
            <textarea
              className="h-20 w-full resize-none rounded-md border border-gray-300 p-2 text-sm"
              placeholder="Enter reason for deny policy"
              maxLength={DESCRIPTION_MAX_LENGTH}
              {...register("reason")}
            />
            <div className="mt-1 flex items-center justify-between">
              {errors.reason && <p className="text-xs text-red-600">{errors.reason.message}</p>}
              <CharacterCount
                current={watchedReason.length}
                max={DESCRIPTION_MAX_LENGTH}
                className="ml-auto"
              />
            </div>
          </div>

          <div className="space-y-6">
            <div className="rounded-md border border-gray-200 p-4">
              <div className="mb-4 flex items-center justify-between">
                <div>
                  <Text variant="subheading">Users</Text>
                  <Text variant="description" className="text-gray-500">
                    Select users who will be affected by this deny policy
                  </Text>
                </div>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outlined"
                    className="flex items-center gap-1"
                    onClick={() => setShowAddUsersModal(true)}
                  >
                    <PlusIcon className="h-4 w-4" /> Add Users
                  </Button>
                  {selectedUsers.length > 0 && (
                    <Button
                      type="button"
                      variant="destructive"
                      className="flex items-center gap-1"
                      onClick={clearAllUsers}
                    >
                      <XMarkIcon className="h-4 w-4" /> Clear All
                    </Button>
                  )}
                </div>
              </div>

              {selectedUsers.length > 0 ? (
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {selectedUsers.map((user) => (
                    <div
                      key={user.id}
                      className="flex items-center justify-between rounded-md bg-gray-50 p-3"
                    >
                      <div>
                        <Text variant="paragraph" className="text-xs font-medium">
                          {user.firstName && user.lastName
                            ? `${user.firstName} ${user.lastName}`
                            : user.username}
                        </Text>
                        {user.email && (
                          <Text variant="description" className="text-xs text-gray-600">
                            {user.email}
                          </Text>
                        )}
                      </div>
                      <button
                        type="button"
                        onClick={() => removeUser(user)}
                        className="ml-1 rounded-full p-0.5 hover:bg-red-200"
                        title="Remove User"
                        aria-label="Remove User"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <Text variant="paragraph" className="text-gray-500">
                  At least one user must be selected
                </Text>
              )}
            </div>

            <div className="rounded-md border border-gray-200 p-4">
              <div className="mb-4 flex items-center justify-between">
                <div>
                  <Text variant="subheading">Permissions</Text>
                  <Text variant="description" className="text-gray-500">
                    Select permissions that will be denied for the selected users
                  </Text>
                </div>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outlined"
                    className="flex items-center gap-1"
                    onClick={() => setShowAddPermissionsModal(true)}
                  >
                    <PlusIcon className="h-4 w-4" /> Add Permissions
                  </Button>
                  {selectedPermissions.length > 0 && (
                    <Button
                      type="button"
                      variant="destructive"
                      className="flex items-center gap-1"
                      onClick={clearAllPermissions}
                    >
                      <XMarkIcon className="h-4 w-4" /> Clear All
                    </Button>
                  )}
                </div>
              </div>

              {selectedPermissions.length > 0 ? (
                <div className="grid grid-cols-4 gap-2">
                  {selectedPermissions.map((permission) => (
                    <div key={permission.name} className="flex items-center gap-1">
                      <p>{mapRoleNameToDescriptiveName(permission.name)}</p>
                      <button
                        type="button"
                        onClick={() => removePermission(permission)}
                        className="ml-1 rounded-full p-0.5 hover:bg-red-200"
                        title="Remove Permission"
                        aria-label="Remove Permission"
                      >
                        <XMarkIcon className="h-3 w-3" />
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <Text variant="paragraph" className="text-gray-500">
                  At least one permission must be selected
                </Text>
              )}
            </div>
          </div>

          {error && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="flex">
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error creating deny policy</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{(error as any)?.data?.error || "An unknown error occurred"}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-2 pt-5">
            <Button
              variant="outlined"
              onClick={() => navigate("/users/deny-policies")}
              type="button"
            >
              Cancel
            </Button>
            <Button
              variant="filled"
              type="submit"
              disabled={
                isCreating ||
                selectedUsers.length === 0 ||
                selectedPermissions.length === 0 ||
                !isValid
              }
              className="flex items-center gap-2"
            >
              {isCreating && <LoadingIcon className="h-4 w-4 text-white" />}
              Create Deny Policy
            </Button>
          </div>
        </form>
      </div>

      <AddDenyPolicyUsersModal
        show={showAddUsersModal}
        onClose={() => setShowAddUsersModal(false)}
        selectedUserIds={selectedUserIds}
        onAddUsers={handleAddUsers}
        payerId={payerId}
        existingUserIds={selectedUserIds}
      />

      <AddDenyPolicyPermissionsModal
        show={showAddPermissionsModal}
        onClose={() => setShowAddPermissionsModal(false)}
        selectedPermissionNames={selectedPermissionNames}
        onAddPermissions={handleAddPermissions}
      />

      <BackButtonWarningOnEditModal
        onClose={() => setIsBackButtonWarningModalOpen(false)}
        isBackButtonWarningModalOpen={isBackButtonWarningModalOpen}
        onConfirm={handleBackButtonConfirm}
      />
    </MainWrapper>
  );
}
