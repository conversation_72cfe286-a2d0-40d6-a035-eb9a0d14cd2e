import { useEffect, useState } from "react";
import { useNavigate } from "react-router";
import { Empty } from "~lib/components";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import {
  useGetPayerUsersQuery,
  useGetUserEffectiveRolesQuery,
} from "../../api/features/membershipApi";
import Badge from "../../components/ui/Badge";
import Button from "../../components/ui/Button";
import MainWrapper from "../../components/ui/MainWrapper";
import PrimaryPagination from "../../components/ui/pagination/PrimaryPagination";
import SearchInput from "../../components/ui/input/SearchInput";
import TableDataItem from "../../components/ui/table/TableDataItem";
import TableHeaderItem from "../../components/ui/table/TableHeaderItem";
import Text from "../../components/ui/typography/Text";
import { GetPayerUsersQuery, UserRepresentation } from "../../lib/types/access-control/user";
import UserService from "../../services/UserService";
import { PlusIcon } from "@heroicons/react/24/solid";
import AddUserModal from "./add-user-modal";
import { EyeIcon } from "@heroicons/react/24/outline";
import { PAYER_SUPER_ADMIN_ASSIGNMENT_ROLE } from "../../components/access-management/data";

function UserTableRow({ user }: { user: UserRepresentation }) {
  const navigate = useNavigate();
  const { data: userEffectiveRoles, isLoading: isLoadingUserEffectiveRoles } =
    useGetUserEffectiveRolesQuery(user.id || "", {
      skip: !user.id,
    });

  const effectivePermissionNames =
    userEffectiveRoles?.effectivePermissions?.map((permission) => permission.name) || [];

  const hasPermission = (roles: string[]) => {
    if (!userEffectiveRoles || isLoadingUserEffectiveRoles) {
      return roles.some((role) => UserService.hasRole([role]));
    }

    return roles.some((role) => effectivePermissionNames.includes(role));
  };

  const SELECTED_USER_HAS_PAYER_SUPER_ADMIN_ROLE = hasPermission([
    PAYER_SUPER_ADMIN_ASSIGNMENT_ROLE,
  ]);

  const handleViewProfile = () =>
    SELECTED_USER_HAS_PAYER_SUPER_ADMIN_ROLE
      ? navigate(`/users/super-admin/profile/${user.id}`)
      : navigate(`/users/profile/${user.id}`);

  return (
    <tr key={user.id}>
      <TableDataItem>
        {user.firstName && user.lastName ? (
          `${user.firstName} ${user.lastName}`
        ) : (
          <div className="text-center">-</div>
        )}
      </TableDataItem>
      <TableDataItem>{user.username}</TableDataItem>
      <TableDataItem>{user.email || <div className="text-center">-</div>}</TableDataItem>
      <TableDataItem>
        {user.enabled ? (
          <Badge color="green" text="Enabled" hasDot />
        ) : (
          <Badge color="yellow" text="Disabled" hasDot />
        )}
      </TableDataItem>
      <TableDataItem className="">
        <button onClick={handleViewProfile} className="">
          <EyeIcon className="h-6 w-6" />
        </button>
      </TableDataItem>
    </tr>
  );
}

export default function UserManagementTable() {
  const [users, setUsers] = useState<UserRepresentation[]>([]);
  const payerId: string = UserService.getPayer()?.tokenParsed?.["payerId"];
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [showAddUserModal, setShowAddUserModal] = useState(false);

  const payerUsersParams: GetPayerUsersQuery = {
    payerId,
    page,
    size,
    search: searchTerm,
  };

  const { data, isLoading, isFetching, error } = useGetPayerUsersQuery(payerUsersParams);
  const totalElements = data?.totalElements as number;
  const totalPages = data?.totalPages as number;

  const handlePageNumberClick = (page: number) => {
    setPage(page);
  };

  const handleSizeChange = (size: number) => {
    setSize(size);
    setPage(1);
  };

  useEffect(() => {
    if (data?.content) {
      setUsers(data?.content);
    }
  }, [data]);

  const shouldDisplayTable = !isLoading && !isFetching && !error && users.length > 0;
  const shouldDisplayPagination = !isLoading && !isFetching && !error && totalElements > 0;

  const username = UserService.getUsername()?.toString();

  return (
    <MainWrapper className="flex flex-col gap-6">
      <section className="flex justify-between gap-6">
        <div className="flex flex-col gap-3">
          <Text variant="heading">User Management</Text>
          <Text
            variant="description"
            className="italic"
          >{`Click the eye icon to access the user profile and manage roles.`}</Text>
        </div>
        <div className="flex items-center gap-6">
          <SearchInput
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search by username, email or full name"
            title="Search by username, email or full name"
            className="h-fit w-[37ch]"
          />
          <Button className="whitespace-nowrap" onClick={() => setShowAddUserModal(true)}>
            <PlusIcon className="h-5 w-5" /> Add User
          </Button>
        </div>
      </section>
      {shouldDisplayTable && (
        <table>
          <thead>
            <tr className="bg-gray-50">
              <TableHeaderItem item="name" />
              <TableHeaderItem item="username" />
              <TableHeaderItem item="email address" />
              <TableHeaderItem item="status" />
              <TableHeaderItem item="action" />
            </tr>
          </thead>
          <tbody>
            {users
              .filter((user) => user.username !== username)
              .map((user) => (
                <UserTableRow key={user.id} user={user} />
              ))}
          </tbody>
        </table>
      )}
      {shouldDisplayPagination && (
        <div className="self-start py-4">
          <PrimaryPagination
            pageSize={size}
            totalElements={totalElements ?? 0}
            totalPages={totalPages ?? 1}
            pageNumber={page}
            onPageNumberClick={handlePageNumberClick}
            onSizeChange={handleSizeChange}
          />
        </div>
      )}
      {isLoading || isFetching ? (
        <div className="flex items-center justify-center space-x-2 self-center py-44">
          <LoadingIcon className="h-6 w-6 text-blue-400" />
          <p className="text-blue-700">Loading Users...</p>
        </div>
      ) : error ? (
        <div className="flex items-center justify-center self-center py-44">
          <p className="text-red-700">Error loading users. Try again later.</p>
        </div>
      ) : (
        !shouldDisplayTable &&
        totalElements === 0 && (
          <div className="self-center py-44">
            <Empty message="No users found!" />
          </div>
        )
      )}
      <AddUserModal
        show={showAddUserModal}
        onClose={() => setShowAddUserModal(false)}
        payerId={payerId}
        onSuccess={() => {
          setShowAddUserModal(false);
          setPage(1);
        }}
      />
    </MainWrapper>
  );
}
