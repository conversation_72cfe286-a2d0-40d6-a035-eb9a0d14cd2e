import { useEffect, useState } from "react";
import { useNavi<PERSON>, useParams } from "react-router";
import { toast } from "react-toastify";
import { Empty } from "~lib/components";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import {
  useDeleteUserGroupMutation,
  useGetUserGroupByIdQuery,
  useGetUserGroupMembersQuery,
  useRemoveUsersFromGroupMutation,
  useUpdateGroupRolesMutation,
  useUpdateUserGroupMutation,
} from "../../api/features/membershipApi";
import MainWrapper from "../../components/ui/MainWrapper";
import ProgressModal from "../../components/ui/modal/ProgressModal";
import TopBackButton from "../../components/ui/TopBackButton";
import TabGroup, { TabItem } from "../../components/ui/tab-group";
import DeleteGroupConfirmModal from "../../components/user-group/delete-group-confirm-modal";
import GroupDetailsHeader from "../../components/user-group/group-details-header";
import GroupDetailsInfo from "../../components/user-group/group-details-info";
import GroupMembersList from "../../components/user-group/group-members-list";
import GroupRolesList from "../../components/user-group/group-roles-list";
import {
  CustomRoleResponse,
  RoleManagementDeletionResponse,
  UpdateUserGroupRequest,
  UserGroupResponse,
} from "../../lib/types/access-control/role";
import { UserRepresentation } from "../../lib/types/access-control/user";
import UserService from "../../services/UserService";
import AddGroupRolesModal from "../users/add-group-roles-modal";
import AddGroupUsersModal from "../users/add-group-users-modal";

export default function GroupDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [group, setGroup] = useState<UserGroupResponse | null>(null);
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [selectedUserIds, _setSelectedUserIds] = useState<Set<string>>(new Set());
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(5);
  const [showAddRolesModal, setShowAddRolesModal] = useState(false);
  const [selectedPredefinedRoles, setSelectedPredefinedRoles] = useState<Set<string>>(new Set());
  const [selectedCustomRoleIds, setSelectedCustomRoleIds] = useState<Set<number>>(new Set());
  const [isEditingName, setIsEditingName] = useState(false);
  const [isEditingDescription, setIsEditingDescription] = useState(false);
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");

  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const username = UserService.getUsername() || "";

  const {
    data: groupData,
    isLoading,
    error,
    refetch,
  } = useGetUserGroupByIdQuery(
    {
      id: Number(id),
      payerId,
    },
    { skip: !id || !payerId },
  );

  const { data: membersData, isLoading: isLoadingMembers } = useGetUserGroupMembersQuery(
    {
      groupId: Number(id),
      page,
      size,
    },
    { skip: !id },
  );

  const totalElements = membersData?.totalElements || 0;
  const totalPages = membersData?.totalPages || 1;

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleSizeChange = (newSize: number) => {
    setSize(newSize);
    setPage(1);
  };

  const [addRolesToGroup, { isLoading: isAddingRoles }] = useUpdateGroupRolesMutation();
  const [addUsersToGroup, { isLoading: isAddingUsers }] = useUpdateUserGroupMutation();
  const [removeUsersFromGroup, { isLoading: isRemovingUsersFromGroup }] =
    useRemoveUsersFromGroupMutation();
  const [deleteUserGroup] = useDeleteUserGroupMutation();
  const [updateGroupName, { isLoading: isUpdatingName }] = useUpdateUserGroupMutation();
  const [updateGroupDescription, { isLoading: isUpdatingDescription }] =
    useUpdateUserGroupMutation();
  const [updateUserGroup, { isLoading: isUpdatingUserGroup }] = useUpdateUserGroupMutation();

  useEffect(() => {
    if (groupData) {
      setGroup(groupData);
      setName(groupData.name);
      setDescription(groupData.description || "");

      if (groupData.predefinedRoles) {
        setSelectedPredefinedRoles(new Set(groupData.predefinedRoles));
      }

      if (groupData.customRoles) {
        setSelectedCustomRoleIds(new Set(groupData.customRoles.map((role) => role.id)));
      }
    }
  }, [groupData]);

  const handleAddUsers = async (users: UserRepresentation[]) => {
    if (!groupData || users.length === 0) return;

    try {
      const userIds = users.map((user) => user.id);
      const addGroupUsersPayload: UpdateUserGroupRequest = {
        id: groupData.id,
        payerId,
        body: {
          usersToAdd: userIds,
          updatedBy: username,
        },
      };

      await addUsersToGroup(addGroupUsersPayload).unwrap();
      toast.success("Users added to group successfully");
      refetch();
    } catch (error) {
      toast.error("Failed to add users to group");
      console.error("Error adding users to group:", error);
    }
  };

  const handleRemoveMultipleUsers = async (userIds: string[]) => {
    if (!groupData || userIds.length === 0) return;

    try {
      await removeUsersFromGroup({
        groupId: groupData.id,
        body: {
          userIds: userIds,
          actionedBy: username,
        },
      }).unwrap();
      setPage(1);
      toast.success(
        `${userIds.length} ${userIds.length === 1 ? "user" : "users"} removed from group successfully`,
      );
    } catch (error) {
      toast.error("Failed to remove users from group");
      console.error("Error removing users from group:", error);
    }
  };

  const handleAddRoles = async (predefinedRoles: string[], customRoles: CustomRoleResponse[]) => {
    if (!groupData) return;

    try {
      const newPredefinedRoles = new Set(selectedPredefinedRoles);
      const newCustomRoleIds = new Set(selectedCustomRoleIds);

      predefinedRoles.forEach((role) => newPredefinedRoles.add(role));
      customRoles.forEach((role) => newCustomRoleIds.add(role.id));

      const addGroupRolePayload: UpdateUserGroupRequest = {
        id: groupData.id,
        payerId,
        body: {
          customRolesToAdd: newCustomRoleIds.size > 0 ? Array.from(newCustomRoleIds) : undefined,
          predefinedRolesToAdd:
            newPredefinedRoles.size > 0 ? Array.from(newPredefinedRoles) : undefined,
          updatedBy: username,
        },
      };

      await addRolesToGroup(addGroupRolePayload).unwrap();

      setSelectedPredefinedRoles(newPredefinedRoles);
      setSelectedCustomRoleIds(newCustomRoleIds);

      toast.success("Roles added to group successfully");
      refetch();
    } catch (error) {
      toast.error("Failed to add roles to group");
      console.error("Error adding roles to group:", error);
    } finally {
      setShowAddRolesModal(false);
    }
  };

  const handleDelete = async () => {
    if (!groupData) return;

    try {
      await deleteUserGroup({
        id: groupData.id,
        deletedBy: username,
        payerId,
      }).unwrap();
      toast.success("User group deleted successfully");
      navigate("/users/groups");
    } catch (error: unknown) {
      const errorObj = error as { data?: RoleManagementDeletionResponse };
      const errorMessage = errorObj.data?.message || "Failed to delete user group.";
      toast.error(errorMessage);
      console.error("Error deleting user group:", error);
    }
  };

  const handleUpdateName = async () => {
    if (!groupData || !name.trim() || name === groupData.name) return;

    try {
      const payload = {
        id: groupData.id,
        payerId: Number(payerId),
        body: {
          name: name.trim(),
          updatedBy: username,
        },
      };

      await updateGroupName(payload).unwrap();
      toast.success("Group name updated successfully");
      setIsEditingName(false);
      refetch();
    } catch (error) {
      toast.error("Failed to update group name");
      console.error("Error updating group name:", error);
    }
  };

  const handleUpdateDescription = async () => {
    if (!groupData) return;

    try {
      const payload = {
        id: groupData.id,
        payerId: Number(payerId),
        body: {
          description: description.trim(),
          updatedBy: username,
        },
      };

      await updateGroupDescription(payload).unwrap();
      toast.success("Group description updated successfully");
      setIsEditingDescription(false);
      refetch();
    } catch (error) {
      toast.error("Failed to update group description");
      console.error("Error updating group description:", error);
    }
  };

  if (isLoading) {
    return (
      <MainWrapper className="flex flex-col gap-6">
        <div className="flex items-center justify-center py-44">
          <LoadingIcon className="h-6 w-6 text-blue-400" />
          <p className="ml-2 text-blue-700">Loading user group...</p>
        </div>
      </MainWrapper>
    );
  }

  if (error) {
    return (
      <MainWrapper>
        <TopBackButton onClick={() => navigate("/users/groups")} />
        <div className="py-20">
          <p className="text-red-700">Error loading user group. Try again later.</p>
        </div>
      </MainWrapper>
    );
  }

  if (!groupData) {
    return (
      <MainWrapper>
        <TopBackButton onClick={() => navigate("/users/groups")} />
        <div className="py-20">
          <Empty message="Group not found" />
        </div>
      </MainWrapper>
    );
  }

  const tabs: TabItem[] = [
    {
      title: "Details",
      content: (
        <GroupDetailsInfo
          group={groupData}
          isEditingName={isEditingName}
          setIsEditingName={setIsEditingName}
          isEditingDescription={isEditingDescription}
          setIsEditingDescription={setIsEditingDescription}
          name={name}
          setName={setName}
          description={description}
          setDescription={setDescription}
          onSaveName={handleUpdateName}
          onSaveDescription={handleUpdateDescription}
          isUpdating={isUpdatingName || isUpdatingDescription}
        />
      ),
    },
    {
      title: "Roles",
      content: (
        <GroupRolesList
          predefinedRoleNames={groupData.predefinedRoles}
          customRoles={groupData.customRoles}
          onAddRoles={() => setShowAddRolesModal(true)}
          groupId={groupData.id}
          memberCount={groupData.memberCount}
        />
      ),
    },
    {
      title: "Members",
      content: (
        <GroupMembersList
          members={membersData?.content}
          isLoading={isLoadingMembers}
          onAddUsers={() => setShowAddUserModal(true)}
          onRemoveMultipleUsers={handleRemoveMultipleUsers}
          groupName={group?.name}
          page={page}
          size={size}
          totalElements={totalElements}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          onSizeChange={handleSizeChange}
        />
      ),
    },
  ];

  return (
    <MainWrapper className="flex flex-col gap-6">
      <GroupDetailsHeader
        groupName={groupData.name}
        onBack={() => navigate("/users/groups")}
        onDelete={() => setShowDeleteConfirm(true)}
      />

      <TabGroup tabs={tabs} />

      {/* Modals */}
      <AddGroupUsersModal
        show={showAddUserModal}
        onClose={() => setShowAddUserModal(false)}
        selectedUserIds={selectedUserIds}
        onAddUsers={handleAddUsers}
        payerId={payerId}
        existingUserIds={new Set(membersData?.content?.map((member) => member.userId) || [])}
      />

      <ProgressModal
        isProgressModalOpen={isAddingUsers}
        onClose={() => null}
        title="Adding Users"
        description="Please wait while we add the selected users to the group..."
      />

      <ProgressModal
        isProgressModalOpen={isAddingRoles || isUpdatingUserGroup}
        onClose={() => null}
        title="Adding Roles"
        description="Please wait while we add roles to the group..."
      />

      <ProgressModal
        isProgressModalOpen={isRemovingUsersFromGroup}
        onClose={() => null}
        title="Removing Users"
        description="Please wait while we remove the users from the group..."
      />

      <ProgressModal
        isProgressModalOpen={isUpdatingName || isUpdatingDescription}
        onClose={() => null}
        title="Updating Group Information"
        description="Please wait while we update the group information..."
      />

      <DeleteGroupConfirmModal
        show={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        groupName={groupData.name}
        onDelete={handleDelete}
      />

      <AddGroupRolesModal
        show={showAddRolesModal}
        onClose={() => setShowAddRolesModal(false)}
        selectedPredefinedRoles={selectedPredefinedRoles}
        selectedCustomRoleIds={selectedCustomRoleIds}
        onAddRoles={handleAddRoles}
        handleAddRolesToGroup={async (predefinedRoles, customRoles) => {
          try {
            await updateUserGroup({
              id: groupData.id,
              payerId: Number(payerId),
              body: {
                predefinedRolesToAdd: predefinedRoles.length > 0 ? predefinedRoles : undefined,
                customRolesToAdd:
                  customRoles.length > 0 ? customRoles.map((role) => role.id) : undefined,
                updatedBy: username,
              },
            }).unwrap();

            const newPredefinedRoles = new Set(selectedPredefinedRoles);
            const newCustomRoleIds = new Set(selectedCustomRoleIds);

            predefinedRoles.forEach((role) => newPredefinedRoles.add(role));
            customRoles.forEach((role) => newCustomRoleIds.add(role.id));

            setSelectedPredefinedRoles(newPredefinedRoles);
            setSelectedCustomRoleIds(newCustomRoleIds);

            toast.success("Roles added to group successfully");

            refetch();
          } catch (error) {
            toast.error("Failed to add roles to group");
            console.error("Error adding roles to group:", error);
          } finally {
            setShowAddRolesModal(false);
          }
        }}
      />
    </MainWrapper>
  );
}
