import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router";
import { toast } from "react-toastify";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import {
  useCreateUserGroupMutation,
  useGetCustomRolesQuery,
} from "../../api/features/membershipApi";
import MainWrapper from "../../components/ui/MainWrapper";
import ProgressModal from "../../components/ui/modal/ProgressModal";
import TopBackButton from "../../components/ui/TopBackButton";
import Text from "../../components/ui/typography/Text";
import { CreateUserGroupRequest, CustomRoleResponse } from "../../lib/types/access-control/role";
import { UserRepresentation } from "../../lib/types/access-control/user";
import UserService from "../../services/UserService";
import AddGroupUsersModal from "./add-group-users-modal";
import BackButtonWarningOnEditModal from "../../components/ui/modal/back-button-warning-on-edit-modal";
import Button from "../../components/ui/Button";
import GroupInformationStep from "../../components/user-group/steps/group-information-step";
import CreateGroupRoleSelectionStep from "../../components/user-group/steps/create-group-role-selection-step";
import CreateGroupUserSelectionStep from "../../components/user-group/steps/create-group-user-selection-step";
import CreateGroupReviewStep from "../../components/user-group/steps/create-group-review-step";

enum CreateGroupStep {
  GROUP_INFORMATION = "GROUP_INFORMATION",
  ROLE_SELECTION = "ROLE_SELECTION",
  USER_SELECTION = "USER_SELECTION",
  REVIEW = "REVIEW",
}

const createGroupSchema = z.object({
  name: z
    .string()
    .trim()
    .min(3, "Group name must be at least 3 characters")
    .max(100, "Group name must not exceed 100 characters")
    .transform((val) => val.trim()),
  description: z
    .string()
    .trim()
    .min(3, "Group description must be at least 3 characters")
    .max(500, "Group description must not exceed 500 characters")
    .transform((val) => val.trim()),
  createdBy: z.string(),
  payerId: z.number(),
});

type FormInputs = z.infer<typeof createGroupSchema>;

export default function CreateGroupSteps() {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState<CreateGroupStep>(
    CreateGroupStep.GROUP_INFORMATION,
  );

  const [selectedRoles, setSelectedRoles] = useState<Set<string>>(new Set());
  const [selectedCustomRoleIds, setSelectedCustomRoleIds] = useState<Set<number>>(new Set());
  const [selectedUsers, setSelectedUsers] = useState<UserRepresentation[]>([]);
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [selectedUserIds, setSelectedUserIds] = useState<Set<string>>(new Set());
  const [customRoles, setCustomRoles] = useState<CustomRoleResponse[]>([]);
  const [isRoleTypeSelected, setIsRoleTypeSelected] = useState<boolean>(false);
  const [isBackButtonWarningModalOpen, setIsBackButtonWarningModalOpen] = useState(false);

  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const username = UserService.getUsername();

  const [createUserGroup, { isLoading: isCreating }] = useCreateUserGroupMutation();

  const { data: customRolesData, isLoading: isLoadingCustomRoles } = useGetCustomRolesQuery({
    payerId,
    page: 1,
    size: 100,
  });

  const form = useForm<FormInputs>({
    resolver: zodResolver(createGroupSchema),
    defaultValues: {
      name: "",
      description: "",
      createdBy: username,
      payerId: payerId,
    },
  });

  useEffect(() => {
    if (customRolesData?.content) {
      setCustomRoles(customRolesData.content);
    }
  }, [customRolesData]);

  useEffect(() => {
    setIsRoleTypeSelected(selectedRoles.size > 0 || selectedCustomRoleIds.size > 0);
  }, [selectedRoles, selectedCustomRoleIds]);

  const handlePredefinedRoleToggle = (slug: string) => {
    setSelectedRoles((prev) => {
      const newRoles = new Set(prev);
      if (newRoles.has(slug)) {
        newRoles.delete(slug);
      } else {
        newRoles.add(slug);
      }
      return newRoles;
    });
  };

  const handleCustomRoleToggle = (id: number) => {
    setSelectedCustomRoleIds((prev) => {
      const newRoles = new Set(prev);
      if (newRoles.has(id)) {
        newRoles.delete(id);
      } else {
        newRoles.add(id);
      }
      return newRoles;
    });
  };

  const handleClearAllRoles = () => {
    setSelectedRoles(new Set());
    setSelectedCustomRoleIds(new Set());
  };

  const handleRemoveUser = (userId: string) => {
    setSelectedUserIds((prev) => {
      const newIds = new Set(prev);
      newIds.delete(userId);
      return newIds;
    });
    setSelectedUsers((prev) => prev.filter((user) => user.id !== userId));
  };

  const handleAddUsers = (users: UserRepresentation[]) => {
    try {
      const newSelectedUsers = [...selectedUsers];
      const newSelectedUserIds = new Set(selectedUserIds);

      users.forEach((user) => {
        if (!newSelectedUserIds.has(user.id)) {
          newSelectedUserIds.add(user.id);
          newSelectedUsers.push(user);
        }
      });

      setSelectedUsers(newSelectedUsers);
      setSelectedUserIds(newSelectedUserIds);
    } catch (error) {
      console.error("Error adding users:", error);
      toast.error("Failed to add users");
    }
  };

  const handleBackClick = () => {
    setIsBackButtonWarningModalOpen(true);
  };

  const handleBackButtonConfirm = () => {
    navigate("/users/groups");
  };

  const handleNextStep = async () => {
    let nextStep: CreateGroupStep;
    let result = false;

    switch (currentStep) {
      case CreateGroupStep.GROUP_INFORMATION:
        result = await form.trigger(["name", "description"]);
        nextStep = CreateGroupStep.ROLE_SELECTION;
        break;
      case CreateGroupStep.ROLE_SELECTION:
        if (!isRoleTypeSelected) {
          toast.error("Please select at least one role");
          return;
        }
        result = true;
        nextStep = CreateGroupStep.USER_SELECTION;
        break;
      case CreateGroupStep.USER_SELECTION:
        result = true;
        nextStep = CreateGroupStep.REVIEW;
        break;
      default:
        return;
    }

    if (result) {
      setCurrentStep(nextStep);
    } else {
      toast.error("Please fill in all required fields before proceeding");
    }
  };

  const handlePreviousStep = () => {
    switch (currentStep) {
      case CreateGroupStep.ROLE_SELECTION:
        setCurrentStep(CreateGroupStep.GROUP_INFORMATION);
        break;
      case CreateGroupStep.USER_SELECTION:
        setCurrentStep(CreateGroupStep.ROLE_SELECTION);
        break;
      case CreateGroupStep.REVIEW:
        setCurrentStep(CreateGroupStep.USER_SELECTION);
        break;
      default:
        break;
    }
  };

  const onSubmit = async (data: FormInputs) => {
    if (selectedRoles.size === 0 && selectedCustomRoleIds.size === 0) {
      toast.error("Please select at least one role");
      setCurrentStep(CreateGroupStep.ROLE_SELECTION);
      return;
    }

    try {
      const payload: CreateUserGroupRequest = {
        name: data.name, // Already trimmed by Zod schema
        description: data.description, // Already trimmed by Zod schema
        customRoleIds: selectedCustomRoleIds.size > 0 ? Array.from(selectedCustomRoleIds) : [],
        predefinedRoles: selectedRoles.size > 0 ? Array.from(selectedRoles) : [],
        userIds: selectedUserIds.size > 0 ? Array.from(selectedUserIds) : [],
        createdBy: username,
        payerId: payerId,
      };

      await createUserGroup(payload).unwrap();
      toast.success("User group created successfully");
      navigate("/users/groups");
    } catch (error: unknown) {
      const errorObj = error as { data?: { error?: string } };
      const errorMessage = errorObj.data?.error || "Failed to create user group.";
      toast.error(errorMessage);
      console.error("Error creating user group:", error);
    }
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case CreateGroupStep.GROUP_INFORMATION:
        return "Step 1: Group Information";
      case CreateGroupStep.ROLE_SELECTION:
        return "Step 2: Assign Roles";
      case CreateGroupStep.USER_SELECTION:
        return "Step 3: Add Users";
      case CreateGroupStep.REVIEW:
        return "Step 4: Review and Create";
      default:
        return "";
    }
  };

  return (
    <MainWrapper className="flex flex-col gap-6">
      <section className="flex space-x-6">
        <TopBackButton onClick={handleBackClick} className="mt-1" />
        <div className="flex flex-col space-y-1">
          <Text variant="heading">Create New User Group</Text>
          <Text variant="description" className="italic">
            Create a new user group to assign roles to multiple users at once.
          </Text>
        </div>
      </section>

      <Text variant="subheading" className="mt-4">
        {getStepTitle()}
      </Text>

      <form className="space-y-6" onSubmit={(e) => e.preventDefault()}>
        {currentStep === CreateGroupStep.GROUP_INFORMATION && (
          <GroupInformationStep
            errors={form.formState.errors}
            register={form.register}
            clearErrors={form.clearErrors}
            watch={form.watch}
          />
        )}

        {currentStep === CreateGroupStep.ROLE_SELECTION && (
          <CreateGroupRoleSelectionStep
            selectedRoles={selectedRoles}
            selectedCustomRoleIds={selectedCustomRoleIds}
            customRoles={customRoles}
            isLoadingCustomRoles={isLoadingCustomRoles}
            onPredefinedRoleToggle={handlePredefinedRoleToggle}
            onCustomRoleToggle={handleCustomRoleToggle}
            onClearAllRoles={handleClearAllRoles}
          />
        )}

        {currentStep === CreateGroupStep.USER_SELECTION && (
          <CreateGroupUserSelectionStep
            selectedUsers={selectedUsers}
            onRemoveUser={handleRemoveUser}
            onShowAddUserModal={() => setShowAddUserModal(true)}
          />
        )}

        {currentStep === CreateGroupStep.REVIEW && (
          <CreateGroupReviewStep
            name={form.getValues("name")}
            description={form.getValues("description")}
            selectedRoles={selectedRoles}
            selectedCustomRoleIds={selectedCustomRoleIds}
            selectedUsers={selectedUsers}
            customRoles={customRoles}
          />
        )}

        <div className="flex justify-end space-x-4">
          {currentStep !== CreateGroupStep.GROUP_INFORMATION && (
            <Button variant="outlined" onClick={handlePreviousStep} type="button">
              Back
            </Button>
          )}

          {currentStep === CreateGroupStep.REVIEW ? (
            <Button
              type="button"
              disabled={isCreating}
              className="flex items-center gap-2"
              onClick={form.handleSubmit(onSubmit)}
            >
              {isCreating && <LoadingIcon className="h-4 w-4 text-white" />}
              Create Group
            </Button>
          ) : (
            <Button type="button" onClick={handleNextStep}>
              Next
            </Button>
          )}
        </div>
      </form>

      <AddGroupUsersModal
        show={showAddUserModal}
        onClose={() => setShowAddUserModal(false)}
        selectedUserIds={selectedUserIds}
        onAddUsers={handleAddUsers}
        payerId={payerId}
        existingUserIds={selectedUserIds}
      />

      <ProgressModal
        isProgressModalOpen={isCreating}
        onClose={() => null}
        title="Creating Group"
        description="Please wait while we create the group with the selected users and roles..."
      />

      <BackButtonWarningOnEditModal
        onClose={() => setIsBackButtonWarningModalOpen(false)}
        isBackButtonWarningModalOpen={isBackButtonWarningModalOpen}
        onConfirm={handleBackButtonConfirm}
      />
    </MainWrapper>
  );
}
