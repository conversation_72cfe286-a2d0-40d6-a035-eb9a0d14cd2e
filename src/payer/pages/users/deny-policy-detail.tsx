import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import {
  useDeleteDenyPolicyMutation,
  useGetDenyPolicyByIdQuery,
  useUpdateDenyPolicyMutation,
} from "../../api/features/membershipApi";
import Button from "../../components/ui/Button";
import MainWrapper from "../../components/ui/MainWrapper";
import TabGroup from "../../components/ui/tab-group";
import Text from "../../components/ui/typography/Text";
import {
  PermissionResponse,
  RoleManagementDeletionResponse,
  UpdateDenyPolicyRequest,
} from "../../lib/types/access-control/role";
import UserService from "../../services/UserService";
import { TrashIcon } from "@heroicons/react/24/outline";
import AddDenyPolicyUsersModal from "./add-deny-policy-users-modal";
import AddDenyPolicyPermissionsModal from "./add-deny-policy-permissions-modal";
import { UserRepresentation } from "../../lib/types/access-control/user";
import DenyPolicyInfo from "../../components/deny-policy/deny-policy-info";
import DenyPolicyUsers from "../../components/deny-policy/deny-policy-users";
import DenyPolicyPermissions from "../../components/deny-policy/deny-policy-permissions";
import DenyPolicyDeleteDialog from "../../components/deny-policy/deny-policy-delete-dialog";
import TopBackButton from "../../components/ui/TopBackButton";
import ProgressModal from "../../components/ui/modal/ProgressModal";

export default function DenyPolicyDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isEditingName, setIsEditingName] = useState(false);
  const [isEditingReason, setIsEditingReason] = useState(false);
  const [isAddingUsers, setIsAddingUsers] = useState(false);
  const [isUpdatingPermissions, setIsUpdatingPermissions] = useState(false);
  const [isDeletingPermission, setIsDeletingPermission] = useState(false);
  const [isUpdatingField, setIsUpdatingField] = useState(false);

  const [name, setName] = useState("");
  const [reason, setReason] = useState("");
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showAddUsersModal, setShowAddUsersModal] = useState(false);
  const [showAddPermissionsModal, setShowAddPermissionsModal] = useState(false);
  const [selectedPermissionNames, setSelectedPermissionNames] = useState<Set<string>>(new Set());
  const [isRemovingUser, setIsRemovingUser] = useState(false);

  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const username = UserService.getUsername();

  const {
    data: policyData,
    isLoading,
    error,
    refetch,
  } = useGetDenyPolicyByIdQuery({ id: Number(id), payerId });
  const [updateDenyPolicy] = useUpdateDenyPolicyMutation();
  const [deleteDenyPolicy, { isLoading: isDeleting }] = useDeleteDenyPolicyMutation();

  useEffect(() => {
    if (policyData) {
      setName(policyData.name);
      setReason(policyData.reason || "");
      setSelectedPermissionNames(new Set(policyData.deniedPermissions));
    }
  }, [policyData]);

  const handleSaveName = async () => {
    if (!policyData || name === policyData.name) return;

    try {
      setIsUpdatingField(true);
      const payload: UpdateDenyPolicyRequest = {
        id: policyData.id,
        body: {
          name,
          updatedBy: username,
        },
      };

      await updateDenyPolicy(payload).unwrap();
      toast.success("Policy name updated successfully");
      setIsEditingName(false);
      refetch();
    } catch (error) {
      toast.error("Failed to update policy name");
      console.error("Error updating policy name:", error);
    } finally {
      setIsUpdatingField(false);
    }
  };

  const handleSaveReason = async () => {
    if (!policyData || reason === policyData.reason) return;

    try {
      setIsUpdatingField(true);
      const payload: UpdateDenyPolicyRequest = {
        id: policyData.id,
        body: {
          reason,
          updatedBy: username,
        },
      };

      await updateDenyPolicy(payload).unwrap();
      toast.success("Policy reason updated successfully");
      setIsEditingReason(false);
      refetch();
    } catch (error) {
      toast.error("Failed to update policy reason");
      console.error("Error updating policy reason:", error);
    } finally {
      setIsUpdatingField(false);
    }
  };

  const handleDelete = async () => {
    if (!policyData) return;

    try {
      await deleteDenyPolicy({ id: policyData.id, payerId: policyData.payerId }).unwrap();
      toast.success("Deny policy deleted successfully");
      navigate("/users/deny-policies");
    } catch (error: unknown) {
      const errorObj = error as { data?: RoleManagementDeletionResponse };
      const errorMessage = errorObj.data?.message || "Failed to delete deny policy.";
      toast.error(errorMessage);
      console.error("Error deleting deny policy:", error);
    }
  };

  const handleAddUsers = async (users: UserRepresentation[]) => {
    if (!policyData) return;

    const userIds = new Set(users.map((user) => user.id));

    try {
      setIsAddingUsers(true);
      const payload: UpdateDenyPolicyRequest = {
        id: policyData.id,
        body: {
          addUsers: Array.from(userIds),
          updatedBy: UserService.getUsername() || "",
        },
      };

      await updateDenyPolicy(payload).unwrap();
      toast.success("Users added to deny policy successfully");
      refetch();
    } catch (error) {
      toast.error("Failed to add users to deny policy");
      console.error("Error adding users to deny policy:", error);
    } finally {
      setIsAddingUsers(false);
    }
  };

  const handleAddPermissions = async (permissions: PermissionResponse[]) => {
    if (!policyData) return;

    if (permissions.length === 0) {
      toast.error("No permissions selected");
      return;
    }

    const permissionNames = new Set(
      permissions.map((permission) => {
        return permission.name;
      }),
    );

    try {
      setIsUpdatingPermissions(true);
      const payload: UpdateDenyPolicyRequest = {
        id: policyData.id,
        body: {
          addPermissions: Array.from(permissionNames),
          updatedBy: UserService.getUsername() || "",
        },
      };

      await updateDenyPolicy(payload).unwrap();
      toast.success("Permissions added to deny policy successfully");
      refetch();
    } catch (error) {
      toast.error("Failed to add permissions to deny policy");
      console.error("Error adding permissions to deny policy:", error);
    } finally {
      setIsUpdatingPermissions(false);
    }
  };

  const handleRemoveMultipleUsers = async (userIds: string[]) => {
    if (!policyData || userIds.length === 0) return;

    try {
      setIsRemovingUser(true);
      const payload: UpdateDenyPolicyRequest = {
        id: policyData.id,
        body: {
          removeUsers: userIds,
          updatedBy: UserService.getUsername() || "",
        },
      };

      await updateDenyPolicy(payload).unwrap();
      toast.success(`${userIds.length} user(s) removed from deny policy successfully`);
      refetch();
    } catch (error) {
      toast.error("Failed to remove users from deny policy");
      console.error("Error removing users from deny policy:", error);
    } finally {
      setIsRemovingUser(false);
    }
  };

  const handleRemovePermission = async (permission: string) => {
    if (!policyData) return;

    try {
      setIsDeletingPermission(true);
      const payload: UpdateDenyPolicyRequest = {
        id: policyData.id,
        body: {
          removePermissions: Array.from(new Set([permission])),
          updatedBy: UserService.getUsername() || "",
        },
      };

      await updateDenyPolicy(payload).unwrap();
      toast.success("Permission removed from deny policy successfully");
      refetch();
    } catch (error) {
      toast.error("Failed to remove permission from deny policy");
      console.error("Error removing permission from deny policy:", error);
    } finally {
      setIsDeletingPermission(false);
    }
  };

  if (isLoading) {
    return (
      <MainWrapper className="flex flex-col gap-6">
        <div className="flex items-center justify-center py-44">
          <LoadingIcon className="h-6 w-6 text-blue-400" />
          <p className="ml-2 text-blue-700">Loading deny policy...</p>
        </div>
      </MainWrapper>
    );
  }

  if (error) {
    return (
      <MainWrapper className="flex flex-col gap-6">
        <div className="flex items-center justify-center py-44">
          <p className="text-red-700">Error loading deny policy. Try again later.</p>
        </div>
      </MainWrapper>
    );
  }

  if (!policyData) {
    return (
      <MainWrapper className="flex flex-col gap-6">
        <div className="flex items-center justify-center py-44">
          <p className="text-red-700">Deny policy not found.</p>
        </div>
      </MainWrapper>
    );
  }

  return (
    <MainWrapper className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <TopBackButton onClick={() => navigate("/users/deny-policies")} className="mt-1" />
          <Text variant="heading">{policyData.name}</Text>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="destructive"
            className="flex items-center gap-2"
            onClick={() => setShowDeleteConfirm(true)}
          >
            <TrashIcon className="h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      {/* Create tab items for the TabGroup component */}
      <TabGroup
        tabs={[
          {
            title: "Details",
            content: (
              <DenyPolicyInfo
                policy={policyData}
                isEditingName={isEditingName}
                setIsEditingName={setIsEditingName}
                isEditingReason={isEditingReason}
                setIsEditingReason={setIsEditingReason}
                name={name}
                setName={setName}
                reason={reason}
                setReason={setReason}
                onSaveName={handleSaveName}
                onSaveReason={handleSaveReason}
                isUpdating={isUpdatingField}
              />
            ),
          },
          {
            title: "Users",
            content: (
              <DenyPolicyUsers
                policy={policyData}
                onAddUsers={() => setShowAddUsersModal(true)}
                onRemoveMultipleUsers={handleRemoveMultipleUsers}
              />
            ),
          },
          {
            title: "Permissions",
            content: (
              <DenyPolicyPermissions
                policy={policyData}
                onAddPermissions={() => setShowAddPermissionsModal(true)}
                onRemovePermission={handleRemovePermission}
                isDeletingPermission={isDeletingPermission}
              />
            ),
          },
        ]}
      />

      <DenyPolicyDeleteDialog
        show={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onDelete={handleDelete}
        policyName={policyData.name}
        isDeleting={isDeleting}
      />

      <AddDenyPolicyUsersModal
        show={showAddUsersModal}
        onClose={() => setShowAddUsersModal(false)}
        selectedUserIds={new Set()}
        onAddUsers={handleAddUsers}
        payerId={policyData.payerId.toString()}
        existingUserIds={new Set(policyData.users.map((user) => user.userId))}
      />

      <AddDenyPolicyPermissionsModal
        show={showAddPermissionsModal}
        onClose={() => setShowAddPermissionsModal(false)}
        selectedPermissionNames={selectedPermissionNames}
        onAddPermissions={handleAddPermissions}
      />

      <ProgressModal
        isProgressModalOpen={isRemovingUser}
        onClose={() => null}
        title="Removing User"
        description="Please wait while we remove the user from the deny policy..."
      />

      <ProgressModal
        isProgressModalOpen={isAddingUsers}
        onClose={() => null}
        title="Adding Users"
        description="Please wait while we add users to the deny policy..."
      />

      <ProgressModal
        isProgressModalOpen={isUpdatingPermissions}
        onClose={() => null}
        title="Adding Permissions"
        description="Please wait while we add permissions to the deny policy..."
      />

      <ProgressModal
        isProgressModalOpen={isDeletingPermission}
        onClose={() => null}
        title="Removing Permission"
        description="Please wait while we remove the permission from the deny policy..."
      />

      <ProgressModal
        isProgressModalOpen={isUpdatingField}
        onClose={() => null}
        title="Updating Policy"
        description="Please wait while we update the policy..."
      />
    </MainWrapper>
  );
}
