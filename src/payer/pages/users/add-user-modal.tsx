import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import {
  useAddUserPayerWithOptionalRolesMutation,
  useGetCustomRolesQuery,
  useGetUserGroupsQuery,
} from "../../api/features/membershipApi";
import Button from "../../components/ui/Button";
import ButtonGroup from "../../components/ui/ButtonGroup";
import RequiredTag from "../../components/ui/input/required-tag";
import SearchInput from "../../components/ui/input/SearchInput";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import SecondaryPagination from "../../components/ui/pagination/SecondaryPagination";
import Text from "../../components/ui/typography/Text";
import {
  CreatePayerUserRequest,
  CustomRoleResponse,
  UserGroupResponse,
} from "../../lib/types/access-control/role";
import UserService from "../../services/UserService";

interface AddUserModalProps {
  show: boolean;
  onClose: () => void;
  payerId: string;
  onSuccess?: () => void;
}

enum CreateUserPermissionsTab {
  USER_GROUPS = "User Groups",
  CUSTOM_ROLES = "Custom Roles",
}

const userSchema = z.object({
  username: z
    .string()
    .trim()
    .min(3, "Username must be at least 3 characters")
    .regex(
      /^[a-zA-Z0-9_.-]+$/,
      "Username can only contain letters, numbers, underscores, dashes and dots",
    ),
  email: z.string().trim().email("Please enter a valid email address"),
  firstName: z
    .string()
    .trim()
    .min(2, "First name must be at least 2 characters")
    .regex(/^[a-zA-Z]+$/, "First name can only contain letters"),
  lastName: z
    .string()
    .trim()
    .min(2, "Last name must be at least 2 characters")
    .regex(/^[a-zA-Z]+$/, "Last name can only contain letters"),
});

type UserFormInputs = z.infer<typeof userSchema>;

export default function AddUserModal({ show, onClose, payerId, onSuccess }: AddUserModalProps) {
  const [currentStep, setCurrentStep] = useState<1 | 2>(1);
  const [selectedTab, setSelectedTab] = useState<CreateUserPermissionsTab>(
    CreateUserPermissionsTab.USER_GROUPS,
  );
  const [selectedGroupIds, setSelectedGroupIds] = useState<Set<number>>(new Set());
  const [selectedCustomRoleIds, setSelectedCustomRoleIds] = useState<Set<number>>(new Set());

  const [groupsCurrentPage, setGroupsCurrentPage] = useState(1);
  const [groupsSearchTerm, setGroupsSearchTerm] = useState("");
  const groupsPageSize = 3;

  const [customRolesCurrentPage, setCustomRolesCurrentPage] = useState(1);
  const [customRolesSearchTerm, setCustomRolesSearchTerm] = useState("");
  const customRolesPageSize = 3;

  const [filteredGroups, setFilteredGroups] = useState<UserGroupResponse[]>([]);

  const username = UserService.getUsername();

  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors },
  } = useForm<UserFormInputs>({
    resolver: zodResolver(userSchema),
    mode: "onChange",
  });

  const [addUserWithRoles, { isLoading: isAddingUser }] =
    useAddUserPayerWithOptionalRolesMutation();

  const { data: customRolesData, isLoading: isLoadingCustomRoles } = useGetCustomRolesQuery({
    payerId: Number(payerId),
    page: 1,
    size: 100,
  });

  const { data: userGroupsData, isLoading: isLoadingGroups } = useGetUserGroupsQuery({
    payerId: Number(payerId),
    page: 1,
    size: 100,
  });

  const tabOptions = [
    { title: CreateUserPermissionsTab.USER_GROUPS },
    { title: CreateUserPermissionsTab.CUSTOM_ROLES },
  ];

  useEffect(() => {
    if (userGroupsData?.content) {
      const filtered = userGroupsData.content.filter(
        (group) =>
          group.name.toLowerCase().includes(groupsSearchTerm.toLowerCase()) ||
          (group.description &&
            group.description.toLowerCase().includes(groupsSearchTerm.toLowerCase())),
      );
      setFilteredGroups(filtered);
      setGroupsCurrentPage(1);
    }
  }, [userGroupsData, groupsSearchTerm]);

  const groupsTotalPages = Math.ceil(filteredGroups.length / groupsPageSize);
  const groupsStartIndex = (groupsCurrentPage - 1) * groupsPageSize;
  const groupsEndIndex = groupsStartIndex + groupsPageSize;
  const paginatedGroups = filteredGroups.slice(groupsStartIndex, groupsEndIndex);

  const [filteredCustomRoles, setFilteredCustomRoles] = useState<CustomRoleResponse[]>([]);

  useEffect(() => {
    if (customRolesData?.content) {
      const filtered = customRolesData.content.filter(
        (role) =>
          role.name.toLowerCase().includes(customRolesSearchTerm.toLowerCase()) ||
          (role.description &&
            role.description.toLowerCase().includes(customRolesSearchTerm.toLowerCase())),
      );
      setFilteredCustomRoles(filtered);
      setCustomRolesCurrentPage(1);
    }
  }, [customRolesData, customRolesSearchTerm]);

  const customRolesTotalPages = Math.ceil(filteredCustomRoles.length / customRolesPageSize);
  const customRolesStartIndex = (customRolesCurrentPage - 1) * customRolesPageSize;
  const customRolesEndIndex = customRolesStartIndex + customRolesPageSize;
  const paginatedCustomRoles = filteredCustomRoles.slice(
    customRolesStartIndex,
    customRolesEndIndex,
  );

  const onSubmit = async (data: UserFormInputs) => {
    try {
      const payload: CreatePayerUserRequest = {
        ...data,
        password: "123456",
        payerId: Number(payerId),
        createdBy: username,
        groupIds: selectedGroupIds.size > 0 ? Array.from(selectedGroupIds) : undefined,
        customRoleIds:
          selectedCustomRoleIds.size > 0 ? Array.from(selectedCustomRoleIds) : undefined,
      };

      await addUserWithRoles(payload).unwrap();
      toast.success("User added successfully");
      reset();
      setSelectedGroupIds(new Set());
      setSelectedCustomRoleIds(new Set());
      setGroupsSearchTerm("");
      setCustomRolesSearchTerm("");
      setGroupsCurrentPage(1);
      setCustomRolesCurrentPage(1);
      setCurrentStep(1);
      onSuccess?.();
    } catch (error: unknown) {
      const errorObj = error as { data?: { error?: string } };
      const errorMessage = errorObj.data?.error || "Failed to add user. Please try again.";
      toast.error(errorMessage);
      console.error("Error adding user:", error);
    }
  };

  const handleClose = () => {
    reset();
    setSelectedGroupIds(new Set());
    setSelectedCustomRoleIds(new Set());
    setGroupsSearchTerm("");
    setCustomRolesSearchTerm("");
    setGroupsCurrentPage(1);
    setCustomRolesCurrentPage(1);
    setCurrentStep(1);
    onClose();
  };

  const handleNextStep = () => {
    setCurrentStep(2);
  };

  const handlePreviousStep = () => {
    setCurrentStep(1);
  };

  const handleGroupToggle = (groupId: number) => {
    setSelectedGroupIds((prev) => {
      const newGroups = new Set(prev);
      if (newGroups.has(groupId)) {
        newGroups.delete(groupId);
      } else {
        newGroups.add(groupId);
      }
      return newGroups;
    });
  };

  const handleCustomRoleToggle = (id: number) => {
    setSelectedCustomRoleIds((prev) => {
      const newRoles = new Set(prev);
      if (newRoles.has(id)) {
        newRoles.delete(id);
      } else {
        newRoles.add(id);
      }
      return newRoles;
    });
  };

  return (
    <DialogWrapper show={show} onClose={handleClose} maxWidth="max-w-4xl" className="p-6">
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <Text variant="heading">Add New User</Text>
          <div className="flex items-center space-x-2">
            <div
              className={`flex h-8 w-8 items-center justify-center rounded-full ${currentStep === 1 ? "bg-blue-600 text-white" : "bg-gray-200 text-gray-600"}`}
            >
              1
            </div>
            <div className="h-0.5 w-6 bg-gray-300"></div>
            <div
              className={`flex h-8 w-8 items-center justify-center rounded-full ${currentStep === 2 ? "bg-blue-600 text-white" : "bg-gray-200 text-gray-600"}`}
            >
              2
            </div>
          </div>
        </div>

        <Text variant="description" className="text-gray-600">
          {currentStep === 1
            ? "Step 1: Enter basic user information"
            : "Step 2: Manage Access (optional)"}
        </Text>

        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4">
          {currentStep === 1 ? (
            <>
              <div className="flex flex-col gap-2">
                <label htmlFor="username" className="text-sm font-medium">
                  Username <RequiredTag />
                </label>
                <input
                  id="username"
                  type="text"
                  className={`rounded-md border ${
                    errors.username ? "border-red-500" : "border-gray-300"
                  } px-3 py-2 text-sm`}
                  placeholder="Enter username"
                  {...register("username")}
                />
                {errors.username && (
                  <span className="text-xs text-red-500">{errors.username.message}</span>
                )}
              </div>

              <div className="flex flex-col gap-2">
                <label htmlFor="email" className="text-sm font-medium">
                  Email <RequiredTag />
                </label>
                <input
                  id="email"
                  type="email"
                  className={`rounded-md border ${
                    errors.email ? "border-red-500" : "border-gray-300"
                  } px-3 py-2 text-sm`}
                  placeholder="Enter email"
                  {...register("email")}
                />
                {errors.email && (
                  <span className="text-xs text-red-500">{errors.email.message}</span>
                )}
              </div>

              <div className="flex flex-col gap-2">
                <label htmlFor="firstName" className="text-sm font-medium">
                  First Name <RequiredTag />
                </label>
                <input
                  id="firstName"
                  type="text"
                  inputMode="text"
                  pattern="[A-Za-z\s]+"
                  className={`rounded-md border ${
                    errors.firstName ? "border-red-500" : "border-gray-300"
                  } px-3 py-2 text-sm`}
                  placeholder="Enter first name"
                  {...register("firstName")}
                />
                {errors.firstName && (
                  <span className="text-xs text-red-500">{errors.firstName.message}</span>
                )}
              </div>

              <div className="flex flex-col gap-2">
                <label htmlFor="lastName" className="text-sm font-medium">
                  Last Name <RequiredTag />
                </label>
                <input
                  id="lastName"
                  type="text"
                  pattern="[A-Za-z\s]+"
                  inputMode="text"
                  className={`rounded-md border ${
                    errors.lastName ? "border-red-500" : "border-gray-300"
                  } px-3 py-2 text-sm`}
                  placeholder="Enter last name"
                  {...register("lastName")}
                />
                {errors.lastName && (
                  <span className="text-xs text-red-500">{errors.lastName.message}</span>
                )}
              </div>

              <div className="mt-2 rounded-md bg-blue-50 p-3">
                <p className="text-sm text-gray-700">
                  <strong>Note:</strong> A default password of <strong>123456</strong> will be
                  applied to this user temporarily. The user will be prompted to change it on first
                  login.
                </p>
              </div>

              <div className="mt-4 flex justify-end gap-3">
                <Button type="button" variant="outlined" onClick={handleClose}>
                  Cancel
                </Button>
                <Button
                  type="button"
                  onClick={handleNextStep}
                  disabled={
                    !watch("username") ||
                    !watch("email") ||
                    !watch("firstName") ||
                    !watch("lastName") ||
                    Object.keys(errors).length > 0
                  }
                >
                  Next: Assign Roles
                </Button>
              </div>
            </>
          ) : (
            <>
              <div className="rounded-lg border p-6 shadow-sm">
                <Text variant="subheading" className="mb-4">
                  Assign Access Permissions (Optional)
                </Text>
                <div className="mb-4 rounded-md p-3">
                  <p className="text-sm text-gray-700">
                    <strong>Note:</strong> You can select both user groups and custom roles for this
                    user. Use the tabs to switch between the two options.
                  </p>
                </div>
                <div className="mb-4">
                  <ButtonGroup
                    options={tabOptions}
                    setActiveOption={(title) => setSelectedTab(title)}
                    activeOption={selectedTab}
                  />
                </div>

                {selectedTab === "User Groups" ? (
                  <div className="flex flex-col space-y-4">
                    <div className="flex items-center justify-between">
                      <Text variant="description" className="">
                        Select user groups to assign to this user
                      </Text>
                      <SearchInput
                        value={groupsSearchTerm}
                        onChange={(e) => setGroupsSearchTerm(e.target.value)}
                        placeholder="Search groups..."
                        className="w-64"
                      />
                    </div>

                    <div className="space-y-1">
                      {isLoadingGroups ? (
                        <div className="flex items-center justify-center py-8">
                          <LoadingIcon className="h-6 w-6 text-blue-400" />
                          <span className="ml-2 text-gray-600">Loading user groups...</span>
                        </div>
                      ) : paginatedGroups.length > 0 ? (
                        paginatedGroups.map((group) => (
                          <div
                            key={group.id}
                            className="flex items-start space-x-2 rounded-md border p-3"
                          >
                            <input
                              type="checkbox"
                              id={`group-${group.id}`}
                              checked={selectedGroupIds.has(group.id)}
                              onChange={() => handleGroupToggle(group.id)}
                              className="form-checkbox mt-1"
                            />
                            <label htmlFor={`group-${group.id}`} className="flex flex-col">
                              <span className="font-medium">{group.name}</span>
                              <span className="text-gray-600">
                                {group.description || "No description"}
                              </span>
                            </label>
                          </div>
                        ))
                      ) : (
                        <div className="py-4 text-center text-xs text-gray-500">
                          {userGroupsData?.content && userGroupsData.content.length > 0
                            ? "No user groups match your search"
                            : "No user groups available"}
                        </div>
                      )}
                    </div>

                    {filteredGroups.length > 0 && (
                      <div className="mt-2 flex justify-end">
                        <SecondaryPagination
                          currentPage={groupsCurrentPage}
                          setCurrentPage={setGroupsCurrentPage}
                          size={groupsPageSize}
                          totalElements={filteredGroups.length}
                          totalPages={groupsTotalPages}
                        />
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex flex-col space-y-4">
                    <div className="flex items-center justify-between">
                      <Text variant="description">Select custom roles to assign to this user</Text>
                      <SearchInput
                        value={customRolesSearchTerm}
                        onChange={(e) => setCustomRolesSearchTerm(e.target.value)}
                        placeholder="Search custom roles..."
                        className="w-64"
                      />
                    </div>

                    <div className="space-y-4">
                      {isLoadingCustomRoles ? (
                        <div className="flex items-center justify-center py-8">
                          <LoadingIcon className="h-6 w-6 text-blue-400" />
                          <span className="ml-2 text-gray-600">Loading custom roles...</span>
                        </div>
                      ) : filteredCustomRoles.length > 0 ? (
                        paginatedCustomRoles.map((role) => (
                          <div
                            key={role.id}
                            className="flex items-start space-x-2 rounded-md border p-3"
                          >
                            <input
                              type="checkbox"
                              id={`custom-role-${role.id}`}
                              checked={selectedCustomRoleIds.has(role.id)}
                              onChange={() => handleCustomRoleToggle(role.id)}
                              className="form-checkbox mt-1"
                            />
                            <label htmlFor={`custom-role-${role.id}`} className="flex flex-col">
                              <span className="font-medium">{role.name}</span>
                              <span className="text-sm text-gray-600">
                                {role.description || "No description"}
                              </span>
                            </label>
                          </div>
                        ))
                      ) : (
                        <div className="py-4 text-center text-gray-500">
                          {customRolesData?.content && customRolesData.content.length > 0
                            ? "No custom roles match your search"
                            : "No custom roles available"}
                        </div>
                      )}
                    </div>

                    {filteredCustomRoles.length > 0 && (
                      <div className="mt-2 flex justify-end">
                        <SecondaryPagination
                          currentPage={customRolesCurrentPage}
                          setCurrentPage={setCustomRolesCurrentPage}
                          size={customRolesPageSize}
                          totalElements={filteredCustomRoles.length}
                          totalPages={customRolesTotalPages}
                        />
                      </div>
                    )}
                  </div>
                )}

                <div className="mt-6 rounded-md border bg-gray-50 p-3">
                  <Text variant="subheading" className="mb-2 text-sm">
                    Selected Roles Summary
                  </Text>
                  <div className="text-sm text-gray-600">
                    {selectedGroupIds.size > 0 && (
                      <div className="mb-2">
                        <p className="font-medium">User Groups ({selectedGroupIds.size}):</p>
                      </div>
                    )}
                    {selectedCustomRoleIds.size > 0 && (
                      <div className="mb-2">
                        <p className="font-medium">Custom Roles ({selectedCustomRoleIds.size}):</p>
                      </div>
                    )}
                    {selectedGroupIds.size === 0 && selectedCustomRoleIds.size === 0 && (
                      <p>No groups or roles selected. User will be created without any roles.</p>
                    )}
                  </div>
                </div>
              </div>

              <div className="mt-4 flex justify-end gap-3">
                <Button type="button" variant="outlined" onClick={handlePreviousStep}>
                  Back
                </Button>
                <Button type="submit" disabled={isAddingUser}>
                  {isAddingUser ? "Adding..." : "Add User"}
                </Button>
              </div>
            </>
          )}
        </form>
      </div>
    </DialogWrapper>
  );
}
