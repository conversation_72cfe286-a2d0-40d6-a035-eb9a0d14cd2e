import Text from "../../components/ui/typography/Text";
import { useNavigate } from "react-router";
import { predefinedRoles } from "../../components/access-management/data";
import MainWrapper from "../../components/ui/MainWrapper";
import CardWrapper from "../../components/ui/CardWrapper";

export default function PredefinedRoles() {
  const navigate = useNavigate();

  return (
    <MainWrapper className="flex flex-col">
      <section className="flex justify-between gap-6">
        <div className="flex flex-col gap-3">
          <Text variant="heading">Role Management</Text>
          <Text variant="description" className="italic">
            Predefined roles align with system modules. Click on a role to view its permissions.
          </Text>
        </div>
      </section>
      <div className="flex flex-col">
        <div className="mt-2 rounded-xl bg-white p-3">
          <div className="flex flex-col space-y-6">
            {predefinedRoles.map((role) => (
              <CardWrapper
                key={role.slug}
                className="flex cursor-pointer space-x-3 pt-4"
                onClick={() => navigate(`/users/roles/predefined/${role.slug}`)}
              >
                <div className="pt-2">{role.icon}</div>
                <div>
                  <Text variant="subheading" className="font-medium">
                    {role.name}
                  </Text>
                  <Text variant="description" className="mt-1 text-gray-600">
                    {role.description}
                  </Text>
                </div>
              </CardWrapper>
            ))}
          </div>
        </div>
      </div>
    </MainWrapper>
  );
}
