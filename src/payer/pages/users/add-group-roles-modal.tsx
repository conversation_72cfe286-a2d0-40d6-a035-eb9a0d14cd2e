import { useEffect, useState } from "react";
import { useGetCustomRolesQuery } from "../../api/features/membershipApi";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import Text from "../../components/ui/typography/Text";
import Button from "../../components/ui/Button";
import SearchInput from "../../components/ui/input/SearchInput";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { CustomRoleResponse } from "../../lib/types/access-control/role";
import UserService from "../../services/UserService";
import { predefinedRoles } from "../../components/access-management/data";
import { XMarkIcon } from "@heroicons/react/24/outline";

interface AddGroupRolesModalProps {
  show: boolean;
  onClose: () => void;
  selectedPredefinedRoles: Set<string>;
  selectedCustomRoleIds: Set<number>;
  onAddRoles: (predefinedRoles: string[], customRoles: CustomRoleResponse[]) => Promise<void>;
  handleAddRolesToGroup?: (
    predefinedRoles: string[],
    customRoles: CustomRoleResponse[],
  ) => Promise<void>;
}

export default function AddGroupRolesModal({
  show,
  onClose,
  selectedPredefinedRoles,
  selectedCustomRoleIds,
  onAddRoles,
  handleAddRolesToGroup,
}: AddGroupRolesModalProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<"predefined" | "custom">("predefined");
  const [newPredefinedRoles, setNewPredefinedRoles] = useState<string[]>([]);
  const [newCustomRoles, setNewCustomRoles] = useState<CustomRoleResponse[]>([]);
  const [localSelectedPredefinedRoles, setLocalSelectedPredefinedRoles] = useState<Set<string>>(
    new Set(),
  );
  const [localSelectedCustomRoleIds, setLocalSelectedCustomRoleIds] = useState<Set<number>>(
    new Set(),
  );

  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];

  const { data: customRolesData, isLoading: isLoadingCustomRoles } = useGetCustomRolesQuery(
    {
      payerId: Number(payerId),
      page: 1,
      size: 100,
    },
    { skip: !payerId },
  );

  useEffect(() => {
    if (show) {
      setLocalSelectedPredefinedRoles(new Set());
      setLocalSelectedCustomRoleIds(new Set());
      setNewPredefinedRoles([]);
      setNewCustomRoles([]);
      setSearchTerm("");
      setActiveTab("predefined");
    }
  }, [show]);

  const handleTogglePredefinedRole = (role: string) => {
    const newSet = new Set(localSelectedPredefinedRoles);

    if (newSet.has(role)) {
      newSet.delete(role);
    } else {
      newSet.add(role);
    }

    setLocalSelectedPredefinedRoles(newSet);

    const newRoles = Array.from(newSet).filter((role) => !selectedPredefinedRoles.has(role));
    setNewPredefinedRoles(newRoles);
  };

  const handleToggleCustomRole = (role: CustomRoleResponse) => {
    const newSet = new Set(localSelectedCustomRoleIds);

    if (newSet.has(role.id)) {
      newSet.delete(role.id);
    } else {
      newSet.add(role.id);
    }

    setLocalSelectedCustomRoleIds(newSet);

    const newRoles =
      customRolesData?.content.filter(
        (r) => newSet.has(r.id) && !selectedCustomRoleIds.has(r.id),
      ) || [];

    setNewCustomRoles(newRoles);
  };

  const handleAddRoles = async () => {
    setIsLoading(true);
    try {
      if (handleAddRolesToGroup) {
        await handleAddRolesToGroup(newPredefinedRoles, newCustomRoles);
        onClose();
      } else {
        await onAddRoles(newPredefinedRoles, newCustomRoles);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setLocalSelectedPredefinedRoles(new Set());
    setLocalSelectedCustomRoleIds(new Set());
    setNewPredefinedRoles([]);
    setNewCustomRoles([]);
  };

  const handleClose = () => {
    setLocalSelectedPredefinedRoles(new Set());
    setLocalSelectedCustomRoleIds(new Set());
    setNewPredefinedRoles([]);
    setNewCustomRoles([]);
    onClose();
  };

  const hasNewRoles = newPredefinedRoles.length > 0 || newCustomRoles.length > 0;

  const filteredPredefinedRoles = predefinedRoles
    ? predefinedRoles.filter((role) => {
        if (selectedPredefinedRoles.has(role.role)) {
          return false;
        }
        return (
          role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (role.description && role.description.toLowerCase().includes(searchTerm.toLowerCase()))
        );
      })
    : [];

  const filteredCustomRoles = customRolesData?.content
    ? customRolesData.content.filter((role) => {
        if (selectedCustomRoleIds.has(role.id)) {
          return false;
        }
        return (
          role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (role.description && role.description.toLowerCase().includes(searchTerm.toLowerCase()))
        );
      })
    : [];

  return (
    <DialogWrapper show={show} onClose={handleClose} maxWidth="max-w-5xl" className="h-[700px]">
      <div>
        <div className="relative border-b border-gray-200 px-6 py-4">
          <Text variant="subheading">Add Roles to Group</Text>
          <button
            onClick={handleClose}
            className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
            aria-label="Close modal"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
        <div className="p-6">
          <div className="mb-4 flex justify-between">
            <div>
              <Text variant="description">Select roles to assign to this group.</Text>
            </div>
            <SearchInput
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search roles..."
              className="w-64"
            />
          </div>

          <div className="mb-4 flex border-b">
            <button
              className={`px-4 py-2 font-medium ${activeTab === "predefined" ? "border-b-2 border-blue-500 text-blue-600" : "text-gray-500 hover:text-gray-700"}`}
              onClick={() => setActiveTab("predefined")}
            >
              Predefined Roles
            </button>
            <button
              className={`px-4 py-2 font-medium ${activeTab === "custom" ? "border-b-2 border-blue-500 text-blue-600" : "text-gray-500 hover:text-gray-700"}`}
              onClick={() => setActiveTab("custom")}
            >
              Custom Roles
            </button>
          </div>

          {isLoadingCustomRoles && activeTab === "custom" ? (
            <div className="flex items-center justify-center py-8">
              <LoadingIcon className="h-6 w-6 text-blue-400" />
              <span className="ml-2 text-gray-600">Loading custom roles...</span>
            </div>
          ) : (
            <>
              <div className="mb-4 max-h-96 overflow-y-auto rounded-md border border-gray-200 p-4">
                {activeTab === "predefined" && (
                  <div className="space-y-2">
                    {filteredPredefinedRoles.length > 0 ? (
                      filteredPredefinedRoles.map((role) => (
                        <div
                          key={role.role}
                          className="rounded-md border border-gray-200 p-3 hover:bg-gray-50"
                        >
                          <div className="flex items-start">
                            <div className="mr-2 mt-0.5">
                              <input
                                type="checkbox"
                                id={`predefined-role-${role.role}`}
                                checked={localSelectedPredefinedRoles.has(role.role)}
                                onChange={() => handleTogglePredefinedRole(role.role)}
                                className="h-4 w-4 rounded border-gray-300 text-blue-600"
                              />
                            </div>
                            <div>
                              <label
                                htmlFor={`predefined-role-${role.role}`}
                                className="block font-medium text-gray-700"
                              >
                                {role.name}
                              </label>
                              {role.description && (
                                <p className="mt-1 text-sm text-gray-500">{role.description}</p>
                              )}
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="py-2 text-center text-gray-500">
                        {searchTerm
                          ? "No predefined roles found"
                          : "All predefined roles are already assigned to this group"}
                      </div>
                    )}
                  </div>
                )}

                {activeTab === "custom" && customRolesData?.content && (
                  <div className="space-y-2">
                    {filteredCustomRoles.length > 0 ? (
                      filteredCustomRoles.map((role) => (
                        <div
                          key={role.id}
                          className="rounded-md border border-gray-200 p-3 hover:bg-gray-50"
                        >
                          <div className="flex items-start">
                            <div className="mr-2 mt-0.5">
                              <input
                                type="checkbox"
                                id={`custom-role-${role.id}`}
                                checked={localSelectedCustomRoleIds.has(role.id)}
                                onChange={() => handleToggleCustomRole(role)}
                                className="h-4 w-4 rounded border-gray-300 text-green-600"
                              />
                            </div>
                            <div>
                              <label
                                htmlFor={`custom-role-${role.id}`}
                                className="block font-medium text-gray-700"
                              >
                                {role.name}
                              </label>
                              {role.description && (
                                <p className="mt-1 text-sm text-gray-500">{role.description}</p>
                              )}
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="py-2 text-center text-gray-500">
                        {searchTerm
                          ? "No custom roles found"
                          : "All custom roles are already assigned to this group"}
                      </div>
                    )}
                  </div>
                )}
              </div>

              <div className="mb-4">
                <Text variant="description">
                  {newPredefinedRoles.length + newCustomRoles.length} new role(s) selected
                </Text>
              </div>
            </>
          )}

          <div className="mt-6 flex justify-end space-x-3">
            <Button variant="outlined" onClick={handleCancel} disabled={isLoading || !hasNewRoles}>
              Clear Selection
            </Button>
            <Button
              variant="filled"
              onClick={handleAddRoles}
              disabled={isLoading || !hasNewRoles}
              className="flex items-center gap-2"
            >
              {isLoading ? (
                <>
                  <LoadingIcon className="h-4 w-4 text-white" />
                  Adding...
                </>
              ) : (
                "Add Roles"
              )}
            </Button>
          </div>
        </div>
      </div>
    </DialogWrapper>
  );
}
