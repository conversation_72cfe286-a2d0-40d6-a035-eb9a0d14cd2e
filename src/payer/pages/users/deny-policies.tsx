import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { useGetDenyPoliciesByPayerIdQuery } from "../../api/features/membershipApi";
import Button from "../../components/ui/Button";
import MainWrapper from "../../components/ui/MainWrapper";
import PrimaryPagination from "../../components/ui/pagination/PrimaryPagination";
import SearchInput from "../../components/ui/input/SearchInput";
import Text from "../../components/ui/typography/Text";
import { DenyPolicyDto, GetPayerDenyPoliciesQuery } from "../../lib/types/access-control/role";
import UserService from "../../services/UserService";
import { PlusIcon } from "@heroicons/react/24/solid";
import EmptyState from "../../components/ui/EmptyState";
import NoDenyPolicies from "../../components/illustrations/no-deny-policies";
import DenyPolicyCard from "../../components/deny-policy/deny-policy-card";

export default function DenyPolicies() {
  const navigate = useNavigate();
  const [denyPolicies, setDenyPolicies] = useState<DenyPolicyDto[]>([]);
  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");

  const denyPoliciesParams: GetPayerDenyPoliciesQuery = {
    payerId,
    page,
    size,
  };

  const { data, isLoading, isFetching, error } =
    useGetDenyPoliciesByPayerIdQuery(denyPoliciesParams);
  const totalElements = data?.totalElements as number;
  const totalPages = data?.totalPages as number;

  const handlePageNumberClick = (page: number) => {
    setPage(page);
  };

  const handleSizeChange = (size: number) => {
    setSize(size);
    setPage(1);
  };

  useEffect(() => {
    if (data?.content) {
      setDenyPolicies(data.content);
    }
  }, [data]);

  const filteredPolicies = searchTerm
    ? denyPolicies.filter(
        (policy) =>
          policy.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          policy.reason?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          policy.users.some(
            (user) =>
              user.userName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
              user.userEmail?.toLowerCase().includes(searchTerm.toLowerCase()),
          ),
      )
    : denyPolicies;

  const shouldDisplayData = !isLoading && !isFetching && !error && filteredPolicies.length > 0;

  return (
    <MainWrapper className="flex flex-col gap-6">
      <section className="flex justify-between gap-6">
        <div className="flex flex-col gap-3">
          <Text variant="heading">Deny Policies</Text>
          <Text variant="description" className="italic">
            Deny policies override permissions granted by roles. They take precedence over any
            allowed permissions.
          </Text>
        </div>
        <div className="flex items-center gap-6">
          <SearchInput
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search by name, reason or user"
            title="Search by name, reason or user"
            className="h-fit w-[37ch]"
          />
          <Button
            className="whitespace-nowrap"
            onClick={() => navigate("/users/deny-policies/create")}
          >
            <PlusIcon className="h-5 w-5" /> Create Deny Policy
          </Button>
        </div>
      </section>
      {shouldDisplayData && (
        <section className="pt-2">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-3">
            {filteredPolicies.map((policy) => (
              <DenyPolicyCard key={policy.id} policy={policy} />
            ))}
          </div>
          <div className="self-start py-4">
            <PrimaryPagination
              pageSize={size}
              totalElements={totalElements ?? 0}
              totalPages={totalPages ?? 1}
              pageNumber={page}
              onPageNumberClick={handlePageNumberClick}
              onSizeChange={handleSizeChange}
            />
          </div>
        </section>
      )}
      {isLoading || isFetching ? (
        <div className="flex items-center justify-center space-x-2 self-center py-44">
          <LoadingIcon className="h-6 w-6 text-blue-400" />
          <p className="text-blue-700">Loading Deny Policies...</p>
        </div>
      ) : error ? (
        <div className="flex items-center justify-center self-center py-44">
          <p className="text-red-700">Error loading deny policies. Try again later.</p>
        </div>
      ) : (
        filteredPolicies?.length === 0 && (
          <div className="self-center py-44">
            <EmptyState
              illustration={<NoDenyPolicies />}
              message={{
                title: "No Deny Policies",
                description: "Create a deny policy to override permissions granted by roles.",
              }}
            />
          </div>
        )
      )}
    </MainWrapper>
  );
}
