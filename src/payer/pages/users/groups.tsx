import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { useGetUserGroupsQuery } from "../../api/features/membershipApi";
import Button from "../../components/ui/Button";
import MainWrapper from "../../components/ui/MainWrapper";
import PrimaryPagination from "../../components/ui/pagination/PrimaryPagination";
import SearchInput from "../../components/ui/input/SearchInput";
import Text from "../../components/ui/typography/Text";
import { GetUserGroupsQuery, UserGroupResponse } from "../../lib/types/access-control/role";
import UserService from "../../services/UserService";
import { PlusIcon } from "@heroicons/react/24/solid";
import EmptyState from "../../components/ui/EmptyState";
import NoGroups from "../../components/illustrations/no-groups";
import UserGroupCard from "../../components/user-group/user-group-card";

export default function Groups() {
  const navigate = useNavigate();
  const [userGroups, setUserGroups] = useState<UserGroupResponse[]>([]);
  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");

  const userGroupsParams: GetUserGroupsQuery = {
    payerId,
    page,
    size,
    name: searchTerm,
  };

  const { data, isLoading, isFetching, error } = useGetUserGroupsQuery(userGroupsParams);
  const totalElements = data?.totalElements as number;
  const totalPages = data?.totalPages as number;

  const handlePageNumberClick = (page: number) => {
    setPage(page);
  };

  const handleSizeChange = (size: number) => {
    setSize(size);
    setPage(1);
  };

  useEffect(() => {
    if (data?.content) {
      setUserGroups(data.content);
    }
  }, [data]);

  const filteredGroups = userGroups || [];
  const shouldDisplayData = !isLoading && !isFetching && filteredGroups.length > 0;

  return (
    <MainWrapper className="flex flex-col gap-6">
      <section className="flex justify-between gap-6">
        <div className="flex flex-col gap-3">
          <Text variant="heading">User Groups Management</Text>
          <Text variant="description" className="italic">
            Manage user groups for your team. Groups allow you to assign roles to multiple users at
            once.
          </Text>
        </div>
        <div className="flex items-center gap-6">
          <SearchInput
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search group by name"
            title="Search by name"
            className="h-fit w-[37ch]"
          />
          <Button className="whitespace-nowrap" onClick={() => navigate("/users/groups/create")}>
            <PlusIcon className="h-5 w-5" /> Create New Group
          </Button>
        </div>
      </section>
      {shouldDisplayData && (
        <>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredGroups.map((group) => (
              <UserGroupCard key={group.id} group={group} />
            ))}
          </div>
          <div className="self-start py-4">
            <PrimaryPagination
              pageSize={size}
              totalElements={totalElements ?? 0}
              totalPages={totalPages ?? 1}
              pageNumber={page}
              onPageNumberClick={handlePageNumberClick}
              onSizeChange={handleSizeChange}
            />
          </div>
        </>
      )}
      {isLoading || isFetching ? (
        <div className="flex items-center justify-center space-x-2 self-center py-44">
          <LoadingIcon className="h-6 w-6 text-blue-400" />
          <p className="text-blue-700">Loading User Groups...</p>
        </div>
      ) : error ? (
        <div className="flex items-center justify-center self-center py-44">
          <p className="text-red-700">Error loading user groups. Try again later.</p>
        </div>
      ) : (
        filteredGroups?.length === 0 && (
          <div className="self-center py-44">
            <EmptyState
              illustration={<NoGroups />}
              message={{
                title: "No Groups Found",
                description:
                  "Create a user group to manage permissions for multiple users at once.",
              }}
            />
          </div>
        )
      )}
    </MainWrapper>
  );
}
