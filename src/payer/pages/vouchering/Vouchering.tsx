import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import { useEffect } from "react";
import { toast } from "react-toastify";
import { useVoucherVettedClaimsMutation } from "../../api/features/claimsApi";
import cancel from "../../assets/material-symbols_close.png";
import NoClaimsVouchering from "../../components/illustrations/NoClaimsVouchering";
import EmptyState from "../../components/ui/EmptyState";
import MainWrapper from "../../components/ui/MainWrapper";
import ClaimSummary from "../../components/vouchering/ClaimSummary";
import ClaimsVoucheringFilter from "../../components/vouchering/ClaimsVoucheringFilter";
import CompletedVouchers from "../../components/vouchering/CompletedVouchers";
import InProgressVouchers from "../../components/vouchering/InProgressVouchers";
import InterimSaveButton from "../../components/vouchering/InterimSaveButton";
import AllocationConfirmationModal from "../../components/vouchering/modals/AllocationConfirmationModal";
import AllocationExceededWarningModal from "../../components/vouchering/modals/AllocationExceededWarningModal";
import ClaimsVoucheringAllocationModal from "../../components/vouchering/modals/ClaimsVoucheringAllocationModal";
import InterimSaveSuccessfulModal from "../../components/vouchering/modals/InterimSaveSuccessfulModal";
import ProcessingVouchersModal from "../../components/vouchering/modals/ProcessingVouchersModal";
import VoucheringVettedClaimsTable from "../../components/vouchering/VoucheringVettedClaimsTable";
import VoucherButton from "../../components/vouchering/VoucherButton";
import VoucheringMainCriteriaFilter from "../../components/vouchering/VoucheringMainCriteriaFilter";
import VoucheringProgressBar, {
  VoucheringProgress,
} from "../../components/vouchering/VoucheringProgressBar";
import { setCurrentVoucherAmount } from "../../features/claims/voucherEditingSlice";
import {
  setIsAllocationConfirmationModalOpen,
  setIsAllocationExceededWarningModalOpen,
  setIsBackButtonVisible,
  setIsInterimSaveSuccessfulModalOpen,
  setIsVoucheringAllocationModalOpen,
  setIsVoucheringSelectAllActive,
  setMainVoucheringCriteria,
  setUniqueCheckedClaimsIds,
  setVoucheringCheckedClaimsIds,
  setVoucheringPayableAmount,
  setVoucheringProgress,
} from "../../features/claims/voucheringSlice";
import { useClearFilters } from "../../hooks/useClearFilter";
import useResetVoucheringAllocation from "../../hooks/useResetVoucheringAllocation";
import { VoucherClaimsPayload } from "../../lib/types/claims/invoice";
import UserService from "../../services/UserService";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import { isValidBatchingCriteria, isValidFilterName, isValidId } from "../../utils/utils";

export default function Vouchering() {
  const dispatch = useAppDispatch();
  const clearFilters = useClearFilters();
  const isBackButtonVisible = useAppSelector((state) => state.vouchering.isBackButtonVisible);
  const allocatedAmount = useAppSelector((state) => state.vouchering.allocatedAmount);
  const voucheringPayableAmount = useAppSelector(
    (state) => state.vouchering.voucheringPayableAmount,
  );
  const isAllocationConfirmationModalOpen = useAppSelector(
    (state) => state.vouchering.isAllocationConfirmationModalOpen,
  );
  const isVoucheringAllocationModalOpen = useAppSelector(
    (state) => state.vouchering.isVoucheringAllocationModalOpen,
  );
  const isAllocationExceededWarningModalOpen = useAppSelector(
    (state) => state.vouchering.isAllocationExceededWarningModalOpen,
  );
  const uniqueCheckedClaimsIds = useAppSelector((state) => state.vouchering.uniqueCheckedClaimsIds);
  const mainVoucheringCriteria = useAppSelector((state) => state.vouchering.mainVoucheringCriteria);
  const voucheringProgress = useAppSelector((state) => state.vouchering.voucheringProgress);
  const isCalculatingVoucherAmount = useAppSelector(
    (state) => state.vouchering.isCalculatingVoucherAmount,
  );
  const isInterimSaveSuccessModalOpen = useAppSelector(
    (state) => state.vouchering.isInterimSaveSuccessfulModalOpen,
  );
  const selectedAccount = useAppSelector((state) => state.vouchering.selectedAccount);
  const selectedRegion = useAppSelector((state) => state.vouchering.selectedRegion);
  const selectedAgeBandId = useAppSelector((state) => state.vouchering.selectedAgeBandId);
  const selectedBenefitId = useAppSelector((state) => state.vouchering.selectedBenefitId);
  const selectedSchemeIds = useAppSelector((state) => state.vouchering.selectedSchemeIds);

  const handleAllocationConfirmationModalOpen = () => {
    if (voucheringPayableAmount > allocatedAmount) {
      dispatch(setIsAllocationExceededWarningModalOpen(true));
    } else {
      dispatch(setIsAllocationConfirmationModalOpen(true));
    }
  };

  const handleAllocationConfirmationModalClose = () => {
    dispatch(setIsAllocationConfirmationModalOpen(false));
  };

  const handleVoucheringAllocationModalOpen = () => {
    dispatch(setIsVoucheringAllocationModalOpen(true));
  };

  const handleVoucheringAllocationModalClose = () => {
    dispatch(setIsVoucheringAllocationModalOpen(false));
  };

  const handleAllocationExceededWarningModalOpen = () => {
    dispatch(setIsAllocationExceededWarningModalOpen(true));
  };

  const handleAllocationExceededWarningModalClose = () => {
    dispatch(setIsAllocationExceededWarningModalOpen(false));
  };

  const mainVoucheringCriteriaSelected = mainVoucheringCriteria !== null;
  const payerId: string = UserService.getPayer()?.tokenParsed?.["payerId"];
  const username = UserService.getUsername()?.toString() || "";

  const handleCancelSelection = () => {
    dispatch(setVoucheringCheckedClaimsIds([]));
    dispatch(setVoucheringPayableAmount(0));
    dispatch(setUniqueCheckedClaimsIds([]));
    dispatch(setIsVoucheringSelectAllActive(false));
  };

  const resetAllocation = useResetVoucheringAllocation();

  const handleAllocate = () => {
    if (voucheringPayableAmount > allocatedAmount) {
      handleAllocationExceededWarningModalOpen();
    } else {
      resetAllocation();
      handleVoucheringAllocationModalOpen();
    }
  };

  // interim save
  const [interimSave, { isLoading: isInterimSaving, isSuccess: interimSaveSuccess }] =
    useVoucherVettedClaimsMutation();

  const interimSavePayload: VoucherClaimsPayload = {
    payerId: payerId,
    ...(isValidBatchingCriteria(mainVoucheringCriteria) && {
      batchCriteria: mainVoucheringCriteria,
    }),
    batchStatus: "BATCHED",
    interimSave: true,
    actionedBy: username,
    invoiceIds: uniqueCheckedClaimsIds,
    withVoucher: false,
    onlyProviderWithAccounts: true,
    ...(isValidId(selectedAccount) && { providerAccountIds: [Number(selectedAccount)] }),
    ...(isValidId(selectedBenefitId) && { catalogIds: [selectedBenefitId] }),
    ...(isValidId(selectedAgeBandId) && { ageBandId: selectedAgeBandId }),
    ...(isValidFilterName(selectedRegion) && { region: selectedRegion }),
    ...(selectedSchemeIds.length > 0 && { planIds: selectedSchemeIds }),
  };

  const handleInterimSave = async () => {
    if (voucheringPayableAmount <= allocatedAmount) {
      try {
        await interimSave(interimSavePayload).unwrap();
        dispatch(setCurrentVoucherAmount(voucheringPayableAmount));
        resetAllocation();
        handleCancelSelection();
        clearFilters();
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } catch (error: any) {
        toast.error(error.data.error || "Failed to voucher invoices. Please try again later.");
        handleCancelSelection();
        console.error(error);
      }
    } else {
      dispatch(setIsAllocationExceededWarningModalOpen(true));
    }
  };

  const handleBack = () => {
    dispatch(setMainVoucheringCriteria(null));
    dispatch(setVoucheringProgress(VoucheringProgress.Unallocated));
    dispatch(setIsBackButtonVisible(false));
  };

  useEffect(() => {
    if (interimSaveSuccess) {
      dispatch(setIsInterimSaveSuccessfulModalOpen(true));
    }
  }, [interimSaveSuccess, dispatch]);

  return (
    <MainWrapper className="">
      {uniqueCheckedClaimsIds.length > 0 &&
      voucheringProgress === VoucheringProgress.Unallocated ? (
        <>
          {voucheringProgress === VoucheringProgress.Unallocated && (
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-darkBlue">
                <button onClick={handleCancelSelection}>
                  <img src={cancel} alt="cancel selection" className="w-4" />
                </button>
                <p className="flex items-center gap-1 text-sm">
                  <span>{uniqueCheckedClaimsIds.length}</span> <span>selected</span>
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <InterimSaveButton
                  uniqueCheckedClaimsIds={uniqueCheckedClaimsIds}
                  handleInterimSave={handleInterimSave}
                />
                <VoucherButton
                  uniqueCheckedClaimsIds={uniqueCheckedClaimsIds}
                  handleBatchClick={handleAllocationConfirmationModalOpen}
                />
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="flex items-center space-x-3">
          {isBackButtonVisible && (
            <button className="rounded-[4px] bg-[#E1E8F0] px-2 py-1" onClick={handleBack}>
              <ArrowLeftIcon className="h-5 w-5" />
            </button>
          )}
          <h1 className="text-lg font-medium text-[#111827]">
            {voucheringProgress === VoucheringProgress.Unallocated ? "Claims" : "Vouchers"} List
          </h1>
        </div>
      )}
      <VoucheringProgressBar />
      {mainVoucheringCriteriaSelected && (
        <>
          {voucheringProgress === VoucheringProgress.Unallocated && (
            <section className="">
              <ClaimsVoucheringFilter />
              <section className="flex justify-between space-x-8">
                <div className="min-w-[70%] basis-2/3">
                  <VoucheringVettedClaimsTable />
                </div>
                <div className="basis-1/3">
                  <ClaimSummary
                    isCalculatingVoucherAmount={isCalculatingVoucherAmount}
                    handleAllocate={handleAllocate}
                  />
                </div>
              </section>
            </section>
          )}
        </>
      )}
      {voucheringProgress === VoucheringProgress.InProgress && <InProgressVouchers />}
      {voucheringProgress === VoucheringProgress.Completed && <CompletedVouchers />}
      {!mainVoucheringCriteriaSelected && (
        <>
          {voucheringProgress === VoucheringProgress.Unallocated && (
            <section>
              <div className="px-16 py-10">
                <VoucheringMainCriteriaFilter />
              </div>
              <EmptyState
                illustration={<NoClaimsVouchering />}
                message={{
                  title: "No Claims",
                  description:
                    "Please apply the filters to view all the individual claims before proceeding to the vouchering process....",
                }}
              />
            </section>
          )}
        </>
      )}
      <section>
        <AllocationConfirmationModal
          onClose={handleAllocationConfirmationModalClose}
          isSuccessModalOpen={isAllocationConfirmationModalOpen}
        />
        <ClaimsVoucheringAllocationModal
          onClose={handleVoucheringAllocationModalClose}
          isSuccessModalOpen={isVoucheringAllocationModalOpen}
        />
        <AllocationExceededWarningModal
          onClose={handleAllocationExceededWarningModalClose}
          isSuccessModalOpen={isAllocationExceededWarningModalOpen}
        />
        <ProcessingVouchersModal
          isProcessingVouchersModalOpen={isInterimSaving}
          onClose={() => true}
        />
        <InterimSaveSuccessfulModal
          onClose={() => dispatch(setIsInterimSaveSuccessfulModalOpen(false))}
          isSuccessModalOpen={isInterimSaveSuccessModalOpen}
        />
      </section>
    </MainWrapper>
  );
}
