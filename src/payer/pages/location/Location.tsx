import { useEffect, useState } from "react";

import ModalBasic from "../../components/ModalBasic";
import {
  addCountry,
  addRegion,
  getCountry,
  getRegionsByCountry,
} from "../../store/countries/actions";
import { connect, useSelector } from "react-redux";
import { RootState } from "../../store";

import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { CountryDropdown } from "react-country-region-selector";
import LocationSettingsCard from "../../components/LocationSettingsCard";
import ModalSmall from "../../components/ModalSmall";

function Location({ addCountry, addRegion, getCountry, getRegionsByCountry }) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [selectedItems, setSelectedItems] = useState([]);
  const [locationModalOpen, setLocationModalOpen] = useState(false);
  const [regionModalOpen, setRegionModalOpen] = useState(false);
  const [countryName, setCountryName] = useState("");
  const [regionName, setRegionName] = useState("");
  const [countrySelected, setCountrySelected] = useState("");
  const [countryClicked, setCountryClicked] = useState("");
  const [countrySubmitted, setCountrySubmitted] = useState(false);
  const [regionSubmitted, setRegionSubmitted] = useState(false);

  const handleSelectedItems = (selectedItems) => {
    setSelectedItems([...selectedItems]);
  };
  const countries = useSelector((state: RootState) => state.location.countries);
  const regions = useSelector((state: RootState) => state.location.regions);
  console.log(regions);

  const success = useSelector((state: RootState) => state.location.success);

  const handleCountryChange = (e) => {
    setCountrySelected(e.target.value);
  };
  const handleBtnClick = (e) => {
    if (e == "country") {
      setLocationModalOpen(true);
    } else if (e == "region") {
      setRegionModalOpen(true);
    }
  };
  const showRegionsInCountry = (e, country) => {
    e.stopPropagation();
    setCountryClicked(country.name);
    getRegionsByCountry(country.id);
  };

  useEffect(() => {
    getCountry();
  }, []);

  useEffect(() => {
    if (countrySubmitted)
      if (success) {
        toast.success("Country Added Successfully!", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        setCountrySubmitted(false);
      } else {
        toast.error("An Error Occurred!", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        setCountrySubmitted(false);
      }
  }, [countrySubmitted]);

  useEffect(() => {
    if (regionSubmitted)
      if (success) {
        toast.success("Region Added Successfully!", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        setRegionSubmitted(false);
      } else {
        toast.error("An Error Occurred!", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        setRegionSubmitted(false);
      }
  }, [regionSubmitted]);

  const handleSubmit = (event, param) => {
    if (param == "country") {
      event.preventDefault();

      if (!countryName) {
        toast.error("Country is Required!", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        return;
      }

      addCountry({
        name: countryName,
      })
        .then(() => {
          setLocationModalOpen(false);
          setCountrySubmitted(true);
          ///
        })
        .then(() => {
          getCountry();
        });
    } else if (param == "region") {
      event.preventDefault();
      if (!countrySelected || !regionName) {
        toast.error("Country & Region are  Required!", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        return;
      }

      addRegion({
        countryId: countrySelected,
        name: regionName,
      }).then(() => {
        setRegionModalOpen(false);
        setRegionSubmitted(true);
      });
    }
  };
  const items = [
    {
      id: 0,
      name: "Add Country",

      link: "country",

      content: "Add a new Country with regions for operations.",
      message: "Add New Country",
    },
    {
      id: 1,
      name: "Add Region",

      link: "region",
      content: "Add a new Region to an existing country",
      message: "Add New Region",
    },
  ];

  return (
    <div className="flex h-full overflow-hidden">
      <div className="relative flex flex-1 flex-col overflow-y-auto overflow-x-hidden">
        <main className="mx-4 mb-4 lg:mx-8 lg:mb-8">
          <div className="mx-auto w-full max-w-9xl rounded-md px-4 shadow-lg sm:px-6 lg:px-8">
            {/* Page header */}
            <div className="mb-8 sm:flex sm:items-center sm:justify-between">
              {/* Left: Title */}
              <div className="mb-4 sm:mb-0">
                <h1 className="md:text-md mr-6 text-sm font-bold text-gray-800 lg:text-lg">
                  Location Settings
                </h1>
              </div>

              {/* Right: Actions */}
              <div className="grid grid-flow-col justify-start gap-2 sm:auto-cols-max sm:justify-end">
                {/* Add customer button */}
                {/* <button
                  className="btn bg-blue-500 hover:bg-blue-500 text-white"
                  onClick={(e) => {
                    e.stopPropagation();
                    setLocationModalOpen(true);
                  }}
                >
                  <svg
                    className="w-4 h-4 fill-current shrink-0"
                    viewBox="0 0 16 16"
                  >
                    <path d="M15 7H9V1c0-.6-.4-1-1-1S7 .4 7 1v6H1c-.6 0-1 .4-1 1s.4 1 1 1h6v6c0 .6.4 1 1 1s1-.4 1-1V9h6c.6 0 1-.4 1-1s-.4-1-1-1z" />
                  </svg>
                  <span className="hidden xs:block ml-2">Add Country</span>
                </button> */}
                {/* Add customer button */}
                {/* <button
                  className="btn bg-blue-500 hover:bg-blue-500 text-white"
                  onClick={(e) => {
                    e.stopPropagation();
                    setRegionModalOpen(true);
                  }}
                >
                  <svg
                    className="w-4 h-4 fill-current shrink-0"
                    viewBox="0 0 16 16"
                  >
                    <path d="M15 7H9V1c0-.6-.4-1-1-1S7 .4 7 1v6H1c-.6 0-1 .4-1 1s.4 1 1 1h6v6c0 .6.4 1 1 1s1-.4 1-1V9h6c.6 0 1-.4 1-1s-.4-1-1-1z" />
                  </svg>
                  <span className="hidden xs:block ml-2">Add Region</span>
                </button> */}
              </div>
            </div>
            {/* content */}
            <div>
              <div className="grid grid-cols-12 gap-6">
                {items.map((item) => {
                  return (
                    <LocationSettingsCard
                      key={item.id}
                      id={item.id}
                      name={item.name}
                      link={item.link}
                      content={item.content}
                      message={item.message}
                      handleClick={handleBtnClick}
                    />
                  );
                })}
              </div>
              <div>
                <div className="m-10">
                  <h2 className="md:text-md mr-6 text-sm font-bold text-gray-800 lg:text-lg">
                    Countries
                  </h2>
                  <div className="place-content-center px-10 py-4">
                    {countries.map((country) => (
                      <span className="mr-5 " onClick={(e) => showRegionsInCountry(e, country)}>
                        {/* Start */}
                        <h3 className="inline-block cursor-pointer rounded-full border border-gray-100 bg-yellow-100 px-2.5 py-2 text-center text-sm font-medium text-yellow-600  shadow hover:bg-yellow-50">
                          {country.name}
                        </h3>
                        {/* End */}
                      </span>
                    ))}
                  </div>
                  <div className="px-10">
                    <h2 className="lg:text-md mr-6 text-sm font-bold  md:text-base">
                      {countryClicked ? ` Regions in ${countryClicked}` : ""}
                    </h2>
                    {countryClicked ? (
                      regions.length > 0 ? (
                        regions.map((region) => (
                          <h3 className="mb-5 mt-5 inline-block rounded-full border border-gray-100 bg-yellow-100 px-2.5 py-2 text-center text-sm font-medium text-yellow-600 shadow hover:bg-yellow-50">
                            {region.name}
                          </h3>
                        ))
                      ) : (
                        <h3 className="mb-5 mt-5 inline-block px-2.5 py-2 text-center text-sm font-medium text-yellow-600 hover:bg-yellow-50">
                          {`None`}
                        </h3>
                      )
                    ) : (
                      ""
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Modal */}
            <ModalSmall
              id="feedback-modal"
              modalOpen={locationModalOpen}
              setModalOpen={setLocationModalOpen}
              title="Add Country Location"
            >
              {/* Modal content */}

              <div className="px-10 pb-1 pt-4">
                <h3 className="mb-5 text-lg text-gray-400">Add Country</h3>

                <div className="text-sm">
                  {/* Options */}
                  <div className="flex justify-center">
                    <div className="mr-9 w-64">
                      <label className="mb-1 block text-sm font-medium" htmlFor="name">
                        Country Name :
                        <CountryDropdown
                          value={countryName}
                          onChange={(val) => setCountryName(val)}
                        />
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              {/* Modal footer */}
              <div className="mt-5 px-5 py-4">
                <div className="flex flex-wrap justify-center space-x-2">
                  <button
                    className="btn-sm w-44 border-gray-200 px-16 py-2 text-gray-600 hover:border-gray-300"
                    onClick={(e) => {
                      e.stopPropagation();
                      setLocationModalOpen(false);
                    }}
                  >
                    Clear
                  </button>
                  <button
                    type="submit"
                    className="btn-sm w-44 bg-blue-500 px-16 py-2 text-white hover:bg-blue-600"
                    onClick={(e) => {
                      handleSubmit(e, "country");
                    }}
                  >
                    Save
                  </button>
                </div>
              </div>
            </ModalSmall>
            {/* Modal */}
            <ModalBasic
              id="feedback-modal"
              modalOpen={regionModalOpen}
              setModalOpen={setRegionModalOpen}
              title="Add Region"
            >
              {/* Modal content */}

              <div className="px-10 pb-1 pt-4">
                <h3 className="mb-5 text-lg text-gray-400">Add Region</h3>

                <div className="text-sm">
                  {/* Options */}
                  <div className="flex justify-center">
                    <div className="mr-9 w-64 ">
                      <label className="mb-1 block text-sm font-medium" htmlFor="name">
                        Select Country
                      </label>

                      <select onChange={handleCountryChange} name="country">
                        <option value="Select Country" selected disabled>
                          Select Country
                        </option>
                        {countries.map((country) => (
                          <option value={country.id}>{country.name}</option>
                        ))}
                      </select>
                    </div>

                    <div className="mr-9 w-64">
                      <label className="mb-1 block text-sm font-medium" htmlFor="name">
                        Region Name
                      </label>
                      <input
                        id="name"
                        className="form-input w-full"
                        type="text"
                        placeholder="Region Name"
                        value={regionName}
                        onChange={(e) => setRegionName(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </div>
              {/* Modal footer */}
              <div className="mt-5 px-5 py-4">
                <div className="flex flex-wrap justify-center space-x-2">
                  <button
                    className="btn-sm w-44 border-gray-200 px-16 py-2 text-gray-600 hover:border-gray-300"
                    onClick={(e) => {
                      e.stopPropagation();
                      setRegionModalOpen(false);
                    }}
                  >
                    Clear
                  </button>
                  <button
                    type="submit"
                    className="btn-sm w-44 bg-blue-500 px-16 py-2 text-white hover:bg-blue-600"
                    onClick={(e) => {
                      handleSubmit(e, "region");
                    }}
                  >
                    Save
                  </button>
                </div>
              </div>
            </ModalBasic>
          </div>
        </main>
      </div>
    </div>
  );
}

const mapDispatchToProps = (dispatch) => ({
  addCountry: (country) => dispatch(addCountry(country)),
  addRegion: (region) => dispatch(addRegion(region)),
  getCountry: () => dispatch(getCountry()),
  getRegionsByCountry: (countryId) => dispatch(getRegionsByCountry(countryId)),
});
export default connect(null, mapDispatchToProps)(Location);
