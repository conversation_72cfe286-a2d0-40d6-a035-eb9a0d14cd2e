import FilterLinesIcon from "../../components/icons/FilterLinesIcon";
import NoClaimsVouchering from "../../components/illustrations/NoClaimsVouchering";
import SignOffClaimsTable from "../../components/signoff/SignOffClaimsTable";
import SignOffFilters from "../../components/signoff/SignOffFilters";
import SignOffSummary from "../../components/signoff/SignOffSummary";
import Button from "../../components/ui/Button";
import EmptyState from "../../components/ui/EmptyState";
import MainWrapper from "../../components/ui/MainWrapper";
import Text from "../../components/ui/typography/Text";
import { setIsFiltersViewOpen } from "../../features/finance/signoff/signOffSlice";
import { useAppDispatch, useAppSelector } from "../../store/hooks";

export default function SignOff() {
  const isFiltersViewOpen = useAppSelector((state) => state.signOff.isFiltersViewOpen);
  const selectedSignOffAccount = useAppSelector((state) => state.signOff.selectedSignOffAccount);
  const fromDate = useAppSelector((state) => state.signOff.fromDate);
  const toDate = useAppSelector((state) => state.signOff.toDate);
  const dispatch = useAppDispatch();

  const handleFiltersViewOpen = () => {
    dispatch(setIsFiltersViewOpen(!isFiltersViewOpen));
  };

  const isMainFiltersSelected =
    Boolean(selectedSignOffAccount) && Boolean(fromDate) && Boolean(toDate);

  return (
    <MainWrapper className="flex flex-col">
      <section className="flex items-center justify-between">
        <Text variant="heading" className="text-lg">
          Claims Signoff Process
        </Text>
        <Button
          onClick={handleFiltersViewOpen}
          className={`${isFiltersViewOpen && "bg-lightBlue"}`}
        >
          <FilterLinesIcon />
          Filters
        </Button>
      </section>

      {isFiltersViewOpen && <SignOffFilters />}

      {isMainFiltersSelected ? (
        <div className="flex w-full space-x-12 pt-5">
          <div className="min-w-[70%] basis-2/3">
            <SignOffClaimsTable />
          </div>
          <div className="basis-1/3">
            <SignOffSummary />
          </div>
        </div>
      ) : (
        <EmptyState
          illustration={<NoClaimsVouchering />}
          message={{
            title: "Sign Off Claims",
            description:
              "Review and finalize the claims ready for payment processing. Once signed off, the claims will be locked and prepared for the next steps. Ensure all details are correct before proceeding.",
          }}
        />
      )}
    </MainWrapper>
  );
}
