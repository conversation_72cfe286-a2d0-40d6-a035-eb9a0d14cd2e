import React, { useState, useEffect } from "react";
import { connect, useSelector } from "react-redux";

import ModalBasic from "../../components/ModalBasic";
import PoliciesOverviewTable from "../../pages/partials/pageItems/PoliciesOverviewTable";
import PolicyCategoriesTable from "../../pages/partials/pageItems/PolicyCategoriesTable";
import {
  editPolicy,
  getPolicyCategories,
  getPolicyOverview,
  postCategories,
  uploadMembersToPolicy,
} from "../../store/policies/actions";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { RootState } from "../../store";
import * as notifications from "../../lib/notifications.js";
import { TailSpin } from "react-loader-spinner";

const PolicyOverview = ({
  getPolicyOverview,
  getPolicyCategories,
  postCategories,
  success,
  message,
  uploadMembersToPolicy,
}) => {
  const [payerEditModalOpen, setEditPayerModalOpen] = useState(false);
  const [uploadMembersModalOpen, setUploadMembersModalOpen] = useState(false);
  const [categoryModalOpen, setupCategoryModalOpen] = useState(false);
  const [fileSelected, setFileSelected] = useState("");

  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [openTab, setOpenTab] = React.useState(1);
  const [color, setColor] = React.useState("blue");
  const [selectedItems, setSelectedItems] = useState([]);
  const [payerModalOpen, setPayerModalOpen] = useState(false);
  const [catSubmit, setCatSubmit] = useState(false);
  const [memberUpload, setMemberUpload] = useState(false);
  const [policyCategory, setPolicyCategory] = useState("");
  const [policyDescription, setPolicyDescription] = useState("");
  const [refreshKey, setRefreshKey] = useState(0);

  let policyId = "";
  if (window.location.pathname.toString().includes("addCategory")) {
    policyId = window.location.pathname.split(
      "policies/overview/addCategory/"
    )[1];
  } else {
    policyId = window.location.pathname.split("/policies/overview/")[1];
  }

  console.log(policyId);

  const policyItem = useSelector((state: RootState) => state.policy.policyItem);

  const selectedPolicy = useSelector(
    (state: RootState) => state.policy.selectedPolicy
  );

  const policyCategories = useSelector(
    (state: RootState) => state.policy.policyCategories
  );

  const loadingUpload = useSelector(
    (state: RootState) => state.policy.loadingUpload
  );

  const uploadSuccess = useSelector(
    (state: RootState) => state.policy.successUpload
  );

  const handleSelectedItems = (selectedItems) => {
    setSelectedItems([...selectedItems]);
  };
  const taskId = useSelector((state: RootState) => state.dashboard.taskId);

  if (catSubmit) {
    if (message.success) {
      toast.success("Success", {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      setCatSubmit(false);
      setupCategoryModalOpen(false);
      setRefreshKey((oldKey) => oldKey + 1);
    } else if (message.success === false) {
      toast.error("A problem Occurred", {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      setCatSubmit(false);
      setupCategoryModalOpen(false);
    }
  }
  ///const policy = useSelector((state: RootState) => state.policy);
  const catArray = [];

  useEffect(() => {
    if (window.location.pathname.includes("policies/overview/addCategory/")) {
      getPolicyOverview(policyId);
      getPolicyCategories(policyId)
        .then(() => {
          setRefreshKey((oldKey) => oldKey + 1);
        })
        .then(() => {
          setupCategoryModalOpen(true);
        });
    } else {
      getPolicyOverview(policyId);
      getPolicyCategories(policyId).then(() => {
        setRefreshKey((oldKey) => oldKey + 1);
      });
    }
    // setFormData(currentTrip);
  }, []);

  const handlePolicyCategorySubmit = (event) => {
    event.preventDefault();
    console.log(event);

    if (!policyCategory && !policyDescription) {
      toast.error("Please fill in all category setup fields", {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      return;
    }
    catArray.push({ name: policyCategory, description: policyDescription });

    const categoryObject = {
      policyId: policyId,
      categories: catArray,
      taskId,
    };
    postCategories(categoryObject).then(() => {
      setCatSubmit(true);
      getPolicyCategories(policyId).then(() => {
        setRefreshKey((oldKey) => oldKey + 1);
      });
    });
  };

  const handleMemberUpload = (event) => {
    event.preventDefault();
    console.log(fileSelected);
    setMemberUpload(true);
    if (!fileSelected) {
      toast.error("Please select a file to upload", {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
    } else {
      uploadMembersToPolicy(fileSelected, policyId);
    }
  };

  useEffect(() => {
    if (uploadSuccess && memberUpload) {
      setUploadMembersModalOpen(false);
      notifications.Success({
        title: " Members Uploaded Successfully",
      });
      setMemberUpload(false);
    }
  }, [uploadSuccess]);

  const onChangeMemberFile = (e) => {
    console.log(e.target.files[0]);
    setFileSelected(e.target.files[0]);
  };

  const refresh = () => {
    getPolicyCategories(policyId).then(() => {
      setRefreshKey((oldKey) => oldKey + 1);
    });
  };
  useEffect(() => {
    setRefreshKey((oldKey) => oldKey + 1);
  }, [policyCategories]);

  return (
    <div className="flex h-full overflow-hidden">
      {/* Content area */}
      <div className="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
        <main className="mx-4 lg:mx-8 mb-4 lg:mb-8">
          <div className="px-4 sm:px-6 lg:px-8 pb-5 w-full max-w-9xl mx-auto rounded-md shadow-lg mb-10">
            {/* Page header */}
            <div className="sm:flex sm:justify-between sm:items-center ">
              {/* Left: Title */}
              <div className="mb-2 sm:mb-0">
                <h3 className="text-sm md:text-base lg:text-lg text-gray-800 font-bold">
                  Policy No.{policyItem.policyNumber}
                </h3>
                <h5 className="text-sm md:text-base lg:text-lg text-gray-800 font-bold mt-3">
                  Policy Overview
                </h5>
              </div>
            </div>

            {/* More actions */}
            <div className="sm:flex sm:justify-between sm:items-center"></div>
            {/* Modal */}
            <ModalBasic
              id="feedback-modal"
              modalOpen={payerModalOpen}
              setModalOpen={setPayerModalOpen}
              title="Add Payer"
            >
              {/* Modal content */}
              <div className="px-10 pt-4 pb-1">
                <h3 className="text-gray-400 mb-5 text-lg">PAYER DETAILS</h3>

                <div className="text-sm">
                  {/* Options */}
                  <div className="flex">
                    <div className="w-64 mr-9">
                      <label
                        className="block text-sm font-medium mb-1"
                        htmlFor="name"
                      >
                        Payer Name
                      </label>
                      <input
                        id="name"
                        className="form-input w-full"
                        type="text"
                        placeholder="Payer Name"
                      />
                    </div>
                    <div className="w-64 mr-9">
                      <label
                        className="block text-sm font-medium mb-1"
                        htmlFor="email"
                      >
                        Email Address
                      </label>
                      <input
                        id="email"
                        className="form-input w-full "
                        type="email"
                        placeholder="Email Address"
                      />
                    </div>
                    <div className="w-64 mr-9">
                      <label
                        className="block text-sm font-medium mb-1"
                        htmlFor="name"
                      >
                        Mobile
                      </label>
                      <input
                        id="name"
                        className="form-input w-full"
                        type="phone"
                        placeholder="Mobile"
                      />
                    </div>
                  </div>
                  <div className="mt-10">
                    <h3 className="text-gray-400 mb-5 text-lg">PAYER TYPE</h3>
                  </div>

                  <div className="flex mt-5">
                    <div className="flex  mr-9 ">
                      <div className="w-48  mr-5">
                        <input
                          type="radio"
                          name="option"
                          id="option1"
                          className="hidden "
                        />
                        <label
                          htmlFor="option1"
                          className="label-checked:text-gray-100 label-background:bg-blue-900 border-2 px-16 py-3 rounded-md"
                        >
                          Underwriter
                        </label>
                      </div>
                      <div className="w-48 mr-5">
                        <input
                          type="radio"
                          name="option"
                          id="option2"
                          className="hidden"
                        />
                        <label
                          htmlFor="option2"
                          className="label-checked:text-gray-100 label-background:bg-blue-900 border-2  px-16 py-3 rounded-md"
                        >
                          Corporate
                        </label>
                      </div>
                      <div className="w-48 mr-5">
                        <input
                          type="radio"
                          name="option"
                          id="option3"
                          className="hidden"
                        />
                        <label
                          htmlFor="option3"
                          className="label-checked:text-gray-100 label-background:bg-blue-900 border-2  px-16 py-3 rounded-md"
                        >
                          Intermediary
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {/* Modal footer */}
              <div className="px-5 py-4 mt-5">
                <div className="flex flex-wrap justify-center space-x-2">
                  <button
                    className="btn-sm border-gray-200 hover:border-gray-300 text-gray-600 w-44 px-16 py-2"
                    onClick={(e) => {
                      e.stopPropagation();
                      setPayerModalOpen(false);
                    }}
                  >
                    Clear
                  </button>
                  <button className="btn-sm bg-blue-500 hover:bg-blue-600 text-white w-44 px-16 py-2">
                    Save
                  </button>
                </div>
              </div>
            </ModalBasic>

            {/* Table */}
            <PoliciesOverviewTable />
            {/* buttons
             */}
            <div className="sm:flex sm:items-center sm:justify-center sm:w-6/12">
              {/* <button
                className="btn mt-3 ml-2 w-full bg-blue-700 border-gray-200 hover:border-gray-300 text-gray-600"
                onClick={(e) => {
                  e.stopPropagation();
                  setupCategoryModalOpen(true);
                }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="#ffffff"
                  strokeWidth="2"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <span className="ml-2 text-white ">Setup Category</span>
              </button> */}
              {/* <button
                className="btn mt-3 ml-2 w-full bg-blue-700 border-gray-200 hover:border-gray-300 "
                onClick={(e) => {
                  e.stopPropagation();
                  setUploadMembersModalOpen(true);
                }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="#ffffff"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"
                  />
                </svg>
                <span className="ml-2 text-white">Upload Members</span>
              </button> */}
            </div>

            <h3 className="text-sm md:text-base text-gray-800 font-bold mt-5">
              Policy Categories
            </h3>

            <div className="mt-0">
              <PolicyCategoriesTable
                selectedItems={handleSelectedItems}
                policy={selectedPolicy}
                policyCategories={policyCategories}
                refresh={refresh}
                key={refreshKey}
              />
            </div>
            <div>
              {/* Pagination */}
              <div className="mt-8">{/* <Pagination /> */}</div>
            </div>
            <div className="space-x-1">
              <div>
                <ModalBasic
                  id="feedback-modal"
                  modalOpen={payerEditModalOpen}
                  setModalOpen={setEditPayerModalOpen}
                  title="Edit Payer"
                >
                  {/* Modal content */}
                  <div className="px-10 pt-4 pb-1">
                    <h3 className="text-gray-400 mb-5 text-lg">
                      PAYER DETAILS
                    </h3>

                    <div className="text-sm">
                      {/* Options */}
                      <div className="flex">
                        <div className="w-64 mr-9">
                          <label
                            className="block text-sm font-medium mb-1"
                            htmlFor="name"
                          >
                            Payer Name
                          </label>
                          <input
                            id="name"
                            className="form-input w-full"
                            type="text"
                            placeholder="Payer Name"
                          />
                        </div>
                        <div className="w-64 mr-9">
                          <label
                            className="block text-sm font-medium mb-1"
                            htmlFor="email"
                          >
                            Email Address
                          </label>
                          <input
                            id="email"
                            className="form-input w-full "
                            type="email"
                            placeholder="Email Address"
                          />
                        </div>
                        <div className="w-64 mr-9">
                          <label
                            className="block text-sm font-medium mb-1"
                            htmlFor="name"
                          >
                            Mobile
                          </label>
                          <input
                            id="name"
                            className="form-input w-full"
                            type="phone"
                            placeholder="Mobile"
                          />
                        </div>
                      </div>
                      <div className="mt-10">
                        <h3 className="text-gray-400 mb-5 text-lg">
                          PAYER TYPE
                        </h3>
                      </div>

                      <div className="flex mt-5">
                        <div className="flex  mr-9 ">
                          <div className="w-48  mr-5">
                            <input
                              type="radio"
                              name="optionEdit"
                              id="option11"
                              className="hidden p-20"
                            />
                            <label
                              htmlFor="option11"
                              className="inline-block cursor-pointer  p-5 label-checked:text-gray-100 label-background:bg-blue-900 border-2 px-16 py-3 rounded-md"
                            >
                              Underwriter
                            </label>
                          </div>
                          <div className="w-48 mr-5">
                            <input
                              type="radio"
                              name="optionEdit"
                              id="option22"
                              className="hidden"
                            />
                            <label
                              htmlFor="option22"
                              className="inline-block cursor-pointer label-checked:text-gray-100 label-background:bg-blue-900 border-2  px-16 py-3 rounded-md"
                            >
                              Corporate
                            </label>
                          </div>
                          <div className="w-48 mr-5">
                            <input
                              type="radio"
                              name="optionEdit"
                              id="option33"
                              className="hidden"
                            />
                            <label
                              htmlFor="option33"
                              className="inline-block cursor-pointer label-checked:text-gray-100 label-background:bg-blue-900 border-2  px-16 py-3 rounded-md"
                            >
                              Intermediary
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* Modal footer */}
                  <div className="px-5 py-4 mt-5">
                    <div className="flex flex-wrap justify-center space-x-2">
                      <button
                        className="btn-sm border-gray-200 hover:border-gray-300 text-gray-600 w-44 px-16 py-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          setEditPayerModalOpen(false);
                        }}
                      >
                        Clear
                      </button>
                      <button className="btn-sm bg-blue-500 hover:bg-blue-600 text-white w-44 px-16 py-2">
                        Save
                      </button>
                    </div>
                  </div>
                </ModalBasic>
              </div>
            </div>

            {/* Member Upload modal */}
            <div className="space-x-1">
              <div>
                <ModalBasic
                  id="feedback-modal"
                  modalOpen={uploadMembersModalOpen}
                  setModalOpen={setUploadMembersModalOpen}
                  title="Member Upload"
                >
                  {/* Modal content */}
                  <div className="px-10 pt-4 pb-1 justify-center">
                    <h3 className="text-gray-400 mb-5 ml-28 text-lg uppercase">
                      Member Upload
                    </h3>

                    <div className="text-sm">
                      {/* Options */}
                      <div className="flex justify-center">
                        <div className="w-64 mr-9">
                          <input
                            type="file"
                            name="memberFile"
                            onChange={(e) => onChangeMemberFile(e)}
                          />
                        </div>
                        {loadingUpload ? (
                          <TailSpin
                            height="30"
                            width="50"
                            color="#2193FF"
                            ariaLabel="loading"
                          />
                        ) : (
                          ""
                        )}
                      </div>
                    </div>
                  </div>
                  {/* Modal footer */}
                  <div className=" mt-10 px-16 py-2">
                    <div className="flex flex-wrap justify-center space-x-2">
                      {loadingUpload ? (
                        <button
                          className="btn bg-gray-500 hover:bg-gray-600 text-white disabled:border-gray-200 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed shadow-none"
                          disabled
                          onClick={(e) => {
                            e.stopPropagation();
                            handleMemberUpload(e);
                          }}
                        >
                          Uploading Members
                        </button>
                      ) : (
                        <button
                          className="btn bg-blue-500 hover:bg-blue-600 text-white "
                          onClick={(e) => {
                            e.stopPropagation();
                            handleMemberUpload(e);
                          }}
                        >
                          Upload Members
                        </button>
                      )}
                    </div>
                  </div>
                </ModalBasic>
              </div>
            </div>

            {/* category mapping modal */}
            <div className="">
              <div>
                <ModalBasic
                  id="feedback-modal"
                  modalOpen={categoryModalOpen}
                  setModalOpen={setupCategoryModalOpen}
                  title="Policy Setup"
                >
                  <div className="sm:items-center sm:justify-center sm:mx-auto sm:px-20">
                    {/* Modal content */}
                    <div className="px-10 pt-4 pb-1 sm:items-center sm:justify-center sm:mx-auto">
                      <h3 className="text-gray-400 mb-5 text-sm md:text-base lg:text-lg  uppercase">
                        category setup
                      </h3>

                      {/* Options */}
                      <div className="max-w-2xl">
                        <label
                          className="block text-sm font-medium mb-1"
                          htmlFor="name"
                        >
                          Input Category
                        </label>
                        <input
                          id="policyCategory"
                          className="form-input w-full"
                          type="text"
                          placeholder="Input Category"
                          value={policyCategory}
                          onChange={(e) => setPolicyCategory(e.target.value)}
                        />
                      </div>
                      <div className="max-w-2xl mt-10">
                        <label
                          className="block text-sm font-medium mb-1"
                          htmlFor="country"
                        >
                          Description
                        </label>
                        <textarea
                          className="form-input w-full rounded-md resize-none"
                          placeholder="Description"
                          value={policyDescription}
                          onChange={(e) => setPolicyDescription(e.target.value)}
                        ></textarea>
                      </div>
                    </div>
                    {/* Modal footer */}
                    <div className="px-5 py-4 mt-10">
                      <div className="flex flex-wrap justify-center space-x-2">
                        <button
                          className="btn-sm border-gray-200 hover:border-gray-300 text-gray-600 w-44 px-16 py-2"
                          onClick={(e) => {
                            setupCategoryModalOpen(false);
                          }}
                        >
                          Clear
                        </button>
                        <button
                          type="submit"
                          onClick={(e) => handlePolicyCategorySubmit(e)}
                          className="btn-sm bg-blue-500 hover:bg-blue-600 text-white w-44 px-16 py-2"
                        >
                          Save
                        </button>
                      </div>
                    </div>
                  </div>
                </ModalBasic>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

const mapStateToProps = (state) => ({
  policies: state.policy.policies,
  schemes: state.schemes.schemes,
  success: state.policy.success,
  message: state.policy,
  loading: state.policy.loading,
});
const mapDispatchToProps = (dispatch) => ({
  editPolicy: (policyObject) => dispatch(editPolicy(policyObject)),
  getPolicyOverview: (policyId) => dispatch(getPolicyOverview(policyId)),
  getPolicyCategories: (policyId) => dispatch(getPolicyCategories(policyId)),
  uploadMembersToPolicy: (fileSelected, policyId) =>
    dispatch(uploadMembersToPolicy(fileSelected, policyId)),
  postCategories: (categoryObject) => dispatch(postCategories(categoryObject)),
});
export default connect(mapStateToProps, mapDispatchToProps)(PolicyOverview);
