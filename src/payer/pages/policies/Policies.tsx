import { useState, useEffect } from "react";
import { connect, useSelector } from "react-redux";

import PoliciesTable from "../../pages/partials/pageItems/PoliciesTable";
import ModalBasic from "../../components/ModalBasic";

import Flatpickr from "react-flatpickr";
import { addPolicy, getPolicies } from "../../store/policies/actions";
import { getSchemes } from "../../store/schemes/actions";

import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import makeAnimated from "react-select/animated";
import { RootState } from "../../store";
import { TailSpin } from "react-loader-spinner";
import * as notifications from "../../lib/notifications.js";
import { AsyncPaginate } from "react-select-async-paginate";
import { loadschemeOptions } from "../../lib/loadOptions";

const animatedComponents = makeAnimated();

const Policies = ({ addPolicy, getSchemes, getPolicies, schemes }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [selectedItems, setSelectedItems] = useState([]);
  const [policyNumber, setPolicyNumber] = useState("");
  const [endDate, setEndDate] = useState(new Date("dd/mm/yy"));
  const [startDate, setStartDate] = useState(new Date("dd/mm/yy"));
  const [addPolicyModalOpen, setAddPolicyModalOpen] = useState(false);

  const [scheme, setScheme] = useState(false);
  const [schemeValue, setSchemeValue] = useState({});
  const [refreshKey, setRefreshKey] = useState(0);
  const [policySubmitted, setPolicySubmitted] = useState(false);

  const handleSelectedItems = (selectedItems) => {
    setSelectedItems([...selectedItems]);
  };
  const saveNavigateToCoverPage = (event) => {
    event.preventDefault();
    handleAddPolicy();
  };

  const handleAddPolicy = () => {
    if (!schemeValue || !startDate || !endDate || !policyNumber) {
      toast.error("All Form Fields are Required!", {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      return;
    }

    const policyObject = {
      planId: schemeValue,
      startDate,
      endDate,
      policyNumber,
      taskId,
    };
    addPolicy(policyObject).then(() => {
      setAddPolicyModalOpen(false);
      setPolicySubmitted(true);

      getPolicies();
    });
  };
  const success = useSelector((state: RootState) => state.policy.success);
  const policies = useSelector((state: RootState) => state.policy.policies);
  const msg = useSelector((state: RootState) => state.policy.msg);
  const taskId = useSelector((state: RootState) => state.dashboard.taskId);
  const loadingPolicies = useSelector((state: RootState) => state.policy.loading);

  useEffect(() => {
    if (policySubmitted) {
      if (success) {
        setPolicySubmitted(false);
        toast.success("Policy Added Successfully!", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
      } else {
        setPolicySubmitted(false);
        toast.error("Error Occurred!", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        notifications.Error({
          title: msg,
        });
      }
    }
  }, [policySubmitted]);

  useEffect(() => {
    if (window.location.pathname === "/policies/addPolicy/") {
      getPolicies()
        .then(() => {
          getSchemes();
        })
        .then(() => {
          handleAddPolicyClick();
        });
    } else {
      getPolicies().then(() => {
        getSchemes();
      });
    }
  }, []);

  const data = [];
  if (schemes != null) {
    for (let i = 0; i < schemes.length; i++) {
      data.push({
        label: schemes[i].name,
        value: schemes[i].id,
      });
    }
  }

  const changeSchemeName = (e) => {
    if (e) {
      setScheme(e);
      console.log(e);
      console.log(e["value"]);
      setSchemeValue(e["value"]);
    } else {
      setScheme(e);
    }
  };

  const options = {
    mode: "single",
    static: true,
    monthSelectorType: "static",
    dateFormat: "Y-m-d",
    defaultDate: [new Date("Y-m-d")],
    enableTime: false,
    prevArrow:
      '<svg class="fill-current" width="7" height="11" viewBox="0 0 7 11"><path d="M5.4 10.8l1.4-1.4-4-4 4-4L5.4 0 0 5.4z" /></svg>',
    nextArrow:
      '<svg class="fill-current" width="7" height="11" viewBox="0 0 7 11"><path d="M1.4 10.8L0 9.4l4-4-4-4L1.4 0l5.4 5.4z" /></svg>',
  };
  const handleAddPolicyClick = () => {
    setAddPolicyModalOpen(true);
  };

  return (
    <div className="flex h-full overflow-hidden">
      {/* Content area */}
      <div className="relative flex flex-1 flex-col overflow-y-auto overflow-x-hidden pb-5">
        <main className="ml-10 mr-10 pb-5">
          <div className="mx-auto w-full max-w-9xl rounded-md px-4 pb-5 shadow-lg sm:px-6 lg:px-8">
            {/* Page header */}
            <div className="mb-8 sm:flex sm:justify-between">
              {/* Left: Title */}
              <div className="mb-4 sm:mb-0">
                <h1 className="text-sm font-bold text-gray-800 md:text-base lg:text-lg">
                  Policies
                </h1>
                <div className="flex">
                  <h3 className="text-sm font-normal text-gray-400 md:text-base lg:text-lg">
                    Add a Policy , upload a member and setup a policy category
                  </h3>
                </div>
              </div>

              {/* Right: Actions */}
              <div className=" mt-3 sm:h-10">
                {/* Add customer button */}
                <button
                  className="btn mr-3 bg-blue-500 text-white hover:bg-blue-600"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAddPolicyClick();
                  }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  <span className="xs:block ml-2 hidden">Add Policy</span>
                </button>
                {/* Add customer button */}
              </div>
            </div>
            {/* Modal */}
            <ModalBasic
              id="addPolicy-modal"
              modalOpen={addPolicyModalOpen}
              setModalOpen={setAddPolicyModalOpen}
              title="Add Policy"
            >
              {/* Modal content */}
              <div className="sm:h-full sm:w-full sm:items-center sm:justify-center sm:px-14">
                <h3 className="text-sm  text-gray-400 md:text-base lg:text-base">POLICY DETAILS</h3>

                <div className="">
                  {/* Options */}
                  <div className=" sm:flex sm:items-center sm:justify-center">
                    <div className="mr-5  mt-10 w-full">
                      <label
                        className="mb-1 block text-sm font-medium md:text-base lg:text-base"
                        htmlFor="schemename"
                      >
                        Select Scheme Name
                      </label>

                      <AsyncPaginate
                        className="react-select"
                        classNamePrefix="react-select"
                        name="schemename"
                        value={scheme}
                        loadOptions={loadschemeOptions}
                        onChange={(value) => changeSchemeName(value)}
                        placeholder="Select Scheme Name"
                        isClearable={true}
                      />
                      {/* <Select
                        className="react-select"
                        classNamePrefix="react-select"
                        name="schemename"
                        defaultValue={data}
                        options={data}
                        value={scheme}
                        onChange={(value) => changeSchemeName(value)}
                        placeholder="Select Scheme Name"
                        closeMenuOnSelect={true}
                        isClearable={true}
                        isSearchable={true}
                        components={animatedComponents}
                      /> */}
                    </div>
                    <div className="w-full " id="startDate">
                      <label
                        className="mb-1 block text-sm font-medium md:text-base lg:text-base"
                        htmlFor="startDate"
                      >
                        Start Date
                      </label>
                      <div className="absolute" id="startDate">
                        <Flatpickr
                          className="startDate form-input  w-full pl-9 font-medium hover:text-gray-600 focus:border-gray-300"
                          options={options}
                          value={startDate}
                          placeholder="Select Start Date"
                          onChange={(date, dateStr) => {
                            setStartDate(dateStr);
                          }}
                        />
                        <div className="pointer-events-none absolute inset-0 right-auto flex items-center">
                          <svg
                            className="ml-3 h-4 w-4 fill-current text-gray-500"
                            viewBox="0 0 16 16"
                          >
                            <path d="M15 2h-2V0h-2v2H9V0H7v2H5V0H3v2H1a1 1 0 00-1 1v12a1 1 0 001 1h14a1 1 0 001-1V3a1 1 0 00-1-1zm-1 12H2V6h12v8z" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="pt-3 sm:flex sm:items-center sm:justify-center">
                    <div className="mr-5  mt-10 w-full">
                      <label
                        className="mb-1 block text-sm font-medium md:text-base lg:text-base"
                        htmlFor="policyNumber"
                      >
                        Policy Number
                      </label>
                      <input
                        id="policyNumber"
                        value={policyNumber}
                        onChange={(e) => setPolicyNumber(e.target.value)}
                        className="form-input w-full  pl-5  focus:border-gray-300"
                        type="search"
                        placeholder="Policy Number"
                      />
                    </div>
                    <div className="w-full" id="endDate">
                      <label
                        className="mb-1 block text-sm font-medium md:text-base lg:text-base"
                        htmlFor="endDate"
                      >
                        End Date
                      </label>
                      <div className="absolute">
                        <Flatpickr
                          className="endDate form-input  w-full pl-9 font-medium hover:text-gray-600 focus:border-gray-300"
                          options={options}
                          value={endDate}
                          placeholder="Select End Date"
                          onChange={(date, dateStr) => {
                            setEndDate(dateStr);
                          }}
                        />
                        <div className="pointer-events-none absolute inset-0 right-auto flex items-center">
                          <svg
                            className="ml-3 h-4 w-4 fill-current text-gray-500"
                            viewBox="0 0 16 16"
                          >
                            <path d="M15 2h-2V0h-2v2H9V0H7v2H5V0H3v2H1a1 1 0 00-1 1v12a1 1 0 001 1h14a1 1 0 001-1V3a1 1 0 00-1-1zm-1 12H2V6h12v8z" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {/* Modal footer */}

              <div className="m-5 mb-10 mt-10 sm:m-5 sm:flex sm:items-center sm:justify-center sm:px-5 sm:py-4">
                <button
                  className="btn m-5 w-full border-gray-200 text-gray-600 hover:border-gray-300 sm:w-44 sm:px-16 sm:py-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    setAddPolicyModalOpen(false);
                  }}
                >
                  Clear
                </button>
                <button
                  className="btn m-5 w-full bg-blue-500 text-white hover:bg-blue-600 sm:w-44 sm:px-16 sm:py-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    saveNavigateToCoverPage(e);
                  }}
                >
                  Save
                </button>
              </div>
            </ModalBasic>

            {loadingPolicies === true ? (
              <TailSpin height="40" width="60" color="#2193FF" ariaLabel="loading" />
            ) : (
              <PoliciesTable selectedItems={handleSelectedItems} policies={policies} />
            )}
            {/* Table */}

            {/* Pagination */}
            <div className="mt-8">{/* <Pagination /> */}</div>
          </div>
        </main>
      </div>
    </div>
  );
};
const mapStateToProps = (state) => ({
  schemes: state.schemes.schemes,
  success: state.policy.success,
  loading: state.policy.loading,
});
const mapDispatchToProps = (dispatch) => ({
  addPolicy: (policyObject) => dispatch(addPolicy(policyObject)),
  getSchemes: (page) => dispatch(getSchemes(page)),
  getPolicies: () => dispatch(getPolicies()),
});
export default connect(mapStateToProps, mapDispatchToProps)(Policies);
