import React, { useState, useEffect } from "react";
import { connect, useSelector } from "react-redux";

import Pagination from "../../../components/Pagination";
import ModalBasic from "../../../components/ModalBasic";
import CategoryBenefitTable from "../../../pages/partials/pageItems/CategoryBenefitTable";
import ModalMedium from "../../../components/ModalMedium";

import Flatpickr from "react-flatpickr";
import CategoryMembersTable from "../../../pages/partials/pageItems/CategoryMembersTable";
import CategoryCopaymentTable from "../../../pages/partials/pageItems/CategoryCopaymentTable";
import {
  getPolicyCategoryBenefit,
  getPolicyCategoryMembers,
  getPolicyCategoryProviderExclusion,
  getPolicyCategoryCopayments,
  addPolicyCategoryBenefit,
  addPolicyCategoryMembers,
  addPolicyCategoryProviderExclusion,
  addPolicyCategoryCopayment,
  processCategoryBenefits,
  getPolicyCategoryPrincipalMembers,
  getCategoryMainBenefits,
  getPolicyCategoryProviderRestriction,
} from "../../../store/policies/policyCategory/actions";
import { RootState } from "../../../store";
import Select from "react-select";

import { getPayers } from "../../../store/payers/actions";
import { getProviders } from "../../../store/providers/actions";
import makeAnimated from "react-select/animated";
import { toast } from "react-toastify";
import * as notifications from "../../../lib/notifications.js";
import { AsyncPaginate } from "react-select-async-paginate";
import {
  loadBenefitCatalogOptions,
  loadPayerOptions,
  loadProviderOptions,
} from "../../../lib/loadOptions";
import CategoryBenefitItemSummary from "../../partials/pageItems/CategoryBenefitItemSummary";
import CategoryProvRestrictionTable from "../../partials/pageItems/CategoryProvRestrictionTable";
const animatedComponents = makeAnimated();

interface Payer {
  id: number;
  type: string;
  name: string;
  contact: string;
}
const PolicyCategoryOverview = ({
  getPolicyCategoryBenefit,
  getPolicyCategoryMembers,
  addPolicyCategoryBenefit,
  addPolicyCategoryMembers,
  addPolicyCategoryProviderExclusion,
  addPolicyCategoryCopayment,
  getProviders,
  categoryBenefits,
  getPayers,
  getPolicyCategoryProviderExclusion,
  getPolicyCategoryCopayments,
  processCategoryBenefits,
  getPolicyCategoryPrincipalMembers,
  getCategoryMainBenefits,
  getPolicyCategoryProviderRestriction,
}) => {
  const [categoryBenefitModalOpen, setCategoryBenefitModalOpen] = useState(false);

  const [categoryMemberModalOpen, setCategoryMemberModalOpen] = useState(false);

  const [categoryCopaymentModalOpen, setCategoryCopaymentModalOpen] = useState(false);
  const [categoryProviderExclusionModalOpen, setCategoryProviderExclusionModalOpen] =
    useState(false);
  const [providerMappingModalOpen, setProviderMappingModalOpen] = useState(false);
  const [benefitMappingModalOpen, setBenefitMappingModalOpen] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [openTab, setOpenTab] = React.useState(1);
  const [color, setColor] = React.useState("blue");
  const [selectedItems, setSelectedItems] = useState([]);
  const [payerModalOpen, setPayerModalOpen] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [refreshKeyMembers, setRefreshKeyMembers] = useState(0);
  const [refreshKeyProviderExclusions, setRefreshKeyProviderExclusions] = useState(0);
  const [refreshKeyCopayment, setRefreshKeyCopayment] = useState(0);
  const [processBenefitsModalOpen, setProcessBenefitsModalOpen] = useState(false);
  const handleSelectedItems = (selectedItems) => {
    setSelectedItems([...selectedItems]);
  };

  let policyCategoryId = "";
  if (window.location.pathname.toString().includes("policies/overview/addBenefits")) {
    policyCategoryId = window.location.pathname.split("policies/overview/addBenefits/")[1];
  } else if (window.location.pathname.toString().includes("policies/overview/addMembers")) {
    policyCategoryId = window.location.pathname.split("policies/overview/addMembers/")[1];
  } else if (window.location.pathname.toString().includes("/policies/overview/livenPolicy")) {
    policyCategoryId = window.location.pathname.split("policies/overview/livenPolicy/")[1];
  } else {
    policyCategoryId = window.location.pathname.split("/policies/category/overview/")[1];
  }
  ///console.log(policyCategoryId);

  // data
  const [benefitName, setBenefitName] = useState("");
  const [benefitApplicableGender, setBenefitApplicableGender] = useState("");
  const [benefitApplicableMember, setBenefitApplicableMember] = useState("");
  const [benefitLimit, setBenefitLimit] = useState("");
  const [benefitSuspensionThreshold, setBenefitSuspensionThreshold] = useState("");
  const [benefitRequirePreAuth, setBenefitRequirePreAuth] = useState(false);

  const [showPrincipalList, setShowPrincipalList] = useState(false);
  const [principalValue, setPrincipalValue] = useState(0);
  const [mainBenefitValue, setMainBenefitValue] = useState(0);
  const [principalSelected, setPrincipalSelected] = useState(false);
  const [mainBenefitSelected, setMainBenefitSelected] = useState(false);

  const [benefitPreAuthType, setBenefitPreAuthType] = useState("");
  const [benefitSharing, setBenefitSharing] = useState("");
  const [benefitCopaymentReq, setBenefitCopaymentReq] = useState(false);
  const [benefitCopayAmount, setBenefitCopayAmount] = useState(0);
  const [benefitWaitingPeriod, setBenefitWaitingPeriod] = useState("");
  const [tabCategory, setTabCategory] = useState("");
  const [benefitMainBenefit, setBenefitMainBenefit] = useState(0);

  //
  const [error, setError] = useState("");
  const [memberName, setMemberName] = useState("");
  const [memberNumber, setMemberNumber] = useState("");
  const [nhifNumber, setnhifNumber] = useState("");
  const [memberDOB, setMemberDOB] = useState("");
  const [memberGender, setMemberGender] = useState("");
  const [memberPhoneNumber, setMemberPhoneNumber] = useState("");
  const [memberEmail, setMemberEmail] = useState("");
  const [memberBeneficiaryType, setMemberBeneficiaryType] = useState("");
  const [memberPrincipalId, setMemberPrincipalId] = useState("");
  const [providerSelected, setProviderSelected] = useState("");
  const [copayAmount, setCopayAmount] = useState("");
  const [mainBenefitSelect, setMainBenefitSelect] = useState("");
  const [providerSelectedCopay, setProviderSelectedCopay] = useState("");
  const [benefitSubmitted, setBenefitSubmitted] = useState(false);
  const [memberSubmitted, setMemberSubmitted] = useState(false);
  const [benefitProcessClicked, setBenefitProcessClicked] = useState(false);
  const [provExclusionSubmitted, setProvExclusionSubmitted] = useState(false);
  const [copaymentSubmitted, setCopaymentSubmitted] = useState(false);
  const [categoryBenefitsSubmitted, setCategoryBenefitsSubmitted] = useState(false);

  const [payerSelected, setPayerSelected] = useState<Payer>();
  const [selectedPayerValue, setSelectedPayerValue] = useState<null | Payer>();

  const [catalogBenefitSelected, setCatalogBenefitSelected] = useState(false);
  const [selectedcatalogBenefitValue, setSelectedCatalogBenefitValue] = useState();

  const [providerExclusion, setProviderExclusion] = useState(false);
  const [providerExclusionValue, setProviderExclusionValue] = useState({});

  const [providerCopayment, setProviderCopayment] = useState(false);
  const [providerCopaymentValue, setProviderCopaymentValue] = useState({});
  const [refreshKeyRestrictions, setRefreshKeyRestrictions] = useState(0);

  const PolicyCategoryItem = useSelector((state: RootState) => state.policyCategory.policyCategory);

  const payers = useSelector((state: RootState) => state.payers.payers);
  const success = useSelector((state: RootState) => state.policyCategory.success);
  const processBenefitsSuccess = useSelector(
    (state: RootState) => state.policyCategory.processBenefitsSuccess,
  );

  const taskId = useSelector((state: RootState) => state.dashboard.taskId);
  const categoryId = useSelector((state: RootState) => state.dashboard.categoryId);
  const policyId = useSelector((state: RootState) => state.dashboard.policyId);

  useEffect(() => {
    if (benefitRequirePreAuth === false) {
      setBenefitPreAuthType("NONE");
    }
    if (benefitCopaymentReq === false) {
      setBenefitCopayAmount(0);
    }
  }, [benefitRequirePreAuth, benefitCopaymentReq]);

  const handleCategoryBenefitSubmit = (e) => {
    e.preventDefault();
    setError("");
    let ticketingObj = {};

    if (
      !benefitName ||
      !benefitApplicableGender ||
      !benefitApplicableMember ||
      !benefitLimit ||
      !benefitSuspensionThreshold ||
      !benefitWaitingPeriod ||
      !selectedPayerValue
    ) {
      toast.error("Complete / Select Required Fields", {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      setBenefitSubmitted(false);
      setError("* Complete / Select Required Fields");
      return;
    }
    if (window.location.pathname.includes("policies/overview/addBenefits/")) {
      ticketingObj = {
        taskId,
        categoryId,
        policyId,
      };
    }

    const benefitsObject = {
      categoryId: Number(policyCategoryId),
      name: benefitName,
      applicableGender: benefitApplicableGender.toUpperCase(),
      applicableMember: benefitApplicableMember.toUpperCase(),
      limit: Number(benefitLimit),
      suspensionThreshold: Number(benefitSuspensionThreshold),
      preAuthType: benefitPreAuthType.toUpperCase(),
      sharing: benefitSharing.toUpperCase(),
      coPaymentRequired: benefitCopaymentReq,
      coPaymentAmount: benefitCopayAmount,
      waitingPeriod: benefitWaitingPeriod.toUpperCase(),
      parentBenefitId: mainBenefitValue ? mainBenefitValue : 0,
      catalogRefId: selectedcatalogBenefitValue,
      payer: {
        id: payerSelected.id,
        name: payerSelected.name,
        type: payerSelected.type,
        contact: payerSelected.contact,
      },
      ticketingObj,
    };
    addPolicyCategoryBenefit(benefitsObject).then(() => {
      setBenefitSubmitted(true);
      getPolicyCategoryBenefit(policyCategoryId).then(() => {
        setRefreshKey((oldKey) => oldKey + 1);
      });
      setCategoryBenefitModalOpen(false);
    });
  };

  useEffect(() => {
    getProviders();
  }, []);

  const providers = useSelector((state: RootState) => state.providers.providers);

  const handleCategoryProvExclusionSubmit = () => {
    setError("");
    if (!providerExclusionValue || !policyCategoryId) {
      toast.error("Complete / Select Required Fields", {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      setCategoryProviderExclusionModalOpen(true);
      setError("* Complete / Select Required Fields");
      return;
    }
    const catProvExclusionObj = {
      categoryId: Number(policyCategoryId),
      providerId: providerExclusionValue,
    };
    setCategoryProviderExclusionModalOpen(false);

    addPolicyCategoryProviderExclusion(catProvExclusionObj).then(() => {
      setProvExclusionSubmitted(true);
      getPolicyCategoryProviderExclusion(policyCategoryId).then(() => {
        setRefreshKeyProviderExclusions((oldKey) => oldKey + 1);
      });
    });
  };

  useEffect(() => {
    if (benefitSubmitted) {
      if (success) {
        toast.success("Benefit Added Successfully!", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        setBenefitSubmitted(false);
      } else {
        toast.error("Error Occurred!", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        setBenefitSubmitted(false);
      }
    } else if (memberSubmitted) {
      if (success) {
        toast.success(`Member Added Successfully!`, {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        setMemberSubmitted(false);
      } else {
        toast.error("Error Occurred!", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        setMemberSubmitted(false);
      }
    } else if (provExclusionSubmitted) {
      if (success) {
        toast.success(`Provider Exclusion Added Successfully!`, {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        setProvExclusionSubmitted(false);
      } else {
        toast.error("Error Occurred!", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        setProvExclusionSubmitted(false);
      }
    } else if (copaymentSubmitted) {
      if (success) {
        toast.success(`Copayment Added Successfully!`, {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        setCopaymentSubmitted(false);
      } else {
        toast.error("Error Occurred!", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        setCopaymentSubmitted(false);
      }
    }
  }, [benefitSubmitted, memberSubmitted, provExclusionSubmitted, copaymentSubmitted]);

  const categoryProviderExclusion = useSelector(
    (state: RootState) => state.policyCategory.categoryProviderExclusion,
  );

  const categoryCopayemnt = useSelector(
    (state: RootState) => state.policyCategory.categoryCopayemnt,
  );

  const totalBenefitElements = useSelector(
    (state: RootState) => state.policyCategory.totalBenefitElements,
  );
  const totalBenefitPages = useSelector(
    (state: RootState) => state.policyCategory.totalBenefitPages,
  );
  const pageBenefitNumber = useSelector(
    (state: RootState) => state.policyCategory.pageBenefitNumber,
  );
  //

  const totalMembersElements = useSelector(
    (state: RootState) => state.policyCategory.totalMembersElements,
  );
  const totalMembersPages = useSelector(
    (state: RootState) => state.policyCategory.totalMembersPages,
  );
  const pageMembersNumber = useSelector(
    (state: RootState) => state.policyCategory.pageMembersNumber,
  );

  //
  const totalProvExclusionElements = useSelector(
    (state: RootState) => state.policyCategory.totalProvExclusionElements,
  );
  const totalProvExclusionPages = useSelector(
    (state: RootState) => state.policyCategory.totalProvExclusionPages,
  );
  const pageProvExclusionNumber = useSelector(
    (state: RootState) => state.policyCategory.pageProvExclusionNumber,
  );

  //
  const totalCopaymentElements = useSelector(
    (state: RootState) => state.policyCategory.totalCopaymentElements,
  );
  const totalCopaymentPages = useSelector(
    (state: RootState) => state.policyCategory.totalCopaymentPages,
  );
  const pageCopaymentNumber = useSelector(
    (state: RootState) => state.policyCategory.pageCopaymentNumber,
  );

  //
  const categoryMembers = useSelector((state: RootState) => state.policyCategory.categoryMembers);

  const categoryPrincipals = useSelector(
    (state: RootState) => state.policyCategory.categoryPrincipals,
  );
  const processBenefitsloading = useSelector(
    (state: RootState) => state.policyCategory.processBenefitsloading,
  );

  const categoryMainBenefits = useSelector(
    (state: RootState) => state.policyCategory.categoryMainBenefits,
  );

  const categoryProviderRestrictions = useSelector(
    (state: RootState) => state.policyCategory.categoryProviderRestrictions,
  );
  const policyItem = useSelector((state: RootState) => state.policy.policyItem);

  const handleCategoryMembersSubmit = (e) => {
    e.preventDefault();
    let ticketingObj = {};
    setError("");
    if (
      !memberName ||
      !policyCategoryId ||
      !memberName ||
      !memberNumber ||
      !memberGender ||
      !memberPhoneNumber ||
      !memberBeneficiaryType
    ) {
      setError("* Complete / Select Required Fields");
      return;
    }
    if (window.location.pathname.includes("policies/overview/addMembers")) {
      ticketingObj = {
        taskId,
        categoryId,
        policyId,
      };
    }

    if (!policyCategoryId) {
      policyCategoryId = window.location.pathname.split("/policies/category/overview/")[1];
    }

    const memberObject = {
      categoryId: Number(policyCategoryId),
      name: memberName,
      memberNumber: memberNumber,
      nhifNumber,
      dob: memberDOB,
      gender: memberGender.toUpperCase(),
      phoneNumber: memberPhoneNumber,
      email: memberEmail,
      beneficiaryType: memberBeneficiaryType.toUpperCase(),
      principalId: principalValue,
      ticketingObj,
    };

    addPolicyCategoryMembers(memberObject).then(() => {
      setCategoryMemberModalOpen(false);
      setMemberSubmitted(true);
      getPolicyCategoryMembers(policyCategoryId).then(() => {
        setRefreshKeyMembers((oldKey) => oldKey + 10);
      });
    });
  };

  const handleCategoryCopaymentSubmit = (e) => {
    e.preventDefault();

    setError("");
    if (!copayAmount || !providerCopaymentValue) {
      setError("* Complete / Select Required Fields");
      return;
    }

    if (!policyCategoryId) {
      policyCategoryId = window.location.pathname.split("/policies/category/overview/")[1];
    }

    const coPayObj = {
      categoryId: Number(policyCategoryId),
      amount: copayAmount,
      providerId: providerCopaymentValue,
    };
    setCategoryCopaymentModalOpen(false);

    addPolicyCategoryCopayment(coPayObj).then(() => {
      setCopaymentSubmitted(true);
      getPolicyCategoryCopayments(policyCategoryId).then(() => {
        setRefreshKeyCopayment((oldKey) => oldKey + 1);
      });
    });
  };

  const handleBenefitProcess = (e, processCategoryId) => {
    e.preventDefault();
    let ticketingObj = {};
    if (window.location.pathname.includes("policies/overview/livenPolicy")) {
      ticketingObj = {
        taskId,
        categoryId,
        policyId,
      };
    }

    if (!processCategoryId) {
      processCategoryId = window.location.pathname.split("/policies/category/overview/")[1];
    }

    const processBenefitObj = {
      processCategoryId,
      ticketingObj,
    };
    setBenefitProcessClicked(true);
    processCategoryBenefits(processBenefitObj)
      .then(() => {
        setCategoryBenefitsSubmitted(true);
      })
      .then(() => {});
  };

  console.log(processBenefitsSuccess);

  useEffect(() => {
    console.log("hered");

    if (benefitProcessClicked && processBenefitsSuccess === true) {
      notifications.Success({
        title: "Benefits Processed Successfully !",
      });
      setBenefitProcessClicked(false);
    } else if (benefitProcessClicked && processBenefitsSuccess === false) {
      console.log("errorb");
      notifications.Error({
        title: "An Error Occurred while Processing Benefits!",
      });
      setBenefitProcessClicked(false);
    }
  }, [benefitProcessClicked, processBenefitsSuccess]);

  useEffect(() => {
    if (window.location.pathname.includes("policies/overview/addBenefits/")) {
      getPolicyCategoryBenefit(policyCategoryId).then(() => {
        setRefreshKey((oldKey) => oldKey + 1);
      });
      getPolicyCategoryMembers(policyCategoryId).then(() => {
        setRefreshKeyMembers((oldKey) => oldKey + 1);
      });
      getPolicyCategoryProviderExclusion(policyCategoryId).then(() => {
        setRefreshKeyProviderExclusions((oldKey) => oldKey + 1);
      });
      getPolicyCategoryPrincipalMembers(policyCategoryId);
      getCategoryMainBenefits(policyCategoryId);

      getPolicyCategoryCopayments(policyCategoryId).then(() => {
        setRefreshKeyCopayment((oldKey) => oldKey + 1);
      });
      setOpenTab(1);
      getPayers().then(() => {});
      setCategoryBenefitModalOpen(true);
    } else if (window.location.pathname.toString().includes("policies/overview/addMembers")) {
      getPolicyCategoryBenefit(policyCategoryId).then(() => {
        setRefreshKey((oldKey) => oldKey + 1);
      });
      getPolicyCategoryMembers(policyCategoryId).then(() => {
        setRefreshKeyMembers((oldKey) => oldKey + 1);
      });
      getPolicyCategoryProviderExclusion(policyCategoryId).then(() => {
        setRefreshKeyProviderExclusions((oldKey) => oldKey + 1);
      });
      getPolicyCategoryPrincipalMembers(policyCategoryId);
      getCategoryMainBenefits(policyCategoryId);

      getPolicyCategoryCopayments(policyCategoryId).then(() => {
        setRefreshKeyCopayment((oldKey) => oldKey + 1);
      });
      getPayers().then(() => {});
      setOpenTab(2);
      setCategoryMemberModalOpen(true);
    } else if (window.location.pathname.toString().includes("policies/overview/livenPolicy")) {
      getPolicyCategoryBenefit(policyCategoryId).then(() => {
        setRefreshKey((oldKey) => oldKey + 1);
      });
      getPolicyCategoryMembers(policyCategoryId).then(() => {
        setRefreshKeyMembers((oldKey) => oldKey + 1);
      });
      getPolicyCategoryProviderExclusion(policyCategoryId).then(() => {
        setRefreshKeyProviderExclusions((oldKey) => oldKey + 1);
      });
      getPolicyCategoryPrincipalMembers(policyCategoryId);
      getCategoryMainBenefits(policyCategoryId);

      getPolicyCategoryCopayments(policyCategoryId).then(() => {
        setRefreshKeyCopayment((oldKey) => oldKey + 1);
      });
      getPayers().then(() => {});
    } else {
      getPolicyCategoryBenefit(policyCategoryId).then(() => {
        setRefreshKey((oldKey) => oldKey + 1);
      });
      getPolicyCategoryMembers(policyCategoryId).then(() => {
        setRefreshKeyMembers((oldKey) => oldKey + 1);
      });
      getPolicyCategoryPrincipalMembers(policyCategoryId);
      getCategoryMainBenefits(policyCategoryId);

      getPolicyCategoryProviderExclusion(policyCategoryId).then(() => {
        setRefreshKeyProviderExclusions((oldKey) => oldKey + 1);
      });
      getPolicyCategoryProviderRestriction(policyCategoryId).then(() => {
        setRefreshKeyMembers((oldKey) => oldKey + 1);
      });

      getPolicyCategoryCopayments(policyCategoryId).then(() => {
        setRefreshKeyCopayment((oldKey) => oldKey + 1);
      });
      getPayers().then(() => {});
    }
  }, []);

  const changePayerName = (e) => {
    setPayerSelected(e);
    console.log(e);
    if (e !== null) {
      setSelectedPayerValue(e["value"]);
      console.log(e["value"]);
    }
  };
  const changeBenefitCatalogName = (e) => {
    setCatalogBenefitSelected(e);
    if (e !== null) {
      setSelectedCatalogBenefitValue(e["value"]);
    }
  };

  const handleProviderChange = (e) => {
    setProviderSelected(e.target.value);
  };
  const handleProviderChangeCopay = (e) => {
    setProviderSelectedCopay(e.target.value);
  };
  const handleMainBenefitChange = (e) => {
    console.log(e.target.value);
    setMainBenefitSelect(e.target.value);
  };

  const data = [];
  if (payers != null) {
    for (var i = 0; i < payers.length; i++) {
      data.push({
        label: payers[i].name,
        value: payers[i],
      });
    }
  }

  // Pagination change
  const handleBenefitPaginationChange = (e) => {
    console.log(e);
    getPolicyCategoryBenefit(policyCategoryId, e).then(() => {
      setRefreshKey((oldKey) => oldKey + 1);
    });
  };
  // pagination Member  handleProviderExclusionPaginationChange
  const handleMemberPaginationChange = (e) => {
    console.log(e);
    getPolicyCategoryMembers(policyCategoryId, e).then(() => {
      setRefreshKeyMembers((oldKey) => oldKey + 1);
    });
  };

  const handleProviderExclusionPaginationChange = (e) => {
    console.log(e);
    getPolicyCategoryProviderExclusion(policyCategoryId, e).then(() => {
      setRefreshKeyProviderExclusions((oldKey) => oldKey + 1);
    });
  };

  const options = {
    mode: "single",
    static: true,
    monthSelectorType: "static",
    dateFormat: "Y-m-d",
    defaultDate: [new Date("Y-m-d")],
    enableTime: false,
    // altInput: true,
    prevArrow:
      '<svg class="fill-current" width="7" height="11" viewBox="0 0 7 11"><path d="M5.4 10.8l1.4-1.4-4-4 4-4L5.4 0 0 5.4z" /></svg>',
    nextArrow:
      '<svg class="fill-current" width="7" height="11" viewBox="0 0 7 11"><path d="M1.4 10.8L0 9.4l4-4-4-4L1.4 0l5.4 5.4z" /></svg>',
  };

  const changeProviderName = (e) => {
    if (e) {
      setProviderExclusion(e);
      console.log(e);
      console.log(e["value"]);
      setProviderExclusionValue(e["value"]);
    } else {
      setProviderExclusion(e);
    }
  };

  const changeProviderNameCopayment = (e) => {
    if (e) {
      setProviderCopayment(e);
      console.log(e);
      console.log(e["value"]);
      setProviderCopaymentValue(e["value"]);
    } else {
      setProviderCopayment(e);
    }
  };

  const handleSelectBeneficiaryType = (e) => {
    setMemberBeneficiaryType(e.target.value);
    console.log(e.target.value.toString().toLowerCase());
    if (e.target.value.toString().toLowerCase() !== "principal") {
      setShowPrincipalList(true);
    } else {
      setShowPrincipalList(false);
    }
  };

  const changePrincipalName = (e) => {
    if (e) {
      setPrincipalSelected(e);
      setPrincipalValue(e["value"].id);
    } else {
      setPrincipalValue(0);
      setPrincipalSelected(e);
    }
  };
  const changeMainBenefitName = (e) => {
    if (e) {
      setMainBenefitSelected(e);
      console.log(e["value"].id);
      setMainBenefitValue(e["value"].id);
    } else {
      setMainBenefitValue(0);
      setMainBenefitSelected(e);
    }
  };

  const prinsipalData = [];
  if (categoryPrincipals != null) {
    for (var i = 0; i < categoryPrincipals.length; i++) {
      prinsipalData.push({
        label: categoryPrincipals[i].name,
        value: categoryPrincipals[i],
      });
    }
  }

  const mainBenefitsData = [];
  if (categoryMainBenefits != null) {
    for (var i = 0; i < categoryMainBenefits.length; i++) {
      mainBenefitsData.push({
        label: categoryMainBenefits[i].name,
        value: categoryMainBenefits[i],
      });
    }
  }

  const handleRefreshPrincipalsInCategory = () => {
    if (policyCategoryId) {
      getPolicyCategoryPrincipalMembers(policyCategoryId);
    }
  };

  const handleRefreshMainBenefitsInCategory = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (policyCategoryId) {
      getCategoryMainBenefits(policyCategoryId);
    }
  };

  useEffect(() => {
    setRefreshKeyRestrictions((oldKey) => oldKey + 1);
  }, [categoryProviderRestrictions]);

  return (
    <div className="flex h-full overflow-hidden">
      {/* Sidebar */}
      {/* Content area */}
      <div className="relative flex flex-1 flex-col overflow-y-auto overflow-x-hidden">
        {/*  Site header */}
        <main className="mx-4 mb-4 lg:mx-8 lg:mb-8">
          <div className="mx-auto mb-10 mt-1 w-full max-w-9xl px-4 py-2 shadow-xl sm:px-6 lg:px-8">
            {/* Page header */}
            <div className="sm:flex sm:items-center sm:justify-between">
              {/* Left: Title */}
              <div className=" sm:mb-0">
                <h1 className="text-sm font-bold text-gray-800 md:text-base lg:text-lg">
                  Policy No:{" "}
                  {Object.keys(PolicyCategoryItem).length === 0 ? "" : policyItem.policyNumber}
                </h1>
                <h3 className="mt-2 text-sm font-bold text-gray-800 md:text-base lg:text-lg">
                  Category: {PolicyCategoryItem.name}
                </h3>
              </div>

              {/* <button
                className="transition ease-in-out  pt-2 pb-2 pl-9 pr-9 border-double border-gray-100 border-4 rounded-md
              text-gray-200 font-medium hover:-translate-y-1 hover:scale-130  duration-300 mt-5 btn bg-blue-500 hover:bg-blue-600 text-white"
                onClick={() => setProcessBenefitsModalOpen(true)}
              >
                {" "}
                Process Benefits
              </button> */}
            </div>
            {/* More actions */}
            {/* Modal */}
            <ModalBasic
              id="feedback-modal"
              modalOpen={payerModalOpen}
              setModalOpen={setPayerModalOpen}
              title="Add Payer"
            >
              {/* Modal content */}
              <div className="px-10 pb-1 pt-4">
                <h3 className="mb-5 text-lg text-gray-400">PAYER DETAILS</h3>

                <div className="text-sm">
                  {/* Options */}
                  <div className="flex">
                    <div className="mr-9 w-64">
                      <label className="mb-1 block text-sm font-medium" htmlFor="name">
                        Payer Name
                      </label>
                      <input
                        id="name"
                        className="form-input w-full"
                        type="text"
                        placeholder="Payer Name"
                      />
                    </div>
                    <div className="mr-9 w-64">
                      <label className="mb-1 block text-sm font-medium" htmlFor="email">
                        Email Address
                      </label>
                      <input
                        id="email"
                        className="form-input w-full "
                        type="email"
                        placeholder="Email Address"
                      />
                    </div>
                    <div className="mr-9 w-64">
                      <label className="mb-1 block text-sm font-medium" htmlFor="name">
                        Mobile
                      </label>
                      <input
                        id="name"
                        className="form-input w-full"
                        type="phone"
                        placeholder="Mobile"
                      />
                    </div>
                  </div>
                  <div className="mt-10">
                    <h3 className="mb-5 text-lg text-gray-400">PAYER TYPE</h3>
                  </div>

                  <div className="mt-5 flex">
                    <div className="mr-9  flex ">
                      <div className="mr-5  w-48">
                        <input type="radio" name="option" id="option1" className="hidden " />
                        <label
                          htmlFor="option1"
                          className="rounded-md border-2 px-16 py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                        >
                          Underwriter
                        </label>
                      </div>
                      <div className="mr-5 w-48">
                        <input type="radio" name="option" id="option2" className="hidden" />
                        <label
                          htmlFor="option2"
                          className="rounded-md border-2 px-16  py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                        >
                          Corporate
                        </label>
                      </div>
                      <div className="mr-5 w-48">
                        <input type="radio" name="option" id="option3" className="hidden" />
                        <label
                          htmlFor="option3"
                          className="rounded-md border-2 px-16  py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                        >
                          Intermediary
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {/* Modal footer */}
              <div className="mt-5 px-5 py-4">
                <div className="flex flex-wrap justify-center space-x-2">
                  <button
                    className="btn-sm w-44 border-gray-200 px-16 py-2 text-gray-600 hover:border-gray-300"
                    onClick={(e) => {
                      e.stopPropagation();
                      setPayerModalOpen(false);
                    }}
                  >
                    Clear
                  </button>
                  <button className="btn-sm w-44 bg-blue-500 px-16 py-2 text-white hover:bg-blue-600">
                    Save
                  </button>
                </div>
              </div>
            </ModalBasic>

            <div className="mt-5">
              <div className="mt-15 flex flex-wrap">
                <div className="w-full">
                  <ul
                    className="mb-0 flex list-none flex-row flex-wrap border-b-2 pt-3"
                    role="tablist"
                  >
                    <li
                      className={
                        "-mb-px mr-2 flex-auto text-center last:mr-0" +
                        (openTab === 1
                          ? " border-  border-b-4" + color + "-600"
                          : " border-b-gray-600")
                      }
                    >
                      <a
                        className={
                          "block rounded px-5 py-3  text-lg font-bold leading-normal " +
                          (openTab === 1
                            ? "text-" + color + "-600" + " border-b-" + color + "-600"
                            : "text-gray-700")
                        }
                        onClick={(e) => {
                          e.preventDefault();
                          setOpenTab(1);
                        }}
                        data-toggle="tab"
                        href="#link1"
                        role="tablist"
                      >
                        <i className="fas fa-space-shuttle mr-1 text-base"></i> Category Benefit
                      </a>
                    </li>
                    <li
                      className={
                        "-mb-px mr-2 flex-auto text-center last:mr-0" +
                        (openTab === 2
                          ? " border-  border-b-4" + color + "-600"
                          : " border-b-gray-600")
                      }
                    >
                      <a
                        className={
                          "block rounded  px-5 py-3  text-lg font-bold leading-normal " +
                          (openTab === 2
                            ? "text-" + color + "-600" + " border-b-" + color + "-600"
                            : "text-gray-700")
                        }
                        onClick={(e) => {
                          e.preventDefault();
                          setOpenTab(2);
                        }}
                        data-toggle="tab"
                        href="#link2"
                        role="tablist"
                      >
                        <i className="fas fa-cog mr-1 text-base"></i> Members
                      </a>
                    </li>
                    <li
                      className={
                        "-mb-px mr-2 flex-auto text-center last:mr-0" +
                        (openTab === 3
                          ? " border-  border-b-4" + color + "-600"
                          : " border-b-gray-600")
                      }
                    >
                      <a
                        className={
                          "block rounded  px-5 py-3  text-lg font-bold leading-normal " +
                          (openTab === 3
                            ? "text-" + color + "-600" + " border-b-" + color + "-600"
                            : "text-gray-700")
                        }
                        onClick={(e) => {
                          e.preventDefault();
                          setOpenTab(3);
                        }}
                        data-toggle="tab"
                        href="#link3"
                        role="tablist"
                      >
                        <i className="fas fa-cog mr-1 text-base"></i> Provider Restriction
                      </a>
                    </li>
                    <li
                      className={
                        "-mb-px mr-2 flex-auto text-center last:mr-0" +
                        (openTab === 4
                          ? " border-  border-b-4" + color + "-600"
                          : " border-b-gray-600")
                      }
                    >
                      <a
                        className={
                          "block rounded  px-5 py-3 text-lg font-bold leading-normal " +
                          (openTab === 4
                            ? "text-" + color + "-600" + " border-b-" + color + "-600"
                            : "text-gray-700")
                        }
                        onClick={(e) => {
                          e.preventDefault();
                          setOpenTab(4);
                        }}
                        data-toggle="tab"
                        href="#link4"
                        role="tablist"
                      >
                        <i className="fas fa-cog mr-1 text-base"></i> Co-Payment
                      </a>
                    </li>
                  </ul>
                  <div className="relative mb-6 flex w-full min-w-0 flex-col break-words rounded  bg-white">
                    <div className="flex-auto px-4 py-2">
                      <div className="tab-content tab-space">
                        {/* Category benefit */}
                        <div className={openTab === 1 ? "block" : "hidden"} id="link1">
                          <div className="mb-3 mt-5 flex sm:justify-between">
                            <div>
                              {/* <p className="text-gray-500 font-normal hidden sm:block">
                                To setup a new category benefit click on the{" "}
                                <br />{" "}
                                <span className="text-blue-400">
                                  add category benefit button
                                </span>{" "}
                                on the far right
                              </p> */}
                            </div>
                            <div>
                              {/* <button
                                className="btn bg-blue-500 hover:bg-blue-600 text-white xs:w-full"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setCategoryBenefitModalOpen(true);
                                }}
                              >
                                <svg
                                  className="w-4 h-4 fill-current shrink-0"
                                  viewBox="0 0 16 16"
                                >
                                  <path d="M15 7H9V1c0-.6-.4-1-1-1S7 .4 7 1v6H1c-.6 0-1 .4-1 1s.4 1 1 1h6v6c0 .6.4 1 1 1s1-.4 1-1V9h6c.6 0 1-.4 1-1s-.4-1-1-1z" />
                                </svg>
                                <span className="ml-2">
                                  Add Category Benefit
                                </span>
                              </button> */}
                            </div>
                          </div>

                          {/* Table */}
                          <CategoryBenefitTable
                            selectedItems={handleSelectedItems}
                            key={refreshKey}
                            categoryBenefits={categoryBenefits}
                          />
                          {/* No elements exist */}
                          <div className="mr-6 flex justify-center pt-5 font-sans text-sm font-normal md:text-base lg:text-lg">
                            {totalBenefitElements < 1 ? <div>No Records Exist</div> : ""}
                          </div>
                          {/* Pagination */}
                          <div className="mt-8">
                            <Pagination
                              totalElements={totalBenefitElements}
                              totalPages={totalBenefitPages}
                              pageNumber={pageBenefitNumber}
                              OnPageNumberClick={handleBenefitPaginationChange}
                            />
                          </div>
                        </div>
                        {/* members */}
                        <div className={openTab === 2 ? "block" : "hidden"} id="link2">
                          <div className="mb-3 mt-5 flex sm:justify-between">
                            {/* <div>
                              <p className="text-gray-500 font-normal hidden sm:block">
                                To setup a new member click on the <br />{" "}
                                <span className="text-blue-400">
                                  add member button
                                </span>{" "}
                                on the far right
                              </p>
                            </div> */}
                            <div>
                              {/* <button
                                className="btn bg-blue-500 hover:bg-blue-600 text-white xs:w-full "
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setCategoryMemberModalOpen(true);
                                }}
                              >
                                <svg
                                  className="w-4 h-4 fill-current shrink-0"
                                  viewBox="0 0 16 16"
                                >
                                  <path d="M15 7H9V1c0-.6-.4-1-1-1S7 .4 7 1v6H1c-.6 0-1 .4-1 1s.4 1 1 1h6v6c0 .6.4 1 1 1s1-.4 1-1V9h6c.6 0 1-.4 1-1s-.4-1-1-1z" />
                                </svg>
                                <span className="ml-2">Add a Member</span>
                              </button> */}
                            </div>
                          </div>
                          {/* Table */}

                          <CategoryMembersTable
                            selectedItems={handleSelectedItems}
                            key={refreshKeyMembers}
                            categoryMembers={categoryMembers}
                          />
                          {/* No elements exist */}
                          <div className="mr-6 flex justify-center pt-5 font-sans text-sm font-normal md:text-base lg:text-lg">
                            {totalMembersElements < 1 ? <div>No Records Exist</div> : ""}
                          </div>
                          {/* Pagination */}
                          <div className="mt-8">
                            <Pagination
                              totalElements={totalMembersElements}
                              totalPages={totalMembersPages}
                              pageNumber={pageMembersNumber}
                              OnPageNumberClick={handleMemberPaginationChange}
                            />
                          </div>
                        </div>
                        {/* Provider restriction */}
                        <div className={openTab === 3 ? "block" : "hidden"} id="link3">
                          <div className="mb-3 mt-5 flex sm:justify-between">
                            <div>
                              {/* <button
                                className="btn bg-blue-500 hover:bg-blue-600 text-white xs:w-full"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setCategoryProviderExclusionModalOpen(true);
                                }}
                              >
                                <svg
                                  className="w-4 h-4 fill-current shrink-0"
                                  viewBox="0 0 16 16"
                                >
                                  <path d="M15 7H9V1c0-.6-.4-1-1-1S7 .4 7 1v6H1c-.6 0-1 .4-1 1s.4 1 1 1h6v6c0 .6.4 1 1 1s1-.4 1-1V9h6c.6 0 1-.4 1-1s-.4-1-1-1z" />
                                </svg>
                                <span className="ml-2">
                                  Add Provider Exclusion
                                </span>
                              </button> */}
                            </div>
                          </div>
                          <CategoryProvRestrictionTable
                            key={refreshKeyRestrictions}
                            categoryProviderRestrictions={categoryProviderRestrictions}
                          />
                          {/* No elements exist */}
                          <div className="mr-6 flex justify-center pt-5 font-sans text-sm font-normal md:text-base lg:text-lg">
                            {categoryProviderRestrictions.length < 1 ? (
                              <div>No Records Exist</div>
                            ) : (
                              ""
                            )}
                          </div>
                          {/* Pagination */}
                          <div className="mt-8">
                            <Pagination
                              totalElements={totalProvExclusionElements}
                              totalPages={totalProvExclusionPages}
                              pageNumber={pageProvExclusionNumber}
                              OnPageNumberClick={handleProviderExclusionPaginationChange}
                            />
                          </div>
                        </div>
                        {/* Co-payment */}
                        <div className={openTab === 4 ? "block" : "hidden"} id="link4">
                          <div className="mb-3 mt-5 flex sm:justify-between">
                            <div>
                              {/* <p className="text-gray-500 font-normal hidden sm:block">
                                To setup a new co-payment click on the <br />{" "}
                                <span className="text-blue-400">
                                  add Co-payment button
                                </span>{" "}
                                on the far right
                              </p> */}
                            </div>
                            <div>
                              {/* <button
                                className="btn bg-blue-500 hover:bg-blue-600 text-white xs:w-full "
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setCategoryCopaymentModalOpen(true);
                                }}
                              >
                                <svg
                                  className="w-4 h-4 fill-current shrink-0"
                                  viewBox="0 0 16 16"
                                >
                                  <path d="M15 7H9V1c0-.6-.4-1-1-1S7 .4 7 1v6H1c-.6 0-1 .4-1 1s.4 1 1 1h6v6c0 .6.4 1 1 1s1-.4 1-1V9h6c.6 0 1-.4 1-1s-.4-1-1-1z" />
                                </svg>
                                <span className="ml-2">Add Co-Payment</span>
                              </button> */}
                            </div>
                          </div>
                          <CategoryCopaymentTable
                            selectedItems={handleSelectedItems}
                            key={refreshKeyCopayment}
                            categoryCopayment={categoryCopayemnt}
                          />
                          {/* No elements exist */}
                          {/* <div className="flex justify-center font-sans text-sm md:text-base lg:text-lg mr-6 pt-5 font-normal">
                            {totalCopaymentElements < 1 ? (
                              <div>No Records Exist</div>
                            ) : (
                              ""
                            )}
                          </div> */}
                          {/* Pagination */}
                          {/* <div className="mt-8">
                            <Pagination
                              totalElements={totalCopaymentElements}
                              totalPages={totalCopaymentPages}
                              pageNumber={pageCopaymentNumber}
                              OnPageNumberClick={handleBenefitPaginationChange}
                            />
                          </div> */}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* {General process benefits modal} */}
            <div className="space-x-1">
              <div>
                <ModalMedium
                  id="feedback-modal"
                  modalOpen={processBenefitsModalOpen}
                  setModalOpen={setProcessBenefitsModalOpen}
                  title="Liven Policy"
                >
                  {/* Modal content */}

                  <div className="px-10 pb-1 pt-4">
                    <h3 className="text-md mb-5 uppercase text-gray-400">Category Summary</h3>
                    <div>
                      <div>
                        {/* Table */}
                        <div className="overflow-x-auto">
                          <table className="w-full table-auto">
                            {/* Table header */}
                            <thead className="text-md border-b    border-gray-200 font-semibold">
                              <tr>
                                <th className="whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
                                  <div className="text-left font-semibold">Name</div>
                                </th>
                                <th className="whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
                                  <div className="text-left font-semibold">Limit</div>
                                </th>
                                <th className="whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
                                  <div className="text-left font-semibold">Payer</div>
                                </th>
                                <th className="whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
                                  <div className="text-left font-semibold">Sharing</div>
                                </th>
                                <th className="whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
                                  <div className="text-left font-semibold">Co-Payment</div>
                                </th>
                                <th className="whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
                                  <div className="text-left font-semibold">PreAuth</div>
                                </th>
                                <th className="whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
                                  <div className="text-left font-semibold">Gender</div>
                                </th>
                                <th className="whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
                                  <div className="text-left font-semibold">Co-Payment Amount</div>
                                </th>
                                <th className="whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
                                  <div className="text-left font-semibold">Parent Benefit</div>
                                </th>
                                <th className="whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
                                  <div className="text-left font-semibold">Waiting Period</div>
                                </th>
                                <th className="whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
                                  <div className="text-left font-semibold">
                                    Suspension Threshold
                                  </div>
                                </th>
                              </tr>
                            </thead>
                            {/* Table body */}
                            <tbody className="divide-none  text-sm ">
                              {categoryBenefits.map((categoryBenefit) => {
                                return (
                                  <CategoryBenefitItemSummary
                                    key={categoryBenefit.id}
                                    id={categoryBenefit.id}
                                    name={categoryBenefit.name}
                                    payer={categoryBenefit.payer.name}
                                    limit={categoryBenefit.limit}
                                    sharing={categoryBenefit.sharing}
                                    copay={categoryBenefit.coPaymentRequired}
                                    preauth={categoryBenefit.preAuthType}
                                    // processed={categoryBenefit.processed}
                                    applicableGender={categoryBenefit.applicableGender}
                                    coPaymentAmount={categoryBenefit.coPaymentAmount}
                                    parentBenefit={categoryBenefit.parentBenefit}
                                    waitingPeriod={categoryBenefit.waitingPeriod}
                                    suspensionThreshold={categoryBenefit.suspensionThreshold}
                                  />
                                );
                              })}
                            </tbody>
                          </table>
                        </div>
                      </div>
                      <div className="mr-6 flex justify-center pt-5 font-sans text-sm font-normal md:text-base lg:text-lg">
                        {totalBenefitElements < 1 ? <div>No Records Exist</div> : ""}
                      </div>
                      <div className="mt-8">
                        <Pagination
                          totalElements={totalBenefitElements}
                          totalPages={totalBenefitPages}
                          pageNumber={pageBenefitNumber}
                          OnPageNumberClick={handleBenefitPaginationChange}
                        />
                      </div>
                    </div>
                  </div>
                  {/* Modal footer */}
                  <div className="mt-5 px-5 py-4">
                    <div className="flex flex-wrap justify-center space-x-2">
                      <button
                        className="btn-sm mt-5 bg-blue-500 px-8 text-white hover:bg-blue-600"
                        onClick={(e) => {
                          e.stopPropagation();
                          setProcessBenefitsModalOpen(false);
                        }}
                      >
                        Cancel
                      </button>

                      {/* {processBenefitsloading ? (
                        <button
                          className="transition ease-in-out delay-150 bg-gray-600 pt-2 pb-2 pl-9 pr-1 border-double border-gray-100 border-4 rounded-md
              text-gray-200 font-medium hover:-translate-y-1 hover:scale-130 hover:bg-gray-400 duration-300 mt-5"
                          disabled
                        >
                          <span className="flex">
                            Processing Benefits
                            <svg
                              className="animate-spin w-6 h-6 fill-current shrink-0 "
                              viewBox="0 0 16 16"
                            >
                              <path d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z" />
                            </svg>
                          </span>
                        </button>
                      ) : (
                        <button
                          className="transition ease-in-out delay-150 bg-yellow-600 pt-2 pb-2 pl-9 pr-9 border-double border-gray-100 border-4 rounded-md
              text-gray-200 font-medium hover:-translate-y-1 hover:scale-130 hover:bg-yellow-400 duration-300 mt-5"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleBenefitProcess(e, PolicyCategoryItem.id);
                          }}
                        >
                          {" "}
                          Process Benefits
                        </button>
                      )} */}

                      {/* <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleBenefitProcess(e, policyCategoryId);
                        }}
                        type="submit"
                        className="btn-sm bg-blue-500 hover:bg-blue-600 text-white  px-8 py-2"
                      >
                        Process Benefits
                      </button> */}
                    </div>
                  </div>
                </ModalMedium>
              </div>
            </div>

            {/* category Members Modal */}

            <div className="space-x-1">
              <div>
                <ModalBasic
                  id="feedback-modal"
                  modalOpen={categoryMemberModalOpen}
                  setModalOpen={setCategoryMemberModalOpen}
                  title="Category"
                >
                  {/* Modal content */}

                  <div className="px-10 pb-1 pt-4">
                    <h3 className="text-md mb-5 uppercase text-gray-400">
                      category beneficiaries/member details
                    </h3>
                    <div>
                      <label className="mb-1 block text-sm font-medium" htmlFor="memberName">
                        Name
                      </label>
                      <input
                        id="memberName"
                        className="form-input w-full"
                        type="text"
                        placeholder="Name"
                        value={memberName}
                        onChange={(e) => setMemberName(e.target.value)}
                      />
                    </div>

                    <div className="mt-10 flex">
                      <div>
                        <input
                          id="MemberNumber"
                          className="form-input w-64"
                          type="text"
                          placeholder="Member Number"
                          value={memberNumber}
                          onChange={(e) => setMemberNumber(e.target.value)}
                        />
                      </div>
                      <div className="ml-10">
                        <input
                          id="NHIFNumber"
                          className="form-input w-64"
                          type="text"
                          placeholder="SHIF Number"
                          value={nhifNumber}
                          onChange={(e) => setnhifNumber(e.target.value)}
                        />
                      </div>
                      <div className="ml-5">(Date of Birth)</div>
                      <div className="ml-1">
                        <div className="flex" id="dob">
                          <div className="absolute" id="dob">
                            <Flatpickr
                              className="dob form-input w-64 pl-9 font-medium text-gray-500 hover:text-gray-600 focus:border-gray-300"
                              options={options}
                              onChange={(date, dateStr) => {
                                setMemberDOB(dateStr);
                              }}
                            />
                            <div className="pointer-events-none absolute inset-0 right-auto flex items-center">
                              <svg
                                className="ml-3 h-4 w-4 fill-current text-gray-500"
                                viewBox="0 0 16 16"
                              >
                                <path d="M15 2h-2V0h-2v2H9V0H7v2H5V0H3v2H1a1 1 0 00-1 1v12a1 1 0 001 1h14a1 1 0 001-1V3a1 1 0 00-1-1zm-1 12H2V6h12v8z" />
                              </svg>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <h4 className="text-md mb-5  mt-5 uppercase text-gray-400">Gender</h4>
                    <div className="flex">
                      <div className="mt-1 flex">
                        <div className="mr-9  flex ">
                          <div className="mr-5  w-48">
                            <input
                              type="radio"
                              name="optionEdit-Gender"
                              id="male"
                              className="hidden p-20"
                              value="Male"
                              onChange={(e) => setMemberGender(e.target.value)}
                            />
                            <label
                              htmlFor="male"
                              className="inline-block cursor-pointer  rounded-md border-2 p-5 px-16 py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                            >
                              Male
                            </label>
                          </div>
                          <div className="ml-5 mr-5 w-48">
                            <input
                              type="radio"
                              name="optionEdit-Gender"
                              id="female"
                              className="hidden"
                              value="Female"
                              onChange={(e) => setMemberGender(e.target.value)}
                            />
                            <label
                              htmlFor="female"
                              className="inline-block cursor-pointer rounded-md border-2 px-16  py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                            >
                              Female
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="mt-10 flex">
                      <div>
                        <input
                          id="memberMobile"
                          className="form-input w-64"
                          type="text"
                          placeholder="Mobile"
                          value={memberPhoneNumber}
                          onChange={(e) => setMemberPhoneNumber(e.target.value)}
                        />
                      </div>
                      <div className="ml-10">
                        <input
                          id="memberEmail"
                          className="form-input w-64"
                          type="text"
                          placeholder="Email"
                          value={memberEmail}
                          onChange={(e) => setMemberEmail(e.target.value)}
                        />
                      </div>
                    </div>

                    <h4 className="text-md mb-2 mt-10 uppercase text-gray-400">beneficiary Type</h4>
                    <div className="flex">
                      <div className="flex ">
                        <div className="mr-9  flex ">
                          <div className="mr-5  w-48">
                            <input
                              type="radio"
                              name="optionEdit-beneficiaryType"
                              id="beneficiaryTypePrincipal"
                              className="hidden p-20"
                              value="Principal"
                              onChange={(e) => handleSelectBeneficiaryType(e)}
                            />
                            <label
                              htmlFor="beneficiaryTypePrincipal"
                              className="inline-block cursor-pointer  rounded-md border-2 p-5 px-16 py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                            >
                              Principal
                            </label>
                          </div>
                          <div className="ml-5 mr-5 w-48">
                            <input
                              type="radio"
                              name="optionEdit-beneficiaryType"
                              id="beneficiaryTypeSpouse"
                              className="hidden"
                              value="Spouse"
                              onChange={(e) => handleSelectBeneficiaryType(e)}
                            />
                            <label
                              htmlFor="beneficiaryTypeSpouse"
                              className="inline-block cursor-pointer rounded-md border-2 px-16  py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                            >
                              Spouse
                            </label>
                          </div>
                          <div className="mr-5 w-48">
                            <input
                              type="radio"
                              name="optionEdit-beneficiaryType"
                              id="beneficiaryTypechild"
                              className="hidden"
                              value="Child"
                              onChange={(e) => handleSelectBeneficiaryType(e)}
                            />
                            <label
                              htmlFor="beneficiaryTypechild"
                              className="inline-block cursor-pointer rounded-md border-2 px-16  py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                            >
                              Child
                            </label>
                          </div>
                          <div className="mr-5 w-48">
                            <input
                              type="radio"
                              name="optionEdit-beneficiaryType"
                              id="beneficiaryTypeall"
                              className="hidden"
                              value="Parent"
                              onChange={(e) => handleSelectBeneficiaryType(e)}
                            />
                            <label
                              htmlFor="beneficiaryTypeall"
                              className="inline-block cursor-pointer rounded-md border-2 px-16  py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                            >
                              Parent
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>

                    {showPrincipalList === true ? (
                      <div>
                        <h4 className="text-md mb-2 mt-10 uppercase text-gray-400">
                          Select Principal Member
                        </h4>
                        <div className="flex">
                          <div className="flex ">
                            <div className="mr-9  flex ">
                              <div className="mr-5  w-48">
                                <label
                                  className="mb-1 block text-sm font-medium"
                                  htmlFor="principalname"
                                >
                                  Select Principal
                                </label>{" "}
                                <Select
                                  className="react-select"
                                  classNamePrefix="react-select"
                                  name="principalname"
                                  defaultValue={prinsipalData}
                                  options={prinsipalData}
                                  value={principalSelected}
                                  onChange={(value) => changePrincipalName(value)}
                                  placeholder="Select Principal Name"
                                  closeMenuOnSelect={true}
                                  isClearable={true}
                                  isSearchable={true}
                                  components={animatedComponents}
                                />{" "}
                              </div>
                              <button
                                className="btn m-5 w-10 border-gray-200 px-2 text-gray-600 hover:border-gray-300 "
                                onClick={handleRefreshPrincipalsInCategory}
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-6 w-6"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                                  />
                                </svg>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      ""
                    )}
                  </div>
                  {/* Modal footer */}
                  <div className="mt-5 px-5 py-4">
                    <div className="mb-5 flex justify-center text-red-600">{error}</div>
                    <div className="flex flex-wrap justify-center space-x-2">
                      <button
                        className="btn-sm w-44 border-gray-200 px-16 py-2 text-gray-600 hover:border-gray-300"
                        onClick={(e) => {
                          e.stopPropagation();
                          setCategoryMemberModalOpen(false);
                        }}
                      >
                        Clear
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCategoryMembersSubmit(e);
                        }}
                        type="submit"
                        className="btn-sm w-44 bg-blue-500 px-16 py-2 text-white hover:bg-blue-600"
                      >
                        Save
                      </button>
                    </div>
                  </div>
                </ModalBasic>
              </div>
            </div>

            {/* Category Benefit Modal*/}
            <div className="space-x-1">
              <div>
                <ModalMedium
                  id="feedback-modal"
                  modalOpen={categoryBenefitModalOpen}
                  setModalOpen={setCategoryBenefitModalOpen}
                  title="Category"
                >
                  {/* Modal content */}'
                  <form onSubmit={handleCategoryBenefitSubmit}>
                    <div className="px-10 pb-1">
                      <h3 className="text-md mb-5 uppercase text-gray-400">
                        category benefit Details
                      </h3>
                      <div>
                        <label className="mb-1 block text-sm font-medium" htmlFor="default">
                          Name
                        </label>
                        <input
                          id="default"
                          className="form-input w-full"
                          type="text"
                          placeholder="Name"
                          value={benefitName}
                          onChange={(e) => setBenefitName(e.target.value)}
                        />
                      </div>
                      <div className="form-group mt-5">
                        {<h4 className="text-md mb-2  mt-5 uppercase text-gray-400">Payer</h4>}
                        <AsyncPaginate
                          className="react-select "
                          classNamePrefix="react-select"
                          name="providername"
                          value={payerSelected}
                          loadOptions={loadPayerOptions}
                          onChange={(value) => changePayerName(value)}
                          placeholder="Select Payer"
                          isClearable={true}
                        />
                        {/* <Select
                          className="react-select"
                          classNamePrefix="react-select"
                          defaultValue={data}
                          options={data}
                          value={payerSelected}
                          onChange={(value) => changePayerName(value)}
                          placeholder="Select Payer"
                          closeMenuOnSelect={true}
                          isClearable={true}
                          isSearchable={true}
                          components={animatedComponents}
                        /> */}
                      </div>
                      <div className="form-group mt-5">
                        {
                          <h4 className="text-md mb-2  mt-5 uppercase text-gray-400">
                            Link Catalog Benefit
                          </h4>
                        }
                        <AsyncPaginate
                          className="react-select "
                          classNamePrefix="react-select"
                          name="benefitCataloglist"
                          value={catalogBenefitSelected}
                          loadOptions={loadBenefitCatalogOptions}
                          onChange={(value) => changeBenefitCatalogName(value)}
                          placeholder="Select Benefit Name in Catalog "
                          isClearable={true}
                        />
                        {/* <Select
                          className="react-select"
                          classNamePrefix="react-select"
                          defaultValue={data}
                          options={data}
                          value={payerSelected}
                          onChange={(value) => changePayerName(value)}
                          placeholder="Select Payer"
                          closeMenuOnSelect={true}
                          isClearable={true}
                          isSearchable={true}
                          components={animatedComponents}
                        /> */}
                      </div>

                      <h4 className="text-md mb-5  mt-5 uppercase text-gray-400">
                        applicable Gender
                      </h4>
                      <div className="flex">
                        <div className="mt-1 flex">
                          <div className="mr-9  flex ">
                            <div className="mr-5  w-48">
                              <input
                                type="radio"
                                name="optionEdit"
                                id="maleGender"
                                className="hidden p-20"
                                value="Male"
                                onChange={(e) => setBenefitApplicableGender(e.target.value)}
                              />
                              <label
                                htmlFor="maleGender"
                                className="inline-block cursor-pointer  rounded-md border-2 p-5 px-16 py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                              >
                                Male
                              </label>
                            </div>
                            <div className="ml-5 mr-5 w-48">
                              <input
                                type="radio"
                                name="optionEdit"
                                id="femaleGender"
                                className="hidden"
                                value="Female"
                                onChange={(e) => setBenefitApplicableGender(e.target.value)}
                              />
                              <label
                                htmlFor="femaleGender"
                                className="inline-block cursor-pointer rounded-md border-2 px-16  py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                              >
                                Female
                              </label>
                            </div>
                            <div className="mr-5 w-48">
                              <input
                                type="radio"
                                name="optionEdit"
                                id="allGender"
                                className="hidden"
                                value="All"
                                onChange={(e) => setBenefitApplicableGender(e.target.value)}
                              />
                              <label
                                htmlFor="allGender"
                                className="inline-block cursor-pointer rounded-md border-2 px-16  py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                              >
                                All
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>
                      <h4 className="text-md mb-2 mt-10 uppercase text-gray-400">
                        applicable member
                      </h4>
                      <div className="flex">
                        <div className="flex ">
                          <div className="mr-9  flex ">
                            <div className="mr-5 w-full  sm:w-16">
                              <input
                                type="radio"
                                name="optionEdit-am"
                                id="apPrincipal"
                                className="hidden p-20"
                                value="Principal"
                                onChange={(e) => setBenefitApplicableMember(e.target.value)}
                              />
                              <label
                                htmlFor="apPrincipal"
                                className="inline-block cursor-pointer rounded-md border-2 p-5 px-8 py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                              >
                                Principal
                              </label>
                            </div>
                            <div className="ml-5 mr-5 w-full sm:w-16">
                              <input
                                type="radio"
                                name="optionEdit-am"
                                id="apSpouse"
                                className="hidden"
                                value="Spouse"
                                onChange={(e) => setBenefitApplicableMember(e.target.value)}
                              />
                              <label
                                htmlFor="apSpouse"
                                className="ml-10 inline-block  cursor-pointer rounded-md border-2 px-8  py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                              >
                                Spouse
                              </label>
                            </div>
                            <div className="ml-10 mr-5 w-full sm:w-16">
                              <input
                                type="radio"
                                name="optionEdit-am"
                                id="apchild"
                                className="hidden"
                                value="Child"
                                onChange={(e) => setBenefitApplicableMember(e.target.value)}
                              />
                              <label
                                htmlFor="apchild"
                                className="ml-10 inline-block  cursor-pointer rounded-md border-2 px-8  py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                              >
                                Child
                              </label>
                            </div>
                            <div className="ml-10 mr-5 w-full sm:w-16">
                              <input
                                type="radio"
                                name="optionEdit-am"
                                id="apall"
                                className="hidden"
                                value="All"
                                onChange={(e) => setBenefitApplicableMember(e.target.value)}
                              />
                              <label
                                htmlFor="apall"
                                className="ml-10 inline-block  cursor-pointer rounded-md border-2 px-8  py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                              >
                                All
                              </label>
                            </div>
                            <div className="ml-5 flex w-full  sm:w-16">
                              <input
                                type="radio"
                                name="optionEdit-am"
                                id="apPRINCIPAL_AND_SPOUSE"
                                className="hidden "
                                value="PRINCIPAL_AND_SPOUSE"
                                onChange={(e) => setBenefitApplicableMember(e.target.value)}
                              />
                              <label
                                htmlFor="apPRINCIPAL_AND_SPOUSE"
                                className="ml-10 inline-block cursor-pointer rounded-md border-2 px-8  py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                              >
                                PRINCIPAL & SPOUSE
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="mt-10 flex">
                        <div>
                          <input
                            id="limit"
                            className="form-input w-64"
                            type="number"
                            placeholder="Limit"
                            value={benefitLimit}
                            onChange={(e) => setBenefitLimit(e.target.value)}
                          />
                        </div>
                        <div className="ml-10">
                          <input
                            id="suspensionthreshold"
                            className="form-input w-64"
                            type="number"
                            placeholder="Suspension Threshold"
                            value={benefitSuspensionThreshold}
                            onChange={(e) => setBenefitSuspensionThreshold(e.target.value)}
                          />
                        </div>
                        <div className="ml-10">
                          <div className="flex items-center">
                            <div className="text-md ml-2 mr-4 uppercase italic text-gray-600">
                              Requires pre-authorization
                            </div>
                            <div className="form-switch">
                              <input
                                type="checkbox"
                                id="switch-1"
                                className="sr-only "
                                checked={benefitRequirePreAuth}
                                onChange={() => setBenefitRequirePreAuth(!benefitRequirePreAuth)}
                              />
                              <label className="bg-gray-400" htmlFor="switch-1">
                                <span className="bg-white shadow-sm" aria-hidden="true"></span>
                                <span className="sr-only">Switch label</span>
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="mt-5  flex">
                        <h4 className="text-md mb-5 w-96 uppercase text-gray-400">
                          Pre-authorization type
                        </h4>
                        <h4 className="text-md mb-5 ml-32 uppercase text-gray-400">sharing</h4>
                      </div>

                      <div className="flex">
                        <div className="flex ">
                          <div className="mr-9  flex ">
                            {benefitRequirePreAuth ? (
                              <div className="flex">
                                <div className="mr-1  w-48">
                                  <input
                                    type="radio"
                                    name="optionEditpa"
                                    id="Payeropt"
                                    className="hidden p-20"
                                    value="Payer"
                                    onChange={(e) => setBenefitPreAuthType(e.target.value)}
                                  />
                                  <label
                                    htmlFor="Payeropt"
                                    className="inline-block cursor-pointer  rounded-md border-2 p-5 px-16 py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                                  >
                                    Payer
                                  </label>
                                </div>
                                <div className="ml-1 w-48">
                                  <input
                                    type="radio"
                                    name="optionEditpa"
                                    id="hropt"
                                    className="hidden"
                                    value="hr"
                                    onChange={(e) => setBenefitPreAuthType(e.target.value)}
                                  />
                                  <label
                                    htmlFor="hropt"
                                    className="inline-block cursor-pointer rounded-md border-2 px-16  py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                                  >
                                    HR
                                  </label>
                                </div>
                              </div>
                            ) : (
                              <div className="flex w-96">No Pre-Auth</div>
                            )}
                            <div className="ml-10 mr-1 w-48">
                              <input
                                type="radio"
                                name="optionEdit-sharing"
                                id="Family"
                                className="hidden"
                                value="Family"
                                onChange={(e) => setBenefitSharing(e.target.value)}
                              />
                              <label
                                htmlFor="Family"
                                className="inline-block cursor-pointer rounded-md border-2 px-16  py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                              >
                                Family
                              </label>
                            </div>
                            <div className="mr-5 w-48">
                              <input
                                type="radio"
                                name="optionEdit-sharing"
                                id="Individual"
                                className="hidden"
                                value="Individual"
                                onChange={(e) => setBenefitSharing(e.target.value)}
                              />
                              <label
                                htmlFor="Individual"
                                className="inline-block cursor-pointer rounded-md border-2 px-16  py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                              >
                                Individual
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>

                      <h4 className="text-md mb-5 mt-5 uppercase text-gray-400">
                        co-payment required
                      </h4>
                      <div className="flex ">
                        <div className="mb-5 mt-5 flex">
                          <div className="form-switch">
                            <input
                              type="checkbox"
                              id="switch-copayment"
                              className="sr-only"
                              checked={benefitCopaymentReq}
                              onChange={() => setBenefitCopaymentReq(!benefitCopaymentReq)}
                            />
                            <label className="bg-gray-400" htmlFor="switch-copayment">
                              <span className="bg-white shadow-sm" aria-hidden="true"></span>
                              <span className="sr-only">Switch label</span>
                            </label>
                          </div>
                          <div className="ml-2 text-sm italic text-gray-400">
                            {benefitCopaymentReq ? "On" : "Off"}
                          </div>
                        </div>
                        <div className="mb-5 ml-5 mt-5 w-6/12">
                          {benefitCopaymentReq ? (
                            <input
                              id="default"
                              className="form-input w-full"
                              type="number"
                              placeholder="Co-Pay amount"
                              value={benefitCopayAmount}
                              onChange={(e) => setBenefitCopayAmount(Number(e.target.value))}
                            />
                          ) : (
                            <div className="flex w-96">No Co-payment</div>
                          )}
                        </div>
                      </div>

                      <h4 className="text-md mb-5 mt-5 uppercase text-gray-400">waiting period</h4>
                      <div className="flex">
                        <div className="flex ">
                          <div className="flex ">
                            <div className="">
                              <input
                                type="radio"
                                name="optionEdit-waiting"
                                id="0Days"
                                className="hidden p-20"
                                value="ZERO_DAYS"
                                onChange={(e) => setBenefitWaitingPeriod(e.target.value)}
                              />
                              <label
                                htmlFor="0Days"
                                className="inline-block w-36  cursor-pointer rounded-md border-2 p-5 px-5 py-3 label-checked:text-gray-100  label-background:bg-blue-900"
                              >
                                0 Days
                              </label>
                            </div>
                            <div className="">
                              <input
                                type="radio"
                                name="optionEdit-waiting"
                                id="30Days"
                                className="hidden"
                                value="THIRTY_DAYS"
                                onChange={(e) => setBenefitWaitingPeriod(e.target.value)}
                              />
                              <label
                                htmlFor="30Days"
                                className="ml-3 inline-block w-36 cursor-pointer rounded-md  border-2 px-5 py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                              >
                                30 Days
                              </label>
                            </div>
                            <div className=" ">
                              <input
                                type="radio"
                                name="optionEdit-waiting"
                                id="60Days"
                                className="hidden"
                                value="SIXTY_DAYS"
                                onChange={(e) => setBenefitWaitingPeriod(e.target.value)}
                              />
                              <label
                                htmlFor="60Days"
                                className="ml-3 inline-block w-36 cursor-pointer rounded-md  border-2 px-5 py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                              >
                                60 Days
                              </label>
                            </div>
                            <div className=" mr-1">
                              <input
                                type="radio"
                                name="optionEdit-waiting"
                                id="90Days"
                                className="hidden"
                                value="NINETY_DAYS"
                                onChange={(e) => setBenefitWaitingPeriod(e.target.value)}
                              />
                              <label
                                htmlFor="90Days"
                                className="ml-3 inline-block w-36 cursor-pointer rounded-md  border-2 px-5 py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                              >
                                90 Days
                              </label>
                            </div>
                            <div className="">
                              <input
                                type="radio"
                                name="optionEdit-waiting"
                                id="180Days"
                                className="hidden"
                                value="ONE_HUNDRED_EIGHTY_DAYS"
                                onChange={(e) => setBenefitWaitingPeriod(e.target.value)}
                              />
                              <label
                                htmlFor="180Days"
                                className="ml-3 inline-block w-36 cursor-pointer rounded-md  border-2 px-5 py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                              >
                                180 Days
                              </label>
                            </div>
                            <div className="">
                              <input
                                type="radio"
                                name="optionEdit-waiting"
                                id="270Days"
                                className="hidden"
                                value="NINE_MONTHS"
                                onChange={(e) => setBenefitWaitingPeriod(e.target.value)}
                              />
                              <label
                                htmlFor="270Days"
                                className="ml-3 inline-block w-36 cursor-pointer rounded-md  border-2 px-5 py-3 label-checked:text-gray-100 label-background:bg-blue-900"
                              >
                                9 Months
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>

                      <h4 className="text-md mb-5 mt-5 uppercase text-gray-400">main Benefit</h4>

                      <div className="flex">
                        <div className="mr-9  flex ">
                          <div className="mr-10  sm:mb-0 ">
                            <div className=" mb-5">
                              <Select
                                className="react-select"
                                classNamePrefix="react-select"
                                name="principalname"
                                defaultValue={mainBenefitsData}
                                options={mainBenefitsData}
                                value={mainBenefitSelected}
                                onChange={(value) => changeMainBenefitName(value)}
                                placeholder="Select Main Benefit"
                                closeMenuOnSelect={true}
                                isClearable={true}
                                isSearchable={true}
                                components={animatedComponents}
                              />{" "}
                            </div>
                          </div>
                          <button
                            className="btn mb-5 w-10 border-gray-200 px-2 text-gray-600 hover:border-gray-300 "
                            onClick={(e) => handleRefreshMainBenefitsInCategory(e)}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-6 w-6"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                              strokeWidth="2"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                              />
                            </svg>
                          </button>
                          {/* <div className="w-48  mr-5">
                              <input
                                type="radio"
                                name="optionEdit-mainbenefit"
                                id="Optical"
                                className="hidden p-20"
                              />
                              <label
                                htmlFor="Optical"
                                className="inline-block cursor-pointer  p-5 label-checked:text-gray-100 label-background:bg-blue-900 border-2 px-16 py-3 rounded-md"
                              >
                                Optical
                              </label>
                            </div>
                            <div className="w-48 ml-5 mr-5">
                              <input
                                type="radio"
                                name="optionEdit-mainbenefit"
                                id="Inpatient"
                                className="hidden"
                              />
                              <label
                                htmlFor="Inpatient"
                                className="inline-block cursor-pointer label-checked:text-gray-100 label-background:bg-blue-900 border-2  px-16 py-3 rounded-md"
                              >
                                Inpatient
                              </label>
                            </div>
                            <div className="w-48 mr-5">
                              <input
                                type="radio"
                                name="optionEdit-mainbenefit"
                                id="Outpatient"
                                className="hidden"
                              />
                              <label
                                htmlFor="Outpatient"
                                className="inline-block cursor-pointer label-checked:text-gray-100 label-background:bg-blue-900 border-2  px-16 py-3 rounded-md"
                              >
                                Outpatient
                              </label>
                            </div> */}
                        </div>
                      </div>
                    </div>
                    {/* Modal footer */}
                    <div className="mt-5 px-5 py-4 pb-10">
                      <div className="mb-5 flex justify-center text-red-600">{error}</div>
                      <div className=" w-full flex-wrap justify-center space-x-2 sm:flex">
                        <button
                          className="btn-sm w-44 border-gray-200 px-16 py-2 text-gray-600 hover:border-gray-300"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            setCategoryBenefitModalOpen(false);
                          }}
                        >
                          Clear
                        </button>
                        <button
                          type="submit"
                          className="btn-sm w-44 bg-blue-500 px-16 py-2 text-white hover:bg-blue-600"
                        >
                          Save
                        </button>
                      </div>
                    </div>
                  </form>
                </ModalMedium>
              </div>
            </div>

            {/*  Category Provider Exclusion modal */}
            <div className="space-x-1">
              <div>
                <ModalBasic
                  id="feedback-modal"
                  modalOpen={categoryProviderExclusionModalOpen}
                  setModalOpen={setCategoryProviderExclusionModalOpen}
                  title="Category"
                >
                  {/* Modal content */}

                  <div className="px-10 pb-1 pt-4 sm:mx-auto sm:items-center sm:justify-center">
                    <h3 className="mb-5 text-lg  uppercase text-gray-400">
                      Provider exclusion details
                    </h3>

                    {/* Options */}
                    <div className="">
                      <div className="sm:flex sm:items-center sm:justify-center">
                        <label className="mb-1 mr-5 block text-sm font-medium" htmlFor="name">
                          Select Provider:
                        </label>
                        <AsyncPaginate
                          className="react-select sm:w-1/3 "
                          classNamePrefix="react-select"
                          name="providername"
                          value={providerExclusion}
                          loadOptions={loadProviderOptions}
                          onChange={(value) => changeProviderName(value)}
                          placeholder="Select Provider Name"
                          isClearable={true}
                        />
                        {/* <select
                          onChange={handleProviderChange}
                          name="country"
                          className=" w-full sm:w-6/12 form-input"
                        >
                          <option value="Select Country" selected disabled>
                            Select Provider
                          </option>
                          {providers.map((provider) => (
                            <option value={provider.id}>{provider.name}</option>
                          ))}
                        </select> */}
                      </div>
                    </div>
                  </div>
                  {/* Modal footer */}
                  <div className="mt-10 px-5 py-4">
                    <div className="mb-5 flex justify-center text-red-600">{error}</div>
                    <div className="flex flex-wrap justify-center space-x-2">
                      <button
                        className="btn-sm w-44 border-gray-200 px-16 py-2 text-gray-600 hover:border-gray-300"
                        onClick={(e) => {
                          e.stopPropagation();
                          setCategoryProviderExclusionModalOpen(false);
                        }}
                      >
                        Clear
                      </button>
                      <button
                        className="btn-sm w-44 bg-blue-500 px-16 py-2 text-white hover:bg-blue-600"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCategoryProvExclusionSubmit();
                        }}
                      >
                        Save
                      </button>
                    </div>
                  </div>
                </ModalBasic>
              </div>
            </div>

            {/*  Category Co-Payment  modal */}
            <div className="space-x-1">
              <div>
                <ModalBasic
                  id="feedback-modal"
                  modalOpen={categoryCopaymentModalOpen}
                  setModalOpen={setCategoryCopaymentModalOpen}
                  title="Category"
                >
                  {/* Modal content */}
                  <div className="justify-center px-10 pb-1 pt-4">
                    <h3 className="mb-5 text-lg  uppercase text-gray-400">co-payment details</h3>

                    {/* Options */}
                    <div className="sm:flex sm:content-center sm:items-center sm:justify-center">
                      <div className="mr-5 w-full">
                        <label className="mb-1 block text-sm font-medium" htmlFor="name">
                          Select Provider
                        </label>
                        <AsyncPaginate
                          className="react-select w-full"
                          classNamePrefix="react-select"
                          name="providername"
                          value={providerCopayment}
                          loadOptions={loadProviderOptions}
                          onChange={(value) => changeProviderNameCopayment(value)}
                          placeholder="Select Provider Name"
                          isClearable={true}
                        />
                      </div>
                      <div className="w-full sm:ml-3">
                        <div>
                          <label className="mb-1 block text-sm font-medium" htmlFor="copayamount">
                            Amount
                          </label>
                          <input
                            id="copayamount"
                            className="form-input w-full"
                            type="number"
                            placeholder="Enter Copay Amount"
                            value={copayAmount}
                            onChange={(e) => setCopayAmount(e.target.value)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* Modal footer */}
                  <div className="mt-10 px-5 py-4">
                    <div className="mb-5 flex justify-center text-red-600">{error}</div>
                    <div className="flex flex-wrap justify-center space-x-2">
                      <button
                        className="btn mt-5 w-44 border-gray-200 px-16 py-2 text-gray-600 hover:border-gray-300"
                        onClick={(e) => {
                          e.stopPropagation();
                          setCategoryCopaymentModalOpen(false);
                        }}
                      >
                        Clear
                      </button>
                      <button
                        type="submit"
                        className="btn mt-5 w-44 bg-blue-500 px-16 py-2 text-white hover:bg-blue-600"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCategoryCopaymentSubmit(e);
                        }}
                      >
                        Save
                      </button>
                    </div>
                  </div>
                </ModalBasic>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};
const mapStateToProps = (state) => ({
  categoryBenefits: state.policyCategory.categoryBenefits,
  categoryMembers: state.policyCategory.categoryMembers,
  categoryCopayemnt: state.policyCategory.categoryCopayemnt,
  success: state.policy.success,
  message: state.policy,
  loading: state.policy.loading,
});
const mapDispatchToProps = (dispatch) => ({
  getPolicyCategoryBenefit: (policyCategoryId, page) =>
    dispatch(getPolicyCategoryBenefit(policyCategoryId, page)),

  getPolicyCategoryPrincipalMembers: (policyCategoryId) =>
    dispatch(getPolicyCategoryPrincipalMembers(policyCategoryId)),

  getPolicyCategoryMembers: (policyCategoryId, page) =>
    dispatch(getPolicyCategoryMembers(policyCategoryId, page)),

  getCategoryMainBenefits: (policyCategoryId) =>
    dispatch(getCategoryMainBenefits(policyCategoryId)),

  addPolicyCategoryBenefit: (benefitsObject) => dispatch(addPolicyCategoryBenefit(benefitsObject)),

  addPolicyCategoryMembers: (memberObject) => dispatch(addPolicyCategoryMembers(memberObject)),

  addPolicyCategoryProviderExclusion: (catProvExclusionObj) =>
    dispatch(addPolicyCategoryProviderExclusion(catProvExclusionObj)),

  addPolicyCategoryCopayment: (copayObj) => dispatch(addPolicyCategoryCopayment(copayObj)),

  processCategoryBenefits: (categoryId) => dispatch(processCategoryBenefits(categoryId)),

  getPolicyCategoryProviderExclusion: (policyCategoryId, page) =>
    dispatch(getPolicyCategoryProviderExclusion(policyCategoryId, page)),

  getPolicyCategoryCopayments: (policyCategoryId, page) =>
    dispatch(getPolicyCategoryCopayments(policyCategoryId, page)),
  getPolicyCategoryProviderRestriction: (policyCategoryId) =>
    dispatch(getPolicyCategoryProviderRestriction(policyCategoryId)),

  getPayers: () => dispatch(getPayers()),
  getProviders: (page) => dispatch(getProviders(page)),
});
export default connect(mapStateToProps, mapDispatchToProps)(PolicyCategoryOverview);
