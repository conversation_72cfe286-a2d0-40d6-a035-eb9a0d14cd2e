import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { useTransferBenefitBalanceMutation } from "~lib/api";
import { TransferBenefitBalanceRequest } from "~lib/api/schema";
import { BeneficiaryBenefit } from "~lib/api/types";
import { FieldWrapper, Input, Select, TextArea } from "~lib/components";
import { Form } from "~lib/components/Form";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import {
  formatMoney,
  isErrorWithMessage,
  isFetchBaseQueryError,
  queryError,
  responseError,
} from "~lib/utils";
import UserService from "../../../services/UserService";

type Inputs = {
  amount: number;
  reason: string;
  destinationBenefitId: string | undefined;
};

type Props = {
  sourceBenefitId: number;
  beneficiaryBenefits: BeneficiaryBenefit[];
  closeModal: () => void;
};

export default function TransferBenefit({
  beneficiaryBenefits,
  sourceBenefitId,
  closeModal,
}: Props) {
  // TODO: Handle non-existent source benefit id
  const sourceBenefit = beneficiaryBenefits.find(
    (b) => b.id == sourceBenefitId,
  ) as BeneficiaryBenefit;


  const methods = useForm<Inputs>({
    mode: "onBlur",
    defaultValues: {
      amount: 0,
      reason: "",
      destinationBenefitId: undefined,
    },
  });

  const { reset, watch } = methods;

  const form = watch();

  const destinationBenefit = form.destinationBenefitId
    ? beneficiaryBenefits.find((b) => b.id.toString() == form.destinationBenefitId)
    : undefined;

  const username = UserService.getUsername();

  const [transferBenefitBalance, { isLoading: isTransferBenefitBalanceLoading }] =
    useTransferBenefitBalanceMutation();

  async function handleSubmit(form: Inputs) {
    try {
      const { reason, amount, destinationBenefitId: destinationBenefitIdRaw } = form;

      const _destinationBenefitId = destinationBenefitIdRaw
        ? window.parseInt(destinationBenefitIdRaw)
        : undefined;

      if (!destinationBenefit) {
        throw new Error("Source benefit not selected");
      }

      const { beneficiaryId, benefitId, aggregateId, memberNumber } = sourceBenefit;

      const request: TransferBenefitBalanceRequest = {
      transferFromId:sourceBenefitId,
      transferToId:form.destinationBenefitId,
      transferAmount:amount,
      transferReason:reason,
      transferBy:username
      };

      const response = await transferBenefitBalance(request);
      const message = responseError(response);

      if (message) {
        throw new Error(message);
      }

      toast.success("Benefit balance transferred successfully");
    } catch (error) {
      let message = "Something went wrong. Please try again.";
      if (isFetchBaseQueryError(error) || isErrorWithMessage(error)) {
        message = queryError(error) || message;
      }

      toast.error(message);
      console.error(error);
    }

    closeModal();
  }

  return (
    <div className="px-6 py-2">
      <h2 className="mb-2 text-base font-medium">Source Benefit</h2>

      <div className="mb-4 flex gap-8">
        <div>
          <p className="font-medium">Name</p>
          <p className="text-gray-500">{sourceBenefit.benefitName}</p>
        </div>

        <div>
          <p className="font-medium">Balance</p>
          <p className="text-gray-500">{formatMoney(sourceBenefit.balance)}</p>
        </div>
      </div>

      <Form
        methods={methods}
        onSubmit={handleSubmit}
        className="grid grid-cols-1 gap-4 lg:grid-cols-2"
      >
        <div>
          <FieldWrapper
            name="destinationBenefitId"
            label="Destination Benefit"
            className="max-w-xs flex-shrink flex-grow"
          >
            <Select
              options={
                beneficiaryBenefits
                  ?.filter((b) => b.id != sourceBenefitId)
                  .map((b) => ({
                    label: b.benefitName,
                    value: b.id,
                  })) || []
              }
              className="min-w-44"
              isDisabled={!beneficiaryBenefits?.length}
            />
          </FieldWrapper>
        </div>

        <FieldWrapper name="amount" label="Amount (KES)" required>
          <Input
            type="number"
            options={{
              validate: {
                gtZero: (value) => {
                  if (value < 1) {
                    return "Amount must be greater than zero";
                  }
                  return true;
                },
                amountLteSourceBalance: (value) => {
                  if (value > sourceBenefit.balance) {
                    return "Amount cannot be greater than source balance";
                  }
                  return true;
                },
              },
            }}
          />
        </FieldWrapper>

        {destinationBenefit && (
          <div className="mb-4 flex gap-8">
            <div>
              <p className="font-medium">Old Balance</p>
              <p className="text-gray-500">{formatMoney(destinationBenefit.balance)}</p>
            </div>

            <div>
              <p className="font-medium">Balance</p>
              <p className="text-gray-500">
                {formatMoney(destinationBenefit.balance + form.amount)}
              </p>
            </div>
          </div>
        )}

        <FieldWrapper name="reason" label="Reason" required>
          <TextArea />
        </FieldWrapper>

        <div className="flex justify-end gap-2 lg:col-span-2">
          <button
            type="reset"
            className="rounded border border-gray-200 px-4 py-2 font-medium text-gray-500 enabled:hover:border-gray-300 disabled:opacity-80"
            onClick={() => {
              reset();
            }}
            disabled={isTransferBenefitBalanceLoading}
          >
            Reset
          </button>

          <button
            type="submit"
            className="flex items-center gap-2 rounded bg-blue-500 px-4 py-2 font-medium text-white enabled:hover:bg-blue-700 disabled:opacity-80"
            disabled={isTransferBenefitBalanceLoading}
          >
            {isTransferBenefitBalanceLoading && <LoadingIcon className="h-4 w-4 text-white" />}
            <span>Submit</span>
          </button>
        </div>
      </Form>
    </div>
  );
}
