import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { useTopUpBenefitMutation } from "~lib/api";
import { TopUpBenefitRequest } from "~lib/api/schema";
import { BeneficiaryBenefit } from "~lib/api/types";
import { FieldWrapper, Input, TextArea } from "~lib/components";
import { Form } from "~lib/components/Form";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import {
  formatMoney,
  isErrorWithMessage,
  isFetchBaseQueryError,
  queryError,
  responseError,
} from "~lib/utils";
import UserService from "../../../services/UserService";

type Inputs = {
  amount: number;
  reason: string;
};

type Props = {
  benefit: BeneficiaryBenefit;
  closeModal: () => void;
};

export default function TopUpBenefit({ benefit, closeModal }: Props) {
  const methods = useForm<Inputs>({
    mode: "onBlur",
    defaultValues: {
      amount: 0,
      reason: "",
    },
  });

  const { reset } = methods;

  const username = UserService.getUsername();

  const [topUpBenefit, { isLoading: isTopUpBenefitLoading }] = useTopUpBenefitMutation();

  async function handleSubmit(form: Inputs) {
    const { reason, amount } = form;
    const { benefitId, beneficiaryId, aggregateId, memberNumber,id } = benefit;

    const request:TopUpBenefitRequest = {
      topUpReason: reason,
      topUpAmount:amount,
      topUpBy:username,
      //benefitId,
      //beneficiaryId,
      id: id,
      // memberNumber,
      // aggregateId,
    };

    try {
      const response = await topUpBenefit(request);
      const message = responseError(response);

      if (message) {
        throw new Error(message);
      }

      toast.success("Benefit updated successfully");
    } catch (error) {
      let message = "Something went wrong. Please try again.";
      if (isFetchBaseQueryError(error) || isErrorWithMessage(error)) {
        message = queryError(error) || message;
      }

      toast.error(message);
      console.error(error);
    }

    closeModal();
  }

  return (
    <div className="px-6 py-2">
      <div className="mb-4 flex gap-8">
        <div>
          <p className="font-medium">Benefit Name</p>
          <p className="text-gray-500">{benefit.benefitName}</p>
        </div>

        <div>
          <p className="font-medium">Balance</p>
          <p className="text-gray-500">{formatMoney(benefit.balance)}</p>
        </div>
      </div>

      <Form
        methods={methods}
        onSubmit={handleSubmit}
        className="grid grid-cols-1 gap-4 lg:grid-cols-2"
      >
        <FieldWrapper name="amount" label="Amount (KES)" required>
          <Input
            type="number"
            options={{
              min: {
                value: 1,
                message: "Amount must be greater than 0",
              },
            }}
          />
        </FieldWrapper>

        <FieldWrapper name="reason" label="Reason" required>
          <TextArea />
        </FieldWrapper>

        <div className="flex justify-end gap-2 lg:col-span-2">
          <button
            type="reset"
            className="rounded border border-gray-200 px-4 py-2 font-medium text-gray-500 enabled:hover:border-gray-300 disabled:opacity-80"
            onClick={() => {
              reset();
            }}
            disabled={isTopUpBenefitLoading}
          >
            Reset
          </button>

          <button
            type="submit"
            className="flex items-center gap-2 rounded bg-blue-500 px-4 py-2 font-medium text-white enabled:hover:bg-blue-700 disabled:opacity-80"
            disabled={isTopUpBenefitLoading}
          >
            {isTopUpBenefitLoading && <LoadingIcon className="h-4 w-4 text-white" />}
            <span>Submit</span>
          </button>
        </div>
      </Form>
    </div>
  );
}
