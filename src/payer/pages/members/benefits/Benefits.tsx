import { Menu, Transition } from "@headlessui/react";
import { EllipsisVerticalIcon } from "@heroicons/react/24/outline";
import { Fragment, useEffect, useState } from "react";
import {
  Beneficiary,
  BeneficiaryBenefit,
  BenefitStatus,
  benefitStatusLabels,
} from "~lib/api/types";
import { Empty, Modal } from "~lib/components";
import StatusBadge from "~lib/components/StatusBadge";
import { headlessUiMenuTransitionAttrs } from "~lib/constants";
import { BenefitNode, Status } from "~lib/types";
import { clsx, formatMoney, treeify } from "~lib/utils";
import UserService from "../../../services/UserService";
import { MemberDetailsAction } from "../MemberDetails";
import useMemberAction from "../useMemberAction";
import TopUpBenefit from "./TopUp";
import TransferBenefit from "./Transfer";
import ToggleBenefitStatus from "./UpdateStatus";

interface Props {
  beneficiaryBenefits: BeneficiaryBenefit[];
  beneficiary: Beneficiary;
}

enum PageModal {
  TOP_UP,
  TRANSFER,
  TOGGLE_STATUS,
}

export default function Benefits({ beneficiaryBenefits, beneficiary }: Props) {
  const USER_HAS_MEMBERSHIP_BENEFIT_SUSPEND_ROLE = UserService.hasRole([
    "MEMBERSHIP_BENEFIT_SUSPEND_ROLE",
  ]);
  const USER_HAS_MEMBERSHIP_BENEFIT_TOPUP_ROLE = UserService.hasRole([
    "MEMBERSHIP_BENEFIT_TOPUP_ROLE",
  ]);
  const USER_HAS_MEMBERSHIP_BENEFIT_TRANSFER_ROLE = UserService.hasRole([
    "MEMBERSHIP_BENEFIT_TRANSFER_ROLE",
  ]);
  const USER_HAS_BENEFIT_ENHANCEMENT_ROLE =
    USER_HAS_MEMBERSHIP_BENEFIT_SUSPEND_ROLE ||
    USER_HAS_MEMBERSHIP_BENEFIT_TOPUP_ROLE ||
    USER_HAS_MEMBERSHIP_BENEFIT_TRANSFER_ROLE;

  const [activeModal, setActiveModal] = useState<PageModal | undefined>();
  const [selectedBenefit, setSelectedBenefit] = useState<BeneficiaryBenefit | undefined>();

  const benefitStatusMap: Record<BenefitStatus, Status> = {
    [BenefitStatus.ACTIVE]: Status.SUCCESS,
    [BenefitStatus.SUSPENDED]: Status.FAILURE,
    [BenefitStatus.CANCELED]: Status.FAILURE,
    [BenefitStatus.DEPLETED]: Status.FAILURE,
    [BenefitStatus.THRESHOLD_HIT]: Status.PENDING,
  };

  function selectBenefit(benefit: BeneficiaryBenefit, modal: PageModal) {
    setActiveModal(modal);
    setSelectedBenefit(benefit);
  }

  function closeModal() {
    setActiveModal(undefined);
    setSelectedBenefit(undefined);
  }

  const { action, clearAction } = useMemberAction();

  useEffect(() => {
    if (action == MemberDetailsAction.BENEFIT_ENHANCEMENT) {
      clearAction();
    }
  }, [action, clearAction]);

  const benefitTree = treeify(beneficiaryBenefits);

  const renderBenefit = (b: BenefitNode, level: number): JSX.Element => {
    // WARN: The safelist in the tailwind.config.js file must be updated if levels are added or removed
    const padding = 4 * (level + 2);

    return (
      <>
        <tr key={b.id + b.benefitId}>
          <td
            className={clsx(
              "px-2 py-4",
              level == 0 && "font-medium first:pl-8 last:pr-8",
              level > 0 && `first:pl-${padding} last:pr-${padding}`,
            )}
          >
            {b.benefitName}
          </td>
          <td className="px-2 py-4 first:pl-8 last:pr-8">{formatMoney(b.initialLimit)}</td>
          <td className="px-2 py-4 first:pl-8 last:pr-8">{formatMoney(b.utilization)}</td>
          <td className="px-2 py-4 first:pl-8 last:pr-8">{formatMoney(b.balance)}</td>
          <td className="px-2 py-4 first:pl-8 last:pr-8">
            <StatusBadge status={benefitStatusMap[b.status] ?? Status.OTHER}>
              {benefitStatusLabels[b.status] ?? "Unknown"}
            </StatusBadge>
          </td>
          {USER_HAS_BENEFIT_ENHANCEMENT_ROLE && (
            <td>
              <Menu as="div" className="relative inline-block text-left">
                <div>
                  <Menu.Button className="inline-flex w-full items-center justify-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-700">
                    <EllipsisVerticalIcon className="h-6 w-6" />
                  </Menu.Button>
                </div>

                <Transition as={Fragment} {...headlessUiMenuTransitionAttrs}>
                  <Menu.Items className="absolute right-0 z-10 mt-2 origin-top-left rounded-md border border-gray-200 bg-white focus:outline-none">
                    <Menu.Item>
                      {({ active }) => (
                        <button
                          className={clsx(
                            "group flex w-full items-center px-6 py-4 text-sm disabled:cursor-not-allowed disabled:opacity-60",
                            active && "bg-gray-100",
                            b.status == BenefitStatus.ACTIVE ? "text-red-500" : "text-green-600",
                          )}
                          onClick={() => {
                            selectBenefit(b, PageModal.TOGGLE_STATUS);
                          }}
                        >
                          {b.status == BenefitStatus.ACTIVE ? "Suspend" : "Activate"}
                        </button>
                      )}
                    </Menu.Item>

                    {b.parent === null ? (
                      <Menu.Item>
                        {({ active }) => (
                          <button
                            className={clsx(
                              "group flex w-full items-center px-6 py-4 text-sm",
                              active && "bg-gray-100",
                            )}
                            onClick={() => {
                              selectBenefit(b, PageModal.TOP_UP);
                            }}
                          >
                            Top&nbsp;Up
                          </button>
                        )}
                      </Menu.Item>
                    ) : null}
                    {b.transferable && (
                      <Menu.Item>
                        {({ active }) => (
                          <button
                            className={clsx(
                              "group flex w-full items-center px-6 py-4 text-sm disabled:cursor-not-allowed disabled:opacity-60",
                              active && "bg-gray-100",
                            )}
                            onClick={() => {
                              selectBenefit(b, PageModal.TRANSFER);
                            }}
                            disabled={beneficiaryBenefits.length < 2}
                          >
                            Transfer
                          </button>
                        )}
                      </Menu.Item>
                    )}
                  </Menu.Items>
                </Transition>
              </Menu>
            </td>
          )}
        </tr>

        {b.children.map((child) => renderBenefit(child, level + 1))}
      </>
    );
  };

  return (
    <div className="">
      {beneficiaryBenefits.length > 1 ? (
        <div
          className="overflow-x-auto overflow-y-visible bg-white pb-16 text-gray-600"
          // TODO: Remove responsiveness hack
          style={{ maxWidth: "100vw" }}
        >
          <table className="w-full">
            <thead className="text-left">
              <tr className="bg-gray-100">
                <th className="px-2 py-4 first:pl-8 last:pr-8">Name</th>
                {/* <th className="px-2 py-4 first:pl-8 last:pr-8">Sharing</th> */}
                <th className="px-2 py-4 first:pl-8 last:pr-8">Limit</th>
                {/* TODO: Highlight balance below threshold */}
                <th className="px-2 py-4 first:pl-8 last:pr-8">Utilization</th>
                <th className="px-2 py-4 first:pl-8 last:pr-8">Balance</th>
                <th className="px-2 py-4 first:pl-8 last:pr-8">Status</th>
                {USER_HAS_BENEFIT_ENHANCEMENT_ROLE && (
                  <th className="px-2 py-4 first:pl-8 last:pr-8">Actions</th>
                )}
              </tr>
            </thead>

            <tbody>{benefitTree.map((b) => renderBenefit(b, 0))}</tbody>
          </table>
        </div>
      ) : (
        <Empty message="Member has no benefits" />
      )}

      <Modal
        id="toggle-benefit-status"
        modalOpen={activeModal == PageModal.TOGGLE_STATUS}
        title={
          selectedBenefit && selectedBenefit.status == BenefitStatus.ACTIVE
            ? "Suspend Benefit"
            : "Activate Benefit"
        }
        onClose={() => {
          closeModal();
        }}
        size="lg"
      >
        {selectedBenefit ? (
          <ToggleBenefitStatus
            beneficiaryBenefit={selectedBenefit}
            beneficiary={beneficiary}
            closeModal={closeModal}
          />
        ) : (
          <Empty message="No benefit selected" />
        )}
      </Modal>

      <Modal
        id="top-up-benefit"
        modalOpen={activeModal == PageModal.TOP_UP}
        title="Top Up Benefit"
        onClose={() => {
          closeModal();
        }}
        size="lg"
      >
        {selectedBenefit ? (
          <TopUpBenefit benefit={selectedBenefit} closeModal={closeModal} />
        ) : (
          <Empty message="No benefit selected" />
        )}
      </Modal>

      <Modal
        id="transfer-benefit"
        modalOpen={activeModal == PageModal.TRANSFER}
        title="Transfer Benefit Balance"
        onClose={() => {
          closeModal();
        }}
        size="lg"
      >
        {selectedBenefit ? (
          <TransferBenefit
            beneficiaryBenefits={beneficiaryBenefits}
            sourceBenefitId={selectedBenefit.id}
            closeModal={closeModal}
          />
        ) : (
          <Empty message="No benefit selected" />
        )}
      </Modal>
    </div>
  );
}
