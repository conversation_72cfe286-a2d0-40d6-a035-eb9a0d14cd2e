import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { useUpdateBeneficiaryBenefitStatusMutation } from "~lib/api";
import { BeneficiaryBenefitStatusUpdateRequest } from "~lib/api/schema";
import {
  Beneficiary,
  BeneficiaryBenefit,
  BeneficiaryType,
  BenefitSharing,
  BenefitStatus,
} from "~lib/api/types";
import { FieldWrapper, RadioGroup, TextArea } from "~lib/components";
import { Form } from "~lib/components/Form";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import {
  formatMoney,
  isErrorWithMessage,
  isFetchBaseQueryError,
  queryError,
  responseError,
} from "~lib/utils";
import UserService from "../../../services/UserService";
import { Scope, scopeLabels } from "../types";

type Inputs = {
  reason: string;
  scope: Scope;
};

type Props = {
  beneficiary: Beneficiary;
  beneficiaryBenefit: BeneficiaryBenefit;
  closeModal: () => void;
};

export default function ToggleBenefitStatus({
  beneficiaryBenefit,
  beneficiary,
  closeModal,
}: Props) {
  const methods = useForm<Inputs>({
    mode: "onBlur",
    defaultValues: {
      reason: "",
      scope: Scope.INDIVIDUAL,
    },
  });

  const { reset } = methods;

  const username = UserService.getUsername();

  const [updateBeneficiaryBenefitStatus, { isLoading }] =
    useUpdateBeneficiaryBenefitStatusMutation();

  async function handleSubmit(form: Inputs) {
    const { reason, scope } = form;

    const request: BeneficiaryBenefitStatusUpdateRequest = {
      reason,
      updateBy: username,
      updateList: [
        {
          benefitStatus:
            beneficiaryBenefit.status == BenefitStatus.ACTIVE
              ? BenefitStatus.SUSPENDED
              : BenefitStatus.ACTIVE,
          benefitIds: [beneficiaryBenefit.benefitId],
          beneficiaryId: beneficiary.id,
          updateType: scope,
        },
      ],
    };

    try {
      const response = await updateBeneficiaryBenefitStatus(request);
      const message = responseError(response);
      if (message) {
        throw new Error(message);
      }

      toast.success("Benefit updated successfully");
    } catch (error) {
      let message = "Something went wrong. Please try again.";
      if (isFetchBaseQueryError(error) || isErrorWithMessage(error)) {
        message = queryError(error) || message;
      }

      toast.error(message);
      console.error(error);
    }

    closeModal();
  }

  return (
    <div className="px-6 py-2">
      <div className="mb-4 flex gap-8">
        <div>
          <p className="font-medium">Benefit Name</p>
          <p className="text-gray-500">{beneficiaryBenefit.benefitName}</p>
        </div>

        <div>
          <p className="font-medium">Balance</p>
          <p className="text-gray-500">{formatMoney(beneficiaryBenefit.balance)}</p>
        </div>
      </div>

      <Form
        methods={methods}
        onSubmit={handleSubmit}
        className="grid grid-cols-1 gap-4 lg:grid-cols-2"
      >
        {beneficiaryBenefit.sharing === BenefitSharing.FAMILY &&
          beneficiary.beneficiaryType === BeneficiaryType.PRINCIPAL && (
            // TODO: Disable if family size is less than 2
            <FieldWrapper name="scope" label="Option">
              <RadioGroup
                solid
                orientation="horizontal"
                options={Object.values(Scope).map((scope) => ({
                  label: scopeLabels[scope] || "Unknown",
                  value: scope,
                }))}
                // isDisabled={familySize < 1}
              />
            </FieldWrapper>
          )}

        <FieldWrapper name="reason" label="Reason" required>
          <TextArea />
        </FieldWrapper>

        <div className="flex justify-end gap-2 lg:col-span-2">
          <button
            type="reset"
            className="rounded border border-gray-200 px-4 py-2 font-medium text-gray-500 enabled:hover:border-gray-300 disabled:opacity-80"
            onClick={() => {
              reset();
            }}
            disabled={isLoading}
          >
            Reset
          </button>

          <button
            type="submit"
            className="flex items-center gap-2 rounded bg-blue-500 px-4 py-2 font-medium text-white enabled:hover:bg-blue-700 disabled:opacity-80"
            disabled={isLoading}
          >
            {isLoading && <LoadingIcon className="h-4 w-4 text-white" />}
            <span>Submit</span>
          </button>
        </div>
      </Form>
    </div>
  );
}
