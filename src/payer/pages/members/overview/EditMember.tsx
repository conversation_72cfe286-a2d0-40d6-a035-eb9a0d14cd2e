import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import {
  useChangeBeneficiaryCategoryMutation,
  useGetPolicyCategoriesQuery,
  useUpdateMemberMutation,
} from "~lib/api";
import { UpdateMemberRequest } from "~lib/api/schema";
import {
  Beneficiary,
  BeneficiaryType,
  beneficiaryTypeLabels,
  Gender,
  genderLabels,
  MemberPrivilege,
  memberPrivilegeLabels,
  VerificationMode,
  verificationModeLabels,
} from "~lib/api/types";
import { DateInput, FieldWrapper, Input, RadioGroup, Select, TextArea } from "~lib/components";
import { Form } from "~lib/components/Form";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { isErrorWithMessage, isFetchBaseQueryError, queryError, responseError } from "~lib/utils";
import UserService from "../../../services/UserService";
import { Scope, scopeLabels } from "../types";
import { APA_INSURANCE_PAYER_ID, JUBILEE_HEALTH_PAYER_ID } from "../../../utils/constants/payerIds";

type Inputs = {
  name: string;
  email: string;
  phoneNumber: string;
  dob: string;
  gender: Gender;
  reason: string;
  verificationMode: VerificationMode;
  beneficiaryType: BeneficiaryType;
  privilege: MemberPrivilege | null;
  categoryId: string;
  categoryScope: Scope;
  phoneNumberScope: Scope;
  otherNumber?: string;
};

type Props = {
  beneficiary: Beneficiary;
  family: Beneficiary[];
  closeModal: () => void;
  resetForm?: boolean;
};

export default function EditMember({ beneficiary, family, closeModal, resetForm }: Props) {
  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const isPayerApa = payerId === APA_INSURANCE_PAYER_ID;
  const isJubilee = payerId === JUBILEE_HEALTH_PAYER_ID;

  const methods = useForm<Inputs>({
    mode: "onBlur",
    defaultValues: {
      name: beneficiary.name,
      email: beneficiary.email ?? "",
      phoneNumber: beneficiary.phoneNumber,
      dob: beneficiary.dob, // TODO: Verify format
      gender: beneficiary.gender,
      reason: "",
      verificationMode:
        beneficiary.canUseBiometrics == false ? VerificationMode.OTP : VerificationMode.BIOMETRIC,
      beneficiaryType: beneficiary.beneficiaryType,
      categoryId: beneficiary.category.id.toString(),
      categoryScope: Scope.INDIVIDUAL,
      phoneNumberScope: Scope.INDIVIDUAL,
      privilege: beneficiary.privilege,
      otherNumber: beneficiary?.otherNumber || "",
    },
  });

  const { reset, watch, formState } = methods;
  const form = watch();
  const { isDirty } = formState;

  const username = UserService.getUsername();

  // Reset form when modal is closed
  useEffect(() => {
    return () => {
      reset();
    };
  }, [reset]);

  useEffect(() => {
    if (resetForm) {
      reset();
    }
  }, [resetForm, reset]);

  const handleReset = () => {
    reset();
  };

  const {
    data: categories,
    isLoading: isCategoriesLoading,
    error: categoriesError,
  } = useGetPolicyCategoriesQuery({ policyId: beneficiary.category.policy.id });

  const [updateMember, { isLoading: isUpdateMemberLoading }] = useUpdateMemberMutation();

  const [categoryChange, { isLoading: isCategoryChangeLoading }] =
    useChangeBeneficiaryCategoryMutation();

  const isLoading = isUpdateMemberLoading || isCategoryChangeLoading;

  const newCategoryId = form.categoryId ? parseInt(form.categoryId) : undefined;

  async function handleSubmit(form: Inputs) {
    try {
      let message: string | undefined = "";

      const {
        verificationMode,
        categoryId: categoryIdRaw,
        reason,
        categoryScope,
        phoneNumberScope,
        phoneNumber,
        otherNumber,
        ...rest
      } = form;

      const categoryId = categoryIdRaw ? parseInt(categoryIdRaw) : undefined;

      const originalValues: UpdateMemberRequest = {
        name: beneficiary.name,
        email: beneficiary.email ?? "",
        dob: beneficiary.dob,
        gender: beneficiary.gender,
        canUseBiometrics: beneficiary.canUseBiometrics,
        beneficiaryType: beneficiary.beneficiaryType,
        privilege: beneficiary.privilege,
        otherNumber: beneficiary.otherNumber,
      };

      const payload: UpdateMemberRequest = {
        canUseBiometrics: verificationMode === VerificationMode.BIOMETRIC,
        ...rest,
        ...(!isPayerApa &&
          !isJubilee && {
            // only include otherNumber if it's different from the original
            otherNumber: otherNumber !== originalValues.otherNumber ? otherNumber : undefined,
          }),
        reason,
        updateBy: username,
      };

      const diff: UpdateMemberRequest = Object.fromEntries(
        (Object.keys(payload) as Array<keyof UpdateMemberRequest>)
          .map((key) => {
            // handling otherNumber to preserve hyphens
            if (key === "otherNumber") {
              // only include if the value has actually changed
              return payload[key] !== originalValues[key] ? [key, payload[key]] : [key, undefined];
            }

            if (payload[key] !== originalValues[key]) {
              return [key, payload[key]];
            }
            return [key, undefined];
          })
          .filter(([, value]) => value !== undefined),
      );

      if (Object.keys(diff).length > 2) {
        const updateMemberResponse = await updateMember({
          id: beneficiary.id,
          ...diff,
        });
        message = responseError(updateMemberResponse);
      }

      const beneficiaryIds = [beneficiary.id];
      const familyBeneficiaryIds = [beneficiary.id, ...family.map((beneficiary) => beneficiary.id)];

      if (categoryId && categoryId != beneficiary.category.id) {
        const categoryChangeResponse = await categoryChange({
          beneficiaryIds: categoryScope == Scope.FAMILY ? familyBeneficiaryIds : beneficiaryIds,
          categoryId,
          updateBy: username,
          reason,
          transferUtilization: true,
        });
        message = responseError(categoryChangeResponse);
      }

      if (phoneNumber && phoneNumber != beneficiary.phoneNumber) {
        const response = await updateMember({
          id: beneficiary.id,
          phoneNumber,
          updateType: phoneNumberScope,
          updateBy: username,
          reason,
        });
        message = responseError(response);
      }

      if (message) {
        throw new Error(message);
      } else {
        toast.success("Member updated successfully");
        closeModal();
      }
    } catch (error) {
      let message = "Something went wrong. Please try again.";
      if (isFetchBaseQueryError(error) || isErrorWithMessage(error)) {
        message = queryError(error) || message;
      }

      toast.error(message);
      console.error(error);
    }
  }

  return (
    <div className="px-6 py-2">
      <Form
        methods={methods}
        onSubmit={handleSubmit}
        className="grid grid-cols-1 gap-4 lg:grid-cols-2"
      >
        <FieldWrapper name="name" label="Name" required>
          <Input type="text" />
        </FieldWrapper>
        <FieldWrapper name="email" label="Email">
          {/* TODO: Validate email */}
          <Input type="text" />
        </FieldWrapper>
        <FieldWrapper name="phoneNumber" label="Phone Number" required>
          {/* TODO: Validate phone */}
          <Input type="text" />
        </FieldWrapper>

        {/* Enable scope option when beneficiary is principal and phone has changed */}
        <FieldWrapper name="phoneNumberScope" label="Phone Number Option">
          <RadioGroup
            solid
            orientation="horizontal"
            options={Object.values(Scope).map((scope) => ({
              label: scopeLabels[scope] || scope,
              value: scope,
            }))}
            isDisabled={
              family.length < 1 ||
              beneficiary.beneficiaryType != BeneficiaryType.PRINCIPAL ||
              !form.phoneNumber ||
              form.phoneNumber == beneficiary.phoneNumber
            }
          />
        </FieldWrapper>

        <FieldWrapper name="dob" label="Date of Birth" required>
          <DateInput placeholder="Select Date..." maxDate="today" className="w-36" />
        </FieldWrapper>

        <FieldWrapper name="privilege" label="Member Privilege" required>
          <Select
            options={Object.values(MemberPrivilege).map((privilege) => ({
              label: memberPrivilegeLabels[privilege] || "Unknown",
              value: privilege,
            }))}
            placeholder="Select..."
            className="min-w-44 max-w-xs"
          />
        </FieldWrapper>

        <FieldWrapper name="gender" label="Gender" required>
          <RadioGroup
            solid
            orientation="horizontal"
            options={Object.values(Gender).map((gender) => ({
              label: genderLabels.get(gender) || "Unknown",
              value: gender,
            }))}
          />
        </FieldWrapper>

        <FieldWrapper name="beneficiaryType" label="Beneficiary Type">
          <Select
            options={Object.values(BeneficiaryType).map((beneficiaryType) => ({
              label: beneficiaryTypeLabels.get(beneficiaryType) || "Unknown",
              value: beneficiaryType,
            }))}
            placeholder="Select..."
            className="min-w-44 max-w-xs"
          />
        </FieldWrapper>

        <FieldWrapper name="verificationMode" label="Verification Mode">
          <RadioGroup
            solid
            orientation="horizontal"
            options={Object.values(VerificationMode).map((verificationMode) => ({
              label: verificationModeLabels.get(verificationMode) || "Unknown",
              value: verificationMode,
            }))}
          />
        </FieldWrapper>

        {/* Other Number field for all payers except APA and Jubilee */}
        {!isPayerApa && !isJubilee && (
          <FieldWrapper name="otherNumber" label="Other Number">
            <Input type="text" />
          </FieldWrapper>
        )}

        {categoriesError ? (
          <div className="py-2">
            <p className="text-red-400">Error fetching categories</p>
          </div>
        ) : (
          <>
            <FieldWrapper
              name="categoryId"
              label="Category"
              className="max-w-xs flex-shrink flex-grow"
            >
              <Select
                options={(categories ?? []).map((category) => ({
                  label: category.name,
                  value: category.id.toString(),
                }))}
                className="min-w-44"
                isDisabled={true}
                // isDisabled={!categories?.length}
                isLoading={isCategoriesLoading}
                defaultValue={beneficiary.category.id.toString()}
              />
            </FieldWrapper>

            {/* Enable scope option when beneficiary is principal and category has changed */}
            <FieldWrapper name="categoryScope" label="Category Option">
              <RadioGroup
                solid
                orientation="horizontal"
                options={Object.values(Scope).map((scope) => ({
                  label: scopeLabels[scope] || scope,
                  value: scope,
                }))}
                isDisabled={
                  family.length < 1 ||
                  beneficiary.beneficiaryType != BeneficiaryType.PRINCIPAL ||
                  newCategoryId == undefined ||
                  newCategoryId == beneficiary.category.id
                }
              />
            </FieldWrapper>
          </>
        )}

        <FieldWrapper name="reason" label="Reason" required>
          <TextArea />
        </FieldWrapper>

        <div className="flex justify-end gap-2 lg:col-span-2">
          <button
            type="reset"
            className="rounded border border-gray-200 px-4 py-2 font-medium text-gray-500 enabled:hover:border-gray-300 disabled:cursor-not-allowed disabled:opacity-80"
            onClick={handleReset}
            disabled={isLoading || !isDirty}
          >
            Reset
          </button>

          <button
            type="submit"
            className="flex items-center gap-2 rounded bg-blue-500 px-4 py-2 font-medium text-white enabled:hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-80"
            disabled={isLoading || !isDirty}
          >
            {isLoading && <LoadingIcon className="h-4 w-4 text-white" />}
            <span>Submit</span>
          </button>
        </div>
      </Form>
    </div>
  );
}
