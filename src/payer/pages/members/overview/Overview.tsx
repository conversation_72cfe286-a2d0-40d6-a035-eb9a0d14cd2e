import { UserCircleIcon } from "@heroicons/react/24/solid";
import { useEffect, useState } from "react";
import {
  Beneficiary,
  BeneficiaryStatus,
  MemberPrivilege,
  PlanDate,
  PlanType,
  accessModeLabels,
  beneficiaryStatusLabels,
  beneficiaryTypeLabels,
  genderLabels,
} from "~lib/api/types";
import { Badge, Modal } from "~lib/components";
import StatusBadge from "~lib/components/StatusBadge";
import { beneficiaryStatusMap } from "~lib/constants";
import { Status } from "~lib/types";
import { formatCalculation, formatDateStringGB } from "~lib/utils";
import { calculateAgeYearsMonth } from "~lib/utils/dates";
import UserService from "../../../services/UserService";
import {
  APA_INSURANCE_PAYER_ID,
  FIRST_ASSURANCE_PAYER_ID,
  JUBILEE_HEALTH_PAYER_ID,
} from "../../../utils/constants/payerIds";
import { MemberDetailsAction } from "../MemberDetails";
import useMemberAction from "../useMemberAction";
import EditMember from "./EditMember";
import UpdateMemberStatus from "./UpdateMemberStatus";

type Props = {
  beneficiary: Beneficiary;
  family: Beneficiary[];
};

enum UserModal {
  EDIT,
  UPDATE_STATUS,
}

export default function Overview({ beneficiary, family }: Props) {
  const [activeModal, setActiveModal] = useState<UserModal | undefined>();
  const [newStatus, setNewStatus] = useState<BeneficiaryStatus | undefined>(undefined);
  const [resetEditForm, setResetEditForm] = useState(false);

  const { action, clearAction } = useMemberAction();

  function closeModal() {
    setActiveModal(undefined);
    clearAction();
    setResetEditForm(true);
  }

  function updateStatus(newStatus: BeneficiaryStatus) {
    setNewStatus(newStatus);
    setActiveModal(UserModal.UPDATE_STATUS);
  }

  const verificationModeLabel = (canUseBiometrics: boolean | null) => {
    switch (canUseBiometrics) {
      case false:
        return "OTP";
      default:
        return "Biometric";
    }
  };

  useEffect(() => {
    switch (action) {
      case MemberDetailsAction.EDIT:
        setActiveModal(UserModal.EDIT);
        break;
      case MemberDetailsAction.UPDATE_STATUS:
        setActiveModal(UserModal.UPDATE_STATUS);
        // TODO: Set and pass desired status to modal
        break;
    }
  }, [action]);

  // Reset the resetEditForm state after the modal is closed
  useEffect(() => {
    if (activeModal !== UserModal.EDIT) {
      setResetEditForm(false);
    }
  }, [activeModal]);

  // membership status update
  const USER_HAS_MEMBERSHIP_ACTIVATE_ROLE = UserService.hasRole(["MEMBERSHIP_ACTIVATE_ROLE"]);
  const USER_HAS_MEMBERSHIP_SUSPEND_ROLE = UserService.hasRole(["MEMBERSHIP_SUSPEND_ROLE"]);
  const USER_HAS_MEMBERSHIP_DEACTIVATE_ROLE = UserService.hasRole(["MEMBERSHIP_DEACTIVATE_ROLE"]);
  const USER_HAS_MEMBERSHIP_EDIT_ROLE = UserService.hasRole(["MEMBERSHIP_EDIT_ROLE"]);

  const userHasEditAccessOnly =
    USER_HAS_MEMBERSHIP_EDIT_ROLE &&
    !USER_HAS_MEMBERSHIP_ACTIVATE_ROLE &&
    !USER_HAS_MEMBERSHIP_SUSPEND_ROLE &&
    !USER_HAS_MEMBERSHIP_DEACTIVATE_ROLE;

  const userHasDeactivateAccessOnly =
    USER_HAS_MEMBERSHIP_DEACTIVATE_ROLE &&
    !USER_HAS_MEMBERSHIP_ACTIVATE_ROLE &&
    !USER_HAS_MEMBERSHIP_SUSPEND_ROLE &&
    !USER_HAS_MEMBERSHIP_EDIT_ROLE;

  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const isPayerApa = payerId == APA_INSURANCE_PAYER_ID;
  const isJubilee = payerId == JUBILEE_HEALTH_PAYER_ID;
  const isFirstAssurance = payerId == FIRST_ASSURANCE_PAYER_ID;

  const getOtherNumber = (
    isPayerApa: boolean,
    isJubilee: boolean,
    isFirstAssurance: boolean,
    beneficiary: Beneficiary,
  ): string => {
    if (isPayerApa) return String(beneficiary?.apaEntityId) || "Unknown";
    if (isJubilee) return String(beneficiary?.jicEntityId) || "Unknown";
    if (isFirstAssurance) return beneficiary?.otherNumber || "Unknown";
    return beneficiary?.otherNumber || "Unknown";
  };

  return (
    <div className="px-4">
      <div className="mb-4 flex flex-col gap-x-4 gap-y-8 lg:flex-row">
        {/* Avatar and status */}
        <div className="flex items-center gap-2 lg:grow">
          <UserCircleIcon className="h-16 w-16 text-gray-400" />
          <StatusBadge status={beneficiaryStatusMap[beneficiary.status] ?? Status.OTHER}>
            {beneficiaryStatusLabels.get(beneficiary.status) ?? beneficiary.status}
          </StatusBadge>

          {(beneficiary.privilege == MemberPrivilege.VIP ||
            beneficiary.privilege == MemberPrivilege.VVIP) && (
            <Badge className="border-yellow-600 text-yellow-700" circleClassName="bg-yellow-600">
              {beneficiary.privilege}
            </Badge>
          )}
        </div>

        {/* Action buttons */}
        <div className="flex items-center lg:grow">
          <div className="inline-flex rounded-md shadow-sm" role="group">
            {USER_HAS_MEMBERSHIP_EDIT_ROLE && (
              <button
                type="button"
                className={`${userHasEditAccessOnly ? "" : "rounded-s-lg"} border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-blue-500 enabled:hover:bg-gray-100 enabled:hover:text-blue-700 enabled:focus:z-10 enabled:focus:text-blue-700 enabled:focus:ring-2 enabled:focus:ring-blue-700 disabled:cursor-not-allowed disabled:opacity-80`}
                onClick={() => {
                  setActiveModal(UserModal.EDIT);
                }}
              >
                Edit
              </button>
            )}
            {beneficiary.status == BeneficiaryStatus.ACTIVE ? (
              <>
                {USER_HAS_MEMBERSHIP_SUSPEND_ROLE && (
                  <button
                    onClick={() => {
                      updateStatus(BeneficiaryStatus.SUSPENDED);
                    }}
                    className="rounded-none border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-red-600 enabled:hover:bg-gray-100 enabled:hover:text-red-700 enabled:focus:z-10 enabled:focus:text-red-700 enabled:focus:ring-2 enabled:focus:ring-blue-700 disabled:cursor-not-allowed disabled:opacity-80"
                    title="Suspend member"
                    type="button"
                  >
                    Suspend
                  </button>
                )}
                {USER_HAS_MEMBERSHIP_DEACTIVATE_ROLE && (
                  <button
                    className={`${userHasDeactivateAccessOnly ? "" : "rounded-e-lg"} border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-red-600 enabled:hover:bg-gray-100 enabled:hover:text-red-700 enabled:focus:z-10 enabled:focus:text-red-700 enabled:focus:ring-2 enabled:focus:ring-blue-700 disabled:cursor-not-allowed disabled:opacity-80`}
                    title="Deactivate Beneficiary"
                    onClick={() => {
                      updateStatus(BeneficiaryStatus.DEACTIVATED);
                    }}
                  >
                    Deactivate
                  </button>
                )}
              </>
            ) : (
              <>
                {USER_HAS_MEMBERSHIP_ACTIVATE_ROLE && (
                  <button
                    className="rounded-e-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-green-500 enabled:hover:bg-gray-100 enabled:hover:text-green-700 enabled:focus:z-10 enabled:focus:text-green-700 enabled:focus:ring-2 enabled:focus:ring-blue-700 disabled:cursor-not-allowed disabled:opacity-80"
                    onClick={() => {
                      setActiveModal(UserModal.UPDATE_STATUS);
                    }}
                  >
                    Activate
                  </button>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      <div className="flex flex-col gap-x-4 gap-y-8 lg:flex-row">
        <div className="lg:grow">
          <hgroup className="mb-8">
            <h3 className="text-xl font-medium">Personal Information</h3>
            <p className="text-gray-500">Member details</p>
          </hgroup>

          <div className="space-y-4">
            <div>
              <p className="font-medium">Name</p>
              <p className="text-gray-500">{beneficiary.name}</p>
            </div>
            <div>
              <p className="font-medium">Member Number</p>
              <p className="text-gray-500">{beneficiary.memberNumber}</p>
            </div>
            <div>
              <p className="font-medium">Gender</p>
              <p className="text-gray-500">{genderLabels.get(beneficiary.gender)}</p>
            </div>
            <div>
              <p className="font-medium">Age</p>
              <p className="text-gray-500">
                {formatCalculation(calculateAgeYearsMonth(new Date(beneficiary.dob)))}
              </p>
            </div>
            <div>
              <p className="font-medium">Phone Number</p>
              <p className="text-gray-500">{beneficiary.phoneNumber}</p>
            </div>
            <div>
              <p className="font-medium">ID Number</p>
              <p className="text-gray-500">{beneficiary.nhifNumber || "Unknown"}</p>
            </div>
            <div>
              <p className="font-medium">Other Number</p>
              <p className="text-gray-500">
                {getOtherNumber(isPayerApa, isJubilee, isFirstAssurance, beneficiary)}
              </p>
            </div>
            <div>
              <p className="font-medium">Verification mode</p>
              <p className="text-gray-500">{verificationModeLabel(beneficiary.canUseBiometrics)}</p>
            </div>
          </div>
        </div>

        <div className="lg:grow">
          <hgroup className="mb-8">
            <h3 className="text-xl font-medium">Cover Details</h3>
            <p className="text-gray-500">Member cover summary</p>
          </hgroup>

          <div className="space-y-4">
            <div>
              <p className="font-medium">Beneficiary Type</p>
              <p className="text-gray-500">
                {beneficiaryTypeLabels.get(beneficiary.beneficiaryType)}
              </p>
            </div>
            <div>
              <p className="font-medium">Category</p>
              <p className="text-gray-500">{beneficiary.category.name}</p>
            </div>
            <div>
              <p className="font-medium">Category Description</p>
              <p className="text-gray-500">{beneficiary.category.description}</p>
            </div>
            <div>
              <p className="font-medium">Scheme</p>
              <p className="text-gray-500">{beneficiary.category.policy.plan.name}</p>
            </div>
            <div>
              <p className="font-medium">Policy Dates</p>
              <p className="text-gray-500">
                {beneficiary.category.policy.plan.planDate == PlanDate.MEMBER_JOIN_DATE ||
                beneficiary.category.policy.plan.type == PlanType.RETAIL
                  ? formatDateStringGB(beneficiary.joinDate)
                  : formatDateStringGB(beneficiary.category.policy.startDate)}{" "}
                - {formatDateStringGB(beneficiary.category.policy.endDate)}
              </p>
            </div>
            <div>
              <p className="font-medium">Access Mode</p>
              <p className="text-gray-500">
                {accessModeLabels[beneficiary.category.policy.plan.accessMode] ?? "Unknown"}
              </p>
            </div>
          </div>
        </div>
      </div>

      <Modal
        id="edit-user"
        modalOpen={activeModal == UserModal.EDIT}
        title="Edit User"
        onClose={() => {
          closeModal();
        }}
        size="lg"
      >
        <EditMember
          beneficiary={beneficiary}
          family={family}
          closeModal={closeModal}
          resetForm={resetEditForm}
        />
      </Modal>

      <Modal
        id="update-user-status"
        modalOpen={activeModal == UserModal.UPDATE_STATUS}
        title={
          beneficiary.status === BeneficiaryStatus.ACTIVE
            ? "Deactivate/Suspend Member"
            : "Activate Member"
        }
        onClose={() => {
          closeModal();
        }}
        size="lg"
      >
        <UpdateMemberStatus
          beneficiary={beneficiary}
          familySize={family.length}
          closeModal={closeModal}
          newStatus={newStatus}
        />
      </Modal>
    </div>
  );
}
