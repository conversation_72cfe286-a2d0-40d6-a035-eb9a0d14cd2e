import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { useUpdateBeneficiaryStatusMutation } from "~lib/api";
import { BeneficiaryStatusUpdateRequest } from "~lib/api/schema";
import { Beneficiary, BeneficiaryStatus } from "~lib/api/types";
import { FieldWrapper, RadioGroup, TextArea } from "~lib/components";
import { Form } from "~lib/components/Form";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import {
  formatDateISO,
  isErrorWithMessage,
  isFetchBaseQueryError,
  queryError,
  responseError,
} from "~lib/utils";
import UserService from "../../../services/UserService";
import { Scope, scopeLabels } from "../types";

type Inputs = {
  startDate: string;
  reason: string;
  scope: Scope;
  newStatus: BeneficiaryStatus | undefined;
};

type Props = {
  beneficiary: Beneficiary;
  closeModal: () => void;
  familySize: number;
  newStatus?: BeneficiaryStatus | undefined;
};

export default function UpdateMemberStatus({
  beneficiary,
  closeModal,
  familySize,
  newStatus,
}: Props) {
  const methods = useForm<Inputs>({
    mode: "onBlur",
    defaultValues: {
      startDate: formatDateISO(new Date()), // TODO: Verify format
      reason: "",
      scope: Scope.INDIVIDUAL,
      newStatus:
        beneficiary.status === BeneficiaryStatus.ACTIVE ? newStatus : BeneficiaryStatus.ACTIVE,
    },
  });

  const { reset, setValue } = methods;

  const username = UserService.getUsername();

  const [updateBeneficiaryStatus, { isLoading: isUpdateBeneficiaryStatusLoading }] =
    useUpdateBeneficiaryStatusMutation();

  async function handleSubmit(form: Inputs) {
    try {
      const { reason, startDate } = form;

      if (!form.newStatus) {
        throw new Error("Action not selected");
      }

      const payload: BeneficiaryStatusUpdateRequest = {
        beneficiaryIds: [beneficiary.id],
        reason,
        updateBy: username,
        status: form.newStatus,
        updateType: form.scope,
      };

      const response = await updateBeneficiaryStatus(payload);
      const message = responseError(response);

      if (message) {
        throw new Error(message);
      } else {
        toast.success("Beneficiary updated successfully");
        closeModal();
      }
    } catch (error) {
      let message = "Something went wrong. Please try again.";
      if (isFetchBaseQueryError(error) || isErrorWithMessage(error)) {
        message = queryError(error) || message;
      }

      toast.error(message);
      console.error(error);
    }
  }

  const actionOptions =
    beneficiary.status === BeneficiaryStatus.ACTIVE
      ? [
          {
            label: "Suspend",
            value: BeneficiaryStatus.SUSPENDED,
          },
          {
            label: "Deactivate",
            value: BeneficiaryStatus.DEACTIVATED,
          },
        ]
      : [
          {
            label: "Activate",
            value: BeneficiaryStatus.ACTIVE,
          },
        ];

  /* --------------- Fixes an issue where the default action is not set correctly --------------- */

  useEffect(() => {
    if (newStatus) {
      setValue("newStatus", newStatus);
    }
  }, [newStatus, setValue]);

  useEffect(() => {
    if (beneficiary.status !== BeneficiaryStatus.ACTIVE) {
      setValue("newStatus", BeneficiaryStatus.ACTIVE);
    }
  }, [beneficiary.status, setValue]);

  return (
    <div className="px-6 py-2">
      <Form
        methods={methods}
        onSubmit={handleSubmit}
        className="grid grid-cols-1 gap-4 lg:grid-cols-2"
      >
        <FieldWrapper name="newStatus" label="Action">
          <RadioGroup
            solid
            orientation="horizontal"
            options={actionOptions}
            // disable if only option is "Activate" or action has already been selected
            isDisabled={beneficiary.status != BeneficiaryStatus.ACTIVE || Boolean(newStatus)}
          />
        </FieldWrapper>

        <FieldWrapper name="scope" label="Option">
          <RadioGroup
            solid
            orientation="horizontal"
            options={Object.values(Scope).map((scope) => ({
              label: scopeLabels[scope] || "Unknown",
              value: scope,
            }))}
            isDisabled={familySize < 1}
          />
        </FieldWrapper>

        <FieldWrapper name="reason" label="Reason" required>
          <TextArea />
        </FieldWrapper>

        <div className="flex justify-end gap-2 lg:col-span-2">
          <button
            type="reset"
            className="rounded border border-gray-200 px-4 py-2 font-medium text-gray-500 enabled:hover:border-gray-300 disabled:opacity-80"
            onClick={() => {
              reset();
            }}
            disabled={isUpdateBeneficiaryStatusLoading}
          >
            Reset
          </button>

          <button
            type="submit"
            className="flex items-center gap-2 rounded bg-blue-500 px-4 py-2 font-medium text-white enabled:hover:bg-blue-700 disabled:opacity-80"
            disabled={isUpdateBeneficiaryStatusLoading}
          >
            {isUpdateBeneficiaryStatusLoading && <LoadingIcon className="h-4 w-4 text-white" />}
            <span>Submit</span>
          </button>
        </div>
      </Form>
    </div>
  );
}
