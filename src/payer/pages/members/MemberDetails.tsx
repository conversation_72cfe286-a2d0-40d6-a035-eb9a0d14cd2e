import { Tab } from "@headlessui/react";
import { skipToken } from "@reduxjs/toolkit/query";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import {
  useGetBeneficiaryBenefitsQuery,
  useGetBeneficiaryFamilyQuery,
  useGetBeneficiaryQuery,
  useGetPayerPlansQuery,
} from "~lib/api";
import { BeneficiaryType } from "~lib/api/types";
import { Empty } from "~lib/components";
import ErrorMessage from "~lib/components/Error";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { clsx } from "~lib/utils";
import UserService from "../../services/UserService";
import ChangeLog from "./ChangeLog";
import Claims from "./Claims";
import Family from "./Family";
import Benefits from "./benefits/Benefits";
import Biometrics from "./biometrics/Biometrics";
import Overview from "./overview/Overview";
import useMemberAction from "./useMemberAction";

export enum MemberDetailsAction {
  DETACH_BIOMETRICS = "detach-biometrics",
  BENEFIT_ENHANCEMENT = "benefit-enhancement",
  EDIT = "edit",
  UPDATE_STATUS = "update-status",
}

enum PageTab {
  OVERVIEW,
  FAMILY,
  BIOMETRICS,
  BENEFITS,
  CLAIMS,
  CHANGE_LOG,
}

export default function MemberDetails() {
  const [activeTab, setActiveTab] = useState<PageTab>(PageTab.OVERVIEW);

  const { id: idString } = useParams();
  const { action } = useMemberAction();

  const id = idString ? window.parseInt(idString) : undefined;

  const payerIdRaw = UserService.getPayer()?.tokenParsed?.["payerId"];

  // should always be defined because of initial auth check.
  const payerId = window.parseInt(payerIdRaw);

  const { data: plans } = useGetPayerPlansQuery(!payerId ? skipToken : { payerId });

  const {
    data: beneficiary,
    isLoading: isBeneficiaryLoadingNative,
    isFetching: isBeneficiaryFetching,
    error: beneficiaryError,
  } = useGetBeneficiaryQuery(id ? { beneficiaryId: id } : skipToken, {});

  const memberNumber = beneficiary?.memberNumber;
  const planId = beneficiary?.category?.policy?.plan?.id;

  const isBeneficiaryLoading = isBeneficiaryLoadingNative || isBeneficiaryFetching;

  const {
    data: beneficiaryBenefits,
    isLoading: isBeneficiaryBenefitsLoading,
    error: beneficiaryBenefitsError,
  } = useGetBeneficiaryBenefitsQuery(id ? { beneficiaryId: id } : skipToken);

  const {
    data: familyResponse,
    isLoading: isFamilyLoading,
    error: familyError,
  } = useGetBeneficiaryFamilyQuery(
    beneficiary && beneficiary.beneficiaryType == BeneficiaryType.PRINCIPAL
      ? { id: beneficiary.id }
      : skipToken,
  );

  // Remove principal from list of family
  const family = familyResponse?.data?.filter((dependent) => dependent.id != beneficiary?.id);

  const familyIds = family?.map((dependent) => dependent?.id);

  const beneficiaryPlanId = beneficiary?.category?.policy?.plan?.id;
  const payerPlanIds = plans?.map((plan) => plan.id);

  const isBeneficiaryDisallowed =
    beneficiaryPlanId && plans && !payerPlanIds?.includes(beneficiaryPlanId);

  useEffect(() => {
    switch (action) {
      case MemberDetailsAction.DETACH_BIOMETRICS:
        setActiveTab(PageTab.BIOMETRICS);
        break;
      case MemberDetailsAction.BENEFIT_ENHANCEMENT:
        setActiveTab(PageTab.BENEFITS);
        break;
    }
  }, [action, id]);

  useEffect(() => {
    if (!action && id) {
      // Jump to overview on member change
      // Fixes an issue where clicking on "Details" from the Family Tab confusingly jumps to the Family Tab of the referred member
      setActiveTab(PageTab.OVERVIEW);
      return;
    }
  }, [id]);

  // TODO: Refactor?
  const tabs = {
    Overview: isBeneficiaryLoading ? (
      <LoadingScreen />
    ) : beneficiaryError ? (
      <ErrorMessage title="Error loading member" />
    ) : !beneficiary ? (
      <Empty message="Member not found" />
    ) : (
      <Overview beneficiary={beneficiary} family={family ?? []} />
    ),
    Family: isFamilyLoading ? (
      <LoadingScreen />
    ) : familyError ? (
      <ErrorMessage title="Error loading family" />
    ) : !family ? (
      <Empty message="No family found" />
    ) : (
      <Family family={family} />
    ),
    Biometrics: isBeneficiaryLoading ? (
      <LoadingScreen />
    ) : beneficiaryError ? (
      <ErrorMessage title="Error loading member" />
    ) : !beneficiary ? (
      <Empty message="Member not found" />
    ) : (
      <Biometrics beneficiary={beneficiary} />
    ),
    Benefits:
      isBeneficiaryBenefitsLoading || isBeneficiaryLoading ? (
        <LoadingScreen />
      ) : beneficiaryBenefitsError ? (
        <ErrorMessage title="Error loading benefits" />
      ) : beneficiaryError ? (
        <ErrorMessage title="Error loading member" />
      ) : !beneficiaryBenefits ? (
        <Empty message="No benefits found" />
      ) : !beneficiary ? (
        <Empty message="Member not found" />
      ) : (
        <Benefits beneficiaryBenefits={beneficiaryBenefits} beneficiary={beneficiary} />
      ),
    Claims:
      isBeneficiaryLoading || isFamilyLoading ? (
        <LoadingScreen />
      ) : beneficiaryError ? (
        <ErrorMessage title="Error loading member" />
      ) : familyError ? (
        <ErrorMessage title="Error loading family" />
      ) : !beneficiary ? (
        <Empty message="Member not found" />
      ) : (
        <Claims
          beneficiaryId={beneficiary.id}
          familyIds={familyIds}
          memberNumber={memberNumber ?? ""}
          planId={planId as number}
        />
      ),
    "Change Log": isBeneficiaryLoading ? (
      <LoadingScreen />
    ) : beneficiaryError ? (
      <ErrorMessage title="Error loading member" />
    ) : !beneficiary ? (
      <Empty message="Member not found" />
    ) : (
      <ChangeLog beneficiary={beneficiary} />
    ),
  };

  return (
    <div className="text-sm text-gray-700 sm:px-0">
      <Tab.Group key={id} selectedIndex={activeTab} onChange={setActiveTab}>
        <Tab.List className="-mb-px flex flex-wrap justify-center space-x-1 p-1 text-center font-medium lg:flex-nowrap">
          {Object.keys(tabs).map((label) => (
            <Tab
              key={label}
              className={({ selected }) =>
                clsx(
                  "inline-block max-w-xs rounded-t-lg border-b-2 p-4",
                  "-2.5 w-full text-sm font-medium leading-5",
                  selected
                    ? "border-blue-600 text-blue-600"
                    : "text-gray-500 hover:border-gray-300 hover:text-gray-600",
                )
              }
            >
              {label}
            </Tab>
          ))}
        </Tab.List>

        {isBeneficiaryDisallowed ? (
          <div>
            <ErrorMessage title="Beneficiary not found in payer's plans" />
          </div>
        ) : (
          <Tab.Panels className="mt-1">
            {Object.values(tabs).map((component, idx) => (
              <Tab.Panel
                key={idx}
                className={clsx(
                  "rounded bg-white py-2",
                  "ring-white/60 ring-offset-2 ring-offset-blue-400 focus:outline-none",
                )}
              >
                {component}
              </Tab.Panel>
            ))}
          </Tab.Panels>
        )}
      </Tab.Group>
    </div>
  );
}

function LoadingScreen() {
  return (
    <div className="flex justify-center py-8">
      <LoadingIcon className="h-6 w-6" />
    </div>
  );
}
