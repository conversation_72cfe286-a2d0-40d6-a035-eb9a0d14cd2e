import { UserCircleIcon } from "@heroicons/react/24/solid";
import { NavLink } from "react-router-dom";
import {
  Beneficiary,
  beneficiaryStatusLabels,
  beneficiaryTypeLabels,
  genderLabels,
} from "~lib/api/types";
import { Empty } from "~lib/components";
import StatusBadge from "~lib/components/StatusBadge";
import { beneficiaryStatusMap } from "~lib/constants";
import { Status } from "~lib/types";
import { pluralize } from "~lib/utils";
import { calculateAge } from "~lib/utils/dates";

type Props = {
  family: Beneficiary[];
};

export default function Family({ family }: Props) {
  return (
    <div className="p-4">
      {family.length > 0 ? (
        <ul className="grid grid-cols-1 justify-between gap-8 lg:grid-cols-2">
          {family.map((dependent) => (
            <li className="flex flex-wrap gap-4 rounded-md p-4 shadow">
              <div className="space-y-2">
                <UserCircleIcon className="h-12 w-12 text-gray-400" />
                <StatusBadge status={beneficiaryStatusMap[dependent.status] ?? Status.OTHER}>
                  {beneficiaryStatusLabels.get(dependent.status)}
                </StatusBadge>
              </div>

              <div className="grow space-y-2">
                <div>
                  <p className="font-medium">Name</p>
                  <p className="text-gray-500">{dependent.name}</p>
                </div>
                <div>
                  <p className="font-medium">Member Number</p>
                  <p className="text-gray-500">{dependent.memberNumber}</p>
                </div>
                <div>
                  <p className="font-medium">Beneficiary Type</p>
                  <p className="text-gray-500">
                    {beneficiaryTypeLabels.get(dependent.beneficiaryType)}
                  </p>
                </div>
                <div>
                  <p className="font-medium">Gender</p>
                  <p className="text-gray-500">{genderLabels.get(dependent.gender)}</p>
                </div>
                <div>
                  <p className="font-medium">Age</p>
                  <p className="text-gray-500">
                    {calculateAge(new Date(dependent.dob))}{" "}
                    {pluralize(calculateAge(new Date(dependent.dob)), "year")}
                  </p>
                </div>
              </div>

              <div className="flex items-end">
                <NavLink
                  className="flex gap-2 rounded bg-blue-500 px-4 py-2 font-medium text-white enabled:hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-60"
                  to={`/members/${dependent.id}`}
                >
                  Details
                </NavLink>
              </div>
            </li>
          ))}
        </ul>
      ) : (
        <div>
          {/* WARN: Assumes only principal has no family  */}
          <Empty message="Principal has no dependants" />
        </div>
      )}
    </div>
  );
}
