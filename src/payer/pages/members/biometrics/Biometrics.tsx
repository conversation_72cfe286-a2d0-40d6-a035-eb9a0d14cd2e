import { useEffect, useState } from "react";
import { useGetBeneficiaryBiometricsQuery } from "~lib/api";
import { BeneficiaryFingerPrint } from "~lib/api/schema";
import {
  Beneficiary,
  BiometricStatus,
  biometricStatusLabels,
  fingerLabels,
  handLabels,
} from "~lib/api/types";
import { Empty, Modal, Pagination } from "~lib/components";
import ErrorMessage from "~lib/components/Error";
import StatusBadge from "~lib/components/StatusBadge";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { PAGE_SIZES } from "~lib/constants";
import { Status } from "~lib/types";
import { clsx, formatDateStringGB } from "~lib/utils";
import { Action } from "../types";
import useMemberAction from "../useMemberAction";
import DetachBiometrics from "./DetachBiometrics";
import UserService from "../../../services/UserService";

interface Props {
  beneficiary: Beneficiary;
}

export default function Biometrics({ beneficiary }: Props) {
  const [page, setPage] = useState(1);
  const [size, setSize] = useState<number>(PAGE_SIZES[0]);
  const [showDetachBiometricsModal, setShowDetachBiometricsModal] = useState(false);

  const { action, clearAction } = useMemberAction();

  const {
    data: biometricsResponse,
    isLoading: isBiometricsLoading,
    isFetching: isBiometricsFetching,
    error: biometricsError,
  } = useGetBeneficiaryBiometricsQuery({
    memberNumber: beneficiary.memberNumber,
    page,
    size,
  });

  const biometrics = biometricsResponse?.data?.content;
  const isBiometricsFetchingOnly = isBiometricsFetching && !isBiometricsLoading;

  useEffect(() => {
    switch (action) {
      case Action.DETACH_BIOMETRICS:
        setShowDetachBiometricsModal(true);
        break;
    }
  }, [action]);

  function closeModal() {
    setShowDetachBiometricsModal(false);
    clearAction();
  }

  const mapBiometricsStatus: Record<BiometricStatus, Status> = {
    ACTIVE: Status.SUCCESS,
    DELETED: Status.FAILURE,
  };

  // TODO: Sort by date
  const groupedByDate = biometrics
    ?.slice()
    ?.sort((a, b) => Date.parse(b.captureDate) - Date.parse(a.captureDate))
    ?.reduce(
      (acc, b) => {
        if (!acc[b.captureDate]) {
          acc[b.captureDate] = [];
        }

        acc[b.captureDate]?.push(b);

        return acc;
      },
      {} as Record<string, BeneficiaryFingerPrint[]>,
    );

  const USER_HAS_MEMBERSHIP_DETACH_BIOMETRICS_ROLE = UserService.hasRole([
    "MEMBERSHIP_DETACH_BIOMETRICS_ROLE",
  ]);

  return (
    <div>
      {isBiometricsLoading ? (
        <div className="flex items-center justify-center p-8">
          <LoadingIcon className="h-6 w-6" />
        </div>
      ) : biometricsError ? (
        <div className="flex items-center justify-center p-8">
          <ErrorMessage title="Error fetching biometrics" />
        </div>
      ) : !biometrics?.length || !groupedByDate ? (
        <Empty message="No biometrics registered" />
      ) : (
        <div>
          <div className="mb-2 flex justify-end px-4">
            {USER_HAS_MEMBERSHIP_DETACH_BIOMETRICS_ROLE && (
              <button
                className="rounded border border-red-500 px-4 py-2 font-medium text-red-500 enabled:hover:border-red-600 enabled:hover:text-red-600 disabled:cursor-not-allowed disabled:opacity-60"
                onClick={() => {
                  setShowDetachBiometricsModal(true);
                }}
              >
                Detach Biometrics
              </button>
            )}
          </div>

          <div
            className="mb-4 overflow-x-auto bg-white text-gray-600"
            // TODO: Remove responsiveness hack
            style={{ maxWidth: "100vw" }}
          >
            <table className="w-full">
              <thead className="text-left">
                <tr className="bg-gray-100">
                  <th className="px-2 py-4 first:pl-8 last:pr-8">Hand</th>
                  <th className="px-2 py-4 first:pl-8 last:pr-8">Finger</th>
                  <th className="px-2 py-4 first:pl-8 last:pr-8">Status</th>
                  <th className="px-2 py-4 first:pl-8 last:pr-8">Date Captured</th>
                </tr>
              </thead>

              <tbody>
                {Object.values(groupedByDate).map((biometrics: BeneficiaryFingerPrint[]) => (
                  <>
                    {biometrics?.map((b, index) => (
                      <tr
                        className={clsx(
                          index == biometrics.length - 1 && "border-b border-gray-200",
                        )}
                        key={b.biometricId}
                      >
                        <td className="px-2 py-4 first:pl-8 last:pr-8">
                          {handLabels[b.hand] ?? b.hand}
                        </td>
                        <td className="px-2 py-4 first:pl-8 last:pr-8">
                          {fingerLabels[b.finger] ?? b.finger}
                        </td>
                        <td className="px-2 py-4 first:pl-8 last:pr-8">
                          <StatusBadge
                            status={mapBiometricsStatus[b.biometricStatus] ?? Status.OTHER}
                          >
                            {biometricStatusLabels.get(b.biometricStatus) ?? "Unknown"}
                          </StatusBadge>
                        </td>
                        {index == 0 && (
                          <td
                            className="bg-gray-50 px-2 py-4 align-top font-medium first:pl-8 last:pr-8"
                            rowSpan={biometrics.length}
                          >
                            {formatDateStringGB(b.captureDate)}
                          </td>
                        )}
                      </tr>
                    ))}
                  </>
                ))}
              </tbody>
            </table>
          </div>

          {biometricsResponse && (
            <div className="pb-4">
              <Pagination
                totalElements={biometricsResponse?.data.totalElements}
                totalPages={biometricsResponse?.data.totalPages}
                setPage={setPage}
                setSize={setSize}
                page={page}
                size={size}
                isLoading={isBiometricsFetchingOnly}
              />
            </div>
          )}
        </div>
      )}

      <Modal
        id="detach-biometrics"
        modalOpen={showDetachBiometricsModal}
        title="Detach biometrics"
        onClose={() => {
          closeModal();
        }}
        size="lg"
      >
        {isBiometricsLoading ? (
          <LoadingIcon className="h-6 w-6" />
        ) : biometricsError ? (
          <div className="flex flex-col items-center">
            <ErrorMessage title="Error fetching biometrics" />
          </div>
        ) : !biometrics?.length ? (
          <div className="flex items-center justify-center p-8">
            <div className="flex flex-col items-center">
              <Empty message="No biometrics found." />
            </div>
          </div>
        ) : (
          <DetachBiometrics
            beneficiary={beneficiary}
            biometrics={biometrics}
            closeModal={closeModal}
          />
        )}
      </Modal>
    </div>
  );
}
