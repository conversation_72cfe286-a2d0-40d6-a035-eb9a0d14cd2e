import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { useDetachBiometricsMutation } from "~lib/api";
import { BeneficiaryFingerPrint } from "~lib/api/schema";
import { Beneficiary } from "~lib/api/types";
import { FieldWrapper, TextArea } from "~lib/components";
import { Form } from "~lib/components/Form";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import {
  formatDateStringGB,
  isErrorWithMessage,
  isFetchBaseQueryError,
  pluralize,
  queryError,
  responseError,
} from "~lib/utils";
import { calculateAge } from "~lib/utils/dates";
import UserService from "../../../services/UserService";

type Inputs = {
  reason: string;
};

type Props = {
  beneficiary: Beneficiary;
  closeModal: () => void;
  biometrics: BeneficiaryFingerPrint[];
};

export default function DetachBiometrics({ beneficiary, closeModal, biometrics }: Props) {
  const methods = useForm<Inputs>({
    mode: "onBlur",
    defaultValues: {
      reason: "",
    },
  });

  const { reset } = methods;

  const username = UserService.getUsername();

  const [detachBiometrics, { isLoading: isDetachBiometricsLoading }] =
    useDetachBiometricsMutation();

  async function handleDetachBiometrics() {
    try {
      const response = await detachBiometrics({ memberNumber: beneficiary.memberNumber });
      const message = responseError(response);

      if (message) {
        throw new Error(message);
      }

      toast.success("Biometrics detached successfully");
    } catch (error) {
      let message = "Something went wrong. Please try again.";
      if (isFetchBaseQueryError(error) || isErrorWithMessage(error)) {
        message = queryError(error) || message;
      }

      toast.error(message);
      console.error(error);
    }
  }

  async function handleSubmit(form: Inputs) {
    const { reason } = form;

    const _payload = {
      reason,
      username,
    };

    await handleDetachBiometrics();
    closeModal();
  }

  return (
    <div className="px-6 py-2">
      <div className="mb-4 flex gap-8">
        <div>
          <p className="font-medium">Member Name</p>
          <p className="text-gray-500">{beneficiary.name}</p>
        </div>

        <div>
          <p className="font-medium">Age</p>
          <p className="text-gray-500">
            {calculateAge(new Date(beneficiary.dob))}{" "}
            {pluralize(calculateAge(new Date(beneficiary.dob)), "year")}
          </p>
        </div>

        <div>
          <p className="font-medium">Join Date</p>
          <p className="text-gray-500">{formatDateStringGB(beneficiary.joinDate)}</p>
        </div>
      </div>

      <Form
        methods={methods}
        onSubmit={handleSubmit}
        className="grid grid-cols-1 gap-4 lg:grid-cols-2"
        disabled={!biometrics.length}
      >
        {!biometrics.length && (
          <div className="col-span-2">
            <div className="mb-2 text-xs text-red-500">Member has no biometrics</div>
          </div>
        )}

        <FieldWrapper name="reason" label="Reason" required>
          <TextArea />
        </FieldWrapper>

        <div className="flex justify-end gap-2 lg:col-span-2">
          <button
            type="reset"
            className="rounded border border-gray-200 px-4 py-2 font-medium text-gray-500 enabled:hover:border-gray-300 disabled:opacity-80"
            onClick={() => {
              reset();
            }}
            disabled={isDetachBiometricsLoading}
          >
            Reset
          </button>

          <button
            type="submit"
            className="flex items-center gap-2 rounded bg-blue-500 px-4 py-2 font-medium text-white enabled:hover:bg-blue-700 disabled:opacity-80"
            disabled={isDetachBiometricsLoading || !biometrics.length}
          >
            {isDetachBiometricsLoading && <LoadingIcon className="h-4 w-4 text-white" />}
            <span>Submit</span>
          </button>
        </div>
      </Form>
    </div>
  );
}
