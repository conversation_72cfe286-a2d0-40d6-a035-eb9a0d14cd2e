import { <PERSON>u, Popover, Transition } from "@headlessui/react";
import { Fragment, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import {
  useExportBeneficiariesMutation,
  useGetPayerPoliciesQuery,
  useGetPoliciesCategoriesQuery,
  useLazySearchPayerBeneficiariesQuery,
} from "~lib/api";
import {
  Beneficiary,
  BeneficiaryStatus,
  BeneficiaryType,
  ChangeLogAction,
  ExportFileType,
  FilterBeneficiaries,
  PolicyStatus,
  VerificationMode,
  beneficiaryStatusLabels,
  beneficiaryTypeLabels,
  biometricStatusLabels,
  changeLogActionLabels,
  beneficiariesReportChangeLogActions,
  verificationModeLabels,
} from "~lib/api/types";
import doctorSvg from "~lib/assets/doctor.svg";
import AuthenticationError from "~lib/components/AuthenticationError";
import CheckboxGroup from "~lib/components/CheckboxGroup";
import DateRangePicker from "~lib/components/DateRangePicker";
import Empty from "~lib/components/Empty";
import FieldWrapper from "~lib/components/FieldWrapper";
import { Form } from "~lib/components/Form";
import Pagination from "~lib/components/Pagination";
import RadioGroup from "~lib/components/RadioGroup";
import Select from "~lib/components/Select";
import { beneficiaryStatusBadgeColors, dropdownTransitions } from "~lib/constants";
import usePrevious from "~lib/hooks/usePrevious";
import { Entries } from "~lib/types";
import { clsx, formatDateISO, formatDateStringGB } from "~lib/utils";
import UserService from "../../services/UserService";
import MainWrapper from "../../components/ui/MainWrapper";

enum ReportTypes {
  MEMBER_STATUS = "MEMBER_STATUS",
  VERIFICATION = "VERIFICATION",
  MEMBER_EDITS = "MEMBER_EDITS",
}

const reportTypesLabels = new Map([
  [ReportTypes.MEMBER_STATUS, "Member status"],
  [ReportTypes.VERIFICATION, "Verification details"],
  [ReportTypes.MEMBER_EDITS, "Member edits"],
]);

interface Column {
  label: string;
  render?: (beneficiary: Beneficiary) => JSX.Element;
}

type Filters = Omit<FilterBeneficiaries, "payerId">;

interface Inputs {
  policyId?: string;
  categoryIds?: string[];
  beneficiaryStatuses?: BeneficiaryStatus[];
  beneficiaryTypes?: BeneficiaryType[];
  verificationMode?: VerificationMode | "";
  changeLogType?: ChangeLogAction;
}

const DEFAULT_SIZE = 10;
const DEFAULT_PAGE = 1;

const filterLabels = new Map<keyof Inputs, string>([
  ["policyId", "Scheme"],
  ["categoryIds", "Categories"],
  ["beneficiaryStatuses", "Status"],
  ["beneficiaryTypes", "Beneficiary Type"],
  ["verificationMode", "Verification Mode"],
  ["changeLogType", "Member Edits"],
]);

export default function MemberReports() {
  const [reportType, setReportType] = useState(ReportTypes.MEMBER_STATUS);
  const [page, setPage] = useState(DEFAULT_PAGE);
  const [size, setSize] = useState(DEFAULT_SIZE);
  const [expandedBeneficiary, setExpandedBeneficiary] = useState<undefined | number>();
  const [filters, setFilters] = useState<Filters | undefined>();
  const [dateRange, setDateRange] = useState<[Date?, Date?]>([undefined, undefined]);

  const payerId = UserService.getPayer().tokenParsed.payerId;

  const methods = useForm<Inputs>({
    defaultValues: {
      policyId: undefined,
      categoryIds: [],
      beneficiaryStatuses: [],
      beneficiaryTypes: [],
      verificationMode: "",
      changeLogType: ChangeLogAction.MEMBER_UPDATE,
    },
  });

  const { watch, reset, resetField, handleSubmit } = methods;

  const form = watch();

  const { policyId, categoryIds } = form;

  const previousPolicyId = usePrevious(policyId);

  const activeFilterEntries = Object.entries(form).filter(([key, value]) => {
    if (reportType !== ReportTypes.MEMBER_EDITS && key === "changeLogType") {
      return false;
    }

    return Array.isArray(value) ? value.length > 0 : Boolean(value);
  }) as Entries<Inputs>;

  const {
    data: policies,
    isLoading: isPoliciesLoading,
    error: policiesError,
  } = useGetPayerPoliciesQuery({
    payerId,
  });

  const {
    data: categories,
    isLoading: isCategoriesLoading,
    error: categoriesError,
  } = useGetPoliciesCategoriesQuery(
    {
      policyIds: policyId ? [Number(policyId)] : undefined,
    },
    {
      skip: !policyId,
    },
  );

  const isFiltersFormValid = Boolean(policyId || categoryIds?.length);
  const isFiltersValid = filters?.policyIds?.length > 0 || filters?.categoryIds?.length > 0;

  const [
    fetchBeneficiaries,
    {
      isLoading: isBeneficiariesLoading,
      isFetching: isBeneficiariesFetching,
      error: beneficiariesError,
      data: beneficiariesResponse,
      isUninitialized: isBeneficiariesUninitialized,
    },
  ] = useLazySearchPayerBeneficiariesQuery();

  const [exportBeneficiaries, { isLoading: isExportLoading }] = useExportBeneficiariesMutation();

  const beneficiaries = beneficiariesResponse?.data?.content;
  const isBeneficiariesFetchingOnly = isBeneficiariesFetching && !isBeneficiariesLoading;

  function handleReportTypeChange(e: React.ChangeEvent<HTMLSelectElement>) {
    setReportType(e.target.value as ReportTypes);
  }

  function handleExpandBeneficiary(beneficiaryId: number) {
    if (expandedBeneficiary === beneficiaryId) {
      setExpandedBeneficiary(undefined);
    } else {
      setExpandedBeneficiary(beneficiaryId);
    }
  }

  const activePolicy = policyId ? policies?.find((p) => p.id === Number(policyId)) : undefined;

  async function handleExport(fileType: ExportFileType) {
    try {
      await exportBeneficiaries({
        ...filters,
        payerId,
        fileType,
        page,
        size,
      }).unwrap();
    } catch (error) {
      toast.error("Something went wrong. Please try again.");

      if (import.meta.env.DEV) {
        console.error(error);
      }
    }
  }

  /**
   * Expects the following variables in scope:
   * - page
   * - size
   * - dateRange
   * @param {Inputs} filters filters form data
   */
  function handleSubmitFilters({
    policyId,
    categoryIds,
    beneficiaryStatuses,
    beneficiaryTypes,
    verificationMode,
    changeLogType,
  }: Inputs) {
    const filters: Filters = {
      policyIds: policyId ? [Number(policyId)] : undefined,
      categoryIds: categoryIds?.length ? categoryIds?.map(Number) : undefined,
      statuses: beneficiaryStatuses?.length ? beneficiaryStatuses : undefined,
      beneficiaryTypes: beneficiaryTypes?.length ? beneficiaryTypes : undefined,
      verificationMode: (verificationMode || undefined) as VerificationMode | undefined,
      changeLogType: reportType === ReportTypes.MEMBER_EDITS ? changeLogType : undefined,
      fromDate: formatDateISO(dateRange[0]) || undefined,
      toDate: formatDateISO(dateRange[1]) || undefined,
    };

    // Trigger fetch request
    fetchBeneficiaries({
      payerId,
      page,
      size,
      ...filters,
    });

    setFilters(filters);
  }

  const statusUpdatedAt = (beneficiary: Beneficiary) => {
    let value = beneficiary.changeLog
      ?.slice()
      .reverse()
      .find((update) => update.type === ChangeLogAction.MEMBERSTATUS_UPDATE)?.time;

    if (!value && beneficiary.status === BeneficiaryStatus.ACTIVE) {
      /**
       * If no status update is found in the change log, and the beneficiary
       * is active, then the join date is the status updated at date.
       */
      value = beneficiary.joinDate;
    }

    return value ? new Date(value).toLocaleDateString() : "";
  };

  const getFilterValueLabel = (key: keyof Inputs, value: Inputs[keyof Inputs]) => {
    switch (key) {
      case "beneficiaryStatuses":
        return (value as Inputs["beneficiaryStatuses"])
          ?.map((v) => beneficiaryStatusLabels.get(v))
          .join(", ");
      case "beneficiaryTypes":
        return (value as Inputs["beneficiaryTypes"])
          ?.map((v) => beneficiaryTypeLabels.get(v))
          .join(", ");
      case "policyId":
        return policies?.find((p) => p.id === Number(value))?.plan.name || "1 selected";
      case "categoryIds":
        return `${(value as Inputs["categoryIds"])?.length} selected`;
      case "verificationMode":
        return verificationModeLabels.get(value as VerificationMode);
      case "changeLogType":
        return changeLogActionLabels.get(value as ChangeLogAction);
      default:
        return value;
    }
  };

  useEffect(() => {
    if (policyId !== previousPolicyId) {
      /**
       * Reset categoryIds when policyId changes.
       */
      resetField("categoryIds");
    }
  }, [policyId, previousPolicyId, resetField]);

  useEffect(() => {
    /**
     * Reset filters form, filters, and date range when report type changes.
     */
    reset();
    setFilters(undefined);
    setExpandedBeneficiary(undefined);
    setDateRange([undefined, undefined]);
    setPage(DEFAULT_PAGE);
    setSize(DEFAULT_SIZE);
  }, [reportType, reset]);

  const beneficiaryReportTypeColumns = new Map<ReportTypes, Column[]>([
    [
      ReportTypes.MEMBER_STATUS,
      [
        {
          label: "Current Status",
          render: (beneficiary) => (
            <div
              className={clsx(
                "flex items-center gap-1 rounded-full border px-2 py-1 text-xs font-bold",
                beneficiaryStatusBadgeColors.get(beneficiary.status)?.[0],
              )}
              style={{ width: "fit-content" }}
            >
              <span
                className={clsx(
                  "inline-block h-3 w-3 rounded-full",
                  beneficiaryStatusBadgeColors.get(beneficiary.status)?.[1],
                )}
              ></span>
              <span>{beneficiaryStatusLabels.get(beneficiary.status)}</span>
            </div>
          ),
        },
        {
          label: "Status Updated At",
          render: (beneficiary) => <>{statusUpdatedAt(beneficiary) || "N/A"}</>,
        },
      ],
    ],
    [
      ReportTypes.VERIFICATION,
      [
        {
          label: "Verification Type",
          render: (beneficiary) => (
            <>
              {beneficiary.canUseBiometrics === false
                ? verificationModeLabels.get(VerificationMode.OTP)
                : verificationModeLabels.get(VerificationMode.BIOMETRIC)}
            </>
          ),
        },
        {
          label: "Biometrics Status",
          render: (beneficiary) => (
            <>{biometricStatusLabels.get(beneficiary.biometricStatus) || "N/A"}</>
          ),
        },
        {
          label: "Biometrics Captured At",
          render: (beneficiary) => (
            <>{formatDateStringGB(beneficiary.biometricCaptureDate) || "N/A"}</>
          ),
        },
      ],
    ],
    [
      ReportTypes.MEMBER_EDITS,
      [
        {
          label: "View Edits",
          render: (beneficiary) => (
            <>
              <button
                className="text-gray-400"
                onClick={() => handleExpandBeneficiary(beneficiary.id)}
                disabled={!beneficiary.changeLog?.length}
                title={!beneficiary.changeLog?.length ? "No edits found" : ""}
              >
                {expandedBeneficiary === beneficiary.id ? (
                  // prettier-ignore
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
                  </svg>
                ) : (
                  // prettier-ignore
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                  </svg>
                )}
              </button>
            </>
          ),
        },
      ],
    ],
  ]);

  useEffect(() => {
    if (activePolicy) {
      setDateRange([new Date(activePolicy.startDate), new Date(activePolicy.endDate)]);
    }
  }, [activePolicy]);

  const SubmitFilters = (
    <Popover.Button
      type="button"
      className={clsx(
        "flex items-center justify-center gap-2 rounded-md bg-blue-500 px-8 py-2 text-center font-medium text-white focus:ring-2 focus:ring-blue-700",
        !isFiltersFormValid || isBeneficiariesLoading
          ? "cursor-not-allowed opacity-40"
          : "hover:bg-blue-600",
      )}
      title={!isFiltersFormValid ? "Select one or more schemes/categories" : "Search"}
      disabled={isBeneficiariesLoading || !isFiltersFormValid}
      onClick={() => {
        // Manually submit the form
        handleSubmit(handleSubmitFilters)();
      }}
    >
      Search
    </Popover.Button>
  );

  useEffect(() => {
    if (isFiltersFormValid && !isBeneficiariesFetching && !isBeneficiariesLoading) {
      // Fetch beneficiaries when page and size change
      handleSubmit(handleSubmitFilters)();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, size]);

  return (
    <MainWrapper className="flex h-full overflow-hidden">
      {/* Content area */}
      <div className="relative flex max-h-screen flex-1 flex-col overflow-y-auto overflow-x-hidden ">
        <main className="px-10 pb-10 text-gray-600">
          {!payerId && <AuthenticationError />}

          <div className="mb-4 flex flex-wrap items-center justify-between gap-4 py-2">
            <div className="flex items-center gap-2">
              <div title="Report Type">
                <label htmlFor="report-type" className="text-gray-500">
                  Report type:{" "}
                </label>

                <select
                  id="report-type"
                  value={reportType}
                  onChange={handleReportTypeChange}
                  className="rounded-md border border-gray-300 bg-white p-2"
                >
                  {Object.keys(ReportTypes).map((type) => (
                    <option value={ReportTypes[type]} key={type}>
                      {reportTypesLabels.get(ReportTypes[type])}
                    </option>
                  ))}
                </select>
              </div>

              <Popover className="relative">
                <Popover.Button
                  className={clsx(
                    "inline-flex w-full items-center justify-center gap-2 rounded-md border border-gray-300 px-4 py-2 text-sm font-medium hover:border-gray-400",
                  )}
                >
                  {/* prettier-ignore */}
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 01-.659 1.591l-5.432 5.432a2.25 2.25 0 00-.659 1.591v2.927a2.25 2.25 0 01-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 00-.659-1.591L3.659 7.409A2.25 2.25 0 013 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0112 3z" />
                  </svg>
                  Filter
                  {/* prettier-ignore */}
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="-mr-1 h-5 w-5">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                  </svg>
                </Popover.Button>

                <Transition as={Fragment} {...dropdownTransitions}>
                  <Popover.Panel
                    className={clsx(
                      "absolute z-10 mt-2 w-96 origin-top-right overflow-y-auto rounded-lg border border-gray-300 bg-gray-50 p-4 shadow focus:outline-none",
                    )}
                    style={{ maxHeight: "calc(100vh - 10rem)" }}
                  >
                    <div>
                      <div className="flex justify-between gap-2">
                        <p className="uppercase text-gray-400">Filtered By</p>

                        <button
                          onClick={() => {
                            reset();
                          }}
                          className={clsx(
                            "text-sm",
                            !activeFilterEntries.length
                              ? "cursor-not-allowed text-gray-300"
                              : "text-red-500",
                          )}
                          disabled={!activeFilterEntries.length}
                        >
                          Clear All
                        </button>
                      </div>

                      {!activeFilterEntries.length ? (
                        <p className="py-4 text-center text-gray-400">No filters applied</p>
                      ) : (
                        <div className="flex flex-col gap-1 py-2 text-sm">
                          {activeFilterEntries.map(([key, value]) => (
                            <div key={key} className="flex justify-between gap-2">
                              <p>
                                <span className="text-gray-300">{filterLabels.get(key)}: </span>
                                <span className="text-gray-400">
                                  {getFilterValueLabel(key as keyof Inputs, value)}
                                </span>
                              </p>

                              <button
                                className="text-red-400"
                                title="Remove filter"
                                onClick={() => {
                                  resetField(key as keyof Inputs);
                                }}
                              >
                                {/* prettier-ignore */}
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-4 h-4">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                              </button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    <Form
                      className="flex flex-col gap-2"
                      methods={methods}
                      onSubmit={handleSubmitFilters}
                    >
                      <details>
                        <summary className="mb-2">Scheme</summary>

                        <div>
                          {isPoliciesLoading ? (
                            <div className="flex items-center justify-center py-8">
                              {/* prettier-ignore */}
                              <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-blue-400 w-8 h-8">
                              <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
                            </svg>
                            </div>
                          ) : policiesError ? (
                            <div className="px-2 py-8 text-red-500">
                              Something went wrong while fetching schemes. Please refresh the page
                              to retry.
                            </div>
                          ) : !policies?.length ? (
                            <Empty message="No schemes found" />
                          ) : (
                            <div>
                              <Select
                                name="policyId"
                                options={policies
                                  .filter((policy) => policy.status == PolicyStatus.ACTIVE)
                                  .map((policy) => ({
                                    label: policy.plan.name,
                                    value: policy.id.toString(),
                                  }))}
                                placeholder="Search scheme"
                                styles={{
                                  menuList: (provided) => ({
                                    ...provided,
                                    height: "120px",
                                  }),
                                }}
                                required={true}
                              />
                            </div>
                          )}
                        </div>
                      </details>

                      <details>
                        <summary className="mb-2">Categories</summary>

                        <div>
                          {!policyId ? (
                            <p className="px-2 py-4 text-sm text-gray-400">
                              Select a scheme to view categories
                            </p>
                          ) : isCategoriesLoading ? (
                            <div className="flex items-center justify-center py-8">
                              {/* prettier-ignore */}
                              <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-blue-400 w-8 h-8">
                              <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
                            </svg>
                            </div>
                          ) : categoriesError ? (
                            <div className="px-2 py-8 text-red-500">
                              Something went wrong while fetching categories. Please refresh the
                              page to retry.
                            </div>
                          ) : !categories?.length ? (
                            <Empty message="No categories found" />
                          ) : (
                            <div>
                              <CheckboxGroup
                                name="categoryIds"
                                label="Categories"
                                options={categories.map((category) => ({
                                  label: category.name,
                                  value: category.id.toString(),
                                }))}
                                fieldSetClassName="max-h-96 overflow-y-auto"
                              />
                            </div>
                          )}
                        </div>
                      </details>

                      <details>
                        <summary className="mb-2">Status</summary>

                        <div>
                          <CheckboxGroup
                            name="beneficiaryStatuses"
                            options={Object.keys(BeneficiaryStatus).map((status) => ({
                              label: status,
                              value: BeneficiaryStatus[status],
                            }))}
                            showFilter={false}
                          />
                        </div>
                      </details>

                      <details>
                        <summary className="mb-2">Type</summary>
                        <div>
                          <CheckboxGroup
                            name="beneficiaryTypes"
                            options={Object.keys(BeneficiaryType).map((type) => ({
                              label: type,
                              value: BeneficiaryType[type],
                            }))}
                            showFilter={false}
                          />
                        </div>
                      </details>

                      {reportType === ReportTypes.VERIFICATION && (
                        <details>
                          <summary className="mb-2">Verification</summary>

                          <div>
                            <FieldWrapper label="" name="verificationMode">
                              <RadioGroup
                                options={Object.values(VerificationMode).map((mode) => ({
                                  label: verificationModeLabels.get(mode),
                                  value: mode,
                                }))}
                                orientation="horizontal"
                                solid
                              />
                            </FieldWrapper>
                          </div>
                        </details>
                      )}

                      {reportType === ReportTypes.MEMBER_EDITS && (
                        <details>
                          <summary className="mb-2">Member Edits</summary>

                          <div>
                            <FieldWrapper label="" name="changeLogType">
                              <RadioGroup
                                className="gap-0"
                                options={beneficiariesReportChangeLogActions
                                  .filter((action) => action != ChangeLogAction.NONE)
                                  .map((action) => ({
                                    label: changeLogActionLabels.get(action),
                                    value: action,
                                  }))}
                              />
                            </FieldWrapper>
                          </div>
                        </details>
                      )}

                      {SubmitFilters}
                    </Form>
                  </Popover.Panel>
                </Transition>
              </Popover>

              <DateRangePicker
                value={dateRange}
                onChange={setDateRange}
                options={
                  activePolicy
                    ? {
                        minDate: activePolicy.startDate,
                        maxDate: activePolicy.endDate,
                      }
                    : {}
                }
                footer={<div className="flex justify-end py-1">{SubmitFilters}</div>}
              />

              <div>
                {isBeneficiariesFetchingOnly && (
                  <>
                    {/* prettier-ignore */}
                    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-gray-400 w-6 h-6">
                      <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z">
                        <animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/>
                      </path>
                    </svg>
                  </>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Menu as="div" className="relative inline-block text-left">
                <div>
                  <Menu.Button
                    className={clsx(
                      "inline-flex w-full items-center justify-center gap-2 rounded-md bg-blue-500 px-4 py-2 text-sm font-medium text-white",
                      isExportLoading || !isFiltersValid
                        ? "cursor-not-allowed opacity-60"
                        : "hover:bg-blue-600",
                    )}
                    disabled={isExportLoading || !isFiltersValid}
                  >
                    {isExportLoading && (
                      <>
                        {/* prettier-ignore */}
                        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="h-5 w-5">
                          <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
                        </svg>
                      </>
                    )}
                    {/* prettier-ignore */}
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="h-5 w-5">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"/>
                    </svg>
                    Export
                    {/* prettier-ignore */}
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="-mr-1 h-5 w-5">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                    </svg>
                  </Menu.Button>
                </div>

                <Transition
                  as={Fragment}
                  enter="transition ease-out duration-100"
                  enterFrom="transform opacity-0 scale-95"
                  enterTo="transform opacity-100 scale-100"
                  leave="transition ease-in duration-75"
                  leaveFrom="transform opacity-100 scale-100"
                  leaveTo="transform opacity-0 scale-95"
                >
                  <Menu.Items className="absolute right-0 mt-2 w-36 origin-top-right rounded-md border border-gray-200 bg-white focus:outline-none">
                    {Object.values(ExportFileType).map((fileType) => (
                      <Menu.Item key={fileType}>
                        {({ active }) => (
                          <button
                            className={`${
                              active ? "bg-gray-50" : ""
                            } group flex w-full items-center rounded-md px-6 py-4 text-sm`}
                            onClick={() => handleExport(ExportFileType[fileType])}
                          >
                            {fileType}
                          </button>
                        )}
                      </Menu.Item>
                    ))}
                  </Menu.Items>
                </Transition>
              </Menu>
            </div>
          </div>

          {/* <div className="mb-4 flex justify-end">
            {JSON.stringify({ form, filters })}
          </div> */}

          <div className="flex-grow pb-4">
            {isBeneficiariesUninitialized || !isFiltersValid ? (
              <div className="flex items-center justify-center p-4">
                <div className="flex flex-col items-center gap-1">
                  <img src={doctorSvg} alt="Image of a doctor" className="h-auto w-48" />

                  <h2 className="text-center text-2xl font-medium">No reports</h2>

                  <p className="text-center text-gray-300">
                    Click on the filter button to select the reports you want to view...
                  </p>
                </div>
              </div>
            ) : isBeneficiariesLoading ? (
              <div className="flex items-center justify-center py-8">
                {/* prettier-ignore */}
                <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-blue-400">
                  <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
                </svg>
              </div>
            ) : beneficiariesError ? (
              <div className="px-2 py-8 text-red-500">
                Something went wrong. Please refresh the page to retry.
              </div>
            ) : !beneficiaries?.length ? (
              <div className="flex items-center justify-center p-8">
                <div className="flex flex-col items-center">
                  <Empty message="No users matching filters" />
                </div>
              </div>
            ) : (
              <div>
                <div className="mb-4 max-w-full overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="py-4 pl-8 text-left">Member Number</th>
                        <th className="px-2 py-4 text-left">Full Name</th>
                        <th className="px-2 py-4 text-left">Gender</th>
                        <th className="px-2 py-4 text-left">Phone Number</th>
                        <th className="px-2 py-4 text-left">Date of Birth</th>
                        <th className="px-2 py-4 text-left">Member Type</th>
                        <th className="px-2 py-4 text-left">Scheme</th>
                        {beneficiaryReportTypeColumns.get(reportType)?.map((column) => (
                          <th className="px-2 py-4 text-left" key={column.label}>
                            {column.label}
                          </th>
                        ))}
                      </tr>
                    </thead>

                    <tbody>
                      {beneficiaries?.map((beneficiary) => (
                        <Fragment key={beneficiary.id}>
                          <tr>
                            <td className="py-4 pl-8">{beneficiary.memberNumber}</td>
                            <td className="px-2 py-4">{beneficiary.name}</td>
                            <td className="px-2 py-4">{beneficiary.gender}</td>
                            <td className="px-2 py-4">{beneficiary.phoneNumber || "N/A"}</td>
                            <td className="px-2 py-4">{formatDateStringGB(beneficiary.dob)}</td>
                            <td className="px-2 py-4">{beneficiary.beneficiaryType}</td>
                            <td className="px-2 py-4">{beneficiary.category.policy.plan.name}</td>
                            {beneficiaryReportTypeColumns.get(reportType)?.map((column) => (
                              <td className="px-2 py-4 text-left" key={column.label}>
                                {column.render(beneficiary)}
                              </td>
                            ))}
                          </tr>

                          {reportType === ReportTypes.MEMBER_EDITS &&
                            expandedBeneficiary === beneficiary.id &&
                            beneficiary.changeLog.length && (
                              <tr>
                                {/* WARNING: colspan will fail with a fixed table, or more than 100 columns */}
                                <td colSpan={100} className="rounded border-b bg-gray-50">
                                  <table className="w-full">
                                    <thead className="bg-gray-100 text-sm uppercase text-gray-500">
                                      <tr>
                                        <th className="px-2 py-2 text-left font-medium">Date</th>
                                        <th className="py-2 pl-8 text-left font-medium">User</th>
                                        <th className="px-2 py-2 text-left font-medium">Reason</th>
                                        <th className="px-2 py-2 text-left font-medium">
                                          Organisation
                                        </th>
                                        <th className="py-2 pr-8 text-left font-medium">Action</th>
                                      </tr>
                                    </thead>

                                    <tbody>
                                      {beneficiary.changeLog
                                        ?.filter((log) =>
                                          beneficiariesReportChangeLogActions.find((action) =>
                                            [log.type, log.action].includes(action),
                                          ),
                                        )
                                        .map((log) => (
                                          <tr key={log.id}>
                                            <td className="px-2 py-2">
                                              {formatDateStringGB(log.time)}
                                            </td>
                                            <td className="py-2 pl-8">{log.user}</td>
                                            <td className="px-2 py-2">{log.reason}</td>
                                            <td className="px-2 py-2">{log.organisation}</td>
                                            <td className="py-2 pr-8">
                                              {changeLogActionLabels.get(
                                                (log.type || log.action) as ChangeLogAction,
                                              ) || "N/A"}
                                            </td>
                                          </tr>
                                        ))}
                                    </tbody>
                                  </table>
                                </td>
                              </tr>
                            )}
                        </Fragment>
                      ))}
                    </tbody>
                  </table>
                </div>

                <Pagination
                  totalElements={beneficiariesResponse.data.totalElements}
                  totalPages={beneficiariesResponse.data.totalPages}
                  page={page}
                  size={size}
                  setPage={setPage}
                  setSize={setSize}
                  isLoading={isBeneficiariesFetchingOnly}
                />
              </div>
            )}
          </div>
        </main>
      </div>
    </MainWrapper>
  );
}
