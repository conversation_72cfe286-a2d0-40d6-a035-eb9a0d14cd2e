import { useState } from "react";
import { toast } from "react-toastify";
import * as xlsx from "xlsx";
import {
  useBatchUpdateBeneficiariesMutation,
  useBatchUpdateBeneficiaryStatusMutation,
} from "~lib/api";
import { BeneficiaryStatus } from "~lib/api/types";
import { Pagination } from "~lib/components";
import ErrorMessage from "~lib/components/Error";
import { PAGE_SIZES } from "~lib/constants";
import { RTKResult, responseError } from "~lib/utils";
import UserService from "../../../services/UserService";
import { headers } from "./constants";
import { MemberStatusUpdate, MemberUpdate } from "./schema";
import { Action, Data, Inputs, SheetError } from "./types";

interface Props {
  form: Inputs;
  data: Data;
  errors: SheetError;
  close: () => void;
  reset: () => void;
}

const formatValue = (value: unknown) => {
  if (value instanceof Date) {
    return value.toLocaleDateString();
  } else {
    return value?.toString();
  }
};

const formatNumber = (value: number) => (window.isNaN(value) ? "" : value);

const Preview = ({ data, errors, close, form, reset }: Props) => {
  const [page, setPage] = useState<number>(1);
  const [size, setSize] = useState<number>(PAGE_SIZES[0] ?? 10);

  const rows = data?.data;
  const activeEntity = errors && typeof errors != "string" ? errors : rows;

  const totalElements = activeEntity?.length ?? 0;
  const totalPages = Math.floor((activeEntity?.length ?? 0) / (size ?? 1)) + 1;

  /**
   * Pagination start index, inclusive
   */
  const startIndex = (page - 1) * (size ?? 0);

  /**
   * Pagination end index, exclusive
   */
  const endIndex = Math.min(startIndex + (size ?? 0), activeEntity?.length ?? 0);

  const [updateBeneficiaries, { isLoading: isUpdateBeneficiariesLoading }] =
    useBatchUpdateBeneficiariesMutation();
  const [updateBeneficiaryStatus, { isLoading: isUpdateBeneficiaryStatusLoading }] =
    useBatchUpdateBeneficiaryStatusMutation();

  const isLoading = isUpdateBeneficiariesLoading || isUpdateBeneficiaryStatusLoading;

  const username = UserService.getUsername();

  async function handleSubmit() {
    const { scheme, action, document: bufferFile, reason } = form;

    try {
      if (!scheme) {
        throw new Error("Scheme is invalid");
      }

      if (!bufferFile) {
        throw new Error("File not found");
      }

      const { type } = bufferFile;

      const policyId = window.parseInt(scheme);
      let response: RTKResult<true> | undefined;

      // Ensure all cells in the Excel file are properly formatted as strings
      // This is a workaround for the backend issue with numeric cells
      try {
        // Create a text-only version of the file to ensure all cells are strings
        const workbook = xlsx.read(bufferFile.buffer, {
          type: "buffer",
          raw: true,
        });

        if (!workbook.SheetNames.length) {
          throw new Error("No sheets found in workbook");
        }

        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName as keyof typeof workbook.Sheets];

        // Convert all cells to strings
        for (const cell in worksheet) {
          if (cell[0] === "!") continue; // Skip special keys

          const cellRef = worksheet[cell];
          if (!cellRef) continue;

          // Force string conversion for the cell
          if (cellRef.v !== undefined) {
            let stringValue = "";

            if (typeof cellRef.v === "number") {
              stringValue = cellRef.v.toString();
            } else if (cellRef.v instanceof Date) {
              stringValue = cellRef.v.toISOString().split("T")[0];
            } else {
              stringValue = String(cellRef.v).trim();
            }

            // Update the cell with string value
            worksheet[cell] = {
              t: "s", // Set type to string
              v: stringValue, // Set raw value
              w: stringValue, // Set formatted text
            };
          }
        }

        // Write the modified workbook back to a buffer
        const newBuffer = xlsx.write(workbook, { type: "buffer", bookType: "xlsx" });

        // Use the modified buffer for the API call
        if (action == Action.MEMBER_ACTIVATION || action == Action.MEMBER_DEACTIVATION) {
          const status =
            action == Action.MEMBER_ACTIVATION
              ? BeneficiaryStatus.ACTIVE
              : BeneficiaryStatus.DEACTIVATED;

          const payload = {
            status,
            policyId,
            updateBy: username,
            file: new Blob([newBuffer], { type }),
            reason,
          };
          response = await updateBeneficiaryStatus(payload);
        } else if (action == Action.MEMBER_EDITING) {
          const payload = {
            policyId,
            updateBy: username,
            reason,
            file: new Blob([newBuffer], { type }),
          };

          response = await updateBeneficiaries(payload);
        }
      } catch (error) {
        console.error("Error processing Excel file:", error);

        // Fallback to original file if processing fails
        if (action == Action.MEMBER_ACTIVATION || action == Action.MEMBER_DEACTIVATION) {
          const status =
            action == Action.MEMBER_ACTIVATION
              ? BeneficiaryStatus.ACTIVE
              : BeneficiaryStatus.DEACTIVATED;

          const payload = {
            status,
            policyId,
            updateBy: username,
            file: new Blob([bufferFile.buffer], { type }),
            reason,
          };
          response = await updateBeneficiaryStatus(payload);
        } else if (action == Action.MEMBER_EDITING) {
          const payload = {
            policyId,
            updateBy: username,
            reason,
            file: new Blob([bufferFile.buffer], { type }),
          };

          response = await updateBeneficiaries(payload);
        }
      }

      if (!response) {
        throw new Error("Received empty response");
      }

      const errorMessage = responseError(response);
      if (errorMessage) {
        throw new Error(errorMessage);
      }

      toast.success("Edits applied successfully");
      reset();
      close();
    } catch (error) {
      toast.error((error as Error).message || "Something went wrong");
    }
  }

  return (
    <div className="pb-2">
      {typeof errors == "string" ? (
        <ErrorMessage title="Error validating document" message={errors} />
      ) : errors ? (
        <div className="overflow-x-auto bg-white text-gray-600">
          <table className="w-full">
            <thead className="text-left">
              <tr className="bg-gray-100">
                <th className="px-2 py-4 first:pl-8 last:pr-8">Row</th>
                <th className="px-2 py-4 first:pl-8 last:pr-8">Column</th>
                <th className="px-2 py-4 first:pl-8 last:pr-8">Error</th>
              </tr>
            </thead>

            <tbody>
              {errors?.slice(startIndex, endIndex).map((error, index) => (
                <tr key={index}>
                  <td className="px-2 py-4 first:pl-8 last:pr-8">
                    {formatNumber(error.row + 1) || "-"}
                  </td>
                  <td className="px-2 py-4 first:pl-8 last:pr-8">
                    {formatNumber(error.column + 1) || "-"}
                  </td>
                  <td className="px-2 py-4 text-red-600 first:pl-8 last:pr-8">{error.message}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="overflow-x-auto bg-white text-gray-600">
          <table className="w-full">
            <thead className="text-left">
              <tr className="bg-gray-100">
                <th className="px-2 py-4 first:pl-8 last:pr-8">Category</th>
                {form.action &&
                  headers
                    .get(form.action)
                    ?.map((header) => <th className="px-2 py-4 first:pl-8 last:pr-8">{header}</th>)}
              </tr>
            </thead>

            <tbody>
              {rows
                ?.slice(startIndex, endIndex)
                .map((row: MemberUpdate | MemberStatusUpdate, index) => (
                  <tr className="cursor-pointer" key={index}>
                    <td className="px-2 py-4 first:pl-8 last:pr-8">{data?.sheetName}</td>
                    {row.map((entry) => (
                      <td className="px-2 py-4 first:pl-8 last:pr-8">{formatValue(entry)}</td>
                    ))}
                  </tr>
                ))}
            </tbody>
          </table>
        </div>
      )}

      {typeof errors != "string" && (
        <Pagination
          {...{
            totalElements,
            totalPages,
            size,
            setSize,
            page,
            setPage,
          }}
        />
      )}

      <div className="flex justify-end gap-4 px-4 py-2">
        <button
          onClick={() => {
            close();
          }}
          className="flex gap-2 rounded border border-red-500 px-4 py-2 font-medium text-red-500 enabled:hover:border-red-600 enabled:hover:text-red-600 disabled:cursor-not-allowed disabled:opacity-80"
        >
          Close
        </button>

        {!errors && (
          <button
            onClick={handleSubmit}
            className="flex gap-2 rounded border border-blue-500 px-4 py-2 font-medium text-blue-500 enabled:hover:border-blue-600 enabled:hover:text-blue-600 disabled:cursor-not-allowed disabled:opacity-80"
            disabled={isLoading}
          >
            {isLoading && (
              <>
                {/* prettier-ignore */}
                <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-blue-500">
                  <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z">
                    <animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite" />
                  </path>
                </svg>
              </>
            )}
            OK
          </button>
        )}
      </div>
    </div>
  );
};

export default Preview;
