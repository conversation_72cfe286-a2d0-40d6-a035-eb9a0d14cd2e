import { useState } from "react";
import { useNavigate } from "react-router-dom";

const Help = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const navigate = useNavigate();

  function handleBack() {
    navigate(-1);
  }

  return (
    <div className="flex h-full overflow-hidden">
      <div className="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden ">
        <main className="mx-4 lg:mx-8 mb-4 lg:mb-8 text-gray-800">
          <div className="px-4 sm:px-6 lg:px-8 py-2 rounded-md shadow-lg pb-10">
            {/* Header */}
            <div className="mb-4">
              <div className="flex gap-4 justify-between mb-4">
                <h2 className="text-xl font-medium">User Guide—Mass Actions</h2>
                <button
                  className="text-gray-500 enabled:hover:text-gray-600"
                  onClick={() => {
                    handleBack();
                  }}
                  title="Go Back"
                >
                  {/* prettier-ignore */}
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
              </div>

              <ol className="list-decimal ml-4 space-y-2">
                <li>
                  Select the desired action by clicking in the <strong>Action</strong> text box.
                </li>
                <li>
                  Click on the <strong>Download Template</strong> button at the top right corner of
                  the screen.
                </li>
                <li>
                  On your computer, open the template and add the required rows. Note that{" "}
                  <em>the sheet names must match the members' categories</em>.
                </li>
                <li>
                  Upload the template by either dragging and dropping it into the upload area or
                  clicking <strong>Browse</strong> to select it from your computer.
                </li>
                <li>
                  Click on the <strong>Scheme</strong> text box to select the appropriate scheme.
                </li>
                <li>
                  Add a reason for the selected action in the <strong>Reason</strong> textbox.
                </li>
                <li>
                  Click <strong>Submit</strong> to review the changes or any errors in the document.
                </li>
                <li>
                  Click <strong>OK</strong> to submit the document, or <strong>Close</strong> to
                  close the preview.
                </li>
              </ol>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Help;
