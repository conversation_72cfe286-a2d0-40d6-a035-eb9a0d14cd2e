import { MemberStatusUpdate, MemberUpdate } from "./schema";

export enum Action {
  MEMBER_ACTIVATION = "MEMBER_ACTIVATION",
  MEMBER_EDITING = "MEMBER_EDITING",
  MEMBER_DEACTIVATION = "MEMBER_DEACTIVATION",
}

export const actionLabels = new Map<Action, string>([
  [Action.MEMBER_ACTIVATION, "Member Activation"],
  [Action.MEMBER_EDITING, "Member Editing"],
  [Action.MEMBER_DEACTIVATION, "Member Deactivation"],
]);

export type CellError = {
  row: number;
  column: number;
  message: string;
};

export type SheetError = CellError[] | string | undefined;
export type Data =
  | {
      sheetName: string;
      data: MemberUpdate[] | MemberStatusUpdate[];
    }
  | undefined;

export type Inputs = {
  action: Action | undefined;
  document: BufferFile | null;
  reason: string;
  scheme: string | undefined;
};

export type BufferFile = {
  name: string;
  size: number;
  type: string;
  lastModified: number;
  buffer: ArrayBuffer;
};
