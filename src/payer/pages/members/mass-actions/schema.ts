import { z } from "zod";
import { pluralize } from "~lib/utils";

export const MemberStatusUpdate = z.tuple([
  z.string().trim().min(1, "Member number must be longer than one character"),
  z.string().trim().min(1, "Reason must be longer than one character"),
]);

export type MemberStatusUpdate = z.infer<typeof MemberStatusUpdate>;

export const MemberStatusUpdates = z.array(MemberStatusUpdate);

/**
 * WARN: Does not handle landline numbers.
 * Sample accepted formats
 * 712123456
 * 0712123456
 * 254712123456
 * +254712123456
 */
const phoneNumberPrefixRegex = /(0|254|\+254)?(?=[1,7][0-9]{8})/;

/**
 * If regex validation fails, the string validation for length 0 is used
 */
export const OptionalPhoneNumber = z
  .union([
    z.string().length(0, "Invalid phone number"),
    // Coerce numeric value
    z.coerce
      .string()
      .trim()
      .regex(phoneNumberPrefixRegex, "Invalid phone number")
      .transform((v) => v.replace(phoneNumberPrefixRegex, "0")), // Convert to form 07...
  ])
  .optional()
  .transform((e) => (e === "" ? undefined : e));

/**
 * Schema order matches:
 * 1. MEMBER NUMBER (Required)
 * 2. OTHER NUMBER
 * 3. MEMBER NAME
 * 4. PHONE NUMBER
 * 5. EMAIL
 * 6. NHIF NUMBER
 * 7. JOIN DATE
 * 8. REASON
 */
export const MemberUpdate = z.tuple(
  [
    // 1. MEMBER NUMBER (Required)
    z
      .string()
      .trim()
      .min(1, "Member number is required")
      .transform((val) => val.toUpperCase()),

    // 2. OTHER NUMBER
    z
      .union([z.string().trim(), z.string().length(0)])
      .optional()
      .transform((e) => (e === "" ? undefined : e)),

    // 3. MEMBER NAME
    z
      .union([z.string().min(3, "Member name must be at least 3 characters"), z.string().length(0)])
      .optional()
      .transform((e) => (e === "" ? undefined : e)),

    // 4. PHONE NUMBER
    OptionalPhoneNumber,

    // 5. EMAIL
    z
      .union([z.string().trim().email("Invalid email address"), z.string().length(0)])
      .optional()
      .transform((e) => (e === "" ? undefined : e)),

    // 6. NHIF NUMBER
    z
      .union([z.string().trim(), z.string().length(0)])
      .optional()
      .transform((e) => (e === "" ? undefined : e)),

    // 7. JOIN DATE
    z
      .union([
        z.coerce.date().refine((data) => data < new Date(), {
          message: "Join date must be in the past",
        }),
        z.string().length(0),
      ])
      .optional()
      .transform((e) => (e === "" ? undefined : e)),

    // 8. REASON
    z.string().trim().min(1, "Reason is required").min(3, "Reason must be at least 3 characters"),
  ],
  {
    errorMap: (issue, ctx) => {
      const columnNames = [
        "Member Number",
        "Other Number",
        "Member Name",
        "Phone Number",
        "Email",
        "SHIF Number",
        "Join Date",
        "Reason",
      ] as const;

      // Fix: Type assertion for the array index
      const columnIndex = typeof issue.path[1] === "number" ? issue.path[1] : -1;
      const columnName = columnIndex >= 0 ? columnNames[columnIndex] : "Unknown field";

      if (
        issue.code === z.ZodIssueCode.invalid_type &&
        issue.received === z.ZodParsedType.undefined
      ) {
        const message = `${columnName} is required`;
        return { message };
      }

      if (issue.code === z.ZodIssueCode.too_small) {
        if (issue.type === "array") {
          const message = `Row must contain ${
            issue.exact ? "exactly" : issue.inclusive ? `at least` : `more than`
          } ${issue.minimum} ${pluralize(issue.minimum as number, "element")}`;
          return { message };
        }

        if (issue.type === "string") {
          const message = `${columnName} must be at least ${issue.minimum} characters`;
          return { message };
        }
      }

      if (issue.code === z.ZodIssueCode.too_big && issue.type === "array") {
        const message = `Row must contain ${
          issue.exact ? `exactly` : issue.inclusive ? `at most` : `less than`
        } ${issue.maximum} ${pluralize(issue.maximum as number, "element")}`;
        return { message };
      }

      if (issue.code === z.ZodIssueCode.invalid_string) {
        if (issue.validation === "email") {
          return { message: `Invalid email address in ${columnName}` };
        }
      }

      if (issue.code === z.ZodIssueCode.invalid_date) {
        return { message: `Invalid date format in ${columnName}` };
      }

      if (issue.code === z.ZodIssueCode.custom) {
        if (columnName === "Join Date") {
          return { message: "Join date must be in the past" };
        }
      }

      // For any unhandled cases, include the column name
      return { message: `${columnName}: ${ctx.defaultError}` };
    },
  },
);

export type MemberUpdate = z.infer<typeof MemberUpdate>;

export const MemberUpdates = z.array(MemberUpdate);
