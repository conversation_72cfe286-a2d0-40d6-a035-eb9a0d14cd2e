import { TemplateType } from "~lib/api/types";
import { Action } from "./types";

export const headers = new Map<Action, string[]>([
  [
    Action.MEMBER_EDITING,
    [
      "Member Number",
      "Other Number",
      "Member Name",
      "Phone Number",
      "Email",
      "SHIF Number",
      "Join Date",
      "Reason",
    ],
  ],
  [Action.MEMBER_ACTIVATION, ["Member Number", "Reason"]],
  [Action.MEMBER_DEACTIVATION, ["Member Number", "Reason"]],
]);

export const actionTemplate = new Map<Action, TemplateType>([
  [Action.MEMBER_ACTIVATION, TemplateType.MEMBER_ACTIVATION_OR_DEACTIVATION],
  [Action.MEMBER_DEACTIVATION, TemplateType.MEMBER_ACTIVATION_OR_DEACTIVATION],
  [Action.MEMBER_EDITING, TemplateType.MEMBER_EDIT],
]);
