import { useState } from "react";
import { useForm } from "react-hook-form";
import { Link } from "react-router-dom";
import { toast } from "react-toastify";
import * as xlsx from "xlsx";
import { ZodError } from "zod";
import { useGetPayerPoliciesQuery } from "~lib/api";
import { Empty, FieldWrapper, Modal, Select, TextArea } from "~lib/components";
import { Form } from "~lib/components/Form";
import { baseUrl } from "~lib/constants";
import { clsx } from "~lib/utils";
import MainWrapper from "../../../components/ui/MainWrapper";
import UserService from "../../../services/UserService";
import Preview from "./Preview";
import UploadFile from "./UploadFile";
import { actionTemplate, headers } from "./constants";
import { MemberStatusUpdates, MemberUpdates } from "./schema";
import { Action, BufferFile, CellError, Data, Inputs, SheetError, actionLabels } from "./types";

const MIME_TYPES = new Map([
  ["application/vnd.ms-excel", "XLS"],
  ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "XLSX"],
]);

/**
 * Limit to a smaller size to speed up client side validation and reduce memory
 * usage since the whole file is read into memory.
 */
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

enum Tab {
  PREVIEW,
  ERROR,
}

const MassActions = () => {
  const [data, setData] = useState<Data>();
  const [sheetErrors, setSheetErrors] = useState<SheetError>();
  const [activeTab, setActiveTab] = useState<Tab | undefined>();

  const methods = useForm<Inputs>({
    mode: "onBlur",
    defaultValues: {
      action: undefined,
      document: null,
      reason: "",
      scheme: undefined,
    },
  });

  const { watch, reset } = methods;
  const form = watch();

  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];

  const {
    data: policies,
    isLoading: isPoliciesLoading,
    error: policiesError,
  } = useGetPayerPoliciesQuery({
    payerId,
  });

  async function handleSubmit(params: Inputs) {
    try {
      if (!params.action) {
        throw new Error("Action not selected");
      }

      if (!params.document) {
        throw new Error("File not found");
      }

      setData(undefined);
      setSheetErrors(undefined);

      const [errors, result] = await processFile(params.action, params.document);

      setActiveTab(Tab.PREVIEW);

      if (errors) {
        setSheetErrors(errors);
      } else {
        setData(result);
      }
    } catch (error) {
      let message = "Something went wrong";

      if (error instanceof Error) {
        message = error.message ?? message;
      }
      console.error(message);
      toast.error(message);
    }
  }

  /**
   *
   * @param rows
   * @returns - a tuple of headers and parsed rows
   */
  function parseRows(
    action: Action,
    sheetName: string,
    rows: unknown[],
  ): [CellError[] | string | undefined, Data] {
    const [_header, ...dataRows] = rows;

    try {
      if (dataRows.length < 1) {
        throw new Error("The document has no data rows");
      }

      switch (action) {
        case Action.MEMBER_ACTIVATION:
        case Action.MEMBER_DEACTIVATION:
          return [undefined, { sheetName, data: MemberStatusUpdates.parse(dataRows) }];
        case Action.MEMBER_EDITING:
          return [undefined, { sheetName, data: MemberUpdates.parse(dataRows) }];
        default:
          // add an exhaustive check for action
          return ["Invalid action type", undefined];
      }
    } catch (error) {
      if (error instanceof ZodError) {
        return [
          error.issues.map((issue) => {
            const row = Number(issue.path[0]);
            const column = Number(issue.path[1]);

            const rowData = dataRows[row] as unknown[];
            const value: unknown = rowData?.[column];

            return {
              row: row,
              column: column,
              message: `${issue.message}. Received value: "${value}"`,
            };
          }),
          undefined,
        ];
      } else if (error instanceof Error) {
        console.error(error);
        return [error.message, undefined];
      } else {
        console.error(error);
        return ["Something went wrong", undefined];
      }
    }
  }

  async function processFile(action: Action, file: BufferFile): Promise<[SheetError, Data]> {
    try {
      // read the workbook as raw data
      const workbook = xlsx.read(file.buffer, {
        type: "buffer",
        raw: true,
        cellDates: false,
        cellNF: false,
        cellText: false,
      });

      if (!workbook.SheetNames.length) {
        throw new Error("No sheets found in workbook");
      }

      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName as keyof typeof workbook.Sheets];

      if (!worksheet) {
        throw new Error("Could not read worksheet");
      }

      // Create a new workbook with all cells as strings to avoid backend type conversion issues
      const newWorkbook = xlsx.utils.book_new();
      const newWorksheetData: string[][] = [];

      // Get the original data as an array of arrays
      const originalData = xlsx.utils.sheet_to_json<any[]>(worksheet, {
        header: 1,
        raw: true,
        // @ts-ignore - defval is a valid option in xlsx library
        defval: "",
      });

      // Process each row and convert all cells to strings
      originalData.forEach((row, rowIndex) => {
        const newRow: string[] = [];

        // Process each cell in the row
        row.forEach((cell, cellIndex) => {
          let stringValue = "";

          try {
            // Handle different types of values
            if (cell === null || cell === undefined) {
              stringValue = "";
            } else if (typeof cell === "number") {
              // Convert numbers to strings, avoiding scientific notation
              stringValue = cell.toString();

              // Handle scientific notation
              if (stringValue.includes("e") || stringValue.includes("E")) {
                stringValue = Number(cell).toString();
              }

              // Remove trailing zeros
              stringValue = stringValue.replace(/\.?0+$/, "");
            } else if (cell instanceof Date) {
              // Convert dates to ISO string format
              stringValue = (cell as Date).toISOString().split("T")[0] as string;
            } else {
              // Convert everything else to string and trim
              stringValue = String(cell).trim();
            }
          } catch (error) {
            console.error(`Error converting row ${rowIndex}, cell ${cellIndex}:`, error);
            stringValue = "";
          }

          newRow.push(stringValue);
        });

        // Add the processed row to the new worksheet data
        newWorksheetData.push(newRow);
      });

      // Create a new worksheet with the processed data
      const newWorksheet = xlsx.utils.aoa_to_sheet(newWorksheetData);

      // Add the new worksheet to the new workbook
      xlsx.utils.book_append_sheet(newWorkbook, newWorksheet, sheetName);

      // Convert the new workbook to a buffer
      const newBuffer = xlsx.write(newWorkbook, { type: "buffer", bookType: "xlsx" });

      // Create a new BufferFile with the processed workbook
      const processedFile: BufferFile = {
        ...file,
        buffer: newBuffer,
      };

      // Read the processed workbook for validation
      const validationWorkbook = xlsx.read(processedFile.buffer, {
        type: "buffer",
        raw: false,
      });

      const validationWorksheet =
        validationWorkbook.Sheets[sheetName as keyof typeof validationWorkbook.Sheets];

      // Read the processed worksheet for validation
      if (!validationWorksheet) {
        throw new Error("Could not read validation worksheet");
      }

      const rows = xlsx.utils.sheet_to_json(validationWorksheet, {
        header: 1,
        raw: false,
        // @ts-ignore - defval and blankrows are valid options in xlsx library
        defval: "",
        blankrows: true,
      });

      // Additional string processing for the rows
      const paddedRows = rows.map((row: unknown) => {
        const rowArray = row as unknown[];
        const processedRow = rowArray.map((cell) => {
          if (cell === null || cell === undefined || cell === "") {
            return undefined;
          }

          // All cells should already be strings at this point
          return String(cell).trim();
        });

        // Ensure the row has the correct number of columns
        const requiredLength = headers.get(action)?.length ?? 0;
        while (processedRow.length < requiredLength) {
          processedRow.push(undefined);
        }

        return processedRow;
      });

      // Update the file in the form with the processed file
      if (file === methods.getValues().document) {
        methods.setValue("document", processedFile);
      }

      return parseRows(action, String(sheetName), paddedRows);
    } catch (error) {
      console.error("Excel processing error:", error);
      const message =
        error instanceof Error ? error.message : "Something went wrong processing the file";
      return [message, undefined];
    }
  }

  const selectedPolicy = policies?.find((policy) => policy.id.toString() == form.scheme);
  const policyNumber = selectedPolicy?.policyNumber;

  const isDownloadTemplateDisabled = !form.action;

  const generateDownloadTemplateUrl = () => {
    const url = new URL(`${baseUrl}/api/v1/membership/template/download`);

    if (!form.action) {
      return;
    }

    url.searchParams.append("templateType", actionTemplate.get(form.action) ?? "");

    return url;
  };

  const downloadTemplateUrl = generateDownloadTemplateUrl();

  const downloadTemplateHref =
    isDownloadTemplateDisabled && downloadTemplateUrl ? "#" : downloadTemplateUrl?.toString();

  return (
    <MainWrapper className="flex h-full overflow-hidden">
      <div className="relative flex flex-1 flex-col overflow-y-auto overflow-x-hidden ">
        <main className="text-gray-800">
          <div className="mx-auto rounded-md px-4 py-2 pb-10">
            {/* Header */}
            <div className="mb-4 flex items-start justify-between gap-4">
              <h3 className="text-xl font-medium">Mass Actions</h3>
              <Link
                to="/members/batch/help"
                title="Help"
                className=" flex gap-1 text-gray-600 hover:text-gray-700"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="h-6 w-6"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"
                  />
                </svg>
                Help
              </Link>
            </div>

            <Form methods={methods} onSubmit={handleSubmit}>
              <div className="flex flex-wrap gap-4 lg:flex-nowrap">
                <div className="flex flex-col gap-1">
                  <div className="flex items-end gap-2">
                    <FieldWrapper name="action" label="Action" className="grow" required>
                      <Select
                        options={Object.values(Action).map((action) => ({
                          label: actionLabels.get(action),
                          value: action,
                        }))}
                      />
                    </FieldWrapper>

                    <div className="flex flex-col gap-1">
                      <a
                        className={clsx(
                          "flex gap-2 rounded-md border border-gray-300 bg-white px-4 py-2",
                          isDownloadTemplateDisabled && "cursor-not-allowed opacity-60",
                        )}
                        href={downloadTemplateHref}
                        target={isDownloadTemplateDisabled ? undefined : "_blank"}
                      >
                        {/* prettier-ignore */}
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"/>
                        </svg>
                        Download Template
                      </a>

                      {/* Align spacing */}
                      <span className="text-xs text-red-500">&#8203;</span>
                    </div>
                  </div>

                  <div className="flex gap-4">
                    <div className="grow">
                      {isPoliciesLoading ? (
                        <div className="flex items-center justify-center py-8">
                          {/* prettier-ignore */}
                          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-blue-400 w-8 h-8">
                            <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
                          </svg>
                        </div>
                      ) : policiesError ? (
                        <div className="px-2 py-8 text-red-500">
                          Something went wrong while fetching schemes. Please refresh the page to
                          retry.
                        </div>
                      ) : !policies?.length ? (
                        <Empty message="No schemes found" />
                      ) : (
                        <FieldWrapper name="scheme" label="Scheme" required>
                          <Select
                            options={policies.map((policy) => ({
                              label: policy.plan.name,
                              value: policy.id.toString(),
                            }))}
                            placeholder="Search scheme"
                            required={true}
                          />
                        </FieldWrapper>
                      )}
                    </div>

                    <div>
                      <div className="flex flex-col gap-1">
                        <label>Policy Number</label>
                        <p className="min-w-36 cursor-not-allowed rounded border border-gray-300 px-4 py-2 opacity-60">
                          {policyNumber || <>&#8203;</>}
                        </p>
                        <span className="text-xs text-red-500">&#8203;</span>
                      </div>
                    </div>
                  </div>

                  <FieldWrapper name="reason" label="Reason" required>
                    <TextArea />
                  </FieldWrapper>
                </div>

                <div className="grow">
                  <FieldWrapper name="document" label="Document" required>
                    <UploadFile allowedMimeTypes={MIME_TYPES} maxFileSize={MAX_FILE_SIZE} />
                  </FieldWrapper>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <button
                  type="reset"
                  className="rounded border border-gray-200 px-4 py-2 font-medium text-gray-500 hover:border-gray-300"
                  onClick={() => {
                    reset();
                  }}
                >
                  Reset
                </button>

                <button
                  type="submit"
                  className={clsx(
                    "flex gap-2 rounded bg-blue-500 px-4 py-2 font-medium text-white hover:bg-blue-700",
                    false && "cursor-not-allowed opacity-50",
                  )}
                  disabled={false}
                >
                  {false && (
                    <>
                      {/* prettier-ignore */}
                      <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-white">
                        <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z">
                          <animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite" />
                        </path>
                      </svg>
                    </>
                  )}
                  <span>Submit</span>
                </button>
              </div>
            </Form>
          </div>
        </main>
      </div>

      <Modal
        id="preview"
        modalOpen={activeTab == Tab.PREVIEW}
        onClose={() => {
          setActiveTab(undefined);
        }}
        title={sheetErrors ? "Errors" : "Preview"}
        size="xl"
      >
        {form.action && (
          <Preview
            data={data}
            errors={sheetErrors}
            close={() => {
              setActiveTab(undefined);
            }}
            form={form}
            reset={reset}
          />
        )}
      </Modal>
    </MainWrapper>
  );
};

export default MassActions;
