import { ChangeEvent, DragEvent, useState } from "react";
import { Controller, Path, PathValue, useFormContext, useWatch } from "react-hook-form";
import { toast } from "react-toastify";
import { clsx, formatBytes } from "~lib/utils";
import { BufferFile } from "./types";

type Props<N extends string> = {
  /**
   * @example
   * { "image/jpeg": "JPEG" }
   */
  allowedMimeTypes: Map<string, string>;
  /**
   * Maximum file size in bytes
   * @default MAX_FILE_SIZE in constants
   */
  maxFileSize?: number;
  /**
   * Fields potentially injected by FieldWrapper
   */
  name?: N;
  required?: boolean;
};

/**
 * Only one file uploaded. On upload replace existing. Require at least one file.
 * Show form submission progress.
 */
function UploadFile<const N extends string>({ required, maxFileSize, allowedMimeTypes }: Props<N>) {
  const name = "document";

  type Inputs = {
    [name]: BufferFile | null;
  };

  const [dragActive, setDragActive] = useState(false);
  const { control, formState, resetField } = useFormContext<Inputs>();

  const document = useWatch({
    control,
    defaultValue: null,
    name,
  });

  const error = formState.errors?.[name]?.root;

  async function handleDrag(e: React.DragEvent) {
    e.preventDefault();
    e.stopPropagation();

    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }

  /**
   * Returns true if all files are valid, false otherwise
   * @param file {File[]}
   * @returns {[boolean, string]} [isValid, errorMessage]
   */
  function validateFile(file: File | null | undefined): [boolean, string] {
    if (file == null) {
      return [false, "No file selected"];
    } else if (!Array.from(allowedMimeTypes.keys()).includes(file.type)) {
      return [false, `Only ${Array.from(allowedMimeTypes.values()).join(", ")} allowed`];
    } else if (maxFileSize && file.size > maxFileSize) {
      return [false, `Only files below ${formatBytes(maxFileSize)} are allowed`];
    }

    return [true, ""];
  }

  async function handleFileChange(
    file: File | null,
    onChange: (event: PathValue<Inputs, Path<Inputs>> | ChangeEvent<Element>) => void
  ) {
    // Remove exisiting file
    resetField(name);

    const [valid, error] = validateFile(file);

    if (error && !valid) toast.error(error);
    if (error && valid) toast.warning(error);

    if (!file) return;
    if (!valid) return;

    // Read file into memory to prevent permissions issues
    const buffer = await file.arrayBuffer();

    onChange({
      name: file.name,
      size: file.size,
      lastModified: file.lastModified,
      type: file.type,
      buffer,
    });
  }

  return (
    <div>
      <div className="px-2 py-4">
        <Controller
          control={control}
          name={name}
          rules={{
            required: {
              value: required ?? false,
              message: "Document is required",
            },
          }}
          // Ignore value
          render={({ field: { onChange, value: _value, ...rest } }) => (
            <div
              className={clsx(
                "flex items-center justify-center w-full h-56 border-2 border-dashed border-blue-300 rounded-lg",
                dragActive ? "bg-blue-50" : "bg-white"
              )}
              onDragEnter={handleDrag}
            >
              <div className="flex flex-col items-center">
                {/* prettier-ignore */}
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-24 h-24 text-blue-400">
                  <path fillRule="evenodd" d="M10.5 3.75a6 6 0 00-5.98 6.496A5.25 5.25 0 006.75 20.25H18a4.5 4.5 0 002.206-8.423 3.75 3.75 0 00-4.133-4.303A6.001 6.001 0 0010.5 3.75zm2.03 5.47a.75.75 0 00-1.06 0l-3 3a.75.75 0 101.06 1.06l1.72-1.72v4.94a.75.75 0 001.5 0v-4.94l1.72 1.72a.75.75 0 101.06-1.06l-3-3z" clipRule="evenodd" />
                </svg>

                <div>
                  <p className="text-center px-4">
                    Drag and drop here or
                    <label className="px-2 underline cursor-pointer text-blue-400 hover:text-blue-500">
                      browse
                      <input
                        type="file"
                        // name="documents"
                        id="documents"
                        className="hidden"
                        multiple={false}
                        accept={Array.from(allowedMimeTypes.keys()).join(",")}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          e.preventDefault();
                          e.stopPropagation();

                          const file: File | null = e.target.files?.item(0) || null;
                          handleFileChange(file, onChange);
                        }}
                        onClick={(e: React.MouseEvent<HTMLInputElement>) => {
                          if (e.target instanceof HTMLInputElement) {
                            // Reset the input so that the onChange handler runs even when uploading the same file
                            e.target.value = "";
                          }
                        }}
                        {...rest}
                      />
                    </label>
                  </p>

                  <p className="text-center px-4 text-xs text-gray-400 font-medium">
                    {Array.from(allowedMimeTypes.values()).join(", ")} files only.
                    {maxFileSize && `Max file size is ${formatBytes(maxFileSize)}`}
                  </p>
                </div>
              </div>

              {dragActive && (
                <div
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={(e: DragEvent<HTMLDivElement>) => {
                    e.preventDefault();
                    e.stopPropagation();

                    setDragActive(false);

                    const file = e.dataTransfer.items
                      ? Array.from(e.dataTransfer.items)
                          .filter((item) => item.kind === "file")[0]
                          ?.getAsFile() ?? null
                      : e.dataTransfer.files.item(0);

                    handleFileChange(file, onChange);
                  }}
                  className="absolute inset-0 z-10 top-0 left-0 right-0 bottom-0 w-full h-full opacity-0"
                />
              )}
            </div>
          )}
        />
        {<span className="text-xs text-red-500">{error ? error.message : ""}</span>}
      </div>

      {document && (
        <div className="flex gap-4 mb-2 px-4 items-center">
          <div className="break-all flex-grow text-sm text-gray-600">{document.name}</div>

          <div className="flex gap-2">
            {/* prettier-ignore */}
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-6 h-6 text-green-500">
              <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>

            <button
              className={clsx("text-red-500 hover:text-red-600")}
              type="button"
              title="Remove"
              onClick={() => {
                resetField(name);
              }}
            >
              {/* prettier-ignore */}
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
              </svg>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default UploadFile;
