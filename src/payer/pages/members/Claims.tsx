import { ArrowDownTrayIcon, ArrowPathIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import { skipToken } from "@reduxjs/toolkit/query";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import {
  api,
  useExportMemberStatementMutation,
  useGetCoverPeriodsQuery,
  useGetProviderBranchesQuery,
  useSearchClaimsQuery,
} from "~lib/api";
import { StatementType, VisitStatus, VisitType, visitStatusLabels } from "~lib/api/types";
import {
  AsyncSelect,
  DateInput,
  Empty,
  FieldWrapper,
  Input,
  Pagination,
  RadioButtonGroup,
  Select,
} from "~lib/components";
import ErrorMessage from "~lib/components/Error";
import { Form } from "~lib/components/Form";
import StatusBadge from "~lib/components/StatusBadge";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { PAGE_SIZES, PAYER_CLAIMS_EXCLUDE_VISIT_STATUSES, baseUrl } from "~lib/constants";
import { useDebounce } from "~lib/hooks/useDebounce";
import useSearchProviderOptions from "~lib/hooks/useSearchProviderOptions";
import { Option, PartialOrUndefined } from "~lib/types";
import {
  branchName,
  formatDateISO,
  formatMoney,
  getVisitStatusBadge,
  queryError,
  truncate,
} from "~lib/utils";
import Dropdown from "../../components/ui/Dropdown";
import UserService from "../../services/UserService";
import { useAppDispatch } from "../../store/hooks";

type FilledInputs = {
  provider: Option<string>;
  branch: Option<string>;
  query: string;
  status: VisitStatus;
  startDate: string;
  endDate: string;
};

type Inputs = PartialOrUndefined<FilledInputs> & {
  view: StatementType;
};

interface Props {
  beneficiaryId: number;
  planId: number;
  memberNumber: string;
  familyIds?: number[] | undefined;
}

const viewLabels: Record<StatementType, string> = {
  INDIVIDUAL: "Individual",
  FAMILY: "Family",
};

interface CoverPeriod {
  id: number;
  category: {
    policy: {
      startDate: string;
      endDate: string;
    };
  };
}

export default function Claims({ beneficiaryId, familyIds, memberNumber, planId }: Props) {
  const [page, setPage] = useState(1);
  const [size, setSize] = useState<number>(PAGE_SIZES[0]);
  const [selectedBeneficiaryId, setSelectedBeneficiaryId] = useState<number | undefined>(undefined);

  const dispatch = useAppDispatch();

  const methods = useForm<Inputs>({
    mode: "onBlur",
    defaultValues: {
      provider: undefined,
      branch: undefined,
      status: undefined,
      query: "",
      startDate: "",
      endDate: formatDateISO(new Date()),
      view: StatementType.INDIVIDUAL,
    },
  });

  //cover periods

  const memberNo = memberNumber;

  // Explicitly annotate the type of data

  const { data: coverPeriods, error, isLoading } = useGetCoverPeriodsQuery({ planId, memberNo });

  let idsArray: number[] = [];

  if (coverPeriods && coverPeriods.data) {
    idsArray = coverPeriods.data.map((coverPeriod: CoverPeriod) => coverPeriod.id);
  }

  // Construct options for dropdown
  const coverPeriodOptions =
    coverPeriods?.data.map((period) => ({
      label: `${period.category.policy.startDate} / ${period.category.policy.endDate}`,
      value: period.id,
    })) || [];

  //end of cover period

  const { watch } = methods;

  const form = watch();
  const queryDebounced = useDebounce(form.query, 200);

  const payerIdRaw = UserService.getPayer()?.tokenParsed?.["payerId"];

  // should always be defined because of initial auth check.
  const payerId = window.parseInt(payerIdRaw);

  const providerId = window.parseInt(form.provider?.value ?? "") || undefined;

  const {
    data: providersResponse,
    error: branchesError,
    isLoading: isBranchesLoading,
  } = useGetProviderBranchesQuery(providerId ? { providerId } : skipToken);

  const branches = providersResponse?.data;

  const branchesOptions = [
    // Current (main) facility
    ...(providerId ? [{ label: branchName(form.provider?.label ?? ""), value: providerId }] : []),
    // Branches
    ...(branches || []).map(({ providerId, providerName }) => ({
      label: branchName(providerName),
      value: providerId,
    })),
  ];

  const getProviderParam = () => {
    if (!providerId) {
      return [];
    }

    if (form.branch?.value) {
      // Branch selected
      const branchId = window.parseInt(form.branch.value);
      const providerIds = [branchId];
      return providerIds;
    } else {
      // No branch selected - return all branches
      const providerIds = [providerId, ...(branches || []).map((branch) => branch.providerId)];
      return providerIds;
    }
  };

  // const params = {
  //   beneficiaryIds: [
  //     beneficiaryId,
  //     ...(form.view === StatementType.FAMILY && familyIds ? familyIds : []),
  //   ],
  //   providerIds: getProviderParam(),
  //   payerIds: [payerId],
  //   visitTypes: [VisitType.ONLINE, VisitType.OFF_LCT],
  //   visitStatuses: form.status
  //     ? [form.status]
  //     : Object.values(VisitStatus).filter(
  //         (status) => !PAYER_CLAIMS_EXCLUDE_VISIT_STATUSES.includes(status),
  //       ),
  //   ...(queryDebounced && { query: queryDebounced }),
  //   ...(form.startDate && { startDate: form.startDate }),
  //   ...(form.endDate && { endDate: form.endDate }),
  // };

  const params = {
    beneficiaryIds: [
      idsArray,
      ...(form.view === StatementType.FAMILY && familyIds ? familyIds : []),
    ],
    providerIds: getProviderParam(),
    payerIds: [payerId],
    visitTypes: [VisitType.ONLINE, VisitType.OFF_LCT, VisitType.REIMBURSEMENT],
    visitStatuses: form.status
      ? [form.status]
      : Object.values(VisitStatus).filter(
          (status) => !PAYER_CLAIMS_EXCLUDE_VISIT_STATUSES.includes(status),
        ),
    ...(queryDebounced && { query: queryDebounced }),
    ...(form.startDate && { startDate: form.startDate }),
    ...(form.endDate && { endDate: form.endDate }),
  };

  const {
    data: claimsResponse,
    error: claimsError,
    isLoading: isClaimsLoading,
    isFetching: isClaimsFetching,
  } = useSearchClaimsQuery(!payerId ? skipToken : { page, size, ...params });

  const [exportMemberStatement, { isLoading: isExportMemberStatementLoading }] =
    useExportMemberStatementMutation();
  const claims = claimsResponse?.data.content;

  const isClaimsFetchingOnly = isClaimsFetching && !isClaimsLoading;

  const { getProviderOptions } = useSearchProviderOptions();

  const isExportFiltersValid = true;
  const isExportLoading = isExportMemberStatementLoading;
  const isExportDisabled = isExportLoading || !isExportFiltersValid;

  const refresh = () => dispatch(api.util.invalidateTags(["Claims"]));

  async function handleExportMemberStatement(
    statementType: StatementType,
    fileType: "PDF" | "XLSX",
  ) {
    try {
      if (!payerId) {
        throw new Error("Payer ID not found");
      }

      const payload = {
        payerId,
        beneficiaryId: selectedBeneficiaryId ?? beneficiaryId,
        statementType,
        fileType,
      };

      const _response = await exportMemberStatement(payload).unwrap();
    } catch (error) {
      const message = "Something went wrong. Please try again.";
      toast.error(message);
      console.error(error);
    }
  }

  return (
    // TODO: Fix export menu obscured
    <div className="w-full pb-8 text-gray-600">
      <div className="max-w-full rounded-md bg-white shadow-sm">
        <div className="z-10 bg-gray-50 px-8 py-4">
          <Form
            className="pt-2 text-sm"
            methods={methods}
            onSubmit={(_data, e) => {
              e?.preventDefault();
            }}
          >
            <details className="[&>summary_svg.chevron]:open:rotate-90" open>
              <summary className="mb-2 flex flex-wrap items-center justify-between gap-4 font-medium lg:flex-nowrap">
                <span className="flex gap-2 py-2">
                  <ChevronRightIcon className="chevron h-5 w-5 shrink-0 rotate-0 transform transition-all duration-300" />
                  Filter Claims
                </span>

                {isClaimsFetchingOnly ? (
                  <LoadingIcon className="h-5 w-5 text-gray-400" />
                ) : (
                  <button
                    title="Refresh"
                    className="flex gap-2 rounded-full bg-transparent font-medium text-gray-400 enabled:hover:text-gray-500 disabled:cursor-not-allowed disabled:opacity-60"
                    type="button"
                    onClick={() => {
                      refresh();
                    }}
                  >
                    <ArrowPathIcon className="h-5 w-5" />
                  </button>
                )}

                <div className="flex gap-2">
                  <FieldWrapper
                    label=""
                    name="view"
                    labelClassName="font-semibold text-gray-500"
                    hideErrorMessage
                  >
                    <RadioButtonGroup
                      options={Object.values(StatementType).map((view) => ({
                        label: viewLabels[view] ?? "Unknown",
                        value: view,
                      }))}
                      isDisabled={!familyIds?.length}
                      // title={!familyIds?.length ? "Member has no dependents" : ""}
                    />
                  </FieldWrapper>

                  <Dropdown
                    LeftIcon={
                      isExportLoading ? (
                        <LoadingIcon className="h-4 w-4" />
                      ) : (
                        <ArrowDownTrayIcon className="w-5" />
                      )
                    }
                    disabled={isExportDisabled}
                    buttonLabel="Export Statement"
                    buttonClassName="bg-white text-blue-500 font-medium border-blue-500 border rounded hover:border-blue-600 hover:text-blue-600  "
                    items={[
                      {
                        label: "PDF",
                        onClick: () => handleExportMemberStatement(form.view, "PDF"),
                      },
                      {
                        label: "XLSX",
                        onClick: () => handleExportMemberStatement(form.view, "XLSX"),
                      },
                    ]}
                  />
                </div>
              </summary>

              <div className="grid grid-cols-7 gap-1 ">
                <FieldWrapper
                  name="provider"
                  label="Provider"
                  className="col-span-2 max-w-[100%] flex-shrink flex-grow"
                  labelClassName="font-semibold text-gray-500"
                >
                  <AsyncSelect
                    name="provider"
                    getOptions={getProviderOptions}
                    placeholder="Search provider..."
                    className="max-w-[100%]"
                  />
                </FieldWrapper>

                <FieldWrapper
                  name="branch"
                  label="Branch"
                  className="max-w-full flex-shrink flex-grow"
                  labelClassName="font-semibold text-gray-500"
                  fieldError={queryError(branchesError)}
                >
                  <Select
                    options={branchesOptions}
                    className="max-w-[100%]"
                    isLoading={isBranchesLoading}
                    isDisabled={branchesError != undefined}
                  />
                </FieldWrapper>

                {/* cover period */}

                <FieldWrapper
                  name="coverPeriod"
                  label="Cover Period"
                  labelClassName="font-semibold text-gray-500"
                >
                  <select
                    className="rounded-md border border-gray-300 bg-white p-2 text-sm"
                    onChange={(e) => {
                      const selectedCoverPeriodId = parseInt(e.target.value);
                      const selectedCoverPeriod = coverPeriods?.data.find(
                        (period) => period.id === selectedCoverPeriodId,
                      );
                      if (selectedCoverPeriod) {
                        setSelectedBeneficiaryId(selectedCoverPeriod.id);
                        methods.setValue(
                          "startDate",
                          selectedCoverPeriod.category.policy.startDate,
                        );
                        methods.setValue("endDate", selectedCoverPeriod.category.policy.endDate);
                      }
                    }}
                  >
                    <option value="">Select Cover Period</option>
                    {coverPeriodOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </FieldWrapper>

                {/* end cover perios */}

                {/* TODO: Fix date font size */}
                <FieldWrapper
                  name="startDate"
                  label="Start Date"
                  labelClassName="font-semibold text-gray-500"
                >
                  <DateInput
                    placeholder={new Date().toISOString().split("T")[0]}
                    maxDate="today"
                    className=" max-w-[100%] p-2 text-sm"
                  />
                </FieldWrapper>

                <FieldWrapper
                  name="endDate"
                  label="End Date"
                  labelClassName="font-semibold text-gray-500"
                >
                  <DateInput
                    placeholder={new Date().toISOString().split("T")[0]}
                    maxDate="today"
                    className="max-w-[100%] p-2 text-sm"
                  />
                </FieldWrapper>

                <FieldWrapper
                  name="query"
                  label="Search"
                  className="flex-grow"
                  labelClassName="font-semibold text-gray-500"
                >
                  <Input
                    type="text"
                    placeholder="Invoice number..."
                    className="max-w-full text-sm"
                  />
                </FieldWrapper>
              </div>
            </details>
          </Form>
        </div>

        <div className="mb-4">
          {isClaimsLoading ? (
            <div className="flex items-center justify-center py-8">
              <LoadingIcon className="h-6 w-6 text-blue-400" />
            </div>
          ) : claimsError ? (
            <ErrorMessage
              title="Error fetching claims"
              message={queryError(claimsError) ?? "Something went wrong"}
            />
          ) : !claims?.length ? (
            <Empty message="No claims found" />
          ) : (
            <div
              className="overflow-x-auto bg-white text-gray-600"
              // TODO: Remove responsiveness hack
              style={{ maxWidth: "100vw" }}
            >
              <table className="w-full">
                <thead className="text-left">
                  <tr className="bg-gray-100">
                    <th className="px-2 py-4 first:pl-8 last:pr-8">Member Number</th>
                    <th className="px-2 py-4 first:pl-8 last:pr-8">Member Name</th>
                    <th className="px-2 py-4 first:pl-8 last:pr-8">Visit Number</th>
                    <th className="px-2 py-4 first:pl-8 last:pr-8">Provider</th>
                    <th className="px-2 py-4 first:pl-8 last:pr-8">Benefit</th>
                    <th className="px-2 py-4 first:pl-8 last:pr-8">Invoice Number</th>
                    <th className="px-2 py-4 first:pl-8 last:pr-8">Date</th>
                    <th className="px-2 py-4 first:pl-8 last:pr-8">Amount</th>
                    <th className="px-2 py-4 first:pl-8 last:pr-8">Status</th>
                    <th className="px-2 py-4 first:pl-8 last:pr-8">Action</th>
                  </tr>
                </thead>

                <tbody>
                  {claims.map((claim) => (
                    <tr className="cursor-pointer" key={claim.invoiceId}>
                      <td className="px-2 py-4 first:pl-8 last:pr-8">{claim.memberNumber}</td>
                      <td className="px-2 py-4 first:pl-8 last:pr-8">{claim.memberName}</td>
                      <td className="px-2 py-4 first:pl-8 last:pr-8">{claim.visitNumber}</td>
                      <td className="px-2 py-4 first:pl-8 last:pr-8" title={claim.providerName}>
                        {truncate(claim.providerName, 30)}
                      </td>
                      <td className="px-2 py-4 first:pl-8 last:pr-8" title={claim.benefitName}>
                        {truncate(claim.benefitName, 30)}
                      </td>
                      <td className="px-2 py-4 first:pl-8 last:pr-8">{claim.invoiceNumber}</td>
                      <td className="px-2 py-4 first:pl-8 last:pr-8">
                        {/* isoDateStringToLocal(claim.createdAt ?? "") */}
                        {claim.createdAt}
                      </td>
                      <td className="px-2 py-4 first:pl-8 last:pr-8">
                        {formatMoney(claim.invoiceAmount)}
                      </td>
                      <td className="px-2 py-4 first:pl-8 last:pr-8">
                        <StatusBadge status={getVisitStatusBadge(claim.status)}>
                          {visitStatusLabels.get(claim.status)}
                        </StatusBadge>
                      </td>
                      <td className="px-2 py-4 first:pl-8 last:pr-8">
                        <a
                          href={`${baseUrl}/api/v1/visit/statement/singleClaim?hospitalProviderId=${claim.hospitalProviderId}&visitNumber=${claim.visitNumber}&invoiceNumber=${claim.invoiceNumber}&invoiceId=${claim.invoiceId}`}
                          target="_blank"
                          className="flex gap-2 rounded px-4 py-2 font-medium text-gray-500 hover:text-gray-700"
                        >
                          <ArrowDownTrayIcon className="h-5 w-5" />
                        </a>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {claimsResponse && (
          <div className="pb-4">
            <Pagination
              totalElements={claimsResponse?.data.totalElements}
              totalPages={claimsResponse?.data.totalPages}
              setPage={setPage}
              setSize={setSize}
              page={page}
              size={size}
              isLoading={isClaimsFetchingOnly}
            />
          </div>
        )}
      </div>
    </div>
  );
}
