import { skipToken } from "@reduxjs/toolkit/query";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { NavLink, useLocation, useSearchParams } from "react-router-dom";
import { useSearchBeneficiariesByPayerQuery } from "~lib/api";
import { Beneficiary } from "~lib/api/types";
import medicineSvg from "~lib/assets/medicine.svg";
import { Empty } from "~lib/components";
import ErrorMessage from "~lib/components/Error";
import { Form } from "~lib/components/Form";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { queryError } from "~lib/utils";
import Input from "../../rhf-components/Input";
import UserService from "../../services/UserService";
import useDebounce from "../../utils/useDebounce";
import MainWrapper from "../../components/ui/MainWrapper";

type Inputs = {
  query: string;
};

type Props = {
  /**
   * @returns A tuple with the first element being the path to redirect to and the second element being the state to pass to the redirected page.
   */
  next?: <T = undefined>(beneficiary: Beneficiary) => [string, T?] | undefined;
};

const MIN_QUERY_LENGTH = 5;

export default function FindMember({ next: redirect }: Props) {
  const methods = useForm<Inputs>({
    mode: "onBlur",
    defaultValues: {
      query: "",
    },
  });

  const { watch, setFocus } = methods;
  const { query } = watch();

  const queryDebounced = useDebounce(query, 200);
  const queryTrimmed = queryDebounced.trim();
  const isQueryValid = queryTrimmed.length >= MIN_QUERY_LENGTH;

  const payerIdRaw = UserService.getPayer()?.tokenParsed?.["payerId"];
  const payerId = window.parseInt(payerIdRaw) || undefined;

  const {
    data: beneficiariesResponse,
    error: beneficiariesError,
    isLoading: isBeneficiariesLoading,
    isFetching: isBeneficiariesFetching,
  } = useSearchBeneficiariesByPayerQuery(
    !payerId || !isQueryValid
      ? skipToken
      : {
          payerId,
          query: queryTrimmed,
        },
  );

  const beneficiaries = beneficiariesResponse?.data;
  const beneficiariesErrorMessage = beneficiariesError ? queryError(beneficiariesError) : undefined;

  // Location state takes precedence over next() prop
  const location = useLocation();
  const locationState = location.state;

  // URL params take precedence over next() prop
  const [searchParams] = useSearchParams();
  const redirectPath = searchParams.get("redirect") ?? "";

  useEffect(() => {
    setFocus("query");
  }, [setFocus]);

  return (
    <MainWrapper className="space-y-4 px-4 text-gray-700">
      <div className="flex items-center justify-center p-8">
        <Form
          methods={methods}
          onSubmit={(_data, e) => {
            e?.preventDefault();
          }}
          className="flex-grow"
        >
          <label htmlFor="" className="flex flex-col items-center gap-2">
            <span className="font-medium text-gray-700">Search Member</span>
            <Input
              type="text"
              name="query"
              className="w-full min-w-56 max-w-2xl px-4"
              placeholder="Member number or member name"
            />
          </label>
        </Form>
      </div>

      {!isQueryValid ? (
        <div className="flex items-center justify-center p-4">
          <div className="flex flex-col items-center gap-1">
            <img src={medicineSvg} alt="Image of a doctor" className="h-auto w-56" />

            <p className="text-center text-gray-300">
              Enter at least 5 characters to search for a member...
            </p>
          </div>
        </div>
      ) : isBeneficiariesLoading || isBeneficiariesFetching ? (
        <div className="flex items-center justify-center py-8">
          <LoadingIcon className="h-6 w-6" />
        </div>
      ) : beneficiariesError ? (
        <div className="flex flex-col items-center">
          <ErrorMessage
            title="Error fetching beneficiaries"
            message={beneficiariesErrorMessage ?? "Refresh the page to retry"}
          />
        </div>
      ) : !beneficiaries?.length ? (
        <div className="flex items-center justify-center p-8">
          <div className="flex flex-col items-center">
            <Empty message="No members found." />
          </div>
        </div>
      ) : (
        // TODO: Test subgrid in older browsers
        <ul className="mx-auto grid max-w-4xl grid-cols-4 space-y-2 pb-4">
          {beneficiaries.map((member) => (
            <li
              className="col-span-4 grid grid-rows-2 rounded border border-dashed border-light-blue-700 p-5 shadow-md hover:shadow-lg lg:grid-rows-1 lg:space-x-8 xl:space-x-12"
              key={member.id}
              style={{
                gridTemplateColumns: "subgrid",
              }}
            >
              <div className="hidden font-medium lg:block">
                <p>Name: </p>
                <p>Member Number: </p>
                <p>Scheme: </p>
              </div>

              <div className="col-span-4 lg:col-span-2">
                <p className="whitespace-break-spaces">{member.name}</p>
                <p>{member.memberNumber}</p>
                <p>{member.category.policy.plan.name}</p>
              </div>

              <div className="col-span-4 flex grow items-center justify-end lg:col-span-1">
                <NavLink
                  className="flex gap-2 rounded bg-blue-500 px-4 py-2 font-medium text-white enabled:hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-60"
                  to={redirectPath || redirect?.(member)?.[0] || "#"}
                  // WARNING: state must be valid StartVisitState
                  state={
                    locationState
                      ? {
                          ...locationState,
                          beneficiaryId: member.id,
                          memberNumber: member?.memberNumber,
                        }
                      : redirect?.(member)?.[1]
                  }
                >
                  Continue
                </NavLink>
              </div>
            </li>
          ))}
        </ul>
      )}
    </MainWrapper>
  );
}
