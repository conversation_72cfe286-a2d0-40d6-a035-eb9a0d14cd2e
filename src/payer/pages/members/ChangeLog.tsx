import {
  Beneficiary,
  ChangeLog,
  beneficiary<PERSON>ieldLabels,
  changeLogActionLabels,
} from "~lib/api/types";
import { Empty } from "~lib/components";
import { clsx, formatDateGB, formatDateStringGB } from "~lib/utils";

interface Props {
  beneficiary: Beneficiary;
}

export default function ChangeLog({ beneficiary}: Props) {
  const changeLog = beneficiary.changeLog;

  const changeLogDiff = (log: ChangeLog) =>
    Object.fromEntries(
      Object.entries(JSON.parse(log.data) || {}).filter(
        ([key, value]) => value != null && Object.keys(beneficiary).includes(key),
      ),
    );

  const renderValue = (value: any, key?: string) => {
    // Format dob
    // TODO: Fix change long date format in the backend
    if (Array.isArray(value) && key === "dob") {
      const [year, month, day] = value;
      const date = new Date(year, month - 1, day);
      return formatDateGB(date);
    }

    if (typeof value === "boolean") {
      return value ? "Yes" : "No";
    }

    if (typeof value === "number") {
      return value;
    }

    if (typeof value === "string") {
      return value;
    }

    if (Array.isArray(value)) {
      return value.join(", ");
    }

    return JSON.stringify(value);
  }

  return (
    <div className="px-8">
      {changeLog.length > 0 ? (
        <ol className="relative border-l border-gray-200 dark:border-gray-700">
          {changeLog.toReversed().map((log, index) => (
            <li className={clsx("ml-4", index < changeLog.length - 1 && "mb-10")} key={log.id}>
              <div className="absolute -left-1.5 mt-1.5 h-3 w-3 rounded-full border border-white bg-gray-200 dark:border-gray-900 dark:bg-gray-700"></div>
              <time className="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">
                {formatDateStringGB(log.time)}
              </time>
              <h3 className="text-lg font-medium text-gray-600">{log.reason}</h3>

              {/* TODO: Show previous and new value */}
              <p className="mb-4 text-gray-400">
                <span className="text-gray-500">{log.user}</span> performed action{" "}
                <span className="text-gray-500">"{changeLogActionLabels.get(log.action)}"</span>
              </p>

              {Object.keys(changeLogDiff(log))?.length > 0 && (
                <div className="overflow-x-auto bg-white text-gray-600">
                  <table className="w-full">
                    <thead className="text-left">
                      <tr className="bg-gray-100">
                        <th className="px-2 py-2 first:pl-4 last:pr-4">Field</th>
                        <th className="px-2 py-2 first:pl-4 last:pr-4">New Value</th>
                      </tr>
                    </thead>

                    <tbody>
                      {Object.entries(changeLogDiff(log)).map(([key, value]) => (
                        <tr key={key}>
                          <th className="px-2 py-2 first:pl-4 last:pr-4">
                            {beneficiaryFieldLabels[key as keyof Beneficiary]}
                          </th>
                          <td className="px-2 py-2 first:pl-4 last:pr-4">{renderValue(value, key)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </li>
          ))}
        </ol>
      ) : (
        <Empty message="No update found" />
      )}
    </div>
  );
}
