import { useState } from "react";
import { TailSpin } from "react-loader-spinner";
import { toast } from "react-toastify";
import { useGetPaymentsQuery } from "../../../api/settlement/settlementApi";
import NoClaimsVouchering from "../../../components/illustrations/NoClaimsVouchering";
import EmptyState from "../../../components/ui/EmptyState";
import SearchInput from "../../../components/ui/input/SearchInput";
import MainWrapper from "../../../components/ui/MainWrapper";
import PrimaryPagination from "../../../components/ui/pagination/PrimaryPagination";
import Text from "../../../components/ui/typography/Text";
import UserService from "../../../services/UserService";
import SettlementNavigation from "../SettlementNavigation";
import { UserInfo } from "../settlementTypes";
import PaidVouchersCard from "./PaidVouchersCard";
import LoadingAnimation from "../../../components/animations/LoadingAnimation/LoadingAnimation";

export default function Paid() {
  const userInfo = UserService.kcObject.tokenParsed as UserInfo;
  const [query, setQuery] = useState("");
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const {
    data: paymentsData,
    isLoading: isLoadingPayments,
    isFetching: isFetchingPayments,
    isError: isPaymentsError,
  } = useGetPaymentsQuery({
    parameters: {
      query: {
        payerId: userInfo.payerId,
        paid: true,
        page: page,
        size: pageSize,
        ...(query && { query: query }),
      },
    },
  });

  if (isPaymentsError) toast.error("Error occurred while getting payments!");

  const payments = paymentsData?.data.content;
  const totalPages = paymentsData?.data?.totalPages;
  const totalElements = paymentsData?.data?.totalElements;

  const isPendingPayments = isLoadingPayments || isFetchingPayments;

  return (
    <MainWrapper>
      <section className="mb-6 flex items-center justify-between">
        <Text variant="heading">Payment List</Text>
        <SearchInput
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Search by reference number"
          className="w-[400px]"
        />
      </section>

      <SettlementNavigation />

      <div className="mt-4 flex flex-col gap-4">
        {isPendingPayments ? (
          <div className="flex h-[60vh] items-center justify-center">
            <LoadingAnimation size={50} />
          </div>
        ) : payments && payments?.length > 0 ? (
          <>
            {payments?.map((payment) => <PaidVouchersCard key={payment.id} payment={payment} />)}

            <PrimaryPagination
              onPageNumberClick={(page) => setPage(page)}
              onSizeChange={(size) => setPageSize(size)}
              pageNumber={page}
              pageSize={pageSize}
              totalPages={totalPages as number}
              totalElements={totalElements as number}
            />
          </>
        ) : (
          <EmptyState
            illustration={<NoClaimsVouchering />}
            message={{
              title: "No payments available",
              description:
                "You haven't made any payments yet. Once the payments have been completed, they'll be displayed here for your review and further actions...",
            }}
          />
        )}
      </div>
    </MainWrapper>
  );
}
