import React from "react";
import { render, screen } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import PaidInvoiceDetailsTable from "./PaidInvoiceDetailsTable";
import { Invoice } from "../settlementTypes";

vi.mock("../../../components/ui/table/TableHeaderItem", () => ({
  default: ({ item }: { item: string }) => <th>{item}</th>,
}));

vi.mock("./PaidInVoiceDetailsRow", () => ({
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  default: ({ invoice }: { invoice: any }) => (
    <tr data-testid="invoice-row">
      <td>{invoice.invoiceNumber}</td>
    </tr>
  ),
}));

describe("PaidInvoiceDetailsTable", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders the table with header row even if invoices are undefined", () => {
    render(<PaidInvoiceDetailsTable invoices={undefined} />);

    expect(screen.getByRole("table")).toBeInTheDocument();

    expect(screen.getByText("INVOICE NUMBER")).toBeInTheDocument();
    expect(screen.getByText("ACCOUNT NAME")).toBeInTheDocument();
    expect(screen.getByText("INVOICE AMOUNT")).toBeInTheDocument();
    expect(screen.getByText("INVOICE DATE")).toBeInTheDocument();

    expect(screen.queryByTestId("invoice-row")).not.toBeInTheDocument();
  });

  it("renders invoice rows when invoices are provided", () => {
    const invoices = [
      {
        id: 1,
        invoiceNumber: "INV001",
        accountName: "Account1",
        invoiceAmount: 100,
        invoiceDate: "2023-02-01",
      },
      {
        id: 2,
        invoiceNumber: "INV002",
        accountName: "Account2",
        invoiceAmount: 200,
        invoiceDate: "2023-02-02",
      },
    ] as unknown as Invoice[];

    render(<PaidInvoiceDetailsTable invoices={invoices} />);

    expect(screen.getByRole("table")).toBeInTheDocument();

    expect(screen.getByText("INVOICE NUMBER")).toBeInTheDocument();
    expect(screen.getByText("ACCOUNT NAME")).toBeInTheDocument();
    expect(screen.getByText("INVOICE AMOUNT")).toBeInTheDocument();
    expect(screen.getByText("INVOICE DATE")).toBeInTheDocument();

    const invoiceRows = screen.getAllByTestId("invoice-row");
    expect(invoiceRows).toHaveLength(invoices.length);

    expect(screen.getByText("INV001")).toBeInTheDocument();
    expect(screen.getByText("INV002")).toBeInTheDocument();
  });
});
