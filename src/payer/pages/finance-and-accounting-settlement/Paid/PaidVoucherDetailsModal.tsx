import { XMarkIcon } from "@heroicons/react/24/outline";
import React, { useState } from "react";
import { TailSpin } from "react-loader-spinner";
import { toast } from "react-toastify";
import PdfFileIcon from "../../../components/icons/PdfFileIcon";
import Text from "../../../components/ui/typography/Text";
import { baseUrl } from "../../../lib/Utils";
import { downloadFile } from "../../../utils/downloadFile";
import { Payment } from "../settlementTypes";
import PaidVoucherDetailsTable from "./PaidVoucherDetailsTable";

type Props = {
  setIsShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  payment: Payment;
};

type ResultUrl = {
  data: string;
  msg: string;
  success: boolean;
};

export default function PaidVoucherDetailsModal({ setIsShowModal, payment }: Props) {
  const [isDownloadingFile, setIsDownloadingFile] = useState(false);

  async function handleDownloadFile() {
    try {
      setIsDownloadingFile(true);

      //get the download url
      const response = await fetch(`${baseUrl}/api/file/download?name=${payment.bankSchedule}`);
      const result = (await response.json()) as ResultUrl;

      downloadFile(result.data, payment.paymentReference);
    } catch (error) {
      if (error instanceof Error) toast.error(error.message);
      console.error(error);
    } finally {
      setIsDownloadingFile(false);
    }
  }

  return (
    <section className="relative overflow-y-auto border  border-slate-100 bg-white p-8 shadow-md">
      <button className="absolute right-6 top-6" onClick={() => setIsShowModal(false)}>
        <XMarkIcon className="w-5" />
      </button>

      <Text variant="heading">Payment Details</Text>

      <div className="mt-4 grid  grid-cols-4 gap-y-6">
        <div className="flex flex-col gap-2">
          <p>Payment Mode</p>
          <p className="text-sm   ">{payment.modeOfPayment}</p>
        </div>

        {payment.modeOfPayment === "Cheque" && (
          <>
            <div className="flex flex-col gap-2">
              <p>Cheque Date</p>
              <p className="text-sm   ">
                {new Intl.DateTimeFormat("en-GB", { dateStyle: "long" }).format(
                  new Date(payment.createdOn),
                )}
              </p>
            </div>

            <div className="flex flex-col gap-2">
              <p>Cheque Number</p>
              <p className="text-sm   ">{payment.chequeNo || "NA"}</p>
            </div>
          </>
        )}

        {payment.modeOfPayment === "BankTransfer" && (
          <>
            <div className="flex flex-col gap-2">
              <p>Account Number</p>
              <p className="text-sm   ">{payment.account[0]?.accountNumber || "NA"}</p>
            </div>

            <div className="flex flex-col gap-2">
              <p>Bank Code</p>
              <p className="text-sm   ">{payment.account[0]?.bankCode || "NA"}</p>
            </div>

            <div className="flex flex-col gap-2">
              <p>Branch Code</p>
              <p className="text-sm   ">{payment.account[0]?.branchCode || "NA"}</p>
            </div>

            <div className="flex flex-col gap-2">
              <p>Account Name</p>
              <p className="text-sm   ">{payment.account[0]?.accountName || "NA"}</p>
            </div>

            <div className="flex flex-col gap-2">
              <p>Bank Name</p>
              <p className="text-sm   ">{payment.account[0]?.bankName || "NA"}</p>
            </div>

            <div className="flex flex-col gap-2">
              <p>Branch Name</p>
              <p className="text-sm   ">{payment.account[0]?.bankBranch || "NA"}</p>
            </div>
          </>
        )}
      </div>

      {payment.modeOfPayment === "BankTransfer" && payment.bankSchedule && (
        <div className="relative mt-8 flex w-fit flex-col gap-2">
          <p className="font-medium">Bank Schedule</p>
          <button className="w-fit rounded bg-slate-100 p-2" onClick={handleDownloadFile}>
            <PdfFileIcon />
          </button>

          {isDownloadingFile && (
            <div className="absolute inset-0 flex items-center justify-center ">
              <TailSpin color="blue" height={40} />
            </div>
          )}
        </div>
      )}

      <div className="mt-8">
        <Text variant="heading">Voucher Details</Text>
      </div>

      <PaidVoucherDetailsTable paymentId={payment.id} />
    </section>
  );
}
