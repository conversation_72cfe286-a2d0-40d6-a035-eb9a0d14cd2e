// PaidInVoiceDetailsRow.test.tsx
import React from "react";
import { render, screen } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import PaidInVoiceDetailsRow from "./PaidInVoiceDetailsRow";

// ----- <PERSON>cks ----- //

// Mock TableDataItem to simply render a <td> element with the provided item text.
vi.mock("../../../components/ui/table/TableDataItem", () => ({
  default: ({ item }: { item: string }) => <td>{item}</td>,
}));

// Mock the utility functions for predictable output.
vi.mock("../../../utils/formatCurrency", () => ({
  formatNumberToKes: (num: number) => `KES ${num.toFixed(2)}`,
}));

vi.mock("../../../utils/convertDateString", () => ({
  convertDateString: (date: string) => date, // For testing, simply return the date string.
}));

// ----- Tests ----- //
describe("PaidInVoiceDetailsRow", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders invoice details correctly", () => {
    // Arrange: Create a dummy invoice object.
    const invoice = {
      id: 1,
      invoiceNumber: "INV-001",
      providerName: "Test Provider",
      totalAmount: 1000,
      createdAt: "2025-01-01T00:00:00Z",
    };

    // Act: Render the component within a valid table structure.
    render(
      <table>
        <tbody>
          <PaidInVoiceDetailsRow invoice={invoice} />
        </tbody>
      </table>,
    );

    // Assert: Check that each transformed invoice detail is rendered.
    expect(screen.getByText("INV-001")).toBeInTheDocument();
    expect(screen.getByText("Test Provider")).toBeInTheDocument();
    expect(screen.getByText("KES 1000.00")).toBeInTheDocument();
    expect(screen.getByText("2025-01-01T00:00:00Z")).toBeInTheDocument();
  });
});
