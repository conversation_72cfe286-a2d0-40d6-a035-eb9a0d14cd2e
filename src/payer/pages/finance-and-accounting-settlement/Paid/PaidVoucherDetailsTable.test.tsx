import { render, screen } from "@testing-library/react";
import { Mock, vi } from "vitest";
import * as settlementApi from "../../../api/settlement/settlementApi";
import PaidVoucherDetailsTable from "./PaidVoucherDetailsTable";

vi.mock("../../../api/settlement/settlementApi", () => ({
  useGetPaymentVouchersQuery: vi.fn(),
}));

vi.mock("../../../services/UserService", () => ({
  default: { kcObject: { tokenParsed: { payerId: 123 } } },
}));

vi.mock("react-loader-spinner", () => ({
  TailSpin: () => <div data-testid="tail-spin">Loading spinner</div>,
}));

vi.mock("../../../components/ui/table/TableHeaderItem", () => ({
  default: ({ item }: { item: string }) => <th>{item}</th>,
}));

vi.mock("./PaidVoucherDetailsRow", () => ({
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  default: ({ voucher }: { voucher: any }) => (
    <tr data-testid="voucher-row">
      <td>{voucher.voucherNumber}</td>
    </tr>
  ),
}));

describe("PaidVoucherDetailsTable", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders loading spinner when vouchers are loading", () => {
    (settlementApi.useGetPaymentVouchersQuery as Mock).mockReturnValue({
      data: undefined,
      isLoading: true,
    });

    render(<PaidVoucherDetailsTable paymentId={1} />);

    expect(screen.getByTestId("tail-spin")).toBeInTheDocument();
    expect(screen.queryByRole("table")).not.toBeInTheDocument();
  });

  it("renders table with voucher details when data is loaded", () => {
    const vouchersData = {
      data: {
        content: [
          {
            id: 1,
            voucherNumber: "VOUCHER001",
            voucherAmount: 100,
            discount: 10,
            payableAmount: 90,
            dateCreated: "2023-02-15",
            createdBy: "Admin",
          },
          {
            id: 2,
            voucherNumber: "VOUCHER002",
            voucherAmount: 200,
            discount: 20,
            payableAmount: 180,
            dateCreated: "2023-02-16",
            createdBy: "User",
          },
        ],
      },
    };

    (settlementApi.useGetPaymentVouchersQuery as Mock).mockReturnValue({
      data: vouchersData,
      isLoading: false,
    });

    render(<PaidVoucherDetailsTable paymentId={1} />);

    expect(screen.getByRole("table")).toBeInTheDocument();

    expect(screen.getByText("VOUCHER NUMBER")).toBeInTheDocument();
    expect(screen.getByText("VOUCHER AMOUNT")).toBeInTheDocument();
    expect(screen.getByText("DISCOUNT")).toBeInTheDocument();
    expect(screen.getByText("PAYABLE AMOUNT")).toBeInTheDocument();
    expect(screen.getByText("DATE CREATED")).toBeInTheDocument();
    expect(screen.getByText("CREATED BY")).toBeInTheDocument();
    expect(screen.getByText("ACTION")).toBeInTheDocument();

    const voucherRows = screen.getAllByTestId("voucher-row");
    expect(voucherRows).toHaveLength(2);
    expect(screen.getByText("VOUCHER001")).toBeInTheDocument();
    expect(screen.getByText("VOUCHER002")).toBeInTheDocument();
  });
});
