import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { Provider } from "react-redux";
import { toast } from "react-toastify";
import { Mock, vi } from "vitest";
import { useSendRemittanceMutation } from "../../../api/settlement/settlementApi";
import { store } from "../../../store";
import { Payment } from "../settlementTypes";
import PaidVouchersCard from "./PaidVouchersCard";

vi.mock("../../../api/settlement/settlementApi", async () => ({
  ...(await vi.importActual("../../../api/settlement/settlementApi")),
  useSendRemittanceMutation: vi.fn(),
}));

vi.mock("../../../services/UserService", () => ({
  default: {
    kcObject: {
      tokenParsed: {
        preferred_username: "testUser",
      },
    },
  },
}));

describe("PaidVouchersCard", () => {
  const dummyPayment = {
    id: 123,
    paymentReference: "REF123",
    account: [{ accountName: "Test Account" }],
    amount: 1000,
    createdOn: new Date().toISOString(),
    createdBy: { userName: "payerUser" },
  } as Payment;

  let sendRemittanceMock: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    sendRemittanceMock = vi.fn();

    (useSendRemittanceMutation as Mock).mockReturnValue([sendRemittanceMock, { isLoading: false }]);
    vi.clearAllMocks();

    global.ResizeObserver = class {
      observe = () => null;
      unobserve = () => null;
      disconnect = () => null;
    };
  });

  it("renders payment details", () => {
    render(<PaidVouchersCard payment={dummyPayment} />);

    expect(screen.getByText(dummyPayment.paymentReference)).toBeInTheDocument();

    expect(screen.getByText(dummyPayment?.account[0]?.accountName as string)).toBeInTheDocument();

    expect(screen.getByText(/Amount Paid :/)).toBeInTheDocument();
    expect(screen.getByText(/Paid By :/)).toBeInTheDocument();
    expect(screen.getByText(dummyPayment.createdBy.userName)).toBeInTheDocument();
  });

  it('opens details modal when clicking "View voucher details"', async () => {
    render(
      <Provider store={store}>
        <PaidVouchersCard payment={dummyPayment} />
      </Provider>,
    );

    const menuButton = screen.getAllByRole("button")[0];
    fireEvent.click(menuButton as HTMLElement);

    const viewDetailsButton = await screen.findByText("View voucher details");
    fireEvent.click(viewDetailsButton);

    await waitFor(() => {
      expect(screen.getByRole("dialog")).toBeInTheDocument();
    });
  });

  it("handles send remittance success", async () => {
    sendRemittanceMock.mockResolvedValue({});
    render(<PaidVouchersCard payment={dummyPayment} />);
    const menuButton = screen.getAllByRole("button")[0];
    fireEvent.click(menuButton as HTMLElement);

    const sendRemittanceButton = await screen.findByText("Send remittance");
    fireEvent.click(sendRemittanceButton);

    const successModalTitle = await screen.findByText("Remittance sent successfully.");
    expect(successModalTitle).toBeInTheDocument();
  });

  it("handles send remittance failure", async () => {
    sendRemittanceMock.mockResolvedValue({
      error: {
        data: { error: "Test error message" },
      },
    });

    const toastErrorSpy = vi.spyOn(toast, "error");
    render(<PaidVouchersCard payment={dummyPayment} />);
    const menuButton = screen.getAllByRole("button")[0];
    fireEvent.click(menuButton as HTMLElement);
    const sendRemittanceButton = await screen.findByText("Send remittance");
    fireEvent.click(sendRemittanceButton);

    await waitFor(() => {
      expect(toastErrorSpy).toHaveBeenCalledWith("Test error message");
    });
  });

  it("shows progress modal when loading", () => {
    (useSendRemittanceMutation as Mock).mockReturnValue([sendRemittanceMock, { isLoading: true }]);
    render(<PaidVouchersCard payment={dummyPayment} />);

    expect(screen.getByText("Sending remittance...")).toBeInTheDocument();
  });
});
