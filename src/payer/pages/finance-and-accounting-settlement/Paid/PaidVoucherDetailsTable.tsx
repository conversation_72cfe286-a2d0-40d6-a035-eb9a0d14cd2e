import { TailSpin } from "react-loader-spinner";
import { useGetPaymentVouchersQuery } from "../../../api/settlement/settlementApi";
import TableHeaderItem from "../../../components/ui/table/TableHeaderItem";
import UserService from "../../../services/UserService";
import { UserInfo } from "../settlementTypes";
import PaidVoucherDetailsRow from "./PaidVoucherDetailsRow";

type Props = {
  paymentId: number;
};

export default function PaidVoucherDetailsTable({ paymentId }: Props) {
  const userInfo = UserService.kcObject.tokenParsed as UserInfo;

  const { data: vouchersData, isLoading: isLoadingVouchers } = useGetPaymentVouchersQuery({
    payerId: userInfo.payerId,
    searchParams: {
      paymentIds: [paymentId],
    },
  });

  const vouchers = vouchersData?.data.content;

  return isLoadingVouchers ? (
    <div className="flex w-full items-center justify-center p-8">
      <TailSpin color="blue" />
    </div>
  ) : (
    <table className="mt-2 w-full text-center">
      <thead>
        <tr className="border-b">
          <TableHeaderItem item="VOUCHER NUMBER" />
          <TableHeaderItem item="VOUCHER AMOUNT" />
          <TableHeaderItem item="DISCOUNT" />
          <TableHeaderItem item="PAYABLE AMOUNT" />
          <TableHeaderItem item="DATE CREATED" />
          <TableHeaderItem item="CREATED BY" />
          <TableHeaderItem item="ACTION" />
        </tr>
      </thead>

      <tbody>
        {vouchers &&
          vouchers.length > 0 &&
          vouchers.map((voucher) => <PaidVoucherDetailsRow key={voucher.id} voucher={voucher} />)}
      </tbody>
    </table>
  );
}
