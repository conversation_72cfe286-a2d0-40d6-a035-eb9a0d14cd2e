import { XMarkIcon } from "@heroicons/react/24/outline";
import React, { useState } from "react";
import { TailSpin } from "react-loader-spinner";
import { useGetVoucherInvoicesQuery } from "../../../api/settlement/settlementApi";
import SecondaryPagination from "../../../components/ui/pagination/SecondaryPagination";
import Text from "../../../components/ui/typography/Text";
import PaidInvoiceDetailsTable from "./PaidInvoiceDetailsTable";

type Props = {
  setIsShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  voucherId: number;
};
export default function PaidInvoiceDetailsModal({ setIsShowModal, voucherId }: Props) {
  const [page, setPage] = useState(1);
  const size = 3;

  const { data: invoicesData, isLoading: isLoadingInvoices } = useGetVoucherInvoicesQuery({
    searchParams: {
      voucherIds: [voucherId],
      page: page,
      size: size,
    },
  });

  const invoices = invoicesData?.data.content;
  const totalPages = invoicesData?.data.totalPages as number;
  const totalElements = invoicesData?.data.totalPages as number;

  return (
    <section className="relative overflow-y-auto border  border-slate-100 bg-white p-8 shadow-md">
      <button className="absolute right-6 top-6 " onClick={() => setIsShowModal(false)}>
        <XMarkIcon className="w-5" />
      </button>

      {isLoadingInvoices ? (
        <div className="flex w-full items-center justify-center p-8">
          <TailSpin color="blue" />
        </div>
      ) : (
        <div className=" flex items-center justify-between pr-8">
          <Text variant="heading">Invoice Details</Text>
          {totalElements > 0 && (
            <SecondaryPagination
              currentPage={page}
              setCurrentPage={setPage}
              size={size}
              totalElements={totalElements}
              totalPages={totalPages}
            />
          )}
        </div>
      )}

      <PaidInvoiceDetailsTable invoices={invoices} />
    </section>
  );
}
