import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it, vi } from "vitest";
import { Voucher } from "../settlementTypes";
import PaidVoucherDetailsRow from "./PaidVoucherDetailsRow";

vi.mock("./PaidInvoiceDetailsModal", () => {
  return {
    default: (props: { voucherId: string; setIsShowModal: (show: boolean) => void }) => (
      <div data-testid="paid-invoice-details-modal">
        Modal Content for voucher {props.voucherId}
      </div>
    ),
  };
});

describe("PaidVoucherDetailsRow", () => {
  const voucher = {
    id: 1,
    voucherNo: "VCH001",
    amount: 1000,
    discount: 100,
    payableAmount: 900,
    createdOn: "2021-09-15T00:00:00Z",
    createdBy: { userName: "JohnDoe" },
  } as Voucher;

  beforeAll(() => {
    global.ResizeObserver = class {
      observe = () => null;
      unobserve = () => null;
      disconnect = () => null;
    };
  });

  it("renders voucher details and toggles modal on button click", async () => {
    render(
      <table>
        <tbody>
          <PaidVoucherDetailsRow voucher={voucher} />
        </tbody>
      </table>,
    );

    expect(screen.getByText(voucher.voucherNo)).toBeInTheDocument();

    const formattedDate = new Intl.DateTimeFormat("en-GB", { dateStyle: "long" }).format(
      new Date(voucher.createdOn),
    );
    expect(screen.getByText(formattedDate)).toBeInTheDocument();

    expect(screen.getByText(voucher?.createdBy?.userName as string)).toBeInTheDocument();

    expect(screen.queryByTestId("paid-invoice-details-modal")).not.toBeInTheDocument();

    const button = screen.getByRole("button");
    await userEvent.click(button);

    expect(screen.getByTestId("paid-invoice-details-modal")).toBeInTheDocument();
    expect(screen.getByText(`Modal Content for voucher ${voucher.id}`)).toBeInTheDocument();
  });
});
