import { Menu } from "@headlessui/react";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { useSendRemittanceMutation } from "../../../api/settlement/settlementApi";
import CustomSuccessModal from "../../../components/CustomSuccessModal/CustomSuccessModal";
import DotIcon from "../../../components/icons/DotIcon";
import EyeIcon from "../../../components/icons/EyeIcon";
import ReceiptIcon from "../../../components/icons/ReceiptIcon";
import ThreeDotsIcon from "../../../components/icons/ThreeDotsIcon";
import CardWrapper from "../../../components/ui/CardWrapper";
import DialogWrapper from "../../../components/ui/modal/DialogWrapper";
import ProgressModal from "../../../components/ui/modal/ProgressModal";
import UserService from "../../../services/UserService";
import { formatNumberToKes } from "../../../utils/formatCurrency";
import getTimeBetweenAsString from "../../../utils/getTimeBetweenAsString";
import { Payment, UserInfo } from "../settlementTypes";
import PaidVoucherDetailsModal from "./PaidVoucherDetailsModal";

type Props = {
  payment: Payment;
};

export default function PaidVouchersCard({ payment }: Props) {
  const userInfo = UserService.kcObject.tokenParsed as UserInfo;
  const [isShowSuccessModal, setIsShowSuccessModal] = useState(false);

  const [isShowDetailsModal, setIsShowDetailsModal] = useState(false);

  const offsetInMillis = new Date().getTimezoneOffset() * 60 * 1000;

  const timeZoneTimeString = new Date(
    new Date(payment.createdOn).getTime() - offsetInMillis,
  ).toISOString();

  const [sendRemittance, { isLoading }] = useSendRemittanceMutation();

  async function handleSendRemittance() {
    try {
      const response = await sendRemittance({
        body: {
          paymentId: payment.id,
          actionedBy: userInfo.preferred_username,
        },
      });

      if ("error" in response) {
        const { error } = response;
        if (error && "data" in error) {
          const errorData = error.data;
          if (typeof errorData === "object" && errorData !== null && "error" in errorData) {
            const errorMessage = errorData.error as string;
            throw new Error(errorMessage);
          }
        }

        throw new Error("Error occurred when uploading file!");
      }

      setIsShowSuccessModal(true);
    } catch (error) {
      if (error instanceof Error) {
        console.error(error.message);
        toast.error(error.message);
      } else {
        console.error(error);
        toast.error("Error occurred");
      }
    }
  }

  useEffect(() => {
    if (!isShowSuccessModal) return;

    const timeout = setTimeout(() => {
      setIsShowSuccessModal(false);
    }, 3000);

    return () => clearTimeout(timeout);
  }, [isShowSuccessModal]);

  return (
    <CardWrapper className="relative flex flex-col gap-2 text-sm ">
      <p className="text-base font-medium text-[#304254]">{payment.paymentReference || "NA"}</p>

      <p className="flex items-center gap-2">
        <span className="text-[#6B7280]">Account Name :</span>{" "}
        <span className="text-[#374151]">{payment.account[0]?.accountName || "NA"} </span>
      </p>

      <p className="flex items-center gap-2">
        <span className="text-[#6B7280]">Amount Paid :</span>{" "}
        <span className="text-[#374151]">{formatNumberToKes(payment.amount)}</span>
      </p>

      <p className="flex items-center gap-2">
        <span className="text-[#6B7280]">Date Paid :</span>
        <span className="flex items-center gap-2">
          <span className="text-[#374151]">
            {new Intl.DateTimeFormat("en-GB", {
              dateStyle: "long",
              timeZone: "Africa/Nairobi",
            }).format(new Date(payment.createdOn))}
          </span>
          <DotIcon />

          <span className="text-[#374151]">{getTimeBetweenAsString(timeZoneTimeString)}</span>
        </span>
      </p>

      <p className="flex items-center gap-2">
        <span className="text-[#6B7280]">Paid By :</span>{" "}
        <span className="text-[#374151]">{payment?.createdBy?.userName || "NA"}</span>
      </p>

      <Menu as={`div`} className={`absolute  right-6 top-4`}>
        <Menu.Button>
          <ThreeDotsIcon />
        </Menu.Button>

        <Menu.Items
          className={`absolute right-0 flex w-56 flex-col gap-4 rounded-md border p-3 shadow-md`}
        >
          <Menu.Item>
            <button
              className="flex items-center gap-2 text-sm font-medium "
              onClick={() => setIsShowDetailsModal(true)}
            >
              <EyeIcon />
              <span>View voucher details</span>
            </button>
          </Menu.Item>

          <Menu.Item>
            <button
              className="flex items-center gap-2 text-sm font-medium "
              onClick={handleSendRemittance}
            >
              <ReceiptIcon />
              <span>Send remittance</span>
            </button>
          </Menu.Item>
        </Menu.Items>
      </Menu>

      <DialogWrapper
        maxWidth="max-w-[800px]"
        onClose={() => setIsShowDetailsModal(false)}
        show={isShowDetailsModal}
      >
        <PaidVoucherDetailsModal setIsShowModal={setIsShowDetailsModal} payment={payment} />
      </DialogWrapper>

      <ProgressModal
        isProgressModalOpen={isLoading}
        onClose={() => null}
        title="Sending remittance..."
      />

      <CustomSuccessModal
        isOpen={isShowSuccessModal}
        title="Remittance sent successfully."
        description="Your remittance has been successfully processed and sent. Thank you!!!"
        isShowCloseButton
        setIsOpen={() => setIsShowSuccessModal(false)}
      />
    </CardWrapper>
  );
}
