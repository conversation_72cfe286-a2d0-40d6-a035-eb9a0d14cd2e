import TableDataItem from "../../../components/ui/table/TableDataItem";
import { convertDateString } from "../../../utils/convertDateString";
import { formatNumberToKes } from "../../../utils/formatCurrency";
import { Invoice } from "../settlementTypes";

type Props = {
  invoice: Invoice;
};

export default function PaidInVoiceDetailsRow({ invoice }: Props) {
  return (
    <tr className="border-b">
      <TableDataItem item={invoice.invoiceNumber} />
      <TableDataItem item={invoice.providerName} />
      <TableDataItem item={formatNumberToKes(invoice.totalAmount)} />
      <TableDataItem item={convertDateString(invoice.createdAt)} />
    </tr>
  );
}
