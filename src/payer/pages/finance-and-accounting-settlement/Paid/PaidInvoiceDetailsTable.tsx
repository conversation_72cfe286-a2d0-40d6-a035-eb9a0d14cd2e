import TableHeaderItem from "../../../components/ui/table/TableHeaderItem";
import { Invoice } from "../settlementTypes";
import PaidInVoiceDetailsRow from "./PaidInVoiceDetailsRow";

type Props = {
  invoices: Invoice[] | undefined;
};

export default function PaidInvoiceDetailsTable({ invoices }: Props) {
  return (
    <table className="mt-8 w-full text-center">
      <thead>
        <tr className="border-b">
          <TableHeaderItem item="INVOICE NUMBER" />
          <TableHeaderItem item="ACCOUNT NAME" />
          <TableHeaderItem item="INVOICE AMOUNT" />
          <TableHeaderItem item="INVOICE DATE" />
        </tr>
      </thead>

      <tbody>
        {invoices?.map((invoice) => <PaidInVoiceDetailsRow invoice={invoice} key={invoice.id} />)}
      </tbody>
    </table>
  );
}
