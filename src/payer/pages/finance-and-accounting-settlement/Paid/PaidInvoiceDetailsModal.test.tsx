/* eslint-disable @typescript-eslint/no-explicit-any */
import { fireEvent, render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it, Mock, vi } from "vitest";
import { useGetVoucherInvoicesQuery } from "../../../api/settlement/settlementApi";
import PaidInvoiceDetailsModal from "./PaidInvoiceDetailsModal";

vi.mock("../../../api/settlement/settlementApi", () => ({
  useGetVoucherInvoicesQuery: vi.fn(),
}));
const mockUseGetVoucherInvoicesQuery = useGetVoucherInvoicesQuery as Mock;

vi.mock("react-loader-spinner", () => ({
  TailSpin: () => <div data-testid="tailspin">Loading...</div>,
}));

vi.mock("../../../components/ui/pagination/SecondaryPagination", () => ({
  default: ({ currentPage, totalPages }: any) => (
    <div data-testid="secondary-pagination">
      Pagination: {currentPage} of {totalPages}
    </div>
  ),
}));

vi.mock("../../../components/ui/typography/Text", () => ({
  default: ({ children }: any) => <div>{children}</div>,
}));

vi.mock("./PaidInvoiceDetailsTable", () => ({
  default: ({ invoices }: any) => (
    <div data-testid="paid-invoice-details-table">
      {invoices ? `${invoices.length} invoices` : "No invoices"}
    </div>
  ),
}));

describe("PaidInvoiceDetailsModal", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders loading spinner when invoices are loading", () => {
    mockUseGetVoucherInvoicesQuery.mockReturnValue({
      isLoading: true,
      data: undefined,
    });

    const mockSetIsShowModal = vi.fn();
    render(<PaidInvoiceDetailsModal setIsShowModal={mockSetIsShowModal} voucherId={123} />);

    expect(screen.getByTestId("tailspin")).toBeInTheDocument();

    expect(screen.queryByText("Invoice Details")).not.toBeInTheDocument();
  });

  it("renders invoice details and pagination when data is loaded", () => {
    const fakeData = {
      data: {
        content: [{ id: 1 }, { id: 2 }],
        totalPages: 2,
      },
    };

    mockUseGetVoucherInvoicesQuery.mockReturnValue({
      isLoading: false,
      data: fakeData,
    });

    const mockSetIsShowModal = vi.fn();
    render(<PaidInvoiceDetailsModal setIsShowModal={mockSetIsShowModal} voucherId={123} />);

    expect(screen.getByText("Invoice Details")).toBeInTheDocument();

    expect(screen.getByTestId("secondary-pagination")).toBeInTheDocument();

    expect(screen.getByTestId("paid-invoice-details-table")).toHaveTextContent("2 invoices");
  });

  it("calls setIsShowModal with false when the close button is clicked", () => {
    const fakeData = {
      data: {
        content: [],
        totalPages: 0,
      },
    };

    mockUseGetVoucherInvoicesQuery.mockReturnValue({
      isLoading: false,
      data: fakeData,
    });

    const mockSetIsShowModal = vi.fn();
    render(<PaidInvoiceDetailsModal setIsShowModal={mockSetIsShowModal} voucherId={123} />);

    const closeButton = screen.getByRole("button");
    fireEvent.click(closeButton);

    expect(mockSetIsShowModal).toHaveBeenCalledWith(false);
  });
});
