import { useState } from "react";
import EyeIcon from "../../../components/icons/EyeIcon";
import DialogWrapper from "../../../components/ui/modal/DialogWrapper";
import TableDataItem from "../../../components/ui/table/TableDataItem";
import { formatNumberToKes } from "../../../utils/formatCurrency";
import { Voucher } from "../settlementTypes";
import PaidInvoiceDetailsModal from "./PaidInvoiceDetailsModal";

type Props = {
  voucher: Voucher;
};

export default function PaidVoucherDetailsRow({ voucher }: Props) {
  const [isShowInvoiceDetailsModal, setIsShowInvoiceDetailsModal] = useState(false);

  return (
    <>
      <tr className="border-b">
        <TableDataItem item={voucher.voucherNo} />
        <TableDataItem item={formatNumberToKes(voucher.amount)} />
        <TableDataItem item={formatNumberToKes(voucher.discount)} />
        <TableDataItem item={formatNumberToKes(voucher.payableAmount)} />
        <TableDataItem
          item={new Intl.DateTimeFormat("en-GB", { dateStyle: "long" }).format(
            new Date(voucher.createdOn),
          )}
        />
        <TableDataItem item={voucher.createdBy?.userName || "NA"} />
        <td className="px-4 py-2">
          <button onClick={() => setIsShowInvoiceDetailsModal(true)}>
            <EyeIcon fill="#1D4ED8" />
          </button>
        </td>
      </tr>

      <DialogWrapper
        maxWidth="max-w-[750px]"
        onClose={() => setIsShowInvoiceDetailsModal(false)}
        show={isShowInvoiceDetailsModal}
      >
        <PaidInvoiceDetailsModal
          setIsShowModal={setIsShowInvoiceDetailsModal}
          voucherId={voucher.id}
        />
      </DialogWrapper>
    </>
  );
}
