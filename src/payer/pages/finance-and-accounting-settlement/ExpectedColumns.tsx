import DownloadIcon from "../../components/icons/DownloadIcon";
import { baseUrl } from "../../lib/Utils";
import UserService from "../../services/UserService";
import { UserInfo } from "./settlementTypes";

type Props = {
  isShowDownloadButton?: boolean;
};

export default function ExpectedColumns({ isShowDownloadButton = true }: Props) {
  const userInfo = UserService.kcObject.tokenParsed as UserInfo;

  const downloadUrl = `${baseUrl}/api/v1/visit/payments/download/${userInfo.payerId}`;

  return (
    <>
      {isShowDownloadButton && (
        <a
          href={downloadUrl}
          className="ml-auto mt-2 flex w-fit items-center  gap-2 rounded-md bg-blue-600 px-3 py-2 text-xs text-white"
        >
          <DownloadIcon height={18} /> <span>Download Bank Transfer Template</span>
        </a>
      )}

      <p className="text-blue mt-4 text-lg font-semibold">
        <span>Expected Columns</span>
      </p>

      <table className="text-blue mt-4 w-full text-left text-xs">
        <thead className="text-sm ">
          <tr>
            <th className="">COLUMNS</th>
            <th className="">DESCRIPTION</th>
          </tr>
        </thead>

        <tbody className=" text-slate-700">
          <tr className="border-b">
            <td className="py-4">DATE</td>
            <td className="py-4">The template download date</td>
          </tr>
          <tr className="border-b">
            <td className="py-4">BANK CODE</td>
            <td className="py-4">The code of the bank to which the payment is made</td>
          </tr>
          <tr className="border-b">
            <td className="py-4">BRANCH CODE</td>
            <td className="py-4">The code of the bank branch where the payment is made</td>
          </tr>
          <tr className="border-b">
            <td className="py-4">ACCOUNT NUMBER</td>
            <td className="py-4">The account number to which the payment is made</td>
          </tr>
          <tr className="border-b">
            <td className="py-4">ACCOUNT NAME</td>
            <td className="py-4">The account name to which the payment is made</td>
          </tr>
          <tr className="border-b">
            <td className="py-4">TOTAL AMOUNT</td>
            <td className="py-4">The total amount to be paid by the bank transfer.</td>
          </tr>
          {/* <tr className="border-b">
            <td className="py-4">VOUCHER NUMBER</td>
            <td className="py-4">The voucher numbers of the vouchers to be paid.</td>
          </tr> */}
          <tr className="border-b">
            <td className="py-4">PAYMENT REFERENCE</td>
            <td className="py-4">A bank payment transaction reference number.</td>
          </tr>
        </tbody>
      </table>
    </>
  );
}
