import { CheckCircleIcon, ClockIcon } from "@heroicons/react/24/outline";
import { NavLink } from "react-router-dom";
import progressClock from "../../assets/progress_clock.svg";

export default function SettlementNavigation() {
  return (
    <nav className="flex w-fit items-center">
      <NavLink
        to={`/finance-and-accounting/settlement/`}
        className={({ isActive }) =>
          `flex cursor-pointer items-center space-x-2 rounded-l-md border border-gray-200 px-4 py-1.5 text-midGray ${isActive && "bg-faintBlue text-txtBlue"}`
        }
      >
        <ClockIcon className="h-5 w-5" />
        <p className="text-sm">Unpaid</p>
      </NavLink>

      <NavLink
        to={`/finance-and-accounting/settlement/paying`}
        className={({ isActive }) =>
          `flex cursor-pointer items-center space-x-2 border border-gray-200 px-4 py-1.5 text-midGray ${isActive && "bg-faintBlue text-txtBlue"}`
        }
      >
        <img src={progressClock} alt="progress clock" className="h-5 w-5" />
        <p className="text-sm">Paying</p>
      </NavLink>

      <NavLink
        to={`/finance-and-accounting/settlement/paid`}
        className={({ isActive }) =>
          `flex cursor-pointer items-center space-x-2 rounded-r-md border border-gray-200 px-4 py-1.5 text-midGray ${isActive && "bg-faintBlue text-txtBlue"}`
        }
      >
        <CheckCircleIcon className="h-5 w-5" />
        <p className="text-sm">Paid</p>
      </NavLink>
    </nav>
  );
}
