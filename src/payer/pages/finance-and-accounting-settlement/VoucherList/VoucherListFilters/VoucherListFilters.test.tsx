import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { vi } from "vitest";
import VoucherListFilters from "./VoucherListFilters";

// Create mocks for context functions
const mockSetStartDate = vi.fn();
const mockSetEndDate = vi.fn();
const mockSetSelectedAccount = vi.fn();
const mockSetSelectedRegion = vi.fn();

// Mock the voucher list context hook
vi.mock("../useVoucherListContext", () => ({
  default: () => ({
    userInfo: { payerId: "123" },
    startDate: "2023-01-01",
    endDate: "2023-01-31",
    selectedAccount: "",
    selectedRegion: "",
    setStartDate: mockSetStartDate,
    setEndDate: mockSetEndDate,
    setSelectedAccount: mockSetSelectedAccount,
    setSelectedRegion: mockSetSelectedRegion,
  }),
}));

// Mock the API query hooks with predictable data
vi.mock("../../../../api/settlement/settlementApi", () => ({
  useGetProvidersAccountsQuery: () => ({
    data: { data: [{ id: "account1", accountName: "Test Account" }] },
  }),
  useGetPayerRegionsQuery: () => ({
    data: { data: [{ id: "region1", name: "Test Region" }] },
  }),
}));

// Optionally, mock the DateInput to a simple input that calls onChange correctly.
vi.mock("../../../../components/ui/input/DateInput", () => ({
  default: (props: { value: string; placeholder: string; onChange: (v: string) => void }) => (
    <input
      data-testid="date-input"
      value={props.value}
      placeholder={props.placeholder}
      onChange={(e) => props.onChange(e.target.value)}
    />
  ),
}));

// Optionally, mock CustomSelect and CustomOption so we can verify their children are rendered.
vi.mock("./CustomSelect/CustomSelect", () => ({
  default: (props: {
    label: string;
    placeHolder: string;
    children: React.ReactNode;
    value: string;
    onChange: (v: unknown) => void;
  }) => (
    <div data-testid="custom-select">
      <span>{props.label}</span>
      {props.children}
    </div>
  ),
}));
vi.mock("./CustomSelect/CustomOption", () => ({
  default: (props: { value: string; children: React.ReactNode; onClick?: () => void }) => (
    <div data-testid="custom-option" onClick={props.onClick}>
      {props.children}
    </div>
  ),
}));

describe("VoucherListFilters", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders all filter elements correctly", () => {
    render(<VoucherListFilters />);

    // Check for filter labels
    expect(screen.getByText("Account")).toBeInTheDocument();
    expect(screen.getByText("Region")).toBeInTheDocument();
    expect(screen.getByText("Start date")).toBeInTheDocument();
    expect(screen.getByText("End date")).toBeInTheDocument();

    // Check that options from the API mocks render
    expect(screen.getByText("Test Account")).toBeInTheDocument();
    expect(screen.getByText("Test Region")).toBeInTheDocument();

    // Check that date inputs render with the correct placeholders
    expect(screen.getByPlaceholderText("Select the Start date")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Select the End date")).toBeInTheDocument();
  });

  it("calls setStartDate when the start date changes", () => {
    render(<VoucherListFilters />);
    const startDateInput = screen.getByPlaceholderText("Select the Start date");

    fireEvent.change(startDateInput, { target: { value: "2023-02-01" } });
    expect(mockSetStartDate).toHaveBeenCalledWith("2023-02-01");
  });

  it("calls setEndDate when the end date changes", () => {
    render(<VoucherListFilters />);
    const endDateInput = screen.getByPlaceholderText("Select the End date");

    fireEvent.change(endDateInput, { target: { value: "2023-02-28" } });
    expect(mockSetEndDate).toHaveBeenCalledWith("2023-02-28");
  });
});
