import {
  useGetPayerRegionsQuery,
  useGetProvidersAccountsQuery,
} from "../../../../api/settlement/settlementApi";
import DateInput from "../../../../components/ui/input/DateInput";
import useVoucherListContext from "../useVoucherListContext";
import CustomOption from "./CustomSelect/CustomOption";
import CustomSelect from "./CustomSelect/CustomSelect";

export default function VoucherListFilters() {
  const {
    userInfo,
    endDate,
    selectedAccount,
    selectedRegion,
    setEndDate,
    setSelectedAccount,
    setSelectedRegion,
    setStartDate,
    startDate,
  } = useVoucherListContext();

  const { data: accountsData } = useGetProvidersAccountsQuery({
    parameters: { payerId: Number(userInfo?.payerId) },
  });
  const { data: regionsData } = useGetPayerRegionsQuery({
    parameters: { payerId: Number(userInfo?.payerId) },
  });

  const accounts = accountsData?.data;
  const regions = regionsData?.data;

  return (
    <div className="mt-6 grid grid-cols-4 gap-8">
      {/* Account */}
      <CustomSelect
        label="Account"
        placeHolder="Select the Account"
        value={selectedAccount}
        onChange={(value) => setSelectedAccount(value as string)}
      >
        {accounts?.map((account) => (
          <CustomOption key={account.id} value={account.id}>
            {account.accountName}
          </CustomOption>
        ))}
      </CustomSelect>

      {/* Region */}
      <CustomSelect
        label="Region"
        placeHolder="Select the Region"
        value={selectedRegion}
        onChange={(value) => setSelectedRegion(value as string)}
      >
        {regions?.map((region) => (
          <CustomOption key={region.id} value={region.id}>
            {region.name}
          </CustomOption>
        ))}
      </CustomSelect>

      {/* Start date */}
      <div className="flex flex-col gap-2 font-medium">
        <label className="text-sm" htmlFor="">
          Start date
        </label>
        <DateInput
          value={startDate}
          onChange={(value) => setStartDate(value)}
          placeholder="Select the Start date"
        />
      </div>

      {/* End date */}
      <div className="flex flex-col gap-2 font-medium">
        <label className="text-sm" htmlFor="">
          End date
        </label>
        <DateInput
          value={endDate}
          onChange={(value) => setEndDate(value)}
          placeholder="Select the End date"
          position="below right"
        />
      </div>
    </div>
  );
}
