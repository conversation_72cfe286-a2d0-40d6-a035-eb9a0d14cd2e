import { Listbox, Transition } from "@headlessui/react";
import { ChevronDownIcon, XMarkIcon } from "@heroicons/react/20/solid";
import { createContext, Fragment, ReactNode, useState } from "react";

type Props = {
  label?: string;
  placeHolder: string;
  value: string | number | readonly string[] | undefined;
  onChange: (value: string | number | readonly string[] | undefined) => void;
  children: ReactNode;
  className?: string;
  optionsContainerClassName?: string;
  disabled?: boolean;
};

type CustomSelectContextType = {
  selectedOptionName: string;
  setSelectedOptionName: React.Dispatch<React.SetStateAction<string>>;
};

export const CustomSelectContext = createContext<CustomSelectContextType>({
  selectedOptionName: "",
  setSelectedOptionName: () => "",
});

export default function CustomSelect({
  placeHolder,
  label,
  value,
  onChange,
  children,
  className,
  optionsContainerClassName,
  disabled = false,
}: Props) {
  const [selectedOptionName, setSelectedOptionName] = useState("");

  function handleResetSelect(e: React.MouseEvent<SVGSVGElement, MouseEvent>) {
    e.stopPropagation();
    onChange("");
    setSelectedOptionName("");
  }

  return (
    <Listbox defaultValue={value} disabled={disabled} value={value} onChange={onChange}>
      <div className="relative">
        <div className="flex flex-col gap-2">
          {label && (
            <Listbox.Label htmlFor={label} className={`ml-1 cursor-pointer text-sm font-medium`}>
              {label}
            </Listbox.Label>
          )}
          <Listbox.Button
            id={label}
            className={`flex w-full items-center justify-between rounded-md border px-3 py-2 text-xs ${className} ${
              disabled && "cursor-not-allowed opacity-50"
            }`}
          >
            <span className={`${!selectedOptionName && " text-slate-500"}`}>
              {selectedOptionName || placeHolder}
            </span>
            <span>
              {value ? (
                <XMarkIcon
                  className="h-5 w-5 text-gray-600"
                  onClick={(e) => handleResetSelect(e)}
                />
              ) : (
                <ChevronDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
              )}
            </span>
          </Listbox.Button>
        </div>

        <Transition
          as={Fragment}
          leave="transition ease-in duration-100"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <Listbox.Options
            className={` . absolute z-10 mt-1 w-full rounded-md border bg-white shadow-md ${optionsContainerClassName}`}
          >
            <CustomSelectContext.Provider value={{ selectedOptionName, setSelectedOptionName }}>
              {children}
            </CustomSelectContext.Provider>
          </Listbox.Options>
        </Transition>
      </div>
    </Listbox>
  );
}
