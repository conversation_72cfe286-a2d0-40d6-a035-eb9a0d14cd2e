// CustomSelect.test.tsx
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { vi } from "vitest";
import CustomSelect from "./CustomSelect";

describe("CustomSelect", () => {
  test("renders placeholder text when no option is selected", () => {
    const onChange = vi.fn();
    render(
      <CustomSelect placeHolder="Select an option" value="" onChange={onChange}>
        <div>Option 1</div>
      </CustomSelect>,
    );

    // The Listbox.Button should show the placeholder text
    const button = screen.getByRole("button");
    expect(button).toHaveTextContent("Select an option");

    // When no value is provided, the ChevronDownIcon is rendered.
    // We check for an SVG with the expected class from ChevronDownIcon.
    const icon = button.querySelector("svg");
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveClass("text-gray-400");
  });

  test("renders label when provided", () => {
    const onChange = vi.fn();
    render(
      <CustomSelect label="Test Label" placeHolder="Select an option" value="" onChange={onChange}>
        <div>Option 1</div>
      </CustomSelect>,
    );

    expect(screen.getByText("Test Label")).toBeInTheDocument();
  });

  test("calls onChange with an empty string when the XMarkIcon is clicked", () => {
    const onChange = vi.fn();
    render(
      <CustomSelect
        placeHolder="Select an option"
        value="selected value" // any truthy value so that the XMarkIcon is rendered
        onChange={onChange}
      >
        <div>Option 1</div>
      </CustomSelect>,
    );

    // When value is provided, the XMarkIcon is rendered instead of the ChevronDownIcon.
    const button = screen.getByRole("button");
    const icon = button.querySelector("svg");
    expect(icon).toBeInTheDocument();
    // Check for XMarkIcon by confirming it has the expected class.
    expect(icon).toHaveClass("text-gray-600");

    // Simulate clicking the XMarkIcon.
    fireEvent.click(icon!);
    expect(onChange).toHaveBeenCalledWith("");
  });

  test("applies disabled styles when disabled", () => {
    const onChange = vi.fn();
    render(
      <CustomSelect placeHolder="Select an option" value="" onChange={onChange} disabled>
        <div>Option 1</div>
      </CustomSelect>,
    );

    const button = screen.getByRole("button");
    // The disabled prop should add specific styling classes.
    expect(button).toHaveClass("cursor-not-allowed");
    expect(button).toHaveClass("opacity-50");
  });

  test("renders children options when the Listbox is opened", async () => {
    const onChange = vi.fn();
    render(
      <CustomSelect placeHolder="Select an option" value="" onChange={onChange}>
        {/* We add a data-testid to make querying easier */}
        <div data-testid="option-1">Option 1</div>
      </CustomSelect>,
    );

    const button = screen.getByRole("button");
    // Open the Listbox options by clicking the button.
    fireEvent.click(button);

    // The child option should now be visible.
    const option = await screen.findByTestId("option-1");
    expect(option).toBeInTheDocument();
  });
});
