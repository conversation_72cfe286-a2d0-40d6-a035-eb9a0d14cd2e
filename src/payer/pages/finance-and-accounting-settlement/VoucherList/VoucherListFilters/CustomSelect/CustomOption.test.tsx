// CustomOption.test.tsx
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { vi } from "vitest";
import { Listbox } from "@headlessui/react";
import CustomOption from "./CustomOption";
import { CustomSelectContext } from "./CustomSelect";

describe("CustomOption", () => {
  /**
   * Helper function to render the CustomOption wrapped inside the necessary context and Listbox components.
   */
  const setup = (props: { disabled?: boolean } = {}) => {
    const mockSetSelectedOptionName = vi.fn();
    const contextValue = {
      selectedOptionName: "",
      setSelectedOptionName: mockSetSelectedOptionName,
    };

    render(
      <CustomSelectContext.Provider value={contextValue}>
        <Listbox value="" onChange={() => {}}>
          {/* Use Listbox.Options with the static prop to force rendering */}
          <Listbox.Options static>
            <CustomOption value="option1" disabled={props.disabled}>
              Option 1
            </CustomOption>
          </Listbox.Options>
        </Listbox>
      </CustomSelectContext.Provider>,
    );

    return { mockSetSelectedOptionName };
  };

  it("calls setSelectedOptionName with the option text when clicked", () => {
    const { mockSetSelectedOptionName } = setup();
    const optionElement = screen.getByText("Option 1");
    expect(optionElement).toBeInTheDocument();

    // Simulate a click on the option element.
    fireEvent.click(optionElement);

    // Verify that setSelectedOptionName is called with the children ("Option 1").
    expect(mockSetSelectedOptionName).toHaveBeenCalledWith("Option 1");
  });

  it("does not call setSelectedOptionName when the option is disabled", () => {
    const { mockSetSelectedOptionName } = setup({ disabled: true });
    const optionElement = screen.getByText("Option 1");
    expect(optionElement).toBeInTheDocument();

    // Attempt to click the disabled option.
    fireEvent.click(optionElement);

    // Since the option is disabled, the click should not trigger setSelectedOptionName.
    expect(mockSetSelectedOptionName).not.toHaveBeenCalled();
  });
});
