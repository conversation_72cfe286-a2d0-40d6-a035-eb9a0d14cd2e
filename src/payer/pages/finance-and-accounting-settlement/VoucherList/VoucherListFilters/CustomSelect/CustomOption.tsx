import { Listbox } from "@headlessui/react";
import { useCustomSelectContext } from "./useCustomSelectContext";

type Props = {
  value: string | number | readonly string[] | undefined;
  children: string;
  disabled?: boolean;
};

export default function CustomOption({ value, children, disabled = false }: Props) {
  const { setSelectedOptionName, selectedOptionName } = useCustomSelectContext();
  return (
    <Listbox.Option
      className={`  hover:cursor-pointer `}
      onClick={() => setSelectedOptionName(children)}
      value={value}
      disabled={disabled}
    >
      {({ active, selected }) => {
        if (selected && !selectedOptionName) setSelectedOptionName(children);
        return (
          <span
            className={`flex items-center justify-between p-2 text-sm ${active && "bg-faintBlue"}`}
          >
            <span>{children}</span>
          </span>
        );
      }}
    </Listbox.Option>
  );
}
