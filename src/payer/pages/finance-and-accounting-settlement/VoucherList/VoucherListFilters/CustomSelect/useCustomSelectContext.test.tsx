// useCustomSelectContext.test.tsx
import React from "react";
import { render, screen } from "@testing-library/react";
import { vi } from "vitest";
import { CustomSelectContext } from "./CustomSelect"; // Adjust path as needed
import { useCustomSelectContext } from "./useCustomSelectContext"; // Adjust path as needed

// Create a simple component that uses the hook
function TestComponent() {
  const { selectedOptionName } = useCustomSelectContext();
  return <div data-testid="context-value">{selectedOptionName}</div>;
}

describe("useCustomSelectContext", () => {
  it("returns the context value when used inside a provider", () => {
    const mockSetSelectedOptionName = vi.fn();
    const contextValue = {
      selectedOptionName: "Test Option",
      setSelectedOptionName: mockSetSelectedOptionName,
    };

    render(
      <CustomSelectContext.Provider value={contextValue}>
        <TestComponent />
      </CustomSelectContext.Provider>,
    );

    const displayedValue = screen.getByTestId("context-value");
    expect(displayedValue).toHaveTextContent("Test Option");
  });

  it("throws an error when used outside of a provider", () => {
    // Override the provider value with null to simulate a missing context.
    expect(() =>
      render(
        <CustomSelectContext.Provider value={null as any}>
          <TestComponent />
        </CustomSelectContext.Provider>,
      ),
    ).toThrowError("useCustomSelectContext must be used within a CustomSelectProvider");
  });
});
