import React from "react";
import { render, screen } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import VoucherList from "./VoucherList";

// --- Mock nested components ---

vi.mock("../../../components/ui/MainWrapper", () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="main-wrapper">{children}</div>
  ),
}));

vi.mock("../SettlementNavigation", () => ({
  default: () => <div data-testid="settlement-navigation">SettlementNavigation</div>,
}));

vi.mock("./HeaderSection", () => ({
  default: () => <div data-testid="header-section">HeaderSection</div>,
}));

vi.mock("./UnpaidVouchers/UnpaidVouchers", () => ({
  default: () => <div data-testid="unpaid-vouchers">UnpaidVouchers</div>,
}));

vi.mock("./VoucherListContextProvider", () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="voucher-list-context-provider">{children}</div>
  ),
}));

describe("VoucherList", () => {
  it("renders all nested components within the context provider and main wrapper", () => {
    render(<VoucherList />);

    // Ensure the context provider wrapper is rendered
    expect(screen.getByTestId("voucher-list-context-provider")).toBeInTheDocument();

    // Ensure the main wrapper is rendered and includes children
    expect(screen.getByTestId("main-wrapper")).toBeInTheDocument();

    // Check for the header section, settlement navigation, and unpaid vouchers components
    expect(screen.getByTestId("header-section")).toBeInTheDocument();
    expect(screen.getByTestId("settlement-navigation")).toBeInTheDocument();
    expect(screen.getByTestId("unpaid-vouchers")).toBeInTheDocument();
  });
});
