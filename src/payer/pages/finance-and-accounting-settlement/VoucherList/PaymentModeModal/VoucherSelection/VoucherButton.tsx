import { useState } from "react";

type Props = {
  handleCheck: () => void;
  handleUncheck: () => void;
  checked: boolean;
};
export default function VoucherButton({ handleCheck, handleUncheck, checked }: Props) {
  const [isChecked, setIsChecked] = useState(checked);

  function handleClick() {
    if (isChecked) {
      handleUncheck();
    } else {
      handleCheck();
    }
    setIsChecked(!isChecked);
  }

  return (
    <button
      className={`h-[15px] w-[15px] rounded-full border p-[3px] ${
        isChecked ? "border-blue-600 bg-blue-50" : "border-slate-400"
      }`}
      onClick={handleClick}
    >
      {isChecked && <div className="h-full w-full rounded-full bg-blue-600"></div>}
    </button>
  );
}
