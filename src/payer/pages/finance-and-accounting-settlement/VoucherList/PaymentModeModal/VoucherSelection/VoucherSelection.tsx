import { useState } from "react";
import { TailSpin } from "react-loader-spinner";
import { useGetPaymentVouchersQuery } from "../../../../../api/settlement/settlementApi";
import DialogWrapper from "../../../../../components/ui/modal/DialogWrapper";
import SecondaryPagination from "../../../../../components/ui/pagination/SecondaryPagination";
import { formatNumberToKes } from "../../../../../utils/formatCurrency";
import { VoucherPaymentStatus } from "../../../settlementTypes";
import useVoucherListContext from "../../useVoucherListContext";
import usePaymentModalContext from "../usePaymentModalContext";
import PaymentConfirmationModal from "./PaymentConfirmationModal";
import VoucherCard from "./VoucherCard";

export default function VoucherSelection() {
  const {
    isShowPaymentConfirmationModal,
    currentAccount,
    selectedVouchers,
    setIsShowPaymentConfirmationModal,
  } = usePaymentModalContext();
  const { userInfo } = useVoucherListContext();

  const [currentPage, setCurrentPage] = useState(1);
  const size = 6;

  const {
    data: vouchersData,
    isLoading: isLoadingVouchers,
    isFetching: isFetchingVouchers,
  } = useGetPaymentVouchersQuery({
    payerId: Number(userInfo?.payerId),
    searchParams: {
      page: currentPage,
      size: size,
      voucherPaymentStatus: VoucherPaymentStatus.Unpaid,
      providerAccountIds: [Number(currentAccount)],
    },
  });

  const isPending = isLoadingVouchers || isFetchingVouchers;
  const vouchers = vouchersData?.data.content;
  const totalElements = vouchersData?.data.totalElements;
  const totalPages = vouchersData?.data.totalPages;

  return (
    <>
      <div className=" mt-8 flex  items-center justify-between">
        <p className="text-blue text-xl font-semibold">
          <span>Select vouchers</span> <span className="text-red-600">*</span>
        </p>

        <SecondaryPagination
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          size={size}
          totalElements={totalElements || 0}
          totalPages={totalPages || 0}
        />
      </div>

      {/* Vouchers */}
      <div className="mt-4 flex flex-col gap-2">
        {isPending && (
          <div className="flex h-[300px] items-center justify-center">
            <TailSpin color="blue" />
          </div>
        )}

        {!isPending &&
          vouchers?.map((voucher) => <VoucherCard key={voucher.id} voucher={voucher} />)}
      </div>

      <p className="mt-4">
        <span>Running Total: </span>{" "}
        <span className="font-semibold">
          {formatNumberToKes(
            selectedVouchers.reduce(
              (accumulator, currentValue) => accumulator + currentValue.amount,
              0,
            ),
          )}
        </span>
      </p>

      <DialogWrapper
        maxWidth="max-w-[700px]"
        onClose={() => setIsShowPaymentConfirmationModal(false)}
        show={isShowPaymentConfirmationModal}
      >
        <PaymentConfirmationModal />
      </DialogWrapper>

      {/* {isShowPaymentConfirmationModal && <PaymentConfirmationModal />} */}
    </>
  );
}
