// PaymentConfirmationModal.test.tsx
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import PaymentConfirmationModal from "./PaymentConfirmationModal";
import { PaymentMode, ProgressStage } from "../PaymentModalContextProvider";
import usePaymentModalContext from "../usePaymentModalContext";

// --- Mock external dependencies ---

// Mock the context hook so we can control its values
vi.mock("../usePaymentModalContext", () => ({
  default: vi.fn(),
}));

// Mock the XIcon and CircleTickIcon components
vi.mock("../../../../../components/icons/XIcon", () => ({
  default: () => <div data-testid="x-icon" />,
}));

vi.mock("../../../../../components/icons/CircleTickIcon", () => ({
  default: () => <div data-testid="circle-tick-icon" />,
}));

// Mock the currency formatting utility
vi.mock("../../../../../utils/formatCurrency", () => ({
  formatNumberToKes: (num: number) => `KES ${num}`,
}));

describe("PaymentConfirmationModal Component", () => {
  let mockSetIsShowPaymentConfirmationModal: ReturnType<typeof vi.fn>;
  let mockSetIsBankTransferPaymentConfirmed: ReturnType<typeof vi.fn>;
  let mockSetProgressStage: ReturnType<typeof vi.fn>;
  let mockHandleCreatePayment: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    mockSetIsShowPaymentConfirmationModal = vi.fn();
    mockSetIsBankTransferPaymentConfirmed = vi.fn();
    mockSetProgressStage = vi.fn();
    mockHandleCreatePayment = vi.fn();

    // Provide default context values (non-bank-transfer scenario)
    (usePaymentModalContext as vi.Mock).mockReturnValue({
      setIsShowPaymentConfirmationModal: mockSetIsShowPaymentConfirmationModal,
      progressStage: ProgressStage.One, // default stage
      currentPaymentMode: PaymentMode.CreditCard, // not bank transfer
      setIsBankTransferPaymentConfirmed: mockSetIsBankTransferPaymentConfirmed,
      setProgressStage: mockSetProgressStage,
      selectedVouchers: [
        { id: 1, amount: 100 },
        { id: 2, amount: 200 },
      ],
      handleCreatePayment: mockHandleCreatePayment,
      isLoadingPayment: false,
    });
  });

  it("renders the payment confirmation modal with computed payable amount", () => {
    render(<PaymentConfirmationModal />);

    // Verify the title and computed amount (100 + 200 = 300)
    expect(screen.getByText("Payment Confirmation")).toBeInTheDocument();
    expect(screen.getByText("KES 300")).toBeInTheDocument();

    // Verify that both Cancel and Confirm buttons are present
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Confirm" })).toBeInTheDocument();
  });

  it("closes the modal when the X icon or Cancel button is clicked", () => {
    render(<PaymentConfirmationModal />);

    // The X icon is rendered inside a button (without accessible text), so locate it via its test id
    const xIcon = screen.getByTestId("x-icon");
    const xIconButton = xIcon.closest("button");
    expect(xIconButton).not.toBeNull();
    fireEvent.click(xIconButton!);
    expect(mockSetIsShowPaymentConfirmationModal).toHaveBeenCalledWith(false);

    // Click the Cancel button as well
    const cancelButton = screen.getByRole("button", { name: "Cancel" });
    fireEvent.click(cancelButton);
    // Expect another call to hide the modal
    expect(mockSetIsShowPaymentConfirmationModal).toHaveBeenCalledTimes(2);
  });

  it("calls handleCreatePayment when confirm is clicked and bank transfer conditions are not met", () => {
    render(<PaymentConfirmationModal />);

    const confirmButton = screen.getByRole("button", { name: "Confirm" });
    fireEvent.click(confirmButton);

    // Since we're not in the bank transfer confirmation stage, handleCreatePayment should be called.
    expect(mockHandleCreatePayment).toHaveBeenCalledTimes(1);
    // And no bank transfer related functions should be invoked.
    expect(mockSetIsBankTransferPaymentConfirmed).not.toHaveBeenCalled();
    expect(mockSetProgressStage).not.toHaveBeenCalled();
  });

  it("handles bank transfer confirmation when conditions are met", () => {
    // Set the context for a bank transfer confirmation:
    // progressStage === ProgressStage.Two and currentPaymentMode === PaymentMode.BankTransfer
    (usePaymentModalContext as vi.Mock).mockReturnValue({
      setIsShowPaymentConfirmationModal: mockSetIsShowPaymentConfirmationModal,
      progressStage: ProgressStage.Two,
      currentPaymentMode: PaymentMode.BankTransfer,
      setIsBankTransferPaymentConfirmed: mockSetIsBankTransferPaymentConfirmed,
      setProgressStage: mockSetProgressStage,
      selectedVouchers: [
        { id: 1, amount: 100 },
        { id: 2, amount: 200 },
      ],
      handleCreatePayment: mockHandleCreatePayment,
      isLoadingPayment: false,
    });

    render(<PaymentConfirmationModal />);
    const confirmButton = screen.getByRole("button", { name: "Confirm" });
    fireEvent.click(confirmButton);

    // In bank transfer confirmation, the following should occur:
    expect(mockSetIsBankTransferPaymentConfirmed).toHaveBeenCalledWith(true);
    expect(mockSetProgressStage).toHaveBeenCalled();
    // Verify that setProgressStage was called with an updater function.
    const updater = mockSetProgressStage.mock.calls[0][0];
    expect(updater(2)).toBe(3); // e.g., previous stage 2 becomes 3
    expect(mockSetIsShowPaymentConfirmationModal).toHaveBeenCalledWith(false);
    // And handleCreatePayment should not be called.
    expect(mockHandleCreatePayment).not.toHaveBeenCalled();
  });

  it("disables confirm button and shows 'Loading...' when payment is loading", () => {
    (usePaymentModalContext as vi.Mock).mockReturnValue({
      setIsShowPaymentConfirmationModal: mockSetIsShowPaymentConfirmationModal,
      progressStage: ProgressStage.One,
      currentPaymentMode: PaymentMode.CreditCard,
      setIsBankTransferPaymentConfirmed: mockSetIsBankTransferPaymentConfirmed,
      setProgressStage: mockSetProgressStage,
      selectedVouchers: [{ id: 1, amount: 100 }],
      handleCreatePayment: mockHandleCreatePayment,
      isLoadingPayment: true,
    });

    render(<PaymentConfirmationModal />);
    const confirmButton = screen.getByRole("button", { name: "Loading..." });
    expect(confirmButton).toBeDisabled();
  });
});
