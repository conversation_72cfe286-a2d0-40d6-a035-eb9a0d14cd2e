import VoucherButton from "./VoucherButton";
import { Voucher } from "../../../settlementTypes";
import { formatNumberToKes } from "../../../../../utils/formatCurrency";
import usePaymentModalContext from "../usePaymentModalContext";

type Props = {
  voucher: Voucher;
};

export default function VoucherCard({ voucher }: Props) {
  const { selectedVouchers, setSelectedVouchers } = usePaymentModalContext();

  function addVoucher(newVoucher: Voucher) {
    setSelectedVouchers((prev) => (prev.includes(newVoucher) ? prev : [...prev, newVoucher]));
  }

  function removeVoucher(voucherToRemove: Voucher) {
    setSelectedVouchers((prev) => prev.filter((voucher) => voucher.id !== voucherToRemove.id));
  }

  return (
    <article className="flex gap-4 rounded-md border p-3">
      <VoucherButton
        handleCheck={() => addVoucher(voucher)}
        handleUncheck={() => removeVoucher(voucher)}
        checked={selectedVouchers.includes(voucher)}
      />

      <div className=" flex flex-col gap-1">
        <p className="font-medium">{voucher?.voucherNo || "NA"}</p>
        <p>{formatNumberToKes(voucher?.amount)}</p>
      </div>
    </article>
  );
}
