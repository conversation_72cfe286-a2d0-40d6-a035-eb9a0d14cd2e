// VoucherSelection.test.jsx
import { render, screen } from "@testing-library/react";
import { vi } from "vitest";
import { useGetPaymentVouchersQuery } from "../../../../../api/settlement/settlementApi";
import usePaymentModalContext from "../usePaymentModalContext";
import VoucherSelection from "./VoucherSelection";

// Mock the custom hooks used by VoucherSelection
vi.mock("../usePaymentModalContext", () => ({
  default: vi.fn(() => ({
    isShowPaymentConfirmationModal: false,
    currentAccount: "1",
    selectedVouchers: [],
    setIsShowPaymentConfirmationModal: vi.fn(),
  })),
}));

vi.mock("../../useVoucherListContext", () => ({
  default: vi.fn(() => ({
    userInfo: { payerId: "123" },
  })),
}));

// Mock the API hook with a default implementation
vi.mock("../../../../../api/settlement/settlementApi", () => ({
  useGetPaymentVouchersQuery: vi.fn().mockReturnValue({
    data: {
      data: {
        content: [
          { id: 1, amount: 100 },
          { id: 2, amount: 200 },
        ],
        totalElements: 2,
        totalPages: 1,
      },
    },
    isLoading: false,
    isFetching: false,
  }),
}));

// Mock utility and child components
vi.mock("../../../../../utils/formatCurrency", () => ({
  formatNumberToKes: (num) => `KES ${num}`,
}));

vi.mock("../../../../../components/ui/modal/DialogWrapper", () => ({
  default: ({ children, show, onClose }) =>
    show ? (
      <div data-testid="dialog-wrapper" onClick={onClose}>
        {children}
      </div>
    ) : null,
}));

vi.mock("../../../../../components/ui/pagination/SecondaryPagination", () => ({
  default: (props) => <div data-testid="secondary-pagination">Pagination</div>,
}));

vi.mock("./VoucherCard", () => ({
  default: ({ voucher }) => <div data-testid="voucher-card">Voucher {voucher.id}</div>,
}));

vi.mock("./PaymentConfirmationModal", () => ({
  default: () => <div data-testid="payment-confirmation-modal">Payment Confirmation</div>,
}));

describe("VoucherSelection Component", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("displays loading spinner when vouchers are loading", () => {
    // Override the API hook to simulate a loading state
    (useGetPaymentVouchersQuery as vi.Mock).mockReturnValue({
      data: null,
      isLoading: true,
      isFetching: false,
    });

    render(<VoucherSelection />);

    // Use an escaped selector for the spinner container
    const spinnerContainer = document.querySelector(".h-\\[300px\\]");
    expect(spinnerContainer).toBeInTheDocument();
  });

  it("renders voucher cards, pagination, and running total when data is loaded", () => {
    // Simulate loaded vouchers data
    (useGetPaymentVouchersQuery as vi.Mock).mockReturnValue({
      data: {
        data: {
          content: [
            { id: 1, amount: 100 },
            { id: 2, amount: 200 },
          ],
          totalElements: 2,
          totalPages: 1,
        },
      },
      isLoading: false,
      isFetching: false,
    });

    // Override the payment modal context to include some selected vouchers for running total
    (usePaymentModalContext as vi.Mock).mockReturnValue({
      isShowPaymentConfirmationModal: false,
      currentAccount: "1",
      selectedVouchers: [{ amount: 50 }, { amount: 150 }], // total 200
      setIsShowPaymentConfirmationModal: vi.fn(),
    });

    render(<VoucherSelection />);

    // Verify voucher cards are rendered
    expect(screen.getByText("Voucher 1")).toBeInTheDocument();
    expect(screen.getByText("Voucher 2")).toBeInTheDocument();

    // Verify the pagination component is rendered
    expect(screen.getByTestId("secondary-pagination")).toBeInTheDocument();

    // Verify running total is correctly calculated and formatted ("KES 200")
    expect(screen.getByText("KES 200")).toBeInTheDocument();
  });

  it("renders payment confirmation modal when isShowPaymentConfirmationModal is true", () => {
    // Set the payment modal context to show the modal
    (usePaymentModalContext as vi.Mock).mockReturnValue({
      isShowPaymentConfirmationModal: true,
      currentAccount: "1",
      selectedVouchers: [],
      setIsShowPaymentConfirmationModal: vi.fn(),
    });

    // Provide some voucher data (could be a single voucher)
    (useGetPaymentVouchersQuery as vi.Mock).mockReturnValue({
      data: {
        data: {
          content: [{ id: 1, amount: 100 }],
          totalElements: 1,
          totalPages: 1,
        },
      },
      isLoading: false,
      isFetching: false,
    });

    render(<VoucherSelection />);

    // Verify that the DialogWrapper and PaymentConfirmationModal are rendered
    expect(screen.getByTestId("dialog-wrapper")).toBeInTheDocument();
    expect(screen.getByTestId("payment-confirmation-modal")).toBeInTheDocument();
  });
});
