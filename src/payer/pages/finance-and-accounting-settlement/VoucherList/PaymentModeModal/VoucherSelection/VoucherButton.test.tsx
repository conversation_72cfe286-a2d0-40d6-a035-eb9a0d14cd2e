// VoucherButton.test.tsx
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import VoucherButton from "./VoucherButton";

describe("VoucherButton component", () => {
  it("renders unchecked state and toggles to checked when clicked", () => {
    const handleCheck = vi.fn();
    const handleUncheck = vi.fn();

    render(
      <VoucherButton handleCheck={handleCheck} handleUncheck={handleUncheck} checked={false} />,
    );

    const button = screen.getByRole("button");

    // Initially, unchecked: no inner div and unchecked style applied
    expect(button.querySelector("div")).toBeNull();
    expect(button.className).toContain("border-slate-400");
    expect(button.className).not.toContain("border-blue-600");

    // Click the button, should call handleCheck and update state to checked
    fireEvent.click(button);
    expect(handleCheck).toHaveBeenCalledTimes(1);
    expect(handleUncheck).not.toHaveBeenCalled();

    // After click, the inner circle should be rendered and styles updated
    expect(button.querySelector("div")).toBeInTheDocument();
    expect(button.className).toContain("border-blue-600");
    expect(button.className).toContain("bg-blue-50");
  });

  it("renders checked state and toggles to unchecked when clicked", () => {
    const handleCheck = vi.fn();
    const handleUncheck = vi.fn();

    render(
      <VoucherButton handleCheck={handleCheck} handleUncheck={handleUncheck} checked={true} />,
    );

    const button = screen.getByRole("button");

    // Initially, checked: inner div exists and checked style applied
    expect(button.querySelector("div")).toBeInTheDocument();
    expect(button.className).toContain("border-blue-600");
    expect(button.className).toContain("bg-blue-50");

    // Click the button, should call handleUncheck and update state to unchecked
    fireEvent.click(button);
    expect(handleUncheck).toHaveBeenCalledTimes(1);
    expect(handleCheck).not.toHaveBeenCalled();

    // After click, the inner circle should be removed and unchecked style applied
    expect(button.querySelector("div")).toBeNull();
    expect(button.className).toContain("border-slate-400");
  });
});
