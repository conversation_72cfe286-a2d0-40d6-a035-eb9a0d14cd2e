// VoucherCard.test.tsx
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { vi } from "vitest";
import VoucherCard from "./VoucherCard";
import usePaymentModalContext from "../usePaymentModalContext";

// Create a dummy voucher for testing
const dummyVoucher = { id: 1, voucherNo: "VCH-001", amount: 1000 };

// Create a mock function for setSelectedVouchers
const setSelectedVouchersMock = vi.fn();

// Mock the custom hook usePaymentModalContext
vi.mock("../usePaymentModalContext", () => ({
  default: vi.fn(),
}));

// Mock the formatNumberToKes utility
vi.mock("../../../../../utils/formatCurrency", () => ({
  formatNumberToKes: (num: number) => `KES ${num}`,
}));

// Mock the VoucherButton component to simulate its behavior
vi.mock("./VoucherButton", () => ({
  default: ({
    handleCheck,
    handleUncheck,
    checked,
  }: {
    handleCheck: () => void;
    handleUncheck: () => void;
    checked: boolean;
  }) => (
    <div data-testid="voucher-button">
      <button data-testid="check-button" onClick={handleCheck}>
        Check
      </button>
      <button data-testid="uncheck-button" onClick={handleUncheck}>
        Uncheck
      </button>
      <span data-testid="checked-status">{checked ? "checked" : "not checked"}</span>
    </div>
  ),
}));

describe("VoucherCard Component", () => {
  beforeEach(() => {
    // Reset the mock function between tests
    setSelectedVouchersMock.mockReset();
  });

  it("renders voucher details correctly", () => {
    // Return an empty selectedVouchers list for this test
    (usePaymentModalContext as any).mockReturnValue({
      selectedVouchers: [],
      setSelectedVouchers: setSelectedVouchersMock,
    });

    render(<VoucherCard voucher={dummyVoucher} />);

    // Verify that the voucher number and formatted amount are rendered
    expect(screen.getByText("VCH-001")).toBeInTheDocument();
    expect(screen.getByText("KES 1000")).toBeInTheDocument();

    // VoucherButton should display that the voucher is not checked
    expect(screen.getByTestId("checked-status").textContent).toBe("not checked");
  });

  it("calls setSelectedVouchers with updater to add voucher when check button is clicked", () => {
    (usePaymentModalContext as any).mockReturnValue({
      selectedVouchers: [],
      setSelectedVouchers: setSelectedVouchersMock,
    });

    render(<VoucherCard voucher={dummyVoucher} />);

    const checkButton = screen.getByTestId("check-button");
    fireEvent.click(checkButton);

    // Ensure setSelectedVouchers was called once
    expect(setSelectedVouchersMock).toHaveBeenCalledTimes(1);

    // setSelectedVouchers is called with an updater function.
    const updater = setSelectedVouchersMock.mock.calls[0][0];

    // If previous selected vouchers is an empty array, updater should return an array with the new voucher.
    expect(updater([])).toEqual([dummyVoucher]);

    // If the voucher is already present, the updater should return the same array.
    expect(updater([dummyVoucher])).toEqual([dummyVoucher]);
  });

  it("calls setSelectedVouchers with updater to remove voucher when uncheck button is clicked", () => {
    (usePaymentModalContext as any).mockReturnValue({
      // Initialize with dummyVoucher selected
      selectedVouchers: [dummyVoucher],
      setSelectedVouchers: setSelectedVouchersMock,
    });

    render(<VoucherCard voucher={dummyVoucher} />);

    const uncheckButton = screen.getByTestId("uncheck-button");
    fireEvent.click(uncheckButton);

    // Ensure setSelectedVouchers was called once
    expect(setSelectedVouchersMock).toHaveBeenCalledTimes(1);

    const updater = setSelectedVouchersMock.mock.calls[0][0];

    // When previous array contains the voucher, updater should remove it.
    expect(updater([dummyVoucher])).toEqual([]);
    // When voucher is not in the array, updater should return an empty array.
    expect(updater([])).toEqual([]);
  });
});
