import XIcon from "../../../../../components/icons/XIcon";
import CircleTickIcon from "../../../../../components/icons/CircleTickIcon";
import usePaymentModalContext from "../usePaymentModalContext";
import { PaymentMode, ProgressStage } from "../PaymentModalContextProvider";
import { formatNumberToKes } from "../../../../../utils/formatCurrency";

export default function PaymentConfirmationModal() {
  const {
    setIsShowPaymentConfirmationModal,
    progressStage,
    currentPaymentMode,
    setIsBankTransferPaymentConfirmed,
    setProgressStage,
    selectedVouchers,
    handleCreatePayment,
    isLoadingPayment,
  } = usePaymentModalContext();

  function handleConfirmButtonClick() {
    if (progressStage === ProgressStage.Two && currentPaymentMode === PaymentMode.BankTransfer) {
      setIsBankTransferPaymentConfirmed(true);
      setProgressStage((prev) => prev + 1);
      setIsShowPaymentConfirmationModal(false);
      return;
    }

    handleCreatePayment();
  }

  return (
    <section className="relative  border border-slate-300 bg-white px-12 py-8 text-base shadow-md">
      <button
        className="absolute right-4 top-4"
        onClick={() => setIsShowPaymentConfirmationModal(false)}
      >
        <XIcon height={20} />
      </button>

      <div className="flex gap-6">
        <span className="flex h-[40px] w-[40px] items-center justify-center rounded-full bg-green-100">
          <CircleTickIcon height={22} />
        </span>

        <div className="flex flex-col gap-4">
          <p className="text-xl text-slate-900">Payment Confirmation</p>

          <p className="flex gap-4">
            <span className="text-lg text-slate-600">Payable Amount:</span>
            <span className="text-xl">
              {formatNumberToKes(
                selectedVouchers.reduce(
                  (accumulator, currentValue) => accumulator + currentValue.amount,
                  0,
                ),
              )}
            </span>
          </p>
        </div>
      </div>

      <p className="mt-8 text-slate-600">
        The vouchers you selected total the amount shown above. Please confirm that this is the
        amount you intend to pay before proceeding to finalize the payment.
      </p>

      <div className="mt-8 flex justify-end gap-16">
        <button
          className="rounded-md border px-3 py-2 text-sm "
          onClick={() => setIsShowPaymentConfirmationModal(false)}
        >
          Cancel
        </button>

        <button
          className={`rounded-md border bg-blue-600 px-3 py-2 text-sm text-white ${
            isLoadingPayment && "opacity-50"
          }`}
          onClick={handleConfirmButtonClick}
          disabled={isLoadingPayment}
        >
          {isLoadingPayment ? "Loading..." : "Confirm"}
        </button>
      </div>
    </section>
  );
}
