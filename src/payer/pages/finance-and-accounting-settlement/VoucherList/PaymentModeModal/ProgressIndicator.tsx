import { PaymentMode, ProgressStage } from "./PaymentModalContextProvider";
import ProgressInfo from "./ProgressInfo";
import usePaymentModalContext from "./usePaymentModalContext";

export default function ProgressIndicator() {
  const { progressStage, currentPaymentMode } = usePaymentModalContext();

  return (
    <div className="mb-16 flex flex-col justify-between ">
      <div className="flex w-fit grow flex-col items-center gap-4  ">
        <ProgressInfo
          number={ProgressStage.One}
          title="Payment Mode"
          description="Select the payment method and account to be paid"
        />

        {/* vertical line */}
        <div
          role="separator"
          className={`w-0 grow border   ${
            progressStage > 1 ? "border-blue-600" : "border-slate-300"
          }`}
        ></div>

        <ProgressInfo
          number={ProgressStage.Two}
          title={
            currentPaymentMode === PaymentMode.BankTransfer
              ? "Voucher Selections"
              : "Payment Details"
          }
          description={
            currentPaymentMode === PaymentMode.BankTransfer
              ? "Select the voucher you want to pay"
              : "Add details about the payment i.e bank details or cheque details"
          }
        />

        {/* vertical line */}
        <div
          role="separator"
          className={`w-0 grow border   ${
            progressStage > 2 ? "border-blue-600" : "border-slate-300"
          }`}
        ></div>

        <ProgressInfo
          number={ProgressStage.Three}
          title={
            currentPaymentMode === PaymentMode.BankTransfer
              ? "Payment Details"
              : "Voucher Selections"
          }
          description={
            currentPaymentMode === PaymentMode.BankTransfer
              ? "Add details about the payment i.e bank details or cheque details"
              : "Select the voucher you want to pay"
          }
        />
      </div>
    </div>
  );
}
