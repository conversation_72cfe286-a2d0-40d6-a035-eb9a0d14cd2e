import React from "react";
import XIcon from "../../../../components/icons/XIcon";
import PaymentModalContextProvider from "./PaymentModalContextProvider";
import PaymentView from "./PaymentView";
import ProgressButtons from "./ProgressButtons";
import ProgressIndicator from "./ProgressIndicator";

type Props = {
  setIsShowModal: React.Dispatch<React.SetStateAction<boolean>>;
};

export default function PaymentModeModal({ setIsShowModal }: Props) {
  return (
    <PaymentModalContextProvider>
      <section className="relative flex max-h-[100vh] min-h-[80vh] min-w-[65%] flex-col overflow-y-auto border  border-slate-100 bg-white p-8 text-left shadow-md">
        <button
          data-testid="close-button"
          className="absolute right-4 top-4 "
          onClick={() => setIsShowModal(false)}
        >
          <XIcon width={24} />
        </button>

        <p className="text-blue text-center text-2xl font-semibold">Payment Mode</p>

        <div className="mt-2 grid grow grid-cols-3">
          <ProgressIndicator />

          <PaymentView />
        </div>

        <ProgressButtons />
      </section>
    </PaymentModalContextProvider>
  );
}
