import { useEffect, useRef } from "react";
import { PaymentMode } from "./PaymentModalContextProvider";
import usePaymentModalContext from "./usePaymentModalContext";

export default function SelectMode() {
  const { currentPaymentMode, setCurrentPaymentMode } = usePaymentModalContext();
  const chequeRef = useRef<HTMLInputElement>(null);
  const bankTransferRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (chequeRef.current) chequeRef.current.checked = currentPaymentMode === PaymentMode.Cheque;
    if (bankTransferRef.current)
      bankTransferRef.current.checked = currentPaymentMode === PaymentMode.BankTransfer;
  }, [chequeRef, bankTransferRef, currentPaymentMode]);

  function handlePaymentModeChange(selectedMode: PaymentMode) {
    if (selectedMode === currentPaymentMode) {
      setCurrentPaymentMode(null);
      return;
    }

    setCurrentPaymentMode(selectedMode);
  }
  return (
    <div>
      <p className="text-blue mt-8 text-xl font-semibold">
        <span>Select Mode of payment</span> <span className="text-red-600">*</span>
      </p>
      <div className="mt-4 flex items-center gap-2 text-base">
        <input
          ref={chequeRef}
          onChange={() => handlePaymentModeChange(PaymentMode.Cheque)}
          className="rounded"
          type="checkbox"
          name="cheque"
          id="cheque"
        />
        <label htmlFor="cheque">Cheque</label>
      </div>
      <div className="mt-4 flex items-center gap-2 text-base">
        <input
          ref={bankTransferRef}
          onChange={() => handlePaymentModeChange(PaymentMode.BankTransfer)}
          className="rounded"
          type="checkbox"
          name="bankTransfer"
          id="bankTransfer"
        />
        <label htmlFor="bankTransfer">Bank transfer</label>
      </div>
    </div>
  );
}
