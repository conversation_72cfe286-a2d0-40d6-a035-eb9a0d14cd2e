import { PaymentMode, ProgressStage } from "./PaymentModalContextProvider";
import usePaymentModalContext from "./usePaymentModalContext";

export default function ProgressButtons() {
  const {
    setIsShowPaymentConfirmationModal,
    progressStage,
    currentAccount,
    currentPaymentMode,
    chequeNumber,
    chequeDate,
    setProgressStage,
    selectedVouchers,
    handleCreatePayment,
  } = usePaymentModalContext();

  const isNextButtonDisabled =
    (progressStage === 1 && (!currentAccount || !currentPaymentMode)) ||
    (progressStage === 2 &&
      currentPaymentMode === PaymentMode.Cheque &&
      (!chequeNumber || !chequeDate)) ||
    (progressStage === 2 && selectedVouchers.length <= 0);

  const isPayButtonDisabled = selectedVouchers.length <= 0;

  function handleNextButtonClick() {
    if (progressStage === ProgressStage.Two && currentPaymentMode === PaymentMode.BankTransfer) {
      setIsShowPaymentConfirmationModal(true);
    } else {
      setProgressStage((prev) => prev + 1);
    }
  }

  return (
    <div className=" flex items-center justify-end gap-16">
      {/* Back button */}
      {progressStage > 1 && (
        <button
          className={`w-[80px] rounded-md border px-3 py-2 font-medium  `}
          onClick={() => setProgressStage((prev) => prev - 1)}
        >
          Back
        </button>
      )}

      {/* Next button */}
      {progressStage < 3 && (
        <button
          className={`w-[80px] rounded-md border border-blue-600 bg-blue-600 px-3 py-2 font-medium text-white ${
            isNextButtonDisabled && "cursor-not-allowed opacity-50"
          }`}
          onClick={handleNextButtonClick}
          disabled={isNextButtonDisabled}
        >
          Next
        </button>
      )}

      {/* Pay button */}
      {progressStage > 2 && currentPaymentMode === PaymentMode.Cheque && (
        <button
          className={`w-[80px] rounded-md border border-blue-600 bg-blue-600 px-3 py-2 font-medium text-white ${
            (isNextButtonDisabled || isPayButtonDisabled) && "cursor-not-allowed opacity-50"
          }`}
          onClick={() => setIsShowPaymentConfirmationModal(true)}
          disabled={isNextButtonDisabled || isPayButtonDisabled}
        >
          Pay
        </button>
      )}

      {/* Done button */}
      {progressStage > 2 && currentPaymentMode === PaymentMode.BankTransfer && (
        <button
          className={`w-[80px] rounded-md border border-blue-600 bg-blue-600 px-3 py-2 font-medium text-white ${
            isNextButtonDisabled && "cursor-not-allowed opacity-50"
          }`}
          onClick={() => handleCreatePayment()}
          disabled={isNextButtonDisabled}
        >
          Done
        </button>
      )}
    </div>
  );
}
