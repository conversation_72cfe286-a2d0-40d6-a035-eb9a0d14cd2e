import { render, screen } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import ProgressInfo from "./ProgressInfo";
import { PaymentModalContext } from "./PaymentModalContextProvider";
import { ProgressStage } from "./PaymentModalContextProvider";
import TickIcon from "../../../../components/icons/TickIcon";

vi.mock("../../../../components/icons/TickIcon", () => ({
  default: () => <svg data-testid="tick-icon" />,
}));

describe("ProgressInfo", () => {
  const renderComponent = (progressStage) => {
    return render(
      <PaymentModalContext.Provider value={{ progressStage }}>
        <ProgressInfo number={1} title="Step 1" description="Description 1" />
      </PaymentModalContext.Provider>,
    );
  };

  it("renders the component correctly", () => {
    renderComponent(ProgressStage.One);

    expect(screen.getByText("Step 1")).toBeInTheDocument();
    expect(screen.getByText("Description 1")).toBeInTheDocument();
    expect(screen.getByText("1")).toBeInTheDocument();
  });

  it("displays tick icon when progressStage is beyond the step", () => {
    renderComponent(ProgressStage.Two);

    expect(screen.getByTestId("tick-icon")).toBeInTheDocument();
  });

  it("displays the step number when progressStage is at the step", () => {
    renderComponent(ProgressStage.One);

    expect(screen.getByText("1")).toBeInTheDocument();
  });
});
