import { renderHook } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import { PaymentModalContext } from "./PaymentModalContextProvider";
import usePaymentModalContext from "./usePaymentModalContext";

// Mock Context Provider
const mockContextValue = { isOpen: true, toggleModal: () => {} };

describe("usePaymentModalContext", () => {
  it("throws an error when used outside of the provider", () => {
    expect(() => renderHook(() => usePaymentModalContext())).toThrow(
      "usePaymentModalContext must be used within a PaymentModalContext Provider",
    );
  });

  it("returns the context value when used within the provider", () => {
    const wrapper = ({ children }) => (
      <PaymentModalContext.Provider value={mockContextValue}>
        {children}
      </PaymentModalContext.Provider>
    );

    const { result } = renderHook(() => usePaymentModalContext(), { wrapper });

    expect(result.current).toBe(mockContextValue);
  });
});
