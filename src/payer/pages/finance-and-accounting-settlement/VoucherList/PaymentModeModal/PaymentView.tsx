import ExpectedColumns from "../../ExpectedColumns";
import ChequeDetails from "./ChequeDetails";
import { PaymentMode, ProgressStage } from "./PaymentModalContextProvider";
import SelectAccountName from "./SelectAccountName";
import SelectMode from "./SelectMode";
import usePaymentModalContext from "./usePaymentModalContext";
import VoucherSelection from "./VoucherSelection/VoucherSelection";

export default function PaymentView() {
  const { progressStage, currentPaymentMode, isBankTransferPaymentConfirmed } =
    usePaymentModalContext();
  return (
    <div className="col-span-2 mb-8 ">
      {progressStage === ProgressStage.One && (
        <>
          <SelectMode />

          <SelectAccountName />
        </>
      )}

      {progressStage === ProgressStage.Two && currentPaymentMode === PaymentMode.Cheque && (
        <ChequeDetails />
      )}

      {progressStage === ProgressStage.Two && currentPaymentMode === PaymentMode.BankTransfer && (
        <VoucherSelection />
      )}

      {progressStage === ProgressStage.Three && currentPaymentMode === PaymentMode.Cheque && (
        <VoucherSelection />
      )}

      {progressStage === ProgressStage.Three &&
        currentPaymentMode === PaymentMode.BankTransfer &&
        isBankTransferPaymentConfirmed && <ExpectedColumns isShowDownloadButton={false} />}
    </div>
  );
}
