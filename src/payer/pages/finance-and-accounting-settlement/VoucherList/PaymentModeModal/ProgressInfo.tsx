import TickIcon from "../../../../components/icons/TickIcon";
import { ProgressStage } from "./PaymentModalContextProvider";
import usePaymentModalContext from "./usePaymentModalContext";

type ProgressProps = {
  number: number;
  title: string;
  description: string;
};

export default function ProgressInfo({ number, title, description }: ProgressProps) {
  const { progressStage } = usePaymentModalContext();

  return (
    <div
      className={`relative flex h-[40px] w-[40px] items-center justify-center  rounded-full  p-2 text-sm  ${
        (number === ProgressStage.One && progressStage > ProgressStage.One) ||
        (number === ProgressStage.Two && progressStage > ProgressStage.Two)
          ? "bg-blue-600 text-white"
          : "bg-slate-300 text-slate-800"
      }`}
    >
      {(number === ProgressStage.One && progressStage > ProgressStage.One) ||
      (number === ProgressStage.Two && progressStage > ProgressStage.Two) ? (
        <TickIcon />
      ) : (
        <span>{number}</span>
      )}

      <div className="absolute left-full top-1 ml-4 w-[170px] text-slate-800">
        <p className="mb-2 font-bold">{title}</p>
        <p className="text-xs">{description}</p>
      </div>
    </div>
  );
}
