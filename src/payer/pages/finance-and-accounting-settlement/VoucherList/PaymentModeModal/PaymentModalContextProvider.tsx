import React, { createContext, ReactNode, useEffect, useState } from "react";
import { toast } from "react-toastify";
import { useCreatePaymentMutation } from "../../../../api/settlement/settlementApi";
import LoadingAnimation from "../../../../components/animations/LoadingAnimation/LoadingAnimation";
import SuccessModal from "../../../../components/ui/modal/SuccessModal";
import { Voucher } from "../../settlementTypes";
import useVoucherListContext from "../useVoucherListContext";

export enum PaymentMode {
  Cheque = "Cheque",
  BankTransfer = "BankTransfer",
}

export enum ProgressStage {
  One = 1,
  Two = 2,
  Three = 3,
}

type PaymentModalContextType = {
  currentPaymentMode: PaymentMode | null;
  progressStage: ProgressStage;
  isShowPaymentConfirmationModal: boolean;
  setIsShowPaymentConfirmationModal: React.Dispatch<React.SetStateAction<boolean>>;
  setIsBankTransferPaymentConfirmed: React.Dispatch<React.SetStateAction<boolean>>;
  setProgressStage: React.Dispatch<React.SetStateAction<ProgressStage>>;
  setCurrentPaymentMode: React.Dispatch<React.SetStateAction<PaymentMode | null>>;
  isBankTransferPaymentConfirmed: boolean;
  currentAccount: string | undefined;
  setCurrentAccount: React.Dispatch<React.SetStateAction<string | undefined>>;
  chequeNumber: string | null;
  setChequeNumber: React.Dispatch<React.SetStateAction<string | null>>;
  chequeDate: string | null;
  setChequeDate: React.Dispatch<React.SetStateAction<string | null>>;
  selectedVouchers: Voucher[];
  setSelectedVouchers: React.Dispatch<React.SetStateAction<Voucher[]>>;
  handleCreatePayment(): Promise<void>;
  isLoadingPayment: boolean;
};

type Props = {
  children: ReactNode;
};

export const PaymentModalContext = createContext<PaymentModalContextType | null>(null);

export default function PaymentModalContextProvider({ children }: Props) {
  const [progressStage, setProgressStage] = useState(ProgressStage.One);
  const [isShowPaymentConfirmationModal, setIsShowPaymentConfirmationModal] = useState(false);
  const [isBankTransferPaymentConfirmed, setIsBankTransferPaymentConfirmed] = useState(false);
  const [currentPaymentMode, setCurrentPaymentMode] = useState<PaymentMode | null>(null);
  const [currentAccount, setCurrentAccount] = useState<string>();
  const [chequeNumber, setChequeNumber] = useState<string | null>(null);
  const [chequeDate, setChequeDate] = useState<string | null>(null);
  const [selectedVouchers, setSelectedVouchers] = useState<Voucher[]>([]);

  const [isShowSuccessModal, setIsShowSuccessModal] = useState(false);

  const { userInfo, setIsShowPaymentModal } = useVoucherListContext();

  const [createPayment, { isLoading: isLoadingPayment }] = useCreatePaymentMutation();

  useEffect(() => {
    if (!isShowSuccessModal) return;

    setTimeout(() => {
      setIsShowPaymentModal(false);
    }, 2500);
  }, [isShowSuccessModal, setIsShowPaymentModal]);

  async function handleCreatePayment() {
    try {
      const selectedVoucherIds = selectedVouchers.map((voucher) => voucher.id);

      const result = await createPayment({
        body: {
          payerId: Number(userInfo?.payerId),
          actionedBy: userInfo?.preferred_username as string,
          chequeDate: chequeDate?.split("T")[0] as string,
          chequeNo: chequeNumber as string,
          modeOfPayment: currentPaymentMode as PaymentMode,
          voucherIds: selectedVoucherIds,
        },
      });

      if ("error" in result) throw new Error("An error occurred while creating payment!");

      if (currentPaymentMode === PaymentMode.Cheque) {
        setIsShowSuccessModal(true);
      } else {
        setIsShowPaymentModal(false);
      }
    } catch (error) {
      if (error instanceof Error) toast.error(error.message);
      console.error(error);
    }
  }

  return (
    <PaymentModalContext.Provider
      value={{
        progressStage,
        isShowPaymentConfirmationModal,
        setIsShowPaymentConfirmationModal,
        setIsBankTransferPaymentConfirmed,
        currentPaymentMode,
        setProgressStage,
        setCurrentPaymentMode,
        isBankTransferPaymentConfirmed,
        currentAccount,
        setCurrentAccount,
        chequeDate,
        chequeNumber,
        setChequeDate,
        setChequeNumber,
        selectedVouchers,
        setSelectedVouchers,
        handleCreatePayment,
        isLoadingPayment,
      }}
    >
      {children}
      <SuccessModal
        title="Payment successful"
        description="The voucher you selected has been paid successfully. Thank you!!!"
        onClose={() => setIsShowSuccessModal(true)}
        isSuccessModalOpen={isShowSuccessModal}
      />

      {isLoadingPayment && (
        <div className="fixed inset-0 z-20 flex items-center justify-center bg-white bg-opacity-50">
          <LoadingAnimation size={100} />
        </div>
      )}
    </PaymentModalContext.Provider>
  );
}
