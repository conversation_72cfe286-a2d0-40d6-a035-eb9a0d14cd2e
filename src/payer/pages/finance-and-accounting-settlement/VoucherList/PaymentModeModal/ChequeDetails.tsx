import DateInput from "../../../../components/ui/input/DateInput";
import usePaymentModalContext from "./usePaymentModalContext";

export default function ChequeDetails() {
  const { chequeNumber, setChequeNumber, chequeDate, setChequeDate } = usePaymentModalContext();

  return (
    <>
      <p className="text-blue mt-8 text-xl font-semibold">
        <span>Enter cheque details</span> <span className="text-red-600">*</span>
      </p>

      <div className="mt-6">
        <label className=" flex items-center gap-1">
          <span>Cheque number</span> <span className="text-red-600">*</span>
        </label>

        <input
          placeholder="Enter cheque number"
          className="mt-4 w-[40%] rounded-md border-slate-300 text-xs"
          value={chequeNumber || ""}
          onChange={(e) => setChequeNumber(e.target.value)}
        />
      </div>

      <div className="mt-6">
        <label className=" flex items-center gap-1">
          <span>Cheque date</span> <span className="text-red-600">*</span>
        </label>

        <DateInput
          placeholder="Select the date"
          value={chequeDate as string}
          className="mt-4 w-[40%]"
          onChange={(value) => setChequeDate(value)}
          maxDate={new Date()}
        />
      </div>
    </>
  );
}
