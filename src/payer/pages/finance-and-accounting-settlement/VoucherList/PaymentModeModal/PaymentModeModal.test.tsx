// PaymentModeModal.test.tsx
import React from "react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import PaymentModeModal from "./PaymentModeModal";
import { Provider } from "react-redux";
import { store } from "../../../../store";

// Mock the useVoucherListContext hook so that PaymentModalContextProvider doesn't throw.
// Adjust the relative path to match your project structure.
vi.mock("../useVoucherListContext", () => ({
  default: () => ({
    voucherList: [],
    setVoucherList: vi.fn(),
  }),
}));

// Mock the XIcon component.
vi.mock("../../../../components/icons/XIcon", () => ({
  default: ({ width }: { width: number }) => (
    <div data-testid="x-icon" style={{ width }}>
      XIcon
    </div>
  ),
}));

// Mock child components to simplify our assertions.
vi.mock("./PaymentView", () => ({
  default: () => <div data-testid="payment-view">PaymentView</div>,
}));

vi.mock("./ProgressButtons", () => ({
  default: () => <div data-testid="progress-buttons">ProgressButtons</div>,
}));

vi.mock("./ProgressIndicator", () => ({
  default: () => <div data-testid="progress-indicator">ProgressIndicator</div>,
}));

describe("PaymentModeModal", () => {
  let mockSetIsShowModal: jest.Mock;

  beforeEach(() => {
    mockSetIsShowModal = vi.fn();
  });

  it("renders modal content correctly", () => {
    render(
      <Provider store={store}>
        <PaymentModeModal setIsShowModal={mockSetIsShowModal} />
      </Provider>,
    );

    // Check that the close button and XIcon are rendered.
    expect(screen.getByTestId("close-button")).toBeInTheDocument();
    expect(screen.getByTestId("x-icon")).toBeInTheDocument();

    // Check that the header text is rendered.
    expect(screen.getByText("Payment Mode")).toBeInTheDocument();

    // Check that the child components are rendered.
    expect(screen.getByTestId("progress-indicator")).toBeInTheDocument();
    expect(screen.getByTestId("payment-view")).toBeInTheDocument();
    expect(screen.getByTestId("progress-buttons")).toBeInTheDocument();
  });

  it("calls setIsShowModal(false) when the close button is clicked", () => {
    render(
      <Provider store={store}>
        <PaymentModeModal setIsShowModal={mockSetIsShowModal} />
      </Provider>,
    );

    // Simulate a click on the close button and verify the callback is called.
    fireEvent.click(screen.getByTestId("close-button"));
    expect(mockSetIsShowModal).toHaveBeenCalledWith(false);
  });
});
