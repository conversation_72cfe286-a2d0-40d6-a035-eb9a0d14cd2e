import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import SelectMode from "./SelectMode";
import { PaymentModalContext, PaymentMode } from "./PaymentModalContextProvider";

const mockContextValue = {
  currentPaymentMode: null,
  setCurrentPaymentMode: vi.fn(),
};

describe("SelectMode", () => {
  it("renders the component correctly", () => {
    render(
      <PaymentModalContext.Provider value={mockContextValue}>
        <SelectMode />
      </PaymentModalContext.Provider>,
    );
    expect(screen.getByText("Select Mode of payment")).toBeInTheDocument();
    expect(screen.getByLabelText("Cheque")).toBeInTheDocument();
    expect(screen.getByLabelText("Bank transfer")).toBeInTheDocument();
  });

  it("calls setCurrentPaymentMode when selecting a payment mode", () => {
    render(
      <PaymentModalContext.Provider value={mockContextValue}>
        <SelectMode />
      </PaymentModalContext.Provider>,
    );

    const chequeInput = screen.getByLabelText("Cheque");
    fireEvent.click(chequeInput);
    expect(mockContextValue.setCurrentPaymentMode).toHaveBeenCalledWith(PaymentMode.Cheque);
  });

  it("unselects the current payment mode if clicked again", () => {
    mockContextValue.currentPaymentMode = PaymentMode.Cheque;
    render(
      <PaymentModalContext.Provider value={mockContextValue}>
        <SelectMode />
      </PaymentModalContext.Provider>,
    );

    const chequeInput = screen.getByLabelText("Cheque");
    fireEvent.click(chequeInput);
    expect(mockContextValue.setCurrentPaymentMode).toHaveBeenCalledWith(null);
  });
});
