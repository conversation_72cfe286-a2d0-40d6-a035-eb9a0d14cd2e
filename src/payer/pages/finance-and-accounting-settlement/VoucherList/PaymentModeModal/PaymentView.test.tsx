// PaymentView.test.tsx
import React from "react";
import { describe, it, expect, vi, afterEach } from "vitest";
import { render, screen } from "@testing-library/react";
import PaymentView from "./PaymentView";
import { ProgressStage, PaymentMode } from "./PaymentModalContextProvider";

// Create a mock function for the payment modal context hook.
const mockUsePaymentModalContext = vi.fn();

// Mock the usePaymentModalContext hook.
vi.mock("./usePaymentModalContext", () => ({
  default: () => mockUsePaymentModalContext(),
}));

// Mock child components to render a unique element.
vi.mock("./SelectMode", () => ({
  default: () => <div data-testid="select-mode">SelectMode</div>,
}));

vi.mock("./SelectAccountName", () => ({
  default: () => <div data-testid="select-account-name">SelectAccountName</div>,
}));

vi.mock("./ChequeDetails", () => ({
  default: () => <div data-testid="cheque-details">ChequeDetails</div>,
}));

vi.mock("./VoucherSelection/VoucherSelection", () => ({
  default: () => <div data-testid="voucher-selection">VoucherSelection</div>,
}));

vi.mock("../../ExpectedColumns", () => ({
  default: ({ isShowDownloadButton }: { isShowDownloadButton: boolean }) => (
    <div data-testid="expected-columns">{isShowDownloadButton ? "Download" : "No Download"}</div>
  ),
}));

describe("PaymentView", () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it("renders SelectMode and SelectAccountName when progressStage is One", () => {
    mockUsePaymentModalContext.mockReturnValue({
      progressStage: ProgressStage.One,
      currentPaymentMode: PaymentMode.Cheque, // irrelevant in stage one
      isBankTransferPaymentConfirmed: false,
    });

    render(<PaymentView />);
    expect(screen.getByTestId("select-mode")).toBeDefined();
    expect(screen.getByTestId("select-account-name")).toBeDefined();
  });

  it("renders ChequeDetails when progressStage is Two and currentPaymentMode is Cheque", () => {
    mockUsePaymentModalContext.mockReturnValue({
      progressStage: ProgressStage.Two,
      currentPaymentMode: PaymentMode.Cheque,
      isBankTransferPaymentConfirmed: false,
    });

    render(<PaymentView />);
    expect(screen.getByTestId("cheque-details")).toBeDefined();
  });

  it("renders VoucherSelection when progressStage is Two and currentPaymentMode is BankTransfer", () => {
    mockUsePaymentModalContext.mockReturnValue({
      progressStage: ProgressStage.Two,
      currentPaymentMode: PaymentMode.BankTransfer,
      isBankTransferPaymentConfirmed: false,
    });

    render(<PaymentView />);
    expect(screen.getByTestId("voucher-selection")).toBeDefined();
  });

  it("renders VoucherSelection when progressStage is Three and currentPaymentMode is Cheque", () => {
    mockUsePaymentModalContext.mockReturnValue({
      progressStage: ProgressStage.Three,
      currentPaymentMode: PaymentMode.Cheque,
      isBankTransferPaymentConfirmed: false,
    });

    render(<PaymentView />);
    expect(screen.getByTestId("voucher-selection")).toBeDefined();
  });

  it("renders ExpectedColumns when progressStage is Three, currentPaymentMode is BankTransfer and payment is confirmed", () => {
    mockUsePaymentModalContext.mockReturnValue({
      progressStage: ProgressStage.Three,
      currentPaymentMode: PaymentMode.BankTransfer,
      isBankTransferPaymentConfirmed: true,
    });

    render(<PaymentView />);
    const expectedColumns = screen.getByTestId("expected-columns");
    expect(expectedColumns).toBeDefined();
    expect(expectedColumns.textContent).toBe("No Download");
  });

  it("does not render ExpectedColumns when progressStage is Three, currentPaymentMode is BankTransfer but payment is not confirmed", () => {
    mockUsePaymentModalContext.mockReturnValue({
      progressStage: ProgressStage.Three,
      currentPaymentMode: PaymentMode.BankTransfer,
      isBankTransferPaymentConfirmed: false,
    });

    render(<PaymentView />);
    expect(screen.queryByTestId("expected-columns")).toBeNull();
  });
});
