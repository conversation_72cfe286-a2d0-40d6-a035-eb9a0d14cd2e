import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import ProgressButtons from "./ProgressButtons";
import { PaymentModalContext, PaymentMode, ProgressStage } from "./PaymentModalContextProvider";

const mockSetIsShowPaymentConfirmationModal = vi.fn();
const mockSetProgressStage = vi.fn();
const mockHandleCreatePayment = vi.fn();

const mockContextValue = {
  setIsShowPaymentConfirmationModal: mockSetIsShowPaymentConfirmationModal,
  progressStage: ProgressStage.One,
  currentAccount: "test-account",
  currentPaymentMode: PaymentMode.BankTransfer,
  chequeNumber: "12345",
  chequeDate: "2025-02-20",
  setProgressStage: mockSetProgressStage,
  selectedVouchers: [], // Ensure this is defined
  handleCreatePayment: mockHandleCreatePayment,
};

describe("ProgressButtons", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders the Next button and it is disabled when required fields are missing", () => {
    render(
      <PaymentModalContext.Provider
        value={{ ...mockContextValue, currentAccount: "", currentPaymentMode: "" }}
      >
        <ProgressButtons />
      </PaymentModalContext.Provider>,
    );

    const nextButton = screen.getByText("Next");
    expect(nextButton).toBeDisabled();
  });

  it("calls setProgressStage when Next button is clicked", () => {
    render(
      <PaymentModalContext.Provider value={mockContextValue}>
        <ProgressButtons />
      </PaymentModalContext.Provider>,
    );

    const nextButton = screen.getByText("Next");
    fireEvent.click(nextButton);
    expect(mockSetProgressStage).toHaveBeenCalled();
  });

  it("shows and enables the Back button after stage 1", () => {
    render(
      <PaymentModalContext.Provider
        value={{ ...mockContextValue, progressStage: ProgressStage.Two }}
      >
        <ProgressButtons />
      </PaymentModalContext.Provider>,
    );

    const backButton = screen.getByText("Back");
    expect(backButton).toBeInTheDocument();
    expect(backButton).toBeEnabled();
  });

  it("calls handleCreatePayment when Done button is clicked at stage 3 with BankTransfer", () => {
    render(
      <PaymentModalContext.Provider
        value={{ ...mockContextValue, progressStage: ProgressStage.Three }}
      >
        <ProgressButtons />
      </PaymentModalContext.Provider>,
    );

    const doneButton = screen.getByText("Done");
    fireEvent.click(doneButton);
    expect(mockHandleCreatePayment).toHaveBeenCalled();
  });
});
