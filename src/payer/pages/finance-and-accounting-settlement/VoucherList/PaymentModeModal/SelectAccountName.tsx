import { useGetProvidersAccountsQuery } from "../../../../api/settlement/settlementApi";
import useVoucherListContext from "../useVoucherListContext";
import usePaymentModalContext from "./usePaymentModalContext";

export default function SelectAccountName() {
  const { userInfo } = useVoucherListContext();
  const { currentAccount, setCurrentAccount, currentPaymentMode } = usePaymentModalContext();
  const { data: accountsData } = useGetProvidersAccountsQuery({
    parameters: { payerId: Number(userInfo?.payerId) },
  });

  const accounts = accountsData?.data;
  return (
    <div>
      <p className="text-blue mt-8 text-xl font-semibold">
        <span>Select Account Name</span> <span className="text-red-600">*</span>
      </p>

      <div className="mt-6 text-xs">
        <label htmlFor="account" className=" flex items-center gap-1">
          <span>Account</span> <span className="text-red-600">*</span>
        </label>

        <select
          id="account"
          value={currentAccount}
          onChange={(e) => setCurrentAccount(e.target.value)}
          className="mt-2 w-[50%] rounded-md border border-slate-300 text-xs"
          disabled={!currentPaymentMode}
          defaultValue={``}
        >
          <option disabled className=" bg-slate-100 text-sm   text-slate-500" value={``}>
            Select the account
          </option>
          {accounts?.map((account) => (
            <option key={account.id} className=" text-sm" value={account.id}>
              {account.accountName}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
}
