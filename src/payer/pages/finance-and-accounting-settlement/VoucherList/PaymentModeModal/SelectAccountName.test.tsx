import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import SelectAccountName from "./SelectAccountName";

import { useGetProvidersAccountsQuery } from "../../../../api/settlement/settlementApi";
import { VoucherListContext } from "../VoucherListContextProvider";
import { PaymentModalContext } from "./PaymentModalContextProvider";

vi.mock("../../../../api/settlement/settlementApi", async () => ({
  ...(await vi.importActual("../../../../api/settlement/settlementApi")),
  useGetProvidersAccountsQuery: vi.fn(),
}));

describe("SelectAccountName", () => {
  const mockSetCurrentAccount = vi.fn();
  const mockContextValue = {
    currentAccount: "",
    setCurrentAccount: mockSetCurrentAccount,
    currentPaymentMode: "someMode",
  };
  const mockUserInfo = { payerId: 123 };
  const mockAccountsData = {
    data: [
      { id: "1", accountName: "Account 1" },
      { id: "2", accountName: "Account 2" },
    ],
  };

  beforeEach(() => {
    vi.clearAllMocks();
    useGetProvidersAccountsQuery.mockReturnValue({ data: mockAccountsData });
  });

  it("renders the component correctly", () => {
    render(
      <VoucherListContext.Provider value={{ userInfo: mockUserInfo }}>
        <PaymentModalContext.Provider value={mockContextValue}>
          <SelectAccountName />
        </PaymentModalContext.Provider>
      </VoucherListContext.Provider>,
    );

    expect(screen.getByText("Select Account Name")).toBeInTheDocument();
    expect(screen.getByText("Select the account")).toBeInTheDocument();
    expect(screen.getByText("Account 1")).toBeInTheDocument();
    expect(screen.getByText("Account 2")).toBeInTheDocument();
  });

  it("calls setCurrentAccount when selecting an account", () => {
    render(
      <VoucherListContext.Provider value={{ userInfo: mockUserInfo }}>
        <PaymentModalContext.Provider value={mockContextValue}>
          <SelectAccountName />
        </PaymentModalContext.Provider>
      </VoucherListContext.Provider>,
    );

    const select = screen.getByLabelText(/Account/i);
    fireEvent.change(select, { target: { value: "1" } });
    expect(mockSetCurrentAccount).toHaveBeenCalledWith("1");
  });

  it("disables the select dropdown when no payment mode is selected", () => {
    render(
      <VoucherListContext.Provider value={{ userInfo: mockUserInfo }}>
        <PaymentModalContext.Provider value={{ ...mockContextValue, currentPaymentMode: null }}>
          <SelectAccountName />
        </PaymentModalContext.Provider>
      </VoucherListContext.Provider>,
    );

    expect(screen.getByLabelText(/Account/i)).toBeDisabled();
  });
});
