// PaymentModalContextProvider.test.tsx
import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { vi } from "vitest";
import PaymentModalContextProvider, {
  PaymentMode,
  PaymentModalContext,
} from "./PaymentModalContextProvider";

// --- <PERSON>cks ---

// Mock the voucher list context hook to track the modal-closing behavior.
const mockSetIsShowPaymentModal = vi.fn();
vi.mock("../useVoucherListContext", () => ({
  default: () => ({
    userInfo: { payerId: "123", preferred_username: "tester" },
    setIsShowPaymentModal: mockSetIsShowPaymentModal,
  }),
}));

// Mock the API hook so that handleCreatePayment resolves successfully.
vi.mock("../../../../api/settlement/settlementApi", () => ({
  useCreatePaymentMutation: () => [
    vi.fn(() => Promise.resolve({ data: "success" })),
    { isLoading: false },
  ],
}));

// Mock the SuccessModal component so we can verify its appearance.
vi.mock("../../../../components/ui/modal/SuccessModal", () => ({
  default: ({
    title,
    description,
    onClose,
    isSuccessModalOpen,
  }: {
    title: string;
    description: string;
    onClose: () => void;
    isSuccessModalOpen: boolean;
  }) => {
    return isSuccessModalOpen ? (
      <div data-testid="success-modal">
        <h1>{title}</h1>
        <p>{description}</p>
        <button onClick={onClose}>Close</button>
      </div>
    ) : null;
  },
}));

// Mock the LoadingAnimation component so we can detect it.
vi.mock("../../../../components/animations/LoadingAnimation/LoadingAnimation", () => ({
  default: ({ size }: { size: number }) => (
    <div data-testid="loading-animation">Loading {size}</div>
  ),
}));

// --- Test Component ---
// This component uses the PaymentModalContext to set up state values and trigger handleCreatePayment.
function TestComponent() {
  const {
    handleCreatePayment,
    setCurrentPaymentMode,
    setChequeNumber,
    setChequeDate,
    setSelectedVouchers,
    currentPaymentMode,
  } = React.useContext(PaymentModalContext)!;

  // This function sets the necessary state for the payment.
  const handleSetup = () => {
    setCurrentPaymentMode(PaymentMode.Cheque);
    setChequeNumber("12345");
    setChequeDate("2022-01-01T00:00:00");
    setSelectedVouchers([{ id: 1 }]);
  };

  return (
    <div>
      <button onClick={handleSetup}>Setup Payment</button>
      {currentPaymentMode && <span data-testid="payment-mode">{currentPaymentMode}</span>}
      <button onClick={handleCreatePayment}>Create Payment</button>
    </div>
  );
}

describe("PaymentModalContextProvider", () => {
  beforeEach(() => {
    mockSetIsShowPaymentModal.mockClear();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it("displays the loading animation when payment is loading", async () => {
    // Override the API hook mock to simulate a loading state.
    const settlementApi = await import("../../../../api/settlement/settlementApi");
    settlementApi.useCreatePaymentMutation = () => [
      vi.fn(() => Promise.resolve({ data: "success" })),
      { isLoading: true },
    ];

    render(
      <PaymentModalContextProvider>
        <div>Test Child</div>
      </PaymentModalContextProvider>,
    );

    // Assert that the loading animation is rendered.
    expect(screen.getByTestId("loading-animation")).toBeInTheDocument();
  });
});
