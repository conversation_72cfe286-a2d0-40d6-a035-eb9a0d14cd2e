import { render, screen } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import ProgressIndicator from "./ProgressIndicator";
import { PaymentModalContext } from "./PaymentModalContextProvider";
import { PaymentMode, ProgressStage } from "./PaymentModalContextProvider";

vi.mock("./ProgressInfo", () => ({
  default: ({ number, title, description }) => (
    <div data-testid={`progress-info-${number}`}>
      <p>{title}</p>
      <p>{description}</p>
    </div>
  ),
}));

describe("ProgressIndicator", () => {
  const renderComponent = (progressStage, currentPaymentMode) => {
    return render(
      <PaymentModalContext.Provider value={{ progressStage, currentPaymentMode }}>
        <ProgressIndicator />
      </PaymentModalContext.Provider>,
    );
  };

  it("renders all progress steps correctly", () => {
    renderComponent(ProgressStage.One, PaymentMode.BankTransfer);

    expect(screen.getByText("Payment Mode")).toBeInTheDocument();
    expect(screen.getByText("Voucher Selections")).toBeInTheDocument();
    expect(screen.getByText("Payment Details")).toBeInTheDocument();
  });

  it("displays correct titles and descriptions based on payment mode", () => {
    renderComponent(ProgressStage.Two, PaymentMode.Cheque);

    expect(screen.getByText("Payment Mode")).toBeInTheDocument();
    expect(screen.getByText("Payment Details")).toBeInTheDocument();
    expect(screen.getByText("Voucher Selections")).toBeInTheDocument();
  });

  it("updates progress lines based on progressStage", () => {
    renderComponent(ProgressStage.Three, PaymentMode.BankTransfer);

    const lines = screen.getAllByRole("separator");
    expect(lines.length).toBe(2);
  });
});
