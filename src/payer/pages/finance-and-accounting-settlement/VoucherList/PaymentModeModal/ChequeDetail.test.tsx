// ChequeDetails.test.tsx
import React from "react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import ChequeDetails from "./ChequeDetails";

// Define mock functions and initial values.
const mockSetChequeNumber = vi.fn();
const mockSetChequeDate = vi.fn();
const initialChequeNumber = "123";
const initialChequeDate = "2023-01-01";

// Mock the usePaymentModalContext hook.
vi.mock("./usePaymentModalContext", () => ({
  default: () => ({
    chequeNumber: initialChequeNumber,
    setChequeNumber: mockSetChequeNumber,
    chequeDate: initialChequeDate,
    setChequeDate: mockSetChequeDate,
  }),
}));

// Mock the DateInput component.
vi.mock("../../../../components/ui/input/DateInput", () => ({
  default: ({
    placeholder,
    value,
    onChange,
  }: {
    placeholder: string;
    value: string;
    onChange: (value: string) => void;
  }) => (
    <input
      data-testid="date-input"
      placeholder={placeholder}
      value={value}
      onChange={(e) => onChange(e.target.value)}
    />
  ),
}));

describe("ChequeDetails", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders cheque details form correctly", () => {
    render(<ChequeDetails />);

    // Check for heading text and required indicator.
    expect(screen.getByText("Enter cheque details")).toBeInTheDocument();
    expect(screen.getAllByText("*")[0]).toBeInTheDocument();

    // Verify cheque number input.
    const chequeNumberInput = screen.getByPlaceholderText("Enter cheque number");
    expect(chequeNumberInput).toBeInTheDocument();
    expect(chequeNumberInput).toHaveValue(initialChequeNumber);

    // Verify DateInput.
    const dateInput = screen.getByPlaceholderText("Select the date");
    expect(dateInput).toBeInTheDocument();
    expect(dateInput).toHaveValue(initialChequeDate);
  });

  it("calls setChequeNumber on cheque number input change", () => {
    render(<ChequeDetails />);

    const chequeNumberInput = screen.getByPlaceholderText("Enter cheque number");
    fireEvent.change(chequeNumberInput, { target: { value: "456" } });
    expect(mockSetChequeNumber).toHaveBeenCalledWith("456");
  });

  it("calls setChequeDate on date input change", () => {
    render(<ChequeDetails />);

    const dateInput = screen.getByPlaceholderText("Select the date");
    fireEvent.change(dateInput, { target: { value: "2023-12-31" } });
    expect(mockSetChequeDate).toHaveBeenCalledWith("2023-12-31");
  });
});
