import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import HeaderSection from "./HeaderSection";

// --- Mock dependent components ---

vi.mock("../../../components/icons/FilterLinesIcon", () => ({
  default: () => <svg data-testid="filter-lines-icon" />,
}));

vi.mock("../../../components/ui/Button", () => ({
  default: (props: any) => <button {...props}>{props.children}</button>,
}));

vi.mock("../../../components/ui/modal/DialogWrapper", () => ({
  default: (props: any) => (
    <div data-testid="dialog-wrapper">
      {props.show && props.children}
      <button data-testid="close-dialog" onClick={props.onClose}>
        Close
      </button>
    </div>
  ),
}));

vi.mock("../../../components/ui/typography/Text", () => ({
  default: (props: any) => (
    <div data-testid="text" {...props}>
      {props.children}
    </div>
  ),
}));

vi.mock("./PaymentModeModal/PaymentModeModal", () => ({
  default: (props: any) => <div data-testid="payment-mode-modal">Payment Mode Modal</div>,
}));

vi.mock("./VoucherListFilters/VoucherListFilters", () => ({
  default: () => <div data-testid="voucher-list-filters">Voucher List Filters</div>,
}));

// --- Mock the custom hook ---
const mockSetIsShowPaymentModal = vi.fn();
const mockSetIsShowFilters = vi.fn();
let mockIsShowFilters = false;
let mockIsShowPaymentModal = false;

vi.mock("./useVoucherListContext", () => ({
  default: () => ({
    setIsShowPaymentModal: mockSetIsShowPaymentModal,
    setIsShowFilters: mockSetIsShowFilters,
    isShowFilters: mockIsShowFilters,
    isShowPaymentModal: mockIsShowPaymentModal,
  }),
}));

describe("HeaderSection", () => {
  beforeEach(() => {
    // Reset mocks and default values before each test
    vi.clearAllMocks();
    mockIsShowFilters = false;
    mockIsShowPaymentModal = false;
  });

  it("renders header elements correctly", () => {
    render(<HeaderSection />);

    // Verify header title
    expect(screen.getByTestId("text")).toHaveTextContent("Voucher List");

    // Verify both buttons exist
    expect(screen.getByRole("button", { name: /Select Payment mode/i })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /Filters/i })).toBeInTheDocument();

    // Filters and modal content should not be rendered initially
    expect(screen.queryByTestId("voucher-list-filters")).toBeNull();
    expect(screen.queryByTestId("payment-mode-modal")).toBeNull();
  });

  it("calls setIsShowPaymentModal(true) when 'Select Payment mode' button is clicked", () => {
    render(<HeaderSection />);
    const paymentModeButton = screen.getByRole("button", {
      name: /Select Payment mode/i,
    });
    fireEvent.click(paymentModeButton);
    expect(mockSetIsShowPaymentModal).toHaveBeenCalledWith(true);
  });

  it("calls setIsShowFilters with toggled value when 'Filters' button is clicked", () => {
    render(<HeaderSection />);
    const filtersButton = screen.getByRole("button", { name: /Filters/i });
    fireEvent.click(filtersButton);
    // Since mockIsShowFilters is false, clicking should call setIsShowFilters(true)
    expect(mockSetIsShowFilters).toHaveBeenCalledWith(true);
  });

  it("renders VoucherListFilters when isShowFilters is true", () => {
    // Update the mock value for isShowFilters
    mockIsShowFilters = true;
    render(<HeaderSection />);
    expect(screen.getByTestId("voucher-list-filters")).toBeInTheDocument();
  });

  it("renders PaymentModeModal when isShowPaymentModal is true", () => {
    // Update the mock value for isShowPaymentModal
    mockIsShowPaymentModal = true;
    render(<HeaderSection />);
    expect(screen.getByTestId("payment-mode-modal")).toBeInTheDocument();
    // Also check that the DialogWrapper is rendered
    expect(screen.getByTestId("dialog-wrapper")).toBeInTheDocument();
  });

  it("calls setIsShowPaymentModal(false) when the dialog close button is clicked", () => {
    mockIsShowPaymentModal = true;
    render(<HeaderSection />);
    const closeButton = screen.getByTestId("close-dialog");
    fireEvent.click(closeButton);
    expect(mockSetIsShowPaymentModal).toHaveBeenCalledWith(false);
  });
});
