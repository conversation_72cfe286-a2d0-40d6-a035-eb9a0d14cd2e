import { TailSpin } from "react-loader-spinner";
import NoClaimsVouchering from "../../../../components/illustrations/NoClaimsVouchering";
import EmptyState from "../../../../components/ui/EmptyState";
import PrimaryPagination from "../../../../components/ui/pagination/PrimaryPagination";
import VoucherCard from "../UnpaidVouchers/VoucherCard";
import useVoucherListContext from "../useVoucherListContext";
import LoadingAnimation from "../../../../components/animations/LoadingAnimation/LoadingAnimation";

export default function UnpaidVouchers() {
  const {
    vouchers,
    isPendingPaymentVouchers,
    page,
    setPage,
    totalPages,
    setPageSize,
    pageSize,
    totalElements,
  } = useVoucherListContext();

  return (
    <div className="mt-4 flex flex-col gap-4">
      {isPendingPaymentVouchers ? (
        <div className="flex h-[60vh] items-center justify-center">
          <LoadingAnimation size={50} />
        </div>
      ) : vouchers && vouchers.length > 0 ? (
        <>
          {vouchers?.map((voucher) => <VoucherCard key={voucher.id} voucher={voucher} />)}

          <PrimaryPagination
            onPageNumberClick={(page) => setPage(page)}
            onSizeChange={(size) => setPageSize(size)}
            pageNumber={page}
            pageSize={pageSize}
            totalElements={totalElements as number}
            totalPages={totalPages as number}
          />
        </>
      ) : (
        <EmptyState
          illustration={<NoClaimsVouchering />}
          message={{
            title: "No vouchers available",
            description:
              "You haven't created any vouchers yet. Once the vouchers have been created, they'll be displayed here for your review and further actions...",
          }}
        />
      )}
    </div>
  );
}
