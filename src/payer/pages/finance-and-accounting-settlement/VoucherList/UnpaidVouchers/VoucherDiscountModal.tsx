import { XMarkIcon } from "@heroicons/react/24/outline";
import currency from "currency.js";
import React, { useRef, useState } from "react";
import { toast } from "react-toastify";
import { useApplyVoucherDiscountMutation } from "../../../../api/settlement/settlementApi";
import { DiscountType, Voucher } from "../../settlementTypes";
import useVoucherListContext from "../../VoucherList/useVoucherListContext";

type Props = {
  setIsShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  voucher: Voucher;
};

export default function VoucherDiscountModal({ setIsShowModal, voucher }: Props) {
  const fixedAmountRef = useRef<HTMLInputElement>(null);
  const percentageRef = useRef<HTMLInputElement>(null);
  const { userInfo } = useVoucherListContext();

  const [discountType, setDiscountType] = useState<DiscountType | null>(null);
  const [discount, setDiscount] = useState("");

  const [applyDiscount, { isLoading }] = useApplyVoucherDiscountMutation();

  function handleCheckboxChange(selectedType: DiscountType) {
    const isCurrentlyChecked = discountType === selectedType;
    const newDiscountType = isCurrentlyChecked ? null : selectedType;

    setDiscount("");
    setDiscountType(newDiscountType);

    if (fixedAmountRef.current) {
      fixedAmountRef.current.checked = newDiscountType === DiscountType.fixed;
    }
    if (percentageRef.current) {
      percentageRef.current.checked = newDiscountType === DiscountType.percentage;
    }
  }

  function formatNumberToKes(value: number) {
    return currency(value, { symbol: "KES " }).format();
  }

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();

    try {
      if (!discountType) {
        toast.warning("Please select a discount type!");
        return;
      }

      const result = await applyDiscount({
        parameters: {
          voucherId: voucher.id,
        },
        body: {
          discountType: discountType as DiscountType,
          discount: Number(discount),
          actionByUser: userInfo?.preferred_username as string,
        },
      });

      if ("error" in result) throw new Error("Error occurred while applying discount!");

      toast.success("Discount applied successfully!");
      setIsShowModal(false);
    } catch (error) {
      if (error instanceof Error) {
        toast.error(error.message);
      }
    }
  }

  return (
    <form
      className="relative border border-slate-100 bg-white px-24 py-8 shadow-md"
      onSubmit={(e) => handleSubmit(e)}
    >
      <p className="text-blue text-center text-2xl font-semibold">Voucher Discount</p>

      <button
        className="absolute right-4 top-4"
        onClick={() => setIsShowModal(false)}
        type="button"
      >
        <XMarkIcon className="w-5" strokeWidth={2} />
      </button>

      <div className="mt-8 flex flex-col gap-4 text-base">
        <p>
          <span>Account Name :</span>{" "}
          <span className="text-black">{voucher.providerAccount?.accountName || "NA"}</span>
        </p>
        <p>
          <span>Voucher Number :</span> <span className="text-black">{voucher.voucherNo}</span>
        </p>
        <p>
          <span>Voucher Amount :</span>{" "}
          <span className="text-black">{formatNumberToKes(voucher.amount) || "NA"}</span>
        </p>
      </div>

      {/* Discount type */}
      <div className="mt-8">
        <p className="text-blue text-2xl font-semibold">Discount Type</p>
        <div className="mt-6 flex flex-col gap-4 text-base text-black">
          <div className="flex items-center gap-3">
            <input
              className="rounded"
              type="checkbox"
              ref={fixedAmountRef}
              value={DiscountType.fixed}
              onChange={() => handleCheckboxChange(DiscountType.fixed)}
            />

            <label htmlFor="">Fixed Amount</label>
          </div>

          <div className="flex items-center gap-3">
            <input
              className="rounded"
              type="checkbox"
              ref={percentageRef}
              value={DiscountType.percentage}
              onChange={() => handleCheckboxChange(DiscountType.percentage)}
            />
            <label htmlFor="">Percentage</label>
          </div>
        </div>

        {/* Fixed / percentage input */}
        {discountType === DiscountType.fixed && (
          <div className=" mt-8 flex flex-col  gap-2 text-sm">
            <label htmlFor="">Fixed amount</label>
            <input
              className="max-w-[50%] rounded border-slate-400 text-sm"
              type="number"
              placeholder="KES"
              min={1}
              max={voucher.amount}
              required
              value={discount}
              onChange={(e) => setDiscount(e.target.value)}
              data-nonscientific-number-input
              data-unsigned-number-input
            />
          </div>
        )}

        {discountType === DiscountType.percentage && (
          <div className=" mt-8 flex flex-col gap-2 text-sm">
            <label htmlFor="">Percentage</label>
            <input
              className="max-w-[50%] rounded border-slate-400 text-sm"
              type="number"
              min={0.01}
              max={100}
              required
              step={0.01}
              placeholder="Enter the discount percentage"
              value={discount}
              onChange={(e) => setDiscount(e.target.value)}
              data-nonscientific-number-input
              data-unsigned-number-input
            />
          </div>
        )}

        {/* Buttons */}
        <div className="mt-16 flex items-center justify-end gap-8">
          <button
            className="w-[120px] rounded-md border border-slate-400 px-3 py-2 text-sm font-medium"
            type="button"
            onClick={() => setIsShowModal(false)}
          >
            Cancel
          </button>
          <button
            className={`w-[120px] rounded-md bg-blue-600 px-3 py-2 text-sm font-medium text-white  ${
              isLoading && "opacity-60"
            }`}
            disabled={isLoading}
          >
            {isLoading ? "Loading..." : "Apply"}
          </button>
        </div>
      </div>
    </form>
  );
}
