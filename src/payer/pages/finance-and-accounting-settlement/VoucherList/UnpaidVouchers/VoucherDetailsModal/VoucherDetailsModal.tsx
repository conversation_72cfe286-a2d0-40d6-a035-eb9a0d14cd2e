import { XMarkIcon } from "@heroicons/react/24/outline";
import currency from "currency.js";
import React from "react";
import { DiscountType, Voucher } from "../../../settlementTypes";
import InvoiceDetailsTable from "./InvoiceDetailsTable";

type Props = {
  setIsShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  voucher: Voucher;
};

export default function VoucherDiscountModal({ setIsShowModal, voucher }: Props) {
  function formatNumberToKes(value: number) {
    return currency(value, { symbol: "KES " }).format();
  }

  return (
    <div className="relative  border border-slate-100 bg-white px-8 py-8 shadow-md">
      <button
        className="absolute right-4 top-4"
        type="button"
        onClick={() => setIsShowModal(false)}
      >
        <XMarkIcon className="w-5" strokeWidth={2} />
      </button>

      <p className="text-xl font-semibold ">More voucher details</p>

      <div className="mt-6 flex gap-32">
        <div className="flex flex-col gap-2">
          <p className="text-xs ">Voucher Amount</p>
          <p className="text-sm font-medium">{formatNumberToKes(voucher.amount) || "NA"}</p>
        </div>
        <div className="flex flex-col gap-2">
          <p className="text-xs ">
            Discount {voucher.discountType === DiscountType.percentage && "(%)"}
          </p>
          <p className="text-sm font-medium">
            {voucher.discount}
            {voucher.discountType === DiscountType.percentage && "%"}
          </p>
        </div>
      </div>

      <InvoiceDetailsTable voucherId={voucher.id} />
    </div>
  );
}
