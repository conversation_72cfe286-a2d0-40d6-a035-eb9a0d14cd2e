import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi, afterEach } from "vitest";
import InvoiceDetailsTable from "./InvoiceDetailsTable";
import * as settlementApi from "../../../../../api/settlement/settlementApi";

// --- Mocks ---

// Mock the react-loader-spinner to render a simple spinner element.
vi.mock("react-loader-spinner", () => ({
  TailSpin: () => <div data-testid="spinner">Spinner</div>,
}));

// Mock the SecondaryPagination component.
vi.mock("../../../../../components/ui/pagination/SecondaryPagination", () => ({
  default: ({ currentPage, size, totalElements, setCurrentPage, totalPages }) => (
    <div data-testid="secondary-pagination">
      Pagination Component (Page: {currentPage})
      <button onClick={() => setCurrentPage(currentPage + 1)}>Next</button>
    </div>
  ),
}));

// Mock the TableHeaderItem component.
vi.mock("../../../../../components/ui/table/TableHeaderItem", () => ({
  default: ({ item }) => <th>{item}</th>,
}));

// Mock the InvoiceDetailsRow component.
vi.mock("./InvoiceDetailsRow", () => ({
  default: ({ invoice }) => (
    <tr data-testid="invoice-row">
      <td>{invoice.id}</td>
    </tr>
  ),
}));

// --- Tests ---

describe("InvoiceDetailsTable", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("displays the loader spinner when data is loading", () => {
    // Simulate the query hook being in a loading state.
    vi.spyOn(settlementApi, "useGetVoucherInvoicesQuery").mockReturnValue({
      data: null,
      isLoading: true,
      isFetching: false,
    });

    render(<InvoiceDetailsTable voucherId={123} />);

    // Expect the loader spinner to be displayed.
    expect(screen.getByTestId("spinner")).toBeInTheDocument();

    // Since data is loading, the SecondaryPagination should not be rendered.
    expect(screen.queryByTestId("secondary-pagination")).not.toBeInTheDocument();
  });

  it("displays a table with invoices when data is available", () => {
    const fakeInvoices = [{ id: "inv1" }, { id: "inv2" }];

    // Simulate a successful query with invoice data.
    vi.spyOn(settlementApi, "useGetVoucherInvoicesQuery").mockReturnValue({
      data: { data: { content: fakeInvoices, totalElements: fakeInvoices.length, totalPages: 1 } },
      isLoading: false,
      isFetching: false,
    });

    render(<InvoiceDetailsTable voucherId={123} />);

    // The header should be visible.
    expect(screen.getByText("Invoice details")).toBeInTheDocument();

    // The table should be rendered.
    const table = screen.getByRole("table");
    expect(table).toBeInTheDocument();

    // Verify that table header items are rendered.
    expect(screen.getByText("INVOICE NUMBER")).toBeInTheDocument();
    expect(screen.getByText("ACCOUNT NAME")).toBeInTheDocument();
    expect(screen.getByText("INVOICE AMOUNT")).toBeInTheDocument();
    expect(screen.getByText("INVOICE DATE")).toBeInTheDocument();

    // Check that an invoice row is rendered for each invoice.
    const rows = screen.getAllByTestId("invoice-row");
    expect(rows).toHaveLength(fakeInvoices.length);

    // Verify that the pagination component is rendered.
    expect(screen.getByTestId("secondary-pagination")).toBeInTheDocument();
  });

  it("displays a message when no invoices are available", () => {
    // Simulate a query that returns an empty invoices array.
    vi.spyOn(settlementApi, "useGetVoucherInvoicesQuery").mockReturnValue({
      data: { data: { content: [], totalElements: 0, totalPages: 0 } },
      isLoading: false,
      isFetching: false,
    });

    render(<InvoiceDetailsTable voucherId={123} />);

    // Expect the no-invoices message to be displayed.
    expect(screen.getByText("This Voucher currently has no invoices")).toBeInTheDocument();

    // The table should not be rendered.
    expect(screen.queryByRole("table")).not.toBeInTheDocument();

    // Even if there are no invoices, the pagination is still rendered as long as data is not loading.
    expect(screen.getByTestId("secondary-pagination")).toBeInTheDocument();
  });
});
