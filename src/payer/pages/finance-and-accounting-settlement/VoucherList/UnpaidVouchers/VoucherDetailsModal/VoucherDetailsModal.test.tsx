import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import VoucherDetailsModal from "./VoucherDetailsModal";
import { DiscountType } from "../../../settlementTypes";

// Mock the InvoiceDetailsTable to avoid rendering its implementation details.
vi.mock("./InvoiceDetailsTable", () => ({
  default: () => <div data-testid="invoice-details-table">Invoice Details Table</div>,
}));

describe("VoucherDetailsModal", () => {
  const voucherPercentage = {
    id: "voucher1",
    amount: 1000,
    discount: 10,
    discountType: DiscountType.percentage,
  };

  const voucherFixed = {
    id: "voucher2",
    amount: 500,
    discount: 50,
    discountType: DiscountType.fixed,
  };

  it("renders voucher details with a percentage discount", () => {
    const setIsShowModal = vi.fn();
    render(<VoucherDetailsModal setIsShowModal={setIsShowModal} voucher={voucherPercentage} />);

    // Check that the modal title is displayed.
    expect(screen.getByText("More voucher details")).toBeInTheDocument();

    // Verify that the voucher amount is formatted correctly.
    expect(screen.getByText("KES 1,000.00")).toBeInTheDocument();

    // For a percentage discount, the component should append "%" to the discount value.
    expect(screen.getByText("10%")).toBeInTheDocument();

    // Verify that the InvoiceDetailsTable component is rendered (via the mock).
    expect(screen.getByTestId("invoice-details-table")).toBeInTheDocument();
  });

  it("renders voucher details without percentage for fixed discount type", () => {
    const setIsShowModal = vi.fn();
    render(<VoucherDetailsModal setIsShowModal={setIsShowModal} voucher={voucherFixed} />);

    // Check that the voucher amount is formatted correctly.
    expect(screen.getByText("KES 500.00")).toBeInTheDocument();

    // Since the discount type is fixed, it should render the discount value without a "%" symbol.
    expect(screen.getByText("50")).toBeInTheDocument();
  });

  it("calls setIsShowModal with false when the close button is clicked", () => {
    const setIsShowModal = vi.fn();
    render(<VoucherDetailsModal setIsShowModal={setIsShowModal} voucher={voucherPercentage} />);

    // Find the close button (there is only one button in this modal).
    const closeButton = screen.getByRole("button");
    fireEvent.click(closeButton);

    // The setIsShowModal function should be called with 'false' when the button is clicked.
    expect(setIsShowModal).toHaveBeenCalledWith(false);
  });
});
