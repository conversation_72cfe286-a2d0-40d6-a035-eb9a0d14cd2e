import TableDataItem from "../../../../../components/ui/table/TableDataItem";
import { convertDateString } from "../../../../../utils/convertDateString";
import { Invoice } from "../../../settlementTypes";

type Props = {
  invoice: Invoice;
};

export default function InvoiceDetailsRow({ invoice }: Props) {
  return (
    <tr className="border-b">
      <TableDataItem item={invoice.invoiceNumber} />
      <TableDataItem item={`The Mombasa Hospital`} />
      <TableDataItem item={invoice.totalAmount} />
      <TableDataItem item={convertDateString(invoice.invoiceDate)} />
    </tr>
  );
}
