import React from "react";
import { render, screen } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import InvoiceDetailsRow from "./InvoiceDetailsRow";

// Mock the convertDateString utility to return a predictable value.
vi.mock("../../../../../utils/convertDateString", () => ({
  convertDateString: (date) => `formatted-${date}`,
}));

// Mock the TableDataItem component so that it renders a simple <td> element.
vi.mock("../../../../../components/ui/table/TableDataItem", () => ({
  default: ({ item }) => <td>{item}</td>,
}));

describe("InvoiceDetailsRow", () => {
  it("renders a table row with invoice details", () => {
    const invoice = {
      invoiceNumber: "INV-123",
      totalAmount: "KES 1,000",
      invoiceDate: "2025-02-25",
    };

    // Wrap the component in a table and tbody since a <tr> needs to be within them.
    render(
      <table>
        <tbody>
          <InvoiceDetailsRow invoice={invoice} />
        </tbody>
      </table>,
    );

    // Assert that each expected text is rendered.
    expect(screen.getByText("INV-123")).toBeInTheDocument();
    expect(screen.getByText("The Mombasa Hospital")).toBeInTheDocument();
    expect(screen.getByText("KES 1,000")).toBeInTheDocument();
    expect(screen.getByText("formatted-2025-02-25")).toBeInTheDocument();
  });
});
