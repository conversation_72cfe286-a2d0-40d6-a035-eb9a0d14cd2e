import { useState } from "react";
import { TailSpin } from "react-loader-spinner";
import { useGetVoucherInvoicesQuery } from "../../../../../api/settlement/settlementApi";
import SecondaryPagination from "../../../../../components/ui/pagination/SecondaryPagination";
import InvoiceDetailsRow from "./InvoiceDetailsRow";
import TableHeaderItem from "../../../../../components/ui/table/TableHeaderItem";

type Props = {
  voucherId: number;
};

export default function InvoiceDetailsTable({ voucherId }: Props) {
  const [page, setPage] = useState(1);
  const size = 10;

  const { data, isLoading, isFetching } = useGetVoucherInvoicesQuery({
    searchParams: {
      voucherIds: [voucherId],
      page: page,
      size: size,
    },
  });

  const invoices = data?.data.content;
  const totalElements = data?.data.totalElements;
  const totalPages = data?.data.totalPages;

  const isPending = isLoading || isFetching;
  const isInvoicesAvailable = invoices && invoices?.length > 0;

  return (
    <div className="relative mt-12">
      <p className="text-xl font-semibold ">Invoice details</p>
      <div className="absolute right-0 top-0">
        {!isPending && (
          <SecondaryPagination
            currentPage={page}
            size={size}
            totalElements={Number(totalElements)}
            setCurrentPage={setPage}
            totalPages={Number(totalPages)}
          />
        )}
      </div>

      {isPending ? (
        <div className="my-8 flex justify-center">
          <TailSpin color="blue" />
        </div>
      ) : isInvoicesAvailable ? (
        <table className="mt-6 w-full text-center">
          <thead className="border-b">
            <tr>
              <TableHeaderItem item="INVOICE NUMBER" />
              <TableHeaderItem item="ACCOUNT NAME" />
              <TableHeaderItem item="INVOICE AMOUNT" />
              <TableHeaderItem item="INVOICE DATE" />
            </tr>
          </thead>

          <tbody>
            {invoices?.map((invoice) => <InvoiceDetailsRow key={invoice.id} invoice={invoice} />)}
          </tbody>
        </table>
      ) : (
        <p className=" pt-8 text-xl font-bold italic text-slate-500  ">
          This Voucher currently has no invoices
        </p>
      )}
    </div>
  );
}
