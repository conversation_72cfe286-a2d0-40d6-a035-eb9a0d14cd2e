// VoucherDiscountModal.test.tsx

import { vi } from "vitest";

// Define mocks inline at the top so they are hoisted properly.
const mockApplyDiscount = vi.fn();
vi.mock("../../../../api/settlement/settlementApi", () => ({
  useApplyVoucherDiscountMutation: () => [mockApplyDiscount, { isLoading: false }],
}));

vi.mock("react-toastify", () => ({
  toast: {
    warning: vi.fn(),
    success: vi.fn(),
    error: vi.fn(),
  },
}));

vi.mock("../../VoucherList/useVoucherListContext", () => ({
  default: () => ({
    userInfo: { preferred_username: "test-user" },
  }),
}));

// Now import modules that depend on the above mocks.
import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import VoucherDiscountModal from "./VoucherDiscountModal";
import { toast } from "react-toastify";

// --- Dummy Data --- //
const dummyVoucher = {
  id: "voucher1",
  voucherNo: "V123",
  amount: 1000,
  providerAccount: { accountName: "Test Account" },
};

describe("VoucherDiscountModal", () => {
  const setIsShowModal = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders voucher information correctly", () => {
    render(<VoucherDiscountModal setIsShowModal={setIsShowModal} voucher={dummyVoucher} />);
    expect(screen.getByText("Voucher Discount")).toBeInTheDocument();
    expect(screen.getByText("Test Account")).toBeInTheDocument();
    expect(screen.getByText("V123")).toBeInTheDocument();
    // Check that voucher amount is rendered (currency formatting should include "KES")
    expect(screen.getByText(/KES/)).toBeInTheDocument();
  });

  it("shows warning if discount type is not selected on submit", async () => {
    render(<VoucherDiscountModal setIsShowModal={setIsShowModal} voucher={dummyVoucher} />);
    const applyButton = screen.getByRole("button", { name: /Apply/i });
    fireEvent.click(applyButton);

    await waitFor(() => {
      expect(toast.warning).toHaveBeenCalledWith("Please select a discount type!");
    });
  });

  it("submits a fixed discount successfully", async () => {
    // Setup the API mock to resolve successfully.
    mockApplyDiscount.mockResolvedValueOnce({ data: {} });
    render(<VoucherDiscountModal setIsShowModal={setIsShowModal} voucher={dummyVoucher} />);

    // Instead of querying by accessible name, find the checkbox with value "Fixed"
    const checkboxes = screen.getAllByRole("checkbox");
    const fixedCheckbox = checkboxes.find((checkbox) => checkbox.getAttribute("value") === "Fixed");
    expect(fixedCheckbox).toBeDefined();

    fireEvent.click(fixedCheckbox!);

    // Enter a discount value.
    const discountInput = screen.getByPlaceholderText("KES");
    fireEvent.change(discountInput, { target: { value: "100" } });

    // Click the Apply button.
    const applyButton = screen.getByRole("button", { name: /Apply/i });
    fireEvent.click(applyButton);

    // Verify that the applyDiscount API is called with the correct parameters.
    await waitFor(() => {
      expect(mockApplyDiscount).toHaveBeenCalledWith({
        parameters: { voucherId: dummyVoucher.id },
        body: {
          discountType: "Fixed", // Updated expectation to match component's output
          discount: 100,
          actionByUser: "test-user",
        },
      });
    });

    await waitFor(() => {
      expect(toast.success).toHaveBeenCalledWith("Discount applied successfully!");
      expect(setIsShowModal).toHaveBeenCalledWith(false);
    });
  });

  it("handles error from applyDiscount call", async () => {
    // Setup the API mock to return an error.
    mockApplyDiscount.mockResolvedValueOnce({ error: {} });
    render(<VoucherDiscountModal setIsShowModal={setIsShowModal} voucher={dummyVoucher} />);

    // Find the percentage checkbox by value.
    const checkboxes = screen.getAllByRole("checkbox");
    const percentageCheckbox = checkboxes.find(
      (checkbox) => checkbox.getAttribute("value") === "Percentage",
    );
    expect(percentageCheckbox).toBeDefined();

    fireEvent.click(percentageCheckbox!);

    // Enter a discount value.
    const discountInput = screen.getByPlaceholderText("Enter the discount percentage");
    fireEvent.change(discountInput, { target: { value: "10" } });

    // Click the Apply button.
    const applyButton = screen.getByRole("button", { name: /Apply/i });
    fireEvent.click(applyButton);

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith("Error occurred while applying discount!");
    });
  });

  it("closes the modal when Cancel button is clicked", () => {
    render(<VoucherDiscountModal setIsShowModal={setIsShowModal} voucher={dummyVoucher} />);
    const cancelButton = screen.getByRole("button", { name: /Cancel/i });
    fireEvent.click(cancelButton);
    expect(setIsShowModal).toHaveBeenCalledWith(false);
  });

  it("closes the modal when the XMarkIcon button is clicked", () => {
    render(<VoucherDiscountModal setIsShowModal={setIsShowModal} voucher={dummyVoucher} />);
    // The first button in the DOM is the XMarkIcon button.
    const buttons = screen.getAllByRole("button");
    fireEvent.click(buttons[0]);
    expect(setIsShowModal).toHaveBeenCalledWith(false);
  });
});
