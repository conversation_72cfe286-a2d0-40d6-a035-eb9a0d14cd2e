import { Menu } from "@headlessui/react";
import { EyeIcon } from "@heroicons/react/24/outline";
import currency from "currency.js";
import { useState } from "react";
import DotIcon from "../../../../components/icons/DotIcon";
import ThreeDotsIcon from "../../../../components/icons/ThreeDotsIcon";
import CardWrapper from "../../../../components/ui/CardWrapper";
import DialogWrapper from "../../../../components/ui/modal/DialogWrapper";
import getTimeBetweenAsString from "../../../../utils/getTimeBetweenAsString";
import { Voucher } from "../../settlementTypes";
import VoucherDetailsModal from "./VoucherDetailsModal/VoucherDetailsModal";
import VoucherDiscountModal from "./VoucherDiscountModal";

type Props = {
  voucher: Voucher;
};

export default function VoucherCard({ voucher }: Props) {
  const [isShowDiscountModal, setIsShowDiscountModal] = useState(false);
  const [isShowDetailsModal, setIsShowDetailsModal] = useState(false);

  function formatNumberToKes(value: number) {
    return currency(value, { symbol: "KES " }).format();
  }

  const timeZoneTimeString = voucher.createdOn + "Z";

  return (
    <CardWrapper className="relative flex flex-col gap-2">
      {/* Voucher number */}
      <p className="text-base font-medium text-[#304254]">{voucher.voucherNo || "NA"}</p>

      <p className="flex items-center gap-2 text-sm">
        <span className="text-[#6B7280]">Account Name :</span>{" "}
        <span className="text-[#374151]">{voucher.providerAccount?.accountName || "NA"} </span>
      </p>

      {voucher.discount > 0 ? (
        <p className=" flex items-center gap-2 text-sm">
          <span className="text-[#6B7280]">Payable Amount :</span>{" "}
          <span className="text-[#374151]">{formatNumberToKes(voucher.payableAmount)}</span>
        </p>
      ) : (
        <p className="flex items-center gap-2 text-sm">
          <span className="text-[#6B7280]">Voucher Amount :</span>{" "}
          <span className="text-[#374151]">{formatNumberToKes(voucher.amount)}</span>
        </p>
      )}

      <p className="flex items-center gap-2 text-sm">
        <span className="text-[#6B7280]">Date Created :</span>
        <span className="flex items-center gap-2">
          <span className="text-[#374151]">
            {new Intl.DateTimeFormat("en-GB", { dateStyle: "long" }).format(
              new Date(voucher.createdOn),
            )}
          </span>
          <DotIcon />
          <span className="text-[#374151]">{getTimeBetweenAsString(timeZoneTimeString)}</span>
        </span>
      </p>

      <p className="flex items-center gap-2 text-sm">
        <span className="text-[#6B7280]">Created By :</span>{" "}
        <span className="text-[#374151]">{voucher.createdBy?.userName || "NA"}</span>
      </p>

      {!(voucher.discount > 0) && (
        <button
          className="absolute bottom-4 right-6 rounded-md  bg-blue-200 px-3 py-2 text-xs font-semibold text-blue-600"
          onClick={() => setIsShowDiscountModal(true)}
        >
          Apply Discount
        </button>
      )}

      <DialogWrapper
        maxWidth="max-w-[750px]"
        onClose={() => setIsShowDiscountModal(false)}
        show={isShowDiscountModal}
      >
        <VoucherDiscountModal voucher={voucher} setIsShowModal={setIsShowDiscountModal} />
      </DialogWrapper>

      {voucher.discount > 0 && (
        <Menu as={`div`} className={`absolute  right-6 top-4`}>
          <Menu.Button>
            <ThreeDotsIcon />
          </Menu.Button>

          <Menu.Items className={`absolute right-0 w-56 rounded-md border p-3 shadow-md`}>
            <Menu.Item>
              <button
                className="flex items-center gap-2 text-sm  font-medium "
                onClick={() => setIsShowDetailsModal(true)}
              >
                <EyeIcon className="w-5" />
                <span>View voucher details</span>
              </button>
            </Menu.Item>
          </Menu.Items>
        </Menu>
      )}

      <DialogWrapper
        maxWidth="max-w-[750px]"
        onClose={() => setIsShowDetailsModal(false)}
        show={isShowDetailsModal}
      >
        <VoucherDetailsModal voucher={voucher} setIsShowModal={setIsShowDetailsModal} />
      </DialogWrapper>
    </CardWrapper>
  );
}
