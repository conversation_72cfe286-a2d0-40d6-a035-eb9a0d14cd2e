// UnpaidVouchers.test.tsx
import React from "react";
import { render, screen } from "@testing-library/react";
import { vi } from "vitest";
import UnpaidVouchers from "./UnpaidVouchers";

// --- Mocks --- //

// Mock the spinner component from react-loader-spinner
vi.mock("react-loader-spinner", () => ({
  TailSpin: (props: any) => <div data-testid="tailspin-spinner" {...props} />,
}));

// Mock the SVG illustration
vi.mock("../../../../assets/svg/NoClaimsVouchering", () => ({
  __esModule: true,
  default: () => <div data-testid="no-claims-vouchering" />,
}));

// Mock EmptyState to display the title from its message prop
vi.mock("../../../../components/ui/EmptyState", () => ({
  __esModule: true,
  default: (props: any) => (
    <div data-testid="empty-state">{props.message && props.message.title}</div>
  ),
}));

// Mock PrimaryPagination
vi.mock("../../../../components/ui/pagination/PrimaryPagination", () => ({
  __esModule: true,
  default: (props: any) => <div data-testid="primary-pagination" />,
}));

// Mock VoucherCard to display voucher number
vi.mock("../UnpaidVouchers/VoucherCard", () => ({
  __esModule: true,
  default: (props: any) => <div data-testid="voucher-card">{props.voucher.voucherNo}</div>,
}));

// Mock the context hook.
// We export a mock function so we can override its return value per test.
const mockedUseVoucherListContext = vi.fn();
vi.mock("../useVoucherListContext", () => ({
  __esModule: true,
  default: () => mockedUseVoucherListContext(),
}));

// --- Tests --- //
describe("UnpaidVouchers", () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it("renders spinner when pending vouchers are loading", () => {
    // Simulate loading state
    mockedUseVoucherListContext.mockReturnValue({
      vouchers: [],
      isPendingPaymentVouchers: true,
      page: 1,
      setPage: vi.fn(),
      totalPages: 1,
      setPageSize: vi.fn(),
      pageSize: 10,
      totalElements: 0,
    });
    render(<UnpaidVouchers />);
    expect(screen.getByTestId("animated-loading-icon")).toBeInTheDocument();
  });

  it("renders voucher cards and pagination when vouchers exist", () => {
    // Provide a non-empty vouchers array.
    mockedUseVoucherListContext.mockReturnValue({
      vouchers: [
        {
          id: "1",
          voucherNo: "V001",
          discount: 0,
          amount: 1000,
          payableAmount: 1000,
          createdOn: "2023-02-01T12:00:00",
          providerAccount: { accountName: "Account 1" },
          createdBy: { userName: "User1" },
        },
        {
          id: "2",
          voucherNo: "V002",
          discount: 0,
          amount: 2000,
          payableAmount: 2000,
          createdOn: "2023-02-02T12:00:00",
          providerAccount: { accountName: "Account 2" },
          createdBy: { userName: "User2" },
        },
      ],
      isPendingPaymentVouchers: false,
      page: 2,
      setPage: vi.fn(),
      totalPages: 5,
      setPageSize: vi.fn(),
      pageSize: 20,
      totalElements: 40,
    });
    render(<UnpaidVouchers />);
    // Expect two voucher cards rendered.
    expect(screen.getAllByTestId("voucher-card")).toHaveLength(2);
    // PrimaryPagination should be rendered.
    expect(screen.getByTestId("primary-pagination")).toBeInTheDocument();
  });

  it("renders empty state when no vouchers exist", () => {
    // Simulate no vouchers available.
    mockedUseVoucherListContext.mockReturnValue({
      vouchers: [],
      isPendingPaymentVouchers: false,
      page: 1,
      setPage: vi.fn(),
      totalPages: 0,
      setPageSize: vi.fn(),
      pageSize: 10,
      totalElements: 0,
    });
    render(<UnpaidVouchers />);
    expect(screen.getByTestId("empty-state")).toBeInTheDocument();
    // Check that the empty state displays the expected message title.
    expect(screen.getByText("No vouchers available")).toBeInTheDocument();
  });
});
