// VoucherCard.test.tsx
import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { vi } from "vitest";
import VoucherCard from "./VoucherCard";

// Mock child modals so we can check that they are rendered
vi.mock("./VoucherDiscountModal", () => ({
  default: () => <div data-testid="voucher-discount-modal">Voucher Discount Modal</div>,
}));

vi.mock("./VoucherDetailsModal/VoucherDetailsModal", () => ({
  default: () => <div data-testid="voucher-details-modal">Voucher Details Modal</div>,
}));

// Dummy voucher data for when discount is NOT applied (discount = 0)
const dummyVoucherNoDiscount = {
  voucherNo: "V123",
  discount: 0,
  amount: 1000,
  payableAmount: 1000,
  createdOn: "2023-02-01T12:00:00", // example ISO string (without Z, but component appends "Z")
  providerAccount: { accountName: "Test Account" },
  createdBy: { userName: "testuser" },
};

// Dummy voucher data for when discount IS applied (discount > 0)
const dummyVoucherWithDiscount = {
  voucherNo: "V456",
  discount: 50,
  amount: 1000,
  payableAmount: 950,
  createdOn: "2023-02-02T12:00:00",
  providerAccount: { accountName: "Test Account 2" },
  createdBy: { userName: "anotheruser" },
};

describe("VoucherCard", () => {
  beforeAll(() => {
    global.ResizeObserver = class {
      observe = () => null;
      unobserve = () => null;
      disconnect = () => null;
    };
  });

  it("renders voucher information correctly when no discount is applied", () => {
    render(<VoucherCard voucher={dummyVoucherNoDiscount} />);
    // Check voucher number and account name
    expect(screen.getByText(dummyVoucherNoDiscount.voucherNo)).toBeInTheDocument();
    expect(screen.getByText("Test Account")).toBeInTheDocument();
    // Since discount is 0, it should show the voucher amount label.
    expect(screen.getByText(/Voucher Amount/i)).toBeInTheDocument();
    // "Apply Discount" button should be present
    expect(screen.getByRole("button", { name: /Apply Discount/i })).toBeInTheDocument();
  });

  it("opens the VoucherDiscountModal when 'Apply Discount' is clicked", async () => {
    render(<VoucherCard voucher={dummyVoucherNoDiscount} />);
    const applyDiscountButton = screen.getByRole("button", { name: /Apply Discount/i });
    fireEvent.click(applyDiscountButton);

    await waitFor(() => {
      expect(screen.getByTestId("voucher-discount-modal")).toBeInTheDocument();
    });
  });

  it("renders a menu with voucher details option when discount is applied", async () => {
    render(<VoucherCard voucher={dummyVoucherWithDiscount} />);
    // For discounted vouchers, the "Apply Discount" button should not be present.
    expect(screen.queryByRole("button", { name: /Apply Discount/i })).toBeNull();

    // The menu button rendered by Headless UI has no accessible name.
    const menuButton = screen.getByRole("button", { name: "" });
    fireEvent.click(menuButton);

    // After clicking the menu button, the menu items should appear.
    await waitFor(() => {
      // The voucher details option is rendered as a menuitem.
      expect(screen.getByRole("menuitem", { name: /View voucher details/i })).toBeInTheDocument();
    });

    // Click the "View voucher details" menu option.
    const viewDetailsMenuItem = screen.getByRole("menuitem", {
      name: /View voucher details/i,
    });
    fireEvent.click(viewDetailsMenuItem);

    // The VoucherDetailsModal should now be rendered.
    await waitFor(() => {
      expect(screen.getByTestId("voucher-details-modal")).toBeInTheDocument();
    });
  });
});
