import React from "react";
import { render, screen } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import VoucherListContextProvider, { VoucherListContext } from "./VoucherListContextProvider";
import { useGetPaymentVouchersQuery } from "../../../api/settlement/settlementApi";
import UserService from "../../../services/UserService";
import { toast } from "react-toastify";

// --- Mocks ---

// Mock the query hook from settlementApi
vi.mock("../../../api/settlement/settlementApi", async () => ({
  ...(await vi.importActual("../../../api/settlement/settlementApi")),
  useGetPaymentVouchersQuery: vi.fn(),
}));

// Mock toast.error from react-toastify
vi.mock("react-toastify", () => ({
  toast: {
    error: vi.fn(),
  },
}));

// --- Dummy consumer component ---
const DummyConsumer = () => {
  const context = React.useContext(VoucherListContext);
  if (!context) return null;
  return (
    <div data-testid="context-values">
      <div data-testid="vouchers">{JSON.stringify(context.vouchers)}</div>
      <div data-testid="userInfo">{JSON.stringify(context.userInfo)}</div>
      <div data-testid="selectedAccount">{context.selectedAccount}</div>
      <div data-testid="page">{context.page}</div>
    </div>
  );
};

describe("VoucherListContextProvider", () => {
  const dummyUserInfo = { payerId: 123, name: "Test User" };

  beforeEach(() => {
    // Reset mocks and set up a dummy user info on UserService
    vi.clearAllMocks();
    UserService.kcObject = { tokenParsed: dummyUserInfo };
  });

  it("provides the correct default context values when the query succeeds", () => {
    // Mock successful query response data
    const mockData = {
      data: {
        data: {
          content: [{ id: 1, voucherName: "Voucher 1" }],
          totalPages: 3,
          totalElements: 10,
        },
      },
      isError: false,
      isLoading: false,
      isFetching: false,
    };

    (useGetPaymentVouchersQuery as vi.Mock).mockReturnValue(mockData);

    render(
      <VoucherListContextProvider>
        <DummyConsumer />
      </VoucherListContextProvider>,
    );

    // Verify that vouchers from the query are available in context
    const vouchersEl = screen.getByTestId("vouchers");
    expect(vouchersEl.textContent).toContain("Voucher 1");

    // Verify that userInfo from UserService is available in context
    const userInfoEl = screen.getByTestId("userInfo");
    expect(userInfoEl.textContent).toContain("Test User");

    // Verify that initial state values (e.g. selectedAccount, page) are correct
    const selectedAccountEl = screen.getByTestId("selectedAccount");
    expect(selectedAccountEl.textContent).toBe("");
    const pageEl = screen.getByTestId("page");
    expect(pageEl.textContent).toBe("1");
  });

  it("shows an error toast when the query has an error", () => {
    // Mock query response with an error
    const errorData = {
      data: undefined,
      isError: true,
      isLoading: false,
      isFetching: false,
    };

    (useGetPaymentVouchersQuery as vi.Mock).mockReturnValue(errorData);

    render(
      <VoucherListContextProvider>
        <DummyConsumer />
      </VoucherListContextProvider>,
    );

    // Expect toast.error to have been called with the error message
    expect(toast.error).toHaveBeenCalledWith("An error occurred while getting vouchers!");
  });
});
