import React, { createContext, ReactNode, useState } from "react";
import { toast } from "react-toastify";
import { useGetPaymentVouchersQuery } from "../../../api/settlement/settlementApi";
import UserService from "../../../services/UserService";
import { UserInfo, Voucher, VoucherPaymentStatus } from "../settlementTypes";

type Props = {
  children: ReactNode;
};

type VoucherListContextType = {
  vouchers: Voucher[] | undefined;
  userInfo: UserInfo | undefined;
  selectedAccount: string;
  setSelectedAccount: React.Dispatch<React.SetStateAction<string>>;
  selectedRegion: string;
  setSelectedRegion: React.Dispatch<React.SetStateAction<string>>;
  startDate: string;
  setStartDate: React.Dispatch<React.SetStateAction<string>>;
  endDate: string;
  setEndDate: React.Dispatch<React.SetStateAction<string>>;
  isPendingPaymentVouchers: boolean;
  isShowPaymentModal: boolean;
  setIsShowPaymentModal: React.Dispatch<React.SetStateAction<boolean>>;
  isShowFilters: boolean;
  setIsShowFilters: React.Dispatch<React.SetStateAction<boolean>>;
  pageSize: number;
  page: number;
  setPage: React.Dispatch<React.SetStateAction<number>>;
  totalPages: number | undefined;
  setPageSize: React.Dispatch<React.SetStateAction<number>>;
  totalElements: number | undefined;
};

export const VoucherListContext = createContext<VoucherListContextType | null>(null);

export default function VoucherListContextProvider({ children }: Props) {
  const userInfo = UserService.kcObject.tokenParsed as UserInfo;
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const [selectedAccount, setSelectedAccount] = useState("");
  const [selectedRegion, setSelectedRegion] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [isShowPaymentModal, setIsShowPaymentModal] = useState(false);
  const [isShowFilters, setIsShowFilters] = useState(false);

  const {
    data: paymentVouchersData,
    isError: isPaymentVouchersError,
    isLoading: isLoadingPaymentVoucher,
    isFetching: isFetchingPaymentVoucher,
  } = useGetPaymentVouchersQuery({
    payerId: userInfo.payerId,
    searchParams: {
      voucherPaymentStatus: VoucherPaymentStatus.Unpaid,
      page: page,
      size: pageSize,
      ...(selectedRegion && { regionIds: [Number(selectedRegion)] }),
      ...(selectedAccount && { providerAccountIds: [Number(selectedAccount)] }),
      ...(startDate && { startDate: startDate.split("T")[0] }),
      ...(endDate && { endDate: endDate.split("T")[0] }),
    },
  });

  if (isPaymentVouchersError) {
    toast.error("An error occurred while getting vouchers!");
  }

  const vouchers = paymentVouchersData?.data.content;
  const totalPages = paymentVouchersData?.data.totalPages;
  const totalElements = paymentVouchersData?.data.totalElements;
  const isPendingPaymentVouchers = isLoadingPaymentVoucher || isFetchingPaymentVoucher;

  return (
    <VoucherListContext.Provider
      value={{
        vouchers,
        userInfo,
        endDate,
        selectedAccount,
        selectedRegion,
        setEndDate,
        setSelectedAccount,
        setSelectedRegion,
        setStartDate,
        startDate,
        isPendingPaymentVouchers,
        isShowPaymentModal,
        setIsShowPaymentModal,
        isShowFilters,
        setIsShowFilters,
        pageSize,
        page,
        setPage,
        totalPages,
        setPageSize,
        totalElements,
      }}
    >
      {children}
    </VoucherListContext.Provider>
  );
}
