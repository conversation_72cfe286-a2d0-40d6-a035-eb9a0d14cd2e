import FilterLinesIcon from "../../../components/icons/FilterLinesIcon";
import Button from "../../../components/ui/Button";
import DialogWrapper from "../../../components/ui/modal/DialogWrapper";
import Text from "../../../components/ui/typography/Text";
import PaymentModeModal from "./PaymentModeModal/PaymentModeModal";
import useVoucherListContext from "./useVoucherListContext";
import VoucherListFilters from "./VoucherListFilters/VoucherListFilters";

export default function HeaderSection() {
  const { setIsShowPaymentModal, setIsShowFilters, isShowFilters, isShowPaymentModal } =
    useVoucherListContext();

  return (
    <header className="mb-6">
      <div className="flex justify-between">
        <Text variant="heading" className="text-lg">
          Voucher List
        </Text>
        <div className="flex gap-12">
          <Button onClick={() => setIsShowPaymentModal(true)} variant="outlined">
            Select Payment mode
          </Button>

          <Button onClick={() => setIsShowFilters(!isShowFilters)}>
            <FilterLinesIcon />
            <span>Filters</span>
          </Button>
        </div>
      </div>

      {/* Filters */}
      {isShowFilters && <VoucherListFilters />}

      <DialogWrapper
        show={isShowPaymentModal}
        maxWidth="max-w-[850px]"
        onClose={() => setIsShowPaymentModal(false)}
      >
        <PaymentModeModal setIsShowModal={setIsShowPaymentModal} />
      </DialogWrapper>
    </header>
  );
}
