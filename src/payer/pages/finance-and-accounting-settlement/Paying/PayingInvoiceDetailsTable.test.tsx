import { render, screen } from "@testing-library/react";
import { vi } from "vitest";
import { Invoice } from "../settlementTypes";
import PayingInvoiceDetailsTable from "./PayingInvoiceDetailsTable";

// --- Mocks ---
// Mock PayingInvoiceDetailsRow to avoid rendering internals and just verify its calls.
vi.mock("./PayingInvoiceDetailsRow", () => ({
  default: ({ invoice }: { invoice: Invoice }) => (
    <tr data-testid="invoice-row">
      <td>{invoice.invoiceNumber}</td>
    </tr>
  ),
}));

describe("PayingInvoiceDetailsTable", () => {
  const dummyInvoices = [
    {
      id: 1,
      invoiceNumber: "INV-001",
      providerName: "Provider A",
      totalAmount: 1000,
      createdAt: "2023-01-01",
    },
    {
      id: 2,
      invoiceNumber: "INV-002",
      providerName: "Provider B",
      totalAmount: 2000,
      createdAt: "2023-02-01",
    },
  ] as Invoice[];

  test("renders table headers correctly", () => {
    render(<PayingInvoiceDetailsTable invoices={[]} />);

    expect(screen.getByText("INVOICE NUMBER")).toBeInTheDocument();
    expect(screen.getByText("ACCOUNT NAME")).toBeInTheDocument();
    expect(screen.getAllByText("INVOICE AMOUNT")).toHaveLength(2); // This header appears twice
  });

  test("renders correct number of invoice rows", () => {
    render(<PayingInvoiceDetailsTable invoices={dummyInvoices} />);

    const rows = screen.getAllByTestId("invoice-row");
    expect(rows).toHaveLength(dummyInvoices.length);
  });

  test("renders no rows when invoices are undefined", () => {
    render(<PayingInvoiceDetailsTable invoices={undefined} />);

    const rows = screen.queryAllByTestId("invoice-row");
    expect(rows).toHaveLength(0);
  });
});
