import TableHeaderItem from "../../../components/ui/table/TableHeaderItem";
import { Invoice } from "../settlementTypes";
import PayingInvoiceDetailsRow from "./PayingInvoiceDetailsRow";

type Props = {
  invoices: Invoice[] | undefined;
};

export default function PayingInvoiceDetailsTable({ invoices }: Props) {
  return (
    <table className="mt-12 w-full text-center">
      <thead>
        <tr className="border-b">
          <TableHeaderItem item="INVOICE NUMBER" />
          <TableHeaderItem item="ACCOUNT NAME" />
          <TableHeaderItem item="INVOICE AMOUNT" />
          <TableHeaderItem item="INVOICE AMOUNT" />
        </tr>
      </thead>

      <tbody>
        {invoices?.map((invoice) => <PayingInvoiceDetailsRow key={invoice.id} invoice={invoice} />)}
      </tbody>
    </table>
  );
}
