import TableHeaderItem from "../../../components/ui/table/TableHeaderItem";
import { Voucher } from "../settlementTypes";
import PayingVoucherDetailsRow from "./PayingVoucherDetailsRow";

type Props = {
  vouchers: Voucher[] | undefined;
};

export default function PayingVoucherDetailsTable({ vouchers }: Props) {
  return (
    <table className="mt-8 w-full text-center ">
      <thead>
        <tr className="border-b">
          <TableHeaderItem item="VOUCHER NUMBER" />
          <TableHeaderItem item="VOUCHER AMOUNT" />
          <TableHeaderItem item="DISCOUNT" />
          <TableHeaderItem item="PAYABLE AMOUNT" />
          <TableHeaderItem item="DATE CREATED" />
          <TableHeaderItem item="CREATED BY" />
          <TableHeaderItem item="ACTION" />
        </tr>
      </thead>

      <tbody>
        {vouchers?.map((voucher) => <PayingVoucherDetailsRow key={voucher.id} voucher={voucher} />)}
      </tbody>
    </table>
  );
}
