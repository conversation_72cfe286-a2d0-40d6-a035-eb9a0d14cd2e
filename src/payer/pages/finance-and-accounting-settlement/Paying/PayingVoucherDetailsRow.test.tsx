import { fireEvent, render, screen } from "@testing-library/react";
import { vi } from "vitest";
import { Voucher } from "../settlementTypes";
import PayingVoucherDetailsRow from "./PayingVoucherDetailsRow";

vi.mock("../../../utils/convertDateString", () => ({
  convertDateString: vi.fn(() => "01/01/2023"),
}));

vi.mock("../../../utils/formatCurrency", () => ({
  formatNumberToKes: vi.fn((num: number) => `KES ${num}`),
}));

vi.mock("./PayingInvoiceDetailsModal", () => ({
  default: ({ voucherId }: { setIsShowModal: unknown; voucherId: number }) => (
    <div data-testid="invoice-details-modal">Invoice Details Modal for voucherId: {voucherId}</div>
  ),
}));

describe("PayingVoucherDetailsRow", () => {
  beforeAll(() => {
    global.ResizeObserver = class {
      observe = () => null;
      unobserve = () => null;
      disconnect = () => null;
    };
  });

  const dummyVoucher = {
    id: 1,
    voucherNo: "VCHR001",
    amount: 1000,
    discount: 100,
    payableAmount: 900,
    createdOn: "2023-01-01T00:00:00Z",
    createdBy: { userName: "John Doe" },
  } as Voucher;

  test("renders voucher details in table row", () => {
    render(
      <table>
        <tbody>
          <PayingVoucherDetailsRow voucher={dummyVoucher} />
        </tbody>
      </table>,
    );

    expect(screen.getByText("VCHR001")).toBeInTheDocument();
    expect(screen.getByText("KES 1000")).toBeInTheDocument();
    expect(screen.getByText("KES 100")).toBeInTheDocument();
    expect(screen.getByText("KES 900")).toBeInTheDocument();
    expect(screen.getByText("01/01/2023")).toBeInTheDocument();
    expect(screen.getByText("John Doe")).toBeInTheDocument();
  });

  test("opens invoice details modal when the eye button is clicked", () => {
    render(
      <table>
        <tbody>
          <PayingVoucherDetailsRow voucher={dummyVoucher} />
        </tbody>
      </table>,
    );

    expect(screen.queryByTestId("invoice-details-modal")).not.toBeInTheDocument();

    const openModalButton = screen.getByRole("button");
    fireEvent.click(openModalButton);

    expect(screen.getByTestId("invoice-details-modal")).toBeInTheDocument();
    expect(
      screen.getByText(`Invoice Details Modal for voucherId: ${dummyVoucher.id}`),
    ).toBeInTheDocument();
  });
});
