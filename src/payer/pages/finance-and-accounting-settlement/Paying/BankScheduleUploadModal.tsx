import React, { useRef, useState } from "react";
import { toast } from "react-toastify";
import { usePaymentsUploadMutation } from "../../../api/settlement/settlementApi";
import LoadingAnimation from "../../../components/animations/LoadingAnimation/LoadingAnimation";
import UploadCloudIcon from "../../../components/icons/UploadCloudIcon";
import XIcon from "../../../components/icons/XIcon";
import UserService from "../../../services/UserService";
import ExpectedColumns from "../ExpectedColumns";
import { UserInfo } from "../settlementTypes";

type Props = {
  setIsShowModal: React.Dispatch<React.SetStateAction<boolean>>;
};

const MAX_FILE_SIZE_MB = 30;
const allowedFileTypes = [".xls", ".xlsx", ".csv"];

export default function BankScheduleUploadModal({ setIsShowModal }: Props) {
  const userInfo = UserService.kcObject.tokenParsed as UserInfo;
  const [isDragging, setIsDragging] = useState(false); // State to track drag status
  const inputRef = useRef<HTMLInputElement | null>(null); // For resetting the input

  const [uploadPayments, { isLoading }] = usePaymentsUploadMutation();

  // Centralized file handling function
  function handleFileSelect(file: File | null) {
    if (!file) return;

    if (file.size > MAX_FILE_SIZE_MB * 1024 * 1024) {
      toast.error(`File size exceeds ${MAX_FILE_SIZE_MB}MB.`);
      return;
    }

    const fileType = file.name.split(".").pop()?.toLowerCase();
    if (!allowedFileTypes.includes(`.${fileType}`)) {
      toast.error("Invalid file type. Please upload a valid Excel or CSV file.");
      return;
    }

    uploadFileAndClose(file);
  }

  function handleFileChange(event: React.ChangeEvent<HTMLInputElement>) {
    const selectedFile = event.target.files?.[0] || null;
    handleFileSelect(selectedFile);
  }

  function handleDragOver(event: React.DragEvent<HTMLDivElement>) {
    event.preventDefault();
    setIsDragging(true);
  }

  function handleDragLeave() {
    setIsDragging(false);
  }

  function handleDrop(event: React.DragEvent<HTMLDivElement>) {
    event.preventDefault();
    setIsDragging(false);
    const droppedFile = event.dataTransfer.files?.[0] || null;
    handleFileSelect(droppedFile);
  }

  async function uploadFileAndClose(file: File) {
    try {
      const uploadResponse = await uploadPayments({
        parameters: {
          query: {
            payerId: userInfo.payerId,
            actionedBy: userInfo.preferred_username,
          },
        },
        body: {
          file: file,
        },
      });

      if ("error" in uploadResponse) {
        const { error } = uploadResponse;
        if (error && "data" in error) {
          const errorData = error.data;
          if (typeof errorData === "object" && errorData !== null && "error" in errorData) {
            const errorMessage = errorData.error as string;
            throw new Error(errorMessage);
          }
        }

        throw new Error("Error occurred when uploading file!");
      }

      toast.success("Bank Schedule Uploaded Successfully!");
      setIsShowModal(false);
    } catch (error) {
      if (error instanceof Error) {
        console.error(error.message);
        toast.error(error.message);
      }
    } finally {
      if (inputRef.current) {
        inputRef.current.value = ""; // Clear input value
      }
    }
  }

  return (
    <form className="relative border border-slate-100 bg-white px-8 py-8 shadow-md">
      <button
        className="absolute right-6 top-6"
        onClick={() => setIsShowModal(false)}
        aria-label="Close upload modal"
        type="button"
      >
        <XIcon width={24} />
      </button>

      <div className="mb-6 flex items-center justify-between">
        <div>
          <p className="text-blue text-xl font-semibold">Bank Schedule Upload</p>
          <p className="mt-2 italic">Upload the bank schedule or advice received from the bank.</p>
        </div>
      </div>

      <ExpectedColumns isShowDownloadButton={false} />

      <p className="mt-8 text-base font-semibold text-black">Upload and attach files</p>
      <p className="mt-1 text-slate-600">Upload and attach files for this payment.</p>

      <div
        className={`mt-4 flex flex-col items-center rounded-md border p-16 ${
          isDragging ? "bg-blue-200" : ""
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="rounded-2xl bg-slate-100 p-4">
          <div className="rounded-2xl bg-slate-200 p-8">
            <UploadCloudIcon height={64} width={64} />
          </div>
        </div>

        <div className="mt-8">
          <label className="cursor-pointer text-sm">
            <span className="font-medium text-blue-500">Click to upload</span> or drag and drop
            <input
              onChange={handleFileChange}
              className="hidden"
              type="file"
              accept=".xls,.xlsx,.csv"
              ref={inputRef}
            />
          </label>
        </div>
        <p className="mt-2 text-xs">Only excel and csv files are allowed. Max file size is 30MB.</p>
      </div>

      {isLoading && (
        <div className="fixed inset-0 flex items-center justify-center">
          <LoadingAnimation size={80} />
        </div>
      )}
    </form>
  );
}
