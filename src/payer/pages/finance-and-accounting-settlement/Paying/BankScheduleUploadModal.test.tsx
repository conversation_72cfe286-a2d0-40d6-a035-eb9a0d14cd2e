import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { toast } from "react-toastify";
import { vi } from "vitest";
import BankScheduleUploadModal from "./BankScheduleUploadModal";

const mockUploadPayments = vi.fn();

vi.mock("../../../api/settlement/settlementApi", async () => ({
  ...(await vi.importActual("../../../api/settlement/settlementApi")),
  usePaymentsUploadMutation: () => [mockUploadPayments, { isLoading: false }],
}));

vi.mock("../../../services/UserService", () => ({
  default: { kcObject: { tokenParsed: { payerId: 123, preferred_username: "test_user" } } },
}));

describe("BankScheduleUploadModal", () => {
  const setIsShowModal = vi.fn();

  beforeEach(() => {
    setIsShowModal.mockReset();
    mockUploadPayments.mockReset();
    vi.clearAllMocks();

    vi.spyOn(toast, "error").mockImplementation(() => {});
    vi.spyOn(toast, "success").mockImplementation(() => {});
  });

  test("closes modal when the close button is clicked", () => {
    render(<BankScheduleUploadModal setIsShowModal={setIsShowModal} />);
    const closeButton = screen.getByRole("button", {
      name: /close upload modal/i,
    });
    fireEvent.click(closeButton);
    expect(setIsShowModal).toHaveBeenCalledWith(false);
  });

  test("displays an error when file size exceeds limit", async () => {
    render(<BankScheduleUploadModal setIsShowModal={setIsShowModal} />);

    const largeFile = new File(["dummy content"], "test.xlsx", {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    // Set size > 30MB (30MB = ******** bytes)
    Object.defineProperty(largeFile, "size", { value: ******** });

    // Include hidden elements in the query
    const fileInput = screen.getByLabelText(/click to upload/i, { hidden: true });
    fireEvent.change(fileInput, { target: { files: [largeFile] } });

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith("File size exceeds 30MB.");
    });
    expect(mockUploadPayments).not.toHaveBeenCalled();
  });

  test("displays an error when file type is invalid", async () => {
    render(<BankScheduleUploadModal setIsShowModal={setIsShowModal} />);
    const invalidFile = new File(["dummy content"], "test.txt", {
      type: "text/plain",
    });
    Object.defineProperty(invalidFile, "size", { value: 1024 * 1024 });

    const fileInput = screen.getByLabelText(/click to upload/i, { hidden: true });
    fireEvent.change(fileInput, { target: { files: [invalidFile] } });

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith(
        "Invalid file type. Please upload a valid Excel or CSV file.",
      );
    });
    expect(mockUploadPayments).not.toHaveBeenCalled();
  });

  test("calls uploadPayments and closes modal on successful upload", async () => {
    mockUploadPayments.mockResolvedValueOnce({ data: { success: true } });

    render(<BankScheduleUploadModal setIsShowModal={setIsShowModal} />);
    const validFile = new File(["dummy content"], "test.csv", {
      type: "text/csv",
    });
    Object.defineProperty(validFile, "size", { value: 1024 * 1024 });

    const fileInput = screen.getByLabelText(/click to upload/i, { hidden: true });
    fireEvent.change(fileInput, { target: { files: [validFile] } });

    await waitFor(() => {
      expect(mockUploadPayments).toHaveBeenCalled();
      expect(toast.success).toHaveBeenCalledWith("Bank Schedule Uploaded Successfully!");
      expect(setIsShowModal).toHaveBeenCalledWith(false);
    });
  });

  test("displays an error when uploadPayments returns an error", async () => {
    const errorResponse = { error: { data: { error: "Upload failed!" } } };
    mockUploadPayments.mockResolvedValueOnce(errorResponse);

    render(<BankScheduleUploadModal setIsShowModal={setIsShowModal} />);
    const validFile = new File(["dummy content"], "test.xlsx", {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    Object.defineProperty(validFile, "size", { value: 1024 * 1024 });

    const fileInput = screen.getByLabelText(/click to upload/i, { hidden: true });
    fireEvent.change(fileInput, { target: { files: [validFile] } });

    await waitFor(() => {
      expect(mockUploadPayments).toHaveBeenCalled();
      expect(toast.error).toHaveBeenCalledWith("Upload failed!");
    });
    expect(setIsShowModal).not.toHaveBeenCalled();
  });

  test("changes background on drag over and resets on drag leave", () => {
    render(<BankScheduleUploadModal setIsShowModal={setIsShowModal} />);

    const dropZone = screen.getByText(/only excel and csv files are allowed/i).parentElement;
    expect(dropZone).toBeInTheDocument();

    fireEvent.dragOver(dropZone as HTMLElement);
    expect(dropZone?.className).toMatch(/bg-blue-200/);

    fireEvent.dragLeave(dropZone as HTMLElement);
    expect(dropZone?.className).not.toMatch(/bg-blue-200/);
  });

  test("handles file drop correctly", async () => {
    mockUploadPayments.mockResolvedValueOnce({ data: { success: true } });

    render(<BankScheduleUploadModal setIsShowModal={setIsShowModal} />);
    const validFile = new File(["dummy content"], "test.csv", {
      type: "text/csv",
    });
    Object.defineProperty(validFile, "size", { value: 1024 * 1024 });

    const dropZone = screen.getByText(/only excel and csv files are allowed/i).parentElement;
    expect(dropZone).toBeInTheDocument();

    const dataTransfer = {
      files: [validFile],
      getData: vi.fn(),
    };

    fireEvent.drop(dropZone, { dataTransfer });
    await waitFor(() => {
      expect(mockUploadPayments).toHaveBeenCalled();
      expect(toast.success).toHaveBeenCalledWith("Bank Schedule Uploaded Successfully!");
      expect(setIsShowModal).toHaveBeenCalledWith(false);
    });
  });
});
