import { XMarkIcon } from "@heroicons/react/24/outline";
import React, { useState } from "react";
import { TailSpin } from "react-loader-spinner";
import { toast } from "react-toastify";
import { useGetVoucherInvoicesQuery } from "../../../api/settlement/settlementApi";
import SecondaryPagination from "../../../components/ui/pagination/SecondaryPagination";
import Text from "../../../components/ui/typography/Text";
import PayingInvoiceDetailsTable from "./PayingInvoiceDetailsTable";

type Props = {
  setIsShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  voucherId: number;
};

export default function PayingInvoiceDetailsModal({ setIsShowModal, voucherId }: Props) {
  const size = 10;
  const [page, setPage] = useState(1);

  const {
    data: invoicesData,
    isLoading: isLoadingInvoices,
    isError: isInvoicesError,
  } = useGetVoucherInvoicesQuery({
    searchParams: {
      voucherIds: [voucherId],
      page,
      size,
    },
  });

  if (isInvoicesError) toast.error("Error occurred while getting invoices");

  const invoices = invoicesData?.data.content;
  const totalElements = invoicesData?.data.totalElements;
  const totalPages = invoicesData?.data.totalPages;

  return (
    <div className="relative p-8">
      <button className="absolute right-4 top-4 " onClick={() => setIsShowModal(false)}>
        <XMarkIcon className="w-5" />
      </button>

      <div className=" flex w-full items-center justify-between pr-8">
        <Text variant="heading">Invoice Details</Text>

        <SecondaryPagination
          currentPage={page}
          setCurrentPage={setPage}
          size={size}
          totalElements={Number(totalElements)}
          totalPages={Number(totalPages)}
        />
      </div>

      {isLoadingInvoices ? (
        <div className="flex h-full w-full items-center justify-center p-4" role="img">
          <TailSpin color="blue" />
        </div>
      ) : (
        <PayingInvoiceDetailsTable invoices={invoices} />
      )}
    </div>
  );
}
