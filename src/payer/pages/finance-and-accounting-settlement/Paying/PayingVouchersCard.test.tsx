import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { vi } from "vitest";
import { Payment } from "../settlementTypes";
import PayingVouchersCard from "./PayingVouchersCard";

// --- Mocks ---
// Mock currency formatting utility.
vi.mock("../../../utils/formatCurrency", () => ({
  formatNumberToKes: (amount: number) => `KES ${amount.toLocaleString()}`,
}));

// Mock time-between utility.
vi.mock("../../../utils/getTimeBetweenAsString", () => ({
  default: () => "5 mins ago",
}));

// Mock the PayingVoucherDetailsModal to render a dummy element.
vi.mock("./PayingVoucherDetailsModal", () => ({
  default: () => <div data-testid="voucher-details-modal">Voucher Details Modal</div>,
}));

// --- Dummy Data ---
const dummyPayment = {
  id: 123,
  paymentReference: "PAY-001",
  account: [{ accountName: "Test Account" }],
  amount: 1000,
  createdOn: new Date("2022-01-01T12:00:00Z").toISOString(),
  createdBy: { userName: "Tester" },
} as Payment;

describe("PayingVouchersCard", () => {
  beforeAll(() => {
    global.ResizeObserver = class {
      observe = () => null;
      unobserve = () => null;
      disconnect = () => null;
    };
  });

  test("renders payment information correctly", () => {
    render(<PayingVouchersCard payment={dummyPayment} />);

    // Payment reference
    expect(screen.getByText("PAY-001")).toBeInTheDocument();
    // Account name
    expect(screen.getByText("Test Account")).toBeInTheDocument();
    // Formatted payable amount
    expect(screen.getByText("KES 1,000")).toBeInTheDocument();
    // Created by
    expect(screen.getByText("Tester")).toBeInTheDocument();
    // Date created (formatted in en-GB long format, e.g. "1 January 2022")
    expect(screen.getByText(/1 January 2022/i)).toBeInTheDocument();
    // Time between (from getTimeBetweenAsString mock)
    expect(screen.getByText("5 mins ago")).toBeInTheDocument();
  });

  test("opens voucher details modal when 'View voucher details' is clicked", async () => {
    render(<PayingVouchersCard payment={dummyPayment} />);

    // The menu button is rendered by Headless UI.
    // Since it has no accessible label, we grab the first button in the component.
    const menuButton = screen.getAllByRole("button")[0];
    fireEvent.click(menuButton as HTMLElement);

    // After clicking the menu button, the menu items appear.
    // Find the button with the text "View voucher details".
    const viewDetailsButton = await screen.findByText(/view voucher details/i);
    fireEvent.click(viewDetailsButton);

    // Assert that the dummy details modal is rendered.
    await waitFor(() => {
      expect(screen.getByTestId("voucher-details-modal")).toBeInTheDocument();
    });
  });
});
