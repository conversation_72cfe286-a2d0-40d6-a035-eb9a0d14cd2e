import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { toast } from "react-toastify";
import { Mock, vi } from "vitest";
import * as settlementApi from "../../../api/settlement/settlementApi"; // Import the module instead of require
import PayingInvoiceDetailsModal from "./PayingInvoiceDetailsModal";

// --- Dummy Props & Data ---
const mockSetIsShowModal = vi.fn();
const voucherId = 1;

const dummyInvoicesData = {
  data: {
    content: [
      { id: 1, invoiceNumber: "INV-001", amount: 1000 },
      // Add other dummy invoice properties as needed
    ],
    totalElements: 1,
    totalPages: 1,
  },
};

// --- Mocks ---
// Mock the query hook.
vi.mock("../../../api/settlement/settlementApi", () => ({
  useGetVoucherInvoicesQuery: vi.fn(),
}));

// Mock SecondaryPagination to avoid rendering its internals.
vi.mock("../../../components/ui/pagination/SecondaryPagination", () => ({
  default: () => <div data-testid="secondary-pagination">Pagination</div>,
}));

// Mock the PayingInvoiceDetailsTable so we can simply assert its presence.
vi.mock("./PayingInvoiceDetailsTable", () => ({
  default: ({ invoices }: { invoices: unknown }) => (
    <div data-testid="invoice-details-table">Invoice Details Table: {JSON.stringify(invoices)}</div>
  ),
}));

describe("PayingInvoiceDetailsModal", () => {
  beforeEach(() => {
    mockSetIsShowModal.mockReset();
    vi.clearAllMocks();
    vi.spyOn(toast, "error").mockImplementation(() => "");
  });

  test("renders loading spinner when invoices are loading", () => {
    // Arrange: return loading state.
    (settlementApi.useGetVoucherInvoicesQuery as Mock).mockReturnValue({
      data: undefined,
      isLoading: true,
      isError: false,
    });

    render(<PayingInvoiceDetailsModal setIsShowModal={mockSetIsShowModal} voucherId={voucherId} />);

    // Assert: Expect the TailSpin spinner (from react-loader-spinner) to be in the document.
    // TailSpin renders an SVG with role "img"
    const spinner = screen.getByRole("img");
    expect(spinner).toBeInTheDocument();
  });

  test("renders invoice details table when data is loaded", async () => {
    // Arrange: return loaded data.
    (settlementApi.useGetVoucherInvoicesQuery as Mock).mockReturnValue({
      data: dummyInvoicesData,
      isLoading: false,
      isError: false,
    });

    render(<PayingInvoiceDetailsModal setIsShowModal={mockSetIsShowModal} voucherId={voucherId} />);

    // Assert: The mocked invoice details table should be rendered with our dummy data.
    const table = await screen.findByTestId("invoice-details-table");
    expect(table).toBeInTheDocument();
    expect(table.textContent).toContain("INV-001");
  });

  test("calls toast.error when an error occurs while fetching invoices", async () => {
    // Arrange: simulate an error state.
    (settlementApi.useGetVoucherInvoicesQuery as Mock).mockReturnValue({
      data: undefined,
      isLoading: false,
      isError: true,
    });

    render(<PayingInvoiceDetailsModal setIsShowModal={mockSetIsShowModal} voucherId={voucherId} />);

    // Assert: toast.error should be called with the expected error message.
    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith("Error occurred while getting invoices");
    });
  });

  test("closes modal when the close button is clicked", () => {
    // Arrange: return loaded data to ensure the component renders fully.
    (settlementApi.useGetVoucherInvoicesQuery as Mock).mockReturnValue({
      data: dummyInvoicesData,
      isLoading: false,
      isError: false,
    });

    render(<PayingInvoiceDetailsModal setIsShowModal={mockSetIsShowModal} voucherId={voucherId} />);

    // The close button is rendered as the first button in the DOM (it wraps the XMarkIcon)
    const buttons = screen.getAllByRole("button");
    fireEvent.click(buttons[0] as HTMLElement);
    expect(mockSetIsShowModal).toHaveBeenCalledWith(false);
  });
});
