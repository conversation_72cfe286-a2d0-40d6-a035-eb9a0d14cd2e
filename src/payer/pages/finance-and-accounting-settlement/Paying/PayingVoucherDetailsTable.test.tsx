import { render, screen } from "@testing-library/react";
import { Voucher } from "../settlementTypes";
import PayingVoucherDetailsTable from "./PayingVoucherDetailsTable";

// Dummy data for testing.
const dummyVouchers = [
  {
    id: 1,
    voucherNo: "VCHR001",
    amount: 1000,
    discount: 50,
    payableAmount: 950,
    createdOn: "2023-01-01T00:00:00Z",
    createdBy: { userName: "Alice" },
  },
  {
    id: 2,
    voucherNo: "VCHR002",
    amount: 2000,
    discount: 100,
    payableAmount: 1900,
    createdOn: "2023-02-01T00:00:00Z",
    createdBy: { userName: "Bob" },
  },
] as Voucher[];

describe("PayingVoucherDetailsTable", () => {
  test("renders table header items", () => {
    render(<PayingVoucherDetailsTable vouchers={[]} />);

    // Assert that each header cell is rendered.
    expect(screen.getByText("VOUCHER NUMBER")).toBeInTheDocument();
    expect(screen.getByText("VOUCHER AMOUNT")).toBeInTheDocument();
    expect(screen.getByText("DISCOUNT")).toBeInTheDocument();
    expect(screen.getByText("PAYABLE AMOUNT")).toBeInTheDocument();
    expect(screen.getByText("DATE CREATED")).toBeInTheDocument();
    expect(screen.getByText("CREATED BY")).toBeInTheDocument();
    expect(screen.getByText("ACTION")).toBeInTheDocument();
  });

  test("renders voucher rows when vouchers prop is provided", () => {
    render(<PayingVoucherDetailsTable vouchers={dummyVouchers} />);

    // Assert that voucher rows (e.g., voucher numbers) are rendered.
    expect(screen.getByText("VCHR001")).toBeInTheDocument();
    expect(screen.getByText("VCHR002")).toBeInTheDocument();
  });

  test("renders empty tbody when vouchers prop is undefined", () => {
    const { container } = render(<PayingVoucherDetailsTable vouchers={undefined} />);
    const tbody = container.querySelector("tbody");
    // Expect no voucher rows to be rendered.
    expect(tbody?.children.length).toBe(0);
  });
});
