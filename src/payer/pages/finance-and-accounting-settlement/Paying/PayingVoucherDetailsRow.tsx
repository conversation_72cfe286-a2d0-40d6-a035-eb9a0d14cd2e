import { useState } from "react";
import EyeIcon from "../../../components/icons/EyeIcon";
import DialogWrapper from "../../../components/ui/modal/DialogWrapper";
import TableDataItem from "../../../components/ui/table/TableDataItem";
import { convertDateString } from "../../../utils/convertDateString";
import { formatNumberToKes } from "../../../utils/formatCurrency";
import { Voucher } from "../settlementTypes";
import PayingInvoiceDetailsModal from "./PayingInvoiceDetailsModal";

type Props = {
  voucher: Voucher;
};

export default function PayingVoucherDetailsRow({ voucher }: Props) {
  const [isShowInvoiceDetailsModal, setIsShowInvoiceDetailsModal] = useState(false);

  return (
    <>
      <tr className="border-b">
        <TableDataItem item={voucher.voucherNo} />
        <TableDataItem item={formatNumberToKes(voucher.amount)} />
        <TableDataItem item={formatNumberToKes(voucher.discount)} />
        <TableDataItem item={formatNumberToKes(voucher.payableAmount) || "NA"} />
        <TableDataItem item={convertDateString(voucher.createdOn)} />
        <TableDataItem item={voucher.createdBy?.userName || "-"} />
        <td className="py-4">
          <button onClick={() => setIsShowInvoiceDetailsModal(true)}>
            <EyeIcon fill="#1D4ED8" />
          </button>
        </td>
      </tr>

      <DialogWrapper
        maxWidth="max-w-[750px]"
        onClose={() => setIsShowInvoiceDetailsModal(false)}
        show={isShowInvoiceDetailsModal}
      >
        <PayingInvoiceDetailsModal
          setIsShowModal={setIsShowInvoiceDetailsModal}
          voucherId={voucher.id}
        />
      </DialogWrapper>
    </>
  );
}
