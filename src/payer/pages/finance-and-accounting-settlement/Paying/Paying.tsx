import { Menu } from "@headlessui/react";
import { ChevronDownIcon } from "@heroicons/react/24/outline";
import { useState } from "react";
import { TailSpin } from "react-loader-spinner";
import { toast } from "react-toastify";
import { useGetPaymentsQuery } from "../../../api/settlement/settlementApi";
import DownloadCloudIcon from "../../../components/icons/DownloadCloudIcon";
import SearchIcon from "../../../components/icons/SearchIcon";
import UploadCloudIcon from "../../../components/icons/UploadCloudIcon";
import NoClaimsVouchering from "../../../components/illustrations/NoClaimsVouchering";
import EmptyState from "../../../components/ui/EmptyState";
import MainWrapper from "../../../components/ui/MainWrapper";
import DialogWrapper from "../../../components/ui/modal/DialogWrapper";
import PrimaryPagination from "../../../components/ui/pagination/PrimaryPagination";
import Text from "../../../components/ui/typography/Text";
import { baseUrl } from "../../../lib/Utils";
import UserService from "../../../services/UserService";
import SettlementNavigation from "../SettlementNavigation";
import { UserInfo } from "../settlementTypes";
import BankScheduleUploadModal from "./BankScheduleUploadModal";
import PayingVouchersCard from "./PayingVouchersCard";
import LoadingAnimation from "../../../components/animations/LoadingAnimation/LoadingAnimation";

export default function Paying() {
  const userInfo = UserService.kcObject.tokenParsed as UserInfo;
  const downloadUrl = `${baseUrl}/api/v1/visit/payments/download/${userInfo.payerId}`;
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const [query, setQuery] = useState("");

  const [isShowModal, setIsShowModal] = useState(false);

  const {
    data: paymentsData,
    isLoading: isLoadingPayments,
    isFetching: isFetchingPayments,
    isError: isPaymentsError,
  } = useGetPaymentsQuery({
    parameters: {
      query: {
        payerId: userInfo.payerId,
        paid: false,
        page: page,
        size: pageSize,
        ...(query && { query: query }),
      },
    },
  });

  if (isPaymentsError) {
    toast.error("Error occurred while getting payments!");
  }

  const payments = paymentsData?.data?.content;
  const totalPages = paymentsData?.data?.totalPages;
  const totalElements = paymentsData?.data?.totalElements;
  const isPendingPayments = isLoadingPayments || isFetchingPayments;

  return (
    <MainWrapper>
      <header className="mb-6 flex items-center">
        <Text variant="heading">Payment List</Text>

        <div className="ml-auto flex items-center gap-4">
          <div className="relative">
            <span className="absolute left-2 flex h-full items-center">
              <SearchIcon size={18} />
            </span>
            <input
              className="min-w-[400px] rounded-md border-slate-300 pl-8 text-xs"
              placeholder="Search by reference number"
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
            />
          </div>

          <Menu
            as={`div`}
            className={`relative flex items-center gap-2 rounded-md bg-blue-600 px-3 py-1 text-xs text-white`}
          >
            <Menu.Button className={`flex gap-1 p-1`}>
              <span>Actions</span>
              <ChevronDownIcon className="h-4 w-4 " />
            </Menu.Button>

            <Menu.Items
              className={`absolute -right-4 top-[120%] z-10 flex  flex-col gap-4 whitespace-nowrap rounded-md border bg-white p-4  text-black shadow-md`}
            >
              <Menu.Item>
                <a className="flex items-center gap-2  font-medium " href={downloadUrl}>
                  <DownloadCloudIcon size={20} />
                  <span>Download bank transfer template</span>
                </a>
              </Menu.Item>

              <Menu.Item>
                <button
                  className="flex items-center gap-2  font-medium "
                  onClick={() => setIsShowModal(true)}
                >
                  <UploadCloudIcon width={20} height={20} />
                  <span>Upload Bank Schedule</span>
                </button>
              </Menu.Item>
            </Menu.Items>
          </Menu>
        </div>

        <DialogWrapper
          show={isShowModal}
          maxWidth="max-w-[700px]"
          onClose={() => setIsShowModal(false)}
        >
          <BankScheduleUploadModal setIsShowModal={setIsShowModal} />
        </DialogWrapper>
      </header>

      <SettlementNavigation />

      <div className="mt-4 flex flex-col gap-4">
        {isPendingPayments ? (
          <div className="flex h-[60vh] items-center justify-center">
            <LoadingAnimation size={50} />
          </div>
        ) : payments && payments.length > 0 ? (
          <>
            {payments.map((payment) => (
              <PayingVouchersCard key={payment.id} payment={payment} />
            ))}

            <PrimaryPagination
              onPageNumberClick={(page) => setPage(page)}
              onSizeChange={(size) => setPageSize(size)}
              pageNumber={page}
              pageSize={pageSize}
              totalElements={totalElements as number}
              totalPages={totalPages as number}
            />
          </>
        ) : (
          <EmptyState
            illustration={<NoClaimsVouchering />}
            message={{
              title: "No payments available",
              description:
                "You haven't created any payment references yet. Once the references have been created, they'll be displayed here for your review and further actions...",
            }}
          />
        )}
      </div>
    </MainWrapper>
  );
}
