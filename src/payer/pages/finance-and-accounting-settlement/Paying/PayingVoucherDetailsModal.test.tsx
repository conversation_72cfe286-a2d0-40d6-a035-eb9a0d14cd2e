import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { KeycloakInstance } from "keycloak-js";
import { toast } from "react-toastify";
import { Mock, vi } from "vitest";
import * as settlementApi from "../../../api/settlement/settlementApi";
import UserService from "../../../services/UserService";
import PayingVoucherDetailsModal from "./PayingVoucherDetailsModal";

const mockSetIsShowModal = vi.fn();
const paymentId = 1;

const dummyVouchersData = {
  data: {
    content: [{ id: 1, voucherNumber: "VOUCHER-001", amount: 2000 }],
    totalElements: 1,
    totalPages: 1,
  },
};

vi.mock("../../../api/settlement/settlementApi", async () => ({
  ...(await vi.importActual("../../../api/settlement/settlementApi")),
  useGetPaymentVouchersQuery: vi.fn(),
}));

vi.mock("./PayingVoucherDetailsTable", () => ({
  default: ({ vouchers }: { vouchers: unknown }) => (
    <div data-testid="voucher-details-table">Voucher Details Table: {JSON.stringify(vouchers)}</div>
  ),
}));

vi.mock("../../../components/ui/typography/Text", () => ({
  default: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

describe("PayingVoucherDetailsModal", () => {
  beforeEach(() => {
    mockSetIsShowModal.mockReset();
    vi.clearAllMocks();

    vi.spyOn(toast, "error").mockImplementation(() => "");

    const mockKeycloakInstance: Partial<KeycloakInstance> = {
      tokenParsed: { payerId: "12345" },
      init: vi.fn(),
      login: vi.fn(),
      logout: vi.fn(),
      register: vi.fn(),
    };

    vi.spyOn(UserService, "kcObject", "get").mockReturnValue(
      mockKeycloakInstance as KeycloakInstance,
    );
  });

  test("renders loading spinner when vouchers are loading", () => {
    (settlementApi.useGetPaymentVouchersQuery as Mock).mockReturnValue({
      data: undefined,
      isLoading: true,
      isError: false,
    });

    render(<PayingVoucherDetailsModal setIsShowModal={mockSetIsShowModal} paymentId={paymentId} />);

    const spinner = screen.getByRole("img");
    expect(spinner).toBeInTheDocument();
  });

  test("renders voucher details table when data is loaded", async () => {
    (settlementApi.useGetPaymentVouchersQuery as Mock).mockReturnValue({
      data: dummyVouchersData,
      isLoading: false,
      isError: false,
    });

    render(<PayingVoucherDetailsModal setIsShowModal={mockSetIsShowModal} paymentId={paymentId} />);

    const table = await screen.findByTestId("voucher-details-table");
    expect(table).toBeInTheDocument();
    expect(table.textContent).toContain("VOUCHER-001");
  });

  test("calls toast.error when an error occurs while fetching vouchers", async () => {
    (settlementApi.useGetPaymentVouchersQuery as Mock).mockReturnValue({
      data: undefined,
      isLoading: false,
      isError: true,
    });

    render(<PayingVoucherDetailsModal setIsShowModal={mockSetIsShowModal} paymentId={paymentId} />);

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith("Error occurred while getting vouchers!");
    });
  });

  test("closes modal when the close button is clicked", () => {
    (settlementApi.useGetPaymentVouchersQuery as Mock).mockReturnValue({
      data: dummyVouchersData,
      isLoading: false,
      isError: false,
    });

    render(<PayingVoucherDetailsModal setIsShowModal={mockSetIsShowModal} paymentId={paymentId} />);

    const closeButton = screen.getAllByRole("button")[0];
    fireEvent.click(closeButton as HTMLElement);
    expect(mockSetIsShowModal).toHaveBeenCalledWith(false);
  });
});
