import { Menu } from "@headlessui/react";
import { EyeIcon } from "@heroicons/react/24/outline";
import { useState } from "react";
import DotIcon from "../../../components/icons/DotIcon";
import ThreeDotsIcon from "../../../components/icons/ThreeDotsIcon";
import CardWrapper from "../../../components/ui/CardWrapper";
import DialogWrapper from "../../../components/ui/modal/DialogWrapper";
import { formatNumberToKes } from "../../../utils/formatCurrency";
import getTimeBetweenAsString from "../../../utils/getTimeBetweenAsString";
import { Payment } from "../settlementTypes";
import PayingVoucherDetailsModal from "./PayingVoucherDetailsModal";

type Props = {
  payment: Payment;
};

export default function PayingVouchersCard({ payment }: Props) {
  const [isShowDetailsModal, setIsShowDetailsModal] = useState(false);

  const offsetInMillis = new Date().getTimezoneOffset() * 60 * 1000;

  const timeZoneTimeString = new Date(
    new Date(payment.createdOn).getTime() - offsetInMillis,
  ).toISOString();

  return (
    <CardWrapper className="relative flex flex-col gap-2 text-sm">
      <p className="text-base font-medium text-[#304254]">{payment.paymentReference}</p>

      <p className="flex items-center gap-2">
        <span className="text-[#6B7280]">Account Name : </span>{" "}
        <span className="text-[#374151]">{payment.account[0]?.accountName || "NA"} </span>
      </p>

      <p className="flex items-center gap-2">
        <span className="text-[#6B7280]">Payable Amount : </span>{" "}
        <span className="text-[#374151]">{formatNumberToKes(payment.amount)}</span>
      </p>

      <p className="flex items-center gap-2">
        <span className="text-[#6B7280]">Date Created : </span>
        <span className="flex items-center gap-2">
          <span className="text-[#374151]">
            {new Intl.DateTimeFormat("en-GB", { dateStyle: "long" }).format(
              new Date(payment.createdOn),
            )}
          </span>
          <DotIcon />
          <span className="text-[#374151]">{getTimeBetweenAsString(timeZoneTimeString)}</span>
        </span>
      </p>

      <p className="flex items-center gap-2">
        <span className="text-[#6B7280]">Created By : </span>{" "}
        <span className="text-[#374151]">{payment?.createdBy?.userName || "NA"}</span>
      </p>

      <Menu as={`div`} className={`absolute  right-6 top-4`}>
        <Menu.Button>
          <ThreeDotsIcon />
        </Menu.Button>

        <Menu.Items
          className={`absolute right-0 flex w-56 flex-col gap-4 rounded-md border p-3 shadow-md`}
        >
          <Menu.Item>
            <button
              className="flex items-center gap-2  text-sm font-medium "
              onClick={() => setIsShowDetailsModal(true)}
            >
              <EyeIcon className="w-5" />
              <span>View voucher details</span>
            </button>
          </Menu.Item>
        </Menu.Items>
      </Menu>

      <DialogWrapper
        onClose={() => setIsShowDetailsModal(false)}
        show={isShowDetailsModal}
        className="max-w-[90vw]"
      >
        <PayingVoucherDetailsModal setIsShowModal={setIsShowDetailsModal} paymentId={payment.id} />
      </DialogWrapper>
    </CardWrapper>
  );
}
