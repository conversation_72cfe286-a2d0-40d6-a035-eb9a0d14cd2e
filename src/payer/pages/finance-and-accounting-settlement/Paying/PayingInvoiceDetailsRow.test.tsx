import { render, screen } from "@testing-library/react";
import { vi } from "vitest";
import { Invoice } from "../settlementTypes";
import PayingInvoiceDetailsRow from "./PayingInvoiceDetailsRow";

// --- Mocks ---
// Mock formatNumberToKes to return a formatted string.
vi.mock("../../../utils/formatCurrency", () => ({
  formatNumberToKes: vi.fn((num: number) => `KES ${num}`),
}));

// Mock convertDateString to return a predictable formatted date.
vi.mock("../../../utils/convertDateString", () => ({
  convertDateString: vi.fn(() => "formatted-date"),
}));

describe("PayingInvoiceDetailsRow", () => {
  const dummyInvoice = {
    invoiceNumber: "INV-001",
    providerName: "Provider ABC",
    totalAmount: 1234,
    createdAt: "2023-05-05T12:34:56Z",
  } as Invoice;

  test("renders invoice details correctly", () => {
    render(
      <table>
        <tbody>
          <PayingInvoiceDetailsRow invoice={dummyInvoice} />
        </tbody>
      </table>,
    );

    // Assert that each detail is rendered in the row.
    expect(screen.getByText("INV-001")).toBeInTheDocument();
    expect(screen.getByText("Provider ABC")).toBeInTheDocument();
    expect(screen.getByText("KES 1234")).toBeInTheDocument();
    expect(screen.getByText("formatted-date")).toBeInTheDocument();
  });
});
