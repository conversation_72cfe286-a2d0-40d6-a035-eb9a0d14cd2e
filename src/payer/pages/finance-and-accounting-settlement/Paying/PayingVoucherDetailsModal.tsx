import { XMarkIcon } from "@heroicons/react/24/outline";
import React from "react";
import { TailSpin } from "react-loader-spinner";
import { toast } from "react-toastify";
import { useGetPaymentVouchersQuery } from "../../../api/settlement/settlementApi";
import Text from "../../../components/ui/typography/Text";
import UserService from "../../../services/UserService";
import { UserInfo } from "../settlementTypes";
import PayingVoucherDetailsTable from "./PayingVoucherDetailsTable";

type Props = {
  setIsShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  paymentId: number;
};

export default function PayingVoucherDetailsModal({ setIsShowModal, paymentId }: Props) {
  const userInfo = UserService.kcObject.tokenParsed as UserInfo;

  const {
    data: vouchersData,
    isLoading: isLoadingVouchers,
    isError: isVouchersError,
  } = useGetPaymentVouchersQuery({
    payerId: userInfo.payerId,
    searchParams: {
      paymentIds: [paymentId],
    },
  });

  if (isVouchersError) toast.error("Error occurred while getting vouchers!");

  const vouchers = vouchersData?.data.content;

  return (
    <section className="relative overflow-y-auto border  border-slate-100 bg-white p-6 shadow-md">
      <button className="absolute right-6 top-6" onClick={() => setIsShowModal(false)}>
        <XMarkIcon className="w-5" strokeWidth={2} />
      </button>

      <Text variant="heading">Voucher Details</Text>

      {isLoadingVouchers ? (
        <div className="flex h-full w-full items-center justify-center p-4" role="img">
          <TailSpin color="blue" />
        </div>
      ) : (
        <PayingVoucherDetailsTable vouchers={vouchers} />
      )}
    </section>
  );
}
