export type UserInfo = {
  email_verified: boolean;
  payerId: number;
  preferred_username: string;
  sub: string;
};

export type Voucher = {
  id: number;
  payerId: number;
  provider: {
    usesGlobalBatchInvoice: boolean;
    providerName: string;
    providerId: number;
    mainFacilityId: number;
  };
  providerAccount?: {
    mappingId: number;
    accountName: string;
    accountNumber: string;
    providerName: string;
    providerId: number;
    payerId: number;
    accountId: number;
    bankName: string;
    bankBranch: string;
  };
  voucherNo: string;
  amount: number;
  paymentStatus: VoucherPaymentStatus;
  batchCriteria: "Scheme" | "Provider" | "Region" | "Benefit" | "Aging";
  batchCriteriaId: number;
  batchCriteriaValue: string;
  discount: number;
  payableAmount: number;
  discountType: DiscountType;
  createdBy?: {
    name: string;
    value: string;
    id: string;
    email: string;
    userName: string;
    firstName: string;
    lastName: string;
  };
  paidBy: {
    name: string;
    value: string;
    id: string;
    email: string;
    userName: string;
    firstName: string;
    lastName: string;
  };
  createdOn: string; // ISO 8601 date string
};

export enum VoucherPaymentStatus {
  Paid = "Paid",
  Unpaid = "UnPaid",
  Paying = "Paying",
}

export enum DiscountType {
  fixed = "Fixed",
  percentage = "Percentage",
}

export type Invoice = {
  id: number;
  visitNumber: number;
  hospitalProviderId: number | null;
  invoiceNumber: string;
  service: string | null;
  totalAmount: number;
  payableAmount: number | null;
  deductibleAmount: number | null;
  actionedBy: string | null;
  claimRef: string | null;
  batchInvoiceNumber: string | null;
  status: string;
  transType: string;
  batchStatus: string | null;
  vettingStatus: "PENDING" | "APPROVED" | "DECLINED" | "PARTIAL" | "BATCHED";
  vetDate: string | null;
  benefitId: number | null;
  benefitName: string;
  dispatched: string | null;
  createdAt: string;
  payerStatus: string;
  payerMessage: string | null;
  paymentStatus: string;
  paymentReference: string | null;
  paymentDate: string | null;
  providerName: string;
  schemeName: string;
  payerName: string;
  memberNumber: string;
  memberName: string;
  invoiceDate: string;
  visitType: string; // for claim type
  invoiceLines: LineItem[];
};

export type LineItem = {
  id: number;
  lineTotal: number;
  description: string;
  invoiceNumber: string;
  quantity: number;
  unitPrice: number;
  lineType: string | null;
  claimRef: string;
  lineCategory: string;
  createdAt: string;
};

export type ProviderBankAccount = {
  id: number;
  bankName: string;
  bankBranch: string;
  accountName: string;
  accountNumber: string;
  createdAt: string;
};

export type PayerRegion = {
  id: number;
  name: string;
  payer: {
    id: number;
    name: string;
    contact: string;
    email: string;
    website: string;
    streetAddress: string;
    postalAddress: string;
    logo: string;
    type: "UNDERWRITER" | "CORPORATE" | "INTERMEDIARY";
    mainPayer: unknown;
  };
};

export type Payment = {
  id: number;
  payerId: number;
  account: PayerProviderBankAccount[];
  paymentReference: string;
  bankReference: string;
  chequeNo:string;
  amount: number;
  modeOfPayment: "Cheque" | "BankTransfer";
  paid: true;
  bankSchedule: string;
  createdBy: {
    id: string;
    userName: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  createdOn: string;
};

export type PayerProviderBankAccount={
providerName:	string
bankBranch:	string
providerId:	number
payerId:	number
accountId:	number
bankCode:	string
branchCode:	string
accountNumber:	string
accountName:	string
mappingId:	number
bankName:	string

}