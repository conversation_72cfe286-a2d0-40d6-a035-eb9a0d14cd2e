import { useState, useEffect } from "react";
import { connect, useSelector } from "react-redux";
import { getPoliciesInSchemes } from "../../store/schemes/actions";
import makeAnimated from "react-select/animated";
import "react-toastify/dist/ReactToastify.css";
import { RootState } from "../../store";
import SchemesPoliciesTable from "../partials/pageItems/SchemesPoliciesTable";
import { useNavigate } from "react-router-dom";

const animatedComponents = makeAnimated();
const SchemesPolicies = ({ getPoliciesInSchemes }) => {
  const navigate = useNavigate();
  const [refreshKey, setRefreshKey] = useState(0);
  const schemeUrl = window.location.pathname.split("/schemes/")[1];
  const schemeUrlId = schemeUrl.substring(0, schemeUrl.indexOf("/"));
  useEffect(() => {
    getPoliciesInSchemes(schemeUrlId).then(() => {
      setRefresh<PERSON><PERSON>((oldKey) => oldKey + 1);
    });
  }, []);

  const schemePolicies = useSelector((state: RootState) => state.schemes.schemePolicies);
  const schemeObj = useSelector((state: RootState) => state.schemes.selectedScheme);

  return (
    <div className="flex h-full overflow-hidden">
      {/* Content area */}
      <div className="relative flex flex-1 flex-col overflow-y-auto overflow-x-hidden ">
        {/*  Site header */}
        <main className="mb-10 ml-10   mr-10 ">
          <div className="mx-auto w-full max-w-9xl  rounded-md px-4 pb-5 shadow-lg sm:px-6 lg:px-8 ">
            {/* Page header */}
            <button
              onClick={() => navigate("/schemes")}
              className="mb-3 rounded bg-gray-200 p-2"
              title="Back to Schemes"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth="2"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M10 19l-7-7m0 0l7-7m-7 7h18"
                />
              </svg>
            </button>
            <div className="mb-5 mt-5 sm:flex sm:items-center sm:justify-start">
              <div className="sm:mb-0">
                <h1 className="md:text-md text-sm font-bold text-gray-800 lg:text-lg">
                  Policies For <span className="italic">{schemeObj.name}</span> Scheme
                </h1>{" "}
              </div>
            </div>

            <div className="text-sm">
              {schemePolicies.length < 1 ? (
                "No policies for selected Scheme"
              ) : (
                <SchemesPoliciesTable schemePolicies={schemePolicies} />
              )}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};
const mapStateToProps = (state) => ({
  payers: state.payers.payers,
  schemePolicies: state.schemes.schemePolicies,
  loading: state.payers.loading,
});
const mapDispatchToProps = (dispatch) => ({
  getPoliciesInSchemes: (id) => dispatch(getPoliciesInSchemes(id)),
});
export default connect(mapStateToProps, mapDispatchToProps)(SchemesPolicies);
