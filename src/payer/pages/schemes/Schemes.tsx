import { useState, useEffect } from "react";
import SchemesTable from "../../pages/partials/pageItems/SchemesTable";
import { connect, useDispatch, useSelector } from "react-redux";
import makeAnimated from "react-select/animated";
import "react-toastify/dist/ReactToastify.css";
import { RootState } from "../../store";
import { getPlanByPayerId } from "../../store/members/actions";
import UserService from "../../services/UserService";

const animatedComponents = makeAnimated();

export const Schemes = () => {
  const [selectedItems, setSelectedItems] = useState([]);
  const [refreshKey, setRefreshKey] = useState(0);
  const dispatch = useDispatch();

  const handleSelectedItems = (selectedItems: any) => {
    setSelectedItems([...selectedItems]);
  };

  const userObjState: any = useSelector((state: RootState) => state.payers.userObj);
  const schemes = useSelector((state: RootState) => state.memberInfo.plans);

  const payerId = UserService.getPayer().tokenParsed.payerId;
  useEffect(() => {
    dispatch(getPlanByPayerId(payerId));
  }, []);

  useEffect(() => {
    setRefreshKey((oldKey) => oldKey + 1);
  }, [schemes]);

  return (
    <div className="flex h-full overflow-hidden">
      <div className="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden ">
        {/*  Site header */}
        <main className="ml-10 mr-10   mb-10 ">
          <div className="px-4 sm:px-6 lg:px-8  w-full max-w-9xl mx-auto rounded-md shadow-lg pb-5 ">
            {/* Page header */}
            <div className="sm:flex sm:justify-between sm:items-center">
              {/* Left: Title */}

              <div className="sm:mb-0">
                <h1 className="text-sm md:text-md lg:text-lg text-gray-800 font-bold">Schemes</h1>{" "}
              </div>

              {/* Right: Actions */}
              <div className="grid grid-flow-col sm:auto-cols-max justify-start sm:justify-end gap-2"></div>
            </div>

            {/* More actions */}
            <div className="sm:flex sm:justify-between sm:items-center">
              {/* Left side */}
              <div className="mb-4 sm:mb-0 w-96"></div>

              {/* Right side */}
              <div className="grid grid-flow-col sm:auto-cols-max justify-start sm:justify-end gap-2 mb-5">
                {/* Delete button */}
                {/* <DeleteButton selectedItems={selectedItems} /> */}
                {/* Dropdown */}
              </div>
            </div>
            {/* Modal */}

            {/* Table */}
            <SchemesTable
              selectedItems={handleSelectedItems}
              schemes={schemes}
              payerId={userObjState.payerId}
              key={refreshKey}
            />

            {/* No elements exist */}
            <div className="flex justify-center font-sans text-sm md:text-base lg:text-lg mr-6 pt-0 font-normal">
              {schemes?.length < 1 || !schemes ? <div>No Records Exist</div> : ""}
            </div>
            {/* Pagination */}
            <div className="mt-8">
              {/* <Pagination
                totalElements={totalElements}
                totalPages={totalPages}
                pageNumber={pageNumber}
                OnPageNumberClick={handlePaginationChange}
              /> */}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default connect(null, null)(Schemes);
