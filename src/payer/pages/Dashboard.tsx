import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import UserService from "../services/UserService";
import { RootState } from "../store";
import { getPayer, setUserDetails } from "../store/payers/actions";

export default function Dashboard() {
  const dispatch = useDispatch();
  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const userObj = { payerId };

  useEffect(() => {
    if (userObj) {
      dispatch(setUserDetails(userObj));
    }
  }, []);

  const userObjState: any = useSelector((state: RootState) => state.payers.userObj);
  const payerName = useSelector((state: RootState) => state.payers.payer.name);

  useEffect(() => {
    if (Object.keys(userObjState).length > 0) {
      dispatch(getPayer(userObjState.payerId));
    }
  }, [userObjState]);

  return (
    <div className="flex flex-grow overflow-hidden">
      <div className="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
        <main className="ml-10 mr-10 pt-8">
          <section>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 pb-4">
              <div className="flex flex-col items-center gap-2 bg-white rounded-md shadow-md">
                <div className="text-center">
                  <h2 className="text-xl font-semibold text-gray-600 mb-4">Schemes</h2>

                  <p className="text-gray-500 mb-2 text-2xl">-</p>
                </div>
              </div>

              <div className="flex flex-col items-center gap-2 bg-white rounded-md shadow-md">
                <div className="text-center">
                  <h2 className="text-xl font-semibold text-gray-600 mb-4">Providers</h2>

                  <p className="text-gray-500 mb-2 text-2xl">-</p>
                </div>
              </div>

              <div className="flex flex-col items-center gap-2 bg-white rounded-md shadow-md">
                <div className="text-center">
                  <h2 className="text-xl font-semibold text-gray-600 mb-4">Claims</h2>

                  <p className="text-gray-500 mb-2 text-2xl">-</p>
                </div>
              </div>
            </div>
          </section>
        </main>
      </div>
    </div>
  );
}
