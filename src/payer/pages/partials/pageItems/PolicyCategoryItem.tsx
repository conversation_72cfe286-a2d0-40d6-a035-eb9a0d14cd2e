import React, { useState } from "react";
import ModalBasic from "../../../components/ModalBasic";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { showPolicyCategoryPage } from "../../../store/policies/policyCategory/actions";
import ReactTooltip from "react-tooltip";

function PolicyCategoryItem(props) {
  const [payerEditModalOpen, setEditPayerModalOpen] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // <Link className="" to={"/policies/category/overview/" + props.id}>
  const handlePolicyCategoryItemClick = () => {
    dispatch(showPolicyCategoryPage(props));
    navigate("/policies/category/overview/" + props.categoryId);
  };

  console.log(props.id);

  const totalColor = (status) => {
    switch (status) {
      case "Paid":
        return "text-green-500";
      case "Due":
        return "text-yellow-500";
      case "Overdue":
        return "text-red-500";
      default:
        return "text-gray-500";
    }
  };

  const statusColor = (status) => {
    switch (status) {
      case "Paid":
        return "bg-green-100 text-green-600";
      case "Due":
        return "bg-yellow-100 text-yellow-600";
      case "Overdue":
        return "bg-red-100 text-red-500";
      default:
        return "bg-gray-100 text-gray-500";
    }
  };

  const typeIcon = (type) => {
    switch (type) {
      case "Subscription":
        return (
          <svg
            className="w-4 h-4 fill-current text-gray-400 shrink-0 mr-2"
            viewBox="0 0 16 16"
          >
            <path d="M4.3 4.5c1.9-1.9 5.1-1.9 7 0 .7.7 1.2 1.7 1.4 2.7l2-.3c-.2-1.5-.9-2.8-1.9-3.8C10.1.4 5.7.4 2.9 3.1L.7.9 0 7.3l6.4-.7-2.1-2.1zM15.6 8.7l-6.4.7 2.1 2.1c-1.9 1.9-5.1 1.9-7 0-.7-.7-1.2-1.7-1.4-2.7l-2 .3c.2 1.5.9 2.8 1.9 3.8 1.4 1.4 3.1 2 4.9 2 1.8 0 3.6-.7 4.9-2l2.2 2.2.8-6.4z" />
          </svg>
        );
      default:
        return (
          <svg
            className="w-4 h-4 fill-current text-gray-400 shrink-0 mr-2"
            viewBox="0 0 16 16"
          >
            <path d="M11.4 0L10 1.4l2 2H8.4c-2.8 0-5 2.2-5 5V12l-2-2L0 11.4l3.7 3.7c.*******.7.3.3 0 .5-.1.7-.3l3.7-3.7L7.4 10l-2 2V8.4c0-1.7 1.3-3 3-3H12l-2 2 1.4 1.4 3.7-3.7c.4-.4.4-1 0-1.4L11.4 0z" />
          </svg>
        );
    }
  };

  return (
    <tr>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className="font-medium text-gray-800">{props.name}</div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium text-gray-800`}>{props.description}</div>
      </td>
      <div className="flex mt-5 mb-5">
        <div className="text-sm text-gray-400 italic ml-2">
          {props.allowOtpVerificationFailOver ? "Enabled" : "Disabled"}
        </div>
      </div>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div
          className={`inline-flex font-medium rounded-full text-center px-2.5 py-0.5 text-sm text-green-700 border border-green-700`}
        >
          {props.restrictionType ? props.restrictionType : "NONE"}
        </div>
      </td>

      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div
          className={`inline-flex font-medium rounded-full text-center px-2.5 py-0.5`}
        >
          <div className="space-x-1">
            <button
              type="submit"
              data-tip
              data-for="adjustBtn"
              onClick={(e) => {
                e.stopPropagation();
                handlePolicyCategoryItemClick();
              }}
              className="text-gray-400 hover:text-gray-500 rounded-full"
            >
              <span className="sr-only">Adjust</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-blue-400 "
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                style={{ transform: "rotate(90deg)" }}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"
                />
              </svg>
            </button>
            <ReactTooltip
              id="adjustBtn"
              type="dark"
              textColor="#fff"
              backgroundColor="#449FDA"
              effect="float"
            >
              <span>Category Settings</span>
            </ReactTooltip>
          </div>
        </div>
      </td>

      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap w-px pr-10">
        <div className="space-x-1">
          <div></div>
        </div>
      </td>
    </tr>
  );
}

export default PolicyCategoryItem;
