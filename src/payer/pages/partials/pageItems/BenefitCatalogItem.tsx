import React, { useEffect, useState } from "react";
import ModalBasic from "../../../components/ModalBasic";
import { Link } from "react-router-dom";
import { getCatalogProviders, whitelistBenefitProvider } from "../../../store/catalog/actions";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import { connect } from "react-redux";
import ModalSmall from "../../../components/ModalSmall";
import ReactTooltip from "react-tooltip";

function BenefitCatalogItem(props) {
  const [benefitCatalogModalOpen, setbenefitCatalogModalOpen] = useState(false);
  const [facilitySelected, setFacilitySelected] = useState("");
  const dispatch = useDispatch();

  const handleBenefitItemClick = () => {
    setbenefitCatalogModalOpen(true);
  };
  const handleFacilityChange = (e) => {
    setFacilitySelected(e.target.value);
  };
  const handleWhitelistProviderSubmit = () => {
    const whitelistObject = {
      benefitId: props.id,
      providerId: facilitySelected,
    };
    setbenefitCatalogModalOpen(false);
    dispatch(whitelistBenefitProvider(whitelistObject));
  };
  const totalColor = (status) => {
    switch (status) {
      case "Paid":
        return "text-green-500";
      case "Due":
        return "text-yellow-500";
      case "Overdue":
        return "text-red-500";
      default:
        return "text-gray-500";
    }
  };

  const statusColor = (status) => {
    switch (status) {
      case "Paid":
        return "bg-green-100 text-green-600";
      case "Due":
        return "bg-yellow-100 text-yellow-600";
      case "Overdue":
        return "bg-red-100 text-red-500";
      default:
        return "bg-gray-100 text-gray-500";
    }
  };

  const typeIcon = (type) => {
    switch (type) {
      case "Subscription":
        return (
          <svg className="mr-2 h-4 w-4 shrink-0 fill-current text-gray-400" viewBox="0 0 16 16">
            <path d="M4.3 4.5c1.9-1.9 5.1-1.9 7 0 .7.7 1.2 1.7 1.4 2.7l2-.3c-.2-1.5-.9-2.8-1.9-3.8C10.1.4 5.7.4 2.9 3.1L.7.9 0 7.3l6.4-.7-2.1-2.1zM15.6 8.7l-6.4.7 2.1 2.1c-1.9 1.9-5.1 1.9-7 0-.7-.7-1.2-1.7-1.4-2.7l-2 .3c.2 1.5.9 2.8 1.9 3.8 1.4 1.4 3.1 2 4.9 2 1.8 0 3.6-.7 4.9-2l2.2 2.2.8-6.4z" />
          </svg>
        );
      default:
        return (
          <svg className="mr-2 h-4 w-4 shrink-0 fill-current text-gray-400" viewBox="0 0 16 16">
            <path d="M11.4 0L10 1.4l2 2H8.4c-2.8 0-5 2.2-5 5V12l-2-2L0 11.4l3.7 3.7c.*******.7.3.3 0 .5-.1.7-.3l3.7-3.7L7.4 10l-2 2V8.4c0-1.7 1.3-3 3-3H12l-2 2 1.4 1.4 3.7-3.7c.4-.4.4-1 0-1.4L11.4 0z" />
          </svg>
        );
    }
  };

  return (
    <tr className="even:bg-light-blue-50">
      <td className="text-wrap px-2 py-3 first:pl-5 last:pr-5">
        <div className="font-medium text-gray-800 ">{props.code}</div>
      </td>
      <td className="px-2 py-3 first:pl-5 last:pr-5 ">
        <div className={`text-ellipsis font-medium text-gray-800`}>{props.name}</div>
      </td>
      <td className="whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
        <div className={`font-medium text-gray-800`}>{props.serviceGroup}</div>
      </td>

      <td className="w-px whitespace-nowrap px-2 py-3 pr-10 first:pl-5 last:pr-5">
        <button
          type="submit"
          data-tip
          data-for="whitelistBenefit"
          onClick={(e) => {
            e.stopPropagation();
            handleBenefitItemClick();
          }}
          className="rounded-full text-gray-400 hover:text-gray-500"
        >
          <span className="sr-only">Adjustments</span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6 text-blue-400 "
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"
            />
          </svg>
        </button>
        <ReactTooltip
          id="whitelistBenefit"
          type="dark"
          textColor="#fff"
          backgroundColor="#449FDA"
          effect="solid"
        >
          <span>Whitelist Benefit To Provider</span>
        </ReactTooltip>
        <div className="space-x-1">
          <div>
            <ModalSmall
              id="feedback-modal"
              modalOpen={benefitCatalogModalOpen}
              setModalOpen={setbenefitCatalogModalOpen}
              title="Whitelist Benefit at Provider"
            >
              {/* Modal content */}
              <div className="px-10 pb-1 pt-4">
                <h3 className="mb-5 text-lg text-gray-400">PROVIDER LIST</h3>
                <div className="text-sm">
                  {/* Options */}
                  <div className="flex">
                    <div>
                      <label className="mb-1 block text-sm font-medium" htmlFor="Providers">
                        Select Provider Facility to WhiteList Benefit
                      </label>
                      <select
                        name="Providers"
                        id="Providers"
                        onChange={handleFacilityChange}
                        className="btn h-11 min-w-44 justify-between border-gray-200 bg-white text-lg text-gray-500 hover:border-gray-300 hover:text-gray-600"
                      >
                        {props.catelogProviders.map((provider) => (
                          <option value={provider.id}>{provider.name}</option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>
              </div>
              {/* Modal footer */}
              <div className="mt-5 px-5 py-4">
                <div className="flex flex-wrap justify-center space-x-2">
                  <button
                    className="btn-sm w-44 border-gray-200 px-16 py-2 text-gray-600 hover:border-gray-300"
                    onClick={(e) => {
                      e.stopPropagation();
                      setbenefitCatalogModalOpen(false);
                    }}
                  >
                    Clear
                  </button>
                  <button
                    className="btn-sm w-44 bg-blue-500 px-16 py-2 text-white hover:bg-blue-600"
                    onClick={(e) => {
                      e.stopPropagation();

                      handleWhitelistProviderSubmit();
                    }}
                  >
                    Save
                  </button>
                </div>
              </div>
            </ModalSmall>
          </div>
        </div>
      </td>
    </tr>
  );
}

export default BenefitCatalogItem;
