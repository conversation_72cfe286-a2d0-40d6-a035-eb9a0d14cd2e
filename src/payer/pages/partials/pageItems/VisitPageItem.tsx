import { useDispatch } from "react-redux";
import { useNavigate } from "react-router";
import UserService from "../../../services/UserService";
import { setActiveBillData } from "../../../store/members/actions";

function VisitPageItem(props: any) {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const handleBillBtnClick = (e: any, props: any) => {
    e.preventDefault();
    dispatch(setActiveBillData(props.visit));
    navigate(`/billing-page`);
  };

  const username = UserService.getUsername();
  const payerId = UserService.getPayer()?.tokenParsed?.['payerId'];

  return (
    <tr className="w-full">
      <td className="px-1 first:pl-5 last:pr-5 py-1 whitespace-nowrap">
        <div className="flex items-center text-slate-800">
          {" "}
          <div className="font-normal text-slate-800 text-center items-center px-1  ">
            {props.id}
          </div>
        </div>
      </td>
      <td className="px-1 first:pl-5 last:pr-5 py-1 whitespace-nowrap">
        <div
          className=" font-normal rounded-full text-left px-1    uppercase truncate  ..."
          title={props.createdAt}
        >
          {props.createdAt}
        </div>
      </td>

      <td className="px-1 first:pl-5 last:pr-5 py-1 whitespace-nowrap">
        <div className=" font-normal rounded-full text-left px-1   uppercase truncate  ...">
          {props.memberNumber}
        </div>
      </td>
      <td className="px-1 first:pl-5 last:pr-5 py-1 whitespace-nowrap">
        <div
          className=" font-normal rounded-full  text-left px-1   uppercase truncate  ..."
          title={props.memberName}
        >
          {props.memberName}
        </div>
      </td>
      <td className="px-1 first:pl-5 last:pr-5 py-1 whitespace-nowrap">
        <div
          className=" font-normal rounded-full  text-left px-1    uppercase truncate  ..."
          title={props.benefitName}
        >
          {props.benefitName}
        </div>
      </td>
      <td className="px-1 first:pl-5 last:pr-5 py-1 whitespace-nowrap">
        <div
          className=" font-normal rounded-full  text-left px-1 w-full  uppercase truncate  ..."
          title={props.providerName}
        >
          {props.providerName ? props.providerName : "None"}
        </div>
      </td>

      <td className="px-1 first:pl-5 last:pr-5 py-1 whitespace-nowrap">
        <div
          className=" font-normal rounded-full text-left px-1    uppercase truncate  ..."
          title={props.staffName}
        >
          {props.staffName}
        </div>
      </td>

      <td className="px-1 first:pl-5 last:pr-5 py-1 whitespace-nowrap w-px">
        <div className="flex items-center justify-evenly">
          {username.includes("gilbert.matui") && payerId == 1 ? (
            ""
          ) : (
            <button
              onClick={(e) => {
                handleBillBtnClick(e, props);
              }}
              className="mr-1 bg-transparent hover:bg-blue-700 text-blue-700 
              font-semibold hover:text-white py-1 px-1 border border-blue-500 hover:border-transparent rounded "
              title="Proceed To Bill Client"
            >
              Bill Visit
            </button>
          )}
        </div>
      </td>
    </tr>
  );
}
export default VisitPageItem;
