import React, { useEffect, useState } from "react";
import ModalBasic from "../../../components/ModalBasic";
import { Link, NavLink, useNavigate } from "react-router-dom";
import SchemesPoliciesTable from "./SchemesPoliciesTable";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import {
  getPoliciesInSchemes,
  showSchemePolicies,
} from "../../../store/schemes/actions";
import { connect } from "react-redux";
import ReactTooltip from "react-tooltip";
import SchemesPolicies from "../../schemes/SchemesPolicies";
import { truncate } from "~lib/utils";
import { formatValue } from "../../../lib/Utils";

interface ClaimsBatchingTableItemProps {
  type?: any;
  id?: any;
  invoice?: any;
  handleClick?: any;
}

const ClaimsBatchingTableItem: React.FC<ClaimsBatchingTableItemProps> = (props) => {
  const [showSchemePoliciesModalOpen, setShowSchemePoliciesModalOpen] =useState(false);
  const [selectedItems, setSelectedItems] = useState([]);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const handleSchemeItemClick = (id, name) => {
    console.log(id);
    dispatch(showSchemePolicies({ id, name }));
    navigate("/schemes/" + id + "/policies");
  };

  const totalColor = (status) => {
    switch (status) {
      case "Paid":
        return "text-green-500";
      case "Due":
        return "text-yellow-500";
      case "Overdue":
        return "text-red-500";
      default:
        return "text-gray-500";
    }
  };

  const statusColor = (status) => {
    switch (status) {
      case "Paid":
        return "bg-green-100 text-green-600";
      case "Due":
        return "bg-yellow-100 text-yellow-600";
      case "Overdue":
        return "bg-red-100 text-red-500";
      default:
        return "bg-gray-100 text-gray-500";
    }
  };

  const typeIcon = (type) => {
    switch (type) {
      case "Subscription":
        return (
          <svg
            className="w-4 h-4 fill-current text-gray-400 shrink-0 mr-2"
            viewBox="0 0 16 16"
          >
            <path d="M4.3 4.5c1.9-1.9 5.1-1.9 7 0 .7.7 1.2 1.7 1.4 2.7l2-.3c-.2-1.5-.9-2.8-1.9-3.8C10.1.4 5.7.4 2.9 3.1L.7.9 0 7.3l6.4-.7-2.1-2.1zM15.6 8.7l-6.4.7 2.1 2.1c-1.9 1.9-5.1 1.9-7 0-.7-.7-1.2-1.7-1.4-2.7l-2 .3c.2 1.5.9 2.8 1.9 3.8 1.4 1.4 3.1 2 4.9 2 1.8 0 3.6-.7 4.9-2l2.2 2.2.8-6.4z" />
          </svg>
        );
      default:
        return (
          <svg
            className="w-4 h-4 fill-current text-gray-400 shrink-0 mr-2"
            viewBox="0 0 16 16"
          >
            <path d="M11.4 0L10 1.4l2 2H8.4c-2.8 0-5 2.2-5 5V12l-2-2L0 11.4l3.7 3.7c.*******.7.3.3 0 .5-.1.7-.3l3.7-3.7L7.4 10l-2 2V8.4c0-1.7 1.3-3 3-3H12l-2 2 1.4 1.4 3.7-3.7c.4-.4.4-1 0-1.4L11.4 0z" />
          </svg>
        );
    }
  };

  return (
    <tr className=" border-b-2 border-gray-100" key={props.invoice?.visit_number}>
      <td><div className="flex items-center ml-[7px]">
          <label className="inline-flex">
            <span className="sr-only">Select</span>
            <input id={props.invoice?.id} className="form-checkbox" type="checkbox" onChange={props.handleClick} checked={props.isChecked} />
          </label>
        </div></td>
    <td className="py-1">
      <div className=" flex flex-col">
        <div className="flex text-xs whitespace-nowrap">
        <p className="text-gray-500" title={props.invoice.invoiceNumber}></p>{props.invoice.invoiceNumber}
        </div>
        
      </div>
    </td>
    <td className="py-1 ">
      <div className="flex flex-col">
        <div className="flex text-xs whitespace-nowrap">
        <p className="text-gray-500" title={props.invoice.totalAmount}></p>{formatValue(props.invoice.totalAmount)}
        </div>
        
      </div>
    </td>
    <td className="py-1 ">
      <div className="flex flex-col">
        <div className="flex text-xs whitespace-nowrap">
        <p className="text-gray-500" title={props.invoice.payableAmount}></p>{formatValue(props.invoice.payableAmount)}
        </div>
        
      </div>
    </td>
    <td className="py-1 ">
      <div className="flex flex-col  ">
        <div className="flex text-xs whitespace-nowrap">
        <p className="text-gray-500"></p>{props.invoice?.vetDate?.split(" ")[0]}
        </div>
        
        
      </div>
    </td>
    
    <td className="py-1 ">
      <div className="flex flex-col ">
        <div className="flex text-xs text-wrap w-9">
          {props.invoice?.providerName}
        </div>
      </div>
    </td>
    <td className="py-1 ">
      <div className="flex flex-col  justify-center">
        <div className="flex text-xs whitespace-nowrap items-center justify-center">
          
            <div className="flex items-center justify-center  rounded-full  bg-green-100 text-green-700 border-none px-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="w-2 h-2 mr-1"
              >
                <path
                  fill="text-yellow-700"
                  d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm3 10.5a.75.75 0 0 0 0-1.5H9a.75.75 0 0 0 0 1.5h6Z"
                  clip-rule="evenodd"
                />
              </svg>{" "}
              {props.invoice?.vettingStatus}
            </div>
        </div>
      </div>
    </td>
  </tr>
  );
};

const mapStateToProps = (state) => ({
  schemePolicies: state.schemes.schemePolicies,
});

const mapDispatchToProps = (dispatch) => ({
  getPoliciesInSchemes: (id) => dispatch(getPoliciesInSchemes(id)),
});

export default connect(mapStateToProps, mapDispatchToProps)(ClaimsBatchingTableItem);
