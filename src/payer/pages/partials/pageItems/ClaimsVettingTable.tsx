import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import ReactTooltip from "react-tooltip";
import UserService from "../../../services/UserService";
import { showSchemePolicies } from "../../../store/schemes/actions";
import SchemesTableItem from "./SchemesTableItem";
import ClaimsVettingTableItem from "./ClaimsVettingTableItem";

interface SchemesTableProps {
  visits?: any;
  payerId?: any;
}

const ClaimsVettingTable: React.FC<SchemesTableProps> = (props) => {
  const [selectAll, setSelectAll] = useState(false);
  const [isCheck, setIsCheck] = useState([]);
  const [list, setList] = useState([]);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [showSchemePoliciesModalOpen, setShowSchemePoliciesModalOpen] = useState(false);
  const filteredSchemesList = [];

  useEffect(() => {
    setList(props.visits);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSelectAll = () => {
    setSelectAll(!selectAll);
    setIsCheck(list.map((li) => li.id));
    if (selectAll) {
      setIsCheck([]);
    }
  };

  const handleClick = (e) => {
    const { id, checked } = e.target;
    setSelectAll(false);
    setIsCheck([...isCheck, id]);
    if (!checked) {
      setIsCheck(isCheck.filter((item) => item !== id));
    }
  };

  const handleSchemeItemClick = (id, name) => {
    dispatch(showSchemePolicies({ id, name }));
    navigate("/schemes/" + id + "/policies");
  };

  const username = UserService.getUsername();
  const payerId = UserService.getPayer().tokenParsed.payerId;

  ///const schemeId = UserService.getPayer().tokenParsed.schemeId;

  return (
    <div className="relative rounded-sm  border-gray-200  bg-white pl-2">
      <div>
        {/* Table */}
        <div className="overflow-x-auto">
          <table className="w-full table-auto">
            {/* Table header */}
            <thead className="border-b border-gray-200 text-[11px] font-semibold">
              <tr className="bg-gray-100">
                <th className=" py-4 text-left  uppercase">Scheme Details</th>
                <th className=" py-4  text-left uppercase">Member Details</th>
                <th className=" py-4  text-left uppercase">Visit Details</th>
                <th className=" py-4  text-left uppercase">Documentation</th>
                <th className=" py-4  text-left uppercase">Provider</th>
                <th className=" py-4  text-left uppercase">Vetting Status</th>
                <th className=" py-4  text-left uppercase">Action</th>
              </tr>
            </thead>
            {/* Table body */}
            <tbody className="divide-x-2 divide-y text-[11px] ">
              {list?.map((visit) => {
                return (
                  <ClaimsVettingTableItem key={Math.random() * 10000} id={visit.id} visit={visit} />
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ClaimsVettingTable;
