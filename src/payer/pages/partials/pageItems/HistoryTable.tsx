import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import HistoryPageItem from "./historyPageItem";

function HistoryTable({
  historylist,

  openTab,
}) {
  const [selectAll, setSelectAll] = useState(false);
  const [isCheck, setIsCheck] = useState([]);
  const [list, setList] = useState([]);

  useEffect(() => {
    setList(historylist);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSelectAll = () => {
    setSelectAll(!selectAll);
    setIsCheck(list.map((li) => li.id));
    if (selectAll) {
      setIsCheck([]);
    }
  };

  const handleClick = (e) => {
    const { id, checked } = e.target;
    setSelectAll(false);
    setIsCheck([...isCheck, id]);
    if (!checked) {
      setIsCheck(isCheck.filter((item) => item !== id));
    }
  };

  return (
    <div className="bg-white  rounded-sm   relative">
      <div className="">
        {/* Table */}
        <div className="">
          <table className="mx-auto table-fixed w-11/12">
            {/* Table header */}
            <thead className="text-xs font-semibold  text-gray-500   border-b border-sky-700">
              <tr>
                <th className="px-0 first:pl-5 last:pr-5 py-3 whitespace-nowrap  border-r-2 border-gray-50">
                  <div className="font-semibold text-left flex uppercase">
                    Visit Number
                  </div>
                </th>
                <th className="px-2  py-3 whitespace-nowrap border-r-2 justify-items-center border-gray-50">
                  <div className="font-semibold text-left  flex  uppercase">
                    Invoice Date
                  </div>
                </th>

                <th className="px-2  py-3 whitespace-nowrap border-r-2 justify-items-center border-gray-50">
                  <div className="font-semibold text-left  flex uppercase truncate  ...">
                    Member Number
                  </div>
                </th>
                <th className="px-2  py-3 whitespace-nowrap border-r-2 justify-items-center border-gray-50">
                  <div className="font-semibold text-left  flex uppercase">
                    Member Name
                  </div>
                </th>
                <th className="px-2  py-3 whitespace-nowrap border-r-2 justify-items-center border-gray-50">
                  <div className="font-semibold text-left  flex  uppercase">
                    Benefit Name
                  </div>
                </th>
                <th className="px-2  py-3 whitespace-nowrap border-r-2 justify-items-center border-gray-50">
                  <div className="font-semibold text-left  flex  uppercase">
                    Provider Name
                  </div>
                </th>
                <th className="px-2  py-3 whitespace-nowrap border-r-2 justify-items-center border-gray-50">
                  <div className="font-semibold text-left  flex  uppercase truncate  ...">
                    Invoice Number
                  </div>
                </th>

                <th className="px-2  py-3 whitespace-nowrap border-r-2 justify-items-center border-gray-50">
                  <div className="font-semibold text-left  flex  uppercase">
                    Total Invoice Amount
                  </div>
                </th>
              </tr>
            </thead>
            {/* Table body */}
            {list.map((visitsList) => {
              return (
                <HistoryPageItem
                  key={visitsList.id}
                  id={visitsList.id}
                  visitNumber={visitsList.id}
                  staffName={visitsList.staffName}
                  memberNumber={visitsList.memberNumber}
                  memberName={visitsList.memberName}
                  status={visitsList.status}
                  createdAt={visitsList.createdAt}
                  invoiceNumber={visitsList.invoiceNumber}
                  benefitName={visitsList.benefitName}
                  policyNumber={visitsList.policyNumber}
                  payerName={visitsList.payerName}
                  beneficiaryType={visitsList.beneficiaryType}
                  balance={visitsList.totalInvoiceAmount}
                  providerName={visitsList.providerName}
                  visit={visitsList}
                  setOpenTab={openTab}
                />
              );
            })}
          </table>
        </div>
      </div>
    </div>
  );
}

export default HistoryTable;
