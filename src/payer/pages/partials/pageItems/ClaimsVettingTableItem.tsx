import React, { useEffect, useState } from "react";
import ModalBasic from "../../../components/ModalBasic";
import { Link, NavLink, useNavigate } from "react-router-dom";
import SchemesPoliciesTable from "./SchemesPoliciesTable";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import { getPoliciesInSchemes, showSchemePolicies } from "../../../store/schemes/actions";
import { connect } from "react-redux";
import ReactTooltip from "react-tooltip";
import SchemesPolicies from "../../schemes/SchemesPolicies";
import { truncate } from "~lib/utils";

interface ClaimsVettingTableItemProps {
  type?: any;
  id?: any;
  visit?: any;
}

enum DocumentType {
  CLAIM = "CLAIM",
  INVOICE = "INVOICE",
  OTHER = "OTHER"
}

const hasDocumentType = (documents, type) => {
  return documents.some(doc => doc.type === type);
};

const ClaimsVettingTableItem: React.FC<ClaimsVettingTableItemProps> = (props) => {
  const [showSchemePoliciesModalOpen, setShowSchemePoliciesModalOpen] = useState(false);
  const [selectedItems, setSelectedItems] = useState([]);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  console.log(showSchemePoliciesModalOpen);

  const handleSchemeItemClick = (id, name) => {
    console.log(id);
    dispatch(showSchemePolicies({ id, name }));
    navigate("/schemes/" + id + "/policies");
  };

  
  return (
    <tr className=" border-b-2 border-gray-100" key={props.visit.id}>
      <td className="py-4">
        <div className=" flex flex-col">
          <div className="flex whitespace-nowrap text-sm text-xs">
            <p className="text-gray-500">Scheme :</p> {truncate(props.visit.schemeName, 20)}
          </div>
          <div className="flex whitespace-nowrap text-xs">
            <p className="text-gray-500"> Payer :</p> {props.visit.payerName}
          </div>
        </div>
      </td>
      <td className="py-4 ">
        <div className="flex flex-col ">
          <div className="flex whitespace-nowrap text-xs">
            <p className="text-gray-500" title={props.visit.memberName}>
              Name :
            </p>{" "}
            {truncate(props.visit.memberName, 10)}
          </div>
          <div className="flex whitespace-nowrap text-xs">
            <p className="text-gray-500">Member Number :</p> {props.visit.memberNumber}
          </div>
        </div>
      </td>
      <td className="py-4 ">
        <div className="flex flex-col">
          <div className="flex whitespace-nowrap text-xs">
            <p className="text-gray-500">Visit No : </p>
            {props.visit.id}
          </div>
          <div className="flex whitespace-nowrap text-xs">
            <p className="text-gray-500">Visit Date :</p>
            {props.visit.createdAt?.split(" ")[0]}
          </div>
          <div className="flex whitespace-nowrap text-xs">
            <p className="text-gray-500" title={props.visit.benefitName}>
              Benefit :
            </p>
            {truncate(props.visit.benefitName, 10)}
          </div>
          {/* <div className="flex text-xs whitespace-nowrap">
        <p className="text-gray-500">Invoice Count :</p>{props.visit?.invoices?.length}
        </div> */}
        </div>
      </td>
      {/* <td className="py-4 ">
      <div className="flex flex-col ">
        <div className="flex text-xs whitespace-nowrap">Diagnosis</div>
        <div className="flex text-xs whitespace-nowrap">Claim Form</div>
      </div>
    </td> */}
      <td className="py-4 ">
        <div className="flex flex-col ">
          <div className="flex items-center whitespace-nowrap text-xs">
            {
              hasDocumentType(props.visit.supportDocuments, DocumentType.INVOICE)?(
                <svg
                className="mr-2 h-4 w-4 rounded-full text-white"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                <g id="SVGRepo_iconCarrier">
                  {" "}
                  <path
                    d="M12 2C6.49 2 2 6.49 2 12C2 17.51 6.49 22 12 22C17.51 22 22 17.51 22 12C22 6.49 17.51 2 12 2ZM16.78 9.7L11.11 15.37C10.97 15.51 10.78 15.59 10.58 15.59C10.38 15.59 10.19 15.51 10.05 15.37L7.22 12.54C6.93 12.25 6.93 11.77 7.22 11.48C7.51 11.19 7.99 11.19 8.28 11.48L10.58 13.78L15.72 8.64C16.01 8.35 16.49 8.35 16.78 8.64C17.07 8.93 17.07 9.4 16.78 9.7Z"
                    fill="#16A34A"
                  ></path>{" "}
                </g>
              </svg>
              ):(
                <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                className="mr-2 h-[14px] w-[14px] rounded-full bg-red-600 text-white"
              >
                <path
                  fill="currentColor"
                  d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                />
              </svg>
              )
            }

            Invoice
          </div>
          <div className="flex items-center whitespace-nowrap text-xs">

            {
              hasDocumentType(props.visit.supportDocuments, DocumentType.CLAIM) ? (
                <svg
                className="mr-2 h-4 w-4 rounded-full text-white"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                <g id="SVGRepo_iconCarrier">
                  {" "}
                  <path
                    d="M12 2C6.49 2 2 6.49 2 12C2 17.51 6.49 22 12 22C17.51 22 22 17.51 22 12C22 6.49 17.51 2 12 2ZM16.78 9.7L11.11 15.37C10.97 15.51 10.78 15.59 10.58 15.59C10.38 15.59 10.19 15.51 10.05 15.37L7.22 12.54C6.93 12.25 6.93 11.77 7.22 11.48C7.51 11.19 7.99 11.19 8.28 11.48L10.58 13.78L15.72 8.64C16.01 8.35 16.49 8.35 16.78 8.64C17.07 8.93 17.07 9.4 16.78 9.7Z"
                    fill="#16A34A"
                  ></path>{" "}
                </g>
              </svg>
              ):(
                <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                className="mr-2 h-[14px] w-[14px] rounded-full bg-red-600 text-white"
              >
                <path
                  fill="currentColor"
                  d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                />
              </svg>
              )
            }

            Claim Form
          </div>
          <div className="flex items-center whitespace-nowrap text-xs overflow-hidden">
            {
              hasDocumentType(props.visit.supportDocuments,DocumentType.OTHER) ? (
                <svg
                className="mr-2 h-4 w-4 rounded-full text-white"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                <g id="SVGRepo_iconCarrier">
                  {" "}
                  <path
                    d="M12 2C6.49 2 2 6.49 2 12C2 17.51 6.49 22 12 22C17.51 22 22 17.51 22 12C22 6.49 17.51 2 12 2ZM16.78 9.7L11.11 15.37C10.97 15.51 10.78 15.59 10.58 15.59C10.38 15.59 10.19 15.51 10.05 15.37L7.22 12.54C6.93 12.25 6.93 11.77 7.22 11.48C7.51 11.19 7.99 11.19 8.28 11.48L10.58 13.78L15.72 8.64C16.01 8.35 16.49 8.35 16.78 8.64C17.07 8.93 17.07 9.4 16.78 9.7Z"
                    fill="#16A34A"
                  ></path>{" "}
                </g>
              </svg>
              ):(
                <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                className="mr-2 h-[14px] w-[14px] rounded-full bg-red-600 text-white"
              >
                <path
                  fill="currentColor"
                  d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                />
              </svg>             
              )
            }

            Other Documents
          </div>
        </div>
      </td>

      <td className="py-4 ">
        <div className="flex flex-col ">
          <div className="text-wrap flex w-9 text-xs">{props.visit.providerName}</div>
        </div>
      </td>
      <td className="py-4 ">
        <div className="flex flex-col justify-center">
          <div className="flex items-center justify-center whitespace-nowrap text-xs">
            <div className="flex items-center justify-center rounded-full bg-yellow-50 p-2 text-yellow-700">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="mr-1 h-2 w-2"
              >
                <path
                  fill="text-yellow-700"
                  d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm3 10.5a.75.75 0 0 0 0-1.5H9a.75.75 0 0 0 0 1.5h6Z"
                  clip-rule="evenodd"
                />
              </svg>{" "}
              {props.visit?.vettingStatus === null || props.visit?.vettingStatus === undefined
                ? "NEW"
                : props.visit?.vettingStatus}
            </div>
          </div>
        </div>
      </td>
      <td>
        <NavLink className="" to={`/visits/${props.visit.id}/vet`}>
          <button className="btn-sm bg-blue-500 px-2 py-1 text-white">View</button>
        </NavLink>
      </td>
    </tr>
  );
};

const mapStateToProps = (state) => ({
  schemePolicies: state.schemes.schemePolicies,
});

const mapDispatchToProps = (dispatch) => ({
  getPoliciesInSchemes: (id) => dispatch(getPoliciesInSchemes(id)),
});

export default connect(mapStateToProps, mapDispatchToProps)(ClaimsVettingTableItem);
