import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import { getCatalogProviders } from "../../../store/catalog/actions";
import BenefitCatalogItem from "./BenefitCatalogItem";
import PayerItem from "./PayersTableItem";

function BenefitCatalogTable({ benefitsList, catelogProviders }) {
  const [selectAll, setSelectAll] = useState(false);
  const [isCheck, setIsCheck] = useState([]);
  const [list, setList] = useState([]);
  const dispatch = useDispatch();

  useEffect(() => {
    setList(benefitsList);
  }, [benefitsList]);

  const handleSelectAll = () => {
    setSelectAll(!selectAll);
    setIsCheck(list.map((li) => li.id));
    if (selectAll) {
      setIsCheck([]);
    }
  };

  const handleClick = (e) => {
    const { id, checked } = e.target;
    setSelectAll(false);
    setIsCheck([...isCheck, id]);
    if (!checked) {
      setIsCheck(isCheck.filter((item) => item !== id));
    }
  };

  return (
    <div className="relative  rounded-sm  border-gray-200 bg-white">
      <div className="pb-5">
        {/* Table */}
        <div className="overflow-x-auto">
          <table className="w-full table-auto">
            {/* Table header */}
            <thead className="border-b border-gray-200  text-xs   font-semibold text-gray-500">
              <tr>
                <th className="whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
                  <div className="text-left font-semibold">Code</div>
                </th>
                <th className="whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
                  <div className="text-left font-semibold">Name</div>
                </th>
                <th className="whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
                  <div className="text-left font-semibold">Service Group</div>
                </th>

                <th className="whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
                  <div className="text-left font-semibold">Actions</div>
                </th>
              </tr>
            </thead>
            {/* Table body */}
            <tbody className="divide-y divide-none text-sm ">
              {list.map((catalogItem) => {
                return (
                  <BenefitCatalogItem
                    key={catalogItem.id}
                    id={catalogItem.id}
                    name={catalogItem.name}
                    code={catalogItem.code}
                    catelogProviders={catelogProviders}
                    serviceGroup={catalogItem.serviceGroup}
                    handleClick={handleClick}
                    isChecked={isCheck.includes(catalogItem.id)}
                  />
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default BenefitCatalogTable;
