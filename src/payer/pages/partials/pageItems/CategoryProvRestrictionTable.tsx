import React, { useState, useEffect, FC } from "react";
import CategoryBenefitItem from "./CategoryBenefitItem";
import CategoryProvExclusionItem from "./CategoryProvExclusionItem";
import MappingTableItem from "./MappingItem";
import PayerItem from "./PayerItem";
import CategoryProvRestrictionItem from "./CategoryProvRestrictionItem";
import { getPolicyCategoryProviderRestriction } from "../../../store/policies/policyCategory/actions";
import { connect, useDispatch } from "react-redux";
import * as notifications from "../../../lib/notifications.js";

interface Props {
  categoryProviderRestrictions: any[];
}
export const CategoryProvRestrictionTable: FC<Props> = (props) => {
  const [selectAll, setSelectAll] = useState(false);
  const [isCheck, setIsCheck] = useState([]);
  const [list, setList] = useState([]);
  const dispatch = useDispatch();

  useEffect(() => {
    setList(props.categoryProviderRestrictions);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleClick = (id) => {
    const restrictionObj = {
      restrictionId: id,
    };
    ///debugger;
    // props.removeRestriction(restrictionObj).then(() => {
    //   notifications.Success({
    //     title: "Restriction Removed Successfully",
    //   });

    //   let policyCategoryId = window.location.pathname.split(
    //     "/policies/category/overview/"
    //   )[1];

    //   dispatch(getPolicyCategoryProviderRestriction(policyCategoryId));
    // });
  };

  return (
    <div className="bg-white  rounded-sm   relative">
      <div>
        {/* Table */}
        <div className="overflow-x-auto">
          <table className="table-auto w-full">
            {/* Table header */}
            <thead className="text-md font-semibold    border-b border-gray-200">
              <tr>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">Name</div>
                </th>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">Payer</div>
                </th>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">Restriction</div>
                </th>
                {/* <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">Action</div>
                </th> */}
              </tr>
            </thead>
            {/* Table body */}
            <tbody className="text-sm  divide-none ">
              {list.map((restriction, index) => {
                return (
                  <CategoryProvRestrictionItem
                    key={index}
                    id={restriction.id}
                    name={restriction?.provider?.name}
                    payer={restriction?.payer?.name}
                    restrictionType={restriction.category?.restrictionType}
                    handleClick={() => handleClick(restriction.id)}
                  />
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

const mapDispatchToProps = (dispatch) => ({});
export default connect(null, mapDispatchToProps)(CategoryProvRestrictionTable);
