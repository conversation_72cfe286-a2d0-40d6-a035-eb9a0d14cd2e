import React from "react";

interface BatchBtnProps {
  selectedItemsBtn?: any;
  handleBatchClick?: any;
  visit?: any;
  handleClick?: any;
  isChecked?: any;
  isAllSelected:boolean;
  selectAllItems:number;
}

const BatchButton: React.FC<BatchBtnProps> = (props) => {
  return (
    <div className={`${props.selectedItemsBtn?.length < 1 ? "hidden" : "flex items-center justify-between"}`}>
      <div> {props.selectedItemsBtn?.length}  selected</div>
      <div className="btn-sm  bg-blue-700 text-white hover:cursor-pointer" onClick={props.handleBatchClick}>
          Batch Claims
        </div>
    </div>
  );
};

export default BatchButton;
