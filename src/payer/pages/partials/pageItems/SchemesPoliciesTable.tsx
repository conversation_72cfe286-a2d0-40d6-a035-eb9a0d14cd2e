import React, { useState, useEffect } from "react";
import { getPoliciesInSchemes } from "../../../store/schemes/actions";
import SchemesPoliciesTableItem from "./SchemesPoliciesTableItem";
import SchemesTableItem from "./SchemesTableItem";
import { connect, useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";

function SchemesPoliciesTable({ schemePolicies }) {
  const [selectAll, setSelectAll] = useState(false);
  const [isCheck, setIsCheck] = useState([]);
  const [list, setList] = useState([]);
  const [refreshKey, setRefreshKey] = useState(0);
  const dispatch = useDispatch();

  console.log(schemePolicies);

  const handleSelectAll = () => {
    setSelectAll(!selectAll);
    setIsCheck(list.map((li) => li.id));
    if (selectAll) {
      setIsCheck([]);
    }
  };

  const handleClick = (e) => {
    const { id, checked } = e.target;
    setSelectAll(false);
    setIsCheck([...isCheck, id]);
    if (!checked) {
      setIsCheck(isCheck.filter((item) => item !== id));
    }
  };

  return (
    <div className="bg-white  rounded-sm  border-gray-200 relative">
      <div>
        {/* Table */}
        <div className="overflow-x-auto">
          <table className="table-auto w-full">
            {/* Table header */}
            <thead className="text-xs font-semibold border-b border-gray-200">
              <tr>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap w-px">
                  <div className="flex items-center"></div>
                </th>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">Policy Number</div>
                </th>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">Start Date</div>
                </th>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">End Date</div>
                </th>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">Action</div>
                </th>
              </tr>
            </thead>
            {/* Table body */}
            <tbody className="text-sm divide-y divide-none ">
              {schemePolicies.map((schemePolicy) => {
                return (
                  <SchemesPoliciesTableItem
                    key={schemePolicy.id}
                    id={schemePolicy.id}
                    name={schemePolicy.plan.name}
                    type={schemePolicy.plan.type}
                    startDate={schemePolicy.startDate}
                    endDate={schemePolicy.endDate}
                    policyNumber={schemePolicy.policyNumber}
                    handleClick={handleClick}
                    isChecked={isCheck.includes(schemePolicy.id)}
                  />
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default SchemesPoliciesTable;
