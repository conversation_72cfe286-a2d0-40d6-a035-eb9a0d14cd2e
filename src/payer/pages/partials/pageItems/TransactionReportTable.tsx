import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";

import TransactionPageItem from "../../claims/reports/transactionPageItem";

function TransactionReportTable({
  openTab,
  filteredlist,
  providerId,
  showResult,
}) {
  const [selectAll, setSelectAll] = useState(false);
  const [isCheck, setIsCheck] = useState([]);
  const [list, setList] = useState([]);
  const [showData, setShowData] = useState(false);

  useEffect(() => {
    setList(filteredlist);
    setShowData(showResult);
    console.log(showResult);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filteredlist]);

  console.log(list);

  return (
    <div className="flex min-w-fit bg-white shadow-md px-5 justify-center mx-10 rounded-md">
      <table className="">
        {/* Table header */}
        <thead
          className={`text-xs font-semibold  text-gray-500 bg-gray-50  border-b border-sky-700 ${
            list?.length > 0 ? "visible sticky top-14" : "hidden"
          } `}
        >
          <tr>
            <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap mx-5 border-r-2 border-gray-50">
              <div className="font-semibold text-left flex uppercase">
                Visit Number
              </div>
            </th>
            <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap mx-5 border-r-2 border-gray-50">
              <div className="font-semibold text-left flex uppercase">Date</div>
            </th>

            <th className="px-2  py-3 whitespace-nowrap border-r-2 justify-items-center border-gray-50">
              <div className="font-semibold text-left  flex uppercase">
                Member Number
              </div>
            </th>
            <th className="px-1  py-3 whitespace-nowrap border-r-2 justify-items-center border-gray-50">
              <div className="font-semibold text-left  flex uppercase">
                Member Name
              </div>
            </th>
            <th className="px-1  py-3 whitespace-nowrap border-r-2 justify-items-center border-gray-50">
              <div className="font-semibold text-left  flex uppercase">
                Payer
              </div>
            </th>

            <th className="px-1  py-3 whitespace-nowrap border-r-2 justify-items-center border-gray-50">
              <div className="font-semibold text-left  flex  uppercase">
                Benefit Name
              </div>
            </th>
            <th className="px-1  py-3 whitespace-nowrap border-r-2 justify-items-center border-gray-50">
              <div className="font-semibold text-left  flex  uppercase">
                Invoice Number
              </div>
            </th>
            <th className="px-1  py-3 whitespace-nowrap border-r-2 justify-items-center border-gray-50">
              <div className="font-semibold text-left  flex  uppercase">
                Amount
              </div>
            </th>
            <th className="px-1  py-3 whitespace-nowrap border-r-2 justify-items-center border-gray-50">
              <div className="font-semibold text-left  flex  uppercase">
                Action
              </div>
            </th>
          </tr>
        </thead>

        {/* Table body */}
        {list &&
          list.map((visitsList, index) => {
            return (
              <TransactionPageItem
                key={index}
                memberNumber={visitsList.memberNumber}
                visitNumber={visitsList.visitNumber}
                memberName={visitsList.memberName}
                payerName={visitsList.payerName}
                createdAt={visitsList.createdAt}
                invoiceNumber={visitsList.invoiceNumber}
                invoiceAmount={visitsList.invoiceAmount}
                hospitalProviderId={visitsList.hospitalProviderId}
                benefitName={visitsList.benefitName}
                visit={visitsList}
                providerId={providerId}
                setOpenTab={openTab}
              />
            );
          })}
      </table>
      <div
        className={`${
          list === null && list.length < 1
            ? "flex visible text-red-600 font-medium mx-auto "
            : "hidden"
        }`}
      >
        {" "}
        No Transaction(s) for Selected Filter
      </div>
    </div>
  );
}

export default TransactionReportTable;
