import React, { useEffect, FC, useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router";

import { api_url_visit, formatValue } from "../../../lib/Utils";

interface Props {
  id?: any;
  visitNumber?: any;
  staffName?: any;
  memberNumber?: any;
  memberName?: any;
  status?: any;
  createdAt?: any;
  invoiceNumber?: any;
  benefitName?: any;
  policyNumber?: any;
  payerName?: any;
  beneficiaryType?: any;
  balance?: any;
  visit?: any;
  setTransactionPanelOpen?: any;
  setOpenTab?: any;
  providerName?: any;
}
export const HistoryPageItem: FC<Props> = (props) => {
  const [payerEditModalOpen, setEditPayerModalOpen] = useState(false);
  const navigate = useNavigate();
  const [open, setOpen] = useState(false);
  const dispatch = useDispatch();

  const [isLineItemModalOpen, setIsLineItemModalOpen] = useState(false);
  const [isDiagnosisModalOpen, setIsDiagnosisModalOpen] = useState(false);
  const [transactionPanelOpen, setTransactionPanelOpen] = useState(true);

  const handleMoreBtnClick = (e) => {
    setOpen(!open);
    e.stopPropagation();
  };
  const handleAddLineItems = (e) => {
    setIsLineItemModalOpen(true);
    e.stopPropagation();
  };

  return (
    <tbody
      className="divide-y-0 even:bg-light-blue-50 
    hover:bg-light-blue-100 hover:text-sky-700  delay-150 "
    >
      <tr className="cursor-pointer w-full" key={props.visitNumber}>
        <td className="px-1 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
          <div className="flex items-center text-slate-800" title={props.id}>
            {" "}
            <div className="font-normal text-slate-800 text-center items-center px-1 ">
              {props.id}
            </div>
          </div>
        </td>
        <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
          <div
            className=" font-normal rounded-full text-left uppercase px-1 py-0.5 truncate  ..."
            title={props.createdAt}
          >
            {props.createdAt}
          </div>
        </td>
        <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
          <div
            className=" font-normal rounded-full text-left uppercase px-1 py-0.5 truncate  ..."
            title={props.memberNumber}
          >
            {props.memberNumber}
          </div>
        </td>
        <td
          className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap "
          title={props.memberName}
        >
          <div className=" font-normal rounded-full  text-left uppercase px-1 py-0.5 truncate  ...">
            {props.memberName}
          </div>
        </td>

        <td
          className="px-1 first:pl-5 last:pr-2 py-3 whitespace-nowrap"
          title={props.benefitName}
        >
          <div className=" font-normal rounded-full text-left uppercase px-1 py-0.5 text-ellipsis truncate  ...">
            {props.benefitName}
          </div>
        </td>
        <td
          className="px-1 first:pl-5 last:pr-2 py-3 whitespace-nowrap"
          title={props.providerName}
        >
          <div className=" font-normal rounded-full text-left uppercase px-1 py-0.5 truncate  ...">
            {props.providerName}
          </div>
        </td>
        <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
          <div
            className=" font-normal rounded-full text-left uppercase px-1 py-0.5 truncate  ..."
            title={props.invoiceNumber}
          >
            {props.invoiceNumber}
          </div>
        </td>
        <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
          <div className=" font-normal rounded-full text-left uppercase px-1 py-0.5">
            {formatValue(props.balance)}
          </div>
        </td>
        <td>
          {/* {props.status === "LINE_ITEMS_ADDED" ||
          props.status === "CANCELLED" ? (
            <button
              className={`text-slate-400 btn hover:text-slate-500 transform ml-2 flex  border-none`}
              disabled
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth="2"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M4 6h16M4 12h16M4 18h7"
                />
              </svg>
              <span className="sr-only">Add Line Items</span>
              <p className="text-gray-700">Add Line Items</p>
            </button>
          ) : (
            <button
              className={`text-slate-400 btn hover:text-slate-500 transform ml-2 flex  border-none`}
              onClick={(e) => handleAddLineItems(e)}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth="2"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M4 6h16M4 12h16M4 18h7"
                />
              </svg>
              <span className="sr-only">Add Line Items</span>
              <p className="text-blue-700">Add Line Items</p>
            </button>
          )} */}
        </td>
      </tr>

      <tr>
        {/* <ModalBasic
          id="line-item"
          modalOpen={isLineItemModalOpen}
          onClose={() => setIsLineItemModalOpen(false)}
          title="Add Invoice Lines"
        >
          <HistoryLineItems
            open={isLineItemModalOpen}
            onClose={() => setIsLineItemModalOpen(false)}
            historyItem={props}
            handleSaveLine={(obj) => handleSaveLine(obj)}
            handleSaveDocument={(obj) => handleSaveDocument(obj)}
          />
        </ModalBasic> */}
      </tr>
    </tbody>
  );
};
export default HistoryPageItem;
