import React, { useState, useEffect } from "react";
import PolicyCategoryItem from "./PolicyCategoryItem";
import PolicyTableItem from "./PolicyItem";

function PolicyCategoriesTable({
  selectedItems,
  policyCategories,
  policy,
  refresh,
}) {
  const [selectAll, setSelectAll] = useState(false);
  const [isCheck, setIsCheck] = useState([]);
  const [list, setList] = useState([]);

  useEffect(() => {
    setList(policyCategories);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSelectAll = () => {
    setSelectAll(!selectAll);
    setIsCheck(list.map((li) => li.id));
    if (selectAll) {
      setIsCheck([]);
    }
  };

  const handleClick = (e) => {
    const { id, checked } = e.target;
    setSelectAll(false);
    setIsCheck([...isCheck, id]);
    if (!checked) {
      setIsCheck(isCheck.filter((item) => item !== id));
    }
  };

  useEffect(() => {
    selectedItems(isCheck);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isCheck]);

  const res = !list ? "Loaind" : "dfsd";
  console.log(res);

  return (
    <div className="bg-white  rounded-sm   relative">
      <div>
        {/* Table */}
        <div className="overflow-x-auto">
          <table className="table-auto w-full">
            {/* Table header */}
            <thead className="text-md font-normal text-gray-500    border-b border-gray-200">
              <tr>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">Category</div>
                </th>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">
                    Category Description
                  </div>
                </th>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">OTP Failover</div>
                </th>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">Restriction</div>
                </th>

                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">Actions</div>
                </th>
              </tr>
            </thead>
            {/* Table body */}
            <tbody className="text-sm divide-y divide-gray-200 ">
              {list.map((pc, index) => {
                return (
                  <>
                    <PolicyCategoryItem
                      key={index}
                      categoryId={pc.id}
                      name={pc.name}
                      restrictionType={pc.restrictionType}
                      allowOtpVerificationFailOver={
                        pc.allowOtpVerificationFailOver
                      }
                      refresh={refresh}
                      description={pc.description}
                      isChecked={isCheck.includes(pc.id)}
                      policy={policy}
                    />
                  </>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default PolicyCategoriesTable;
