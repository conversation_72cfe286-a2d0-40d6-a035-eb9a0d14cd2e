import React, { useState, useEffect } from "react";
import CategoryBenefitItem from "./CategoryBenefitItem";
import MappingTableItem from "./MappingItem";
import PayerItem from "./PayerItem";

function CategoryBenefitTable({ selectedItems, categoryBenefits }) {
  const [selectAll, setSelectAll] = useState(false);
  const [isCheck, setIsCheck] = useState([]);
  const [list, setList] = useState([]);

  useEffect(() => {
    setList(categoryBenefits);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSelectAll = () => {
    setSelectAll(!selectAll);
    setIsCheck(list.map((li) => li.id));
    if (selectAll) {
      setIsCheck([]);
    }
  };

  const handleClick = (e) => {
    const { id, checked } = e.target;
    setSelectAll(false);
    setIsCheck([...isCheck, id]);
    if (!checked) {
      setIsCheck(isCheck.filter((item) => item !== id));
    }
  };

  return (
    <div className="bg-white  rounded-sm   relative">
      <div>
        {/* Table */}
        <div className="overflow-x-auto">
          <table className="table-auto w-full">
            {/* Table header */}
            <thead className="text-md font-semibold    border-b border-gray-200">
              <tr>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">Name</div>
                </th>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">Limit</div>
                </th>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">Payer</div>
                </th>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">Sharing</div>
                </th>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">Co-Payment</div>
                </th>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">PreAuth</div>
                </th>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">Processed</div>
                </th>
                {/* <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">Action</div>
                </th> */}
              </tr>
            </thead>
            {/* Table body */}
            <tbody className="text-sm  divide-none ">
              {list.map((categoryBenefit) => {
                return (
                  <CategoryBenefitItem
                    key={categoryBenefit.id}
                    id={categoryBenefit.id}
                    name={categoryBenefit.name}
                    payer={categoryBenefit.payer.name}
                    limit={categoryBenefit.limit}
                    sharing={categoryBenefit.sharing}
                    copay={categoryBenefit.coPaymentRequired}
                    preauth={categoryBenefit.preAuthType}
                    processed={categoryBenefit.processed}
                    handleClick={handleClick}
                    isChecked={isCheck.includes(categoryBenefit.id)}
                  />
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default CategoryBenefitTable;
