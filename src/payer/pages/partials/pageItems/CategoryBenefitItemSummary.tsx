import React, { useState } from "react";
import ModalBasic from "../../../components/ModalBasic";
import { Link } from "react-router-dom";
import { formatValue } from "../../../lib/Utils";

function CategoryBenefitItemSummary(props) {
  const [payerEditModalOpen, setEditPayerModalOpen] = useState(false);
  console.log(props.parentBenefit);
  const totalColor = (status) => {
    switch (status) {
      case "Paid":
        return "text-green-500";
      case "Due":
        return "text-yellow-500";
      case "Overdue":
        return "text-red-500";
      default:
        return "text-gray-500";
    }
  };

  const statusColor = (status) => {
    switch (status) {
      case true:
        return "p-1 py-0.5 px-2.5 text-center inline-flex rounded-full border border-green-500 bg-green-100 text-green-600";
      case "Due":
        return "bg-yellow-100 text-yellow-600";
      case false:
        return " p-1 py-0.5 px-2.5 text-center inline-flex rounded-full border border-red-500  bg-red-50 text-red-500";
      default:
        return "bg-gray-100 text-gray-500";
    }
  };

  return (
    <tr className="even:bg-light-blue-50">
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap ">
        <div className="font-medium overflow-hidden truncate w-72">
          {props.name}
        </div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium `}>{formatValue(props.limit)}</div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium `}>{props.payer}</div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium `}>{props.sharing}</div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium `}>
          {props.copay === false ? "NONE" : "TRUE"}
        </div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium `}>{props.preauth}</div>
      </td>

      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium `}>{props.applicableGender}</div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium `}>{props.coPaymentAmount}</div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium `}>
          {props.parentBenefit === null ? "NONE" : props.parentBenefit.name}
        </div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium `}>{props.waitingPeriod}</div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium `}>{props.suspensionThreshold}</div>
      </td>

      <td className="px-2 first:pl-5  py-3 whitespace-nowrap">
        {/* <div
          className={`inline-flex font-medium rounded-full text-center px-2.5 py-0.5`}
        >
          <div className="space-x-1">
            <button
              className="text-gray-400 hover:text-gray-500 rounded-full"
              onClick={(e) => {
                e.stopPropagation();
                // setEditPayerModalOpen(true);
              }}
            >
              <span className="sr-only">Remove</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-blue-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
            </button>
            <Link className="" to="#">
              <button className="text-gray-400 hover:text-gray-500 rounded-full">
                <span className="sr-only">Adjust</span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6 text-blue-400 "
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  style={{ transform: "rotate(90deg)" }}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"
                  />
                </svg>
              </button>
            </Link>
          </div>
        </div> */}
      </td>

      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap w-px pr-10">
        <div className="space-x-1"></div>
      </td>
    </tr>
  );
}

export default CategoryBenefitItemSummary;
