import React, { useState } from "react";
import ModalBasic from "../../../components/ModalBasic";
import { Link } from "react-router-dom";
import { connect, useDispatch } from "react-redux";

function CategoryProvRestrictionItem(props) {
  const [payerEditModalOpen, setEditPayerModalOpen] = useState(false);
  const dispatch = useDispatch;

  return (
    <tr className="even:bg-light-blue-50" key={props.id}>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className="font-medium">{props.name}</div>
      </td>

      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium `}>{props.payer}</div>
      </td>

      <td className="pl-2">
        <div
          className={`inline-flex font-medium rounded-full text-center px-2.5 py-0.5 text-sm text-green-700 border border-green-700`}
        >
          {props.restrictionType || "NONE"}
        </div>
      </td>

      <td className="px-2 first:pl-5  py-3 whitespace-nowrap">
        <div
          className={`inline-flex font-medium rounded-full text-center px-2.5 py-0.5`}
        >
          <div className="space-x-1">
            {/* <button
              className="btntext-gray-400 hover:text-gray-500 rounded-full"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                props.handleClick(props.id);
              }}
            >
              <span className="sr-only">Remove Restriction</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-blue-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                />
              </svg>
            </button> */}
            {/* <Link className="" to="#">
              <button className="text-gray-400 hover:text-gray-500 rounded-full">
                <span className="sr-only">Adjust</span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6 text-blue-400 "
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  style={{ transform: "rotate(90deg)" }}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"
                  />
                </svg>
              </button>
            </Link> */}
          </div>
        </div>
      </td>

      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap w-px pr-10">
        <div className="space-x-1">
          <div></div>
        </div>
      </td>
    </tr>
  );
}

export default CategoryProvRestrictionItem;
