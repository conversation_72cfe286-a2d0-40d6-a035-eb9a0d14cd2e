import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import ReactTooltip from "react-tooltip";
import UserService from "../../../services/UserService";
import { showSchemePolicies } from "../../../store/schemes/actions";
import SchemesTableItem from "./SchemesTableItem";

interface SchemesTableProps {
  selectedItems?: any;
  schemes?: any;
  payerId?: any;
}

const SchemesTable: React.FC<SchemesTableProps> = (props) => {
  const [selectAll, setSelectAll] = useState(false);
  const [isCheck, setIsCheck] = useState([]);
  const [list, setList] = useState([]);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [showSchemePoliciesModalOpen, setShowSchemePoliciesModalOpen] = useState(false);
  const filteredSchemesList = [];

  useEffect(() => {
    setList(props.schemes);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSelectAll = () => {
    setSelectAll(!selectAll);
    setIsCheck(list.map((li) => li.id));
    if (selectAll) {
      setIsCheck([]);
    }
  };

  const handleClick = (e) => {
    const { id, checked } = e.target;
    setSelectAll(false);
    setIsCheck([...isCheck, id]);
    if (!checked) {
      setIsCheck(isCheck.filter((item) => item !== id));
    }
  };

  const handleSchemeItemClick = (id, name) => {
    dispatch(showSchemePolicies({ id, name }));
    navigate("/schemes/" + id + "/policies");
  };

  useEffect(() => {
    props.selectedItems(isCheck);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isCheck]);

  const username = UserService.getUsername();
  const payerId = UserService.getPayer().tokenParsed.payerId;

  const schemeId = UserService.getPayer().tokenParsed.schemeId;

  return (
    <div className="relative  rounded-sm  border-gray-200 bg-white">
      <div>
        {/* Table */}
        <div className="overflow-x-auto">
          <table className="w-full table-auto">
            {/* Table header */}
            <thead className="border-b border-gray-200 text-xs font-semibold">
              <tr>
                <th className="w-px whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
                  <div className="flex items-center"></div>
                </th>
                <th className="whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
                  <div className="text-left font-semibold">Plan Name</div>
                </th>
                <th className="whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
                  <div className="text-left font-semibold">Type</div>
                </th>
                <th className="whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
                  <div className="text-left font-semibold">Access Mode</div>
                </th>
                <th className="whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
                  <div className="text-left font-semibold">Action</div>
                </th>
              </tr>
            </thead>
            {/* Table body */}
            <tbody className="divide-y divide-none text-sm ">
              {list?.map((scheme) => {
                if (UserService.hasRole(["HR_LVL_1"]) && scheme.id === schemeId) {
                  const judiciary = list.filter(function (sch) {
                    return sch.id === schemeId;
                  });
                  console.log(judiciary);
                  return (
                    <SchemesTableItem
                      key={judiciary[0].id}
                      id={judiciary[0].id}
                      name={judiciary[0].name}
                      type={judiciary[0].type}
                      accessMode={judiciary[0].accessMode}
                    />
                  );
                }
              })}

              {UserService.hasRole(["HR_LVL_1"])
                ? ""
                : list?.map((scheme) => {
                    return (
                      <SchemesTableItem
                        key={scheme.id}
                        id={scheme.id}
                        name={scheme.name}
                        type={scheme.type}
                        accessMode={scheme.accessMode}
                      />
                    );
                  })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default SchemesTable;
