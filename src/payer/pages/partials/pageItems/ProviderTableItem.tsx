import React, { useEffect, useState } from "react";
import ModalBasic from "../../../components/ModalBasic";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { showPayer } from "../../../store/payers/actions";
import { connect } from "react-redux";
import { RootState } from "../../../store";
import { showProvider } from "../../../store/providers/actions";
import ReactTooltip from "react-tooltip";

function ProviderTableItem(props) {
  const [payerEditModalOpen, setEditPayerModalOpen] = useState(false);
  const [payerEditProviderCashierModalOpen, setEditProviderCashierModalOpen] =
    useState(false);
  const [cashierName, setCashierName] = useState("");
  const [cashierEmail, setCashierEmail] = useState("");
  const [cashierPassword, setCashierPassword] = useState("");

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const totalColor = (status) => {
    switch (status) {
      case "Paid":
        return "text-green-500";
      case "Due":
        return "text-yellow-500";
      case "Overdue":
        return "text-red-500";
      default:
        return "text-gray-500";
    }
  };
  const handleProviderItemClick = () => {
    console.log(props);
    dispatch(showProvider(props));
    navigate("/provider/overview/" + props.id);
  };

  const statusColor = (status) => {
    switch (status) {
      case "Paid":
        return "bg-green-100 text-green-600";
      case "Due":
        return "bg-yellow-100 text-yellow-600";
      case "Overdue":
        return "bg-red-100 text-red-500";
      default:
        return "bg-gray-100 text-gray-500";
    }
  };

  const typeIcon = (type) => {
    switch (type) {
      case "Subscription":
        return (
          <svg
            className="w-4 h-4 fill-current text-gray-400 shrink-0 mr-2"
            viewBox="0 0 16 16"
          >
            <path d="M4.3 4.5c1.9-1.9 5.1-1.9 7 0 .7.7 1.2 1.7 1.4 2.7l2-.3c-.2-1.5-.9-2.8-1.9-3.8C10.1.4 5.7.4 2.9 3.1L.7.9 0 7.3l6.4-.7-2.1-2.1zM15.6 8.7l-6.4.7 2.1 2.1c-1.9 1.9-5.1 1.9-7 0-.7-.7-1.2-1.7-1.4-2.7l-2 .3c.2 1.5.9 2.8 1.9 3.8 1.4 1.4 3.1 2 4.9 2 1.8 0 3.6-.7 4.9-2l2.2 2.2.8-6.4z" />
          </svg>
        );
      default:
        return (
          <svg
            className="w-4 h-4 fill-current text-gray-400 shrink-0 mr-2"
            viewBox="0 0 16 16"
          >
            <path d="M11.4 0L10 1.4l2 2H8.4c-2.8 0-5 2.2-5 5V12l-2-2L0 11.4l3.7 3.7c.*******.7.3.3 0 .5-.1.7-.3l3.7-3.7L7.4 10l-2 2V8.4c0-1.7 1.3-3 3-3H12l-2 2 1.4 1.4 3.7-3.7c.4-.4.4-1 0-1.4L11.4 0z" />
          </svg>
        );
    }
  };

  const handleAddCashier = (event) => {
    event.preventDefault();
    const cashierObj = {
      cashierName: cashierName,
      cashierEmail: cashierEmail,
      cashierPassword: cashierPassword,
    };
    console.log(cashierObj);

    // whitelistMultipleProviderBenefit(provObj).then(() => {
    //   setBenefitsSubmitted(true);
    //   getProviderWhitelist(providerUrlId).then(() => {
    //     setRefreshWhitelist((oldKey) => oldKey + 1);
    //   });
    // });
  };

  return (
    <tr className="even:bg-light-blue-50">
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap w-px">
        <div className="flex items-center"></div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className="font-medium ">{props.name}</div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium ${totalColor(props.type)}`}>
          {props.type}
        </div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium ${totalColor(props.region)}`}>
          {props.region}
        </div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium `}>{props.mainFacility}</div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium`}>{props.latitude}</div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium `}>{props.longitude}</div>
      </td>

      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap w-px pr-10">
        <div className="space-x-1">
          <button
            type="submit"
            data-tip
            data-for="providerOverview"
            onClick={(e) => {
              e.stopPropagation();
              handleProviderItemClick();
            }}
            className="text-gray-400 hover:text-gray-500 rounded-full"
          >
            <span className="sr-only">Adjustments</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6 text-blue-400 "
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              style={{ transform: "rotate(90deg)" }}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"
              />
            </svg>
          </button>
          <ReactTooltip
            id="providerOverview"
            type="dark"
            textColor="#fff"
            backgroundColor="#449FDA"
            effect="solid"
          >
            <span>Provider Overview</span>
          </ReactTooltip>
          {/* <button
            type="submit"
            data-tip
            data-for="addCashier"
            onClick={(e) => {
              e.stopPropagation();
              setEditProviderCashierModalOpen(true);
            }}
            className="text-gray-400 hover:text-gray-500 rounded-full"
          >
            <span className="sr-only">Add Cashier</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6 text-blue-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              strokeWidth="2"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"
              />
            </svg>
          </button> */}
          <ReactTooltip
            id="addCashier"
            type="dark"
            textColor="#fff"
            backgroundColor="#449FDA"
            effect="solid"
          >
            <span>Add Cashier</span>
          </ReactTooltip>

          <div>
            <ModalBasic
              id="feedback-modal"
              modalOpen={payerEditProviderCashierModalOpen}
              setModalOpen={setEditProviderCashierModalOpen}
              title="Add Cashier"
            >
              {/* Modal content */}
              <div className="px-10 pt-4 pb-1">
                <h3 className="text-gray-400 mb-5 text-lg">CASHIER DETAILS</h3>

                <div className="text-sm">
                  {/* Options */}
                  <div className="flex">
                    <div className="w-64 mr-9">
                      <label
                        className="block text-sm font-medium mb-1"
                        htmlFor="name"
                      >
                        Full Names
                      </label>
                      <input
                        id="name"
                        className="form-input w-full"
                        type="text"
                        placeholder="First Name & Last Name "
                        value={cashierName}
                        onChange={(e) => setCashierName(e.target.value)}
                      />
                    </div>
                    <div className="w-64 mr-9">
                      <label
                        className="block text-sm font-medium mb-1"
                        htmlFor="email"
                      >
                        Email Address
                      </label>
                      <input
                        id="email"
                        className="form-input w-full "
                        type="email"
                        placeholder="Email Address"
                        value={cashierEmail}
                        onChange={(e) => setCashierEmail(e.target.value)}
                      />
                    </div>
                    <div className="w-64 mr-9">
                      <label
                        className="block text-sm font-medium mb-1"
                        htmlFor="name"
                      >
                        Mobile
                      </label>
                      <input
                        id="name"
                        className="form-input w-full"
                        type="phone"
                        placeholder="Mobile"
                      />
                    </div>
                  </div>

                  <div className="flex mt-5">
                    <div className="w-64 mr-9">
                      <label
                        className="block text-sm font-medium mb-1"
                        htmlFor="password"
                      >
                        Password
                      </label>
                      <input
                        id="password"
                        className="form-input w-full"
                        type="password"
                        placeholder="Password"
                        value={cashierPassword}
                        onChange={(e) => setCashierPassword(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </div>
              {/* Modal footer */}
              <div className="px-5 py-4 mt-5">
                <div className="flex flex-wrap justify-center space-x-2">
                  <button
                    className="btn-sm border-gray-200 hover:border-gray-300 text-gray-600 w-44 px-16 py-2"
                    onClick={(e) => {
                      e.stopPropagation();
                      setEditPayerModalOpen(false);
                    }}
                  >
                    Clear
                  </button>
                  <button
                    className="btn-sm bg-blue-500 hover:bg-blue-600 text-white w-44 px-16 py-2"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddCashier(e);
                    }}
                  >
                    Save
                  </button>
                </div>
              </div>
            </ModalBasic>
          </div>
        </div>
      </td>
    </tr>
  );
}
export default ProviderTableItem;
