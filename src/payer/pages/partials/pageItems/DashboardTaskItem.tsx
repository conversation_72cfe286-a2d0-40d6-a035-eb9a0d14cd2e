import React, { useEffect, useState } from "react";
import ModalBasic from "../../../components/ModalBasic";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { showPayer } from "../../../store/payers/actions";
import { connect } from "react-redux";
import { RootState } from "../../../store";
import ReactTooltip from "react-tooltip";
import {
  setTaskCategoryIdPolicyId,
  setTaskId,
} from "../../../store/dashboard/actions";

function DashboardTaskItem(props) {
  const [payerEditModalOpen, setEditPayerModalOpen] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  let viewLinkId = "";
  let policyId = "";
  let policyNumber = "";
  let categoryId = "";
  let customLink = "";
  let narrative = "";
  let taskId = "";

  if (
    props.taskName === "Create Categories" ||
    props.taskName === "Create Benefits" ||
    props.taskName === "Create Members" ||
    props.taskName === "Liven Policy"
  ) {
    viewLinkId = props.taskData.data.linkId ? props.taskData.data.linkId : "";
    policyId = props.taskData.data.policyId ? props.taskData.data.policyId : "";
    policyNumber = props.taskData.data.policyNumber
      ? props.taskData.data.policyNumber
      : "";
    categoryId = props.taskData.data.categoryId
      ? props.taskData.data.categoryId
      : "";
    if (props.taskName === "Create Categories") {
      customLink = viewLinkId + "/" + policyId;
      narrative = "Create Categories for Policy Number " + policyNumber;
      taskId = props.id;
    } else if (props.taskName === "Create Benefits") {
      customLink = viewLinkId + "/" + categoryId;
      narrative = "Create Benefits for Policy" + policyNumber;
      taskId = props.id;
    } else if (props.taskName === "Create Members") {
      customLink = viewLinkId + "/" + categoryId;
      narrative = "Add Members for Policy" + policyNumber;
      taskId = props.id;
    } else if (props.taskName === "Liven Policy") {
      customLink = viewLinkId + "/" + categoryId;
      narrative = "Liven Policy " + policyNumber;
      taskId = props.id;
    }
  } else {
    if (props.taskName === "Define Policy") {
      viewLinkId = props.taskData.linkId ? props.taskData.linkId : "";
      customLink = viewLinkId;
      taskId = props.id;
      narrative =
        "Define Policy for Scheme " +
        props.schemeName +
        " of Type " +
        props.schemeType;
    }
  }
  const handleTaskClick = (linkk, taskId, categoryId, policyId) => {
    props.setTaskId(taskId);
    if (categoryId && policyId) {
      props.setTaskCategoryIdPolicyId({ categoryId, policyId });
    }
    navigate(linkk);
  };

  const totalColor = (status) => {
    switch (status) {
      case "Paid":
        return "text-green-500";
      case "Due":
        return "text-yellow-500";
      case "Overdue":
        return "text-red-500";
      default:
        return "text-gray-500";
    }
  };
  // const handleTaskItemClick = (e) => {
  //   console.log(e);
  //   if (e === "Define Policy") {
  //     navigate("/policies/addPolicy/");
  //   } else if (e === "Create Categories") {
  //   } else if (e === "Create Benefits") {
  //   } else if (e === "Create Members") {
  //   } else if (e === "Liven Policy") {
  //   }
  //   ////dispatch(showPayer(props));
  //   navigate(e);
  // };

  const statusColor = (status) => {
    switch (status) {
      case "Paid":
        return "bg-green-100 text-green-600";
      case "Due":
        return "bg-yellow-100 text-yellow-600";
      case "Overdue":
        return "bg-red-100 text-red-500";
      default:
        return "bg-gray-100 text-gray-500";
    }
  };

  const typeIcon = (type) => {
    switch (type) {
      case "Subscription":
        return (
          <svg
            className="w-4 h-4 fill-current text-gray-400 shrink-0 mr-2"
            viewBox="0 0 16 16"
          >
            <path d="M4.3 4.5c1.9-1.9 5.1-1.9 7 0 .7.7 1.2 1.7 1.4 2.7l2-.3c-.2-1.5-.9-2.8-1.9-3.8C10.1.4 5.7.4 2.9 3.1L.7.9 0 7.3l6.4-.7-2.1-2.1zM15.6 8.7l-6.4.7 2.1 2.1c-1.9 1.9-5.1 1.9-7 0-.7-.7-1.2-1.7-1.4-2.7l-2 .3c.2 1.5.9 2.8 1.9 3.8 1.4 1.4 3.1 2 4.9 2 1.8 0 3.6-.7 4.9-2l2.2 2.2.8-6.4z" />
          </svg>
        );
      default:
        return (
          <svg
            className="w-4 h-4 fill-current text-gray-400 shrink-0 mr-2"
            viewBox="0 0 16 16"
          >
            <path d="M11.4 0L10 1.4l2 2H8.4c-2.8 0-5 2.2-5 5V12l-2-2L0 11.4l3.7 3.7c.*******.7.3.3 0 .5-.1.7-.3l3.7-3.7L7.4 10l-2 2V8.4c0-1.7 1.3-3 3-3H12l-2 2 1.4 1.4 3.7-3.7c.4-.4.4-1 0-1.4L11.4 0z" />
          </svg>
        );
    }
  };

  return (
    <tr className="even:bg-light-blue-50">
      <td className="px-2 first:pl-5 last:pr-5 py-2 whitespace-nowrap w-px">
        <div className="w-9 h-9 rounded-full shrink-0 bg-blue-500  mr-3">
          <svg
            className="w-9 h-9 fill-current text-green-50"
            viewBox="0 0 36 36"
          >
            <path d="M15 13v-3l-5 4 5 4v-3h8a1 1 0 000-2h-8zM21 21h-8a1 1 0 000 2h8v3l5-4-5-4v3z" />
          </svg>
        </div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-1 whitespace-nowrap">
        <div className="font-medium ">{narrative}</div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-1 whitespace-nowrap">
        <button
          className="btn font-medium bg-blue-500 hover:bg-blue-600 text-gray-50"
          onClick={(e) => {
            e.stopPropagation();
            handleTaskClick(customLink, taskId, categoryId, policyId);
          }}
        >
          Process Task <span className="hidden sm:inline"></span>
        </button>
      </td>
    </tr>
  );
}
const mapDispatchToProps = (dispatch) => ({
  setTaskId: (taskId) => dispatch(setTaskId(taskId)),
  setTaskCategoryIdPolicyId: (catPolicyObj) =>
    dispatch(setTaskCategoryIdPolicyId(catPolicyObj)),
});
export default connect(null, mapDispatchToProps)(DashboardTaskItem);
