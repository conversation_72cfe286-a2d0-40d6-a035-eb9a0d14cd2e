import React, { useState } from "react";
import ModalBasic from "../../../components/ModalBasic";
import { Link } from "react-router-dom";

function CategoryMemberItem(props) {
  const [payerEditModalOpen, setEditPayerModalOpen] = useState(false);
  const totalColor = (status) => {
    switch (status) {
      case "true":
        return "text-green-500";
      case "Due":
        return "text-yellow-500";
      case "false":
        return "text-red-500";
      default:
        return "text-gray-500";
    }
  };

  const statusColor = (status) => {
    switch (status) {
      case true:
        return "p-1 py-0.5 px-2.5 text-center inline-flex rounded-full border border-green-500 bg-green-100 text-green-600";
      case "Due":
        return "bg-yellow-100 text-yellow-600";
      case false:
        return " p-1 py-0.5 px-2.5 text-center inline-flex rounded-full border border-red-500  bg-red-50 text-red-500";
      default:
        return "bg-gray-100 text-gray-500";
    }
  };
  console.log(props.processed);
  console.log(props.principal);

  const typeIcon = (type) => {
    switch (type) {
      case "Subscription":
        return (
          <svg
            className="w-4 h-4 fill-current text-gray-400 shrink-0 mr-2"
            viewBox="0 0 16 16"
          >
            <path d="M4.3 4.5c1.9-1.9 5.1-1.9 7 0 .7.7 1.2 1.7 1.4 2.7l2-.3c-.2-1.5-.9-2.8-1.9-3.8C10.1.4 5.7.4 2.9 3.1L.7.9 0 7.3l6.4-.7-2.1-2.1zM15.6 8.7l-6.4.7 2.1 2.1c-1.9 1.9-5.1 1.9-7 0-.7-.7-1.2-1.7-1.4-2.7l-2 .3c.2 1.5.9 2.8 1.9 3.8 1.4 1.4 3.1 2 4.9 2 1.8 0 3.6-.7 4.9-2l2.2 2.2.8-6.4z" />
          </svg>
        );
      default:
        return (
          <svg
            className="w-4 h-4 fill-current text-gray-400 shrink-0 mr-2"
            viewBox="0 0 16 16"
          >
            <path d="M11.4 0L10 1.4l2 2H8.4c-2.8 0-5 2.2-5 5V12l-2-2L0 11.4l3.7 3.7c.*******.7.3.3 0 .5-.1.7-.3l3.7-3.7L7.4 10l-2 2V8.4c0-1.7 1.3-3 3-3H12l-2 2 1.4 1.4 3.7-3.7c.4-.4.4-1 0-1.4L11.4 0z" />
          </svg>
        );
    }
  };

  return (
    <tr className="even:bg-light-blue-50">
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className="font-medium">{props.name}</div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium `}>{props.memberNo}</div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium `}>{props.nhif}</div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className="font-medium">{props.dob}</div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium `}>{props.gender}</div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium `}>{props.phone}</div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className="font-medium">{props.email}</div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium `}>
          {props.principal !== null ? props.principal.name : "None"}
        </div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className={`font-medium ${statusColor(props.processed)} `}>
          {props.processed ? "Processed" : "Not Processed"}
        </div>
      </td>
      <td className="px-2 first:pl-5  py-3 whitespace-nowrap">
        <div
          className={`inline-flex font-medium rounded-full text-center px-2.5 py-0.5`}
        >
          <div className="space-x-1">
            <button
              className="text-gray-400 hover:text-gray-500 rounded-full"
              onClick={(e) => {
                e.stopPropagation();
                // setEditPayerModalOpen(true);
              }}
            >
              <span className="sr-only">Remove</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-blue-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
            </button>
            <Link className="" to="#">
              <button className="text-gray-400 hover:text-gray-500 rounded-full">
                <span className="sr-only">Adjust</span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6 text-blue-400 "
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  style={{ transform: "rotate(90deg)" }}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"
                  />
                </svg>
              </button>
            </Link>
          </div>
        </div>
      </td>
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap w-px pr-10">
        <div className="space-x-1">
          <div>
            <ModalBasic
              id="feedback-modal"
              modalOpen={payerEditModalOpen}
              setModalOpen={setEditPayerModalOpen}
              title="Edit Payer"
            >
              {/* Modal content */}
              <div className="px-10 pt-4 pb-1">
                <h3 className="text-gray-400 mb-5 text-lg">PAYER DETAILS</h3>

                <div className="text-sm">
                  {/* Options */}
                  <div className="flex">
                    <div className="w-64 mr-9">
                      <label
                        className="block text-sm font-medium mb-1"
                        htmlFor="name"
                      >
                        Payer Name
                      </label>
                      <input
                        id="name"
                        className="form-input w-full"
                        type="text"
                        placeholder="Payer Name"
                      />
                    </div>
                    <div className="w-64 mr-9">
                      <label
                        className="block text-sm font-medium mb-1"
                        htmlFor="email"
                      >
                        Email Address
                      </label>
                      <input
                        id="email"
                        className="form-input w-full "
                        type="email"
                        placeholder="Email Address"
                      />
                    </div>
                    <div className="w-64 mr-9">
                      <label
                        className="block text-sm font-medium mb-1"
                        htmlFor="name"
                      >
                        Mobile
                      </label>
                      <input
                        id="name"
                        className="form-input w-full"
                        type="phone"
                        placeholder="Mobile"
                      />
                    </div>
                  </div>
                  <div className="mt-10">
                    <h3 className="text-gray-400 mb-5 text-lg">PAYER TYPE</h3>
                  </div>

                  <div className="flex mt-5">
                    <div className="flex  mr-9 ">
                      <div className="w-48  mr-5">
                        <input
                          type="radio"
                          name="optionEdit"
                          id="option11"
                          className="hidden p-20"
                        />
                        <label
                          htmlFor="option11"
                          className="inline-block cursor-pointer  p-5 label-checked:text-gray-100 label-background:bg-blue-900 border-2 px-16 py-3 rounded-md"
                        >
                          Underwriter
                        </label>
                      </div>
                      <div className="w-48 mr-5">
                        <input
                          type="radio"
                          name="optionEdit"
                          id="option22"
                          className="hidden"
                        />
                        <label
                          htmlFor="option22"
                          className="inline-block cursor-pointer label-checked:text-gray-100 label-background:bg-blue-900 border-2  px-16 py-3 rounded-md"
                        >
                          Corporate
                        </label>
                      </div>
                      <div className="w-48 mr-5">
                        <input
                          type="radio"
                          name="optionEdit"
                          id="option33"
                          className="hidden"
                        />
                        <label
                          htmlFor="option33"
                          className="inline-block cursor-pointer label-checked:text-gray-100 label-background:bg-blue-900 border-2  px-16 py-3 rounded-md"
                        >
                          Intermediary
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {/* Modal footer */}
              <div className="px-5 py-4 mt-5">
                <div className="flex flex-wrap justify-center space-x-2">
                  <button
                    className="btn-sm border-gray-200 hover:border-gray-300 text-gray-600 w-44 px-16 py-2"
                    onClick={(e) => {
                      e.stopPropagation();
                      setEditPayerModalOpen(false);
                    }}
                  >
                    Clear
                  </button>
                  <button className="btn-sm bg-blue-500 hover:bg-blue-600 text-white w-44 px-16 py-2">
                    Save
                  </button>
                </div>
              </div>
            </ModalBasic>
          </div>
        </div>
      </td>
    </tr>
  );
}

export default CategoryMemberItem;
