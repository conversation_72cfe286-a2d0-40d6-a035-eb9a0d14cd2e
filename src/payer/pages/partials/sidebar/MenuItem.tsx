import { NavLink, useLocation } from "react-router-dom";
import { clsx } from "~lib/utils";

type Props = {
  children?: React.ReactNode;
  to: string;
  hidden?: boolean;
  label: React.ReactNode;
  icon?: React.ReactNode;
  toggleSidebar: () => void;
};

export default function MenuItem({ children, toggleSidebar, label, icon, to, hidden }: Props) {
  const location = useLocation();
  const { pathname } = location;

  const active = pathname.includes(to);

  return hidden ? null : (
    <li
      className={clsx(
        "px-2 py-2 transition-colors duration-100 hover:text-blue-500",
        active ? "text-blue-400" : "text-white",
      )}
    >
      <NavLink
        className="flex items-center justify-between gap-2"
        to={to}
        onClick={() => {
          toggleSidebar();
        }}
      >
        <div className="flex items-center gap-2">
          {icon}
          <span className="flex-grow font-medium">{label}</span>
        </div>

        {children}
      </NavLink>
    </li>
  );
}
