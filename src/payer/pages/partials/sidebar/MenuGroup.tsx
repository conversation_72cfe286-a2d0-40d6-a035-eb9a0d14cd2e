import { ChevronDownIcon, MinusIcon, PlusIcon } from "@heroicons/react/24/outline";
import { useEffect, useRef, useState } from "react";

type Props = {
  hidden?: boolean;
  name: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  hasPlusIcon?: boolean;
};

export default function MenuGroup({ children, name, icon, hidden, hasPlusIcon = false }: Props) {
  const [isOpen, setIsOpen] = useState(false);
  const detailsRef = useRef<HTMLDetailsElement>(null);

  useEffect(() => {
    if (detailsRef.current) {
      detailsRef.current.open = isOpen;
    }
  }, [isOpen]);

  const handleToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsOpen(!isOpen);
  };

  const handleDetailsToggle = () => {
    if (detailsRef.current) {
      setIsOpen(detailsRef.current.open);
    }
  };

  return hidden ? null : (
    <li className="px-2 py-2 text-gray-50 transition-colors duration-100 hover:text-blue-500">
      <details
        ref={detailsRef}
        className="cursor-pointer rounded [&>summary_svg.chevron]:open:-rotate-180"
        onToggle={handleDetailsToggle}
      >
        <summary className="list-none [&::-webkit-details-marker]:hidden" onClick={handleToggle}>
          <div className="flex items-center justify-between gap-2">
            <div className="flex items-center gap-2">
              {icon}
              <span className="flex-grow font-medium">{name}</span>
            </div>
            {hasPlusIcon ? (
              <>
                {!isOpen ? (
                  <PlusIcon
                    className="chevron h-[14px] w-[14px] shrink-0 rotate-0 transform transition-all duration-300"
                    strokeWidth={2}
                  />
                ) : (
                  <MinusIcon
                    className="chevron h-[14px] w-[14px] shrink-0 rotate-0 transform transition-all duration-300"
                    strokeWidth={2}
                  />
                )}
              </>
            ) : (
              <ChevronDownIcon
                className="chevron h-4 w-4 shrink-0 rotate-0 transform transition-all duration-300"
                strokeWidth={2}
              />
            )}
          </div>
        </summary>
        <ul className="pl-6 pt-1">{children}</ul>
      </details>
    </li>
  );
}
