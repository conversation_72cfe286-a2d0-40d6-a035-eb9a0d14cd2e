import {
  BanknotesIcon,
  ChatBubbleOvalLeftIcon,
  ClipboardDocumentListIcon,
  CreditCardIcon,
  DocumentTextIcon,
  HeartIcon,
  HomeModernIcon,
  ScaleIcon,
  Squares2X2Icon,
  UserGroupIcon,
  UserPlusIcon,
} from "@heroicons/react/24/outline";
import { HTMLAttributes } from "react";
import { NavLink } from "react-router-dom";
import LCTLogo from "~lib/svg/LCTLogo";
import { clsx } from "~lib/utils";
import UserService from "../../../services/UserService";
import { ALL_CARE_ROLES } from "../../../components/access-management/data";
import {
  FIRST_ASSURANCE_PAYER_ID,
  KENGEN_PAYER_ID,
  KPLC_PAYER_ID,
  KRA_PAYER_ID,
  LCT_PAYER_ID,
  LIAISON_HEALTHCARE_PAYER_ID,
} from "../../../utils/constants/payerIds";
import { useUserEffectivePermissions } from "../../../utils/user-management-utils";
import MenuGroup from "./MenuGroup";
import MenuItem from "./MenuItem";

type Props = HTMLAttributes<HTMLDivElement> & {
  toggleSidebar: () => void;
};

export default function Sidebar({ toggleSidebar, className, ...rest }: Props) {
  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const username = UserService.getUsername()?.toString();

  const { hasPermission } = useUserEffectivePermissions();

  const hasFinanceAccess = payerId === LCT_PAYER_ID;
  const hasAdjudicationAccess =
    payerId === LCT_PAYER_ID ||
    payerId === LIAISON_HEALTHCARE_PAYER_ID ||
    payerId === FIRST_ASSURANCE_PAYER_ID;
  const hasVettingAndBatchingAccess =
    payerId === KPLC_PAYER_ID || payerId === LIAISON_HEALTHCARE_PAYER_ID;
  const hasAiAccess =
    payerId === LCT_PAYER_ID ||
    payerId === LIAISON_HEALTHCARE_PAYER_ID ||
    payerId === FIRST_ASSURANCE_PAYER_ID;

  const CARE_REPORTS_PAYER_EXCLUSION = [KENGEN_PAYER_ID, KRA_PAYER_ID];

  /**
   * Hide care reports for KenGen
   */
  const isCareReportsEnabled = !CARE_REPORTS_PAYER_EXCLUSION.includes(payerId?.toString());

  const isBenefitsEnhancementHidden = payerId == 4 || payerId == 1;

  const isJudiciaryAndHasUsername =
    payerId == 1 &&
    (username.includes("bryan.chemuku") ||
      username.includes("chrispine.omollo") ||
      username.includes("elizabeth.kalei") ||
      username.includes("gilbert.matui"));

  /** user roles*/
  /* hr roles */
  const USER_HAS_ROLE_HR = hasPermission(["HR"]);
  const USER_HAS_ROLE_HR_ONE = hasPermission(["HR_LVL_1"]);

  /* scheme roles */
  const USER_HAS_SCHEME_OVERVIEW_ROLE = hasPermission(["SCHEME_OVERVIEW_ROLE"]);

  /* membership roles */
  const USER_HAS_MEMBERSHIP_ROLE = hasPermission(["MEMBERSHIP_ROLE"]);
  const USER_HAS_MEMBERSHIP_INQUIRY_ROLE = hasPermission(["MEMBERSHIP_INQUIRY_ROLE"]);
  const USER_HAS_MEMBERSHIP_EDIT_ROLE = hasPermission(["MEMBERSHIP_EDIT_ROLE"]);

  // membership status update
  const USER_HAS_MEMBERSHIP_ACTIVATE_ROLE = hasPermission(["MEMBERSHIP_ACTIVATE_ROLE"]);
  const USER_HAS_MEMBERSHIP_DEACTIVATE_ROLE = hasPermission(["MEMBERSHIP_DEACTIVATE_ROLE"]);
  const USER_HAS_MEMBERSHIP_SUSPEND_ROLE = hasPermission(["MEMBERSHIP_SUSPEND_ROLE"]);
  const USER_HAS_MEMBERSHIP_STATUS_UPDATE_ROLE =
    USER_HAS_MEMBERSHIP_ACTIVATE_ROLE ||
    USER_HAS_MEMBERSHIP_DEACTIVATE_ROLE ||
    USER_HAS_MEMBERSHIP_SUSPEND_ROLE;

  // membership biometrics
  const USER_HAS_MEMBERSHIP_DETACH_BIOMETRICS_ROLE = hasPermission([
    "MEMBERSHIP_DETACH_BIOMETRICS_ROLE",
  ]);

  // membership benefit enhancement
  const USER_HAS_MEMBERSHIP_BENEFIT_SUSPEND_ROLE = hasPermission([
    "MEMBERSHIP_BENEFIT_SUSPEND_ROLE",
  ]);
  const USER_HAS_MEMBERSHIP_BENEFIT_TOPUP_ROLE = hasPermission(["MEMBERSHIP_BENEFIT_TOPUP_ROLE"]);
  const USER_HAS_MEMBERSHIP_BENEFIT_TRANSFER_ROLE = hasPermission([
    "MEMBERSHIP_BENEFIT_TRANSFER_ROLE",
  ]);
  const USER_HAS_BENEFIT_ENHANCEMENT_ROLE =
    USER_HAS_MEMBERSHIP_BENEFIT_SUSPEND_ROLE ||
    USER_HAS_MEMBERSHIP_BENEFIT_TOPUP_ROLE ||
    USER_HAS_MEMBERSHIP_BENEFIT_TRANSFER_ROLE;

  // membership mass actions
  const USER_HAS_MEMBERSHIP_MASS_DEACTIVATION_ROLE = hasPermission([
    "MEMBERSHIP_MASS_DEACTIVATION_ROLE",
  ]);
  const USER_HAS_MEMBERSHIP_MASS_ACTIVATION_ROLE = hasPermission([
    "MEMBERSHIP_MASS_ACTIVATION_ROLE",
  ]);
  const USER_HAS_MEMBERSHIP_MASS_EDIT_ROLE = hasPermission(["MEMBERSHIP_MASS_EDIT_ROLE"]);
  const USER_HAS_MASS_ACTIONS_ROLE =
    USER_HAS_MEMBERSHIP_MASS_DEACTIVATION_ROLE ||
    USER_HAS_MEMBERSHIP_MASS_ACTIVATION_ROLE ||
    USER_HAS_MEMBERSHIP_MASS_EDIT_ROLE;

  // membership reports
  const USER_HAS_MEMBERSHIP_REPORTS_ROLE = hasPermission(["MEMBERSHIP_REPORTS_ROLE"]);

  const USER_HAS_ANY_MEMBERSHIP_ROLE =
    USER_HAS_MEMBERSHIP_ROLE ||
    USER_HAS_MEMBERSHIP_INQUIRY_ROLE ||
    USER_HAS_MEMBERSHIP_EDIT_ROLE ||
    USER_HAS_MEMBERSHIP_ACTIVATE_ROLE ||
    USER_HAS_MEMBERSHIP_SUSPEND_ROLE ||
    USER_HAS_MEMBERSHIP_DEACTIVATE_ROLE ||
    USER_HAS_MEMBERSHIP_DETACH_BIOMETRICS_ROLE ||
    USER_HAS_MEMBERSHIP_BENEFIT_SUSPEND_ROLE ||
    USER_HAS_MEMBERSHIP_BENEFIT_TOPUP_ROLE ||
    USER_HAS_MEMBERSHIP_BENEFIT_TRANSFER_ROLE ||
    USER_HAS_MEMBERSHIP_MASS_DEACTIVATION_ROLE ||
    USER_HAS_MEMBERSHIP_MASS_ACTIVATION_ROLE ||
    USER_HAS_MEMBERSHIP_MASS_EDIT_ROLE ||
    USER_HAS_MEMBERSHIP_REPORTS_ROLE;

  /* provider roles */
  const USER_HAS_PROVIDER_ROLE = hasPermission(["PROVIDER_ROLE"]);
  const USER_HAS_PROVIDER_VIEW_ROLE = hasPermission(["PROVIDER_VIEW_ROLE"]);
  const USER_HAS_PROVIDER_RESTRICTIONS_ROLE = hasPermission(["PROVIDER_RESTRICTIONS_ROLE"]);

  const USER_HAS_ANY_PROVIDER_ROLE =
    USER_HAS_PROVIDER_ROLE || USER_HAS_PROVIDER_VIEW_ROLE || USER_HAS_PROVIDER_RESTRICTIONS_ROLE;

  /* care roles */
  const USER_HAS_CARE_PREAUTH_REVIEW_ROLE_BASE = hasPermission(["CARE_PREAUTH_REVIEW_ROLE"]);
  const USER_HAS_CARE_PREAUTH_VIEW_ALL_ROLE = hasPermission(["CARE_PREAUTH_VIEW_ALL_ROLE"]);
  const USER_HAS_CARE_PREAUTH_VIEW_DENTAL_ROLE = hasPermission(["CARE_PREAUTH_VIEW_DENTAL_ROLE"]);
  const USER_HAS_CARE_PREAUTH_VIEW_OPTICAL_ROLE = hasPermission(["CARE_PREAUTH_VIEW_OPTICAL_ROLE"]);
  const USER_HAS_CARE_PREAUTH_VIEW_OUTPATIENT_ROLE = hasPermission([
    "CARE_PREAUTH_VIEW_OUTPATIENT_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_VIEW_INPATIENT_ROLE = hasPermission([
    "CARE_PREAUTH_VIEW_INPATIENT_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_VIEW_MATERNITY_ROLE = hasPermission([
    "CARE_PREAUTH_VIEW_MATERNITY_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_APPROVE_ALL_ROLE = hasPermission(["CARE_PREAUTH_APPROVE_ALL_ROLE"]);
  const USER_HAS_CARE_PREAUTH_APPROVE_DENTAL_ROLE = hasPermission([
    "CARE_PREAUTH_APPROVE_DENTAL_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_APPROVE_OPTICAL_ROLE = hasPermission([
    "CARE_PREAUTH_APPROVE_OPTICAL_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_APPROVE_OUTPATIENT_ROLE = hasPermission([
    "CARE_PREAUTH_APPROVE_OUTPATIENT_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_APPROVE_INPATIENT_ROLE = hasPermission([
    "CARE_PREAUTH_APPROVE_INPATIENT_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_APPROVE_MATERNITY_ROLE = hasPermission([
    "CARE_PREAUTH_APPROVE_MATERNITY_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_TOPUP_ALL_ROLE = hasPermission(["CARE_PREAUTH_TOPUP_ALL_ROLE"]);
  const USER_HAS_CARE_PREAUTH_TOPUP_DENTAL_ROLE = hasPermission(["CARE_PREAUTH_TOPUP_DENTAL_ROLE"]);
  const USER_HAS_CARE_PREAUTH_TOPUP_OPTICAL_ROLE = hasPermission([
    "CARE_PREAUTH_TOPUP_OPTICAL_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_TOPUP_OUTPATIENT_ROLE = hasPermission([
    "CARE_PREAUTH_TOPUP_OUTPATIENT_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_TOPUP_INPATIENT_ROLE = hasPermission([
    "CARE_PREAUTH_TOPUP_INPATIENT_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_TOPUP_MATERNITY_ROLE = hasPermission([
    "CARE_PREAUTH_TOPUP_MATERNITY_ROLE",
  ]);

  const USER_HAS_CARE_PREAUTH_REVIEW_ROLE =
    USER_HAS_CARE_PREAUTH_REVIEW_ROLE_BASE ||
    USER_HAS_CARE_PREAUTH_VIEW_ALL_ROLE ||
    USER_HAS_CARE_PREAUTH_VIEW_DENTAL_ROLE ||
    USER_HAS_CARE_PREAUTH_VIEW_OPTICAL_ROLE ||
    USER_HAS_CARE_PREAUTH_VIEW_OUTPATIENT_ROLE ||
    USER_HAS_CARE_PREAUTH_VIEW_INPATIENT_ROLE ||
    USER_HAS_CARE_PREAUTH_VIEW_MATERNITY_ROLE ||
    USER_HAS_CARE_PREAUTH_APPROVE_ALL_ROLE ||
    USER_HAS_CARE_PREAUTH_APPROVE_DENTAL_ROLE ||
    USER_HAS_CARE_PREAUTH_APPROVE_OPTICAL_ROLE ||
    USER_HAS_CARE_PREAUTH_APPROVE_OUTPATIENT_ROLE ||
    USER_HAS_CARE_PREAUTH_APPROVE_INPATIENT_ROLE ||
    USER_HAS_CARE_PREAUTH_APPROVE_MATERNITY_ROLE ||
    USER_HAS_CARE_PREAUTH_TOPUP_ALL_ROLE ||
    USER_HAS_CARE_PREAUTH_TOPUP_DENTAL_ROLE ||
    USER_HAS_CARE_PREAUTH_TOPUP_OPTICAL_ROLE ||
    USER_HAS_CARE_PREAUTH_TOPUP_OUTPATIENT_ROLE ||
    USER_HAS_CARE_PREAUTH_TOPUP_INPATIENT_ROLE ||
    USER_HAS_CARE_PREAUTH_TOPUP_MATERNITY_ROLE;

  const USER_HAS_CARE_VISIT_REACTIVATION_ROLE = hasPermission(["CARE_VISIT_REACTIVATION_ROLE"]);
  const USER_HAS_CARE_REPORTS_RETRIEVAL_ROLE = hasPermission(["CARE_REPORTS_RETRIEVAL_ROLE"]);

  const USER_HAS_ANY_CARE_ROLE = hasPermission(ALL_CARE_ROLES);

  /* claims roles */
  const USER_HAS_CLAIMS_ROLE = hasPermission(["CLAIMS_ROLE"]);
  const USER_HAS_CLAIMS_REIMBURSEMENT_ROLE = hasPermission(["CLAIMS_REIMBURSEMENT_ROLE"]);
  const USER_HAS_CLAIMS_OFFLCT_ROLE = hasPermission(["CLAIMS_OFFLCT_ROLE"]);
  const USER_HAS_CLAIMS_VETTING_ROLE = hasPermission(["CLAIMS_VETTING_ROLE"]);
  const USER_HAS_CLAIMS_BATCHING_ROLE = hasPermission(["CLAIMS_BATCHING_ROLE"]);
  const USER_HAS_CLAIMS_REPORTS_ROLE = hasPermission(["CLAIMS_REPORTS_ROLE"]);
  const USER_HAS_CLAIMS_REVERSAL_ROLE = hasPermission(["CLAIMS_REVERSAL_ROLE"]);

  const USER_HAS_ANY_CLAIMS_ROLE =
    USER_HAS_CLAIMS_ROLE ||
    USER_HAS_CLAIMS_REIMBURSEMENT_ROLE ||
    USER_HAS_CLAIMS_OFFLCT_ROLE ||
    USER_HAS_CLAIMS_VETTING_ROLE ||
    USER_HAS_CLAIMS_BATCHING_ROLE ||
    USER_HAS_CLAIMS_REPORTS_ROLE ||
    USER_HAS_CLAIMS_REVERSAL_ROLE;

  /* claims adjudication roles */
  const USER_HAS_CLAIMS_ADJUDICATION_ROLE = hasPermission(["CLAIMS_ADJUDICATION_ROLE"]);
  const USER_HAS_CLAIMS_ADJUDICATION_BATCHING_ROLE = hasPermission([
    "CLAIMS_ADJUDICATION_BATCHING_ROLE",
  ]);
  const USER_HAS_CLAIMS_ADJUDICATION_VETTING_ROLE = hasPermission([
    "CLAIMS_ADJUDICATION_VETTING_ROLE",
  ]);
  const USER_HAS_CLAIMS_ADJUDICATION_AI_ROLE = hasPermission(["CLAIMS_ADJUDICATION_AI_ROLE"]);

  const USER_HAS_ANY_ADJUDICATION_ROLE =
    USER_HAS_CLAIMS_ADJUDICATION_ROLE ||
    USER_HAS_CLAIMS_ADJUDICATION_BATCHING_ROLE ||
    USER_HAS_CLAIMS_ADJUDICATION_VETTING_ROLE ||
    USER_HAS_CLAIMS_ADJUDICATION_AI_ROLE;

  /* finance roles */
  const USER_HAS_FINANCE_AND_ACCOUNTING_ROLE = hasPermission(["FINANCE_AND_ACCOUNTING_ROLE"]);
  const USER_HAS_FINANCE_AND_ACCOUNTING_VOUCHERING_ROLE = hasPermission([
    "FINANCE_AND_ACCOUNTING_VOUCHERING_ROLE",
  ]);
  const USER_HAS_FINANCE_AND_ACCOUNTING_SETTLEMENT_ROLE = hasPermission([
    "FINANCE_AND_ACCOUNTING_SETTLEMENT_ROLE",
  ]);
  const USER_HAS_FINANCE_AND_ACCOUNTING_REMITTANCE_ROLE = hasPermission([
    "FINANCE_AND_ACCOUNTING_REMITTANCE_ROLE",
  ]);
  const USER_HAS_FINANCE_AND_ACCOUNTING_CREDIT_ANALYSIS_ROLE = hasPermission([
    "FINANCE_AND_ACCOUNTING_CREDIT_ANALYSIS_ROLE",
  ]);

  const USER_HAS_ANY_FINANCE_AND_ACCOUNTING_ROLE =
    USER_HAS_FINANCE_AND_ACCOUNTING_ROLE ||
    USER_HAS_FINANCE_AND_ACCOUNTING_VOUCHERING_ROLE ||
    USER_HAS_FINANCE_AND_ACCOUNTING_SETTLEMENT_ROLE ||
    USER_HAS_FINANCE_AND_ACCOUNTING_REMITTANCE_ROLE ||
    USER_HAS_FINANCE_AND_ACCOUNTING_CREDIT_ANALYSIS_ROLE;

  /** user management roles*/
  const USER_HAS_USER_MANAGEMENT_ROLES_ASSIGNMENT_ROLE = hasPermission([
    "USER_MANAGEMENT_ROLES_ASSIGNMENT_ROLE",
  ]);
  const USER_HAS_USER_MANAGEMENT_AUDIT_LOGS_ROLE = hasPermission([
    "USER_MANAGEMENT_AUDIT_LOGS_ROLE",
  ]);

  const hasUserManagementAccess =
    USER_HAS_USER_MANAGEMENT_ROLES_ASSIGNMENT_ROLE || USER_HAS_USER_MANAGEMENT_AUDIT_LOGS_ROLE;

  return (
    <aside className={clsx("bg-blue-950", className)} {...rest}>
      <div className="mb-2 mt-6 flex justify-center px-4 lg:mb-4 lg:mt-12">
        <NavLink to="/" className="block">
          <LCTLogo className="h-20 w-48" />
        </NavLink>
      </div>
      {/* Links */}
      <ul className="px-2 text-sm lg:space-y-2">
        {/* <ul className="flex-grow px-4 overflow-y-auto text-sm"> */}
        <MenuItem
          label="Dashboard"
          icon={<Squares2X2Icon className="h-6 w-6" />}
          toggleSidebar={toggleSidebar}
          to="/"
        />
        {USER_HAS_SCHEME_OVERVIEW_ROLE && (
          <MenuItem
            label="Schemes"
            icon={<ClipboardDocumentListIcon className="h-6 w-6" />}
            toggleSidebar={toggleSidebar}
            to="/schemes"
          />
        )}
        {USER_HAS_ANY_MEMBERSHIP_ROLE && (
          <MenuGroup name="Membership" icon={<UserGroupIcon className="h-6 w-6" />}>
            {USER_HAS_MEMBERSHIP_INQUIRY_ROLE && (
              <MenuItem toggleSidebar={toggleSidebar} label="Member Inquiry" to="/members/find" />
            )}
            {USER_HAS_MEMBERSHIP_EDIT_ROLE && (
              <MenuItem
                label="Member Editing"
                to="/members/edit"
                toggleSidebar={toggleSidebar}
                hidden={USER_HAS_ROLE_HR_ONE || isJudiciaryAndHasUsername}
              />
            )}
            {USER_HAS_MEMBERSHIP_STATUS_UPDATE_ROLE && (
              <MenuItem
                label="Activate / Deactivate"
                to="/members/update-status"
                toggleSidebar={toggleSidebar}
                hidden={USER_HAS_ROLE_HR_ONE || isJudiciaryAndHasUsername}
              />
            )}
            {USER_HAS_MEMBERSHIP_DETACH_BIOMETRICS_ROLE && (
              <MenuItem
                label="Biometrics Detachment"
                to="/members/detach-biometrics"
                toggleSidebar={toggleSidebar}
                hidden={USER_HAS_ROLE_HR_ONE || isJudiciaryAndHasUsername}
              />
            )}
            {USER_HAS_BENEFIT_ENHANCEMENT_ROLE && (
              <MenuItem
                label="Benefit Enhancement"
                to="/members/benefit-enhancement"
                toggleSidebar={toggleSidebar}
                hidden={isBenefitsEnhancementHidden || isJudiciaryAndHasUsername}
              />
            )}
            {USER_HAS_MASS_ACTIONS_ROLE && (
              <MenuItem
                label="Mass Actions"
                to="/members/batch"
                toggleSidebar={toggleSidebar}
                hidden={USER_HAS_ROLE_HR_ONE || isJudiciaryAndHasUsername}
              />
            )}
            {USER_HAS_MEMBERSHIP_REPORTS_ROLE && (
              <MenuItem
                label="Reports"
                to="/member/reports"
                toggleSidebar={toggleSidebar}
                hidden={USER_HAS_ROLE_HR_ONE || isJudiciaryAndHasUsername}
              />
            )}
          </MenuGroup>
        )}
        {USER_HAS_ANY_PROVIDER_ROLE && (
          <MenuGroup
            name="Providers"
            icon={<HomeModernIcon className="h-6 w-6" />}
            hidden={USER_HAS_ROLE_HR || USER_HAS_ROLE_HR_ONE || isJudiciaryAndHasUsername}
          >
            {USER_HAS_PROVIDER_VIEW_ROLE && (
              <MenuItem toggleSidebar={toggleSidebar} label="Providers" to="/providers" />
            )}
            {USER_HAS_PROVIDER_RESTRICTIONS_ROLE && (
              <MenuItem toggleSidebar={toggleSidebar} label="Restrictions" to="/restrictions" />
            )}
          </MenuGroup>
        )}
        <MenuItem
          label="Card"
          icon={<CreditCardIcon className="h-6 w-6" />}
          toggleSidebar={toggleSidebar}
          to="/cards"
          hidden={isJudiciaryAndHasUsername}
        />
        {USER_HAS_ANY_CARE_ROLE && (
          <MenuGroup
            name="Care"
            icon={<HeartIcon className="h-6 w-6" />}
            hidden={USER_HAS_ROLE_HR || USER_HAS_ROLE_HR_ONE || isJudiciaryAndHasUsername}
          >
            {USER_HAS_CARE_PREAUTH_REVIEW_ROLE && (
              <MenuItem
                label={
                  <span className="text-sm font-medium duration-200 lg:opacity-100 lg:sidebar-expanded:opacity-100 2xl:opacity-100">
                    Pre-authorizations
                  </span>
                }
                toggleSidebar={toggleSidebar}
                to="/preauth"
              />
            )}
            {USER_HAS_CARE_VISIT_REACTIVATION_ROLE && (
              <MenuItem
                toggleSidebar={toggleSidebar}
                label="Visit Reactivation"
                to="/visit-reactivation"
              />
            )}
            {USER_HAS_CARE_REPORTS_RETRIEVAL_ROLE && (
              <>
                <MenuItem
                  label="Diagnosis Reports"
                  to="/care/reports"
                  toggleSidebar={toggleSidebar}
                  hidden={!isCareReportsEnabled}
                />
                <MenuItem
                  label="Pre-authorization Reports"
                  to="/care/pre-auth-reports"
                  toggleSidebar={toggleSidebar}
                  hidden={!isCareReportsEnabled}
                />
              </>
            )}
          </MenuGroup>
        )}
        {USER_HAS_ANY_CLAIMS_ROLE && (
          <MenuGroup
            name="Claims"
            icon={<DocumentTextIcon className="h-6 w-6" />}
            hidden={USER_HAS_ROLE_HR || USER_HAS_ROLE_HR_ONE || isJudiciaryAndHasUsername}
          >
            {USER_HAS_CLAIMS_REIMBURSEMENT_ROLE && (
              <MenuItem toggleSidebar={toggleSidebar} label="Reimbursements" to="/reimbursements" />
            )}
            {USER_HAS_CLAIMS_OFFLCT_ROLE && (
              <MenuItem
                toggleSidebar={toggleSidebar}
                label={<p>Offline Visits (Off-LCT)</p>}
                to="/offline-visits"
              />
            )}
            {USER_HAS_CLAIMS_REVERSAL_ROLE && (
              <MenuItem toggleSidebar={toggleSidebar} label="Reversal" to="/reversal" />
            )}
            {hasVettingAndBatchingAccess && (
              <>
                {USER_HAS_CLAIMS_VETTING_ROLE && (
                  <MenuItem
                    toggleSidebar={toggleSidebar}
                    label="Claim Vetting"
                    to="/claims/vetting"
                  />
                )}
                {USER_HAS_CLAIMS_BATCHING_ROLE && (
                  <MenuItem
                    toggleSidebar={toggleSidebar}
                    label="Claim Batching"
                    to="/claims/batching"
                  />
                )}
              </>
            )}
            {USER_HAS_CLAIMS_REPORTS_ROLE && (
              <MenuItem toggleSidebar={toggleSidebar} label="Reports" to="/claims/reports" />
            )}
          </MenuGroup>
        )}
        {hasAdjudicationAccess && USER_HAS_ANY_ADJUDICATION_ROLE && (
          <MenuGroup
            name="Claims Adjudication"
            icon={<ScaleIcon className="h-6 w-6" />}
            hidden={USER_HAS_ROLE_HR || USER_HAS_ROLE_HR_ONE || isJudiciaryAndHasUsername}
          >
            {USER_HAS_CLAIMS_ADJUDICATION_BATCHING_ROLE && (
              <MenuItem
                toggleSidebar={toggleSidebar}
                label="Claim Batching"
                to="/adjudication/batching"
              />
            )}
            {USER_HAS_CLAIMS_ADJUDICATION_VETTING_ROLE && (
              <MenuItem
                toggleSidebar={toggleSidebar}
                label="Claim Vetting"
                to="/adjudication/vetting"
              />
            )}
            {hasAiAccess && USER_HAS_CLAIMS_ADJUDICATION_AI_ROLE && (
              <MenuItem
                toggleSidebar={toggleSidebar}
                label="AI Adjudication"
                to="/adjudication/ai"
              />
            )}
          </MenuGroup>
        )}
        {hasFinanceAccess && USER_HAS_ANY_FINANCE_AND_ACCOUNTING_ROLE && (
          <MenuGroup name="Finance & Accounting" icon={<BanknotesIcon className="h-6 w-6" />}>
            {USER_HAS_FINANCE_AND_ACCOUNTING_VOUCHERING_ROLE && (
              <MenuItem
                label="Vouchering"
                toggleSidebar={toggleSidebar}
                to="/finance-and-accounting/vouchering"
              />
            )}
            {USER_HAS_FINANCE_AND_ACCOUNTING_SETTLEMENT_ROLE && (
              <MenuItem
                label="Settlement"
                toggleSidebar={toggleSidebar}
                to="/finance-and-accounting/settlement/"
              />
            )}
            {USER_HAS_FINANCE_AND_ACCOUNTING_REMITTANCE_ROLE && (
              <MenuItem
                label="Remittance"
                toggleSidebar={toggleSidebar}
                to="/finance-and-accounting/remittance/"
              />
            )}
            {USER_HAS_FINANCE_AND_ACCOUNTING_CREDIT_ANALYSIS_ROLE && (
              <MenuGroup name="Credit Analysis" hasPlusIcon>
                <MenuItem
                  label="Reconciliation"
                  toggleSidebar={toggleSidebar}
                  to="/finance-and-accounting/reconciliation/"
                />
                <MenuItem
                  label="Sign Offs"
                  toggleSidebar={toggleSidebar}
                  to="/finance-and-accounting/sign-offs/"
                />
              </MenuGroup>
            )}
          </MenuGroup>
        )}
        {hasUserManagementAccess && (
          <MenuGroup name="User Management" icon={<UserPlusIcon className="h-6 w-6" />}>
            {USER_HAS_USER_MANAGEMENT_ROLES_ASSIGNMENT_ROLE && (
              <>
                <MenuItem label="Role Assignment" toggleSidebar={toggleSidebar} to="/users" />
                <MenuGroup name="Roles" hasPlusIcon>
                  <MenuItem
                    label="Predefined Roles"
                    toggleSidebar={toggleSidebar}
                    to="/users/roles/predefined"
                  />
                  <MenuItem
                    label="Custom Roles"
                    toggleSidebar={toggleSidebar}
                    to="/users/roles/custom"
                  />
                </MenuGroup>
                <MenuItem label="Groups" toggleSidebar={toggleSidebar} to="/users/groups" />
                <MenuItem
                  label="Deny Policies"
                  toggleSidebar={toggleSidebar}
                  to="/users/deny-policies"
                />
              </>
            )}
            {USER_HAS_USER_MANAGEMENT_AUDIT_LOGS_ROLE && (
              <MenuItem label="Audit Logs" toggleSidebar={toggleSidebar} to="/users/audit-logs" />
            )}
          </MenuGroup>
        )}
        <MenuItem
          label="Contact Us"
          icon={<ChatBubbleOvalLeftIcon className="h-6 w-6" />}
          toggleSidebar={toggleSidebar}
          to="/contact-us"
        />
      </ul>
    </aside>
  );
}
