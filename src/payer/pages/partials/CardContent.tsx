import React, { useState } from "react";
import { Link } from "react-router-dom";

function CardContent() {
  const [sync, setSync] = useState(false);

  return (
    <div className="grid grid-cols-12">
      <div className="col-span-1"></div>
      <div className="col-span-10">
        {/* Panel body */}
        <div className="p-6 space-y-6">
          {/* Picture */}
          <section>
            <h2 className="text-xl text-gray-800 font-bold mb-5">
              Onboarding Process
            </h2>
            <p>Please add the Payer you with to include in this setup </p>
          </section>
          {/* Business Profile */}
          <section>
            {/* Progress bar */}
            <div className="px-4 pt-12 pb-8">
              <div className="max-w-md mx-auto">
                <div className="relative">
                  <div
                    className="absolute left-0 top-1/2 -mt-px w-full h-0.5 bg-gray-200"
                    aria-hidden="true"
                  ></div>
                  <ul className="relative flex justify-between w-full">
                    <li>
                      <Link
                        className="flex items-center justify-center w-6 h-6 rounded-full text-xs font-semibold bg-indigo-500 text-white"
                        to="/onboarding-01"
                      >
                        1
                      </Link>
                    </li>
                    <li>
                      <Link
                        className="flex items-center justify-center w-6 h-6 rounded-full text-xs font-semibold bg-gray-100 text-gray-500"
                        to="/onboarding-02"
                      >
                        2
                      </Link>
                    </li>
                    <li>
                      <Link
                        className="flex items-center justify-center w-6 h-6 rounded-full text-xs font-semibold bg-gray-100 text-gray-500"
                        to="/onboarding-03"
                      >
                        3
                      </Link>
                    </li>
                    <li>
                      <Link
                        className="flex items-center justify-center w-6 h-6 rounded-full text-xs font-semibold bg-gray-100 text-gray-500"
                        to="/onboarding-04"
                      >
                        4
                      </Link>
                    </li>
                    <li>
                      <Link
                        className="flex items-center justify-center w-6 h-6 rounded-full text-xs font-semibold bg-gray-100 text-gray-500"
                        to="/onboarding-04"
                      >
                        5
                      </Link>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </section>
          {/* Email */}
        </div>
        <section className="mb-2 ">Policy Setup Details</section>
        {/* Fields */}
        <div className="grid grid-cols-4 gap-3 mb-40">
          <div className="">
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="country"
              >
                Select a Plan <span className="text-red-500">*</span>
              </label>
              <select id="country" className="form-select w-full">
                <option>USA</option>
                <option>Italy</option>
                <option>United Kingdom</option>
              </select>
            </div>
          </div>
          <div className="">
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="company-name"
              >
                Policy Start Date <span className="text-red-500">*</span>
              </label>
              <input
                id="company-name"
                className="form-input w-full"
                type="text"
              />
            </div>
          </div>
          <div className="">
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="company-name"
              >
                Policy End Date <span className="text-red-500">*</span>
              </label>
              <input
                id="company-name"
                className="form-input w-full"
                type="text"
              />
            </div>
          </div>
          <div className="">
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="company-name"
              >
                Policy Number <span className="text-red-500">*</span>
              </label>
              <input
                id="company-name"
                className="form-input w-full"
                type="text"
              />
            </div>
          </div>
        </div>
        <section className="mb-5 ">Category Setup Details</section>
        {/* Fields */}
        <div className="grid grid-cols-4 gap-3 mt-10">
          <div className="">
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="country"
              >
                Select Policy <span className="text-red-500">*</span>
              </label>
              <select id="country" className="form-select w-full">
                <option>USA</option>
                <option>Italy</option>
                <option>United Kingdom</option>
              </select>
            </div>
          </div>
          <div className="">
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="company-name"
              >
                Category Name <span className="text-red-500">*</span>
              </label>
              <input
                id="company-name"
                className="form-input w-full"
                type="text"
              />
            </div>
          </div>
          <div className="">
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="company-name"
              >
                Category Description <span className="text-red-500">*</span>
              </label>
              <input
                id="company-name"
                className="form-input w-full"
                type="text"
              />
            </div>
          </div>
        </div>

        {/* Panel footer */}
        <footer>
          <div className="grid grid-cols-3 gap-3 mt-10 border-gray-200">
            <div className="flex-none w-14 h-14"></div>
            <div className="grow h-14">
              {" "}
              <button className="btn bg-indigo-500 hover:bg-indigo-600 text-white ml-3">
                Proceed
              </button>
            </div>
            <div className="flex-none w-14 h-14"></div>
          </div>
        </footer>
      </div>
      <div className="col-span-1"></div>
    </div>
  );
}

export default CardContent;
