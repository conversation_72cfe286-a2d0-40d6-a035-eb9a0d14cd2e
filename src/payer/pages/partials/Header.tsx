import { Bars3Icon, ChevronDownIcon } from "@heroicons/react/24/outline";
import { useSelector } from "react-redux";
import Avatar from "../../components/ui/Avatar";
import Dropdown from "../../components/ui/Dropdown";
import { AUTHENTICATED_USER } from "../../lib/payer-constants";
import UserService from "../../services/UserService";
import { RootState } from "../../store";
import NotificationBell from "../../components/NotificationBell";

export default function Header() {
  const payerName = useSelector((state: RootState) => state.payers.payer.name);
  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const scheme = UserService.getPayer()?.tokenParsed?.["scheme"];

  const displayName = AUTHENTICATED_USER.getName() || AUTHENTICATED_USER.getUserName();

  function handleLogout() {
    UserService.doLogout();
  }

  return (
    <header className="mb-6 flex max-w-full items-center gap-2 border-none bg-white pr-8 pt-4">
      <label htmlFor="sidebar-drawer" className="drawer-button px-2 py-2 lg:hidden lg:px-4">
        <Bars3Icon className="h-6 w-6" />
      </label>
      <div className="flex flex-grow items-center justify-between px-2 lg:px-4">
        <h1 className="text-lg font-medium text-darkGray">
          {UserService.hasRole(["HR"]) && payerId === 1 ? scheme : payerName}
        </h1>

        <div className="flex items-stretch gap-5 ">
          <NotificationBell />

          <span className="w-px bg-gray-lighter" />

          <div className="flex items-center gap-2">
            <Avatar name={displayName} />
            <p className="text-base font-medium">{displayName}</p>
            <Dropdown
              menuItemsClassName="right-0 mt-2 w-fit"
              itemsClassName="w-fit whitespace-nowrap text-base"
              items={[
                {
                  label: "Log out",
                  onClick: handleLogout,
                },
              ]}
              buttonClassName="p-2 bg-gray-lighter ml-4"
            >
              <ChevronDownIcon className="w-3 text-black" strokeWidth={3} />
            </Dropdown>
          </div>
        </div>
      </div>
    </header>
  );
}
