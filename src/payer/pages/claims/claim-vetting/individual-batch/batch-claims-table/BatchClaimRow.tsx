import { Link, useParams } from "react-router-dom";
import Badge, { BadgeColor } from "../../../../../components/ui/Badge";
import TableDataItem from "../../../../../components/ui/table/TableDataItem";
import { convertDateString } from "../../../../../utils/convertDateString";
import { formatNumberToKes } from "../../../../../utils/formatCurrency";
import { Claim } from "../../types";

type Props = {
  claim: Claim;
};

export default function BatchClaimRow({ claim }: Props) {
  const { batchId } = useParams();

  const badgeColor: BadgeColor =
    claim.vettingStatus === "DECLINED"
      ? "red"
      : claim.vettingStatus === "BATCHED"
        ? "yellow"
        : claim.vettingStatus === "APPROVED"
          ? "green"
          : claim.vettingStatus === "PARTIAL"
            ? "blue"
            : "gray";

  const badgeText =
    claim.vettingStatus === "DECLINED"
      ? "Declined"
      : claim.vettingStatus === "BATCHED"
        ? "Batched"
        : claim.vettingStatus === "APPROVED"
          ? "Approved"
          : claim.vettingStatus === "PARTIAL"
            ? "Partial"
            : claim.vettingStatus;

  return (
    <tr className="border-b font-medium text-slate-500">
      {/* member number */}
      <TableDataItem className="text-xs" item={claim.memberNumber} />
      {/* member name */}
      <TableDataItem className="text-xs" item={claim.memberName} />
      {/* benefit */}
      <TableDataItem className="text-xs" item={claim.benefitName} />
      {/* invoice date */}
      <TableDataItem className="text-xs" item={convertDateString(claim.invoiceDate, ".")} />
      {/* batch invoice number */}
      <TableDataItem className="text-xs" item={claim.batchInvoiceNumber || "-"} />
      {/* total amount */}
      <TableDataItem className="text-xs" item={formatNumberToKes(claim.totalAmount)} />

      {/* provider name */}
      <TableDataItem className="text-xs" item={claim.providerName} />
      {/* status */}
      <td className="px-4 py-2">
        <Badge color={badgeColor} text={badgeText} textClassName="text-xs" />
      </td>
      {/* action */}
      <td className="px-4 py-2">
        <Link
          className="rounded-md bg-blue-600 px-3 py-2 text-sm text-white"
          to={`/adjudication/vetting/batch/${batchId}/claim/${claim.id}`}
        >
          View
        </Link>
      </td>
    </tr>
  );
}
