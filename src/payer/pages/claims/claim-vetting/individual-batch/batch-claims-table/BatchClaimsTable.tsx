import TableHeaderItem from "../../../../../components/ui/table/TableHeaderItem";
import { Claim } from "../../types";
import BatchClaimRow from "./BatchClaimRow";

type Props = {
  claims: Claim[] | undefined;
};

export default function BatchClaimsTable({ claims }: Props) {
  return (
    <table className="mt-8 w-full  table-auto border-collapse text-left text-xs">
      <thead className="border-b bg-[#F9FAFB]">
        <tr className="text-slate-700">
          <TableHeaderItem className="text-xs" item="MEMBER NUMBER" />
          <TableHeaderItem className="text-xs" item="MEMBER NAME" />
          <TableHeaderItem className="text-xs" item="BENEFIT" />
          <TableHeaderItem className="text-xs" item="INVOICE DATE" />
          <TableHeaderItem className="text-xs" item="BATCH INVOICE NUMBER" />
          <TableHeaderItem className="text-xs" item="TOTAL AMOUNT" />
          <TableHeaderItem className="text-xs" item="PROVIDER NAME" />
          <TableHeaderItem className="text-xs" item="STATUS" />
          <TableHeaderItem className="text-xs" item="ACTION" />
        </tr>
      </thead>

      <tbody className="">
        {claims?.map((claim) => <BatchClaimRow key={claim.id} claim={claim} />)}
      </tbody>
    </table>
  );
}
