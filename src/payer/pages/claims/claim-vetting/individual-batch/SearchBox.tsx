import React from "react";
import FilterIcon from "../../../../components/icons/FilterIcon";
import Button from "../../../../components/ui/Button";
import SearchInput from "../../../../components/ui/input/SearchInput";

type Props = {
  query: string;
  setQuery: React.Dispatch<React.SetStateAction<string>>;
  isShowFilters: boolean;
  setIsShowFilters: React.Dispatch<React.SetStateAction<boolean>>;
};

export default function SearchBox({ query, setQuery, setIsShowFilters, isShowFilters }: Props) {
  return (
    <div className="flex items-center justify-center gap-4 text-xs ">
      <form className="relative flex  items-center justify-center ">
        <SearchInput
          placeholder="Search by Member Number"
          className="h-[24px] w-[350px] grow overflow-hidden  rounded-md border border-slate-400 px-2 py-4 pl-7 text-xs   "
          value={query}
          onChange={(e) => setQuery(e.target.value)}
        />
      </form>

      <Button onClick={() => setIsShowFilters(!isShowFilters)}>
        <FilterIcon />
        <span>Filters</span>
      </Button>
    </div>
  );
}
