import { useState } from "react";
import { TailSpin } from "react-loader-spinner";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import {
  useGetBatchClaimsQuery,
  useGetBatchQuery,
} from "../../../../api/claims-vetting-api/claimVettingApi";
import { BatchClaimsRequestArgs } from "../../../../api/claims-vetting-api/requestArgsTypes";
import LeftArrowIcon from "../../../../components/icons/LeftArrowIcon";
import Badge from "../../../../components/ui/Badge";
import PrimaryPagination from "../../../../components/ui/pagination/PrimaryPagination";
import Text from "../../../../components/ui/typography/Text";
import UserService from "../../../../services/UserService";
import { UserInfo } from "../types";
import BatchClaimsFilters from "./batch-claims-filters/BatchClaimsFilters";
import BatchClaimsTable from "./batch-claims-table/BatchClaimsTable";
import SearchBox from "./SearchBox";
import MainWrapper from "../../../../components/ui/MainWrapper";

export default function Batch() {
  const { batchId } = useParams();
  const navigate = useNavigate();

  const userInfo = UserService.kcObject.tokenParsed as UserInfo;

  // filter states
  const [shouldShowFilters, setShouldShowFilters] = useState(false);
  const [query, setQuery] = useState("");
  const [providerId, setProviderId] = useState("");
  const [planIds, setPlanIds] = useState("");
  const [ageBandId, setAgeBandId] = useState("");
  const [catalogIds, setCatalogIds] = useState("");

  // pagination states
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(8);

  const queryParams: BatchClaimsRequestArgs = {
    payerId: userInfo.payerId,
    batchIds: Number(batchId),
    page: Number(page),
    size: size,
    query,
    ...(providerId && { providerId: Number(providerId) }),
    ...(planIds && { planIds: Number(planIds) }),
    ...(ageBandId && { ageBandId: Number(ageBandId) }),
    ...(catalogIds && { catalogIds: Number(catalogIds) }),
  };

  const { data: batchData, isLoading: isLoadingBatch } = useGetBatchQuery(Number(batchId));
  const { data, isLoading, isError, isFetching } = useGetBatchClaimsQuery(queryParams);

  const batch = batchData?.data;

  const claims = data?.data.content || [];
  const totalPages = data?.data.totalPages;
  const totalElements = data?.data.totalElements;

  if (isError) {
    toast.error("Error getting claims!");
  }

  const badgeColor =
    batch?.batchStatus === "UnVetted"
      ? "red"
      : batch?.batchStatus === "InProgress"
        ? "yellow"
        : batch?.batchStatus === "Completed"
          ? "green"
          : "gray";

  const badgeText =
    batch?.batchStatus === "UnVetted"
      ? "Unvetted"
      : batch?.batchStatus === "InProgress"
        ? "Vetting"
        : batch?.batchStatus === "Completed"
          ? "Vetted"
          : batch?.batchStatus;

  return (
    <MainWrapper>
      <div className="flex justify-between">
        <div className="flex items-center gap-4">
          <button
            data-testid="back-button"
            className="rounded-md bg-slate-300 p-2"
            onClick={() => navigate(-1)}
          >
            <LeftArrowIcon />
          </button>

          <Text variant="heading" className="text-lg">
            Batch List / <span className="text-black">BATCH {batchId}</span>
          </Text>

          {!isLoadingBatch && <Badge color={badgeColor} text={badgeText as string} />}
        </div>

        {/* Search box */}
        <SearchBox
          isShowFilters={shouldShowFilters}
          query={query}
          setIsShowFilters={setShouldShowFilters}
          setQuery={setQuery}
        />
      </div>

      {/* Filters */}
      {shouldShowFilters && (
        <BatchClaimsFilters
          providerId={providerId}
          setProviderId={setProviderId}
          payerId={userInfo.payerId}
          planIds={planIds}
          setPlanIds={setPlanIds}
          ageBandId={ageBandId}
          setAgeBandId={setAgeBandId}
          catalogIds={catalogIds}
          setCatalogIds={setCatalogIds}
        />
      )}

      {/* Table */}
      {isLoading || isFetching ? (
        <div
          data-testid="loading-spinner"
          className="flex h-full w-full items-center justify-center"
        >
          <TailSpin color="blue" />
        </div>
      ) : isError ? (
        <p className="mt-8 text-lg text-red-600">Error getting claims</p>
      ) : (
        claims?.length > 0 && (
          <>
            <BatchClaimsTable claims={claims} />
            <PrimaryPagination
              onPageNumberClick={setPage}
              onSizeChange={setSize}
              pageNumber={page}
              pageSize={size}
              totalElements={totalElements as number}
              totalPages={totalPages as number}
            />
          </>
        )
      )}
    </MainWrapper>
  );
}
