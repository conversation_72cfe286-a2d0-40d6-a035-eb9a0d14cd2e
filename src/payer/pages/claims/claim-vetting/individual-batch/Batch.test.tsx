import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { Provider } from "react-redux";
import { MemoryRouter, Route, Routes } from "react-router-dom";
import { Mock, vi } from "vitest";
import {
  useGetBatchClaimsQuery,
  useGetBatchQuery,
} from "../../../../api/claims-vetting-api/claimVettingApi";
import { store } from "../../../../store";
import { Claim } from "../types";
import Batch from "./Batch";

// Create a spy for navigation
const mockNavigate = vi.fn();

// Partially mock "react-router-dom" to override useNavigate while keeping other exports.
vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

vi.mock("../../../../api/claims-vetting-api/claimVettingApi", async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...(typeof actual === "object" && actual !== null ? actual : {}),
    useGetBatchClaimsQuery: vi.fn(),
    useGetBatchQuery: vi.fn(),
  };
});

vi.mock("../../../../services/UserService", () => ({
  default: {
    kcObject: {
      tokenParsed: { payerId: 123 },
    },
  },
}));

describe("Batch Component", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders loading spinner when data is loading", () => {
    (useGetBatchQuery as Mock).mockReturnValue({ data: null, isLoading: true });
    (useGetBatchClaimsQuery as Mock).mockReturnValue({
      data: null,
      isLoading: true,
      isFetching: false,
    });

    render(
      <Provider store={store}>
        <MemoryRouter>
          <Batch />
        </MemoryRouter>
      </Provider>,
    );

    expect(screen.getByTestId("loading-spinner")).toBeInTheDocument();
  });

  it("displays error message when API call fails", () => {
    (useGetBatchClaimsQuery as Mock).mockReturnValue({
      data: null,
      isLoading: false,
      isError: true,
    });

    render(
      <Provider store={store}>
        <MemoryRouter>
          <Batch />
        </MemoryRouter>
      </Provider>,
    );

    expect(screen.getByText("Error getting claims")).toBeInTheDocument();
  });

  it("renders batch data when API call succeeds", () => {
    (useGetBatchQuery as Mock).mockReturnValue({
      data: { data: { batchStatus: "Completed" } },
      isLoading: false,
    });
    (useGetBatchClaimsQuery as Mock).mockReturnValue({
      data: {
        data: {
          content: [{ id: "1", invoiceDate: "1/1/2025" }] as unknown as Claim[],
          totalPages: 1,
          totalElements: 1,
        },
      },
      isLoading: false,
      isError: false,
    });

    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={["/adjudication/vetting/batch/356"]}>
          <Routes>
            <Route path="/adjudication/vetting/batch/:batchId" element={<Batch />} />
          </Routes>
        </MemoryRouter>
      </Provider>,
    );

    expect(screen.getByText("BATCH 356")).toBeInTheDocument();
    expect(screen.getByText("Vetted")).toBeInTheDocument();
  });

  it("navigates back when the back button is clicked", async () => {
    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={["/adjudication/vetting/batch/356"]}>
          <Routes>
            <Route path="/adjudication/vetting/batch/:batchId" element={<Batch />} />
          </Routes>
        </MemoryRouter>
      </Provider>,
    );

    await userEvent.click(screen.getByTestId("back-button"));

    // Assert that our mocked navigate function was called with -1
    expect(mockNavigate).toHaveBeenCalledWith(-1);
  });
});
