import { fireEvent, render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import FilterInput from "./FilterInput";
import { Provider } from "../../types";

const mockItems = [
  { id: 1, name: "Provider 1" },
  { id: 2, name: "Provider 2" },
  { id: 3, name: "Provider 3" },
];

describe("FilterInput", () => {
  it("renders the input field with the placeholder", () => {
    render(
      <FilterInput
        placeHolder="Search"
        items={mockItems as Provider[]}
        itemId=""
        setItemId={vi.fn()}
      />,
    );

    const input = screen.getByPlaceholderText("Search");
    expect(input).toBeInTheDocument();
  });

  it("filters items based on the search query", async () => {
    render(
      <FilterInput
        placeHolder="Search"
        items={mockItems as Provider[]}
        itemId=""
        setItemId={vi.fn()}
      />,
    );

    const input = screen.getByPlaceholderText("Search");

    // Simulate typing in the input field
    fireEvent.change(input, { target: { value: "Provider 1" } });

    // Expect the list to show the filtered results
    const option = await screen.findByText("Provider 1");
    expect(option).toBeInTheDocument();
  });

  it("shows 'Nothing found' if no items match the search query", async () => {
    render(
      <FilterInput
        placeHolder="Search"
        items={mockItems as Provider[]}
        itemId=""
        setItemId={vi.fn()}
      />,
    );

    const input = screen.getByPlaceholderText("Search");

    // Simulate typing a search that doesn't match any items
    fireEvent.change(input, { target: { value: "Nonexistent" } });

    // Expect the 'Nothing found' message to be shown
    const message = await screen.findByText("Nothing found.");
    expect(message).toBeInTheDocument();
  });

  it("calls setItemId when an item is selected", async () => {
    const setItemId = vi.fn();
    render(
      <FilterInput
        placeHolder="Search"
        items={mockItems as Provider[]}
        itemId="1"
        setItemId={setItemId}
      />,
    );

    const input = screen.getByPlaceholderText("Search");

    // Simulate typing and selecting an item
    fireEvent.change(input, { target: { value: "Provider 2" } });
    const option = await screen.findByText("Provider 2");

    fireEvent.click(option);

    // Expect setItemId to be called with the correct value
    expect(setItemId).toHaveBeenCalledWith(2);
  });

  it("displays the selected item's name in the input field", () => {
    render(
      <FilterInput
        placeHolder="Search"
        items={mockItems as Provider[]}
        itemId="2"
        setItemId={vi.fn()}
      />,
    );

    const input = screen.getByPlaceholderText("Search");
    expect(input).toHaveValue("Provider 2");
  });
});
