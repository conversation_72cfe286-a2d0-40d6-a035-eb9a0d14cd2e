import {
  useGetAllAgeBandsQuery,
  useGetAllPayerBenefitCatalogsQuery,
  useGetAllPayerProvidersQuery,
  useGetAllPayerSchemesQuery,
} from "../../../../../api/claims-vetting-api/claimVettingApi";
import Button from "../../../../../components/ui/Button";
import SearchableSelect from "../../../../../components/ui/input/SearchableSelect";
import { AgeBand, Plan, Provider } from "../../types";

type Props = {
  payerId: number;
  providerId: string;
  setProviderId: React.Dispatch<React.SetStateAction<string>>;
  planIds: string;
  setPlanIds: React.Dispatch<React.SetStateAction<string>>;
  ageBandId: string;
  setAgeBandId: React.Dispatch<React.SetStateAction<string>>;
  catalogIds: string;
  setCatalogIds: React.Dispatch<React.SetStateAction<string>>;
};

export default function BatchClaimsFilters({
  payerId,
  providerId,
  setProviderId,
  planIds,
  setPlanIds,
  ageBandId,
  setAgeBandId,
  catalogIds,
  setCatalogIds,
}: Props) {
  // get providers
  const { data: providerData, isLoading: isLoadingProviders } =
    useGetAllPayerProvidersQuery(payerId);
  const providers: Provider[] = providerData?.data.content as Provider[];

  // get schemes
  const { data: schemesData, isLoading: isLoadingSchemes } = useGetAllPayerSchemesQuery(payerId);
  const schemes: Plan[] = schemesData?.data || [];

  // get age bands
  const { data: ageBandsData, isLoading: isLoadingAgeBands } = useGetAllAgeBandsQuery();
  const ageBands: AgeBand[] = ageBandsData?.data || [];

  // get benefits
  const { data: benefitsData, isLoading: isLoadingBenefits } =
    useGetAllPayerBenefitCatalogsQuery(payerId);
  const benefits = benefitsData?.data || [];

  function handleClearFilters() {
    setProviderId("");
    setPlanIds("");
    setAgeBandId("");
    setCatalogIds("");
  }

  return (
    <div className="mt-8 flex gap-4 text-xs">
      <div className="grid grow grid-cols-4 gap-4">
        <div>
          <p className="font-medium">Provider</p>
          {!isLoadingProviders && providers && (
            <SearchableSelect
              placeHolder="Select the main provider"
              onChange={(value) => setProviderId(value)}
              options={providers.map((provider) => ({
                id: provider.id,
                name: provider.name,
                value: provider.id,
              }))}
              value={providerId}
            />
          )}
        </div>
        <div>
          <p className="font-medium">Scheme</p>
          {!isLoadingSchemes && schemes && (
            <SearchableSelect
              placeHolder="Select the main scheme"
              onChange={(value) => setPlanIds(value)}
              options={schemes.map((schemes) => ({
                id: schemes.id,
                name: schemes.name,
                value: schemes.id,
              }))}
              value={planIds}
            />
          )}
        </div>
        <div>
          <p className="font-medium">Aging</p>
          {!isLoadingAgeBands && ageBands && (
            <SearchableSelect
              placeHolder="Select the aging criteria"
              onChange={(value) => setAgeBandId(value)}
              options={ageBands.map((ageBand) => ({
                id: ageBand.id,
                name: ageBand.name,
                value: ageBand.id,
              }))}
              value={ageBandId}
            />
          )}
        </div>
        <div>
          <p className="font-medium">Benefit</p>
          {!isLoadingBenefits && benefits && (
            <SearchableSelect
              placeHolder="Select the benefit"
              onChange={(value) => setCatalogIds(value)}
              options={benefits.map((benefit) => ({
                id: benefit.id,
                name: benefit.name,
                value: benefit.id,
              }))}
              value={catalogIds}
            />
          )}
        </div>
      </div>

      <div className="flex whitespace-nowrap">
        <Button
          className="mt-auto w-full "
          disabled={!providerId && !planIds && !ageBandId && !catalogIds}
          onClick={handleClearFilters}
        >
          Clear filters
        </Button>
      </div>
    </div>
  );
}
