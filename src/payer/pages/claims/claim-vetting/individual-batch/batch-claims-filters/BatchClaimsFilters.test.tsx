import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { describe, expect, it, Mock, vi } from "vitest";
import {
  useGetAllAgeBandsQuery,
  useGetAllPayerBenefitCatalogsQuery,
  useGetAllPayerProvidersQuery,
  useGetAllPayerSchemesQuery,
} from "../../../../../api/claims-vetting-api/claimVettingApi";
// import { AgeBand, Plan, Provider } from "../../types";
import BatchClaimsFilters from "./BatchClaimsFilters";
import userEvent from "@testing-library/user-event";

vi.mock("../../../../../api/claims-vetting-api/claimVettingApi");

const mockProviders = [
  { id: 1, name: "Provider 1" },
  { id: 2, name: "Provider 2" },
];

const mockSchemes = [
  { id: 1, name: "Scheme 1" },
  { id: 2, name: "Scheme 2" },
];

const mockAgeBands = [
  { id: 1, name: "Age Band 1" },
  { id: 2, name: "Age Band 2" },
];

const mockBenefits = [
  { id: 1, name: "Benefit 1" },
  { id: 2, name: "Benefit 2" },
];

describe("BatchClaimsFilters", () => {
  it("displays options when data is loaded", async () => {
    (useGetAllPayerProvidersQuery as Mock).mockReturnValue({
      data: { data: { content: mockProviders } },
      isLoading: false,
    });
    (useGetAllPayerSchemesQuery as Mock).mockReturnValue({
      data: { data: mockSchemes },
      isLoading: false,
    });
    (useGetAllAgeBandsQuery as Mock).mockReturnValue({
      data: { data: mockAgeBands },
      isLoading: false,
    });
    (useGetAllPayerBenefitCatalogsQuery as Mock).mockReturnValue({
      data: { data: mockBenefits },
      isLoading: false,
    });

    render(
      <BatchClaimsFilters
        payerId={1}
        providerId=""
        setProviderId={vi.fn()}
        planIds=""
        setPlanIds={vi.fn()}
        ageBandId=""
        setAgeBandId={vi.fn()}
        catalogIds=""
        setCatalogIds={vi.fn()}
      />,
    );

    // Ensure the options for each filter are rendered
    expect(screen.getByPlaceholderText("Select the main provider")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Select the main scheme")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Select the aging criteria")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Select the benefit")).toBeInTheDocument();
  });

  it("updates providerId when an option is selected", async () => {
    const setProviderId = vi.fn();
    (useGetAllPayerProvidersQuery as Mock).mockReturnValue({
      data: { data: { content: mockProviders } },
      isLoading: false,
    });

    render(
      <BatchClaimsFilters
        payerId={1}
        providerId=""
        setProviderId={setProviderId}
        planIds=""
        setPlanIds={vi.fn()}
        ageBandId=""
        setAgeBandId={vi.fn()}
        catalogIds=""
        setCatalogIds={vi.fn()}
      />,
    );

    const providerInput = screen.getByPlaceholderText("Select the main provider");

    // First, click to open the dropdown (if required by your component)
    await userEvent.click(providerInput);

    // Then type the text
    await userEvent.type(providerInput, "Provider 1");

    // Wait for the option to appear
    const option = await screen.findByText("Provider 1");
    await userEvent.click(option);

    expect(setProviderId).toHaveBeenCalledWith(1);
  });

  it("calls handleClearFilters when 'Clear filters' button is clicked", async () => {
    const setProviderId = vi.fn();
    const setPlanIds = vi.fn();
    const setAgeBandId = vi.fn();
    const setCatalogIds = vi.fn();

    (useGetAllPayerProvidersQuery as Mock).mockReturnValue({
      data: { data: { content: mockProviders } },
      isLoading: false,
    });
    (useGetAllPayerSchemesQuery as Mock).mockReturnValue({
      data: { data: mockSchemes },
      isLoading: false,
    });
    (useGetAllAgeBandsQuery as Mock).mockReturnValue({
      data: { data: mockAgeBands },
      isLoading: false,
    });
    (useGetAllPayerBenefitCatalogsQuery as Mock).mockReturnValue({
      data: { data: mockBenefits },
      isLoading: false,
    });

    render(
      <BatchClaimsFilters
        payerId={1}
        providerId="1"
        setProviderId={setProviderId}
        planIds="1"
        setPlanIds={setPlanIds}
        ageBandId="1"
        setAgeBandId={setAgeBandId}
        catalogIds="1"
        setCatalogIds={setCatalogIds}
      />,
    );

    fireEvent.click(screen.getByText("Clear filters"));
    await waitFor(() => {
      expect(setProviderId).toHaveBeenCalledWith("");
      expect(setPlanIds).toHaveBeenCalledWith("");
      expect(setAgeBandId).toHaveBeenCalledWith("");
      expect(setCatalogIds).toHaveBeenCalledWith("");
    });
  });

  it("disables 'Clear filters' button if no filters are selected", () => {
    const setProviderId = vi.fn();
    const setPlanIds = vi.fn();
    const setAgeBandId = vi.fn();
    const setCatalogIds = vi.fn();

    render(
      <BatchClaimsFilters
        payerId={1}
        providerId=""
        setProviderId={setProviderId}
        planIds=""
        setPlanIds={setPlanIds}
        ageBandId=""
        setAgeBandId={setAgeBandId}
        catalogIds=""
        setCatalogIds={setCatalogIds}
      />,
    );

    expect(screen.getByText("Clear filters")).toBeDisabled();
  });
});
