export type PreAuth = {
  id: number;
  time: string;
  status: string;
  draft: boolean;
  preauthType: string;
  requestAmount: number;
  initialRequestAmount: number;
  authorizedAmount: number;
  initialAuthorizedAmount: number;
  balanceAmount: number;
  reversedAmount: number;
  requester: string;
  authorizer: string;
  rejectBy: string;
  notes: string;
  initialNotes: string;
  authorizationNotes: string;
  rejectNotes: string;
  validForDays: number;
  visit: {
    id: number;
    memberNumber: string;
    memberName: string;
    hospitalProviderId: number;
    staffId: string;
    staffName: string;
    aggregateId: string;
    categoryId: string;
    benefitName: string;
    beneficiaryId: number;
    benefitId: number;
    payerId: string;
    policyNumber: string;
    balanceAmount: number;
    beneficiaryType: string;
    totalInvoiceAmount: number;
    providerMiddleware: string;
    invoiceNumber: string;
    status: string;
    middlewareStatus: string;
    claimProcessStatus: string;
    cancelReason: string;
    createdAt: string;
    updatedAt: string;
    visitEnd: string;
    diagnosis: {
      id: number;
      code: string;
      title: string;
      invoiceNumber: string;
      claimRef: string;
    }[];
    procedures: {
      id: number;
      procedure_code: string;
      procedure_description: string;
      invoiceNumber: string;
      createdAt: string;
      updatedAt: string;
    }[];
    documents: {
      id: number;
      providerName: string;
      providerId: number;
      type: string;
      fileUrl: string;
      invoiceNumber: string;
      createdAt: string;
      updatedAt: string;
      visit: string;
    }[];
    visitType: string;
    offSystemReason: string;
    reimbursementProvider: string;
    reimbursementInvoiceDate: string;
    reimbursementReason: string;
    payerStatus: string;
    facilityType: string;
    serviceGroup: string;
    invoices: {
      id: number;
      hospitalProviderId: number;
      invoiceNumber: string;
      service: string;
      totalAmount: number;
      payableAmount: number;
      deductibleAmount: number;
      actionedBy: string;
      claimRef: string;
      batchInvoiceNumber: string;
      batchInvoiceReference: string;
      status: string;
      transType: string;
      batchStatus: string;
      vettingStatus: string;
      vetDate: string;
      benefitId: number;
      invoiceLines: {
        id: number;
        lineTotal: number;
        description: string;
        invoiceNumber: string;
        quantity: number;
        unitPrice: number;
        lineType: string;
        claimRef: string;
        lineCategory: string;
        invoice: string;
        createdAt: string;
      }[];
      deductibles: {
        id: number;
        deductibleType: string;
        amount: number;
        invoice: string;
      }[];
      visit: string;
      dispatched: boolean;
      createdAt: string;
      payerStatus: string;
      payerMessage: string;
      paymentStatus: string;
      paymentReference: string;
      paymentDate: string;
    }[];
    preAuths: string[];
    providerMapping: string;
    benefitMapping: string;
    payerClaimReference: string;
    invoiceDate: string;
    bigQueryPicked: boolean;
    providerName: string;
    referralVisit: string;
    referToProviderId: number;
    referToProviderName: string;
    referToDoctor: string;
    referNarration: string;
    vettingStatus: string;
    batchNo: string;
    categoryName: string;
    categoryDescription: string;
    schemeName: string;
    payerName: string;
    otherNumber: string;
    supportDocuments: {
      id: number;
      providerName: string;
      providerId: number;
      type: string;
      fileUrl: string;
      invoiceNumber: string;
      createdAt: string;
      updatedAt: string;
      visit: string;
    }[];
    lineItems: {
      id: number;
      lineTotal: number;
      description: string;
      invoiceNumber: string;
      quantity: number;
      unitPrice: number;
      lineType: string;
      claimRef: string;
      lineCategory: string;
      invoice: string;
      createdAt: string;
    }[];
    visitReference: string;
  };
  service: string;
  requestType: string;
  reference: string;
  utilization: number;
  procedureQuantity: number;
  doctorName: string;
  supportingDocuments: string[];
  preDiagnosisCodes: string[];
  procedureCode: string;
  toothNumber: number;
  numberOfTeeth: number;
  causeOfIllness: string;
  surgeryRequired: boolean;
  lensDetails: string;
  typeOfLens: string;
  frameMake: string;
  natureOfInjury: string;
  causeOfAccident: string;
  natureOfAccident: string;
  dateOfAccident: string;
  dateOfAdmission: string;
  expedited: boolean;
  typeOfSurgery: string;
  procedureType: string;
  numberOfSessions: number;
  referredFrom: string;
  dueDate: string;
  bedType: string;
  bedCharges: number;
  netOfNHIFLimit: number;
  doctorFees: number;
  prescriptionType: string;
  prescriptionReason: string;
  otherPrescriptionReason: string;
  otherProcedure: string;
  surgeryDetails: string;
  underlyingCondition: string;
  conditionCongenital: boolean;
  conditionRecurrent: boolean;
  dateOfProcedure: string;
  previousComplication: string;
  caesareanType: string;
  priorCaesarean: string;
  physiotherapySessions: string;
  amountPerSession: number;
  radiologyType: string;
  dateOfFirstDiagnosis: string;
  natureOfTreatment: string;
  dentalConditionType: string;
  specialistReferral: string;
  framesCost: number;
  lensesCost: number;
  consultationFee: number;
  dentalCost: number;
  allowedDaysOfAdmission: number;
  limit: number;
  createdAt: string;
  updatedAt: string;
  authorizedAt: string;
  markAsIncomplete: boolean;
  markAsIncompleteReason: string;
  parentPreAuth: string;
  sessionTrackers: {
    id: number;
    preAuthorization: string;
    amount: number;
    createdAt: string;
  }[];
  lines: {
    id: number;
    preAuthorization: string;
    name: string;
    tag: string;
    numberOfTeeth: number;
    quantity: number;
    cost: number;
    active: boolean;
    createdAt: string;
  }[];
  diagnosisCodes: {
    id: number;
    preAuthorization: string;
    icdCode: {
      id: number;
      code: string;
      title: string;
      preAuthDiagnosisCodes: string[];
    };
  }[];
  diagnosisCodesRetrieved: boolean;
  guidelines: {
    id: number;
    preAuthorization: string;
    description: string;
    cost: number;
    createdAt: string;
    updatedAt: string;
  }[];
  providerName: string;
  payerName: string;
  schemeName: string;
  diagnosisInfo: {
    id: number;
    code: string;
    title: string;
    preAuthDiagnosisCodes: string[];
  }[];
  procedureInfo: {
    id: number;
    procedure_code: string;
    procedure_description: string;
  };
};
