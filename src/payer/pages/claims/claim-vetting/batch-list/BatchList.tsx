import { CheckCircleIcon, ClockIcon } from "@heroicons/react/24/outline";
import { useState } from "react";
import { Tail<PERSON>pin } from "react-loader-spinner";
import { useSearchParams } from "react-router-dom";
import { useGetBatchListAssignedQuery } from "../../../../api/claims-vetting-api/claimVettingApi";
import progressClock from "../../../../assets/progress_clock.svg";
import NoBatches from "../../../../components/illustrations/NoBatches";
import ButtonGroup, { ButtonGroupOption } from "../../../../components/ui/ButtonGroup";
import EmptyState from "../../../../components/ui/EmptyState";
import MainWrapper from "../../../../components/ui/MainWrapper";
import PrimaryPagination from "../../../../components/ui/pagination/PrimaryPagination";
import Text from "../../../../components/ui/typography/Text";
import UserService from "../../../../services/UserService";
import { Batch, UserInfo } from "../types";
import BatchListTable from "./batch-list-table/BatchListTable";
import LoadingAnimation from "../../../../components/animations/LoadingAnimation/LoadingAnimation";

enum VettingStatus {
  UnVetted = "UnVetted",
  InProgress = "InProgress",
  Completed = "Completed",
}

const activeStatuses: ButtonGroupOption<VettingStatus>[] = [
  {
    title: VettingStatus.UnVetted,
    icon: <ClockIcon className="h-5 w-5" />,
  },
  {
    title: VettingStatus.InProgress,
    icon: <img src={progressClock} alt="progress clock" className="h-5 w-5" />,
  },
  {
    title: VettingStatus.Completed,
    icon: <CheckCircleIcon className="h-5 w-5" />,
  },
];

export default function BatchList() {
  const [searchParams, setSearchParams] = useSearchParams();
  const activeStatus: VettingStatus | null =
    (searchParams.get("status") as VettingStatus) || VettingStatus.UnVetted;
  function setActiveStatus(nextStatus: VettingStatus) {
    setSearchParams({ status: nextStatus });
  }

  const [page, setPage] = useState(1);
  const [size, setSize] = useState(6);

  const userInfo = UserService.kcObject.tokenParsed as UserInfo;

  const { data, isLoading, isFetching, isError } = useGetBatchListAssignedQuery({
    payerId: userInfo.payerId,
    page: Number(page),
    size: size,
    batchAllocationStatus: "Allocated",
    invoiceBatchStatus: activeStatus,
    userId: userInfo.preferred_username,
  });

  const batchList = data?.data.content;

  const totalPages = data?.data.totalPages;
  const totalElements = data?.data.totalElements;

  return (
    <MainWrapper>
      <Text variant="heading" className="text-lg">
        Batch List
      </Text>
      <p className="mt-1 text-sm font-medium text-slate-400">
        Please click on the batch to view the claims and vet each individual invoice.
      </p>

      <div className="mt-8">
        <ButtonGroup
          options={activeStatuses}
          activeOption={activeStatus}
          setActiveOption={setActiveStatus}
          titleClassName="text-sm"
        />
      </div>

      {isLoading || isFetching ? (
        <div className="flex h-[60vh] items-center justify-center">
          <LoadingAnimation size={50} />
        </div>
      ) : isError ? (
        <p className="mt-8  text-red-500">An Error occurred while getting the batches</p>
      ) : batchList && batchList.length > 0 ? (
        <>
          <BatchListTable batchList={batchList as Batch[]} />
          <PrimaryPagination
            totalPages={totalPages as number}
            onPageNumberClick={setPage}
            onSizeChange={setSize}
            pageNumber={page}
            pageSize={size}
            totalElements={totalElements as number}
          />
        </>
      ) : (
        <EmptyState
          illustration={<NoBatches />}
          message={{
            title: "Batch List Is Empty",
          }}
        />
      )}
    </MainWrapper>
  );
}
