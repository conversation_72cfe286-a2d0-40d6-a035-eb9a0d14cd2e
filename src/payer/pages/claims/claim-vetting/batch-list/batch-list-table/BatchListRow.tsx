import { Link } from "react-router-dom";
import Badge from "../../../../../components/ui/Badge";
import BatchIdTableDataItem from "../../../../../components/ui/table/BatchIdTableDataItem";
import BatchTableDataItem from "../../../../../components/ui/table/BatchTableDataItem";
import { formatDate, formatValue } from "../../../../../lib/Utils";
import { convertDateString } from "../../../../../utils/convertDateString";
import { Batch } from "../../types";
import { EyeIcon } from "@heroicons/react/24/outline";
import { useEffect, useState } from "react";
import { getDifferenceInDays } from "../../../../../utils/utils";

type Props = {
  batch: Batch;
};

export default function BatchListRow({ batch }: Props) {
  const badgeColor =
    batch.batchStatus === "UnVetted"
      ? "red"
      : batch.batchStatus === "InProgress"
        ? "yellow"
        : batch.batchStatus === "Completed"
          ? "green"
          : "gray";

  const badgeText =
    batch.batchStatus === "UnVetted"
      ? "Unvetted"
      : batch.batchStatus === "InProgress"
        ? "Vetting"
        : batch.batchStatus === "Completed"
          ? "Vetted"
          : batch.batchStatus;

  const [batchAgeString, setBatchAgeString] = useState<string>("");

  useEffect(() => {
    if (batch) {
      if (batch.createdOn) {
        const batchAge = getDifferenceInDays(
          batch.createdOn.replaceAll("/", "."),
          formatDate(new Date(Date.now())),
        );

        const batchAgeString = `${batchAge} ${batchAge === 1 ? "Day" : "Days"}`;
        setBatchAgeString(batchAgeString);
      }
    }
  }, [batch]);

  return (
    <tr className="">
      <BatchIdTableDataItem batchId={batch?.id} />
      <BatchTableDataItem title={"Batch Criteria"} value={batch.batchCriteria} />
      <BatchTableDataItem
        title={"Invoice Count"}
        value={`${batch.invoiceCount} ${batch.invoiceCount > 1 ? "invoices" : "invoice"}`}
      />
      <BatchTableDataItem title={"Total Amount"} value={formatValue(batch.totalAmount)} />
      <BatchTableDataItem title={"Assigned to"} value={batch?.assignedTo?.[0]?.userName} />
      <BatchTableDataItem title={"Created On"} value={convertDateString(batch.createdOn)} />
      <BatchTableDataItem title={"Batch Age"} value={batchAgeString} />
      <td className=" p-1">
        <Badge color={badgeColor} text={badgeText} hasDot textClassName="text-xs" />
      </td>
      <td className="px-4 py-4">
        <Link to={`/adjudication/vetting/batch/${batch.id}`} className="">
          <EyeIcon className="h-6 w-6" />
        </Link>
      </td>
    </tr>
  );
}
