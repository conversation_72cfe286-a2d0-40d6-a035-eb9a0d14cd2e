import { render, screen } from "@testing-library/react";
import BatchListTable from "./BatchListTable";
import { vi } from "vitest";
import { Batch } from "../../types";

vi.mock("./BatchListRow", () => {
  return {
    default: ({ batch }: { batch: Batch }) => (
      <tr data-testid="batch-row">{batch.batchCriteria}</tr>
    ),
  };
});

describe("BatchListTable", () => {
  it("renders the correct number of rows", () => {
    const mockBatchList = [
      {
        id: "1",
        batchCriteria: "Criteria 1",
        invoiceCount: 5,
        totalAmount: 1000,
        batchStatus: "UnVetted",
        assignedTo: [
          {
            name: "<PERSON>",
            value: "123",
            id: "123",
            email: null,
            lastName: null,
            userName: "johndoe",
            firstName: null,
          },
        ],
        batchAge: "2 days",
        createdOn: "2025-02-10",
        status: null,
      },
      {
        id: "2",
        batchCriteria: "Criteria 2",
        invoiceCount: 10,
        totalAmount: 2000,
        batchStatus: "Completed",
        assignedTo: [
          {
            name: "<PERSON>",
            value: "456",
            id: "456",
            email: null,
            lastName: null,
            userName: "janesmith",
            firstName: null,
          },
        ],
        batchAge: "5 days",
        createdOn: "2025-02-08",
        status: null,
      },
    ];

    render(<BatchListTable batchList={mockBatchList as Batch[]} />);

    const rows = screen.getAllByTestId("batch-row");
    expect(rows).toHaveLength(mockBatchList.length);
  });
});
