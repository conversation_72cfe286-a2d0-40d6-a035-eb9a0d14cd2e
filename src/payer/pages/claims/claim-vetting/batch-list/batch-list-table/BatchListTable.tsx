import { Batch } from "../../types";
import BatchListRow from "./BatchListRow";

type Props = {
  batchList: Batch[];
};

export default function BatchListTable({ batchList }: Props) {
  return (
    <table className="mt-8 w-full table-auto border-collapse text-sm">
      <tbody className="">
        {batchList.map((batch) => (
          <BatchListRow key={batch.id} batch={batch} />
        ))}
      </tbody>
    </table>
  );
}
