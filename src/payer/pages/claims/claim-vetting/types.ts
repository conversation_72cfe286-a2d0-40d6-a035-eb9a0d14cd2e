export type UserInfo = {
  email_verified: boolean;
  payerId: number;
  preferred_username: string;
  sub: string;
};

export type SuccessfulServerContentResponse<T> = {
  success: boolean;
  msg: string | null;
  data: {
    content: T[];
    empty: boolean;
    first: boolean;
    last: boolean;
    number: number;
    numberOfElements: number;
    pageable: {
      sort: {
        empty: boolean;
        unsorted: boolean;
        sorted: boolean;
      };
      offset: number;
      pageNumber: number;
      pageSize: number;
      paged: boolean;
      unpaged: boolean;
    };
    size: number;
    sort: {
      empty: boolean;
      unsorted: boolean;
      sorted: boolean;
    };
    totalElements: number;
    totalPages: number;
  };
};

export type SuccessfulServerDataResponse<T> = {
  success: boolean;
  msg: string | null;
  data: T[];
  results?: unknown;
};

export type SuccessfulServerDataObjectResponse<T> = {
  success: boolean;
  msg: string | null;
  data: T;
  results?: unknown;
};

export type SuccessfulUploadResponse = {
  success: boolean;
  msg: string;
  data: string; // url of the file
};

export type SuccessfulSaveDocumentResponse = {
  success: boolean;
  msg: string;
  data: boolean;
};

export type SuccessfulAddProcedureResponse = {
  success: boolean;
  msg: string;
  data: boolean;
};

export type SuccessfulGenericResponse = {
  success: boolean;
  msg: string;
  data: boolean;
};

export type EditInvoiceNumberResponse = {
  success: true;
  msg: string;
  data: true;
};

export type EditInvoiceErrorResponse = {
  timestamp: string;
  code: number;
  error: string;
};

export type Batch = {
  id: string;
  batchCriteria: string;
  invoiceCount: number;
  totalAmount: number;
  batchStatus: "UnVetted" | "InProgress" | "Completed";
  assignedTo: [
    {
      name: string;
      value: string;
      id: string;
      email: null;
      lastName: null;
      userName: string;
      firstName: null;
    },
  ];
  batchAge: string;
  createdOn: string;
  status: null;
};

export type Claim = {
  id: number;
  visitNumber: number;
  hospitalProviderId: number | null;
  invoiceNumber: string;
  service: string | null;
  totalAmount: number;
  payableAmount: number | null;
  deductibleAmount: number | null;
  actionedBy: string | null;
  claimRef: string | null;
  batchInvoiceNumber: string | null;
  status: string;
  transType: string;
  batchStatus: string | null;
  vettingStatus: "PENDING" | "APPROVED" | "DECLINED" | "PARTIAL" | "BATCHED";
  vetDate: string | null;
  benefitId: number | null;
  benefitName: string;
  dispatched: string | null;
  createdAt: string;
  payerStatus: string;
  payerMessage: string | null;
  paymentStatus: string;
  paymentReference: string | null;
  paymentDate: string | null;
  providerName: string;
  schemeName: string;
  payerName: string;
  memberNumber: string;
  memberName: string;
  invoiceDate: string;
  visitType: string; // for claim type
  invoiceLines: LineItem[];
  deductibles: Deductible[];
};

export type Deductible = {
  id: number;
  deductibleType: "COPAYMENT" | "EXCLUSION" | "CASHPAYMENT" | "DISCOUNT" | "NHIF";
  amount: number;
  invoice: unknown;
};

export type LineItem = {
  id: number;
  lineTotal: number;
  description: string;
  invoiceNumber: string;
  quantity: number;
  unitPrice: number;
  lineType: string | null;
  claimRef: string;
  lineCategory: string;
  createdAt: string;
};

export type Provider = {
  id: number;
  name: string;
  email: string | null;
  latitude: number;
  longitude: number;
  tier: string; // Adjust based on possible values
  region: {
    id: number;
    name: string;
    country: {
      id: number;
      name: string;
    };
  };
  baseUrl: string;
  billingStation: boolean;
  billsOnPortal: boolean;
  billsOnHmis: boolean;
  billsOnDevice: boolean;
  billsOnHmisAutomaticClose: boolean;
  canUseOtpVerificationFailOver: boolean;
  usesGlobalBatchInvoice: boolean | null;
  verificationType: string;
  invoiceNumberType: string | null;
  providerMiddleware: string | null;
  useMainHospMiddleware: boolean;
  useBranchMiddleware: boolean;
  isIntergrated: boolean;
  createdOn: string; // ISO 8601 date string
};

export type Plan = {
  id: number;
  name: string;
  type: string;
  planDate: string | null; // Assuming planDate can be a string or null
  accessMode: string;
};

export type AgeBand = {
  id: number;
  name: string;
  description: string;
  minValue: number;
  maxValue: number;
};

export type Benefit = {
  id: number;
  name: string;
  benefitRef: {
    id: number;
    code: string;
    name: string;
    serviceGroup: string;
  };
  applicableGender: string;
  applicableMember: string;
  status: string;
  limit: number;
  suspensionThreshold: number;
  preAuthThreshold: number | null;
  thresholdAction: string;
  preAuthType: string;
  sharing: string;
  coPaymentRequired: boolean;
  coPaymentAmount: number;
  parentBenefit: string | null;
  waitingPeriod: string;
  processed: boolean;
  processedTime: string;
  payer: {
    id: number;
    name: string;
    contact: string;
    email: string;
    website: string | null;
    streetAddress: string | null;
    postalAddress: string | null;
    logo: string | null;
    type: string;
    mainPayer: string | null;
  };
  benefitType: string;
  capitationType: string | null;
  capitationPeriod: string | null;
  visitCountPeriod: string | null;
  capitationMaxVisitCount: number;
  capitationFacilitiesCount: number;
  requireBeneficiaryToSelectProvider: boolean;
  visitLimit: number | null;
  daysOfAdmissionLimit: number;
  amountPerDayLimit: number;
  applicableMinAge: number | null;
  applicableMaxAge: number | null;
  transferable: boolean;
  billable: string | null;
  restriction: string | null;
  changeLog: unknown[]; // todo: Adjust the type if you have specific details about the changeLog
};

export type BenefitCatalog = {
  id: number;
  code: string;
  name: string;
  serviceGroup: string;
};

export type MedicalDrug = {
  id: number;
  name: string;
};

export type Laboratory = {
  id: number;
  name: string;
};

export type Diagnosis = {
  id: number;
  code: string;
  title: string;
  invoiceNumber: string;
  claimRef: string;
};

export type ICD10 = {
  id: number;
  code: string;
  title: string;
};

export type Procedure = {
  id: number;
  procedure_code: string;
  procedure_description: string;
};

export type ProcedureCode = {
  id: number;
  procedure_code: string;
  procedure_description: string;
  invoiceNumber: string;
  createdAt: string;
  updatedAt: string;
};

export type Document = {
  id: number;
  providerName: string;
  providerId: number;
  type: string;
  fileUrl: string;
  invoiceNumber: string;
  createdAt: string;
  updatedAt: string;
};

export type AuditLog = {
  id: 0;
  action: string;
  eventName: string;
  previousValues: string;
  newValues: string;
  actionByUser: string;
  reason: string;
  createdOn: string;
};

export type DeleteInvoiceDocumentRequest = {
  body: { documentIds: number[]; actionedBy: string; reason?: string };
};

export type ResultBoolean = {
  success: boolean;
  msq?: string;
  data?: boolean;
};
