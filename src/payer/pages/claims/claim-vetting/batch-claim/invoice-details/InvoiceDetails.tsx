import { ChevronDownIcon, ChevronUpIcon } from "@heroicons/react/24/outline";
import { useState } from "react";
import PenIcon from "../../../../../components/icons/PenIcon";
import DialogWrapper from "../../../../../components/ui/modal/DialogWrapper";
import { convertDateString } from "../../../../../utils/convertDateString";
import { formatNumberToKes } from "../../../../../utils/formatCurrency";
import { Claim } from "../../types";
import AddLineItemModal from "./AddLineItemModal";
import EditInvoiceNumberModal from "./EditInvoiceNumberModal";
import LineItemsTable from "./LineItemsTable";

type Props = {
  claim: Claim;
};

export default function InvoiceDetails({ claim }: Props) {
  const [isShowEditInvoiceNumberModal, setIsShowEditInvoiceNumberModal] = useState(false);
  const [isShowAddLineItemModal, setIsShowAddLineItemModal] = useState(false);

  const [shouldShowDeductibles, setShouldShowDeductibles] = useState(false);

  return (
    <div>
      {/* Overview and Deductibles */}
      <div className="mt-8 flex flex-col gap-4">
        {/* Overview */}
        <div>
          <p className="text-lg font-semibold">Overview</p>

          <ul className="mt-4 flex flex-col gap-4 text-sm text-slate-600">
            <li className="flex items-center gap-2">
              Invoice Number: <span className="text-black">{claim?.invoiceNumber}</span>{" "}
              {!(
                claim.vettingStatus === "APPROVED" ||
                claim.vettingStatus === "DECLINED" ||
                claim.vettingStatus === "PARTIAL"
              ) && (
                <button
                  data-testid="open-modal-button"
                  onClick={() => setIsShowEditInvoiceNumberModal(true)}
                >
                  <PenIcon />
                </button>
              )}
            </li>

            <li className="flex items-center gap-2">
              Invoice Amount:
              <span className="text-black">{formatNumberToKes(claim?.totalAmount)}</span>
            </li>

            <li className="flex items-center gap-2">
              Invoice Date:{" "}
              <span className="text-black">{convertDateString(claim?.invoiceDate)}</span>
            </li>
          </ul>

          <DialogWrapper
            show={isShowEditInvoiceNumberModal}
            onClose={() => setIsShowEditInvoiceNumberModal(false)}
            maxWidth="max-w-[700px]"
          >
            <EditInvoiceNumberModal
              invoiceId={claim.id}
              invoiceNumber={claim.invoiceNumber}
              setIsShowEditInvoiceNumberModal={setIsShowEditInvoiceNumberModal}
            />
          </DialogWrapper>
        </div>

        <button
          className="mt-2 flex w-fit items-center gap-2 text-xs text-blue-400"
          onClick={() => setShouldShowDeductibles(!shouldShowDeductibles)}
        >
          <span>Show {shouldShowDeductibles ? "less" : "more"} ...</span>
          {shouldShowDeductibles ? (
            <ChevronUpIcon className="w-4" />
          ) : (
            <ChevronDownIcon className="w-4" />
          )}
        </button>

        {/* Deductibles */}
        {shouldShowDeductibles && (
          <div>
            <p className="text-lg font-semibold">Deductibles</p>

            <ul className="mt-4 flex flex-col gap-4 text-sm text-slate-600">
              {claim.deductibles.length < 1 && <li>No Deductible available</li>}
              {claim.deductibles.map((deductible) =>
                deductible.deductibleType === "COPAYMENT" ? (
                  <li className="flex items-center gap-2">
                    Co-payment: <span className="text-black">{deductible.amount}</span>
                  </li>
                ) : deductible.deductibleType === "NHIF" ? (
                  <li className="flex items-center gap-2">
                    SHIF Amount: <span className="text-black">{deductible.amount}</span>
                  </li>
                ) : deductible.deductibleType === "DISCOUNT" ? (
                  <li className="flex items-center gap-2">
                    Discount: <span className="text-black">{deductible.amount}</span>
                  </li>
                ) : (
                  <li>No Deductible available</li>
                ),
              )}
            </ul>
          </div>
        )}
      </div>

      {/* Line items review */}
      <div className="mt-8">
        <div className="flex items-center justify-between">
          <p className="text-lg font-semibold">Line items review</p>
          {claim?.vettingStatus !== "APPROVED" &&
            claim?.vettingStatus !== "DECLINED" &&
            claim?.vettingStatus !== "PARTIAL" && (
              <button
                className="rounded-md bg-blue-600 px-3 py-2 text-xs text-white"
                onClick={() => setIsShowAddLineItemModal(true)}
              >
                + Add Line Item
              </button>
            )}
        </div>

        <LineItemsTable lineItems={claim?.invoiceLines} />

        <DialogWrapper
          show={isShowAddLineItemModal}
          onClose={() => setIsShowAddLineItemModal(false)}
          maxWidth="max-w-[1000px]"
        >
          <AddLineItemModal
            claim={claim}
            setIsShowAddLineItemModal={setIsShowAddLineItemModal}
            savedInvoiceLines={claim.invoiceLines}
          />
        </DialogWrapper>
      </div>
    </div>
  );
}
