import React from "react";
import { LineItem } from "../../types";

type Props = {
  number: number;
  lineItem: LineItem;
};

export default function LineItemRow({ lineItem, number }: Props) {
  return (
    <tr className="border-b-2">
      <td className="py-2">{number}</td>
      <td className="py-2">{lineItem.lineType}</td>
      <td className="py-2">{lineItem.description}</td>
      <td className="py-2">{lineItem.quantity}</td>
      <td className="py-2">{lineItem.unitPrice}</td>
      <td className="py-2">{lineItem.lineTotal}</td>
    </tr>
  );
}
