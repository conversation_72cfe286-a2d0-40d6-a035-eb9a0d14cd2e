import { render, screen, fireEvent } from "@testing-library/react";
import InvoiceLines from "./InvoiceLines";
import { vi } from "vitest";
import { LineItem } from "../../types";

const mockInvoiceLines = [
  { lineType: "Service", description: "Consultation", quantity: 1, unitPrice: 100, lineTotal: 100 },
  {
    lineType: "Medication",
    description: "Painkillers",
    quantity: 2,
    unitPrice: 50,
    lineTotal: 100,
  },
];

describe("InvoiceLines", () => {
  it("removes an invoice line when remove button is clicked", () => {
    const setInvoiceLines = vi.fn();

    render(
      <InvoiceLines
        invoiceLines={mockInvoiceLines as LineItem[]}
        setInvoiceLines={setInvoiceLines}
      />,
    );

    const removeButtons = screen.getAllByRole("button");
    expect(removeButtons).toHaveLength(mockInvoiceLines.length);

    fireEvent.click(removeButtons[0] as HTMLElement);
    expect(setInvoiceLines).toHaveBeenCalledWith([
      {
        lineType: "Medication",
        description: "Painkillers",
        quantity: 2,
        unitPrice: 50,
        lineTotal: 100,
      },
    ]);
  });
});
