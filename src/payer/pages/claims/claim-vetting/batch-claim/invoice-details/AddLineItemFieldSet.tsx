import { Listbox } from "@headlessui/react";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import {
  useGetLaboratoriesQuery,
  useGetMedicalDrugsQuery,
} from "../../../../../api/claims-vetting-api/claimVettingApi";
import checkIfObjectsAreEqual from "../../../../../utils/checkIfObjectsAreEqual";
import { Laboratory, LineItem, MedicalDrug } from "../../types";
import { LineItemType } from "./AddLineItemModal";
import InvoiceCombobox from "./InvoiceComboBox";
import InvoiceLines from "./InvoiceLines";

export type AddLineItemFieldSetProps = {
  type: LineItemType;
  setType: React.Dispatch<React.SetStateAction<"" | LineItemType>>;
  description: string;
  setDescription: React.Dispatch<React.SetStateAction<string>>;
  quantity: string;
  setQuantity: React.Dispatch<React.SetStateAction<string>>;
  unitPrice: string;
  setUnitPrice: React.Dispatch<React.SetStateAction<string>>;
  totalCost: string;
  invoiceLines: LineItem[];
  setInvoiceLines: React.Dispatch<React.SetStateAction<LineItem[]>>;
  claimInvoiceAmount: number;
  savedInvoiceLines: LineItem[];
};

const LINE_ITEM_TYPES: LineItemType[] = [
  "CONSULTATION",
  "PHARMACY",
  "LABORATORY",
  "RADIOLOGY",
  "OTHER",
  //*commented because from previous implementation, these were left out
  // "MEDICALPROCEDURE",
  // "DIAGNOSIS",
  // "INPATIENT",
];

const CONSULTATION_DESCRIPTIONS = [
  "Cardiologist",
  "General Practitioner",
  "Dental",
  "Dermatology",
  "Ear Nose & Throat (ENT)",
  "Gynaecologist",
  "Neurosurgeon",
  "Nutritionist",
  "Orthopaedic",
  "Paediatric Neurologist",
  "Pediatrician",
  "Physician",
  "Psychiatrist",
  "Radiologist",
  "Radiotherapy",
  "Rheumatologist",
  "Specialist",
  "Urology",
];

const RADIOLOGY_OPTIONS = [
  {
    label: "X-Ray",
    value: "X-Ray",
  },
  {
    label: "MRI",
    value: "MRI",
  },
  {
    label: "CT Scan",
    value: "CT Scan",
  },
  {
    label: "Ultrasound",
    value: "Ultrasound",
  },
  {
    label: "Mammogram",
    value: "Mammogram",
  },
  {
    label: "PET Scan",
    value: "PET Scan",
  },
];

export default function AddLineItemFieldSet({
  description,
  quantity,
  setDescription,
  setQuantity,
  setType,
  setUnitPrice,
  totalCost,
  type,
  unitPrice,
  invoiceLines,
  setInvoiceLines,
  claimInvoiceAmount,
  savedInvoiceLines,
}: AddLineItemFieldSetProps) {
  const [title, setTitle] = useState(" ");

  const { data: medicalDrugsData, refetch: refetchMedicalDrugs } = useGetMedicalDrugsQuery({
    title: title || " ",
    page: 1,
    size: 10,
  });

  const medicalDrugs = medicalDrugsData?.data.content;

  const { data: laboratoriesData, refetch: refetchLaboratories } = useGetLaboratoriesQuery({
    title: title || " ",
    page: 1,
    size: 10,
  });

  const laboratories = laboratoriesData?.data.content;

  useEffect(() => {
    refetchLaboratories();
    refetchMedicalDrugs();
  }, [title, refetchLaboratories, refetchMedicalDrugs]);

  function handleAddInvoiceLine(e: React.MouseEvent<HTMLButtonElement, MouseEvent>) {
    e.preventDefault();

    if (!type || !description || !quantity || !unitPrice || !totalCost) {
      toast.error("Please fill in all fields");
      return;
    }

    const newInvoiceLine = {
      lineType: type,
      description: description,
      quantity: Number(quantity),
      unitPrice: Number(unitPrice),
      lineTotal: Number(totalCost),
    } as LineItem;

    const invoiceLinesTotal = invoiceLines.reduce(
      (accumulator, currentValue) => accumulator + currentValue.lineTotal,
      0,
    );

    const savedInvoiceLinesTotal = savedInvoiceLines.reduce(
      (accumulator, currentValue) => accumulator + currentValue.lineTotal,
      0,
    );

    if (
      invoiceLinesTotal + savedInvoiceLinesTotal + newInvoiceLine.lineTotal >
      claimInvoiceAmount
    ) {
      toast.error("Invoice Amount Exceeded");
      return;
    }

    const isDuplicateLineItemFound = invoiceLines.some((invoiceLine) =>
      checkIfObjectsAreEqual(invoiceLine, newInvoiceLine),
    );

    if (isDuplicateLineItemFound) {
      toast.error("A similar invoice line item has already been added!");
      return;
    }

    const isSavedLineItemFound = savedInvoiceLines.some(
      (invoiceLine) =>
        invoiceLine.lineType === newInvoiceLine.lineType &&
        invoiceLine.description === newInvoiceLine.description,
    );

    if (isSavedLineItemFound) {
      toast.error("A similar invoice line item has already been saved!");
      return;
    }

    setInvoiceLines([...invoiceLines, newInvoiceLine]);

    setDescription("");
    setQuantity("");
    setType("");
    setUnitPrice("");
  }

  return (
    <form>
      <fieldset className=" rounded-md border-2 px-4 py-1">
        <legend className="text-xs">
          Invoice lines <span className="text-red-600">*</span>
        </legend>

        <div className="grid grid-cols-5 gap-2 px-4 py-3">
          {/* Type */}
          <div className="flex flex-col gap-2">
            <label className="text-xs" htmlFor="type-button">
              Type <span className="text-red-600">*</span>
            </label>

            <Listbox
              value={type}
              onChange={(value) => {
                setDescription("");
                setType(value);
              }}
            >
              <Listbox.Button
                className="rounded-md border border-slate-300 p-2 text-left text-xs"
                id="type-button"
              >
                {type || "Select line item"}
              </Listbox.Button>
              <div className="relative  ">
                <Listbox.Options className="absolute w-full rounded-md border bg-white">
                  {LINE_ITEM_TYPES.map((type) => (
                    <Listbox.Option
                      className={`cursor-pointer  p-2 text-xs hover:bg-blue-600 hover:text-white `}
                      key={type}
                      value={type}
                    >
                      {type}
                    </Listbox.Option>
                  ))}
                </Listbox.Options>
              </div>
            </Listbox>
          </div>

          {/* Description */}
          <div className="flex flex-col gap-2">
            <label className="text-xs" htmlFor="description-button">
              Description <span className="text-red-600">*</span>
            </label>

            {type === "CONSULTATION" ? (
              <Listbox value={description} onChange={setDescription}>
                <Listbox.Button className="rounded-md border p-2 text-xs" id="description-button">
                  {description || "Select a description"}
                </Listbox.Button>
                <div className="relative">
                  <Listbox.Options className="absolute max-h-[300px] w-full overflow-y-auto rounded-md border bg-white">
                    {CONSULTATION_DESCRIPTIONS.map((description) => (
                      <Listbox.Option
                        className={`cursor-pointer  p-1 text-xs hover:bg-blue-600 hover:text-white `}
                        key={description}
                        value={description}
                      >
                        {description}
                      </Listbox.Option>
                    ))}
                  </Listbox.Options>
                </div>
              </Listbox>
            ) : type === "RADIOLOGY" ? (
              <Listbox value={description} onChange={setDescription}>
                <Listbox.Button className="rounded-md border p-2 text-xs">
                  {description || "Select a description"}
                </Listbox.Button>
                <div className="relative">
                  <Listbox.Options className="absolute max-h-[300px] w-full overflow-y-auto rounded-md border bg-white">
                    {RADIOLOGY_OPTIONS.map((option) => (
                      <Listbox.Option
                        className={`cursor-pointer  p-1 text-xs hover:bg-blue-600 hover:text-white `}
                        key={option.value}
                        value={option.value}
                      >
                        {option.label}
                      </Listbox.Option>
                    ))}
                  </Listbox.Options>
                </div>
              </Listbox>
            ) : type === "PHARMACY" ? (
              <InvoiceCombobox
                description={description}
                items={medicalDrugs as MedicalDrug[]}
                setDescription={setDescription}
                setTitle={setTitle}
                title={title}
              />
            ) : type === "LABORATORY" ? (
              <InvoiceCombobox
                description={description}
                setDescription={setDescription}
                setTitle={setTitle}
                items={laboratories as Laboratory[]}
                title={title}
              />
            ) : type === "OTHER" ? (
              <input
                className="w-full rounded-md border-slate-300 p-2 text-xs "
                type="text"
                list="description"
                required
                value={description}
                onChange={(e) => setDescription(e.target.value)}
              />
            ) : (
              <input
                className="w-full cursor-not-allowed rounded-md border-slate-300 p-2 text-xs "
                type="text"
                disabled
                required
                onChange={(e) => setDescription(e.target.value)}
              />
            )}
          </div>

          <div className="flex flex-col gap-2">
            <label className="text-xs" htmlFor="quantity-input">
              Quantity <span className="text-red-600">*</span>
            </label>
            <input
              className="w-full rounded-md border-slate-300 p-2 text-xs "
              type="number"
              min={1}
              required
              value={quantity}
              onChange={(e) => setQuantity(e.target.value)}
              id="quantity-input"
              data-testid="quantity-input"
            />
          </div>

          <div className="flex flex-col gap-2">
            <label className="text-xs" htmlFor="unit-price-input">
              Unit Price <span className="text-red-600">*</span>
            </label>
            <input
              id="unit-price-input"
              data-testid="unit-price-input"
              className="w-full rounded-md border-slate-300 p-2 text-xs "
              type="number"
              min={1}
              required
              value={unitPrice}
              onChange={(e) => setUnitPrice(e.target.value)}
            />
          </div>

          <div className="flex flex-col gap-2 text-slate-500">
            <label className="text-xs" htmlFor="">
              Total Cost
            </label>
            <input
              className="w-full rounded-md border-slate-300 p-2 text-xs "
              type="number"
              min={1}
              value={totalCost}
              required
              disabled
            />
          </div>
        </div>

        <InvoiceLines invoiceLines={invoiceLines} setInvoiceLines={setInvoiceLines} />

        <button
          className="mb-4 ml-4 mt-12 rounded-md bg-blue-100 px-3 py-2 text-xs font-semibold text-blue-600"
          onClick={(e) => handleAddInvoiceLine(e)}
        >
          Add invoice line
        </button>
      </fieldset>
    </form>
  );
}
