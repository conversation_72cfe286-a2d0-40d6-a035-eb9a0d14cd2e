import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { toast } from "react-toastify";
import { describe, expect, it, vi } from "vitest";
import { TestComponentWrapper } from "../../../../../__tests__/TestComponentWrapper";
import AddLineItemFieldSet, { AddLineItemFieldSetProps } from "./AddLineItemFieldSet";

vi.mock("react-toastify", () => ({ toast: { error: vi.fn() } }));

const mockProps = {
  type: "",
  setType: vi.fn(),
  description: "",
  setDescription: vi.fn(),
  quantity: "",
  setQuantity: vi.fn(),
  unitPrice: "",
  setUnitPrice: vi.fn(),
  totalCost: "0",
  invoiceLines: [],
  setInvoiceLines: vi.fn(),
  claimInvoiceAmount: 1000,
  savedInvoiceLines: [],
};

describe("AddLineItemFieldSet", () => {
  it("renders correctly", () => {
    render(
      <TestComponentWrapper>
        <AddLineItemFieldSet {...(mockProps as AddLineItemFieldSetProps)} />
      </TestComponentWrapper>,
    );
    expect(screen.getByText(/Invoice lines/i)).toBeInTheDocument();
  });

  it("shows an error if trying to add an invoice line with missing fields", async () => {
    render(
      <TestComponentWrapper>
        <AddLineItemFieldSet {...(mockProps as AddLineItemFieldSetProps)} />
      </TestComponentWrapper>,
    );

    const addButton = screen.getByText("Add invoice line");
    await userEvent.click(addButton);

    expect(toast.error).toHaveBeenCalledWith("Please fill in all fields");
  });

  it("adds an invoice line when all fields are filled", async () => {
    const setInvoiceLines = vi.fn();
    render(
      <TestComponentWrapper>
        <AddLineItemFieldSet
          {...(mockProps as AddLineItemFieldSetProps)}
          setInvoiceLines={setInvoiceLines}
          type="CONSULTATION"
          description="General Practitioner"
          quantity="1"
          unitPrice="100"
          totalCost="100"
        />
      </TestComponentWrapper>,
    );

    await userEvent.click(screen.getByText("Add invoice line"));

    await waitFor(() => {
      expect(setInvoiceLines).toHaveBeenCalled();
    });
  });
});
