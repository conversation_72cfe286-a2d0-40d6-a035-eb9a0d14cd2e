import { fireEvent, render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import { store } from "../../../../../store";
import { Claim } from "../../types";
import InvoiceDetails from "./InvoiceDetails";

const mockClaim = {
  id: "123",
  invoiceNumber: "INV-001",
  totalAmount: 5000,
  invoiceDate: "2025-02-10",
  vettingStatus: "PENDING",
  deductibles: [
    { deductibleType: "COPAYMENT", amount: 500 },
    { deductibleType: "NHIF", amount: 1000 },
  ],
  invoiceLines: [{ id: "line1", description: "Service A", amount: 2000 }],
};

describe("InvoiceDetails", () => {
  beforeAll(() => {
    global.ResizeObserver = class {
      observe = () => null;
      unobserve = () => null;
      disconnect = () => null;
    };
  });

  it("renders invoice details correctly", () => {
    render(<InvoiceDetails claim={mockClaim as unknown as Claim} />);

    expect(screen.getByText("Overview")).toBeInTheDocument();
    expect(screen.getByText("Invoice Number:")).toBeInTheDocument();
    expect(screen.getByText("INV-001")).toBeInTheDocument();
    expect(screen.getByText("Invoice Amount:")).toBeInTheDocument();
    expect(screen.getByText(/5,000/i)).toBeInTheDocument();
    expect(screen.getByText("Invoice Date:")).toBeInTheDocument();
  });

  it("toggles deductibles section", () => {
    render(<InvoiceDetails claim={mockClaim as unknown as Claim} />);
    const toggleButton = screen.getByText("Show more ...");

    fireEvent.click(toggleButton);
    expect(screen.getByText("Deductibles")).toBeInTheDocument();
    expect(screen.getByText("Co-payment:")).toBeInTheDocument();
    expect(screen.getByText("500")).toBeInTheDocument();
    expect(screen.getByText("SHIF Amount:")).toBeInTheDocument();
    expect(screen.getByText("1000")).toBeInTheDocument();
  });

  it("opens and closes edit invoice number modal", () => {
    render(
      <Provider store={store}>
        <InvoiceDetails claim={mockClaim as unknown as Claim} />
      </Provider>,
    );

    const editButton = screen.getByTestId("open-modal-button");
    fireEvent.click(editButton);

    expect(screen.getByText(/Edit Invoice Number/i)).toBeInTheDocument();

    const closeButton = screen.getByTestId("close-modal-button");
    fireEvent.click(closeButton);
    expect(screen.queryByText(/Edit Invoice Number/i)).not.toBeInTheDocument();
  });

  it("opens and closes add line item modal", () => {
    render(
      <Provider store={store}>
        <InvoiceDetails claim={mockClaim as unknown as Claim} />
      </Provider>,
    );
    const addButton = screen.getByText("+ Add Line Item");
    fireEvent.click(addButton);

    expect(screen.getByRole("heading", { name: /Add Line Item/i })).toBeInTheDocument();

    const closeButton = screen.getByTestId("close-line-item-modal-button");
    fireEvent.click(closeButton);
    expect(screen.queryByRole("heading", { name: /Add Line Item/i })).not.toBeInTheDocument();
  });
});
