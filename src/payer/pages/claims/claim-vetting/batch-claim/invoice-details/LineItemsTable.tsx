import EmptyStateIcon from "../../../../../components/icons/EmptyStateIcon";
import { LineItem } from "../../types";
import LineItemRow from "./LineItemRow";

type Props = {
  lineItems: LineItem[];
};

export default function LineItemsTable({ lineItems }: Props) {
  return lineItems.length > 0 ? (
    <table className="mt-6 w-full table-auto border-collapse text-left text-sm">
      <thead>
        <tr className="border-b-2">
          <th className="py-2">#</th>
          <th className="py-2">TYPE</th>
          <th className="py-2">DESCRIPTION</th>
          <th className="py-2">QUANTITY</th>
          <th className="py-2">UNIT PRICE</th>
          <th className="py-2">TOTAL COST</th>
        </tr>
      </thead>

      <tbody className="">
        {lineItems &&
          lineItems.map((lineItem, index) => (
            <LineItemRow key={lineItem.id} number={index + 1} lineItem={lineItem} />
          ))}
      </tbody>
    </table>
  ) : (
    <div className="mt-8 flex h-[300px] flex-col items-center justify-center">
      <EmptyStateIcon />
      <p className="mt-8 text-2xl font-semibold">No line items for this invoice </p>
      <p className="mt-4 font-medium text-slate-500">
        Click on the “add line items ” button to add the necessary line item{" "}
      </p>
    </div>
  );
}
