import { Combobox } from "@headlessui/react";
import { Laboratory, MedicalDrug } from "../../types";

type Props = {
  description: string;
  setDescription: (value: React.SetStateAction<string>) => void;
  items: Laboratory[] | MedicalDrug[];
  title: string;
  setTitle: (value: React.SetStateAction<string>) => void;
};

export default function InvoiceCombobox({
  description,
  setDescription,
  items,
  setTitle,
  title,
}: Props) {
  const filteredItems =
    title === ""
      ? items
      : items.filter((item) => item.name.toLowerCase().includes(title.toLowerCase()));

  return (
    <Combobox value={description} onChange={setDescription}>
      <Combobox.Input
        className=" rounded-md border border-slate-300 p-2 text-xs "
        onChange={(event) => setTitle(event.target.value)}
        placeholder="Enter description"
      />

      <div className="relative">
        <Combobox.Options className="absolute max-h-[300px]  w-fit min-w-full overflow-y-auto rounded-md border bg-white">
          {filteredItems.length === 0 && title !== "" ? (
            <div className="relative cursor-default select-none px-4 py-2 text-sm text-gray-700">
              Nothing found.
            </div>
          ) : (
            filteredItems.map((item) => (
              <Combobox.Option
                key={item.id}
                className="p-2 text-xs hover:bg-blue-600 hover:text-white"
                value={item.name}
              >
                {item.name}
              </Combobox.Option>
            ))
          )}
        </Combobox.Options>
      </div>
    </Combobox>
  );
}
