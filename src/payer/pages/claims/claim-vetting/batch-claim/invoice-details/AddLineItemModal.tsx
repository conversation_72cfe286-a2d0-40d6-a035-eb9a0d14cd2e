import { useState } from "react";
import { toast } from "react-toastify";
import { useSaveLineItemsMutation } from "../../../../../api/claims-vetting-api/claimVettingApi";
import { SaveLineItemsRequestArgs } from "../../../../../api/claims-vetting-api/requestArgsTypes";
import XIcon from "../../../../../components/icons/XIcon";
import Button from "../../../../../components/ui/Button";
import Text from "../../../../../components/ui/typography/Text";
import { Claim, LineItem } from "../../types";
import AddLineItemFieldSet from "./AddLineItemFieldSet";

export type LineItemType =
  | "CONSULTATION"
  | "PHARMACY"
  | "LABORATORY"
  | "RADIOLOGY"
  | "MEDICALPROCEDURE"
  | "DIAGNOSIS"
  | "INPATIENT"
  | "OTHER";

type Props = {
  claim: Claim;
  setIsShowAddLineItemModal: React.Dispatch<React.SetStateAction<boolean>>;
  savedInvoiceLines: LineItem[];
};

export default function AddLineItemModal({
  claim,
  setIsShowAddLineItemModal,
  savedInvoiceLines,
}: Props) {
  const [type, setType] = useState<LineItemType | "">("");
  const [description, setDescription] = useState("");
  const [quantity, setQuantity] = useState("");
  const [unitPrice, setUnitPrice] = useState("");
  const totalCost = Number(quantity) * Number(unitPrice);

  const [invoiceLines, setInvoiceLines] = useState<LineItem[]>([]);

  const [saveLineItems, { isLoading }] = useSaveLineItemsMutation();

  async function handleSaveLineItems() {
    try {
      if (!invoiceLines || !(invoiceLines.length > 0)) {
        toast.error("No invoice lines added");
        return;
      }

      // console.log(invoiceLines);

      const result = await saveLineItems({
        providerId: claim.hospitalProviderId,
        invoiceNumber: claim.invoiceNumber,
        visitId: claim.visitNumber,
        lineItems: invoiceLines,
      } as SaveLineItemsRequestArgs);

      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      if (result.error) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        throw new Error(result.error.data.error);
      }

      toast.success("Line item added successfully!");

      setIsShowAddLineItemModal(false);
    } catch (error) {
      if (error instanceof Error) {
        console.error(error.message);
      }
    }
  }

  return (
    <section className="max-h-[100vh] min-w-[40%]  overflow-y-auto rounded-md bg-white p-8">
      <div className="mb-4 flex items-center justify-between">
        <Text variant="heading">Add line item</Text>

        <button
          onClick={() => setIsShowAddLineItemModal(false)}
          data-testid="close-line-item-modal-button"
        >
          <XIcon height={22} width={22} />
        </button>
      </div>

      <AddLineItemFieldSet
        type={type as LineItemType}
        quantity={quantity}
        unitPrice={unitPrice}
        totalCost={totalCost.toString()}
        description={description}
        setType={setType}
        setQuantity={setQuantity}
        setUnitPrice={setUnitPrice}
        setDescription={setDescription}
        invoiceLines={invoiceLines}
        setInvoiceLines={setInvoiceLines}
        claimInvoiceAmount={claim.totalAmount}
        savedInvoiceLines={savedInvoiceLines}
      />

      <div className="mt-8 flex justify-end text-sm font-medium">
        <Button onClick={handleSaveLineItems} disabled={isLoading}>
          {isLoading ? "Updating..." : "Update"}
        </Button>
      </div>
    </section>
  );
}
