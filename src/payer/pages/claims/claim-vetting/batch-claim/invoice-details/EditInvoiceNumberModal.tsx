import React, { useState } from "react";
import { toast } from "react-toastify";
import { useEditInvoiceNumberMutation } from "../../../../../api/claims-vetting-api/claimVettingApi";
import XIcon from "../../../../../components/icons/XIcon";
import UserService from "../../../../../services/UserService";
import { UserInfo } from "../../types";

type Props = {
  invoiceId: number;
  invoiceNumber: string;
  setIsShowEditInvoiceNumberModal: React.Dispatch<React.SetStateAction<boolean>>;
};

export default function EditInvoiceNumberModal({
  invoiceId,
  invoiceNumber,
  setIsShowEditInvoiceNumberModal,
}: Props) {
  const userInfo = UserService.kcObject.tokenParsed as UserInfo;

  const [newInvoiceNumber, setNewInvoiceNumber] = useState("");
  const [reason, setReason] = useState("");
  const [invoiceNumberError, setInvoiceNumberError] = useState("");

  const [editPost, { isLoading }] = useEditInvoiceNumberMutation({});

  async function handleEditInvoiceNumber(e: React.FormEvent<HTMLFormElement>) {
    try {
      e.preventDefault();

      if (!invoiceId || !newInvoiceNumber || !reason) {
        toast.error("Please fill in all fields!");
        return;
      }

      const result = await editPost({
        invoiceId: invoiceId,
        invoiceNumber: newInvoiceNumber,
        reason: reason,
        updatedBy: userInfo.preferred_username,
      });

      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      if (result.error) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        throw new Error(result.error.data.error);
      }

      toast.success("Invoice number updated successfully! ");

      setIsShowEditInvoiceNumberModal(false);
    } catch (error) {
      if (error instanceof Error) {
        console.error(error.message);
        toast.error(error.message);
      }
    }
  }

  function handleInvoiceNumberChange(e: React.ChangeEvent<HTMLInputElement>) {
    const value = e.target.value;
    const isValid = /^[a-zA-Z0-9][a-zA-Z0-9/-]*$/.test(value) || value === "";

    if (isValid) {
      setNewInvoiceNumber(value);
      setInvoiceNumberError("");
    } else {
      setInvoiceNumberError(
        "Only alphanumerics, hyphens (-), and forward slashes (/) are allowed. The input cannot start with a symbol.",
      );
    }
  }

  return (
    <form
      className="min-w-[40%] rounded-md bg-white p-8"
      onSubmit={(e) => handleEditInvoiceNumber(e)}
    >
      <div className="mb-8 flex items-center justify-between">
        <p className=" text-lg font-semibold">Edit invoice number</p>

        <button
          data-testid="close-modal-button"
          onClick={() => setIsShowEditInvoiceNumberModal(false)}
          type="button"
        >
          <XIcon />
        </button>
      </div>

      <div className="flex items-center justify-between gap-8">
        <div className="flex grow flex-col gap-1">
          <label className="text-xs" htmlFor="">
            Current invoice number
          </label>
          <input
            className="w-full rounded-md border-slate-300 p-2 text-xs "
            disabled
            type="text"
            value={invoiceNumber}
          />
        </div>

        <div className="relative flex grow flex-col gap-1">
          <label className="text-xs" htmlFor="">
            New invoice number
          </label>
          <input
            className="w-full rounded-md border-slate-300 p-2 text-xs "
            type="text"
            placeholder="Enter the new invoice number"
            required
            value={newInvoiceNumber}
            onChange={handleInvoiceNumberChange}
          />
          {invoiceNumberError && (
            <p className="absolute top-[100%] mt-1 text-[10px] text-red-500">
              {invoiceNumberError}
            </p>
          )}
        </div>
      </div>

      <fieldset className="mt-12  rounded-md border-2 px-4 py-1">
        <legend className="text-xs">
          Reason <span className="text-red-600">*</span>
        </legend>
        <textarea
          className="h-full max-h-[200px] min-h-[100px] w-full rounded-md border-none text-sm"
          placeholder="Add a reason for the update..."
          required
          value={reason}
          onChange={(e) => setReason(e.target.value)}
        />
      </fieldset>

      <div className="mt-8 flex justify-end text-sm font-medium">
        <button
          disabled={isLoading}
          className={` rounded-md bg-blue-500 px-3 py-2 text-white ${isLoading && "opacity-50"}`}
        >
          {isLoading ? "Saving changes..." : "Update"}
        </button>
      </div>
    </form>
  );
}
