import { fireEvent, render, screen } from "@testing-library/react";
import { vi } from "vitest";
import InvoiceCombobox from "./InvoiceComboBox";

const mockItems = [
  { id: 1, name: "<PERSON>pirin" },
  { id: 2, name: "Ibuprofen" },
  { id: 3, name: "Paracetamol" },
];

describe("InvoiceCombobox", () => {
  it("renders input and allows typing", () => {
    const setDescription = vi.fn();
    const setTitle = vi.fn();

    render(
      <InvoiceCombobox
        description=""
        setDescription={setDescription}
        items={mockItems}
        title=""
        setTitle={setTitle}
      />,
    );

    const input = screen.getByPlaceholderText("Enter description");
    expect(input).toBeInTheDocument();

    fireEvent.change(input, { target: { value: "Asp" } });
    expect(setTitle).toHaveBeenCalledWith("Asp");
  });

  it("filters and displays matching items", () => {
    const setDescription = vi.fn();
    const setTitle = vi.fn();

    render(
      <InvoiceCombobox
        description=""
        setDescription={setDescription}
        items={mockItems}
        title="Asp"
        setTitle={setTitle}
      />,
    );

    const input = screen.getByPlaceholderText("Enter description");
    fireEvent.change(input, { target: { value: "Asp" } });

    expect(screen.getByText("Aspirin")).toBeInTheDocument();
    expect(screen.queryByText("Ibuprofen")).not.toBeInTheDocument();
  });

  it("shows 'Nothing found' when no match exists", () => {
    const setDescription = vi.fn();
    const setTitle = vi.fn();

    render(
      <InvoiceCombobox
        description=""
        setDescription={setDescription}
        items={mockItems}
        title="xyz"
        setTitle={setTitle}
      />,
    );

    const input = screen.getByPlaceholderText("Enter description");
    fireEvent.change(input, { target: { value: "test" } });

    expect(screen.getByText("Nothing found.")).toBeInTheDocument();
  });

  it("calls setDescription when an option is selected", () => {
    const setDescription = vi.fn();
    const setTitle = vi.fn();

    render(
      <InvoiceCombobox
        description=""
        setDescription={setDescription}
        items={mockItems}
        title=""
        setTitle={setTitle}
      />,
    );

    const input = screen.getByPlaceholderText("Enter description");
    fireEvent.change(input, { target: { value: "Asp" } });

    fireEvent.click(screen.getByText("Aspirin"));
    expect(setDescription).toHaveBeenCalledWith("Aspirin");
  });
});
