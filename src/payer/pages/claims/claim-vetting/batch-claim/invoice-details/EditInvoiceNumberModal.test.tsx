import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { toast } from "react-toastify";
import { Mock, vi } from "vitest";
import { useEditInvoiceNumberMutation } from "../../../../../api/claims-vetting-api/claimVettingApi";
import EditInvoiceNumberModal from "./EditInvoiceNumberModal";

vi.mock("../../../../../api/claims-vetting-api/claimVettingApi", async (importActual) => {
  const actual = await importActual();
  return {
    ...(typeof actual === "object" && actual !== null ? actual : {}),
    useEditInvoiceNumberMutation: vi.fn(),
  };
});

vi.mock("react-toastify", () => ({
  toast: { error: vi.fn(), success: vi.fn() },
}));

vi.mock("../../../../../services/UserService", () => ({
  default: {
    kcObject: {
      tokenParsed: { payerId: 123, preferred_username: "test-user" },
    },
  },
}));

describe("EditInvoiceNumberModal", () => {
  let mockEditInvoiceNumber: () => void;
  let setIsShowEditInvoiceNumberModal: () => void;

  beforeEach(() => {
    mockEditInvoiceNumber = vi.fn().mockResolvedValue({});
    (useEditInvoiceNumberMutation as Mock).mockReturnValue([
      mockEditInvoiceNumber,
      { isLoading: false },
    ]);
    setIsShowEditInvoiceNumberModal = vi.fn();
  });

  it("renders correctly with initial values", () => {
    render(
      <EditInvoiceNumberModal
        invoiceId={123}
        invoiceNumber="INV-001"
        setIsShowEditInvoiceNumberModal={setIsShowEditInvoiceNumberModal}
      />,
    );

    expect(screen.getByText("Edit invoice number")).toBeInTheDocument();
    expect(screen.getByDisplayValue("INV-001")).toBeDisabled();
    expect(screen.getByPlaceholderText("Enter the new invoice number")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Add a reason for the update...")).toBeInTheDocument();
  });

  it("validates input and prevents submission if fields are empty", async () => {
    render(
      <EditInvoiceNumberModal
        invoiceId={123}
        invoiceNumber="INV-001"
        setIsShowEditInvoiceNumberModal={setIsShowEditInvoiceNumberModal}
      />,
    );

    await userEvent.click(screen.getByText("Update"));

    // Ensure the required fields are still empty
    expect(screen.getByPlaceholderText("Enter the new invoice number")).toBeInvalid();
    expect(screen.getByPlaceholderText("Add a reason for the update...")).toBeInvalid();
  });

  it("submits the form successfully", async () => {
    render(
      <EditInvoiceNumberModal
        invoiceId={123}
        invoiceNumber="INV-001"
        setIsShowEditInvoiceNumberModal={setIsShowEditInvoiceNumberModal}
      />,
    );

    await userEvent.type(screen.getByPlaceholderText("Enter the new invoice number"), "INV-002");
    await userEvent.type(
      screen.getByPlaceholderText("Add a reason for the update..."),
      "Correction",
    );

    await userEvent.click(screen.getByText("Update"));

    expect(mockEditInvoiceNumber).toHaveBeenCalledWith({
      invoiceId: 123,
      invoiceNumber: "INV-002",
      reason: "Correction",
      updatedBy: expect.any(String),
    });
    expect(toast.success).toHaveBeenCalledWith("Invoice number updated successfully! ");
    expect(setIsShowEditInvoiceNumberModal).toHaveBeenCalledWith(false);
  });

  it("shows an error if API request fails", async () => {
    (mockEditInvoiceNumber as Mock).mockRejectedValue(new Error("Update failed"));

    render(
      <EditInvoiceNumberModal
        invoiceId={123}
        invoiceNumber="INV-001"
        setIsShowEditInvoiceNumberModal={setIsShowEditInvoiceNumberModal}
      />,
    );

    fireEvent.change(screen.getByPlaceholderText("Enter the new invoice number"), {
      target: { value: "INV-003" },
    });

    fireEvent.change(screen.getByPlaceholderText("Add a reason for the update..."), {
      target: { value: "Fixing typo" },
    });

    fireEvent.click(screen.getByText("Update"));

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith("Update failed");
    });
  });
});
