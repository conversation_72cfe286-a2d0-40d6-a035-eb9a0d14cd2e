import { XCircleIcon } from "@heroicons/react/24/outline";
import TableDataItem from "../../../../../components/ui/table/TableDataItem";
import TableHeaderItem from "../../../../../components/ui/table/TableHeaderItem";
import { LineItem } from "../../types";

type Props = {
  invoiceLines: LineItem[];
  setInvoiceLines: React.Dispatch<React.SetStateAction<LineItem[]>>;
};

export default function InvoiceLines({ invoiceLines, setInvoiceLines }: Props) {
  function handleRemoveInvoiceLine(index: number) {
    const newInvoiceLines = invoiceLines.filter((_, i) => i !== index);
    setInvoiceLines(newInvoiceLines);
  }

  return (
    <table className="ml-8 w-full text-left text-xs">
      <thead>
        <tr>
          <TableHeaderItem item="Type" />
          <TableHeaderItem item="Description" />
          <TableHeaderItem item="Quantity" />
          <TableHeaderItem item="Unit" />
          <TableHeaderItem item="Total" />
          <TableHeaderItem item="Remove" />
        </tr>
      </thead>
      <tbody>
        {invoiceLines.map((invoiceLine, index) => (
          <tr key={index} className="">
            <TableDataItem item={invoiceLine.lineType as string} />
            <TableDataItem item={invoiceLine.description} />
            <TableDataItem item={invoiceLine.quantity} />
            <TableDataItem item={invoiceLine.unitPrice} />
            <TableDataItem item={invoiceLine.lineTotal} />
            <td className="p-2">
              <button type="button" onClick={() => handleRemoveInvoiceLine(index)}>
                <XCircleIcon className="h-5 w-5 text-customRed" />
              </button>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}
