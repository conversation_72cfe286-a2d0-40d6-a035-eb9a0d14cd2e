// BatchClaim.test.tsx
import { fireEvent, render, screen } from "@testing-library/react";
import React from "react";
import { MemoryRouter } from "react-router-dom";
import { beforeEach, describe, expect, it, Mock, vi } from "vitest";
import { useGetIndividualBatchClaimQuery } from "../../../../api/claims-vetting-api/claimVettingApi";
import BatchClaim from "./BatchClaim";

// ----- Ensure the API hook is mocked as a function -----
vi.mock("../../../../api/claims-vetting-api/claimVettingApi", async () => ({
  ...(await vi.importActual("../../../../api/claims-vetting-api/claimVettingApi")),
  useGetIndividualBatchClaimQuery: vi.fn(),
}));

// ----- Other Mocks -----

// Override react-router-dom hooks: useNavigate and useParams.
const mockedNavigate = vi.fn();
vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    useNavigate: () => mockedNavigate,
    useParams: () => ({ batchId: "1", claimId: "1" }),
  };
});

// Mock the TailSpin loader from react-loader-spinner.
vi.mock("react-loader-spinner", () => ({
  TailSpin: () => <div data-testid="tail-spin">Loading spinner</div>,
}));

// Mock the LeftArrowIcon.
vi.mock("../../../../components/icons/LeftArrowIcon", () => ({
  default: () => <div>LeftArrowIcon</div>,
}));

// Mock the Badge component.
vi.mock("../../../../components/ui/Badge", () => ({
  default: ({ text }: { text: string }) => <div data-testid="badge">{text}</div>,
}));

// Mock MainWrapper to simply render its children.
vi.mock("../../../../components/ui/MainWrapper", () => ({
  default: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

// Mock Text so that it just renders its children.
vi.mock("../../../../components/ui/typography/Text", () => ({
  default: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

// Stub the sub-components so we can verify when they are rendered.
vi.mock("./invoice-details/InvoiceDetails", () => ({
  default: ({ claim }: { claim: any }) => (
    <div data-testid="invoice-details">InvoiceDetails: {claim?.invoiceNumber}</div>
  ),
}));

vi.mock("./diagnosis-and-procedure/DiagnosisAndProcedure", () => ({
  default: ({
    invoiceNumber,
    visitId,
    benefitName,
  }: {
    invoiceNumber: string;
    visitId: string;
    benefitName: string;
  }) => (
    <div data-testid="diagnosis-and-procedure">
      DiagnosisAndProcedure: {invoiceNumber} {visitId} {benefitName}
    </div>
  ),
}));

vi.mock("./documents/Documents", () => ({
  default: ({
    providerName,
    providerId,
    visitNumber,
  }: {
    providerName: string;
    providerId: number;
    visitNumber: string;
  }) => (
    <div data-testid="documents">
      Documents: {providerName} {providerId} {visitNumber}
    </div>
  ),
}));

vi.mock("./pre-authorizations/PreAuthorizations", () => ({
  default: () => <div data-testid="pre-authorizations">PreAuthorizations</div>,
}));

vi.mock("./decision/Decision", () => ({
  default: ({ claim }: { claim: any }) => (
    <div data-testid="decision">Decision: {claim?.invoiceNumber}</div>
  ),
}));

vi.mock("../../../../services/UserService", () => ({
  default: {
    kcObject: {
      tokenParsed: { payerId: 123 },
    },
  },
}));

// Type cast the API hook so we can control its return value.
const mockUseGetIndividualBatchClaimQuery =
  useGetIndividualBatchClaimQuery as unknown as ReturnType<typeof vi.fn>;

describe("BatchClaim Component", () => {
  const sampleClaim = {
    batchInvoiceNumber: "INV123",
    vettingStatus: "APPROVED",
    invoiceNumber: "INV123",
    visitNumber: "V123",
    benefitName: "Test Benefit",
    providerName: "Provider A",
    hospitalProviderId: 123,
  };

  beforeEach(() => {
    (mockUseGetIndividualBatchClaimQuery as Mock).mockReset();
    mockedNavigate.mockReset();
  });

  it("should display a loading spinner when the claim is loading", () => {
    (mockUseGetIndividualBatchClaimQuery as Mock).mockReturnValue({
      isLoading: true,
      data: {
        data: {
          content: [],
        },
      },
    });

    render(
      <MemoryRouter initialEntries={["/batch/1/claim/1"]}>
        <BatchClaim />
      </MemoryRouter>,
    );

    expect(screen.getByTestId("tail-spin")).toBeInTheDocument();
  });

  it("should display invoice details when the claim is loaded", () => {
    (mockUseGetIndividualBatchClaimQuery as Mock).mockReturnValue({
      isLoading: false,
      data: { data: { content: [sampleClaim] } },
    });

    render(
      <MemoryRouter initialEntries={["/batch/1/claim/1"]}>
        <BatchClaim />
      </MemoryRouter>,
    );

    expect(screen.getByTestId("invoice-details")).toBeInTheDocument();

    expect(screen.getAllByText(/INV123/).length).toBeGreaterThan(0);

    expect(screen.getByTestId("badge")).toHaveTextContent("Approved");
  });

  it("should navigate back when the back button is clicked", () => {
    (mockUseGetIndividualBatchClaimQuery as Mock).mockReturnValue({
      isLoading: false,
      data: { data: { content: [sampleClaim] } },
    });

    render(
      <MemoryRouter initialEntries={["/batch/1/claim/1"]}>
        <BatchClaim />
      </MemoryRouter>,
    );

    const backButton = screen.getByRole("button", { name: /LeftArrowIcon/i });
    fireEvent.click(backButton);
    expect(mockedNavigate).toHaveBeenCalledWith(-1);
  });

  it("should switch displays when navigation buttons are clicked", () => {
    (mockUseGetIndividualBatchClaimQuery as Mock).mockReturnValue({
      isLoading: false,
      data: { data: { content: [sampleClaim] } },
    });

    render(
      <MemoryRouter initialEntries={["/batch/1/claim/1"]}>
        <BatchClaim />
      </MemoryRouter>,
    );

    expect(screen.getByTestId("invoice-details")).toBeInTheDocument();

    fireEvent.click(screen.getByRole("button", { name: /Diagnosis & Procedure/i }));
    expect(screen.getByTestId("diagnosis-and-procedure")).toBeInTheDocument();

    fireEvent.click(screen.getByRole("button", { name: /Documents/i }));
    expect(screen.getByTestId("documents")).toBeInTheDocument();

    fireEvent.click(screen.getByRole("button", { name: /Pre-authorizations/i }));
    expect(screen.getByTestId("pre-authorizations")).toBeInTheDocument();

    fireEvent.click(screen.getByRole("button", { name: /Decision/i }));
    expect(screen.getByTestId("decision")).toBeInTheDocument();
  });
});
