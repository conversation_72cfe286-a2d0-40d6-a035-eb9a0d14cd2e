import React from "react";
import { Claim } from "../../types";
import { formatNumberToKes } from "../../../../../utils/formatCurrency";
import { useGetAuditLogsQuery } from "../../../../../api/claims-vetting-api/claimVettingApi";

type Props = {
  claim: Claim;
};

export default function VettedClaimDecision({ claim }: Props) {
  const { data: auditLogsData } = useGetAuditLogsQuery({
    invoiceId: claim.id,
  });

  const auditLogs = auditLogsData?.data.content;

  const claimLog = auditLogs?.[auditLogs?.length - 1];

  return (
    <div className="mx-2 mt-8">
      <p className={`text-lg font-medium  `}>Approval / Decline</p>

      <div className="mt-4">
        <p className="text-sm text-slate-600">Decision</p>
        <p className="mt-1">{claim.vettingStatus}</p>
      </div>

      {claim.vettingStatus === "PARTIAL" && (
        <div className="mt-4">
          <p className="text-sm text-slate-600">Deductible Amount</p>
          <p className="mt-1">{formatNumberToKes(Number(claim?.deductibleAmount))}</p>
        </div>
      )}

      {claim.vettingStatus !== "DECLINED" && (
        <div className="mt-4">
          <p className="text-sm text-slate-600">Payable Amount</p>
          <p className="mt-1">{formatNumberToKes(Number(claim.payableAmount))}</p>
        </div>
      )}

      {claim.vettingStatus !== "APPROVED" && (
        <div className="mt-4">
          <p className="text-sm text-slate-600">Reason</p>
          <p className="mt-1">{claimLog?.reason}</p>
        </div>
      )}
    </div>
  );
}
