import React, { useState } from "react";
import { Claim, UserInfo } from "../../types";
import { toast } from "react-toastify";
import { useVetClaimMutation } from "../../../../../api/claims-vetting-api/claimVettingApi";
import { TailSpin } from "react-loader-spinner";
import UserService from "../../../../../services/UserService";
import DecisionTerms from "./DecisionTerms";
import VettedClaimDecision from "./VettedClaimDecision";

const DECISIONS = {
  approved: "APPROVED",
  declined: "DECLINED",
  partial: "PARTIAL",
};

type Props = {
  claim: Claim;
};

type VettingStatus = "APPROVED" | "DECLINED" | "PARTIAL";

export default function Decision({ claim }: Props) {
  const userInfo = UserService.kcObject.tokenParsed as UserInfo;

  const [decision, setDecision] = useState<VettingStatus>();
  const [deductibleAmount, setDeductibleAmount] = useState("");
  const [declineReason, setDeclineReason] = useState("");
  const [partialReason, setPartialReason] = useState("");

  const [nullOptionError, setNullOptionError] = useState(false);

  const [vetClaim, { isLoading }] = useVetClaimMutation();

  async function handleVetClaim(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();

    try {
      if (!decision) {
        setNullOptionError(true);
        return;
      }

      const result = await vetClaim({
        actionedBy: userInfo.preferred_username,
        invoiceId: [Number(claim.id)],
        vettingStatus: decision as VettingStatus,
        declineReason: declineReason,
        partialReason: partialReason,
        deductibleAmount: Number(deductibleAmount),
      });

      if ("error" in result) {
        throw new Error("Error Vetting Claim!");
      }

      toast.success("Claim Vetted Successfully!");
    } catch (error) {
      if (error instanceof Error) {
        toast.error(error.message);
        console.error(error.message);
      }
    }
  }

  return (
    <div>
      {claim.vettingStatus === "APPROVED" ||
      claim.vettingStatus === "DECLINED" ||
      claim.vettingStatus === "PARTIAL" ? (
        <VettedClaimDecision claim={claim} />
      ) : (
        <>
          <form onSubmit={(e) => handleVetClaim(e)}>
            <fieldset
              className={`mt-8 rounded-md border-2 px-4 py-6 ${
                nullOptionError && "border-red-600"
              }`}
            >
              <legend className={`text-sm text-slate-500 ${nullOptionError && "text-red-600"}`}>
                Approval / Decline
              </legend>
              <div className="px-6">
                <p>
                  Approval <span className="font-medium text-red-600">*</span>
                </p>

                {nullOptionError && (
                  <p className="mt-2 text-xs text-red-600">
                    Please select an option: Approve, Decline, or Partial Approval, before
                    submitting.
                  </p>
                )}

                {/* Radio buttons */}
                <div className="mb-12 mt-8 flex items-center gap-8">
                  <div className="flex items-center gap-3">
                    <input
                      onChange={(e) => setDecision(e.target.value as VettingStatus)}
                      type="radio"
                      name="approval"
                      id="approved"
                      value={DECISIONS.approved}
                    />
                    <label htmlFor="approved">Approve</label>
                  </div>
                  <div className="flex items-center gap-3">
                    <input
                      onChange={(e) => setDecision(e.target.value as VettingStatus)}
                      type="radio"
                      name="approval"
                      id="declined"
                      value={DECISIONS.declined}
                    />
                    <label htmlFor="declined">Decline</label>
                  </div>
                  <div className="flex items-center gap-3">
                    <input
                      onChange={(e) => setDecision(e.target.value as VettingStatus)}
                      type="radio"
                      name="approval"
                      id="partial"
                      value={DECISIONS.partial}
                    />
                    <label htmlFor="partial">Partial Approval</label>
                  </div>
                </div>

                {decision && (
                  <DecisionTerms
                    claim={claim}
                    decision={decision}
                    DECISIONS={DECISIONS}
                    deductibleAmount={deductibleAmount}
                    setDeductibleAmount={setDeductibleAmount}
                    declineReason={declineReason}
                    setDeclineReason={setDeclineReason}
                    partialReason={partialReason}
                    setPartialReason={setPartialReason}
                  />
                )}
                <div className="flex justify-end">
                  <button className=" mt-1 rounded-md bg-blue-600 px-3 py-2 text-white">
                    Submit
                  </button>
                </div>
              </div>
            </fieldset>
          </form>

          {isLoading && (
            <div className="fixed inset-0 flex items-center justify-center  bg-black bg-opacity-40">
              <TailSpin color="blue" />
            </div>
          )}
        </>
      )}
    </div>
  );
}
