import { fireEvent, render, screen } from "@testing-library/react";
import { Mock, vi } from "vitest";
import { Claim } from "../../types";
import DecisionTerms from "./DecisionTerms";

const DECISIONS = {
  approved: "APPROVED",
  declined: "DECLINED",
  partial: "PARTIAL",
};

describe("DecisionTerms Component", () => {
  const mockClaim = { totalAmount: 100 };
  let setDeductibleAmount: Mock, setDeclineReason: Mock, setPartialReason: Mock;

  beforeEach(() => {
    setDeductibleAmount = vi.fn();
    setDeclineReason = vi.fn();
    setPartialReason = vi.fn();
  });
  it("renders Payable Amount when approved", () => {
    render(
      <DecisionTerms
        decision={DECISIONS.approved}
        DECISIONS={DECISIONS}
        claim={mockClaim as Claim}
        deductibleAmount=""
        setDeductibleAmount={setDeductibleAmount}
        declineReason=""
        setDeclineReason={setDeclineReason}
        partialReason=""
        setPartialReason={setPartialReason}
      />,
    );
    expect(screen.getByLabelText("Payable Amount")).toBeInTheDocument();
  });

  it("renders decline reason input when declined", () => {
    render(
      <DecisionTerms
        decision={DECISIONS.declined}
        DECISIONS={DECISIONS}
        claim={mockClaim as Claim}
        deductibleAmount=""
        setDeductibleAmount={setDeductibleAmount}
        declineReason=""
        setDeclineReason={setDeclineReason}
        partialReason=""
        setPartialReason={setPartialReason}
      />,
    );
    expect(screen.getByLabelText(/Reason/i)).toBeInTheDocument();
  });

  it("renders deductible amount and payable amount when partial", () => {
    render(
      <DecisionTerms
        decision={DECISIONS.partial}
        DECISIONS={DECISIONS}
        claim={mockClaim as Claim}
        deductibleAmount="10"
        setDeductibleAmount={setDeductibleAmount}
        declineReason=""
        setDeclineReason={setDeclineReason}
        partialReason=""
        setPartialReason={setPartialReason}
      />,
    );
    expect(screen.getByLabelText("Deductible Amount")).toBeInTheDocument();
    expect(screen.getByLabelText("Payable Amount")).toBeInTheDocument();
  });

  it("calls setDeductibleAmount on change", () => {
    render(
      <DecisionTerms
        decision={DECISIONS.partial}
        DECISIONS={DECISIONS}
        claim={mockClaim as Claim}
        deductibleAmount="10"
        setDeductibleAmount={setDeductibleAmount}
        declineReason=""
        setDeclineReason={setDeclineReason}
        partialReason=""
        setPartialReason={setPartialReason}
      />,
    );
    fireEvent.change(screen.getByLabelText("Deductible Amount"), { target: { value: "20" } });
    expect(setDeductibleAmount).toHaveBeenCalledWith("20");
  });
});
