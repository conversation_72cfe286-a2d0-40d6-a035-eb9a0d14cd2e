import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { toast } from "react-toastify";
import { Mock, vi } from "vitest";
import { useVetClaimMutation } from "../../../../../api/claims-vetting-api/claimVettingApi";
import { Claim } from "../../types";
import Decision from "./Decision";

vi.mock("../../../../../api/claims-vetting-api/claimVettingApi", () => ({
  useVetClaimMutation: vi.fn(),
}));

vi.mock("../../../../../services/UserService", () => ({
  default: { kcObject: { tokenParsed: { preferred_username: "test_user" } } },
}));

vi.mock("react-toastify", () => ({
  toast: { success: vi.fn(), error: vi.fn() },
}));

describe("Decision Component", () => {
  const mockClaim = { id: 1, vettingStatus: "PENDING" };
  let vetClaimMock: Mock;

  beforeEach(() => {
    vetClaimMock = vi.fn().mockResolvedValue({ data: {} });
    (useVetClaimMutation as Mock).mockReturnValue([vetClaimMock, { isLoading: false }]);
  });

  it("renders decision radio buttons", () => {
    render(<Decision claim={mockClaim as Claim} />);
    expect(screen.getByLabelText("Approve")).toBeInTheDocument();
    expect(screen.getByLabelText("Decline")).toBeInTheDocument();
    expect(screen.getByLabelText("Partial Approval")).toBeInTheDocument();
  });

  it("shows an error message if no decision is selected on submit", async () => {
    render(<Decision claim={mockClaim as Claim} />);
    fireEvent.click(screen.getByText("Submit"));
    expect(await screen.findByText(/Please select an option/)).toBeInTheDocument();
  });

  it("submits approval decision successfully", async () => {
    render(<Decision claim={mockClaim as Claim} />);
    fireEvent.click(screen.getByLabelText("Approve"));
    fireEvent.click(screen.getByText("Submit"));
    await waitFor(() => expect(vetClaimMock).toHaveBeenCalled());
    expect(toast.success).toHaveBeenCalledWith("Claim Vetted Successfully!");
  });

  it("handles API error gracefully", async () => {
    vetClaimMock.mockRejectedValueOnce(new Error("Error Vetting Claim!"));
    render(<Decision claim={mockClaim as Claim} />);
    fireEvent.click(screen.getByLabelText("Approve"));
    fireEvent.click(screen.getByText("Submit"));
    await waitFor(() => expect(toast.error).toHaveBeenCalledWith("Error Vetting Claim!"));
  });
});
