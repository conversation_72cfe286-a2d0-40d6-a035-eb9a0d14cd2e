import React from "react";
import { Claim } from "../../types";

type Props = {
  DECISIONS: {
    approved: string;
    declined: string;
    partial: string;
  };
  decision: string | null | undefined;
  claim: Claim;
  deductibleAmount: string;
  declineReason: string;
  setDeclineReason: React.Dispatch<React.SetStateAction<string>>;
  partialReason: string;
  setPartialReason: React.Dispatch<React.SetStateAction<string>>;
  setDeductibleAmount: React.Dispatch<React.SetStateAction<string>>;
};
export default function DecisionTerms({
  decision,
  DECISIONS,
  claim,
  deductibleAmount,
  setDeductibleAmount,
  declineReason,
  setDeclineReason,
  partialReason,
  setPartialReason,
}: Props) {
  function roundToTwoDecimals(num: number) {
    return Math.round(num * 100) / 100;
  }

  return (
    <fieldset className="w-4/5 rounded-md border-2 px-4 py-3">
      <legend className="text-xs text-slate-500">Terms</legend>

      {decision === DECISIONS.approved ? (
        <div className="mx-2 flex flex-col gap-2">
          <label className="text-sm text-slate-400" htmlFor="approved-payable-amount">
            Payable Amount
          </label>
          <input
            id="approved-payable-amount"
            className="w-2/5 rounded-md border-slate-200 text-slate-500"
            type="number"
            value={Number(claim?.totalAmount)}
            min={1}
            disabled
            required
          />
        </div>
      ) : decision === DECISIONS.declined ? (
        <div className="mx-2 flex flex-col gap-2">
          <label className="text-sm " htmlFor="declined-reason">
            Reason <span className="font-medium text-red-600">*</span>
          </label>
          <input
            id="declined-reason"
            className="w-3/5 rounded-md border-slate-200 "
            value={declineReason}
            onChange={(e) => setDeclineReason(e.target.value)}
            type="text"
            required
          />
        </div>
      ) : (
        decision === DECISIONS.partial && (
          <div className="grid grid-cols-3">
            <div className="mx-2 flex flex-col gap-2">
              <label className="text-sm text-slate-400" htmlFor="partial-deductible-amount">
                Deductible Amount
              </label>
              <input
                id="partial-deductible-amount"
                className=" rounded-md border-slate-200 "
                type="number"
                value={deductibleAmount}
                min={1}
                required
                max={claim.totalAmount as number}
                onChange={(e) => setDeductibleAmount(e.target.value)}
              />
            </div>

            <div className="mx-2 flex flex-col gap-2">
              <label className="text-sm text-slate-400" htmlFor="partial-payable-amount">
                Payable Amount
              </label>
              <input
                id="partial-payable-amount"
                className="rounded-md border-slate-200 text-slate-500"
                type="number"
                value={roundToTwoDecimals(
                  Number(claim.totalAmount) - (Number(deductibleAmount) || 0),
                )}
                min={0}
                disabled
                required
              />
            </div>

            <div className="mx-2 flex flex-col gap-2">
              <label className="text-sm " htmlFor="partial-reason">
                Reason <span className="font-medium text-red-600">*</span>
              </label>
              <input
                id="partial-reason"
                className=" rounded-md border-slate-200 "
                type="text"
                value={partialReason}
                required
                onChange={(e) => setPartialReason(e.target.value)}
              />
            </div>
          </div>
        )
      )}
    </fieldset>
  );
}
