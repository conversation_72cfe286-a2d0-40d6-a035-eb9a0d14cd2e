import { render, screen } from "@testing-library/react";
import { Mock, vi } from "vitest";
import { useGetAuditLogsQuery } from "../../../../../api/claims-vetting-api/claimVettingApi";
import { Claim } from "../../types";
import VettedClaimDecision from "./VettedClaimDecision";

vi.mock("../../../../../api/claims-vetting-api/claimVettingApi", () => ({
  useGetAuditLogsQuery: vi.fn(),
}));

vi.mock("../../../../../utils/formatCurrency", () => ({
  formatNumberToKes: (num: number): string => `KES ${num.toFixed(2)}`,
}));

describe("VettedClaimDecision", () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it("renders approval/decline section", () => {
    (useGetAuditLogsQuery as Mock).mockReturnValue({ data: { data: { content: [] } } });
    render(<VettedClaimDecision claim={{ id: 123, vettingStatus: "APPROVED" } as Claim} />);
    expect(screen.getByText("Approval / Decline")).toBeInTheDocument();
    expect(screen.getByText("Decision")).toBeInTheDocument();
    expect(screen.getByText("APPROVED")).toBeInTheDocument();
  });

  it("renders deductible amount for PARTIAL status", () => {
    (useGetAuditLogsQuery as Mock).mockReturnValue({ data: { data: { content: [] } } });
    render(
      <VettedClaimDecision
        claim={{ id: 123, vettingStatus: "PARTIAL", deductibleAmount: 500 } as Claim}
      />,
    );
    expect(screen.getByText("Deductible Amount")).toBeInTheDocument();
    expect(screen.getByText("KES 500.00")).toBeInTheDocument();
  });

  it("renders payable amount for non-DECLINED status", () => {
    (useGetAuditLogsQuery as Mock).mockReturnValue({ data: { data: { content: [] } } });
    render(
      <VettedClaimDecision
        claim={{ id: 123, vettingStatus: "PARTIAL", payableAmount: 1000 } as Claim}
      />,
    );
    expect(screen.getByText("Payable Amount")).toBeInTheDocument();
    expect(screen.getByText("KES 1000.00")).toBeInTheDocument();
  });

  it("renders reason for non-APPROVED claims", () => {
    (useGetAuditLogsQuery as Mock).mockReturnValue({
      data: { data: { content: [{ reason: "Incorrect invoice details" }] } },
    });
    render(<VettedClaimDecision claim={{ id: 123, vettingStatus: "PARTIAL" } as Claim} />);
    expect(screen.getByText("Reason")).toBeInTheDocument();
    expect(screen.getByText("Incorrect invoice details")).toBeInTheDocument();
  });
});
