import React, { useContext, useState } from "react";
import { TailSpin } from "react-loader-spinner";
import { toast } from "react-toastify";
import { useSaveDocumentMutation } from "../../../../../api/claims-vetting-api/claimVettingApi";
import CloudUploadIcon from "../../../../../components/icons/CloudUploadIcon";
import XIcon from "../../../../../components/icons/XIcon";
import { BatchClaimContext } from "../BatchClaim";

type Props = {
  providerId: number;
  providerName: string;
  setIsShowUploadModal: React.Dispatch<React.SetStateAction<boolean>>;
};

export default function UploadDocumentModal({
  providerId,
  setIsShowUploadModal,
  providerName,
}: Props) {
  const { claim } = useContext(BatchClaimContext);
  const [saveDocument] = useSaveDocumentMutation();
  const [isLoading, setIsLoading] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0] || null;
    if (selectedFile) uploadFileAndClose(selectedFile);
  };

  function handleDrop(event: React.DragEvent<HTMLDivElement>) {
    event.preventDefault();
    setIsDragging(false);

    const file = event.dataTransfer.files?.[0];
    if (file) uploadFileAndClose(file);
  }

  async function uploadFileAndClose(file: File) {
    try {
      setIsLoading(true);

      const formData = new FormData();

      formData.append("providerId", providerId.toString());
      formData.append("providerName", providerName);
      formData.append("type", "INVOICE");
      formData.append("file", file);

      const response = await fetch(`https://api.lctafrica.net/api/file/upload`, {
        method: "POST",
        body: formData,
      });

      const responseBody = await response.json();

      if (!responseBody.data) throw new Error("Failed to upload file, please try again!");

      const saveDocResponse = await saveDocument({
        fileUrl: responseBody.data,
        invoiceNumber: claim?.invoiceNumber as string,
        providerId: claim?.hospitalProviderId as number,
        providerName: claim?.providerName as string,
        type: "INVOICE",
        visitId: claim?.visitNumber as number,
      });

      if ("error" in saveDocResponse) throw new Error("Error saving document!");

      toast.success("Document Uploaded Successfully!");
      setIsShowUploadModal(false);
    } catch (error) {
      if (error instanceof Error) {
        if (error.message.includes("503")) {
          toast.error("Service is temporarily unavailable. Please try again later.");
        } else {
          toast.error(error.message);
        }
      } else {
        const isParsingError =
          typeof error === "object" &&
          error !== null &&
          "status" in error &&
          error?.status === "PARSING_ERROR" &&
          "originalStatus" in error &&
          error?.originalStatus === 503;
        if (isParsingError) {
          toast.error("Service is temporarily unavailable. Please try again later.");
        } else {
          toast.error("An unexpected error occurred. Please try again.");
        }
      }
      console.error("Error: ", error);
    } finally {
      setIsLoading(false);
      setIsShowUploadModal(false);
    }
  }

  return (
    <div className="relative w-full  rounded-md bg-white p-8 shadow-md">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center rounded-md bg-black bg-opacity-20">
          <TailSpin color="blue" />
        </div>
      )}

      <div className="flex justify-between">
        <p className="text-lg font-semibold">
          Supporting Documents <span className="italic">(optional)</span>
        </p>
        <button onClick={() => setIsShowUploadModal(false)}>
          <XIcon />
        </button>
      </div>

      <p className="mt-2 text-sm font-thin italic">
        Feel free to upload any additional documents below...
      </p>

      <div
        className={`mt-12 rounded p-4 shadow-md ${
          isDragging ? "border-4 border-dashed border-blue-400" : ""
        }`}
        onDragOver={(e) => {
          e.preventDefault();
          setIsDragging(true);
        }}
        onDragLeave={() => setIsDragging(false)}
        onDrop={handleDrop}
      >
        <div className="flex flex-col items-center rounded-md border p-4">
          <div className="rounded-lg bg-slate-50 p-2">
            <div className="rounded-lg bg-slate-100 p-4">
              <CloudUploadIcon />
            </div>
          </div>
          <div>
            <label className="mt-12 cursor-pointer text-sm">
              <span className="font-medium text-blue-500">Click to upload</span> or drag and drop
              <input
                onChange={handleFileChange}
                className="hidden"
                type="file"
                accept=".jpg,.jpeg,.png,.pdf" // Restrict file types
              />
            </label>
          </div>
          <p className="mt-2 text-xs">Max file size is 30MB</p>
        </div>
      </div>
    </div>
  );
}
