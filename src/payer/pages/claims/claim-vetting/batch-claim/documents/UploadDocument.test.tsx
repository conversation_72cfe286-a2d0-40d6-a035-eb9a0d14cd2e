import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { Mock, vi } from "vitest";
import UploadDocumentModal from "./UploadDocumentModal";
import { BatchClaimContext } from "../BatchClaim";
import { useSaveDocumentMutation } from "../../../../../api/claims-vetting-api/claimVettingApi";
import { toast } from "react-toastify";
import { Claim } from "../../types";

vi.mock("../../../../../api/claims-vetting-api/claimVettingApi", async () => ({
  ...(await vi.importActual("../../../../../api/claims-vetting-api/claimVettingApi")),
  useSaveDocumentMutation: vi.fn(),
}));

vi.mock("react-toastify", () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe("UploadDocumentModal", () => {
  const mockSetIsShowUploadModal = vi.fn();
  const mockSaveDocument = vi.fn();

  beforeEach(() => {
    (useSaveDocumentMutation as Mock).mockReturnValue([mockSaveDocument]);
  });

  it("renders the modal correctly", () => {
    render(
      <BatchClaimContext.Provider
        value={{
          claim: {
            invoiceNumber: "12345",
            hospitalProviderId: 1,
            providerName: "Test Provider",
            visitNumber: 6789,
          } as Claim,
        }}
      >
        <UploadDocumentModal
          providerId={1}
          providerName="Test Provider"
          setIsShowUploadModal={mockSetIsShowUploadModal}
        />
      </BatchClaimContext.Provider>,
    );

    expect(screen.getByText(/Supporting Documents/i)).toBeInTheDocument();
    expect(
      screen.getByText("Feel free to upload any additional documents below..."),
    ).toBeInTheDocument();
  });

  it("closes the modal when the close button is clicked", () => {
    render(
      <UploadDocumentModal
        providerId={1}
        providerName="Test Provider"
        setIsShowUploadModal={mockSetIsShowUploadModal}
      />,
    );

    fireEvent.click(screen.getByRole("button"));
    expect(mockSetIsShowUploadModal).toHaveBeenCalledWith(false);
  });

  it("uploads a file when selected", async () => {
    // Simulate a successful file upload response from the API
    global.fetch = vi.fn(() =>
      Promise.resolve({
        json: () => Promise.resolve({ data: "mockFileUrl" }),
      } as Response),
    );

    // Simulate a successful saveDocument response.
    // Returning an object that doesn't contain an "error" property triggers success.
    mockSaveDocument.mockResolvedValueOnce({});

    render(
      <BatchClaimContext.Provider
        value={{
          claim: {
            invoiceNumber: "12345",
            hospitalProviderId: 1,
            providerName: "Test Provider",
            visitNumber: 6789,
          } as Claim,
        }}
      >
        <UploadDocumentModal
          providerId={1}
          providerName="Test Provider"
          setIsShowUploadModal={mockSetIsShowUploadModal}
        />
      </BatchClaimContext.Provider>,
    );

    const fileInput = screen.getByLabelText(/Click to upload/i);
    const file = new File(["test content"], "test.pdf", { type: "application/pdf" });

    fireEvent.change(fileInput, { target: { files: [file] } });

    await waitFor(() => expect(mockSaveDocument).toHaveBeenCalled());
    await waitFor(() =>
      expect(toast.success).toHaveBeenCalledWith("Document Uploaded Successfully!"),
    );
  });

  it("shows an error if the upload fails", async () => {
    // Simulate a failed file upload by returning data: null
    global.fetch = vi.fn(() =>
      Promise.resolve({
        json: () => Promise.resolve({ data: null }),
      } as Response),
    );

    render(
      <UploadDocumentModal
        providerId={1}
        providerName="Test Provider"
        setIsShowUploadModal={mockSetIsShowUploadModal}
      />,
    );

    const fileInput = screen.getByLabelText(/Click to upload/i);
    const file = new File(["test content"], "test.pdf", { type: "application/pdf" });

    fireEvent.change(fileInput, { target: { files: [file] } });

    await waitFor(() =>
      expect(toast.error).toHaveBeenCalledWith("Failed to upload file, please try again!"),
    );
  });
});
