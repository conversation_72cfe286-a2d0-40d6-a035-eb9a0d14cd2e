import { fireEvent, render, screen } from "@testing-library/react";
import { vi } from "vitest";
import { Document } from "../../types";
import FileCard from "./FileCard";
import { Provider } from "react-redux";
import { store } from "../../../../../store";

// Mocking the FileIcon and ThreeDotsIcon imports
vi.mock("../../../../../assets/svg/FileIcon.svg", () => ({
  default: "FileIconMock",
}));
vi.mock("../../../../../components/icons/ThreeDotsIcon", () => ({
  default: () => <svg>ThreeDotsIcon</svg>,
}));

describe("FileCard", () => {
  beforeAll(() => {
    global.ResizeObserver = class {
      observe = () => null;
      unobserve = () => null;
      disconnect = () => null;
    };
  });

  const document = {
    type: "PDF",
    createdAt: "2025-01-01T00:00:00Z",
  } as Document;

  it("renders the document type and creation date", () => {
    render(
      <Provider store={store}>
        <FileCard document={document} />
      </Provider>,
    );

    expect(screen.getByText("PDF DOCUMENT")).toBeInTheDocument();
    expect(screen.getByText(new Date(document.createdAt).toLocaleDateString())).toBeInTheDocument();
  });

  it("opens the FileOptionsModal when the three dots button is clicked", () => {
    render(
      <Provider store={store}>
        <FileCard document={document} />
      </Provider>,
    );

    const button = screen.getByRole("button");
    fireEvent.click(button);

    expect(screen.getByTestId("file-options-modal")).toBeInTheDocument();
  });

  it("closes the FileOptionsModal when the modal close button is clicked", () => {
    render(
      <Provider store={store}>
        <FileCard document={document} />
      </Provider>,
    );

    // Open the modal by clicking the button
    const button = screen.getByRole("button");
    fireEvent.click(button);

    // Get the modal
    const modal = screen.getByTestId("file-options-modal");

    // Close the modal by clicking outside (on the backdrop)
    const backdrop = screen.getByRole("back-drop"); // this selects the backdrop div
    fireEvent.click(backdrop);

    // Ensure modal is no longer in the document
    expect(modal).not.toBeInTheDocument();
  });
});
