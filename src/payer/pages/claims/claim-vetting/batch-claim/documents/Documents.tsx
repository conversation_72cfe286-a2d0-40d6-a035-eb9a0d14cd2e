import { useContext, useState } from "react";
import { Tail<PERSON>pin } from "react-loader-spinner";
import { useGetDocumentsQuery } from "../../../../../api/claims-vetting-api/claimVettingApi";
import EmptyStateIcon from "../../../../../components/icons/EmptyStateIcon";
import UploadIcon from "../../../../../components/icons/UploadIcon";
import EmptyState from "../../../../../components/ui/EmptyState";
import DialogWrapper from "../../../../../components/ui/modal/DialogWrapper";
import { Document } from "../../types";
import { BatchClaimContext } from "../BatchClaim";
import FileCard from "./FileCard";
import UploadDocumentModal from "./UploadDocumentModal";

type Props = {
  providerId: number;
  providerName: string;
  visitNumber: number;
};

export default function Documents({ providerId, visitNumber, providerName }: Props) {
  const { claim } = useContext(BatchClaimContext);
  const [isShowUploadModal, setIsShowUploadModal] = useState(false);
  const { data: documentsData, isLoading: isLoadingDocuments } = useGetDocumentsQuery({
    visitNumber: visitNumber,
  });

  const documents = documentsData?.data as Document[];

  return (
    <div className="mt-8">
      <div>
        {claim?.vettingStatus !== "APPROVED" &&
          claim?.vettingStatus !== "DECLINED" &&
          claim?.vettingStatus !== "PARTIAL" && (
            <button
              className="ml-auto flex items-center gap-2 rounded-md bg-blue-600 px-3 py-2 text-xs text-white"
              onClick={() => setIsShowUploadModal(true)}
            >
              <span>
                <UploadIcon />
              </span>
              <span>Upload Document</span>
            </button>
          )}
      </div>

      {isLoadingDocuments ? (
        <div className="flex h-full w-full items-center justify-center" role="loading-indicator">
          <TailSpin color="blue" />
        </div>
      ) : (
        <div className="mt-8 flex flex-col gap-6">
          {/* File Card */}
          {documents && documents.length > 0 ? (
            documents.map((document) => <FileCard key={document.id} document={document} />)
          ) : (
            <EmptyState
              illustration={<EmptyStateIcon />}
              message={{
                title: "No document(s) for this invoice ",
                description: `Click on the “upload document” button to add the necessary documents`,
              }}
            />
          )}
        </div>
      )}

      <DialogWrapper
        maxWidth="max-w-[800px]"
        onClose={() => setIsShowUploadModal(false)}
        show={isShowUploadModal}
      >
        <UploadDocumentModal
          providerName={providerName}
          providerId={providerId}
          setIsShowUploadModal={setIsShowUploadModal}
        />
      </DialogWrapper>
    </div>
  );
}
