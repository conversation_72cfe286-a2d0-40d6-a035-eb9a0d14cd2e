import { CloudArrowDownIcon, TrashIcon } from "@heroicons/react/24/outline";
import React, { useState } from "react";
import { toast } from "react-toastify";
import { useDeleteInvoiceDocumentMutation } from "../../../../../api/claims-vetting-api/claimVettingApi";
import ProgressModal from "../../../../../components/ui/modal/ProgressModal";
import UserService from "../../../../../services/UserService";
import { Document } from "../../types";

type Props = {
  document: Document;
  setIsShowModal: React.Dispatch<React.SetStateAction<boolean>>;
};

export default function FileOptionsModal({ setIsShowModal, document: selectedDocument }: Props) {
  const [deleteInvoiceDocument, { isLoading: isDeletingDocument }] =
    useDeleteInvoiceDocumentMutation();

  async function handleDeleteDocument() {
    try {
      await deleteInvoiceDocument({
        body: {
          actionedBy: UserService.getUsername(),
          documentIds: [selectedDocument.id],
        },
      });

      toast.success("Document deleted successfully!");
    } catch (error) {
      if (error instanceof Error) toast.error(error.message);
      else toast.error("Error occurred");

      console.error(error);
    }
  }

  const [isDownloading, setIsDownloading] = useState(false);

  async function handleDownload() {
    try {
      setIsDownloading(true);
      const url = selectedDocument.fileUrl;

      const response = await fetch(`https://api.lctafrica.net:443/api/file/download?name=${url}`);
      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }
      const result = await response.json();

      if (result.data) {
        const a = document.createElement("a");
        a.href = result.data;
        a.download = url.split("/").pop() || "Document"; // Use file name from URL
        document.body.appendChild(a);
        a.click();
        a.remove();
      } else {
        throw new Error("No URL returned from API");
      }
    } catch (error) {
      if (error instanceof Error) {
        console.error(error.message);
        toast.error(`Download failed: ${error.message}`);
      }
    } finally {
      setIsDownloading(false); // Reset loading state
    }
  }

  return (
    <>
      <div
        className="absolute right-4 top-[70%] z-20 flex flex-col items-start gap-4 rounded border bg-white p-4 text-sm font-medium shadow-md"
        data-testid="file-options-modal"
      >
        <button onClick={handleDownload} className="flex items-center gap-3">
          <span>
            <CloudArrowDownIcon className="w-5" />
          </span>
          <span>Download document</span>
        </button>

        <button className="flex items-center gap-3" onClick={handleDeleteDocument}>
          <TrashIcon className="w-5" />
          <span>Delete document</span>
        </button>
      </div>
      <div
        role="back-drop"
        className="fixed inset-0 z-10 bg-black bg-opacity-50"
        onClick={() => setIsShowModal(false)}
      ></div>

      <ProgressModal
        isProgressModalOpen={isDownloading || isDeletingDocument}
        title="Processing request..."
        onClose={() => null}
      />
    </>
  );
}
