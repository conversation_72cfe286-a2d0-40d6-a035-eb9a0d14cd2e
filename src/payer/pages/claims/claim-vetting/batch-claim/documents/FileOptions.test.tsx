import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { toast } from "react-toastify";
import { Mock, vi } from "vitest";
import { useDeleteInvoiceDocumentMutation } from "../../../../../api/claims-vetting-api/claimVettingApi";
import { Document } from "../../types";
import FileOptionsModal from "./FileOptionsModal";

// --- Mocks ---
vi.mock("../../../../../api/claims-vetting-api/claimVettingApi", () => ({
  useDeleteInvoiceDocumentMutation: vi.fn(),
}));

vi.mock("react-toastify", () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

vi.mock("../../../../../services/UserService", () => ({
  default: {
    getUsername: vi.fn(() => "mockUser"),
  },
}));

describe("FileOptionsModal", () => {
  const mockSetIsShowModal = vi.fn();
  const mockDeleteInvoiceDocument = vi.fn();
  const dummyDocument = { id: "doc123", fileUrl: "https://example.com/file.pdf" };

  beforeAll(() => {
    global.ResizeObserver = class {
      observe = () => null;
      unobserve = () => null;
      disconnect = () => null;
    };
  });

  beforeEach(() => {
    vi.restoreAllMocks(); // Reset all mocks before each test
    mockSetIsShowModal.mockReset();
    mockDeleteInvoiceDocument.mockReset();
    (useDeleteInvoiceDocumentMutation as Mock).mockReturnValue([
      mockDeleteInvoiceDocument,
      { isLoading: false },
    ]);
  });

  it("renders the download and delete buttons", () => {
    render(
      <FileOptionsModal
        setIsShowModal={mockSetIsShowModal}
        document={dummyDocument as unknown as Document}
      />,
    );
    expect(screen.getByText(/Download document/i)).toBeInTheDocument();
    expect(screen.getByText(/Delete document/i)).toBeInTheDocument();
  });

  it("closes the modal when the backdrop is clicked", async () => {
    render(
      <FileOptionsModal
        setIsShowModal={mockSetIsShowModal}
        document={dummyDocument as unknown as Document}
      />,
    );

    const backdrop = document.querySelector(".fixed.inset-0");
    if (backdrop) {
      await userEvent.click(backdrop);
    }
    expect(mockSetIsShowModal).toHaveBeenCalledWith(false);
  });

  it("downloads a file successfully", async () => {
    global.fetch = vi.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ data: "https://example.com/download/file.pdf" }),
      } as Response),
    );

    render(
      <FileOptionsModal
        setIsShowModal={mockSetIsShowModal}
        document={dummyDocument as unknown as Document}
      />,
    );

    const downloadButton = screen.getByText(/Download document/i);
    await userEvent.click(downloadButton);

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        `https://api.lctafrica.net:443/api/file/download?name=${dummyDocument.fileUrl}`,
      );
    });

    expect(toast.error).not.toHaveBeenCalled(); // Ensure no error toast was called
  });

  it("handles download errors", async () => {
    global.fetch = vi.fn(() =>
      Promise.resolve({
        ok: false,
        status: 500,
        statusText: "Internal Server Error",
        json: () => Promise.resolve({}),
      } as Response),
    );

    render(
      <FileOptionsModal
        setIsShowModal={mockSetIsShowModal}
        document={dummyDocument as unknown as Document}
      />,
    );

    const downloadButton = screen.getByText(/Download document/i);
    await userEvent.click(downloadButton);

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith("Download failed: Error 500: Internal Server Error");
    });
  });

  it("deletes the document successfully", async () => {
    mockDeleteInvoiceDocument.mockResolvedValueOnce({});

    render(
      <FileOptionsModal
        setIsShowModal={mockSetIsShowModal}
        document={dummyDocument as unknown as Document}
      />,
    );

    const deleteButton = screen.getByText(/Delete document/i);
    await userEvent.click(deleteButton);

    await waitFor(() => {
      expect(mockDeleteInvoiceDocument).toHaveBeenCalledWith({
        body: {
          actionedBy: "mockUser",
          documentIds: [dummyDocument.id],
        },
      });
    });

    await waitFor(() => {
      expect(toast.success).toHaveBeenCalledWith("Document deleted successfully!");
    });
  });

  it("handles errors when deleting a document", async () => {
    mockDeleteInvoiceDocument.mockRejectedValueOnce(new Error("Delete failed"));

    render(
      <FileOptionsModal
        setIsShowModal={mockSetIsShowModal}
        document={dummyDocument as unknown as Document}
      />,
    );

    const deleteButton = screen.getByText(/Delete document/i);
    await userEvent.click(deleteButton);

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith("Delete failed");
    });
  });
});
