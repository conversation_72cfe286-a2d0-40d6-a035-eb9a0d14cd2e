import { render, screen } from "@testing-library/react";
import { Mock, vi } from "vitest";
import { useGetDocumentsQuery } from "../../../../../api/claims-vetting-api/claimVettingApi";
import { BatchClaimContext } from "../BatchClaim";
import Documents from "./Documents";

// --- Mocks ---
vi.mock("../../../../../api/claims-vetting-api/claimVettingApi", async () => ({
  ...(await vi.importActual("../../../../../api/claims-vetting-api/claimVettingApi")),
  useGetDocumentsQuery: vi.fn(),
}));

const mockClaim = { vettingStatus: "PENDING" };

describe("Documents Component", () => {
  const providerId = 123;
  const providerName = "Test Provider";
  const visitNumber = 456;

  function renderComponent() {
    return render(
      <BatchClaimContext.Provider value={{ claim: mockClaim }}>
        <Documents providerId={providerId} providerName={providerName} visitNumber={visitNumber} />
      </BatchClaimContext.Provider>,
    );
  }

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders the upload button when vetting status allows it", () => {
    (useGetDocumentsQuery as Mock).mockReturnValue({ data: { data: [] }, isLoading: false });

    renderComponent();

    expect(screen.getByRole("button", { name: /Upload Document/i })).toBeInTheDocument();
  });

  it("does not show the upload button if claim is approved, declined, or partial", () => {
    const forbiddenStatuses = ["APPROVED", "DECLINED", "PARTIAL"];

    forbiddenStatuses.forEach((status) => {
      render(
        <BatchClaimContext.Provider value={{ claim: { vettingStatus: status } }}>
          <Documents
            providerId={providerId}
            providerName={providerName}
            visitNumber={visitNumber}
          />
        </BatchClaimContext.Provider>,
      );
      expect(screen.queryByRole("button", { name: /Upload Document/i })).not.toBeInTheDocument();
    });
  });

  it("shows loading spinner when documents are loading", () => {
    (useGetDocumentsQuery as Mock).mockReturnValue({ data: null, isLoading: true });

    renderComponent();

    expect(screen.getByRole("loading-indicator")).toBeInTheDocument();
  });

  it("shows an empty state when there are no documents", async () => {
    (useGetDocumentsQuery as Mock).mockReturnValue({ data: { data: [] }, isLoading: false });

    renderComponent();

    expect(await screen.findByText(/No document\(s\) for this invoice/i)).toBeInTheDocument();
  });
});
