import React, { useState } from "react";
import FileIcon from "../../../../../assets/svg/FileIcon.svg";
import ThreeDotsIcon from "../../../../../components/icons/ThreeDotsIcon";
import { Document } from "../../types";
import FileOptionsModal from "./FileOptionsModal";

type Props = {
  document: Document;
};

export default function FileCard({ document }: Props) {
  const [isShowModal, setIsShowModal] = useState(false);

  return (
    <div className="relative flex items-center gap-8 rounded-md border-2 px-8 py-4">
      <span>
        <img className="w-[50px]" src={FileIcon} alt="" />
      </span>

      <div>
        <p className="text-lg font-semibold text-slate-600">{document.type} DOCUMENT</p>
        <p className="mt-1 flex items-center gap-2 text-sm text-slate-400">
          <span>{document.createdAt && new Date(document.createdAt).toLocaleDateString()}</span>
          <span>{/* 332 KB */}</span>
        </p>
      </div>

      <button className="ml-auto" onClick={() => setIsShowModal(true)}>
        <ThreeDotsIcon />
      </button>

      {isShowModal && <FileOptionsModal document={document} setIsShowModal={setIsShowModal} />}
    </div>
  );
}
