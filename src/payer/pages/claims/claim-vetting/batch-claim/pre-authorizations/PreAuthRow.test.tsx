import { fireEvent, render, screen } from "@testing-library/react";
import { PreAuth } from "../../preAuthType";
import PreAuthRow from "./PreAuthRow";

const mockPreAuth = {
  visit: {
    id: 12345,
    memberName: "John Doe",
    memberNumber: "M12345",
    benefitName: "Inpatient Cover",
    totalInvoiceAmount: 50000,
  },
  schemeName: "Health Plan A",
  status: "Approved",
  supportingDocuments: [],
} as unknown as PreAuth;

describe("PreAuthRow", () => {
  it("renders pre-auth details correctly", () => {
    render(<PreAuthRow preAuth={mockPreAuth} />);

    expect(screen.getByText("12345")).toBeInTheDocument();
    expect(screen.getByText("John Doe")).toBeInTheDocument();
    expect(screen.getByText("M12345")).toBeInTheDocument();
    expect(screen.getByText("Health Plan A")).toBeInTheDocument();
    expect(screen.getByText("Inpatient Cover")).toBeInTheDocument();
    expect(screen.getByText("KES 50,000.00")).toBeInTheDocument();
    expect(screen.getByText("Approved")).toBeInTheDocument();
  });

  it("toggles breakdown visibility on button click", () => {
    render(<PreAuthRow preAuth={mockPreAuth} />);
    const toggleButton = screen.getByRole("button");

    // Initially, breakdown should not be rendered
    expect(screen.queryByTestId("preauth-breakdown"))?.not.toBeInTheDocument();

    // Click button to show breakdown
    fireEvent.click(toggleButton);
    expect(screen.getByTestId("preauth-breakdown")).toBeInTheDocument();

    // Click button again to hide breakdown
    fireEvent.click(toggleButton);
    expect(screen.queryByTestId("preauth-breakdown"))?.not.toBeInTheDocument();
  });
});
