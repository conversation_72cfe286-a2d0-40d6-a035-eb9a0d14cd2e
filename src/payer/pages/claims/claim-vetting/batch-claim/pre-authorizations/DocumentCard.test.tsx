import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { toast } from "react-toastify";
import { vi } from "vitest";
import DocumentCard from "./DocumentCard";

vi.mock("react-toastify", () => ({
  toast: { error: vi.fn() },
}));

beforeEach(() => {
  vi.clearAllMocks();
});

describe("DocumentCard", () => {
  it("renders the correct file icon", () => {
    const { rerender } = render(<DocumentCard url="file.csv" />);
    expect(screen.getByTestId("csv-icon")).toBeInTheDocument();

    rerender(<DocumentCard url="file.pdf" />);
    expect(screen.getByTestId("pdf-icon")).toBeInTheDocument();
  });

  it("displays 'Downloading...' while fetching", async () => {
    global.fetch = vi.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ data: "mocked-url" }),
      } as Response),
    );

    render(<DocumentCard url="file.pdf" />);
    const button = screen.getByRole("button");
    fireEvent.click(button);
    expect(button).toHaveTextContent("Downloading...");

    await waitFor(() => expect(button).toHaveTextContent("New Invoice"));
  });

  it("handles API errors and shows toast message", async () => {
    global.fetch = vi.fn(() =>
      Promise.resolve({ ok: false, status: 404, statusText: "Not Found" } as Response),
    );

    render(<DocumentCard url="file.pdf" />);
    fireEvent.click(screen.getByRole("button"));

    await waitFor(() =>
      expect(toast.error).toHaveBeenCalledWith("Download failed: Error 404: Not Found"),
    );
  });
});
