import { render, screen } from "@testing-library/react";
import { vi } from "vitest";
import { PreAuth } from "../../preAuthType";
import PreAuthBreakDown from "./PreAuthBreakDown";

vi.mock("./DocumentCard", () => ({
  default: ({ url }: { url: string }) => <div data-testid="document-card">{url}</div>,
}));

describe("PreAuthBreakDown", () => {
  const mockPreAuth: PreAuth = {
    visit: {
      id: 12345,
      benefitName: "Outpatient",
      totalInvoiceAmount: 5000,
      memberNumber: "M-001",
      memberName: "John Doe",
    },
    service: "Consultation",
    authorizedAmount: 3000,
    requestType: "New Request",
    notes: "General check-up",
    providerName: "ABC Hospital",
    reference: "REF-123",
    schemeName: "Gold Plan",
    status: "Approved",
    supportingDocuments: ["doc1.pdf", "doc2.pdf"],
  } as PreAuth;

  it("renders the pre-authorization breakdown details", () => {
    render(
      <table>
        <tbody>
          <PreAuthBreakDown preAuth={mockPreAuth} />
        </tbody>
      </table>,
    );

    expect(screen.getByText("Pre-authorization Breakdown")).toBeInTheDocument();
    expect(screen.getByText("Visit Number")).toBeInTheDocument();
    expect(screen.getByText(mockPreAuth.visit.id)).toBeInTheDocument();
    expect(screen.getByText("Benefit")).toBeInTheDocument();
    expect(screen.getByText(mockPreAuth.visit.benefitName)).toBeInTheDocument();
    expect(screen.getByText("Service")).toBeInTheDocument();
    expect(screen.getByText(mockPreAuth.service)).toBeInTheDocument();
    expect(screen.getByText("Status")).toBeInTheDocument();
    expect(screen.getByText(mockPreAuth.status)).toBeInTheDocument();
  });

  it("renders supporting documents", () => {
    render(
      <table>
        <tbody>
          <PreAuthBreakDown preAuth={mockPreAuth} />
        </tbody>
      </table>,
    );

    const documents = screen.getAllByTestId("document-card");
    expect(documents).toHaveLength(mockPreAuth.supportingDocuments.length);
    expect(documents[0]).toHaveTextContent("doc1.pdf");
    expect(documents[1]).toHaveTextContent("doc2.pdf");
  });
});
