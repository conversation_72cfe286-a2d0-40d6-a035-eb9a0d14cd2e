import { useState } from "react";
import { toast } from "react-toastify";
import CsvFileIcon from "../../../../../components/icons/CsvFileIcon";
import JpgFileIcon from "../../../../../components/icons/JpgFileIcon";
import PdfFileIcon from "../../../../../components/icons/PdfFileIcon";
import PngFileIcon from "../../../../../components/icons/PngFileIcon";

type Props = {
  url: string;
};

export default function DocumentCard({ url }: Props) {
  const [loading, setLoading] = useState(false);

  async function fetchSignedUrl() {
    setLoading(true); // Set loading state
    try {
      const response = await fetch(`https://api.lctafrica.net:443/api/file/download?name=${url}`);
      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }
      const result = await response.json();

      if (result.data) {
        const a = document.createElement("a");
        a.href = result.data;
        a.download = url.split("/").pop() || "Document"; // Use file name from URL
        document.body.appendChild(a);
        a.click();
        a.remove();
      } else {
        throw new Error("No URL returned from API");
      }
    } catch (error) {
      if (error instanceof Error) {
        console.error(error.message);
        toast.error(`Download failed: ${error.message}`);
      }
    } finally {
      setLoading(false); // Reset loading state
    }
  }

  return (
    <>
      <button
        onClick={fetchSignedUrl}
        className={`flex flex-col items-center rounded bg-slate-100 p-3 ${
          loading ? "cursor-wait" : ""
        }`}
        disabled={loading}
      >
        {url.endsWith(".csv") ? (
          <CsvFileIcon />
        ) : url.endsWith(".pdf") ? (
          <PdfFileIcon />
        ) : url.endsWith(".png") ? (
          <PngFileIcon />
        ) : (
          url.endsWith(".jpg") && <JpgFileIcon />
        )}
        <p>{loading ? "Downloading..." : "New Invoice"}</p>
      </button>
    </>
  );
}
