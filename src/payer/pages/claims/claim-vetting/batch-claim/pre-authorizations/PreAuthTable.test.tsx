import { render, screen } from "@testing-library/react";
import { Mock, vi } from "vitest";
import { useGetPreAuthorizationsQuery } from "../../../../../api/claims-vetting-api/claimVettingApi";
import { BatchClaimContext } from "../BatchClaim";
import PreAuthTable from "./PreAuthTable";
import { PreAuth } from "../../preAuthType";
import { Claim } from "../../types";

// Mock external components
vi.mock("react-loader-spinner", () => ({
  TailSpin: () => <div data-testid="tail-spin" />,
}));

vi.mock("../../../../../components/icons/EmptyStateIcon", () => ({
  default: () => <div data-testid="empty-state-icon" />,
}));

vi.mock("./PreAuthRow", () => ({
  default: ({ preAuth }: { preAuth: PreAuth }) => <tr data-testid="pre-auth-row">{preAuth.id}</tr>,
}));

// Mock API hook
vi.mock("../../../../../api/claims-vetting-api/claimVettingApi", async () => ({
  ...(await vi.importActual("../../../../../api/claims-vetting-api/claimVettingApi")),
  useGetPreAuthorizationsQuery: vi.fn(),
}));

describe("PreAuthTable", () => {
  const mockClaim = { visitNumber: 123 } as Claim;

  const renderComponent = () => {
    return render(
      <BatchClaimContext.Provider value={{ claim: mockClaim }}>
        <PreAuthTable />
      </BatchClaimContext.Provider>,
    );
  };

  it("renders loading state with TailSpin", () => {
    (useGetPreAuthorizationsQuery as Mock).mockReturnValue({ isLoading: true, data: null });
    renderComponent();
    expect(screen.getByTestId("tail-spin")).toBeInTheDocument();
  });

  it("renders empty state with EmptyStateIcon when no pre-authorizations exist", () => {
    (useGetPreAuthorizationsQuery as Mock).mockReturnValue({
      isLoading: false,
      data: { data: [] },
    });
    renderComponent();
    expect(screen.getByTestId("empty-state-icon")).toBeInTheDocument();
    expect(screen.getByText("No pre-authorizations(s) for this invoice")).toBeInTheDocument();
  });

  it("renders pre-authorization rows when data is available", () => {
    const mockPreAuths = [{ id: 1 }, { id: 2 }];
    (useGetPreAuthorizationsQuery as Mock).mockReturnValue({
      isLoading: false,
      data: { data: mockPreAuths },
    });
    renderComponent();
    expect(screen.getAllByTestId("pre-auth-row").length).toBe(2);
  });
});
