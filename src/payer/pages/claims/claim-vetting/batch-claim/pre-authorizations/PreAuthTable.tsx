import { useContext } from "react";
import { useGetPreAuthorizationsQuery } from "../../../../../api/claims-vetting-api/claimVettingApi";
import { BatchClaimContext } from "../BatchClaim";
import PreAuthRow from "./PreAuthRow";
import EmptyStateIcon from "../../../../../components/icons/EmptyStateIcon";
import { TailSpin } from "react-loader-spinner";

export default function PreAuthTable() {
  const { claim } = useContext(BatchClaimContext);

  const { data: preAuthsData, isLoading: isLoadingPreAuths } = useGetPreAuthorizationsQuery(
    claim?.visitNumber as number,
  );

  const preAuths = preAuthsData?.data;

  return isLoadingPreAuths ? (
    <div className="flex h-full w-full items-center justify-center">
      <TailSpin color="blue" />
    </div>
  ) : !preAuths || preAuths?.length < 1 ? (
    <div className="mt-8 flex flex-col items-center justify-center">
      <EmptyStateIcon />
      <p className="mt-8 text-2xl font-semibold">No pre-authorizations(s) for this invoice </p>
    </div>
  ) : (
    <table className="mt-6 w-full table-auto border-collapse text-left text-sm">
      <tbody>{preAuths?.map((preAuth) => <PreAuthRow key={preAuth.id} preAuth={preAuth} />)}</tbody>
    </table>
  );
}
