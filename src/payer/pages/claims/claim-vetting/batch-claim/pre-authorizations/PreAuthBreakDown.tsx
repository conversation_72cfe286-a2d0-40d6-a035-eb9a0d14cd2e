import React from "react";
import { PreAuth } from "../../preAuthType";
import { formatNumberToKes } from "../../../../../utils/formatCurrency";
import DocumentCard from "./DocumentCard";

type Props = {
  preAuth: PreAuth;
};

export default function PreAuthBreakDown({ preAuth }: Props) {
  return (
    <tr data-testid="preauth-breakdown">
      <td colSpan={7}>
        <div className="mb-8  mt-5">
          <p className="text-lg font-medium text-slate-600">Pre-authorization Breakdown</p>

          <div className="mt-4 grid grid-cols-3 grid-rows-5 gap-2">
            <div>
              <p className="text-sm text-slate-500">Visit Number</p>
              <p>{preAuth.visit.id}</p>
            </div>

            <div>
              <p className="text-sm text-slate-500">Benefit</p>
              <p>{preAuth.visit.benefitName}</p>
            </div>

            <div>
              <p className="text-sm text-slate-500">Amount</p>
              <p>{formatNumberToKes(preAuth.visit.totalInvoiceAmount)}</p>
            </div>

            <div>
              <p className="text-sm text-slate-500">Member Number</p>
              <p>{preAuth.visit.memberNumber}</p>
            </div>

            <div>
              <p className="text-sm text-slate-500">Service</p>
              <p>{preAuth.service}</p>
            </div>

            <div>
              <p className="text-sm text-slate-500">Approved Amount</p>
              <p>{formatNumberToKes(preAuth.authorizedAmount)}</p>
            </div>

            <div>
              <p className="text-sm text-slate-500">Member Name</p>
              <p>{preAuth.visit.memberName}</p>
            </div>

            <div>
              <p className="text-sm text-slate-500">Request Type</p>
              <p>{preAuth.requestType}</p>
            </div>

            <div>
              <p className=" text-sm text-slate-500">Pre-diagnosis</p>
              <p>{preAuth.notes}</p>
            </div>

            <div>
              <p className="text-sm text-slate-500">Provider</p>
              <p>{preAuth.providerName}</p>
            </div>

            <div>
              <p className="text-sm text-slate-500">Reference Number</p>
              <p>{preAuth.reference}</p>
            </div>

            <div>
              <p className="text-sm text-slate-500">Scheme</p>
              <p>{preAuth.schemeName}</p>
            </div>

            <div>
              <p className="text-sm text-slate-500">Status</p>
              <p>{preAuth.status}</p>
            </div>
          </div>

          {/* Supporting documents */}

          <p className="mt-6 text-lg  font-medium text-slate-600">Supporting Documents</p>

          <div className="mt-4 grid grid-cols-4 gap-8 text-[10px] font-medium text-slate-500">
            {/* File */}
            {preAuth.supportingDocuments.map((document) => (
              <DocumentCard key={document} url={document} />
            ))}
          </div>
        </div>
      </td>
    </tr>
  );
}
