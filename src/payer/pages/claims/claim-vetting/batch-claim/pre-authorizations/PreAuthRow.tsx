import { ChevronDownIcon, ChevronUpIcon } from "@heroicons/react/24/outline";
import { useState } from "react";
import Badge from "../../../../../components/ui/Badge";
import TableDataItem from "../../../../../components/ui/table/TableDataItem";
import { formatNumberToKes } from "../../../../../utils/formatCurrency";
import { PreAuth } from "../../preAuthType";
import PreAuthBreakDown from "./PreAuthBreakDown";

type Props = {
  preAuth: PreAuth;
};

export default function PreAuthRow({ preAuth }: Props) {
  const [isShowBreakDown, setIsShowBreakDown] = useState(false);

  return (
    <>
      <tr className="">
        <td className="px-1 py-4">
          <button
            className="flex items-center justify-center"
            onClick={() => setIsShowBreakDown(!isShowBreakDown)}
          >
            {isShowBreakDown ? (
              <ChevronUpIcon className="h-5 w-5 text-slate-800" aria-hidden="true" />
            ) : (
              <ChevronDownIcon className="h-5 w-5 text-slate-800" aria-hidden="true" />
            )}
          </button>
        </td>

        <TableDataItem item={preAuth.visit.id} />
        <TableDataItem item={preAuth.visit.memberName} />
        <TableDataItem item={preAuth.visit.memberNumber} />
        <TableDataItem item={preAuth.schemeName} />
        <TableDataItem item={preAuth.visit.benefitName} />
        <TableDataItem item={formatNumberToKes(preAuth.visit.totalInvoiceAmount)} />
        <td className="px-1 py-4">
          <Badge text={preAuth.status} color="green" />
        </td>

        {/* modal */}
      </tr>
      {isShowBreakDown && <PreAuthBreakDown preAuth={preAuth} />}
    </>
  );
}
