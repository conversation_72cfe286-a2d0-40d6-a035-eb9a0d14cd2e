import { TrashIcon } from "@heroicons/react/24/outline";
import TableDataItem from "../../../../../components/ui/table/TableDataItem";
import TableHeaderItem from "../../../../../components/ui/table/TableHeaderItem";
import { ICD10 } from "../../types";

type Props = {
  diagnosisList: ICD10[];
  setDiagnosisList: React.Dispatch<React.SetStateAction<ICD10[]>>;
};

export default function DiagnosisList({ diagnosisList, setDiagnosisList }: Props) {
  function handleRemoveDiagnosis(diagnosisId: number) {
    const newDiagnosisList = diagnosisList.filter((diagnosis) => diagnosis.id !== diagnosisId);
    setDiagnosisList(newDiagnosisList);
  }

  return (
    <table className="ml-4 mt-8 w-full text-left text-xs">
      <thead>
        <tr>
          <TableHeaderItem item="Code" />
          <TableHeaderItem item="Title" />
          <TableHeaderItem item="Remove" />
        </tr>
      </thead>
      <tbody>
        {diagnosisList.map((diagnosis) => (
          <tr key={diagnosis.id}>
            <TableDataItem item={diagnosis.code} />
            <TableDataItem item={diagnosis.title} />
            <td>
              <button
                type="button"
                className="flex w-full justify-center"
                onClick={() => handleRemoveDiagnosis(diagnosis.id)}
              >
                <TrashIcon className="w-5 text-red-500" />
              </button>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}
