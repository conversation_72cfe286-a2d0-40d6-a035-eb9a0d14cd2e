import { fireEvent, render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import { Mock, vi } from "vitest";
import {
  useGetDiagnosisQuery,
  useGetProcedureCodesQuery,
} from "../../../../../api/claims-vetting-api/claimVettingApi";
import { store } from "../../../../../store";
import { BatchClaimContext } from "../BatchClaim";
import DiagnosisAndProcedure from "./DiagnosisAndProcedure";
import userEvent from "@testing-library/user-event";
import { Claim } from "../../types";

vi.mock("../../../../../api/claims-vetting-api/claimVettingApi", async (originalImport) => {
  const original = await originalImport();
  return {
    ...(typeof original === "object" && original !== null ? original : {}),
    useGetDiagnosisQuery: vi.fn(),
    useGetProcedureCodesQuery: vi.fn(),
  };
});

describe("DiagnosisAndProcedure Component", () => {
  const mockClaim = { vettingStatus: "PENDING" };
  const mockDiagnosis = [{ code: "D123", title: "Mock Diagnosis" }];
  const mockProcedures = [{ procedure_code: "P456", procedure_description: "Mock Procedure" }];

  beforeEach(() => {
    (useGetDiagnosisQuery as Mock).mockReturnValue({
      data: { data: mockDiagnosis },
      isLoading: false,
    });
    (useGetProcedureCodesQuery as Mock).mockReturnValue({
      data: { data: mockProcedures },
      isLoading: false,
    });
  });

  beforeAll(() => {
    global.ResizeObserver = class {
      observe = () => null;
      unobserve = () => null;
      disconnect = () => null;
    };
  });

  it("renders benefit name correctly", () => {
    render(
      <Provider store={store}>
        <BatchClaimContext.Provider value={{ claim: mockClaim as Claim }}>
          <DiagnosisAndProcedure visitId={1} benefitName="Mock Benefit" invoiceNumber="12345" />
        </BatchClaimContext.Provider>
      </Provider>,
    );

    expect(screen.getByText("Benefit Name")).toBeInTheDocument();
    expect(screen.getByText("Mock Benefit")).toBeInTheDocument();
  });

  it("renders diagnosis list correctly", () => {
    render(
      <Provider store={store}>
        <BatchClaimContext.Provider value={{ claim: mockClaim as Claim }}>
          <DiagnosisAndProcedure visitId={1} benefitName="Mock Benefit" invoiceNumber="12345" />
        </BatchClaimContext.Provider>
      </Provider>,
    );

    expect(screen.getByText("Diagnosis")).toBeInTheDocument();
    expect(screen.getByText("D123")).toBeInTheDocument();
    expect(screen.getByText("Mock Diagnosis")).toBeInTheDocument();
  });

  it("renders procedures list correctly", () => {
    render(
      <Provider store={store}>
        <BatchClaimContext.Provider value={{ claim: mockClaim as Claim }}>
          <DiagnosisAndProcedure visitId={1} benefitName="Mock Benefit" invoiceNumber="12345" />
        </BatchClaimContext.Provider>
      </Provider>,
    );

    expect(screen.getByText("Procedures")).toBeInTheDocument();
    expect(screen.getByText("P456")).toBeInTheDocument();
    expect(screen.getByText("Mock Procedure")).toBeInTheDocument();
  });

  it("opens add diagnosis modal on button click", async () => {
    render(
      <Provider store={store}>
        <BatchClaimContext.Provider value={{ claim: mockClaim as Claim }}>
          <DiagnosisAndProcedure visitId={1} benefitName="Mock Benefit" invoiceNumber="12345" />
        </BatchClaimContext.Provider>
      </Provider>,
    );

    const addDiagnosisButton = screen.getByText("+ Add diagnosis");
    await userEvent.click(addDiagnosisButton);
    expect(screen.getByRole("heading", { name: /Add Diagnosis/i })).toBeInTheDocument();
  });

  it("opens add procedure modal on button click", () => {
    render(
      <Provider store={store}>
        <BatchClaimContext.Provider value={{ claim: mockClaim as Claim }}>
          <DiagnosisAndProcedure visitId={1} benefitName="Mock Benefit" invoiceNumber="12345" />
        </BatchClaimContext.Provider>
      </Provider>,
    );

    const addProcedureButton = screen.getByText("+ Add procedure");
    fireEvent.click(addProcedureButton);
    expect(screen.getByRole("heading", { name: /Add Procedure/i })).toBeInTheDocument();
  });
});
