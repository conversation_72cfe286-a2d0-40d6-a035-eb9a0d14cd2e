import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { toast } from "react-toastify";
import { Mock, vi } from "vitest";
import {
  useAddProcedureMutation,
  useSearchProcedureQuery,
} from "../../../../../api/claims-vetting-api/claimVettingApi";
import { Claim } from "../../types";
import { BatchClaimContext } from "../BatchClaim";
import AddProcedureModal from "./AddProcedureModal";

// Mock the API hooks.
vi.mock("../../../../../api/claims-vetting-api/claimVettingApi", async () => ({
  ...(await vi.importActual("../../../../../api/claims-vetting-api/claimVettingApi")),
  useAddProcedureMutation: vi.fn(() => [vi.fn()]),
  useSearchProcedureQuery: vi.fn(() => ({
    data: { data: { content: [] } },
    refetch: vi.fn(),
  })),
}));

// Mock toast notifications.
vi.mock("react-toastify", () => ({
  toast: { success: vi.fn(), error: vi.fn() },
}));

// Mock child components so tests don’t depend on their implementations.
// For ProcedureDescriptionInput, we render an input that calls setItemValue when changed.
vi.mock("./ProcedureDescriptionInput", () => ({
  default: ({ setItemValue }: { setItemValue: (val: unknown) => void }) => (
    <input
      data-testid="procedure-description-input"
      placeholder="Search ..."
      onChange={() =>
        setItemValue({
          id: "proc1",
          procedure_code: "001",
          procedure_description: "Test procedure",
        })
      }
    />
  ),
}));

// For ProceduresList, render a simple placeholder.
vi.mock("./ProceduresList", () => ({
  default: () => <div>Procedures List</div>,
}));

describe("AddProcedureModal", () => {
  const setIsShowAddProcedureModal = vi.fn();
  const mockClaim = { invoiceNumber: "INV123", hospitalProviderId: 456 };

  beforeEach(() => {
    vi.clearAllMocks();
    // Provide a default return value for useSearchProcedureQuery so that destructuring works.
    (useSearchProcedureQuery as Mock).mockReturnValue({
      data: { data: { content: [] } },
      refetch: vi.fn(),
    });
  });

  it("renders the modal correctly", () => {
    render(
      <BatchClaimContext.Provider value={{ claim: mockClaim as Claim }}>
        <AddProcedureModal
          setIsShowAddProcedureModal={setIsShowAddProcedureModal}
          savedProceduresList={[]}
        />
      </BatchClaimContext.Provider>,
    );

    // Use a role-based query to get the heading.
    expect(screen.getByRole("heading", { name: /add procedure/i })).toBeInTheDocument();
  });

  it("calls setIsShowAddProcedureModal when close button is clicked", () => {
    render(
      <BatchClaimContext.Provider value={{ claim: mockClaim as Claim }}>
        <AddProcedureModal
          setIsShowAddProcedureModal={setIsShowAddProcedureModal}
          savedProceduresList={[]}
        />
      </BatchClaimContext.Provider>,
    );

    // The close button (with the XIcon) is the first button in the header.
    const closeButton = screen.getAllByRole("button")[0];
    fireEvent.click(closeButton as HTMLElement);
    expect(setIsShowAddProcedureModal).toHaveBeenCalledWith(false);
  });

  it("displays error if no procedure is selected when adding", async () => {
    render(
      <BatchClaimContext.Provider value={{ claim: mockClaim as Claim }}>
        <AddProcedureModal
          setIsShowAddProcedureModal={setIsShowAddProcedureModal}
          savedProceduresList={[]}
        />
      </BatchClaimContext.Provider>,
    );

    // Instead of using getByText (which returns the heading too), use getByRole to get the button.
    const addProcedureButton = screen.getByRole("button", {
      name: /add procedure/i,
    });
    fireEvent.click(addProcedureButton);

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith("Please select a description!");
    });
  });

  it("displays error if no procedures are added before saving", async () => {
    const saveProcedureMock = vi.fn().mockResolvedValue({});
    (useAddProcedureMutation as Mock).mockReturnValue([saveProcedureMock]);

    render(
      <BatchClaimContext.Provider value={{ claim: mockClaim as Claim }}>
        <AddProcedureModal
          setIsShowAddProcedureModal={setIsShowAddProcedureModal}
          savedProceduresList={[]}
        />
      </BatchClaimContext.Provider>,
    );

    // Click the "Update" button.
    const updateButton = screen.getByRole("button", { name: /update/i });
    fireEvent.click(updateButton);

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith("Please add procedures before updating!");
    });
  });

  it("calls saveProcedure API when procedures are added and saved", async () => {
    const saveProcedureMock = vi.fn().mockResolvedValue({});
    (useAddProcedureMutation as Mock).mockReturnValue([saveProcedureMock]);

    render(
      <BatchClaimContext.Provider value={{ claim: mockClaim as Claim }}>
        <AddProcedureModal
          setIsShowAddProcedureModal={setIsShowAddProcedureModal}
          savedProceduresList={[]}
        />
      </BatchClaimContext.Provider>,
    );

    // Simulate selecting a procedure by triggering a change on the input.
    const procedureInput = screen.getByTestId("procedure-description-input");
    fireEvent.change(procedureInput, { target: { value: "Test procedure" } });

    // Click the "Add procedure" button within the form.
    const addProcedureButton = screen.getByRole("button", {
      name: /add procedure/i,
    });
    fireEvent.click(addProcedureButton);

    // Now click the "Update" button to trigger the API call.
    const updateButton = screen.getByRole("button", { name: /update/i });
    fireEvent.click(updateButton);

    await waitFor(() => {
      expect(saveProcedureMock).toHaveBeenCalledWith({
        invoiceNumber: mockClaim.invoiceNumber,
        providerId: mockClaim.hospitalProviderId,
        lineItems: [
          {
            invoiceNumber: mockClaim.invoiceNumber,
            itemAmount: 0,
            itemCode: "001",
            itemDescription: "Test procedure",
            itemQuantity: 1,
            lineType: "MEDICALPROCEDURE",
          },
        ],
      });
      expect(toast.success).toHaveBeenCalledWith("Procedure added successfully!");
      expect(setIsShowAddProcedureModal).toHaveBeenCalledWith(false);
    });
  });
});
