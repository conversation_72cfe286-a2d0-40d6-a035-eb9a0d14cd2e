import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { toast } from "react-toastify";
import { Mock, vi } from "vitest";
import {
  useAddDiagnosisMutation,
  useSearchICD10ByTitleQuery,
} from "../../../../../api/claims-vetting-api/claimVettingApi";
import { ICD10 } from "../../types";
import AddDiagnosisModal from "./AddDiagnosisModal";

// --- Mocks ---

// Mock the API hooks with default return values.
vi.mock("../../../../../api/claims-vetting-api/claimVettingApi", async () => ({
  ...(await vi.importActual("../../../../../api/claims-vetting-api/claimVettingApi")),
  useAddDiagnosisMutation: vi.fn(() => [vi.fn()]),
  useSearchICD10ByTitleQuery: vi.fn(() => ({
    data: { data: { content: [] } },
    refetch: vi.fn(),
  })),
}));

// Mock toast notifications.
vi.mock("react-toastify", () => ({
  toast: { success: vi.fn(), error: vi.fn() },
}));

// Mock DiagnosisDescriptionInput so we can simulate a diagnosis selection.
vi.mock("./DiagnosisDescriptionInput", () => ({
  default: ({ setItemValue }: { setItemValue: (val: unknown) => void }) => (
    <input
      data-testid="diagnosis-description-input"
      placeholder="Search description"
      onChange={() =>
        setItemValue({
          id: "diag1",
          code: "A01",
          title: "Test diagnosis",
        })
      }
    />
  ),
}));

// Mock DiagnosisList as a simple placeholder.
vi.mock("./DiagnosisList", () => ({
  default: () => <div>Diagnosis List</div>,
}));

// --- Test Suite ---

describe("AddDiagnosisModal", () => {
  const setIsShowAddDiagnosisModal = vi.fn();
  const visitId = 123;
  const savedDiagnosisList: ICD10[] = []; // initially empty

  beforeEach(() => {
    vi.clearAllMocks();
    // Ensure useSearchICD10ByTitleQuery returns a default structure.
    (useSearchICD10ByTitleQuery as Mock).mockReturnValue({
      data: { data: { content: [] } },
      refetch: vi.fn(),
    });
  });

  it("renders the modal correctly", () => {
    render(
      <AddDiagnosisModal
        visitId={visitId}
        setIsShowAddDiagnosisModal={setIsShowAddDiagnosisModal}
        savedDiagnosisList={savedDiagnosisList}
      />,
    );

    expect(screen.getByRole("heading", { name: /add diagnosis/i })).toBeInTheDocument();
  });

  it("calls setIsShowAddDiagnosisModal when close button is clicked", () => {
    render(
      <AddDiagnosisModal
        visitId={visitId}
        setIsShowAddDiagnosisModal={setIsShowAddDiagnosisModal}
        savedDiagnosisList={savedDiagnosisList}
      />,
    );

    // The close button is rendered in the header as the first button.
    const closeButton = screen.getAllByRole("button")[0];
    fireEvent.click(closeButton as HTMLElement);
    expect(setIsShowAddDiagnosisModal).toHaveBeenCalledWith(false);
  });

  it("displays error if diagnosis already saved", async () => {
    // Provide a saved diagnosis that matches the test diagnosis (by code).
    const savedList = [{ id: 1, code: "A01", title: "Saved diagnosis" } as ICD10];
    render(
      <AddDiagnosisModal
        visitId={visitId}
        setIsShowAddDiagnosisModal={setIsShowAddDiagnosisModal}
        savedDiagnosisList={savedList}
      />,
    );

    const diagnosisInput = screen.getByTestId("diagnosis-description-input");
    fireEvent.change(diagnosisInput, { target: { value: "Test diagnosis" } });

    const addDiagnosisButton = screen.getByRole("button", {
      name: /add diagnosis/i,
    });
    fireEvent.click(addDiagnosisButton);

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith("Diagnosis already saved!");
    });
  });

  it("displays error if no diagnoses are added before updating", async () => {
    const updateDiagnosisMock = vi.fn().mockResolvedValue({});
    (useAddDiagnosisMutation as Mock).mockReturnValue([updateDiagnosisMock]);

    render(
      <AddDiagnosisModal
        visitId={visitId}
        setIsShowAddDiagnosisModal={setIsShowAddDiagnosisModal}
        savedDiagnosisList={savedDiagnosisList}
      />,
    );

    // Click the "Update" button without adding any diagnosis.
    const updateButton = screen.getByRole("button", { name: /update/i });
    fireEvent.click(updateButton);

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith("Please add diagnosis");
    });
  });

  it("calls updateDiagnosis API when diagnoses are added and update is clicked", async () => {
    const updateDiagnosisMock = vi.fn().mockResolvedValue({});
    (useAddDiagnosisMutation as Mock).mockReturnValue([updateDiagnosisMock]);

    render(
      <AddDiagnosisModal
        visitId={visitId}
        setIsShowAddDiagnosisModal={setIsShowAddDiagnosisModal}
        savedDiagnosisList={savedDiagnosisList}
      />,
    );

    // Simulate selecting a diagnosis.
    const diagnosisInput = screen.getByTestId("diagnosis-description-input");
    fireEvent.change(diagnosisInput, { target: { value: "Test diagnosis" } });

    // Click the "Add diagnosis" button to add it to the list.
    const addDiagnosisButton = screen.getByRole("button", {
      name: /add diagnosis/i,
    });
    fireEvent.click(addDiagnosisButton);

    // Click the "Update" button to trigger the updateDiagnosis API.
    const updateButton = screen.getByRole("button", { name: /update/i });
    fireEvent.click(updateButton);

    await waitFor(() => {
      expect(updateDiagnosisMock).toHaveBeenCalledWith({
        visitId: visitId,
        description: "Test diagnosis",
        icd10code: "A01",
      });
      expect(toast.success).toHaveBeenCalledWith("Diagnosis added successfully! ");
      expect(setIsShowAddDiagnosisModal).toHaveBeenCalledWith(false);
    });
  });
});
