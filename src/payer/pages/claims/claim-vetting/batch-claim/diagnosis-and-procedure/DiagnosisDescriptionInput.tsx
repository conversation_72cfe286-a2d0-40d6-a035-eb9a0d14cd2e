import { Combobox, Transition } from "@headlessui/react";
import { CheckIcon } from "@heroicons/react/20/solid";
import { ChevronDownIcon } from "@heroicons/react/24/outline";
import React, { Fragment } from "react";
import { ICD10 } from "../../types";

type Props = {
  placeHolder: string;
  items: ICD10[];
  itemValue: ICD10;
  setItemValue: React.Dispatch<React.SetStateAction<ICD10 | null>>;
  searchQuery: string;
  setSearchQuery: React.Dispatch<React.SetStateAction<string>>;
};

export default function DiagnosisDescriptionInput({
  placeHolder,
  items = [],
  itemValue,
  setItemValue,
  searchQuery,
  setSearchQuery,
}: Props) {
  const filteredItems = items.filter((item) =>
    item.title.toLocaleLowerCase().includes(searchQuery.toLocaleLowerCase()),
  );

  const displayValue = (itemValue: ICD10) => {
    if (!itemValue) return "";
    const selectedItem = items.find((item) => item.id === itemValue.id);
    return selectedItem ? selectedItem.title : "";
  };
  return (
    <Combobox value={itemValue} onChange={setItemValue}>
      <div className="relative z-20 mt-1">
        <div className="relative w-full cursor-default overflow-hidden rounded-lg border-2 bg-white text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-blue-300 sm:text-xs">
          <Combobox.Input
            className="w-full border-none py-1 pl-2 pr-10 text-xs leading-5 text-gray-900 focus:ring-0"
            // displayValue={() => ""}
            placeholder={placeHolder}
            onChange={(event) => setSearchQuery(event.target.value)}
            displayValue={displayValue}
          />
          <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
            <ChevronDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
          </Combobox.Button>
        </div>
        <Transition
          as={Fragment}
          leave="transition ease-in duration-100"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
          afterLeave={() => setSearchQuery("")}
        >
          <Combobox.Options className="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-xs">
            {filteredItems && filteredItems.length === 0 && searchQuery !== "" ? (
              <div className="relative cursor-default select-none px-4 py-2 text-gray-700">
                Nothing found.
              </div>
            ) : (
              filteredItems.map((item) => (
                <Combobox.Option
                  key={item.id}
                  className={({ active }) =>
                    `relative cursor-default select-none py-2 pl-10 pr-4 ${
                      active ? "bg-blue-500 text-white" : "text-gray-900"
                    }`
                  }
                  value={item}
                >
                  {({ selected, active }) => (
                    <>
                      <span
                        className={`block truncate ${selected ? "font-medium" : "font-normal"}`}
                      >
                        {item.title}
                      </span>
                      {selected ? (
                        <span
                          className={`absolute inset-y-0 left-0 flex items-center pl-3 ${
                            active ? "text-white" : "text-blue-500"
                          }`}
                        >
                          <CheckIcon className="h-5 w-5" aria-hidden="true" />
                        </span>
                      ) : null}
                    </>
                  )}
                </Combobox.Option>
              ))
            )}
          </Combobox.Options>
        </Transition>
      </div>
    </Combobox>
  );
}
