import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import {
  useAddDiagnosisMutation,
  useSearchICD10ByTitleQuery,
} from "../../../../../api/claims-vetting-api/claimVettingApi";
import XIcon from "../../../../../components/icons/XIcon";
import { ICD10 } from "../../types";
import DiagnosisDescriptionInput from "./DiagnosisDescriptionInput";
import DiagnosisList from "./DiagnosisList";

type Props = {
  visitId: number;
  setIsShowAddDiagnosisModal: React.Dispatch<React.SetStateAction<boolean>>;
  savedDiagnosisList: ICD10[];
};

export default function AddDiagnosisModal({
  visitId,
  setIsShowAddDiagnosisModal,
  savedDiagnosisList,
}: Props) {
  // const userInfo = UserService.kcObject.tokenParsed as UserInfo;
  const [searchQuery, setSearchQuery] = useState("");
  const [description, setDescription] = useState<ICD10 | null>(null);
  const [diagnosisList, setDiagnosisList] = useState<ICD10[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const { data: ICD10Data, refetch: refetchICD10 } = useSearchICD10ByTitleQuery({
    page: 1,
    size: 50,
    title: searchQuery || " ",
  });

  const [updateDiagnosis] = useAddDiagnosisMutation();

  const ICD10 = ICD10Data?.data.content;

  useEffect(() => {
    if (searchQuery.trim().length < 1) return;
    refetchICD10();
  }, [searchQuery, refetchICD10]);

  async function handleUpdateDiagnosis() {
    try {
      if (!diagnosisList || diagnosisList.length < 1) {
        toast.error("Please add diagnosis");
        return;
      }
      setIsLoading(true);

      if (!visitId) {
        toast.error("Claim has no visit number/ID!");
        return;
      }

      diagnosisList.forEach(async (diagnosis) => {
        await updateDiagnosis({
          visitId: visitId,
          description: diagnosis.title,
          icd10code: diagnosis.code,
        });
      });

      toast.success("Diagnosis added successfully! ");
      setIsShowAddDiagnosisModal(false);
    } catch (error) {
      if (error instanceof Error) {
        console.error(error.message);
        toast.error(error.message);
      }
    } finally {
      setIsLoading(false);
    }
  }

  function handleAddDiagnosis(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    if (!description) return;

    if (diagnosisList.some((diagnosis) => diagnosis.id === description.id)) {
      toast.error("Diagnosis already added to list!");
      return;
    }

    if (savedDiagnosisList.some((diagnosis) => diagnosis.code === description.code)) {
      toast.error("Diagnosis already saved!");
      return;
    }

    setDiagnosisList([...diagnosisList, description as ICD10]);
    setDescription(null);
  }

  return (
    <div className=" rounded-md bg-white p-8  ">
      <div className="mb-4 flex items-center justify-between">
        <h2 className=" text-lg font-semibold">Add diagnosis</h2>

        <button onClick={() => setIsShowAddDiagnosisModal(false)} type="button">
          <XIcon />
        </button>
      </div>

      <form
        className=" relative h-full max-h-[60vh] overflow-y-auto"
        onSubmit={(e) => handleAddDiagnosis(e)}
      >
        <fieldset className="h-full min-h-[300px] rounded-md border-2 px-4  py-1 pb-16">
          <legend className="text-xs">
            Diagnosis <span className="text-red-600">*</span>
          </legend>

          <div className="ml-4 flex flex-col gap-2">
            <label className=" mt-3 text-xs">
              Description <span className="text-red-600">*</span>
            </label>

            <DiagnosisDescriptionInput
              itemValue={description as ICD10}
              items={ICD10 as ICD10[]}
              placeHolder="Search description"
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              setItemValue={setDescription}
            />
          </div>

          <DiagnosisList diagnosisList={diagnosisList} setDiagnosisList={setDiagnosisList} />

          <button className="sticky bottom-4 left-4 mt-2 rounded bg-blue-200 px-3 py-2 text-xs font-medium text-blue-600 ">
            Add diagnosis
          </button>
        </fieldset>
      </form>

      <div className="mt-4 flex justify-end text-sm font-medium">
        <button
          disabled={isLoading}
          className={` rounded-md bg-blue-500 px-3 py-2 text-white ${isLoading && "opacity-50"}`}
          onClick={handleUpdateDiagnosis}
        >
          {isLoading ? "Saving changes..." : "Update"}
        </button>
      </div>
    </div>
  );
}
