import React, { useState } from "react";
import { useGetAllPayerBenefitCatalogsQuery } from "../../../../../api/claims-vetting-api/claimVettingApi";
import XIcon from "../../../../../components/icons/XIcon";
import UserService from "../../../../../services/UserService";
import { UserInfo } from "../../types";

type Props = {
  currentBenefit: string;
  setIsShowEditBenefitModal: React.Dispatch<React.SetStateAction<boolean>>;
};

export default function EditBenefitModal({ currentBenefit, setIsShowEditBenefitModal }: Props) {
  const userInfo = UserService.kcObject.tokenParsed as UserInfo;

  const [benefit, setBenefit] = useState("");
  const [reason, setReason] = useState("");

  const { data: benefitCatalogsData } = useGetAllPayerBenefitCatalogsQuery(userInfo.payerId);

  const benefitCatalogs = benefitCatalogsData?.data;

  return (
    <form
      className="min-w-[40%] rounded-md bg-white p-8"
      // onSubmit={(e) => handleEditInvoiceNumber(e)}
    >
      <div className="mb-8 flex items-center justify-between">
        <p className=" text-lg font-semibold">Edit benefit</p>

        <button onClick={() => setIsShowEditBenefitModal(false)} type="button">
          <XIcon />
        </button>
      </div>

      <div className="flex items-center justify-between gap-8">
        <div className="flex grow flex-col gap-1">
          <label className="text-xs" htmlFor="">
            Current benefit
          </label>
          <input
            className="w-full rounded-md border-slate-300 p-2 text-xs "
            disabled
            type="text"
            value={currentBenefit}
          />
        </div>

        <div className="flex grow flex-col gap-1">
          <label className="text-xs" htmlFor="">
            New benefit
          </label>
          <select
            className="w-full rounded-md border-slate-300 p-2 text-xs "
            required
            value={benefit}
            onChange={(e) => setBenefit(e.target.value)}
          >
            <option disabled value="">
              Select new benefit
            </option>
            {benefitCatalogs?.map((benefitCatalog) => (
              <option key={benefitCatalog.id} value={benefitCatalog.name}>
                {benefitCatalog.name}
              </option>
            ))}{" "}
          </select>
        </div>
      </div>

      <fieldset className="mt-12  rounded-md border-2 px-4 py-1">
        <legend className="text-xs">
          Reason <span className="text-red-600">*</span>
        </legend>
        <textarea
          className="h-full min-h-[100px] w-full rounded-md border-none text-sm"
          placeholder="Add a reason for the update..."
          required
          value={reason}
          onChange={(e) => setReason(e.target.value)}
        />
      </fieldset>

      <div className="mt-8 flex justify-end text-sm font-medium">
        <button
          disabled={true}
          className={` rounded-md bg-blue-500 px-3 py-2 text-white ${true && "opacity-50"}`}
        >
          {false ? "Saving changes..." : "Update"}
        </button>
      </div>
    </form>
  );
}
