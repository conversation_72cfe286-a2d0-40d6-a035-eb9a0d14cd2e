import { TrashIcon } from "@heroicons/react/24/outline";
import TableDataItem from "../../../../../components/ui/table/TableDataItem";
import TableHeaderItem from "../../../../../components/ui/table/TableHeaderItem";
import { Procedure } from "../../types";

type Props = {
  proceduresList: Procedure[];
  setProceduresList: React.Dispatch<React.SetStateAction<Procedure[]>>;
};

export default function ProceduresList({ proceduresList, setProceduresList }: Props) {
  function handleRemoveProcedure(procedureId: number) {
    const newProcedure = proceduresList.filter((procedure) => procedure.id !== procedureId);
    setProceduresList(newProcedure);
  }
  return (
    <table className="ml-4 mt-8 w-full text-left text-xs">
      <thead>
        <tr>
          <TableHeaderItem item="Code" />
          <TableHeaderItem item="Description" />
          <TableHeaderItem item="Remove" />
        </tr>
      </thead>
      <tbody>
        {proceduresList.map((procedure) => (
          <tr>
            <TableDataItem item={procedure.procedure_code} />
            <TableDataItem item={procedure.procedure_description} />
            <td>
              <button
              type="button"
                className="flex w-full justify-center"
                onClick={() => handleRemoveProcedure(procedure.id)}
              >
                <TrashIcon className="w-5 text-red-500" />
              </button>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}
