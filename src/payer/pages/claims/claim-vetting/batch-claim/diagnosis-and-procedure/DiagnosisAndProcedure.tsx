import { useContext, useState } from "react";
import EditBenefitModal from "./EditBenefitModal";

import { PencilIcon } from "@heroicons/react/24/outline";
import { TailSpin } from "react-loader-spinner";
import {
  useGetDiagnosisQuery,
  useGetProcedureCodesQuery,
} from "../../../../../api/claims-vetting-api/claimVettingApi";
import EmptyStateIcon from "../../../../../components/icons/EmptyStateIcon";
import DialogWrapper from "../../../../../components/ui/modal/DialogWrapper";
import { BatchClaimContext } from "../BatchClaim";
import AddDiagnosisModal from "./AddDiagnosisModal";
import AddProcedureModal from "./AddProcedureModal";

type Props = {
  benefitName: string;
  visitId: number;
  invoiceNumber: string;
};

export default function DiagnosisAndProcedure({ visitId, benefitName }: Props) {
  const [isShowEditBenefitModal, setIsShowEditBenefitModal] = useState(false);
  const [isShowAddDiagnosisModal, setIsShowAddDiagnosisModal] = useState(false);
  const [isShowAddProcedureModal, setIsShowAddProcedureModal] = useState(false);

  const { data: diagnosisData, isLoading: isLoadingDiagnosis } = useGetDiagnosisQuery(visitId);
  const diagnosis = diagnosisData?.data || [];

  const { data: proceduresData, isLoading: isLoadingProcedures } =
    useGetProcedureCodesQuery(visitId);
  const procedures = proceduresData?.data || [];

  const { claim } = useContext(BatchClaimContext);

  return (
    <div className="mt-8">
      <div>
        <p className="flex items-center gap-2 text-sm text-slate-500">
          <span>Benefit Name</span>
          {/*//todo: api to implement this does not exist yet, might take some time to be implemented */}

          {!(
            claim?.vettingStatus === "APPROVED" ||
            claim?.vettingStatus === "DECLINED" ||
            claim?.vettingStatus === "PARTIAL"
          ) && (
            // todo: once api is implemented, enable the button and change text color to `text-btnBlue`
            <button disabled onClick={() => setIsShowEditBenefitModal(true)}>
              <PencilIcon className="h-4 w-4" />
            </button>
          )}
        </p>

        <p className="mt-2">{benefitName}</p>

        <DialogWrapper
          maxWidth="max-w-[800px]"
          onClose={() => setIsShowEditBenefitModal(false)}
          show={isShowEditBenefitModal}
        >
          <EditBenefitModal
            setIsShowEditBenefitModal={setIsShowEditBenefitModal}
            currentBenefit={benefitName}
          />
        </DialogWrapper>
      </div>

      {/* Diagnosis */}
      <div className="mt-8">
        <div className="flex items-center justify-between">
          <p className="text-lg font-semibold">Diagnosis</p>
          {claim?.vettingStatus !== "APPROVED" &&
            claim?.vettingStatus !== "DECLINED" &&
            claim?.vettingStatus !== "PARTIAL" && (
              <button
                className="rounded-md bg-blue-600 px-3 py-2 text-xs text-white"
                onClick={() => setIsShowAddDiagnosisModal(true)}
              >
                + Add diagnosis
              </button>
            )}

          <DialogWrapper
            maxWidth="max-w-[700px]"
            onClose={() => setIsShowAddDiagnosisModal(false)}
            show={isShowAddDiagnosisModal}
          >
            <AddDiagnosisModal
              visitId={visitId}
              setIsShowAddDiagnosisModal={setIsShowAddDiagnosisModal}
              savedDiagnosisList={diagnosis}
            />
          </DialogWrapper>
        </div>

        {/* Diagnosis List */}

        {isLoadingDiagnosis ? (
          <div className="flex h-full w-full items-center justify-center">
            <TailSpin color="blue" />
          </div>
        ) : (
          <div className=" text-sm">
            {diagnosis && diagnosis?.length > 0 ? (
              diagnosis?.map((diagnosis) => (
                <div className="border-b-2 py-2">
                  <p className="flex items-center gap-2 font-medium">
                    <span className="text-slate-600">Code:</span>
                    <span>{diagnosis.code}</span>
                  </p>

                  <p className="flex items-center gap-2 font-medium">
                    <span className="text-slate-600">Title:</span>
                    <span>{diagnosis.title}</span>
                  </p>
                </div>
              ))
            ) : (
              <div className="mt-8 flex h-[200px] flex-col items-center justify-center">
                <EmptyStateIcon />
                <p className="mt-8 text-2xl font-semibold">No diagnosis for this invoice </p>
                <p className="mt-4 font-medium text-slate-500">
                  Click on the “add diagnosis ” button to add the necessary diagnosis{" "}
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Procedures */}
      <div className="mt-8">
        <div className="flex items-center justify-between">
          <p className="text-lg font-semibold">Procedures</p>

          {claim?.vettingStatus !== "APPROVED" &&
            claim?.vettingStatus !== "DECLINED" &&
            claim?.vettingStatus !== "PARTIAL" && (
              <button
                className="rounded-md bg-blue-600 px-3 py-2 text-xs text-white"
                onClick={() => setIsShowAddProcedureModal(true)}
              >
                + Add procedure
              </button>
            )}

          <DialogWrapper
            maxWidth="max-w-[700px]"
            onClose={() => setIsShowAddProcedureModal(false)}
            show={isShowAddProcedureModal}
          >
            <AddProcedureModal
              setIsShowAddProcedureModal={setIsShowAddProcedureModal}
              savedProceduresList={procedures}
            />
          </DialogWrapper>
        </div>

        {isLoadingProcedures ? (
          <div className="flex h-full w-full items-center justify-center">
            <TailSpin color="blue" />
          </div>
        ) : (
          <div className=" text-sm">
            {procedures && procedures.length > 0 ? (
              procedures.map((procedure) => (
                <div className="border-b-2 py-2">
                  <p className="flex items-center gap-2 font-medium">
                    <span className="text-slate-600">Code:</span>
                    <span>{procedure.procedure_code}</span>
                  </p>

                  <p className="flex items-center gap-2 font-medium">
                    <span className="text-slate-600">Title:</span>
                    <span>{procedure.procedure_description}</span>
                  </p>
                </div>
              ))
            ) : (
              <div className="mt-8 flex h-[200px] flex-col items-center justify-center">
                <EmptyStateIcon />
                <p className="mt-8 text-2xl font-semibold">No procedures for this invoice </p>
                <p className="mt-4 font-medium text-slate-500">
                  Click on the “add procedure ” button to add the necessary procedure{" "}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Procedures List */}
      </div>
    </div>
  );
}
