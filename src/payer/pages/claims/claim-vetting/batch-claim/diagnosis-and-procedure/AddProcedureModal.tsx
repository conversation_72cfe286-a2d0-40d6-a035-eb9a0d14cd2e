import { useContext, useEffect, useState } from "react";
import { toast } from "react-toastify";
import {
  useAddProcedureMutation,
  useSearchProcedureQuery,
} from "../../../../../api/claims-vetting-api/claimVettingApi";
import XIcon from "../../../../../components/icons/XIcon";
import { Procedure } from "../../types";
import { BatchClaimContext } from "../BatchClaim";
import ProcedureDescriptionInput from "./ProcedureDescriptionInput";
import ProceduresList from "./ProceduresList";

type Props = {
  setIsShowAddProcedureModal: React.Dispatch<React.SetStateAction<boolean>>;
  savedProceduresList: Procedure[];
};

export default function AddProcedureModal({
  setIsShowAddProcedureModal,
  savedProceduresList,
}: Props) {
  const [searchQuery, setSearchQuery] = useState("");
  const [description, setDescription] = useState<Procedure | null>(null);
  const [proceduresList, setProceduresList] = useState<Procedure[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { claim } = useContext(BatchClaimContext);

  const { data: proceduresData, refetch: refetchProcedures } = useSearchProcedureQuery({
    page: 1,
    size: 50,
    title: searchQuery || " ",
  });

  const procedures = proceduresData?.data.content;

  const [saveProcedure] = useAddProcedureMutation();

  useEffect(() => {
    if (searchQuery.trim().length < 1) return;
    refetchProcedures();
  }, [searchQuery, refetchProcedures]);

  async function handleSaveProcedures() {
    try {
      if (!proceduresList || proceduresList.length < 1) {
        throw new Error("Please add procedures before updating!");
      }

      setIsLoading(true);

      await saveProcedure({
        invoiceNumber: claim?.invoiceNumber as string,
        providerId: claim?.hospitalProviderId as number,
        lineItems: proceduresList.map((procedure) => ({
          invoiceNumber: claim?.invoiceNumber as string,
          itemAmount: 0,
          itemCode: procedure.procedure_code,
          itemDescription: procedure.procedure_description,
          itemQuantity: 1,
          lineType: "MEDICALPROCEDURE",
        })),
      });

      toast.success("Procedure added successfully!");
    } catch (error) {
      if (error instanceof Error) {
        console.error(error.message);
        toast.error(error.message);
      }
    } finally {
      setIsLoading(false);
      setIsShowAddProcedureModal(false);
    }
  }

  function handleAddProcedure(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    if (!description) {
      toast.error("Please select a description!");
      return;
    }

    if (proceduresList.some((procedure) => procedure.id === description.id)) {
      toast.error("Procedure already added to list!");
      return;
    }

    if (
      savedProceduresList.some(
        (procedure) => procedure.procedure_code === description.procedure_code,
      )
    ) {
      toast.error("Procedure already saved!");
      return;
    }

    setProceduresList([...proceduresList, description]);

    setDescription(null);
  }

  return (
    <div className="rounded-md bg-white p-8">
      <div className="mb-4 flex items-center justify-between">
        <h2 className=" text-lg font-semibold">Add procedure</h2>

        <button onClick={() => setIsShowAddProcedureModal(false)} type="button">
          <XIcon />
        </button>
      </div>

      <form
        className=" relative h-full max-h-[60vh] overflow-y-auto"
        onSubmit={(e) => handleAddProcedure(e)}
      >
        <fieldset className="h-full min-h-[300px] rounded-md border-2 px-4  py-1 pb-16">
          <legend className="text-xs">
            Procedure <span className="text-red-600">*</span>
          </legend>

          <div className="ml-4 flex flex-col gap-2">
            <label className=" mt-3 text-xs">
              Description <span className="text-red-600">*</span>
            </label>

            <ProcedureDescriptionInput
              itemValue={description as Procedure}
              items={procedures as Procedure[]}
              placeHolder="Search ..."
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              setItemValue={setDescription}
            />
          </div>

          <ProceduresList proceduresList={proceduresList} setProceduresList={setProceduresList} />

          <button className="sticky bottom-4 left-4 mt-2 rounded bg-blue-200 px-3 py-2 text-xs font-medium text-blue-600 ">
            Add procedure
          </button>
        </fieldset>
      </form>

      <div className="mt-8 flex justify-end text-sm font-medium">
        <button
          disabled={isLoading}
          className={` rounded-md bg-blue-500 px-3 py-2 text-white ${isLoading && "opacity-50"}`}
          onClick={handleSaveProcedures}
        >
          {isLoading ? "Saving changes..." : "Update"}
        </button>
      </div>
    </div>
  );
}
