// EditBenefitModal.test.tsx
import { fireEvent, render, screen } from "@testing-library/react";
import { Mock, vi } from "vitest";
import EditBenefitModal from "./EditBenefitModal"; // adjust the import path as needed

// Mock data for the benefit catalogs returned from the API
const mockBenefitCatalogs = [
  { id: "1", name: "Benefit A" },
  { id: "2", name: "Benefit B" },
];

// --- <PERSON>cks ---

// Mock the API hook that fetches benefit catalogs
vi.mock("../../../../../api/claims-vetting-api/claimVettingApi", () => ({
  useGetAllPayerBenefitCatalogsQuery: vi.fn(),
}));

// Mock the UserService to provide a dummy payerId
vi.mock("../../../../../services/UserService", () => ({
  default: {
    kcObject: {
      tokenParsed: { payerId: "payer-123" },
    },
  },
}));

// Mock the XIcon component to render accessible text for testing
vi.mock("../../../../../components/icons/XIcon", () => ({
  default: () => <span>XIcon</span>,
}));

// Import the mocked hook so we can set its return value in our tests.
import { useGetAllPayerBenefitCatalogsQuery } from "../../../../../api/claims-vetting-api/claimVettingApi";

describe("EditBenefitModal", () => {
  beforeEach(() => {
    // Whenever the hook is called, return our mock benefit catalogs data.
    (useGetAllPayerBenefitCatalogsQuery as Mock).mockReturnValue({
      data: { data: mockBenefitCatalogs },
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it("renders the modal with the current benefit and all fields", () => {
    const mockSetIsShowEditBenefitModal = vi.fn();

    render(
      <EditBenefitModal
        currentBenefit="Test Benefit"
        setIsShowEditBenefitModal={mockSetIsShowEditBenefitModal}
      />,
    );

    // Check for the modal title
    expect(screen.getByText("Edit benefit")).toBeInTheDocument();

    // Check that the current benefit input is rendered, has the proper value, and is disabled
    const currentBenefitInput = screen.getByDisplayValue("Test Benefit") as HTMLInputElement;
    expect(currentBenefitInput).toBeInTheDocument();
    expect(currentBenefitInput).toHaveAttribute("disabled");

    // Check that the select for new benefit is rendered and has the default value
    const benefitSelect = screen.getByRole("combobox") as HTMLSelectElement;
    expect(benefitSelect).toBeInTheDocument();
    expect(benefitSelect).toHaveValue("");

    // Check that the default disabled option and the API benefit options are present
    expect(screen.getByText("Select new benefit")).toBeInTheDocument();
    expect(screen.getByText("Benefit A")).toBeInTheDocument();
    expect(screen.getByText("Benefit B")).toBeInTheDocument();

    // Check that the reason textarea is rendered with the correct placeholder text
    const reasonTextarea = screen.getByPlaceholderText("Add a reason for the update...");
    expect(reasonTextarea).toBeInTheDocument();

    // Check that the update button is rendered and disabled (as per the component's logic)
    const updateButton = screen.getByRole("button", { name: /update/i });
    expect(updateButton).toBeDisabled();
  });

  it("calls setIsShowEditBenefitModal with false when the close (X) button is clicked", () => {
    const mockSetIsShowEditBenefitModal = vi.fn();

    render(
      <EditBenefitModal
        currentBenefit="Test Benefit"
        setIsShowEditBenefitModal={mockSetIsShowEditBenefitModal}
      />,
    );

    // The close button renders the XIcon with text "XIcon" (from our mock), so we can query it by its accessible name.
    const closeButton = screen.getByRole("button", { name: /xicon/i });
    fireEvent.click(closeButton);

    expect(mockSetIsShowEditBenefitModal).toHaveBeenCalledWith(false);
  });

  it("updates the benefit select and reason textarea when changed", () => {
    const mockSetIsShowEditBenefitModal = vi.fn();

    render(
      <EditBenefitModal
        currentBenefit="Test Benefit"
        setIsShowEditBenefitModal={mockSetIsShowEditBenefitModal}
      />,
    );

    // Change the new benefit selection.
    const benefitSelect = screen.getByRole("combobox") as HTMLSelectElement;
    fireEvent.change(benefitSelect, { target: { value: "Benefit A" } });
    expect(benefitSelect.value).toBe("Benefit A");

    // Change the reason textarea.
    const reasonTextarea: HTMLInputElement = screen.getByPlaceholderText(
      "Add a reason for the update...",
    );
    fireEvent.change(reasonTextarea, { target: { value: "Updated reason" } });
    expect(reasonTextarea.value).toBe("Updated reason");
  });
});
