import { createContext, useState } from "react";
import { Tai<PERSON><PERSON><PERSON> } from "react-loader-spinner";
import { useNavigate, useParams } from "react-router-dom";
import { useGetIndividualBatchClaimQuery } from "../../../../api/claims-vetting-api/claimVettingApi";
import LeftArrowIcon from "../../../../components/icons/LeftArrowIcon";
import Badge, { BadgeColor } from "../../../../components/ui/Badge";
import MainWrapper from "../../../../components/ui/MainWrapper";
import Text from "../../../../components/ui/typography/Text";
import UserService from "../../../../services/UserService";
import { Claim, UserInfo } from "../types";
import Decision from "./decision/Decision";
import DiagnosisAndProcedure from "./diagnosis-and-procedure/DiagnosisAndProcedure";
import Documents from "./documents/Documents";
import InvoiceDetails from "./invoice-details/InvoiceDetails";
import PreAuthorizations from "./pre-authorizations/PreAuthorizations";

type BatchClaimContextType = {
  claim: Claim | null;
};

export const BatchClaimContext = createContext<BatchClaimContextType>({ claim: null });

export default function BatchClaim() {
  const userInfo = UserService.kcObject.tokenParsed as UserInfo;

  const { batchId, claimId } = useParams();
  const navigate = useNavigate();
  const DISPLAYS = {
    invoiceDetails: "Invoice Details",
    diagnosisAndProcedure: "Diagnosis & Procedure",
    documents: "Documents",
    preAuthorizations: "Pre-authorizations",
    decision: "Decision",
  };

  const [display, setDisplay] = useState(DISPLAYS.invoiceDetails);

  const { data: claimData, isLoading: isLoadingClaim } = useGetIndividualBatchClaimQuery({
    batchIds: Number(batchId),
    invoiceIds: Number(claimId),
    payerId: userInfo.payerId,
  });

  const claim = claimData?.data.content[0] as Claim;

  const badgeColor: BadgeColor =
    claim?.vettingStatus === "DECLINED"
      ? "red"
      : claim?.vettingStatus === "BATCHED"
        ? "yellow"
        : claim?.vettingStatus === "APPROVED"
          ? "green"
          : claim?.vettingStatus === "PARTIAL"
            ? "blue"
            : "gray";

  const badgeText =
    claim?.vettingStatus === "DECLINED"
      ? "Declined"
      : claim?.vettingStatus === "BATCHED"
        ? "Batched"
        : claim?.vettingStatus === "APPROVED"
          ? "Approved"
          : claim?.vettingStatus === "PARTIAL"
            ? "Partial"
            : claim?.vettingStatus;

  return (
    <MainWrapper>
      <div className="mb-4 flex items-center gap-4">
        <button className="rounded-md bg-slate-300 p-2" onClick={() => navigate(-1)}>
          <LeftArrowIcon />
        </button>

        <Text variant="heading" className="text-lg">
          Batch Invoice Number /{" "}
          {!isLoadingClaim && (
            <span className="text-black">{claim?.batchInvoiceNumber || "NA"}</span>
          )}
        </Text>

        <Badge color={badgeColor} text={badgeText as string} textClassName="text-xs" />
      </div>

      {/* Navigation buttons */}
      <div className="mt-8 flex justify-between  font-medium text-slate-500">
        <button
          className={`${
            display === DISPLAYS.invoiceDetails && "bg-blue-100 text-blue-700 "
          } rounded-md px-2 py-1  `}
          onClick={() => setDisplay(DISPLAYS.invoiceDetails)}
        >
          Invoice Details
        </button>
        <button
          className={`${
            display === DISPLAYS.diagnosisAndProcedure && "bg-blue-100 text-blue-700 "
          } rounded-md px-2 py-1  `}
          onClick={() => setDisplay(DISPLAYS.diagnosisAndProcedure)}
        >
          Diagnosis & Procedure
        </button>
        <button
          className={`${
            display === DISPLAYS.documents && "bg-blue-100 text-blue-700 "
          } rounded-md px-2 py-1  `}
          onClick={() => setDisplay(DISPLAYS.documents)}
        >
          Documents
        </button>
        <button
          className={`${
            display === DISPLAYS.preAuthorizations && "bg-blue-100 text-blue-700 "
          } rounded-md px-2 py-1  `}
          onClick={() => setDisplay(DISPLAYS.preAuthorizations)}
        >
          Pre-authorizations
        </button>
        <button
          className={`${
            display === DISPLAYS.decision && "bg-blue-100 text-blue-700 "
          } rounded-md px-2 py-1  `}
          onClick={() => setDisplay(DISPLAYS.decision)}
        >
          Decision
        </button>
      </div>

      {/* DISPLAYS */}
      <BatchClaimContext.Provider value={{ claim }}>
        {display === DISPLAYS.invoiceDetails ? (
          isLoadingClaim ? (
            <div className="flex h-full w-full items-center justify-center">
              <TailSpin color="blue" />
            </div>
          ) : (
            <InvoiceDetails claim={claim} />
          )
        ) : display === DISPLAYS.diagnosisAndProcedure ? (
          <DiagnosisAndProcedure
            invoiceNumber={claim?.invoiceNumber}
            visitId={claim?.visitNumber}
            benefitName={claim?.benefitName}
          />
        ) : display === DISPLAYS.documents ? (
          <Documents
            providerName={claim.providerName}
            providerId={claim.hospitalProviderId as number}
            visitNumber={claim.visitNumber}
          />
        ) : display === DISPLAYS.preAuthorizations ? (
          <PreAuthorizations />
        ) : (
          display === DISPLAYS.decision && <Decision claim={claim} />
        )}
      </BatchClaimContext.Provider>
    </MainWrapper>
  );
}
