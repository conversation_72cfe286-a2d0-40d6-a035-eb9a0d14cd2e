import { XMarkIcon } from "@heroicons/react/24/outline";
import { skipToken } from "@reduxjs/toolkit/query";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { useLocation, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import {
  useGetBeneficiaryBenefitsQuery,
  useGetBeneficiaryQuery,
  useStartVisitMutation,
} from "~lib/api";
import { StartVisitRequest } from "~lib/api/schema";
import {
  BeneficiaryStatus,
  OfflineVisitReason,
  VisitType,
  beneficiaryStatusLabels,
  beneficiaryTypeLabels,
  genderLabels,
  normalizedVisitTypeLabels,
  offlineVisitReasonLabels,
} from "~lib/api/types";
import {
  AsyncSelect,
  AsyncSelectCreatable,
  BenefitTreeViewRadio,
  Empty,
  FieldWrapper,
  Input,
  Select,
} from "~lib/components";
import ErrorMessage from "~lib/components/Error";
import { Form } from "~lib/components/Form";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { REIMBURSEMENT_PROVIDER_ID } from "~lib/constants";
import useSearchProviderOptions from "~lib/hooks/useSearchProviderOptions";
import { Option } from "~lib/types";
import {
  capitalize,
  clsx,
  formatDateISO,
  queryError,
  responseError,
  treeify,
  truncate,
} from "~lib/utils";
import { calculateAge } from "~lib/utils/dates";
import UserService from "../../services/UserService";
import { StartVisitState } from "../../utils/routes";

interface Inputs {
  provider: Option<string> | undefined;
  reason: OfflineVisitReason | string | undefined;
  benefit: string | undefined;
}

const EMPTY_LINE = "\u00A0";

export default function StartVisit() {
  const navigate = useNavigate();

  const location = useLocation();
  const locationState: StartVisitState = location.state;

  const { beneficiaryId, visitType } = locationState || {};

  const tokenParsed = UserService.getPayer()?.tokenParsed;

  const staffId = tokenParsed?.["sub"];
  const staffName = tokenParsed?.["preferred_username"];
  const payerId = tokenParsed?.["payerId"];
  const providerMiddleware = tokenParsed?.["providerMiddleware"];

  const methods = useForm<Inputs>({
    mode: "onBlur",
    defaultValues: {
      provider: undefined,
      reason: undefined,
      benefit: undefined,
    },
  });

  const { reset, resetField, watch } = methods;
  const form = watch();

  const {
    data: beneficiary,
    isLoading: isBeneficiaryLoading,
    error: beneficiaryError,
  } = useGetBeneficiaryQuery(beneficiaryId ? { beneficiaryId } : skipToken, {
    refetchOnFocus: true,
  });

  const isNewProvider = Boolean(
    (form.provider as (Option<string> & { __isNew__?: boolean }) | undefined)?.__isNew__,
  );

  const providerId =
    visitType == VisitType.REIMBURSEMENT && isNewProvider
      ? 0
      : form.provider
        ? window.parseInt(form.provider.value)
        : undefined;

  const providerName = form.provider?.label;

  const {
    data: beneficiaryBenefits,
    isLoading: isBeneficiaryBenefitsLoading,
    error: beneficiaryBenefitsError,
  } = useGetBeneficiaryBenefitsQuery(beneficiaryId ? { beneficiaryId } : skipToken);

  // TODO: Filter restricted benefits?

  const benefits = beneficiaryBenefits;
  const isBenefitsLoading = isBeneficiaryBenefitsLoading;
  const benefitsError = beneficiaryBenefitsError;

  const benefitId = form.benefit ? window.parseInt(form.benefit) : undefined;

  const selectedBeneficiaryProviderBenefit = beneficiaryBenefits?.find(
    (beneficiaryBenefit) => beneficiaryBenefit.benefitId == benefitId,
  );

  const { getProviderOptions } = useSearchProviderOptions();

  const [startVisit, { isLoading: isStartVisitLoading }] = useStartVisitMutation();

  function handleBack() {
    reset();
    navigate(-2);
  }

  const label = normalizedVisitTypeLabels.get(visitType) ?? "visit";

  const redirect =
    visitType == VisitType.OFF_LCT
      ? "/offline-visits"
      : visitType == VisitType.REIMBURSEMENT
        ? "/reimbursements"
        : "";

  async function handleSubmit(form: Inputs) {
    try {
      if (!beneficiary) throw new Error("Beneficiary not found");
      if (providerId == undefined) throw new Error("Provider not selected");
      if (!staffId) throw new Error("Staff ID not found");
      if (!payerId) throw new Error("Payer ID not found");
      if (!staffName) throw new Error("Staff name not found");
      if (!selectedBeneficiaryProviderBenefit) throw new Error("Benefit not selected");

      const beneficiaryBenefit = selectedBeneficiaryProviderBenefit;

      const payload: StartVisitRequest = {
        memberNumber: beneficiary.memberNumber,
        memberName: beneficiary.name,
        hospitalProviderId: visitType == VisitType.REIMBURSEMENT ? REIMBURSEMENT_PROVIDER_ID : providerId,
        staffId,
        staffName,
        benefitName: beneficiaryBenefit.benefitName,
        categoryId: beneficiary.category.id.toString(),
        aggregateId: beneficiaryBenefit.aggregateId.toString(),
        payerId,
        balanceAmount: beneficiaryBenefit.balance,
        benefitId: beneficiaryBenefit.benefitId,
        providerMiddleware: !providerMiddleware ? "NONE" : providerMiddleware,
        visitType,
        ...(visitType == VisitType.OFF_LCT && {
          offSystemReason: form.reason as OfflineVisitReason,
          offlctInvoiceDate: formatDateISO(new Date()),
        }),
        ...(visitType == VisitType.REIMBURSEMENT && {
          reimbursementReason: form.reason,
          reimbursementProvider: providerName,
          reimbursementInvoiceDate: formatDateISO(new Date()),
        }),
        /* ------------------------ Optional fields ----------------------- */
        beneficiaryType: beneficiary.beneficiaryType,
        policyNumber: beneficiary.category.policy.policyNumber,
        beneficiaryId: beneficiaryId,
      };

      const response = await startVisit(payload);
      const message = responseError(response);
      if (message) {
        throw new Error(message);
      }

      toast.success(`${capitalize(label)} started successfully`);

      if (redirect) {
        navigate(redirect);
      }
    } catch (error) {
      toast.error((error as Error)?.message || "Something went wrong");
    }
  }

  interface DisplayFieldProps {
    label: string;
    value: string;
    className?: string;
    isBold?: boolean;
  }

  const DisplayField = ({ label, value, className, isBold }: DisplayFieldProps) => (
    <div className={clsx("flex items-baseline gap-2", className)}>
      <p className="mb-1 text-gray-500">{label}: </p>
      <p className={clsx(isBold && "text-lg font-medium")} title={value}>
        {truncate(value, 30) || EMPTY_LINE}
      </p>
    </div>
  );

  const isDisabled = beneficiary?.status != BeneficiaryStatus.ACTIVE;

  useEffect(() => {
    // Reset benefit on provider change
    resetField("benefit");
  }, [providerId, resetField]);

  return (
    <main className="flex h-screen overflow-hidden bg-white">
      <div className="flex-1 overflow-y-auto overflow-x-hidden px-8 pt-4 text-gray-600">
        <div className="mb-4">
          <hgroup className="mb-4 flex justify-between gap-2">
            <h1 className="text-xl font-medium text-gray-700">Patient Details</h1>
            <button
              className="text-gray-500 enabled:hover:text-gray-600"
              onClick={() => {
                handleBack();
              }}
              title="Go Back"
            >
              {/* prettier-ignore */}
              <XMarkIcon strokeWidth={1.5} className="w-6 h-6" />
            </button>
          </hgroup>

          {!beneficiaryId ? (
            <Empty message="No member selected" />
          ) : isBeneficiaryLoading ? (
            <div className="flex items-center justify-center py-8">
              <LoadingIcon className="h-8 w-8 text-blue-400" />
            </div>
          ) : isDisabled ? (
            <ErrorMessage
              title="Member is not eligible"
              message={`Member has status ${
                beneficiary?.status
                  ? beneficiaryStatusLabels.get(beneficiary?.status) ?? "Unknown"
                  : "Unknown"
              }`}
            />
          ) : beneficiaryError ? (
            <ErrorMessage
              title="Error fetching beneficiary"
              message={queryError(beneficiaryError) || "Something wen't wrong"}
            />
          ) : !beneficiary ? (
            <Empty message="Member not found" />
          ) : (
            <div className="grid grid-cols-2 gap-1">
              <DisplayField label="Name" value={beneficiary?.name} />
              <DisplayField label="Member Number" value={beneficiary?.memberNumber} />
              <DisplayField label="Scheme" value={beneficiary?.category.policy.plan.name ?? ""} />
              <DisplayField
                label="Beneficiary Type"
                value={
                  beneficiary?.beneficiaryType
                    ? beneficiaryTypeLabels.get(beneficiary.beneficiaryType) ?? ""
                    : ""
                }
              />
              {beneficiary.dob && (
                <DisplayField
                  label="Age"
                  value={`${calculateAge(new Date(beneficiary.dob))} years`}
                />
              )}
              <DisplayField label="Gender" value={genderLabels.get(beneficiary.gender) ?? ""} />
            </div>
          )}
        </div>

        <div>
          <Form methods={methods} onSubmit={handleSubmit}>
            <div>
              <h1 className="mb-4 text-xl font-medium capitalize text-gray-700">{label} Details</h1>

              <fieldset className="grid grid-cols-2 gap-2" disabled={isDisabled}>
                <FieldWrapper name="provider" label="Provider" required>
                  {visitType == VisitType.REIMBURSEMENT ? (
                    <AsyncSelectCreatable
                      name="provider"
                      getOptions={getProviderOptions}
                      placeholder="Search or enter name..."
                      className="min-w-56 max-w-full flex-grow"
                    />
                  ) : (
                    <AsyncSelect
                      name="provider"
                      getOptions={getProviderOptions}
                      placeholder="Search provider..."
                      className="min-w-56 max-w-full flex-grow"
                    />
                  )}
                </FieldWrapper>

                {visitType == VisitType.OFF_LCT ? (
                  <FieldWrapper name="reason" label="Reason" required>
                    <Select
                      options={Object.values(OfflineVisitReason).map((value) => ({
                        label: offlineVisitReasonLabels[value] || "Unknown",
                        value,
                      }))}
                      placeholder="Reason..."
                    />
                  </FieldWrapper>
                ) : (
                  <FieldWrapper name="reason" label="Reason" required>
                    <Input type="text" />
                  </FieldWrapper>
                )}
              </fieldset>
            </div>

            {/* WARN: If benefits have not completed loading, or an error occurs, the benefit field is not registered and validation will not be performed */}
            {isBenefitsLoading ? (
              <div className="flex gap-2 items-center justify-center p-4">
                <LoadingIcon className="h-6 w-6" />
                <p className="text-gray-400">Loading benefits...</p>
              </div>
            ) : beneficiaryError ? (
              <ErrorMessage
                title="Error fetching member benefits"
                message={queryError(benefitsError) || "Something wen't wrong"}
              />
            ) : benefits?.length ? (
              <FieldWrapper label="Benefit" name="benefit" required>
                <BenefitTreeViewRadio beneficiaryBenefits={treeify(benefits)} />
              </FieldWrapper>
            ) : (
              <Empty message="No benefits found" />
            )}

            <div
              className={clsx(
                "mb-4 rounded border border-gray-300 px-4 py-2",
                selectedBeneficiaryProviderBenefit ? "text-gray-600" : "text-gray-400",
              )}
            >
              {selectedBeneficiaryProviderBenefit
                ? selectedBeneficiaryProviderBenefit.benefitName
                : "Benefit..."}
            </div>

            <div className="mb-2 flex justify-end gap-2">
              <button
                type="reset"
                className="rounded border border-gray-200 px-4 py-2 font-medium text-gray-500 enabled:hover:border-gray-300 disabled:opacity-80"
                onClick={() => {
                  reset();
                }}
                disabled={isDisabled || isStartVisitLoading}
              >
                Reset
              </button>

              <button
                type="submit"
                className={clsx(
                  "flex gap-2 rounded bg-blue-500 px-4 py-2 font-medium text-white enabled:hover:bg-blue-700 disabled:opacity-80",
                )}
                disabled={isDisabled || isStartVisitLoading}
              >
                {isStartVisitLoading && <LoadingIcon className="h-6 w-6 text-white" />}
                <span>Submit</span>
              </button>
            </div>
          </Form>
        </div>
      </div>
    </main>
  );
}
