import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import BatchedClaimsTableWrapper from "../../../components/claimsBatching/BatchedClaimsTableWrapper";
import BatchingCriteriaSelector, {
  BatchingCriteria,
} from "../../../components/claimsBatching/BatchingCriteriaSelector";
import ClaimsViewSelector, {
  ClaimsMenuView,
} from "../../../components/claimsBatching/ClaimsViewSelector";
import AdjudicationBatchingClaimsTable from "../../../components/claimsBatching/AdjudicationBatchingClaimsTable";
import Reading from "../../../components/illustrations/Reading";
import EmptyState from "../../../components/ui/EmptyState";
import {
  setActiveClaimsMenu,
  setSelectedBatchingOption,
} from "../../../features/claims/claimsBatchingSlice";
import { useAppDispatch, useAppSelector } from "../../../store/hooks";
import { isValidBatchingCriteria } from "../../../utils/utils";

const ClaimsBatchingAdjudication = () => {
  const dispatch = useAppDispatch();
  const activeClaimsMenu = useAppSelector((state) => state.claimsBatching.activeClaimsMenu);
  const selectedBatchingOption = useAppSelector(
    (state) => state.claimsBatching.selectedBatchingOption,
  );

  const handleBack = () => {
    if (activeClaimsMenu === ClaimsMenuView.IndividualClaims) {
      dispatch(setSelectedBatchingOption(""));
    } else {
      dispatch(setSelectedBatchingOption(""));
      dispatch(setActiveClaimsMenu(ClaimsMenuView.IndividualClaims));
    }
  };

  const batchingOptionSelected =
    selectedBatchingOption === BatchingCriteria.AGING ||
    selectedBatchingOption === BatchingCriteria.REGION ||
    selectedBatchingOption === BatchingCriteria.PROVIDER ||
    selectedBatchingOption === BatchingCriteria.BENEFIT ||
    selectedBatchingOption === BatchingCriteria.SCHEME;

  const shouldDisplayBackButton =
    isValidBatchingCriteria(selectedBatchingOption) &&
    activeClaimsMenu === ClaimsMenuView.IndividualClaims;

  return (
    <section className="m-8 h-full overflow-hidden rounded-lg border border-gray-200 p-8 shadow-xl">
      <div className="flex flex-col space-y-4">
        <div className="flex items-center space-x-4">
          {shouldDisplayBackButton && (
            <button className="rounded bg-[#E1E8F0] px-2 py-1" onClick={handleBack}>
              <ArrowLeftIcon className="h-5 w-5" />
            </button>
          )}
          <h2 className="text-lg font-medium capitalize text-[#111827]">
            {activeClaimsMenu === ClaimsMenuView.BatchedClaims ? "Batch List" : "Claims List"}
          </h2>
        </div>
        <ClaimsViewSelector />
      </div>
      <div
        className={`my-10 px-10 ${activeClaimsMenu === ClaimsMenuView.BatchedClaims ? "hidden" : "block"} ${
          batchingOptionSelected ? "hidden" : "block"
        }`}
      >
        <BatchingCriteriaSelector />
      </div>
      <div className="h-full overflow-x-auto">
        {activeClaimsMenu === ClaimsMenuView.IndividualClaims && batchingOptionSelected ? (
          <AdjudicationBatchingClaimsTable />
        ) : activeClaimsMenu === ClaimsMenuView.BatchedClaims ? (
          <BatchedClaimsTableWrapper />
        ) : (
          <EmptyState
            illustration={<Reading />}
            message={{
              title: "No Claims",
              description:
                "Please apply the filters to view all the individual claims before proceeding to the batching process....",
            }}
          />
        )}
      </div>
    </section>
  );
};

export default ClaimsBatchingAdjudication;
