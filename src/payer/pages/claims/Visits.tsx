import { Menu, Transition } from "@headlessui/react";
import {
  ArchiveBoxIcon,
  ArrowDownTrayIcon,
  ArrowPathIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";
import { skipToken } from "@reduxjs/toolkit/query";
import { Fragment, useState } from "react";
import { useForm } from "react-hook-form";
import { NavLink } from "react-router-dom";
import { toast } from "react-toastify";
import { api, useExportClaimsMutation, useSearchVisitsQuery } from "~lib/api";
import {
  Pagination as APIPagination,
  ClaimsReportType,
  ExportFileType,
  FilterVisits,
  Visit,
  VisitStatus,
  VisitType,
  normalizedVisitTypeLabels,
} from "~lib/api/types";
import {
  AsyncSelect,
  DateInput,
  Empty,
  FieldWrapper,
  Input,
  Pagination,
  RadioButtonGroup,
} from "~lib/components";
import ErrorMessage from "~lib/components/Error";
import { Form } from "~lib/components/Form";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { PAGE_SIZES, dropdownTransitions } from "~lib/constants";
import { useDebounce } from "~lib/hooks/useDebounce";
import useSearchProviderOptions from "~lib/hooks/useSearchProviderOptions";
import { Option } from "~lib/types";
import { clsx, formatDateTime, formatMoney, pluralize, truncate } from "~lib/utils";
import UserService from "../../services/UserService";
import { useAppDispatch } from "../../store/hooks";

interface Inputs {
  view: View;
  provider?: Option<string> | undefined;
  query?: string;
  startDate?: string;
  endDate?: string;
}

enum View {
  ACTIVE = "ACTIVE",
  HISTORY = "HISTORY",
}

const viewLabels: Record<View, string> = {
  ACTIVE: "Active",
  HISTORY: "History",
};

const viewIcons: Record<View, JSX.Element> = {
  ACTIVE: <ClockIcon className="h-5 w-5" />,
  HISTORY: <ArchiveBoxIcon className="h-5 w-5" />,
};

interface Props {
  visitType: VisitType;
}

// TODO: Replace with global constant
const MAX_INVOICES_PER_VISIT = 12;

export const Visits = ({ visitType }: Props) => {
  const [page, setPage] = useState(1);
  const [size, setSize] = useState<number>(PAGE_SIZES[0]);
  const [activeVisit, setActiveVisit] = useState<number | undefined>();

  const dispatch = useAppDispatch();

  const methods = useForm<Inputs>({
    mode: "onBlur",
    defaultValues: {
      view: View.ACTIVE,
      provider: undefined,
      query: "",
      startDate: "",
      endDate: "",
    },
  });

  const { watch } = methods;

  const form = watch();
  const queryDebounced = useDebounce(form.query, 200);

  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];

  const params = {
    statuses: [form.view == View.ACTIVE ? VisitStatus.ACTIVE : VisitStatus.CLOSED],
    visitTypes: [visitType],
    payerIds: [Number(payerId)],
    query: queryDebounced,
    startDate: form.startDate,
    endDate: form.endDate,
    providerIds: form.provider ? [Number(form.provider.value)] : undefined,
  };

  const exportParams: FilterVisits = {
    ...params,
    ...(queryDebounced ? { query: queryDebounced } : undefined),
  };

  const {
    data: visitsResponse,
    error: visitsError,
    isLoading: isVisitsLoading,
    isFetching: isVisitsFetching,
  } = useSearchVisitsQuery(!payerId ? skipToken : { page, size, ...params }, {
    refetchOnFocus: true,
    /**
     * payerId should always be defined because of initial auth check.
     * In case it isn't, we shouldn't fetch visits for other payers.
     */
    skip: !payerId,
  });

  const [exportClaims, { isLoading: isExportLoading }] = useExportClaimsMutation();

  const visits = visitsResponse?.data.content;
  const isVisitsFetchingOnly = isVisitsFetching && !isVisitsLoading;

  const { getProviderOptions } = useSearchProviderOptions();

  const NA = <span className="uppercase text-gray-400">N/A</span>;

  const isExportFiltersValid = Boolean(form.startDate && form.endDate);
  const isExportDisabled = isExportLoading || !isExportFiltersValid || form.view != View.HISTORY;

  const refresh = () => dispatch(api.util.invalidateTags(["Visits"]));

  const label = normalizedVisitTypeLabels.get(visitType) ?? "visit";

  const memberSearchState = { visitType };

  const visitTypeActionLabel = (visitType: VisitType) => {
    if (visitType == VisitType.REIMBURSEMENT) {
      return <>Reimburse</>;
    }

    return <>Bill&nbsp;Visit</>;
  };

  async function handleExport(fileType: ExportFileType, visitId?: number) {
    setActiveVisit(visitId);

    const { payerIds } = exportParams;

    type ExportClaimsReportParams = {
      fileType: ExportFileType;
      reportType: ClaimsReportType;
    };

    const exportVisitParams: FilterVisits & APIPagination & ExportClaimsReportParams = {
      visitNumbers: visitId ? [visitId] : [],
      payerIds,
      page: 1,
      size: MAX_INVOICES_PER_VISIT,
      reportType: ClaimsReportType.SCHEME_UTILIZATION_CLINICAL,
      fileType,
    };

    const exportVisitsParams = {
      ...exportParams,
      fileType,
      page,
      size,
      reportType: ClaimsReportType.SCHEME_UTILIZATION_CLINICAL,
    };

    try {
      await exportClaims(visitId ? exportVisitParams : exportVisitsParams).unwrap();
    } catch (error) {
      toast.error("Something went wrong. Please try again.");

      if (import.meta.env.DEV) {
        console.error(error);
      }
    }
  }

  return (
    <div className="flex h-full flex-grow overflow-hidden bg-gray-50">
      <div className="flex flex-1 flex-col overflow-y-auto overflow-x-hidden">
        <main>
          <div className="w-full pb-8 text-gray-600">
            <div className="max-w-full rounded-md bg-white shadow-md">
              <div className="z-10 bg-gray-50 px-8 py-4">
                <Form
                  className="pt-2 text-sm"
                  methods={methods}
                  onSubmit={(_data, e) => {
                    e?.preventDefault();
                  }}
                >
                  <div className="flex items-center justify-between pb-2">
                    <FieldWrapper
                      label=""
                      name="view"
                      labelClassName="font-semibold text-gray-500"
                      hideErrorMessage
                    >
                      <RadioButtonGroup
                        options={Object.values(View).map((view) => ({
                          label:
                            (
                              <span className="flex items-center gap-2">
                                {viewIcons[view]} {viewLabels[view]}
                              </span>
                            ) ?? "Unknown",
                          value: view,
                        }))}
                      />
                    </FieldWrapper>

                    <hgroup className="flex items-center gap-2">
                      <h2 className="text-lg font-medium capitalize">{pluralize(2, label)}</h2>
                      {isVisitsFetchingOnly ? (
                        <LoadingIcon className="h-5 w-5 text-gray-400" />
                      ) : (
                        <button
                          title="Refresh"
                          className="flex gap-2 rounded-full bg-transparent font-medium text-gray-400 enabled:hover:text-gray-500 disabled:cursor-not-allowed disabled:opacity-60"
                          type="button"
                          onClick={() => {
                            refresh();
                          }}
                        >
                          <ArrowPathIcon className="h-5 w-5" />
                        </button>
                      )}
                    </hgroup>

                    <div className="flex gap-2">
                      <div>
                        {/* TODO: Type parameters */}
                        <NavLink
                          className="flex gap-2 rounded bg-blue-500 px-4 py-2 font-medium capitalize text-white hover:bg-blue-700"
                          to="/members/search?redirect=/visits/new"
                          state={memberSearchState}
                        >
                          Add {label}
                        </NavLink>
                      </div>
                    </div>
                  </div>

                  <details className="[&>summary_svg.chevron]:open:rotate-90" open>
                    <summary className="mb-2 flex justify-between gap-4 font-medium">
                      <span className="flex gap-2 py-2">
                        <ChevronRightIcon className="chevron h-5 w-5 shrink-0 rotate-0 transform transition-all duration-300" />
                        Filter {pluralize(0, label)}
                      </span>

                      <Menu as="div" className="relative inline-block text-left text-sm">
                        <div>
                          <Menu.Button
                            className={clsx(
                              "flex gap-2 rounded border border-blue-500 px-4 py-2 font-medium text-blue-500",
                              isExportDisabled
                                ? "cursor-not-allowed opacity-60"
                                : "hover:border-blue-600 hover:text-blue-600",
                            )}
                            disabled={isExportDisabled}
                          >
                            {isExportLoading && <LoadingIcon className="h-5 w-5" />}
                            <ArrowDownTrayIcon strokeWidth={1.5} className="h-5 w-5" />
                            <span>Export</span>
                            <ChevronDownIcon strokeWidth={1.5} className="-mr-1 h-5 w-5" />
                          </Menu.Button>
                        </div>

                        <Transition as={Fragment} {...dropdownTransitions}>
                          <Menu.Items className="absolute right-0 mt-2 w-36 origin-top-right rounded-md border border-gray-200 bg-white focus:outline-none">
                            {Object.values(ExportFileType).map((fileType) => (
                              <Menu.Item key={fileType}>
                                {({ active }) => (
                                  <button
                                    className={`${
                                      active ? "bg-gray-50" : ""
                                    } group flex w-full items-center rounded-md px-6 py-4 text-sm`}
                                    onClick={() => {
                                      handleExport(fileType);
                                    }}
                                    disabled={isExportLoading}
                                  >
                                    {fileType}
                                  </button>
                                )}
                              </Menu.Item>
                            ))}
                          </Menu.Items>
                        </Transition>
                      </Menu>
                    </summary>

                    <div className="flex flex-wrap justify-start gap-2">
                      <FieldWrapper
                        name="provider"
                        label="Provider"
                        className="max-w-xs flex-shrink flex-grow"
                        labelClassName="font-semibold text-gray-500"
                      >
                        <AsyncSelect
                          name="provider"
                          getOptions={getProviderOptions}
                          placeholder="Search provider..."
                          className="min-w-56"
                        />
                      </FieldWrapper>

                      <FieldWrapper
                        name="query"
                        label="Search"
                        className="flex-grow"
                        labelClassName="font-semibold text-gray-500"
                      >
                        <Input
                          type="text"
                          placeholder="Member number or invoice number..."
                          className="max-w-full text-sm"
                        />
                      </FieldWrapper>

                      {/* TODO: Fix date font size */}
                      <FieldWrapper
                        name="startDate"
                        label="Start Date"
                        labelClassName="font-semibold text-gray-500"
                      >
                        <DateInput
                          placeholder={new Date().toISOString().split("T")[0]}
                          maxDate="today"
                          className="w-36 text-sm"
                        />
                      </FieldWrapper>

                      <FieldWrapper
                        name="endDate"
                        label="End Date"
                        labelClassName="font-semibold text-gray-500"
                      >
                        <DateInput
                          placeholder={new Date().toISOString().split("T")[0]}
                          maxDate="today"
                          className="w-36 text-sm"
                        />
                      </FieldWrapper>
                    </div>
                  </details>
                </Form>
              </div>

              <div className="mb-4">
                {isVisitsLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <LoadingIcon className="h-6 w-6 text-blue-400" />
                  </div>
                ) : visitsError ? (
                  <ErrorMessage title="Error fetching visits" message="Refresh to retry" />
                ) : !visits?.length ? (
                  <Empty message={`No ${pluralize(0, label)} found`} />
                ) : (
                  <div className="overflow-x-auto bg-white text-gray-600">
                    <table className="w-full">
                      <thead className="text-left">
                        <tr className="bg-gray-100">
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Visit Number</th>
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Member Number</th>
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Member Name</th>
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Provider</th>
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Scheme</th>
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Benefit</th>
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Added By</th>
                          {form.view == View.HISTORY && (
                            <th className="px-2 py-4 first:pl-8 last:pr-8">Amount</th>
                          )}
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Date Created</th>
                          <th className="px-2 py-4 first:pl-8 last:pr-8">Action</th>
                        </tr>
                      </thead>

                      <tbody>
                        {visits.map((visit: Visit) => (
                          <tr key={visit.id}>
                            <td className="px-2 py-4 first:pl-8 last:pr-8">{visit.id}</td>
                            <td className="px-2 py-4 first:pl-8 last:pr-8">{visit.memberNumber}</td>
                            <td className="px-2 py-4 first:pl-8 last:pr-8">{visit.memberName}</td>
                            <td
                              className="px-2 py-4 uppercase first:pl-8 last:pr-8"
                              title={visit.providerName || visit.reimbursementProvider}
                            >
                              {truncate(visit.providerName || visit.reimbursementProvider, 30)}
                            </td>
                            <td
                              className="px-2 py-4 first:pl-8 last:pr-8"
                              title={visit.schemeName || visit?.scheme?.name}
                            >
                              {truncate(visit.schemeName || visit?.scheme?.name, 30) || NA}
                            </td>
                            <td
                              className="px-2 py-4 first:pl-8 last:pr-8"
                              title={visit.benefitName}
                            >
                              {truncate(visit.benefitName, 30) || NA}
                            </td>
                            <td className="px-2 py-4 first:pl-8 last:pr-8" title={visit.staffName}>
                              {truncate(visit.staffName, 12) || NA}
                            </td>
                            {form.view == View.HISTORY && (
                              <>
                                <td className="px-2 py-4 first:pl-8 last:pr-8">
                                  {formatMoney(visit.totalInvoiceAmount) || NA}
                                </td>
                              </>
                            )}
                            <td className="px-2 py-4 first:pl-8 last:pr-8">
                              {visit.createdAt
                                ? formatDateTime(new Date(visit.createdAt + "Z"))
                                : ""}
                            </td>
                            <td className="px-2 py-4 first:pl-8 last:pr-8">
                              {form.view == View.ACTIVE ? (
                                <NavLink
                                  className="flex items-center gap-2 rounded bg-blue-500 px-4 py-2 text-sm font-medium capitalize text-white hover:bg-blue-700"
                                  to={`/visits/${visit.id}/bill`}
                                >
                                  {visitTypeActionLabel(visit.visitType)}
                                </NavLink>
                              ) : form.view == View.HISTORY ? (
                                <div>
                                  <button
                                    className="flex gap-2 rounded px-4 py-2 font-medium text-gray-500 enabled:hover:text-gray-700 disabled:cursor-not-allowed disabled:opacity-80"
                                    title="Export"
                                    onClick={() => {
                                      handleExport(ExportFileType.PDF, visit.id);
                                    }}
                                  >
                                    {isExportLoading && activeVisit == visit.id ? (
                                      <LoadingIcon className="h-5 w-5" />
                                    ) : (
                                      <ArrowDownTrayIcon strokeWidth={1.5} className="h-5 w-5" />
                                    )}
                                  </button>
                                </div>
                              ) : (
                                <></>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>

              {visitsResponse && (
                <div className="pb-4">
                  <Pagination
                    totalElements={visitsResponse?.data.totalElements}
                    totalPages={visitsResponse?.data.totalPages}
                    setPage={setPage}
                    setSize={setSize}
                    page={page}
                    size={size}
                    isLoading={isVisitsFetchingOnly}
                  />
                </div>
              )}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Visits;
