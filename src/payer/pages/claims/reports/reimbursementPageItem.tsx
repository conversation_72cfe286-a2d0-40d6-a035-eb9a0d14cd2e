import React, { useEffect, FC, useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router";
import { api_url_visit, formatValue } from "../../../lib/Utils";
import { setSelectedHistoryItem } from "../../../store/dashboard/actions";

interface Props {
  id?: any;
  visitNumber?: any;
  staffName?: any;
  memberNumber?: any;
  memberName?: any;
  status?: any;
  createdAt?: any;
  invoiceNumber?: any;
  benefitName?: any;
  policyNumber?: any;
  payerName?: any;
  beneficiaryType?: any;
  balance?: any;
  visit?: any;
  setTransactionPanelOpen?: any;
  setOpenTab?: any;
  providerId?: any;
  invoiceAmount?: any;
  hospitalProviderId?: any;
  invoiceDate?: any;
  providerName?: any;
}
export const ReimbursementPageItem: FC<Props> = (props) => {
  const [payerEditModalOpen, setEditPayerModalOpen] = useState(false);
  const navigate = useNavigate();
  const [open, setOpen] = useState(false);
  const dispatch = useDispatch();

  const [isLineItemModalOpen, setIsLineItemModalOpen] = useState(false);
  const [isDiagnosisModalOpen, setIsDiagnosisModalOpen] = useState(false);
  const [transactionPanelOpen, setTransactionPanelOpen] = useState(true);

  return (
    <tbody
      className="divide-y-0 even:bg-light-blue-50 
    hover:bg-light-blue-100 hover:text-sky-700  delay-150 "
    >
      <tr className="cursor-pointer" key={props.visitNumber}>
        <td className="whitespace-nowrap">
          <div className="flex items-center text-slate-800">
            {" "}
            <div
              className="font-normal text-slate-800 text-center items-center"
              title={props.visitNumber}
            >
              {props.visitNumber}
            </div>
          </div>
        </td>
        <td className="whitespace-nowrap">
          <div className="flex items-center text-slate-800">
            {" "}
            <div className="font-normal text-slate-800 text-center items-center">
              {props.invoiceDate}
            </div>
          </div>
        </td>
        <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
          <div
            className=" font-normal rounded-full text-left uppercase px-1 py-0.5"
            title={props.memberNumber}
          >
            {props.memberNumber}
          </div>
        </td>
        <td
          className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap"
          title={props.memberName}
        >
          <div className=" font-normal rounded-full sm:w-36 text-left uppercase px-0 py-0.5 truncate  ...">
            {props.memberName}
          </div>
        </td>

        <td
          className="px-0 first:pl-5 last:pr-2 py-3 whitespace-nowrap"
          title={props.providerName}
        >
          <div className=" font-normal rounded-full text-left uppercase px-0 py-0.5 sm:w-40 text-ellipsis truncate  ...">
            {props.providerName ? props.providerName : "None"}
          </div>
        </td>

        <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
          <div
            className=" font-normal rounded-full text-left uppercase px-1 py-0.5 sm:w-40 text-ellipsis truncate  ..."
            title={props.invoiceNumber}
          >
            {props.invoiceNumber}
          </div>
        </td>
        <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
          <div
            className=" font-normal bg-amber-100 text-base text-amber-600 rounded-full text-left uppercase px-0 py-0.5 sm:w-24 text-ellipsis truncate  ..."
            title={props.invoiceAmount}
          >
            {formatValue(props.invoiceAmount)}
          </div>
        </td>
        <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
          <a
            href={`${api_url_visit}statement/singleReimbursement?visitNumber=${props.visitNumber}`}
            target="_blank"
            rel="noopener noreferrer"
            className="bg-blue-800 p-2 rounded-md ml-2 flex "
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="#FFFFFF"
              className="w-6 h-6 "
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0110.56 0m-10.56 0L6.34 18m10.94-4.171c.************.72.096m-.72-.096L17.66 18m0 0l.229 2.523a1.125 1.125 0 01-1.12 1.227H7.231c-.662 0-1.18-.568-1.12-1.227L6.34 18m11.318 0h1.091A2.25 2.25 0 0021 15.75V9.456c0-1.081-.768-2.015-1.837-2.175a48.055 48.055 0 00-1.913-.247M6.34 18H5.25A2.25 2.25 0 013 15.75V9.456c0-1.081.768-2.015 1.837-2.175a48.041 48.041 0 011.913-.247m10.5 0a48.536 48.536 0 00-10.5 0m10.5 0V3.375c0-.621-.504-1.125-1.125-1.125h-8.25c-.621 0-1.125.504-1.125 1.125v3.659M18 10.5h.008v.008H18V10.5zm-3 0h.008v.008H15V10.5z"
              />
            </svg>
            <p className="text-white">Print </p>
          </a>
        </td>
      </tr>

      <tr></tr>
    </tbody>
  );
};
export default ReimbursementPageItem;
