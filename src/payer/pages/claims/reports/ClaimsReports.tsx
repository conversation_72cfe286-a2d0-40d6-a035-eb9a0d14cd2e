import { <PERSON>u, Popover, Transition } from "@headlessui/react";
import { Fragment, ReactNode, useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import {
  useExportClaimsMutation,
  useGetPayerPoliciesQuery,
  useGetPoliciesCategoriesQuery,
  useLazyGetClaimsQuery,
  useSendKengenClaimsMutation,
  useGetPoliciesCategoriesByPlanIdQuery,
  useExportInvoicesMutation,
  useGetPoliciesByPlanIdQuery,
  useLazyGetInvoicesQuery,
} from "~lib/api";
import {
  Claim,
  ClaimStatus,
  ClaimsReportType,
  ExportFileType,
  FilterVisits,
  InvoiceStatus,
  PayerStatus,
  PolicyStatus,
  SearchProviderFilter,
  ServiceGroup,
  VisitType,
  claimReportsStatuses,
  claimStatusLabels,
  claimsReportsTypes,
  invoiceStatusLabels,
  invoiceStatusLabelsArr,
  payerStatusLabels,
  serviceGroupLabels,
  visitTypeLabels,
  dispatchStatusLabels,
} from "~lib/api/types";
import doctorSvg from "~lib/assets/doctor.svg";
import AuthenticationError from "~lib/components/AuthenticationError";
import Badge from "~lib/components/Badge";
import Checkbox from "~lib/components/Checkbox";
import CheckboxGroup from "~lib/components/CheckboxGroup";
import DateRangePicker from "~lib/components/DateRangePicker";
import Empty from "~lib/components/Empty";
import { Form } from "~lib/components/Form";
import Pagination from "~lib/components/Pagination";
import RadioGroup from "~lib/components/RadioGroup";
import Select from "~lib/components/Select";
import SelectProvider from "~lib/components/SelectProvider";
import SelectProviders from "~lib/components/SelectProviders";
import {
  dropdownTransitions,
  getClaimStatusBadgeColors,
  getDispatchStatusBadgeColors,
  getInvoiceStatusBadgeColors,
  getPayerStatusBadgeColors,
  sortOrder,
} from "~lib/constants";
import usePrevious from "~lib/hooks/usePrevious";
import { Entries, Option, StringOption } from "~lib/types";
import { arrayJoin, clsx, formatDateISO, formatDateStringGB, formatMoney } from "~lib/utils";
import { getLastXDays, getToday } from "~lib/utils/dates";
import UserService from "../../../services/UserService";
import ExportMenu from "./ExportMenu";

const reportTypeLabels = new Map<ClaimsReportType, string>([
  [ClaimsReportType.SCHEME_UTILIZATION, "Scheme Utilization"],
  [ClaimsReportType.PROVIDER_UTILIZATION, "Provider Utilization"],
  [ClaimsReportType.DELIVERY_STATUS, "Delivery Status"],
]);

enum FilterType {
  CLAIM_STATUS = "CLAIM_STATUS",
  PAYER_STATUS = "PAYER_STATUS",
  SCHEMES = "SCHEMES",
  SCHEME = "SCHEME",
  VISIT_TYPES = "VISIT_TYPES",
  MAIN_FACILITY = "MAIN_FACILITY",
  SERVICE_GROUPS = "SERVICE_GROUPS",
  BRANCHES = "BRANCHES",
  CATEGORIES = "CATEGORIES",
  COVER_PERIODS = "COVER_PERIODS",
}

type Filters = Omit<FilterVisits, "payerId">;

type Column = {
  header: ReactNode | string;
  render: ReactNode;
  defaultValue?: unknown;
  hidden?: boolean;
};

type Filter = {
  render: ReactNode;
  mandatory?: boolean;
  input: keyof Inputs;
  filter: keyof Filters;
};

interface Inputs {
  policyId: string;
  policyIds?: string[];
  categoryIds?: string[];
  serviceGroups?: ServiceGroup[];
  statuses?: ClaimStatus[];
  visitTypes?: VisitType[];
  payerStatus?: PayerStatus;
  mainFacility?: Option<string>;
  branches?: Option<string>[];
}

const filterLabels = new Map<keyof Inputs, string>([
  ["policyId", "Scheme"],
  ["policyIds", "Schemes"],
  ["categoryIds", "Category"],
  ["branches", "Branch"],
  ["mainFacility", "Facility"],
  ["serviceGroups", "Benefit"],
  ["statuses", "Status"],
  ["visitTypes", "Visit Type"],
  ["payerStatus", "Delivery Status"],
]);

/**
 * Max number of invoices to show per claim when exporting
 */
const INVOICES_PER_VISIT = 10;
const DEFAULT_SIZE = 10;
const DEFAULT_PAGE = 1;

const reportVisitTypes = new Map<ClaimsReportType, VisitType[]>([
  [
    ClaimsReportType.SCHEME_UTILIZATION,
    [VisitType.OFF_LCT, VisitType.ONLINE, VisitType.REIMBURSEMENT],
  ],
  [ClaimsReportType.PROVIDER_UTILIZATION, [VisitType.OFF_LCT, VisitType.ONLINE]],
  [
    ClaimsReportType.DELIVERY_STATUS,
    [VisitType.OFF_LCT, VisitType.ONLINE, VisitType.REIMBURSEMENT],
  ],
]);

const ClaimsReports = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [reportType, setReportType] = useState(ClaimsReportType.SCHEME_UTILIZATION);
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(10);
  const [dateRange, setDateRange] = useState<[Date?, Date?]>([undefined, undefined]);
  const [expandedClaim, setExpandedClaim] = useState<number>(undefined);
  const [filters, setFilters] = useState<Filters | undefined>();
  const [selectedCoverPeriod, setSelectedCoverPeriod] = useState(null);

  const payerId = UserService.getPayer().tokenParsed.payerId;
  const userName = UserService?.getUsername();

  const filtersMethods = useForm<Inputs>({
    defaultValues: {
      policyId: undefined,
      policyIds: [],
      categoryIds: [],
      branches: [],
      serviceGroups: [],
      statuses: [],
      visitTypes: [],
      payerStatus: undefined,
      mainFacility: undefined,
    },
  });

  const {
    watch: watchFilters,
    reset: resetFilters,
    resetField: resetFiltersField,
    handleSubmit: handleFiltersSubmit,
  } = filtersMethods;

  const filtersForm = watchFilters();

  const selectedClaimsMethods = useForm<{ visitNumbers: string[] }>({
    shouldUnregister: false,
    defaultValues: {
      visitNumbers: [],
    },
  });

  const {
    watch: watchSelectedClaims,
    reset: resetSelectedClaims,
    resetField: resetSelectedClaimsField,
    setValue: setSelectedClaimsValue,
  } = selectedClaimsMethods;

  const selectedClaimsForm = watchSelectedClaims();
  const { visitNumbers } = selectedClaimsForm;

  const [sendVisits, { isLoading: isSendVisitsSentLoading }] = useSendKengenClaimsMutation();

  const { policyIds, mainFacility, policyId } = filtersForm;

  const previousPolicyIds = usePrevious(policyIds);
  const previousPolicyId = usePrevious(policyId);

  const activeFilterEntries = Object.entries(filtersForm).filter(([, value]) =>
    Array.isArray(value) ? value.length > 0 : Boolean(value),
  ) as Entries<Inputs>;

  const arrayOrUndefined = <T,>(value: T[]): T[] | undefined => (value?.length ? value : undefined);

  const numberArrayOrUndefined = (value: string[]): number[] | undefined =>
    value?.length ? value.map(Number) : undefined;

  const optionNumberArrayOrUndefined = (value: StringOption[]): number[] | undefined =>
    value?.length ? value.map((option) => option.value).map(Number) : undefined;

  const mainFacilityId = Number(mainFacility?.value) || 0;

  const formFilterActive = (key: keyof Inputs) => {
    const value: Inputs[typeof key] = filtersForm[key];
    return Array.isArray(value) ? value.length > 0 : Boolean(value);
  };

  const filterActive = (key: keyof Filters) => {
    const value: Filters[typeof key] = filters?.[key];
    return Array.isArray(value) ? value.length > 0 : Boolean(value);
  };

  const {
    data: policies,
    isLoading: isPoliciesLoading,
    error: policiesError,
  } = useGetPayerPoliciesQuery({ payerId });

  const coverPolicyId = selectedCoverPeriod ? Number(selectedCoverPeriod?.value) : null;

  const {
    data: categories,
    isLoading: isCategoriesLoading,
    error: categoriesError,
  } = useGetPoliciesCategoriesQuery({ policyIds: [coverPolicyId] }, { skip: !coverPolicyId });

  const {
    data: policiesCoverPeriods,
    error: coverPeriodsError,
    isLoading: isCoverPeriodsLoading,
  } = useGetPoliciesByPlanIdQuery(policyId);
  const coverPeriodOptions =
    policiesCoverPeriods?.data?.map((cover) => ({
      label: `${cover.startDate}/ ${cover.endDate}`,
      value: cover.id,
      startDate: cover.startDate,
      endDate: cover.endDate,
    })) || [];

  const handleChangeCoverPeriod = (event) => {
    const selectedId = event.target.value; // This is a string
    if (!selectedId) {
      setSelectedCoverPeriod(null);
      resetFiltersField("categoryIds", { defaultValue: [] });
      setDateRange([null, null]);
      return;
    }
    const selectedPeriod = coverPeriodOptions.find(
      (period) => period.value.toString() === selectedId, // Convert id to string for comparison
    );

    setSelectedCoverPeriod(selectedPeriod);
    if (selectedPeriod) {
      setDateRange([new Date(selectedPeriod.startDate), new Date(selectedPeriod.endDate)]);
    } else {
      setDateRange([null, null]);
    }
  };

  const [
    getClaims,
    {
      data: claimsResponse,
      isLoading: isClaimsLoading,
      isFetching: isClaimsFetching,
      error: claimsError,
      isUninitialized: isClaimsUninitialized,
    },
  ] = useLazyGetInvoicesQuery();

  const [exportClaims, { isLoading: isExportClaimsLoading }] = useExportInvoicesMutation();

  const claims = claimsResponse?.data?.content || [];
  const isClaimsFetchingOnly = isClaimsFetching && !isClaimsLoading;

  /**
   * Active policy on scheme utilization.
   * Used to set the default date range.
   */
  const activePolicy =
    reportType === ClaimsReportType.SCHEME_UTILIZATION && policyId
      ? policies?.find((p) => p.id === Number(policyId))
      : undefined;

  function handleChangeReportType(e: React.ChangeEvent<HTMLSelectElement>) {
    setReportType(e.target.value as ClaimsReportType);
  }

  /**
   *
   * @param id Same as claim id
   */
  function handleExpandClaim(id: number) {
    setExpandedClaim(id === expandedClaim ? undefined : id);
  }

  async function handleExport(fileType: ExportFileType) {
    try {
      await exportClaims({
        ...filters,
        fileType,
        page,
        size,
        payerId,
        reportType:
          reportType == ClaimsReportType.SCHEME_UTILIZATION
            ? ClaimsReportType.SCHEME_UTILIZATION_FINANCIAL
            : reportType,
      }).unwrap();
    } catch (error) {
      toast.error("Something went wrong. Please try again.");

      if (import.meta.env.DEV) {
        console.error(error);
      }
    }
  }

  async function handleExportVisit(invoiceId: number, fileType: ExportFileType) {
    try {
      await exportClaims({
        payerId: payerId as number,
        //invoiceNumbers: [String(invoiceNumber)],
        invoiceIds: [Number(invoiceId)],
        fileType,
        page: 1,
        size: INVOICES_PER_VISIT,
        reportType:
          reportType == ClaimsReportType.SCHEME_UTILIZATION
            ? ClaimsReportType.SCHEME_UTILIZATION_FINANCIAL
            : reportType,
      }).unwrap();
    } catch (error) {
      toast.error("Something went wrong. Please try again.");

      if (import.meta.env.DEV) {
        console.error(error);
      }
    }
  }

  async function handleSendVisits() {
    try {
      if (visitNumbers.length === 0) {
        toast.error("Please select at least one visit.");
        return;
      }

      await sendVisits({
        invoiceIds: visitNumbers.map(Number),
        actionedBy: userName,
      }).unwrap();

      toast.success("Claim sent successfully.");
      resetSelectedClaims();
    } catch (error) {
      toast.error("Something went wrong. Please try again.");

      if (import.meta.env.DEV) {
        console.error(error);
      }
    }
  }

  const handleCheckAllChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      // TODO: Deduplicate selected claims
      setSelectedClaimsValue("visitNumbers", [
        ...visitNumbers,
        ...claims.map((claim) => claim.id.toString()),
      ]);
    } else {
      setSelectedClaimsValue(
        "visitNumbers",
        visitNumbers.filter(
          (visitNumber) => !claims.some((claim) => claim.id.toString() === visitNumber),
        ),
      );
    }
  };

  /**
   * Expects the following values in scope
   * page, size, payerId, mainFacilityId, dateRange, reportType
   */
  function handleSubmitFilters({
    categoryIds,
    branches,
    mainFacility,
    serviceGroups,
    statuses,
    visitTypes,
    payerStatus,
    policyIds,
    policyId,
  }: Inputs) {
    const filters: Filters = {
      planIds: policyId ? [Number(policyId)] : numberArrayOrUndefined(policyIds),
      categoryIds: numberArrayOrUndefined(categoryIds),
      ...(mainFacility && { mainProviderIds: [Number(mainFacility?.value)] }),
      ...(mainFacility && {
        providerIds: optionNumberArrayOrUndefined(branches),
      }),
      serviceGroups: arrayOrUndefined(serviceGroups),
      payerStatuses: payerStatus ? [payerStatus] : undefined,
      visitStatuses: arrayOrUndefined(statuses),
      invoiceStatuses: [InvoiceStatus.BALANCE_DEDUCTED],
      sortOrder: sortOrder.DESC,
      sortColumn: "v.invoiceDate",
      visitTypes: arrayOrUndefined(visitTypes),
      startDate: formatDateISO(dateRange[0]) || undefined,
      endDate: formatDateISO(dateRange[1]) || undefined,
    };

    getClaims({
      ...filters,
      payerId,
      page,
      size,
    });

    setFilters(filters);
  }

  const getFilterValueLabel = (key: keyof Inputs) => {
    switch (key) {
      case "policyId":
        return policies?.find((p) => p.id === Number(policyId))?.plan.name || "1 selected";
      case "policyIds":
      case "categoryIds":
      case "branches":
        return `${filtersForm[key]?.length} selected`;
      case "mainFacility":
        return filtersForm[key]?.label;
      case "serviceGroups":
        return arrayJoin(filtersForm[key], (serviceGroup) => serviceGroupLabels.get(serviceGroup));
      case "statuses":
        return arrayJoin(filtersForm[key], (status) => claimStatusLabels.get(status));
      case "visitTypes":
        return arrayJoin(filtersForm[key], (visitType) => visitTypeLabels.get(visitType));
      case "payerStatus":
        return filtersForm[key] ? payerStatusLabels.get(filtersForm[key] as PayerStatus) : "All";
      default:
        return (filtersForm[key] as Inputs[keyof Inputs])?.toString();
    }
  };

  useEffect(() => {
    /**
     * Reset all filters. This effectively resets filters that are not valid for
     * the current report type.
     */
    resetFilters();
    setFilters(undefined);
    setExpandedClaim(undefined); // Collapse all claims
    resetSelectedClaims(); // Unselect all visits
    setPage(DEFAULT_PAGE);
    setSize(DEFAULT_SIZE);

    switch (reportType) {
      /**
       * Set default date range for each report type
       */
      case ClaimsReportType.SCHEME_UTILIZATION:
        // Set to undefined until user selects a scheme
        setDateRange([undefined, undefined]);
        break;
      case ClaimsReportType.PROVIDER_UTILIZATION:
        // Today
        setDateRange(getToday());
        break;
      case ClaimsReportType.DELIVERY_STATUS:
        // Last 30 days
        setDateRange(getLastXDays(30));
        break;
      default:
        setDateRange([undefined, undefined]);
    }
  }, [reportType, resetSelectedClaims, resetSelectedClaimsField, resetFilters]);

  useEffect(() => {
    // Reset branches when main facility changes
    resetFiltersField("branches");
  }, [mainFacilityId, resetFiltersField]);

  useEffect(() => {
    // Reset categories when schemes change

    const policyIdsChanged = policyIds?.some((policyId) => !previousPolicyIds?.includes(policyId));

    if (policyId !== previousPolicyId || policyIdsChanged) {
      /**
       * NOTE: Resettting on policyIds changed is redundant because we don't
       * filter categories by multiple policyIds*. Ideally, we should only
       * remove the categories that don't belong to the currently selected
       * policyIds.
       */
      // resetFiltersField("categoryIds",{defaultValue:[]});
      setSelectedCoverPeriod(null);
      setDateRange([null, null]);
    }
  }, [policyIds, policyId, previousPolicyId, previousPolicyIds, resetFiltersField]);

  useEffect(() => {
    if (selectedCoverPeriod === null || !coverPolicyId)
      resetFiltersField("categoryIds", { defaultValue: [] });
  }, [selectedCoverPeriod, resetFiltersField, coverPolicyId]);

  // useEffect(() => {
  //   if (activePolicy) {
  //     /**
  //      * Set the date range to the active policy's start and end dates
  //      */
  //     setDateRange([new Date(activePolicy.startDate), new Date(activePolicy.endDate)]);
  //   }
  // }, [activePolicy]);

  const isAllSelected = claims?.every((claim) => visitNumbers.includes(claim.id.toString()));

  const isNoneSelected = claims?.every((claim) => !visitNumbers.includes(claim.id.toString()));

  useEffect(() => {
    // Set the indeterminate state of the select all checkbox
    // is (strictly) some of the providers in the page are selected
    if (checkAllRef.current) {
      checkAllRef.current.indeterminate = !isNoneSelected && !isAllSelected;
    }
  }, [isAllSelected, isNoneSelected]);

  const checkAllRef = useRef<HTMLInputElement>(null);

  const claimStatusBadge = (status: InvoiceStatus) => (
    <Badge
      className={getInvoiceStatusBadgeColors(status)[0]}
      circleClassName={getInvoiceStatusBadgeColors(status)[1]}
    >
      {invoiceStatusLabelsArr?.get(status) || "Unknown"}
    </Badge>
  );
  const dispatchStatusBadge = (status: boolean) => (
    <Badge
      className={getDispatchStatusBadgeColors(status)[0]}
      circleClassName={getDispatchStatusBadgeColors(status)[1]}
    >
      {dispatchStatusLabels?.get(status) || "Unknown"}
    </Badge>
  );

  const sendClaimsAllowed =
    reportType === ClaimsReportType.DELIVERY_STATUS &&
    payerId?.toString() == import.meta.env.VITE_KENGEN_PAYER_ID;

  const columnValues = (claim?: Claim) =>
    new Map<ClaimsReportType, Column[]>([
      [
        ClaimsReportType.SCHEME_UTILIZATION,
        [
          { header: "Visit Number", render: claim?.visitNumber },
          { header: "Member Number", render: claim?.memberNumber },
          { header: "Member Name", render: claim?.memberName },
          { header: "Scheme", render: claim?.schemeName },
          { header: "Category", render: claim?.categoryName },
          { header: "Benefit", render: claim?.benefitName },
          { header: "Provider", render: claim?.providerName },
          { header: "Date", render: formatDateStringGB(claim?.invoiceDate) },
          {
            header: "Amount",
            render: formatMoney(claim?.totalAmount || 0),
          },
          { header: "Status", render: dispatchStatusBadge(claim?.dispatched) },
          {
            header: "Actions",
            render: (
              <div className="flex gap-2 pr-2">
                <ExportMenu
                  isLoading={isExportClaimsLoading}
                  handleExport={(fileType) => handleExportVisit(claim?.id, fileType)}
                  buttonClassName="hover:text-gray-700"
                >
                  {/* prettier-ignore */}
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="h-5 w-5">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"/>
                  </svg>
                </ExportMenu>

                {/* <button
                  className="rounded-lg bg-white px-2 py-2 text-sm font-medium text-gray-600 hover:text-blue-700 focus:text-blue-700 focus:ring-2 focus:ring-blue-700"
                  title="Expand"
                  onClick={() => handleExpandClaim(claim?.id)}
                > */}
                {/* prettier-ignore */}
                {/* <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
                    <path d="M12 15a3 3 0 100-6 3 3 0 000 6z" />
                    <path fillRule="evenodd" d="M1.323 11.447C2.811 6.976 7.028 3.75 12.001 3.75c4.97 0 9.185 3.223 10.675 7.69.12.362.12.752 0 1.113-1.487 4.471-5.705 7.697-10.677 7.697-4.97 0-9.186-3.223-10.675-7.69a1.762 1.762 0 010-1.113zM17.25 12a5.25 5.25 0 11-10.5 0 5.25 5.25 0 0110.5 0z" clipRule="evenodd" />
                  </svg>
                </button> */}
              </div>
            ),
          },
        ],
      ],
      [
        ClaimsReportType.PROVIDER_UTILIZATION,
        [
          { header: "Visit Number", render: claim?.visitNumber },
          { header: "Member Number", render: claim?.memberNumber },
          { header: "Member Name", render: claim?.memberName },
          { header: "Scheme", render: claim?.schemeName },
          { header: "Category", render: claim?.categoryName },
          { header: "Benefit", render: claim?.benefitName },
          { header: "Provider", render: claim?.providerName },
          { header: "Date", render: formatDateStringGB(claim?.invoiceDate) },
          {
            header: "Amount",
            render: formatMoney(claim?.totalAmount || 0),
          },
          { header: "Status", render: dispatchStatusBadge(claim?.dispatched) },
          {
            header: "Actions",
            render: (
              <div className="flex gap-2 pr-2">
                <ExportMenu
                  isLoading={isExportClaimsLoading}
                  handleExport={(fileType) => handleExportVisit(claim?.id, fileType)}
                  buttonClassName="hover:text-gray-700"
                >
                  {/* prettier-ignore */}
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="h-5 w-5">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"/>
                </svg>
                </ExportMenu>

                {/* <button
                  className="rounded-lg bg-white px-2 py-2 text-sm font-medium text-gray-600 hover:text-blue-700 focus:text-blue-700 focus:ring-2 focus:ring-blue-700"
                  title="Expand"
                  onClick={() => handleExpandClaim(claim?.id)}
                > */}
                {/* prettier-ignore */}
                {/* <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
                    <path d="M12 15a3 3 0 100-6 3 3 0 000 6z" />
                    <path fillRule="evenodd" d="M1.323 11.447C2.811 6.976 7.028 3.75 12.001 3.75c4.97 0 9.185 3.223 10.675 7.69.12.362.12.752 0 1.113-1.487 4.471-5.705 7.697-10.677 7.697-4.97 0-9.186-3.223-10.675-7.69a1.762 1.762 0 010-1.113zM17.25 12a5.25 5.25 0 11-10.5 0 5.25 5.25 0 0110.5 0z" clipRule="evenodd" />
                  </svg>
                </button> */}
              </div>
            ),
          },
        ],
      ],
      [
        ClaimsReportType.DELIVERY_STATUS,
        [
          {
            header: (
              <>
                <input
                  type="checkbox"
                  name="check-all"
                  className="rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  checked={isAllSelected}
                  ref={checkAllRef}
                  onChange={handleCheckAllChange}
                  title={isAllSelected ? "Unselect All in Page" : "Select All in Page"}
                />
              </>
            ),
            render: (
              <>
                <Checkbox name="visitNumbers" value={claim?.id} />
              </>
            ),
            hidden: !sendClaimsAllowed,
          },
          { header: "Visit Number", render: claim?.visitNumber },
          { header: "Member No", render: claim?.memberNumber },
          { header: "Member Name", render: claim?.memberName },
          { header: "Provider", render: claim?.providerName },
          { header: "Scheme", render: claim?.schemeName },
          {
            header: "Benefit",
            render: claim?.benefitName,
          },
          {
            header: "Invoice Created Date",
            render: formatDateStringGB(claim?.invoiceDate),
          },
          { header: "Invoice No", render: claim?.invoiceNumber },
          { header: "Invoice Status", render: dispatchStatusBadge(claim?.dispatched) },
          {
            header: "Delivery Status",
            render: (
              <>
                <Badge
                  className={getPayerStatusBadgeColors(claim?.payerStatus)[0]}
                  circleClassName={getPayerStatusBadgeColors(claim?.payerStatus)[1]}
                >
                  {payerStatusLabels.get(claim?.payerStatus) || "Unknown"}
                </Badge>
              </>
            ),
          },
        ],
      ],
    ]);

  const getColumnWidth = (label: ReactNode) => {
    if (typeof label !== "string") {
      return 2;
    }

    switch (label) {
      case "Provider":
      case "Scheme":
        return 4;
      case "Select":
      case "Actions":
        return 1;
      default:
        return 2;
    }
  };

  const totalColumnSize = columnValues(claims?.[0])
    .get(reportType)
    .map(({ header }) => getColumnWidth(header))
    .reduce((a, b) => a + b, 0);

  const getPercentageColumnWidth = (label: ReactNode) =>
    `${(getColumnWidth(label) / totalColumnSize) * 100}%`;

  const sharedFilters = new Map<FilterType, Filter>([
    [
      FilterType.CLAIM_STATUS,
      {
        input: "statuses",
        filter: "statuses",
        render: (
          <details>
            <summary className="mb-2">Claim Status</summary>

            <CheckboxGroup
              name="statuses"
              options={claimReportsStatuses.map((status) => ({
                label: claimStatusLabels.get(status),
                value: status,
              }))}
              showFilter={false}
            />
          </details>
        ),
      },
    ],
    [
      FilterType.SCHEMES,
      {
        input: "policyIds",
        filter: "policyIds",
        render: (
          <details>
            <summary className="mb-2">Schemes</summary>

            <div>
              {isPoliciesLoading ? (
                <div className="flex items-center justify-center py-8">
                  {/* prettier-ignore */}
                  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-blue-400 w-8 h-8">
                    <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
                  </svg>
                </div>
              ) : policiesError ? (
                <div className="px-2 py-8 text-red-500">
                  Something went wrong while fetching schemes. Please refresh the page to retry.
                </div>
              ) : !policies?.length ? (
                <Empty message="No schemes found" />
              ) : (
                <div>
                  <CheckboxGroup
                    name="policyIds"
                    label="Schemes"
                    options={policies
                      .filter((policy) => policy.status == PolicyStatus.ACTIVE)
                      .map((policy) => ({
                        label: `${policy.plan.name}${
                          policy.status == PolicyStatus.EXPIRED ? " (Inactive)" : ""
                        }`,
                        value: policy.plan.id.toString(), //change from policyid to planid
                      }))}
                    fieldSetClassName="max-h-72 overflow-y-auto"
                  />
                </div>
              )}
            </div>
          </details>
        ),
      },
    ],
    [
      FilterType.SCHEME,
      {
        input: "policyId",
        filter: "policyIds",
        render: (
          <details>
            <summary className="mb-2">Scheme</summary>

            <div>
              {isPoliciesLoading ? (
                <div className="flex items-center justify-center py-8">
                  {/* prettier-ignore */}
                  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-blue-400 w-8 h-8">
                    <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
                  </svg>
                </div>
              ) : policiesError ? (
                <div className="px-2 py-8 text-red-500">
                  Something went wrong while fetching schemes. Please refresh the page to retry.
                </div>
              ) : !policies?.length ? (
                <Empty message="No schemes found" />
              ) : (
                <div>
                  <Select
                    name="policyId"
                    options={policies
                      .filter((policy) => policy.status === PolicyStatus.ACTIVE)
                      .map((policy) => ({
                        label: policy.plan.name,
                        value: policy.plan.id.toString(), //change from policyid to planid
                      }))}
                    placeholder="Search scheme"
                    styles={{
                      menuList: (provided) => ({
                        ...provided,
                        height: "120px",
                      }),
                    }}
                    required={true}
                  />
                </div>
              )}
            </div>
          </details>
        ),
      },
    ],

    //COVER PERIOD FILTER
    [
      FilterType.COVER_PERIODS,
      {
        // input: "policyId",
        // filter: "policyIds",
        render: (
          <details>
            <summary className="mb-2">Cover Period</summary>

            <div>
              {!(policyId || policyIds?.length) ? (
                <p className="px-2 py-4 text-sm text-gray-400">
                  Select a scheme to view the cover period
                </p>
              ) : isCoverPeriodsLoading ? (
                <div className="flex items-center justify-center py-8">
                  {/* prettier-ignore */}
                  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-blue-400 w-8 h-8">
                    <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
                  </svg>
                </div>
              ) : coverPeriodsError ? (
                <div className="px-2 py-8 text-red-500">
                  Something went wrong while fetching cover periods. Please refresh the page to
                  retry.
                </div>
              ) : !coverPeriodOptions?.length ? (
                <Empty message="No cover period found" />
              ) : (
                <div>
                  <select
                    id="cover-period-select"
                    name="coverPeriod"
                    value={selectedCoverPeriod === null ? "" : selectedCoverPeriod?.value}
                    onChange={handleChangeCoverPeriod}
                    className="w-full rounded border-gray-300"
                  >
                    <option value="">Select Cover Period</option>
                    {coverPeriodOptions.map((period) => (
                      <option value={period.value} key={period.value}>
                        {period.startDate}/{period.endDate}
                      </option>
                    ))}
                  </select>
                </div>
              )}
            </div>
          </details>
        ),
      },
    ],
    //END COVER PERIOD FILTER
    [
      FilterType.VISIT_TYPES,
      {
        input: "visitTypes",
        filter: "visitTypes",
        render: (
          <details>
            <summary className="mb-2">Visit Type</summary>

            <CheckboxGroup
              name="visitTypes"
              options={reportVisitTypes.get(reportType).map((visitType) => ({
                label: visitTypeLabels.get(visitType),
                value: visitType,
              }))}
              showFilter={false}
            />
          </details>
        ),
      },
    ],
    [
      FilterType.PAYER_STATUS,
      {
        input: "payerStatus",
        filter: "payerStatuses",
        render: (
          <details>
            <summary className="mb-2">Delivery Status</summary>

            <RadioGroup
              name="payerStatus"
              options={Object.values(PayerStatus).map((status) => ({
                label: payerStatusLabels.get(status),
                value: status,
              }))}
            />
          </details>
        ),
      },
    ],
    [
      FilterType.MAIN_FACILITY,
      {
        input: "mainFacility",
        filter: "mainFacilities",
        render: (
          <details>
            <summary className="mb-2">Main Facility</summary>

            <SelectProvider
              name="mainFacility"
              type={SearchProviderFilter.MAIN}
              placeholder="Search provider..."
            />
          </details>
        ),
      },
    ],
    [
      FilterType.SERVICE_GROUPS,
      {
        input: "serviceGroups",
        filter: "serviceGroups",
        render: (
          <details>
            <summary className="mb-2">Benefits</summary>

            <CheckboxGroup
              name="serviceGroups"
              options={Object.values(ServiceGroup).map((serviceGroup) => ({
                label: serviceGroupLabels.get(serviceGroup),
                value: serviceGroup,
              }))}
              showFilter={false}
            />
          </details>
        ),
      },
    ],
    [
      FilterType.BRANCHES,
      {
        input: "branches",
        filter: "providerIds",
        render: (
          <details>
            <summary className="mb-2">Branch</summary>

            {!mainFacilityId ? (
              <p className="px-2 py-4 text-sm text-gray-400">
                Select a main facility to select branches
              </p>
            ) : (
              <SelectProviders
                name="branches"
                fieldsetClassName="max-h-72 overflow-y-auto"
                mainProvider={mainFacilityId}
                type={SearchProviderFilter.BRANCH}
              />
            )}
          </details>
        ),
      },
    ],
    [
      FilterType.CATEGORIES,
      {
        input: "categoryIds",
        filter: "categoryIds",
        render: (
          <details>
            <summary className="mb-2">Categories</summary>

            <div>
              {selectedCoverPeriod === null ? (
                <p className="px-2 py-4 text-sm text-gray-400">
                  Select a cover period to view categories
                </p>
              ) : isCategoriesLoading ? (
                <div className="flex items-center justify-center py-8">
                  {/* prettier-ignore */}
                  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-blue-400 w-8 h-8">
                    <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
                  </svg>
                </div>
              ) : categoriesError ? (
                <div className="px-2 py-8 text-red-500">
                  Something went wrong while fetching categories. Please refresh the page to retry.
                </div>
              ) : !categories?.length ? (
                <Empty message="No categories found" />
              ) : (
                <div>
                  <CheckboxGroup
                    name="categoryIds"
                    label="Categories"
                    options={categories.map((category) => ({
                      label: `${category.policy.plan.name} - ${category.name}`,
                      value: category.id.toString(),
                    }))}
                    fieldSetClassName="max-h-72 overflow-y-auto"
                  />
                </div>
              )}
            </div>
          </details>
        ),
      },
    ],
  ]);

  const reportFilters = new Map<ClaimsReportType, Filter[]>([
    [
      ClaimsReportType.SCHEME_UTILIZATION,
      [
        {
          mandatory: true,
          ...sharedFilters.get(FilterType.SCHEME),
        },
        sharedFilters.get(FilterType.COVER_PERIODS),
        sharedFilters.get(FilterType.CATEGORIES),
        sharedFilters.get(FilterType.VISIT_TYPES),
        sharedFilters.get(FilterType.CLAIM_STATUS),
      ],
    ],
    [
      ClaimsReportType.PROVIDER_UTILIZATION,
      [
        {
          mandatory: true,
          ...sharedFilters.get(FilterType.MAIN_FACILITY),
        },
        sharedFilters.get(FilterType.BRANCHES),
        sharedFilters.get(FilterType.SCHEMES),
        sharedFilters.get(FilterType.VISIT_TYPES),
        sharedFilters.get(FilterType.SERVICE_GROUPS),
        sharedFilters.get(FilterType.CLAIM_STATUS),
      ],
    ],
    [
      ClaimsReportType.DELIVERY_STATUS,
      [
        {
          mandatory: true,
          ...sharedFilters.get(FilterType.PAYER_STATUS),
        },
        sharedFilters.get(FilterType.CLAIM_STATUS),
        sharedFilters.get(FilterType.SCHEMES),
        sharedFilters.get(FilterType.MAIN_FACILITY),
        sharedFilters.get(FilterType.VISIT_TYPES),
      ],
    ],
  ]);

  const isFiltersFormValid = reportFilters
    .get(reportType)
    .filter((f) => f.mandatory)
    .map((f) => f.input)
    .every(formFilterActive);

  const isFiltersValid = reportFilters
    .get(reportType)
    .filter((f) => f.mandatory)
    .map((f) => f.filter)
    .every(filterActive);

  const SubmitFilters = (
    <Popover.Button
      type="button"
      className={clsx(
        "flex items-center justify-center gap-2 rounded-md bg-blue-500 px-8 py-2 text-center font-medium text-white focus:ring-2 focus:ring-blue-700",
        !isFiltersFormValid || isClaimsLoading
          ? "cursor-not-allowed opacity-40"
          : "hover:bg-blue-600",
      )}
      title={!isFiltersFormValid ? "Select all required filters" : "Search"}
      disabled={isClaimsLoading || !isFiltersFormValid}
      onClick={() => {
        // Manually submit the form
        handleFiltersSubmit(handleSubmitFilters)();
      }}
    >
      Search
    </Popover.Button>
  );

  useEffect(() => {
    if (isFiltersFormValid && !isClaimsFetching && !isClaimsLoading) {
      handleFiltersSubmit(handleSubmitFilters)();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, size]);

  return (
    <div className="flex h-full overflow-hidden">
      <div className="relative flex max-h-screen flex-1 flex-col overflow-y-auto overflow-x-hidden ">
        <main className="px-4 pb-10 text-gray-600">
          {!payerId && <AuthenticationError />}

          <div className="mb-4 flex flex-wrap items-end justify-between gap-4 py-2">
            <div className="flex items-center gap-2">
              <div title="Report Type">
                <label htmlFor="report-type" className="text-gray-500" title="Report Type">
                  Type:{" "}
                </label>

                <select
                  id="report-type"
                  value={reportType}
                  onChange={handleChangeReportType}
                  className="rounded border-gray-300"
                >
                  {claimsReportsTypes.map((type) => (
                    <option value={type} key={type}>
                      {reportTypeLabels.get(type)}
                    </option>
                  ))}
                </select>
              </div>

              <Popover className="relative">
                <Popover.Button
                  className={clsx(
                    "inline-flex w-full items-center justify-center gap-2 rounded-md border border-gray-300 px-4 py-2 text-sm font-medium hover:border-gray-400",
                  )}
                >
                  {/* prettier-ignore */}
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 01-.659 1.591l-5.432 5.432a2.25 2.25 0 00-.659 1.591v2.927a2.25 2.25 0 01-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 00-.659-1.591L3.659 7.409A2.25 2.25 0 013 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0112 3z" />
                  </svg>
                  Filter
                  {/* prettier-ignore */}
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="-mr-1 h-5 w-5">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                  </svg>
                </Popover.Button>

                <Transition as={Fragment} {...dropdownTransitions}>
                  <Popover.Panel className="absolute z-10 mt-2 w-96 origin-top-right overflow-y-auto rounded-lg border border-gray-300 bg-gray-50 p-4 shadow focus:outline-none">
                    <div>
                      <div className="flex justify-between gap-2">
                        <p className="uppercase text-gray-400">Filtered By</p>

                        <button
                          onClick={() => resetFilters()}
                          className={clsx(
                            "text-sm",
                            !activeFilterEntries.length
                              ? "cursor-not-allowed text-gray-300"
                              : "text-red-500",
                          )}
                          disabled={!activeFilterEntries.length}
                        >
                          Clear All
                        </button>
                      </div>

                      {!activeFilterEntries.length ? (
                        <p className="py-4 text-center text-gray-400">No filters applied</p>
                      ) : (
                        <div className="flex flex-col gap-1 py-2 text-sm">
                          {activeFilterEntries.map(([key]) => (
                            <div key={key} className="flex justify-between gap-2">
                              <p>
                                <span className="text-gray-300">{filterLabels.get(key)}: </span>
                                <span className="text-gray-400">
                                  {getFilterValueLabel(key as keyof Inputs)}
                                </span>
                              </p>

                              <button
                                className="text-red-400"
                                title="Remove filter"
                                onClick={() => {
                                  resetFiltersField(key as keyof Inputs);
                                }}
                              >
                                {/* prettier-ignore */}
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-4 h-4">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                              </button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    <Form
                      className="flex flex-col gap-2"
                      methods={filtersMethods}
                      onSubmit={handleSubmitFilters}
                    >
                      {reportFilters.get(reportType)?.map((filter) => filter.render)}

                      {SubmitFilters}
                    </Form>
                  </Popover.Panel>
                </Transition>
              </Popover>

              <DateRangePicker
                value={dateRange}
                onChange={setDateRange}
                // options={
                //   activePolicy
                //     ? {
                //         minDate: activePolicy.startDate,
                //         maxDate: activePolicy.endDate,
                //       }
                //     : {}
                // }
                options={
                  selectedCoverPeriod
                    ? {
                        minDate: selectedCoverPeriod?.startDate,
                        maxDate: selectedCoverPeriod?.endDate,
                      }
                    : {}
                }
                footer={<div className="flex justify-end py-1">{SubmitFilters}</div>}
              />

              <div>
                {isClaimsFetchingOnly && (
                  <>
                    {/* prettier-ignore */}
                    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-gray-400 w-6 h-6">
                      <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z">
                        <animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/>
                      </path>
                    </svg>
                  </>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Menu as="div" className="relative inline-block text-left">
                <div>
                  <Menu.Button
                    className={clsx(
                      "inline-flex w-full items-center justify-center gap-2 rounded-md bg-blue-500 px-4 py-2 text-sm font-medium text-white",
                      isExportClaimsLoading || !claims.length
                        ? "cursor-not-allowed opacity-60"
                        : "hover:bg-blue-600",
                    )}
                    disabled={isExportClaimsLoading || !claims.length}
                  >
                    {isExportClaimsLoading && (
                      <>
                        {/* prettier-ignore */}
                        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="h-5 w-5">
                          <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
                        </svg>
                      </>
                    )}
                    {/* prettier-ignore */}
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="h-5 w-5">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"/>
                    </svg>
                    Export
                    {/* prettier-ignore */}
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="-mr-1 h-5 w-5">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                    </svg>
                  </Menu.Button>
                </div>

                <Transition as={Fragment} {...dropdownTransitions}>
                  <Menu.Items className="absolute right-0 mt-2 w-36 origin-top-right rounded-md border border-gray-200 bg-white focus:outline-none">
                    {Object.values(ExportFileType).map((fileType) => (
                      <Menu.Item key={fileType}>
                        {({ active }) => (
                          <button
                            className={`${
                              active ? "bg-gray-50" : ""
                            } group flex w-full items-center rounded-md px-6 py-4 text-sm`}
                            onClick={() => handleExport(ExportFileType[fileType])}
                          >
                            {fileType}
                          </button>
                        )}
                      </Menu.Item>
                    ))}
                  </Menu.Items>
                </Transition>
              </Menu>
            </div>
          </div>

          {sendClaimsAllowed && (
            <div className="mb-4 flex justify-between gap-4">
              <button
                className={clsx(
                  "flex items-center gap-2 rounded-full border-2 border-gray-300 bg-white px-4 py-1 text-sm font-medium text-gray-600 hover:border-gray-400 focus:ring-2 focus:ring-blue-700",
                  (isSendVisitsSentLoading || !visitNumbers.length) &&
                    "cursor-not-allowed opacity-60",
                )}
                disabled={isSendVisitsSentLoading || !visitNumbers.length}
                onClick={handleSendVisits}
              >
                <span className="text-green-500">
                  {isSendVisitsSentLoading ? (
                    <>
                      {/* prettier-ignore */}
                      <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="w-5 h-5">
                        <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
                      </svg>
                    </>
                  ) : (
                    <>
                      {/* prettier-ignore */}
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </>
                  )}
                </span>
                <span>Send</span>
              </button>
            </div>
          )}

          <div className="flex-grow pb-4">
            {isClaimsUninitialized ? (
              <div className="flex items-center justify-center p-4">
                <div className="flex flex-col items-center gap-1">
                  <img src={doctorSvg} alt="Image of a doctor" className="h-auto w-48" />

                  <h2 className="text-center text-2xl font-medium">No reports</h2>

                  <p className="text-center text-gray-300">
                    Click on the filter button to select the reports you want to view...
                  </p>
                </div>
              </div>
            ) : isClaimsLoading ? (
              <div className="flex items-center justify-center py-8">
                {/* prettier-ignore */}
                <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-blue-400">
                  <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
                </svg>
              </div>
            ) : claimsError ? (
              <div className="px-2 py-8 text-red-500">
                Something went wrong. Please refresh the page to retry.
              </div>
            ) : !claims.length || filters === undefined ? (
              <div className="flex items-center justify-center p-8">
                <div className="flex flex-col items-center">
                  <img src={doctorSvg} alt="Image of a doctor" className="h-auto w-48" />
                  <Empty message="No claims matching filters" />
                  <p className="text-center text-gray-300">
                    Click on the filter button to select the reports you want to view...
                  </p>
                </div>
              </div>
            ) : (
              <div>
                <div className="mb-4 max-w-full overflow-x-auto">
                  <table className="w-full table-auto">
                    <colgroup>
                      {columnValues(claims?.[0])
                        .get(reportType)
                        ?.filter(({ hidden = false }) => !hidden)
                        ?.map(({ header }, index) => (
                          <col key={index} style={{ width: getPercentageColumnWidth(header) }} />
                        ))}
                    </colgroup>

                    <thead>
                      <tr className="bg-gray-50">
                        {columnValues(claims?.[0])
                          .get(reportType)
                          ?.filter(({ hidden = false }) => !hidden)
                          ?.map((column, index) => (
                            <th
                              className={clsx("px-2 py-4 text-left first:pl-8 last:pr-8")}
                              key={index}
                            >
                              {column.header}
                            </th>
                          ))}
                      </tr>
                    </thead>

                    <Form
                      className="flex flex-col gap-2"
                      methods={selectedClaimsMethods}
                      asFragment
                    >
                      <tbody>
                        {claims?.map((claim) => (
                          <Fragment key={claim.id}>
                            <tr>
                              {columnValues(claim)
                                .get(reportType)
                                ?.filter(({ hidden = false }) => !hidden)
                                ?.map((column, index) => (
                                  <td
                                    className={clsx("px-2 py-4 first:pl-8 last:pr-8")}
                                    key={index}
                                  >
                                    {column.render || column.defaultValue || "-"}
                                  </td>
                                ))}
                            </tr>

                            {expandedClaim === claim.id && (
                              <tr>
                                {/* WARNING: colspan will fail with a fixed table, or more than 100 columns */}
                                <td colSpan={100} className="rounded border-b bg-gray-50">
                                  <table className="w-full">
                                    <thead className="bg-gray-100 text-sm uppercase text-gray-500">
                                      <tr>
                                        <th className="py-2 pl-8 text-left font-medium">ID</th>
                                        <th className="px-2 py-2 text-left font-medium">
                                          Invoice Number
                                        </th>
                                        <th className="px-2 py-2 text-left font-medium">Amount</th>
                                        <th className="px-2 py-2 text-left font-medium">Service</th>
                                        <th className="px-2 py-2 text-left font-medium">Status</th>
                                        <th className="py-2 pr-8 text-left font-medium">
                                          Created Date
                                        </th>
                                      </tr>
                                    </thead>

                                    <tbody>
                                      {claim.invoices?.map((invoice) => (
                                        <tr key={invoice.id}>
                                          <td className="py-2 pl-8">{invoice.id}</td>
                                          <td className="px-2 py-2">{invoice.invoiceNumber}</td>
                                          <td className="px-2 py-2">
                                            {formatMoney(invoice.totalAmount)}
                                          </td>
                                          <td className="px-2 py-2">{claim.benefitName}</td>
                                          <td className="px-2 py-2">
                                            <Badge
                                              className={
                                                getInvoiceStatusBadgeColors(invoice.status)[0] ?? ""
                                              }
                                              circleClassName={
                                                getInvoiceStatusBadgeColors(invoice.status)[1] ?? ""
                                              }
                                            >
                                              {invoiceStatusLabels[invoice.status] ||
                                                invoice.status}
                                            </Badge>
                                          </td>
                                          <td className="py-2 pr-8">
                                            {invoice.createdAt
                                              ? formatDateStringGB(invoice.createdAt)
                                              : "Unknown"}
                                          </td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </td>
                              </tr>
                            )}
                          </Fragment>
                        ))}
                      </tbody>
                    </Form>
                  </table>
                </div>

                <Pagination
                  totalElements={claimsResponse?.data?.totalElements ?? 0}
                  totalPages={claimsResponse?.data?.totalPages ?? 1}
                  page={page}
                  size={size}
                  setPage={setPage}
                  setSize={setSize}
                  isLoading={isClaimsFetchingOnly}
                />
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
};

export default ClaimsReports;
