import { Menu, Transition } from "@headlessui/react";
import { Fragment } from "react";
import { ExportFileType } from "~lib/api/types";
import { dropdownTransitions } from "~lib/constants";
import { clsx } from "~lib/utils";

interface Props {
  isLoading: boolean;
  children?: React.ReactNode;
  handleExport?: (fileType: ExportFileType) => void;
  buttonClassName?: string;
}

const ExportMenu = ({
  isLoading,
  children,
  handleExport,
  buttonClassName,
}: Props) => {
  return (
    <Menu as="div" className="relative inline-block text-left">
      <div>
        <Menu.Button
          className={clsx(
            "inline-flex gap-2 items-center w-full justify-center px-4 py-2 text-sm font-medium",
            isLoading && "cursor-not-allowed opacity-60",
            buttonClassName
          )}
          disabled={isLoading}
          title="Export"
        >
          {isLoading && (
            <>
              {/* prettier-ignore */}
              <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="h-5 w-5">
                <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
              </svg>
            </>
          )}
          {children || "Export"}
        </Menu.Button>
      </div>

      <Transition as={Fragment} {...dropdownTransitions}>
        <Menu.Items className="absolute right-0 mt-2 w-36 z-10 border border-gray-200 origin-top-right rounded-md bg-white focus:outline-none">
          {Object.values(ExportFileType).map((fileType) => (
            <Menu.Item key={fileType}>
              {({ active }) => (
                <button
                  className={`${
                    active ? "bg-gray-50" : ""
                  } group flex w-full items-center rounded-md px-6 py-4 text-sm`}
                  onClick={() => handleExport?.(ExportFileType[fileType])}
                >
                  {fileType}
                </button>
              )}
            </Menu.Item>
          ))}
        </Menu.Items>
      </Transition>
    </Menu>
  );
};

export default ExportMenu;
