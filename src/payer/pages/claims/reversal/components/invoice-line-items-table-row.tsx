import React from "react";
import { InvoiceLine } from "../../../../lib/types/claims/invoice";
import TableDataItem from "../../../../components/ui/table/TableDataItem";

interface InvoiceLinesTableItemProps {
  invoiceLine: InvoiceLine;
}

export default function InvoiceLineItemsTableRow({ invoiceLine }: InvoiceLinesTableItemProps) {
  return (
    <tr>
      <TableDataItem className="text-xs">{invoiceLine.id}</TableDataItem>
      <TableDataItem className="text-xs">{invoiceLine.invoiceNumber}</TableDataItem>
      <TableDataItem className="text-xs">{invoiceLine.description || "-"}</TableDataItem>
      <TableDataItem className="text-xs">{invoiceLine.lineType || "-"}</TableDataItem>
      <TableDataItem className="text-xs">{invoiceLine.lineCategory || "-"}</TableDataItem>
      <TableDataItem className="text-xs">{invoiceLine.quantity}</TableDataItem>
      <TableDataItem className="text-xs">{invoiceLine.unitPrice}</TableDataItem>
      <TableDataItem className="text-xs">{invoiceLine.lineTotal}</TableDataItem>
    </tr>
  );
}
