import React, { useState } from "react";
import ReversalsReportTableRow from "./reversals-report-table-row";
import TableHeaderItem from "../../../../components/ui/table/TableHeaderItem";
import { InvoiceData } from "../../../../lib/types/claims/invoice";
import { Empty } from "~lib/components";

interface ReversalReportTableProps {
  invoices: Array<InvoiceData>;
}

export default function ClaimsReversalInvoicesTable({ invoices }: ReversalReportTableProps) {
  const tableHeaderItems: Array<string> = [
    "Visit Number",
    "Created At",
    "Member Number",
    "Member Name",
    "Benefit Name",
    "Invoice Number",
    "Status",
    "Amount",
    "Action",
  ];

  const [openInvoiceId, setOpenInvoiceId] = useState<string | null>(null);

  const handleToggle = (invoiceId: string) => {
    setOpenInvoiceId((prev) => (prev === invoiceId ? null : invoiceId));
  };

  return (
    <div className="w-full rounded border border-gray-200">
      {invoices.length > 0 ? (
        <table className="w-full">
          <thead className={`${invoices?.length > 0 ? "visible" : "hidden"} `}>
            <tr className="bg-gray-100">
              {tableHeaderItems.map((item) => (
                <TableHeaderItem key={item} item={item} className="text-xs" />
              ))}
            </tr>
          </thead>
          <tbody>
            {invoices.map((invoice) => (
              <ReversalsReportTableRow
                key={invoice.id}
                invoice={invoice}
                colspan={tableHeaderItems.length}
                isInvoiceDetailsOpen={openInvoiceId === String(invoice.id)}
                onToggle={() => handleToggle(String(invoice.id))}
              />
            ))}
          </tbody>
        </table>
      ) : (
        <div className="flex h-[200px] w-full items-center justify-center">
          <Empty message="No Transactions for Selected Filter" />
        </div>
      )}
    </div>
  );
}
