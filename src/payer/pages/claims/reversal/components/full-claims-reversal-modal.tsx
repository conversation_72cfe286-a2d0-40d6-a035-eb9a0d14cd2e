import React from "react";
import UserService from "../../../../services/UserService";
import * as notifications from "../../../../lib/notifications.js";
import { useReverseInvoiceMutation } from "../../../../api/features/claimsApi";
import { toast } from "react-toastify";
import DialogWrapper from "../../../../components/ui/modal/DialogWrapper";
import { InvoiceData, ReverseInvoicePayload } from "../../../../lib/types/claims/invoice";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Button from "../../../../components/ui/Button";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { resetReversalState } from "../../../../features/claims/claimsReversalSlice";
import { useAppSelector } from "../../../../store/hooks";

interface ClaimsReversalModalProps {
  invoice: InvoiceData | null;
  isFullClaimsReversalModalOpen: boolean;
  onClose: () => void;
}

const fullReversalSchema = z.object({
  reason: z.string().min(1, { message: "Reason is required" }),
});

type FullReversalFormValues = z.infer<typeof fullReversalSchema>;

export default function FullClaimsReversalModal({
  invoice,
  isFullClaimsReversalModalOpen,
  onClose,
}: ClaimsReversalModalProps) {
  const username = UserService.getUsername();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<FullReversalFormValues>({
    resolver: zodResolver(fullReversalSchema),
    defaultValues: {
      reason: "",
    },
  });

  const [reverseInvoice, { isLoading: isReversingInvoice }] = useReverseInvoiceMutation();

  const handleClose = () => {
    reset();
    onClose();
    resetReversalState();
  };

  const { shouldIgnoreReversalErrorsForPayerSentClaims } = useAppSelector(
    (state) => state.claimsReversal,
  );

  const handleFullInvoiceReversal = async (data: FullReversalFormValues) => {
    if (invoice == null) {
      toast.error("No invoice selected!");
      return;
    }

    if (invoice?.status === "REJECTED") {
      notifications.Error({
        title: "Claim Already Reversed",
      });
      return;
    }

    const payload: ReverseInvoicePayload = {
      invoiceId: String(invoice.id),
      amount: invoice.totalAmount,
      reason: data.reason,
      reversedBy: username,
      ...(shouldIgnoreReversalErrorsForPayerSentClaims && {
        ignoreErrors: shouldIgnoreReversalErrorsForPayerSentClaims,
      }),
    };

    try {
      await reverseInvoice(payload).unwrap();
      notifications.Success({
        title: "Reversal Successful",
      });
      handleClose();
    } catch (error: unknown) {
      const errorObj = error as { data?: { error: string } };
      const errorMessage = errorObj?.data?.error || "An error occurred while reversing the claim!";
      notifications.Error({
        title: "Claim Reversal Failed",
      });
      toast.error(errorMessage);
    }
  };

  return (
    <DialogWrapper onClose={handleClose} show={isFullClaimsReversalModalOpen} maxWidth="max-w-3xl">
      <form onSubmit={handleSubmit(handleFullInvoiceReversal)}>
        <div className="bg-white px-10 pb-1 pt-4">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-lg text-midGray">
              Reversal for invoice:{" "}
              <span className="font-semibold text-darkGray">{invoice?.invoiceNumber}</span>
            </h2>
            <button onClick={handleClose} type="button" className="focus:outline-none">
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <div className="mt-6 flex flex-col gap-2">
            <div className="flex items-center">
              <label htmlFor="reason" className="text-sm font-medium">
                Reason:
              </label>
              <span className="ml-1 text-red-600">*</span>
            </div>
            <textarea
              id="reason"
              className={`w-full rounded-md border ${errors.reason ? "border-red-500" : "border-gray-300"} px-3 py-2`}
              placeholder="Add Reversal Reason"
              rows={4}
              {...register("reason")}
            ></textarea>
            {errors.reason && <span className="text-xs text-red-500">{errors.reason.message}</span>}
          </div>
        </div>

        <div className="mt-5 bg-white px-5 py-4">
          <div className="flex flex-wrap justify-center space-x-2">
            <Button variant="outlined" onClick={handleClose} type="button" className="w-44">
              Cancel
            </Button>

            {isReversingInvoice ? (
              <Button disabled type="button" className="w-44">
                <span className="flex items-center">
                  Loading...
                  <svg className="ml-1 h-5 w-5 animate-spin fill-current" viewBox="0 0 16 16">
                    <path d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z" />
                  </svg>
                </span>
              </Button>
            ) : (
              <Button type="submit" className="w-44">
                Reverse Invoice
              </Button>
            )}
          </div>
        </div>
      </form>
    </DialogWrapper>
  );
}
