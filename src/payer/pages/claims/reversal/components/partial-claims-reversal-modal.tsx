import React from "react";
import * as notifications from "../../../../lib/notifications.js";
import { useReverseInvoiceMutation } from "../../../../api/features/claimsApi";
import { toast } from "react-toastify";
import DialogWrapper from "../../../../components/ui/modal/DialogWrapper";
import { InvoiceData, ReverseInvoicePayload } from "../../../../lib/types/claims/invoice";
import { SubmitHandler, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import UserService from "../../../../services/UserService";
import Button from "../../../../components/ui/Button";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { resetReversalState } from "../../../../features/claims/claimsReversalSlice";
import { useAppSelector } from "../../../../store/hooks";

interface PartialClaimsReversalModalProps {
  invoice: InvoiceData | null;
  isPartialClaimsReversalModalOpen: boolean;
  onClose: () => void;
}

const partialReversalSchema = z.object({
  creditNoteNumber: z.string().optional(),
  creditNoteAmount: z
    .string()
    .min(1, { message: "Credit Note Amount is required" })
    .refine((val) => /^[+-]?\d*(?:[.,]\d*)?$/.test(val), {
      message: "Credit Note Amount must be a valid number",
    }),
  reason: z.string().min(1, { message: "Reason is required" }),
  sendToPayer: z.boolean().default(false),
});

type PartialReversalFormValues = z.infer<typeof partialReversalSchema>;

export default function PartialClaimsReversalModal({
  invoice,
  isPartialClaimsReversalModalOpen,
  onClose,
}: PartialClaimsReversalModalProps) {
  const username = UserService.getUsername();
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<PartialReversalFormValues>({
    resolver: zodResolver(partialReversalSchema) as any,
    defaultValues: {
      creditNoteNumber: "",
      creditNoteAmount: "",
      reason: "",
      sendToPayer: false,
    },
  });

  const [reverseInvoice, { isLoading: isReversingClaim }] = useReverseInvoiceMutation();

  const handleClose = () => {
    reset();
    onClose();
    resetReversalState();
  };

  const { shouldIgnoreReversalErrorsForPayerSentClaims } = useAppSelector(
    (state) => state.claimsReversal,
  );

  const handleReverse: SubmitHandler<PartialReversalFormValues> = async (data) => {
    if (invoice == null) {
      toast.error("No invoice selected!");
      return;
    }

    if (invoice.status === "REJECTED") {
      notifications.Error({
        title: "Claim Already Reversed",
      });
      return;
    }

    const payload: ReverseInvoicePayload = {
      invoiceId: String(invoice.id),
      amount: parseFloat(data.creditNoteAmount),
      reason: data.reason,
      reversedBy: username,
      creditNoteNumber: data.creditNoteNumber,
      sendToPayer: data.sendToPayer,
      ...(shouldIgnoreReversalErrorsForPayerSentClaims && {
        ignoreErrors: shouldIgnoreReversalErrorsForPayerSentClaims,
      }),
    };

    try {
      await reverseInvoice(payload).unwrap();
      notifications.Success({
        title: "Reversal Successful",
      });
      handleClose();
    } catch (error: unknown) {
      const errorObj = error as { data?: { error: string } };
      const errorMessage = errorObj?.data?.error || "An error occurred while reversing the claim!";
      notifications.Error({
        title: "Claim Reversal Failed",
      });
      toast.error(errorMessage);
    }
  };

  return (
    <DialogWrapper
      onClose={handleClose}
      show={isPartialClaimsReversalModalOpen}
      maxWidth="max-w-3xl"
    >
      <form onSubmit={handleSubmit(handleReverse)}>
        <div className="bg-white px-10 pb-1 pt-4">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-lg text-midGray">
              Partial Reversal for invoice:{" "}
              <span className="font-semibold text-darkGray">{invoice?.invoiceNumber}</span>
            </h2>
            <button onClick={handleClose} type="button" className="focus:outline-none">
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <div className="mt-6 flex flex-col gap-6">
            <div className="flex flex-col gap-2">
              <label htmlFor="creditNoteNumber" className="text-sm font-medium">
                Credit Note Number:
              </label>
              <input
                type="text"
                id="creditNoteNumber"
                {...register("creditNoteNumber")}
                placeholder="Enter Credit Note Number"
                className="rounded-md border border-gray-300 px-3 py-2 focus:border-gray-700 focus:outline-none"
              />
            </div>

            <div className="flex flex-col gap-4 md:flex-row">
              <div className="flex flex-1 flex-col gap-2">
                <div className="flex items-center">
                  <label htmlFor="creditNoteAmount" className="text-sm font-medium">
                    Credit Note Amount:
                  </label>
                  <span className="ml-1 text-red-600">*</span>
                </div>
                <input
                  type="text"
                  id="creditNoteAmount"
                  maxLength={9}
                  {...register("creditNoteAmount")}
                  placeholder="Enter Credit Note Amount"
                  className={`rounded-md border ${
                    errors.creditNoteAmount ? "border-red-500" : "border-gray-300"
                  } px-3 py-2 focus:border-gray-700 focus:outline-none`}
                />
                {errors.creditNoteAmount && (
                  <span className="text-xs text-red-500">{errors.creditNoteAmount.message}</span>
                )}
              </div>

              <div className="flex h-10 items-center self-end">
                <input
                  type="checkbox"
                  id="sendToPayer"
                  {...register("sendToPayer")}
                  className="mr-2 border-gray-300"
                />
                <label htmlFor="sendToPayer" className="text-sm">
                  Tick to resend the invoice to Payer system
                </label>
              </div>
            </div>

            <div className="flex flex-col gap-2">
              <div className="flex items-center">
                <label htmlFor="reason" className="text-sm font-medium">
                  Reason:
                </label>
                <span className="ml-1 text-red-600">*</span>
              </div>
              <textarea
                id="reason"
                className={`w-full rounded-md border ${errors.reason ? "border-red-500" : "border-gray-300"} px-3 py-2`}
                placeholder="Add Partial Reversal Reason"
                rows={4}
                {...register("reason")}
              ></textarea>
              {errors.reason && (
                <span className="text-xs text-red-500">{errors.reason.message}</span>
              )}
            </div>
          </div>
        </div>

        <div className="mt-5 bg-white px-5 py-4">
          <div className="flex flex-wrap justify-center space-x-2">
            <Button variant="outlined" onClick={handleClose} type="button" className="w-44">
              Cancel
            </Button>

            {isReversingClaim ? (
              <Button disabled type="button" className="w-44">
                <span className="flex items-center">
                  Loading...
                  <svg className="ml-1 h-5 w-5 animate-spin fill-current" viewBox="0 0 16 16">
                    <path d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z" />
                  </svg>
                </span>
              </Button>
            ) : (
              <Button type="submit" className="w-44">
                Save
              </Button>
            )}
          </div>
        </div>
      </form>
    </DialogWrapper>
  );
}
