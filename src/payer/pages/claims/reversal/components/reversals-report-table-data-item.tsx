import { ReactNode } from "react";
import { cn } from "~lib/utils/cn";

interface ReversalsReportTableDataItemProps {
  item?: string | number | undefined;
  title?: string | number | undefined;
  children?: ReactNode;
  className?: string;
  isInvoiceDetailsOpen?: boolean;
  colspan?: number;
}

const ReversalsReportTableDataItem = ({
  item,
  title,
  children,
  className,
  isInvoiceDetailsOpen,
  colspan,
}: ReversalsReportTableDataItemProps) => {
  return (
    <td
      title={(title || item) as string}
      className={cn(
        "overflow-hidden overflow-ellipsis px-4 py-2.5 text-left text-base text-customGray",
        className,
        isInvoiceDetailsOpen ? "" : "border-b border-[#EAECF0]",
      )}
      colSpan={colspan}
    >
      {children || item}
    </td>
  );
};

export default ReversalsReportTableDataItem;
