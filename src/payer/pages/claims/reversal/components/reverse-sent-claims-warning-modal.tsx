import DialogWrapper from "../../../../components/ui/modal/DialogWrapper";
import Button from "../../../../components/ui/Button";
import {
  InvoiceReversalType,
  resetReversalState,
  setIsFullReversalModalOpen,
  setIsPartialReversalModalOpen,
  setIsReverseSentClaimsWarningModalOpen,
  setShouldIgnoreReversalErrorsForPayerSentClaims,
} from "../../../../features/claims/claimsReversalSlice";
import { useAppDispatch, useAppSelector } from "../../../../store/hooks";
import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";

export default function ReverseSentClaimsWarningModal() {
  const dispatch = useAppDispatch();
  const { isReverseSentClaimsWarningModalOpen, selectedInvoice, reverseSentClaimsReversalType } =
    useAppSelector((state) => state.claimsReversal);

  const handleClose = () => {
    dispatch(resetReversalState());
  };

  const handleContinue = () => {
    dispatch(setIsReverseSentClaimsWarningModalOpen(false));
    dispatch(setShouldIgnoreReversalErrorsForPayerSentClaims(true));
    if (reverseSentClaimsReversalType === InvoiceReversalType.FULL) {
      dispatch(setIsFullReversalModalOpen(true));
    } else {
      dispatch(setIsPartialReversalModalOpen(true));
    }
  };

  if (!selectedInvoice) return null;

  return (
    <DialogWrapper
      show={isReverseSentClaimsWarningModalOpen}
      onClose={handleClose}
      maxWidth="max-w-2xl"
    >
      <div className="p-6">
        <div className="flex items-center space-x-4 pb-4">
          <div className="rounded-full bg-yellow-50 p-1">
            <ExclamationTriangleIcon className="h-8 w-8 text-yellow-500" />
          </div>
          <h2 className="whitespace-nowrap text-xl font-bold text-darkGray">
            {reverseSentClaimsReversalType}
          </h2>
        </div>
        <div className="mb-6">
          <p className="leading-8 tracking-widest text-customGray">
            This invoice has already been sent to the Payer. Reversing it may cause balance
            mismatches and duplicate invoice numbers. Please ensure the invoice is also reversed on
            your system.
          </p>
        </div>
        <div className="mt-8 flex justify-end space-x-3">
          <Button variant="outlined" onClick={handleClose} type="button">
            No, Cancel
          </Button>
          <Button onClick={handleContinue} type="button">
            Yes, Continue
          </Button>
        </div>
      </div>
    </DialogWrapper>
  );
}
