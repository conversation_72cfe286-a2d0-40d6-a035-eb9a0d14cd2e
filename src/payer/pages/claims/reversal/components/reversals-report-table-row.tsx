import React from "react";
import UserService from "../../../../services/UserService";
import InvoiceLineItemsTableRow from "./invoice-line-items-table-row";
import { formatDateSlash, formatValue } from "../../../../lib/Utils";
import Button from "../../../../components/ui/Button";
import { InvoiceData } from "../../../../lib/types/claims/invoice";
import TableHeaderItem from "../../../../components/ui/table/TableHeaderItem";
import { useAppDispatch } from "../../../../store/hooks";
import { ChevronDownIcon, ChevronUpIcon } from "@heroicons/react/24/outline";
import { Empty } from "~lib/components";
import {
  InvoiceReversalType,
  setIsFullReversalModalOpen,
  setIsPartialReversalModalOpen,
  setIsReverseSentClaimsWarningModalOpen,
  setReverseSentClaimsReversalType,
  setSelectedInvoice,
} from "../../../../features/claims/claimsReversalSlice";
import ReversalsReportTableDataItem from "./reversals-report-table-data-item";
import { getClaimsReversalTextAndColorFromStatus } from "../../../../utils/utils";
import Badge from "../../../../components/ui/Badge";

interface ReversalsReportTableRowProps {
  invoice: InvoiceData;
  colspan: number;
  isInvoiceDetailsOpen: boolean;
  onToggle: () => void;
}

export default function ReversalsReportTableRow({
  invoice,
  colspan,
  isInvoiceDetailsOpen,
  onToggle,
}: ReversalsReportTableRowProps) {
  const username = UserService.getUsername();
  const payerId = UserService.getPayer().tokenParsed?.["payerId"];
  const dispatch = useAppDispatch();

  const initiateReversal = (type: InvoiceReversalType) => {
    dispatch(setSelectedInvoice(invoice));
    dispatch(setReverseSentClaimsReversalType(type));

    if (invoice.payerStatus === "SENT") {
      dispatch(setIsReverseSentClaimsWarningModalOpen(true));
    } else {
      if (type === InvoiceReversalType.FULL) {
        dispatch(setIsFullReversalModalOpen(true));
      } else {
        dispatch(setIsPartialReversalModalOpen(true));
      }
    }
  };

  const handleFullReversal = () => {
    initiateReversal(InvoiceReversalType.FULL);
  };

  const handlePartialReversal = () => {
    initiateReversal(InvoiceReversalType.PARTIAL);
  };

  const invoiceLinesTableHeaderItems: Array<string> = [
    "Invoice Line ID",
    "Invoice Number",
    "Description",
    "Line Type",
    "Line Category",
    "Quantity",
    "Unit Price",
    "Line Total",
  ];

  const [text, color] = getClaimsReversalTextAndColorFromStatus(invoice.status);

  const shouldDisplayReversalButtons =
    invoice.status !== "REJECTED" && invoice.status !== "BALANCE_ADDED";

  return (
    <>
      <tr className="cursor-pointer" key={invoice.visitNumber}>
        <ReversalsReportTableDataItem
          title={invoice.visitNumber}
          className="text-xs"
          isInvoiceDetailsOpen={isInvoiceDetailsOpen}
        >
          {invoice.visitNumber}
        </ReversalsReportTableDataItem>
        <ReversalsReportTableDataItem
          className="text-xs"
          isInvoiceDetailsOpen={isInvoiceDetailsOpen}
        >
          {formatDateSlash(invoice.createdAt)}
        </ReversalsReportTableDataItem>
        <ReversalsReportTableDataItem
          className="text-xs"
          title={invoice.memberNumber}
          isInvoiceDetailsOpen={isInvoiceDetailsOpen}
        >
          {invoice.memberNumber}
        </ReversalsReportTableDataItem>
        <ReversalsReportTableDataItem
          className="text-xs"
          title={invoice.memberName}
          isInvoiceDetailsOpen={isInvoiceDetailsOpen}
        >
          {invoice.memberName}
        </ReversalsReportTableDataItem>
        <ReversalsReportTableDataItem
          className="text-xs"
          title={invoice.benefitName}
          isInvoiceDetailsOpen={isInvoiceDetailsOpen}
        >
          {invoice.benefitName}
        </ReversalsReportTableDataItem>
        <ReversalsReportTableDataItem
          className="text-xs"
          title={invoice.invoiceNumber}
          isInvoiceDetailsOpen={isInvoiceDetailsOpen}
        >
          {invoice.invoiceNumber}
        </ReversalsReportTableDataItem>
        <ReversalsReportTableDataItem
          className="text-xs"
          title={invoice.status}
          isInvoiceDetailsOpen={isInvoiceDetailsOpen}
        >
          <Badge color={color} text={text} textClassName="text-xs whitespace-nowrap" />
        </ReversalsReportTableDataItem>
        <ReversalsReportTableDataItem
          className="text-xs"
          isInvoiceDetailsOpen={isInvoiceDetailsOpen}
        >
          {formatValue(invoice.totalAmount)}
        </ReversalsReportTableDataItem>
        <ReversalsReportTableDataItem className="" isInvoiceDetailsOpen={isInvoiceDetailsOpen}>
          <div className="flex flex-col space-y-2">
            {username.includes("gilbert.matui") && payerId == 1 ? (
              ""
            ) : (
              <button
                title="View"
                className={`ml-2 flex transform rounded-md bg-transparent p-2 text-blue-700 shadow-sm`}
                onClick={onToggle}
              >
                {isInvoiceDetailsOpen ? (
                  <ChevronUpIcon className="h-6 w-6" />
                ) : (
                  <ChevronDownIcon className="h-6 w-6" />
                )}
              </button>
            )}
          </div>
        </ReversalsReportTableDataItem>
      </tr>
      {isInvoiceDetailsOpen && (
        <tr className={`w-full`}>
          <ReversalsReportTableDataItem colspan={colspan}>
            <div className="flex flex-col space-y-2">
              <section className="flex items-center space-x-4 self-end">
                {shouldDisplayReversalButtons && (
                  <>
                    <Button
                      onClick={handleFullReversal}
                      className="transform transition-all duration-200 hover:scale-105 hover:bg-blue-700 active:scale-100"
                    >
                      Full Reversal
                    </Button>
                    <Button
                      onClick={handlePartialReversal}
                      className="transform transition-all duration-200 hover:scale-105 hover:bg-blue-700 active:scale-100"
                    >
                      Partial Reversal
                    </Button>
                  </>
                )}
              </section>
              {invoice.invoiceLines.length > 0 ? (
                <section>
                  <h3 className="flex">Invoice Lines</h3>
                  <div className="flex">
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead>
                          <tr className="bg-gray-50">
                            {invoiceLinesTableHeaderItems.map(
                              (invoiceLinesTableHeaderItem, index) => (
                                <TableHeaderItem
                                  item={invoiceLinesTableHeaderItem}
                                  key={index}
                                  className="text-xs"
                                />
                              ),
                            )}
                          </tr>
                        </thead>
                        <tbody>
                          {invoice.invoiceLines.map((invoiceLine) => (
                            <InvoiceLineItemsTableRow
                              invoiceLine={invoiceLine}
                              key={invoiceLine.id}
                            />
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </section>
              ) : (
                <div className="flex h-[200px] w-full items-center justify-center">
                  <Empty message="Invoice has no invoice lines!" />
                </div>
              )}
            </div>
          </ReversalsReportTableDataItem>
        </tr>
      )}
    </>
  );
}
