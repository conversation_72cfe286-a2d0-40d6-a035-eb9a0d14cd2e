import React, { ChangeEvent, useState } from "react";
import UserService from "../../../services/UserService";
import { TailSpin } from "react-loader-spinner";
import ClaimsReversalInvoicesTable from "./components/claims-reversal-invoices-table";
import MainWrapper from "../../../components/ui/MainWrapper";
import Text from "../../../components/ui/typography/Text";
import { MagnifyingGlassIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { claimsApi, useLazyGetAllClaimsQuery } from "../../../api/features/claimsApi";
import { useDispatch } from "react-redux";
import FullClaimsReversalModal from "./components/full-claims-reversal-modal";
import PartialClaimsReversalModal from "./components/partial-claims-reversal-modal";
import { useAppSelector } from "../../../store/hooks";
import {
  setIsFullReversalModalOpen,
  setIsPartialReversalModalOpen,
  setSelectedInvoice,
} from "../../../features/claims/claimsReversalSlice";
import ReverseSentClaimsWarningModal from "./components/reverse-sent-claims-warning-modal";

enum QuerySelection {
  SEARCH_CRITERIA = "Search Criteria",
  INVOICE_NUMBER = "Invoice Number",
  MEMBER_NUMBER = "Member Number",
}

export default function Reversal() {
  const [memberNumber, setMemberNumber] = useState("");
  const [invoiceNumber, setInvoiceNumber] = useState("");
  const [selectedCriteria, setSelectedCriteria] = useState<QuerySelection | "">("");
  const [hasActiveSearch, setHasActiveSearch] = useState(false);

  const payerId = UserService.getPayer().tokenParsed?.["payerId"];
  const dispatch = useDispatch();

  const [
    searchClaim,
    { data: searchResults, isLoading: isLoadingInvoices, isSuccess: isSearchResultsLoaded, error },
  ] = useLazyGetAllClaimsQuery();

  const initiateSearch = async (query: string) => {
    if (query) {
      setHasActiveSearch(false);
      dispatch(claimsApi.util.resetApiState());
      await new Promise((resolve) => setTimeout(resolve, 0));
      setHasActiveSearch(true);
      searchClaim({ payerId, query });
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      const query = invoiceNumber || memberNumber;
      initiateSearch(query);
    }
  };

  const handleSearch = () => {
    const query = invoiceNumber || memberNumber;
    initiateSearch(query);
  };

  const handleClearCriteria = () => {
    setSelectedCriteria("");
    setInvoiceNumber("");
    setMemberNumber("");
    setHasActiveSearch(false);
    dispatch(claimsApi.util.resetApiState());
  };

  const handleCriteriaChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value as QuerySelection | "";
    setSelectedCriteria(value);
    setInvoiceNumber("");
    setMemberNumber("");
    setHasActiveSearch(false);
    dispatch(setSelectedInvoice(null));
  };

  const invoices = searchResults?.data.content || [];
  const criteria: Array<QuerySelection> = [
    QuerySelection.SEARCH_CRITERIA,
    QuerySelection.INVOICE_NUMBER,
    QuerySelection.MEMBER_NUMBER,
  ];
  const validCriteriaSelected =
    selectedCriteria === QuerySelection.INVOICE_NUMBER ||
    selectedCriteria === QuerySelection.MEMBER_NUMBER;

  const showLoadingSpinner = isLoadingInvoices && hasActiveSearch;
  const showSearchResults =
    hasActiveSearch && isSearchResultsLoaded && !isLoadingInvoices && !error;
  const showError = hasActiveSearch && error && !isLoadingInvoices;

  // modals state
  const isFullClaimsReversalModalOpen = useAppSelector(
    (state) => state.claimsReversal.isFullReversalModalOpen,
  );
  const isPartialClaimsReversalModalOpen = useAppSelector(
    (state) => state.claimsReversal.isPartialReversalModalOpen,
  );

  const handleFullClaimsReversalModalClose = () => {
    dispatch(setIsFullReversalModalOpen(false));
    dispatch(setSelectedInvoice(null));
  };
  const handlePartialClaimsReversalModalClose = () => {
    dispatch(setIsPartialReversalModalOpen(false));
    dispatch(setSelectedInvoice(null));
  };

  const invoice = useAppSelector((state) => state.claimsReversal.selectedInvoice);

  return (
    <MainWrapper>
      <main className="flex flex-col space-y-3 overflow-y-auto overflow-x-hidden p-1">
        <div className="">
          <Text variant="heading">Reversal(s)</Text>
          <Text variant="description">
            Search Claim(s) By Invoice Number or Member Number to Proceed
          </Text>
        </div>

        <div className="flex items-center space-x-4">
          <select
            onChange={(e) => handleCriteriaChange(e)}
            value={selectedCriteria}
            className="w-fit min-w-[200px] rounded-md border border-gray-300 p-2 text-sm leading-5 text-gray-500"
          >
            {criteria.map((criterion, index) => (
              <option
                key={index}
                className={"w-full cursor-pointer text-sm text-gray-600 hover:bg-gray-50"}
                value={criterion}
              >
                {criterion}
              </option>
            ))}
          </select>

          {selectedCriteria === "Invoice Number" && (
            <input
              id="form-provider"
              className="flex w-fit min-w-44 items-center justify-between rounded-md border border-gray-300 py-2 pl-4 pr-2 text-sm leading-5 text-gray-500"
              type="search"
              placeholder="Enter Invoice Number... "
              value={invoiceNumber}
              onChange={(e) => setInvoiceNumber(e.target.value)}
              onKeyDown={handleKeyPress}
              autoComplete="off"
              required
            />
          )}

          {selectedCriteria === "Member Number" && (
            <input
              id="form-provider"
              className="flex w-fit min-w-[200px] items-center justify-between rounded-md border border-gray-300 py-2 pl-4 pr-2 text-sm leading-5 text-gray-500"
              type="search"
              placeholder="Enter Member Number... "
              value={memberNumber}
              onChange={(e) => setMemberNumber(e.target.value)}
              autoComplete="off"
              required
            />
          )}

          <div className={`${validCriteriaSelected ? "block" : "hidden"}`}>
            <div className="ite flex items-center space-x-3">
              <button
                title="Search"
                className="cursor-pointer rounded border border-gray-200 bg-btnBlue px-3 py-1.5 text-white shadow-none disabled:bg-lightGray"
                onClick={handleSearch}
              >
                <MagnifyingGlassIcon className="h-6 w-6" />
              </button>
              <button
                className="cursor-pointer rounded border border-gray-200 px-3 py-1.5 text-blue-500 shadow-none"
                title="Clear"
                onClick={handleClearCriteria}
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
          </div>
        </div>

        {showLoadingSpinner && (
          <div className="flex h-[400px] w-full items-center justify-center">
            <TailSpin height="30" width="50" color="#2193FF" ariaLabel="loading" />
          </div>
        )}
        {showSearchResults && (
          <div className="pt-4">
            <ClaimsReversalInvoicesTable invoices={invoices} />
          </div>
        )}
        {showError && (
          <div className="mt-4 flex h-[200px] w-full items-center justify-center rounded border border-gray-200">
            <p className="text-red-500">
              Sorry, we couldn't retrieve claims. Please try again or contact support if the issue
              persists.
            </p>
          </div>
        )}
      </main>
      {/*modals*/}
      <div>
        <FullClaimsReversalModal
          invoice={invoice}
          isFullClaimsReversalModalOpen={isFullClaimsReversalModalOpen}
          onClose={() => handleFullClaimsReversalModalClose()}
        />
        <PartialClaimsReversalModal
          invoice={invoice}
          isPartialClaimsReversalModalOpen={isPartialClaimsReversalModalOpen}
          onClose={() => handlePartialClaimsReversalModalClose()}
        />
        <ReverseSentClaimsWarningModal />
      </div>
    </MainWrapper>
  );
}
