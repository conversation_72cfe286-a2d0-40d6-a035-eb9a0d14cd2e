import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';
import { useUpdateInvoiceMutation } from '../../../api/services';
import LoadingIcon from '~lib/components/icons/LoadingIcon';
import UserService from '../../../services/UserService';

const EditInvoice = ({ invoice, onClose,onInvoiceChange }) => {
    const [invoiceNumber, setInvoiceNumber] = useState(invoice.invoiceNumber);
    const [reason, setReason] = useState('');
    const [loading, setLoading] = useState(false);
    const [updateInvoice] = useUpdateInvoiceMutation();
    const[error, setError] = useState("");
    const dispatch = useDispatch();
    const username = UserService?.getUsername();
  
    const handleSubmit = async (e) => {
      e.preventDefault();

      if (!reason) { // Check if reason is provided
        toast.error('Reason is required');
        setError("Reason is required");
        return;
      }
      if(invoiceNumber===''){
        setError("Invoice Number is Required");
        return;
      }
      setLoading(true);
      try {
        const response= await updateInvoice({ id: invoice.id, invoiceNumber,reason,username }).unwrap();

        if(response.success){
          toast.success('Invoice updated successfully');
          onClose();
          onInvoiceChange();
        }

      } catch (error) {
        setLoading(false);
        toast.error('Failed to update invoice,try a different invoice number');
      }
    };
  
    return (
      <form onSubmit={handleSubmit} className='p-5'>
        <div className="mb-4">
          <p className='text-red-700 text-sm font-normal'>{error}</p>
          <label className="block text-gray-700 text-sm font-bold mb-2">
            Invoice Number
          </label>
          <input
            type="text"
            value={invoiceNumber}
            onChange={(e) => setInvoiceNumber(e.target.value)}
            className=" appearance-none border border-gray-300 rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            placeholder="Enter invoice number"
          
          />
        </div>
        <div className="mb-4">
        <label className="block text-gray-700 text-sm font-bold mb-2">
          Reason
        </label>
        <input
          type="text"
          value={reason}
          onChange={(e) => setReason(e.target.value)}
          className=" appearance-none border border-gray-300 rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          placeholder="Enter reason"
        />
      </div>
        <div className="flex items-center justify-between">
          <button
            type="submit"
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            disabled={loading}
          >
            {loading ? <LoadingIcon /> : 'Update'}
          </button>
        </div>
      </form>
    );
}

export default EditInvoice