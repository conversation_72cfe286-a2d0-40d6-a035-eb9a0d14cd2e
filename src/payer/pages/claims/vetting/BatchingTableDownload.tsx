import React from "react";
import { api_url_claim_report } from "../../../lib/Utils";
import { ArrowDownOnSquareIcon } from "@heroicons/react/24/outline";
import Dropdown from "../../../components/ui/Dropdown";
import { Menu } from "@headlessui/react";

type Props = {
  payerId: number;
  batchId: number;
  userFullNames: string;
};

enum FileType {
  PDF = "PDF",
  XLSX = "XLSX",
}
export default function BatchingTableDownload({ batchId, payerId, userFullNames }: Props) {
  return (
    <div>
      <Menu as="div" className="relative inline-block text-left">
        <Menu.Button>
          <ArrowDownOnSquareIcon className="w-6" />
        </Menu.Button>
        <Menu.Items className="absolute right-0 z-50 mt-1 flex origin-top-right flex-col gap-4 rounded-md border bg-white p-4 shadow-lg">
          {Object.values(FileType).map((fileType) => (
            <Menu.Item key={fileType}>
              <a
                href={`${api_url_claim_report}/batched/export?payerId=${payerId}&batchNumber=${batchId}&page=1&size=10&printedBy=${userFullNames}&fileType=${fileType}`}
                target="__blank"
              >
                {fileType}
              </a>
            </Menu.Item>
          ))}
        </Menu.Items>
      </Menu>
    </div>
  );
}
