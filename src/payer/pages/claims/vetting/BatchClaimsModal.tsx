import React, { FC, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { connect, useDispatch } from "react-redux";
import { batchInvoices, changeCategory, getMemberDetails } from "../../../store/members/actions";
import { Default } from "react-toastify/dist/utils";
import { useBatchInvoicesMutation } from "~lib/api";
import UserService from "../../../services/UserService";
import { formatValue } from "../../../lib/Utils";
import * as notifications from "../../../lib/notifications.js";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { toast } from "react-toastify";

interface Props {
  open: boolean;
  onClose: () => void;
  planId:number;
  providerIds:number;
  startDate:string;
  endDate:string;
  isSelectAll:boolean;
  invoiceIds:any;
  region:string;
  claimSum:any;
  onBatch:()=> void;
}
export const BatchClaimsModal: FC<Props> = (props) => {
  const dispatch = useDispatch();
  const [btnDisabled, setBtnDisabled] = useState(false);
  const [batchAndApprove, setBatchAndApprove] = useState(false);
  const [batchStatus, setBatchStatus] = useState(false);
  const [selectAllBatchInvoices,{data,isLoading,isSuccess:successResponse,isError}] =useBatchInvoicesMutation();
  
  
  const username = UserService?.getUsername();
  
  const handleBatchAndApprove = async (e) => {
    e.preventDefault();
    setBtnDisabled(true);
    const payerId = UserService?.getPayer()?.tokenParsed?.payerId;

    // const payload = {
    //   payerId: payerId,
    //   ...(props?.providerIds && { providerIds: [props?.providerIds] }),
    //   ...(props?.planId && { planIds: [Number(props.planId)] }),
    //   ...(props?.region && { region: props?.region }),
    //   ...(props?.startDate && { vettingStartDate: props?.startDate }),
    //   ...(props?.endDate && { vettingEndDate: props?.endDate }),
    //   vettingStatuses: ["APPROVED", "PARTIAL"],
    //   batchStatus:"NOT_BATCHED",
    //   ...(username && { actionedBy: username }),
    // };
  

    const batchedInvoices = {
      invoiceIds: props?.invoiceIds,
      payerId,
      actionedBy:username,
      region:props?.region,
      totalAmount:props?.claimSum
    };
    await dispatch(batchInvoices(batchedInvoices));
    setBatchAndApprove(true)

    // if(props?.isSelectAll){
    //   try {
    //   await selectAllBatchInvoices(payload).unwrap();
    //   } catch (error) {
    //     toast.error("Something went wrong try again");
    //   }
      
    // }
    // else{
    //   await dispatch(batchInvoices(batchedInvoices));
    // }
    
  };

  const loadingbatchInvoices = useSelector((state: RootState) => state.memberInfo.loadingbatchInvoices);
  const batchResultStatus = useSelector((state: RootState) => state.memberInfo.batchResultStatus);

  useEffect(()=>{
    if(batchResultStatus?.success && batchAndApprove){
      setBtnDisabled(false)
      setBatchAndApprove(false)
      props.onBatch()
      notifications.Success({
        title:`Batch ` + batchResultStatus?.data?.id +  ` created Successfully`,
      })
    }

  },[batchAndApprove,batchResultStatus])

  useEffect(()=>{
    if(isError){
      setBtnDisabled(false);
    }
  },[isError])

  return (
    <div>
      <div className="space-y-4 p-5 px-10 pb-1 pt-4">
        <div className=" grid-col-3 grid gap-4">
          <div className="col-span-1"></div>
          <div className="col-span-1">
            <div className="flex"></div>
            {/* <div>{props.visitIds}</div> */}
            <div className="mt-10 flex">
              <div className="">Total Payable Amount:</div>
              <div className="font-bold">{formatValue(props.claimSum)}</div>
            </div>
          </div>
          <div className="col-span-1"></div>
        </div>
        <div className="mt-10 flex items-center justify-between py-10 pt-10">

          <button
            className="flex rounded-md  bg-blue-500 px-4 py-2 text-white "
            onClick={(e) => handleBatchAndApprove(e)}
            disabled={btnDisabled}
          >
            {btnDisabled ? (
              <>
                <LoadingIcon className="mr-2" /> Batching...
              </>
            ) : (
              "Batch And Approve"
            )}
          </button>

          <div
            className="btn cursor-pointer border border-sky-700 bg-gray-200 px-4 py-2 text-sky-700"
            onClick={props.onClose}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="currentColor"
              className="mr-1 h-6 w-6"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
            Decline
          </div>
        </div>
      </div>
    </div>
  );
};

export default BatchClaimsModal;
