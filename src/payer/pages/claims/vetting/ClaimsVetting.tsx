import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { connect, useSelector } from "react-redux";
import { VisitType } from "~lib/api/types";
import { AsyncSelect, DateInput, Empty, FieldWrapper, Input } from "~lib/components";
import { Form } from "~lib/components/Form";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { PAGE_SIZES } from "~lib/constants";
import { useDebounce } from "~lib/hooks/useDebounce";
import useSearchProviderOptions from "~lib/hooks/useSearchProviderOptions";
import { Option } from "~lib/types";
import { pluralize } from "~lib/utils";
import Pagination3 from "../../../components/Pagination3";
import UserService from "../../../services/UserService";
import { RootState } from "../../../store";
import { useAppDispatch } from "../../../store/hooks";
import {
  getClaimsToVet,
  getPlanByPayerId,
  getProviderBranches,
} from "../../../store/members/actions";
import ClaimsVettingTable from "../../partials/pageItems/ClaimsVettingTable";

interface Inputs {
  view: View;
  mainprovider?: Option<string> | undefined;
  plan?: Option<string> | undefined;
  query?: string;
  startDate?: string;
  endDate?: string;
  vettingStatus?: string;
  branch?: Option<string> | undefined;
}

enum View {
  ACTIVE = "ACTIVE",
  HISTORY = "HISTORY",
}

const viewLabels: Record<View, string> = {
  ACTIVE: "Active",
  HISTORY: "History",
};

interface Props {
  visitType: VisitType;
}

export const ClaimsVetting = ({ getClaimsToVet, getPlanByPayerId, getProviderBranches }) => {
  const formCacheKey = "claimsVettingFormState";
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState<number>(PAGE_SIZES[0]);
  const [planSelected, setPlanSelected] = useState("");
  const [branchSelected, setBranchSelected] = useState("");
  const [vettingStatusSelected, setVettingStatusSelected] = useState("NEW");
  const [vettingStatus, setVettingStatus] = useState("");
  const [query, setQuery] = useState("");
  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const [selectAll, setSelectAll] = useState(false);
  const [isCheck, setIsCheck] = useState([]);
  const [refreshKey, setRefreshKey] = useState(0);
  const [offSetValue, setOffsetValue] = useState(0);
  const [refreshTableKey, setRefreshTableKey] = useState(0);
  const [providerId, setProviderId] = useState(0);
  const [criteriaSelected, setCriteriaSelected] = useState("");
  const [seed, setSeed] = useState(1);

  const dispatch = useAppDispatch();

  const methods = useForm<Inputs>({
    defaultValues: {
      view: View.ACTIVE,
      mainprovider: undefined,
      plan: undefined,
      query: "",
      startDate: "",
      endDate: "",
      vettingStatus: "NEW",
      branch: undefined,
    },
  });

  const { register, setValue, watch, getValues, reset, formState } = methods;

  const { touchedFields } = formState;

  const form = watch();
  const queryDebounced = useDebounce(form.query, 10);

  const { getProviderOptions } = useSearchProviderOptions();

  const NA = <span className="uppercase text-gray-400">N/A</span>;

  const label = "Claim";

  const handlePlanSelected = (e: any) => {
    const truncatedPlan =
      e.target.value.length > 5 ? `${e.target.value.slice(0, 5)}...` : e.target.value;
    setPlanSelected(truncatedPlan);
    setCriteriaSelected("PLAN");
    setValue("plan", e.target.value);
  };

  const handleBranchSelected = (e: any) => {
    setBranchSelected(e.target.value);
  };

  const handleVettingStatusSelected = (e: any) => {
    setVettingStatusSelected(e.target.value);
    setCriteriaSelected("VETTING");
    setValue("vettingStatus", e.target.value);
  };
  const schemes = useSelector((state: RootState) => state.memberInfo.plans);
  const visits = useSelector((state: RootState) => state.memberInfo.claimsToVet?.data?.content);
  const loadingClaimsToVet = useSelector((state: RootState) => state.memberInfo.loadingClaimsToVet);
  const visitsResponse = useSelector((state: RootState) => state.memberInfo.claimsToVet);
  const providerBranches = useSelector((state: RootState) => state.memberInfo.providerBranches);

  console.log("This is visits", visits);

  const pageNumber = visitsResponse?.pageable?.pageNumber;

  useEffect(() => {
    if (!loadingClaimsToVet) {
      if (branchSelected !== "") {
        getClaimsToVet(
          payerId,
          Number(branchSelected),
          query,
          offSetValue,
          planSelected,
          vettingStatusSelected,
          pageSize,
          form?.startDate,
          form?.endDate,
        );
      } else {
        getClaimsToVet(
          payerId,
          Number(form?.mainprovider?.value),
          query,
          offSetValue,
          planSelected,
          vettingStatusSelected,
          pageSize,
          form?.startDate,
          form?.endDate,
        );
      }
    }
  }, [
    offSetValue,
    query,
    form?.mainprovider?.value,
    planSelected,
    branchSelected,
    pageSize,
    form?.startDate,
    form?.endDate,
    vettingStatusSelected,
  ]);

  const handlePaginationChange = (e) => {
    getClaimsToVet(
      payerId,
      Number(form?.mainprovider?.value),
      queryDebounced,
      e,
      planSelected,
      vettingStatusSelected,
      pageSize,
      form?.startDate,
      form?.endDate,
    ).then(() => {
      setRefreshKey((oldKey) => Math.random() * 1000);
    });
  };

  const handleSizeChange = (e) => {
    console.log(e.target.value);
    setPageSize(e.target.value);
    getClaimsToVet(
      payerId,
      Number(form?.mainprovider?.value),
      queryDebounced,
      offSetValue,
      planSelected,
      vettingStatusSelected,
      e.target.value,
    ).then(() => {
      setRefreshKey((oldKey) => Math.random() * 1000);
    });
  };
  useEffect(() => {
    getPlanByPayerId(payerId);
  }, []);

  //Disable this reset for testing

  // const reset = () => {
  //   setSeed(Math.random());
  // };

  //End reset

  // useEffect(() => {
  //   setQuery("");

  //   if (!loadingClaimsToVet) {
  //     if(branchSelected !== ""){
  //       getClaimsToVet(
  //         payerId,
  //         Number(branchSelected),
  //         "",
  //         offSetValue,
  //         planSelected,
  //         pageSize,
  //       );
  //     }else{
  //       getClaimsToVet(
  //         payerId,
  //         Number(form?.provider?.value),
  //         "",
  //         offSetValue,
  //         planSelected,
  //         pageSize,
  //       );
  //     }
  //   }
  // }, [form?.provider?.value,branchSelected, planSelected]);

  // useEffect(() => {
  //   setQuery("");

  //   if (!loadingClaimsToVet) {
  //     if(branchSelected !== ""){
  //       getClaimsToVet(
  //         payerId,
  //         Number(branchSelected),
  //         "",
  //         offSetValue,
  //         planSelected,
  //         pageSize,
  //       );
  //     }else{
  //       getClaimsToVet(
  //         payerId,
  //         Number(form?.provider?.value),
  //         "",
  //         offSetValue,
  //         planSelected,
  //         pageSize,
  //       );
  //     }
  //   }
  // }, [form?.provider?.value,branchSelected, planSelected]);

  //Adding Cache

  // Function to save form state to localStorage
  const saveFormToLocalStorage = () => {
    localStorage.setItem(formCacheKey, JSON.stringify(getValues()));
  };

  // Function to load form state from localStorage
  // const loadFormFromLocalStorage = () => {
  //   const cachedFormData = localStorage.getItem(formCacheKey);
  //   if (cachedFormData) {
  //     const parsedFormData = JSON.parse(cachedFormData);
  //     Object.entries(parsedFormData).forEach(([key, value]) => {
  //       setValue(key, value);
  //     });
  //   }
  // };

  // Load form state from localStorage when component mounts
  // useEffect(() => {
  //   loadFormFromLocalStorage();
  // }, []);

  // Save form state to localStorage whenever it changes
  useEffect(() => {
    saveFormToLocalStorage();
  }, [form]);

  //End Cache

  useEffect(() => {
    setQuery("");
    if (form?.mainprovider?.value !== undefined) {
      setCriteriaSelected("MAINPROVIDER");
      getProviderBranches(form?.mainprovider?.value);
    }
    if (form?.startDate !== "") {
      setCriteriaSelected("STARTDATE");
    }
    if (form?.endDate !== "") {
      setCriteriaSelected("ENDDATE");
    }
  }, [form?.mainprovider?.value, form?.startDate, form?.endDate]);

  // useEffect(() => {
  //   setQuery("");
  //   if (branchSelected !== "") {
  //     getClaimsToVet(
  //       payerId,
  //       Number(branchSelected),
  //       "",
  //       offSetValue,
  //       planSelected,
  //       vettingStatusSelected,
  //       pageSize,
  //       form?.startDate,
  //       form?.endDate,
  //     ).then(() => {
  //       setRefreshKey((oldKey) => Math.random() * 1000);
  //     });
  //   } else {
  //     getClaimsToVet(
  //       payerId,
  //       form?.mainprovider?.value,
  //       "",
  //       offSetValue,
  //       planSelected,
  //       vettingStatusSelected,
  //       pageSize,
  //       form?.startDate,
  //       form?.endDate,
  //     ).then(() => {
  //       setRefreshKey((oldKey) => Math.random() * 1000);
  //     });
  //   }
  // }, [vettingStatusSelected]);

  const handleClearCriteria = (e) => {
    reset();
  };

  const selectStyle = {
    overflow: "hidden",
    whiteSpace: "nowrap",
    textOverflow: "ellipsis",
    fontSize: "12px",
  };

  // Function to reset form and remove localStorage data
  const resetForm = () => {
    reset({
      view: View.ACTIVE,
      mainprovider: undefined,
      plan: undefined,
      query: "",
      startDate: "",
      endDate: "",
      vettingStatus: "NEW",
      branch: undefined,
    });
    setPlanSelected("");
    setBranchSelected("");
    setVettingStatusSelected("");
    setQuery("");
    setSeed(Math.random());
    localStorage.removeItem(formCacheKey);
  };

  return (
    <div className="flex h-full flex-grow overflow-hidden bg-gray-50">
      <div className="flex flex-1 flex-col overflow-y-auto overflow-x-hidden">
        <main>
          <div className="w-full pb-8 text-gray-600">
            <div className="mx-1 mt-1 max-w-full rounded-md bg-white shadow-md">
              <div className="z-10 bg-gray-50 px-8 py-4" key={seed}>
                <Form
                  className="pt-2 text-sm"
                  methods={methods}
                  onSubmit={(_data, e) => {
                    e?.preventDefault();
                  }}
                >
                  <div className="flex items-center justify-between pb-2">
                    <hgroup className="flex items-center gap-2 ">
                      <h2 className="text-lg font-bold font-medium capitalize text-darkBlue">
                        Claims List
                      </h2>
                    </hgroup>
                  </div>
                  <div>
                    <div className="flex grid grid-cols-2 gap-6 ">
                      <div className="col-span-1 "></div>
                      <div className="col-span-1 flex ">
                        <FieldWrapper
                          name="query"
                          label="Search"
                          className="mr-2 w-full flex-grow"
                          labelClassName="font-semibold text-gray-500"
                        >
                          <Input
                            type="search"
                            placeholder="Member number or invoice number or Member Name ..."
                            className="max-w-full text-sm"
                            value={query}
                            onChange={(e) => setQuery(e.target.value)}
                          />
                        </FieldWrapper>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className="flex flex-wrap justify-start gap-2 sm:grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6">
                      <FieldWrapper
                        name="plan"
                        label="Scheme"
                        className="col-span-1 max-w-full flex-shrink flex-grow"
                        labelClassName="font-semibold text-gray-800"
                      >
                        <select
                          // name="plan"
                          id="plan"
                          {...register("plan")}
                          placeholder="Select Scheme"
                          style={selectStyle}
                          onChange={(e) => handlePlanSelected(e)}
                          className="btn mr-6 w-full justify-between border-gray-200 bg-white text-[12px] uppercase leading-[20px] text-gray-400 hover:border-gray-300 hover:text-gray-600"
                        >
                          <option value="" selected disabled hidden>
                            Select Scheme
                          </option>
                          {schemes.map((scheme, index) => (
                            <option
                              className={
                                "mr-6  flex w-full cursor-pointer items-center px-2 text-[13px] font-medium leading-7 text-gray-600 hover:bg-gray-50 "
                              }
                              value={scheme.id}
                              key={index}
                            >
                              {scheme.name}
                            </option>
                          ))}
                        </select>
                      </FieldWrapper>
                      <FieldWrapper
                        name="mainprovider"
                        label="Main Provider"
                        className="col-span-1 max-w-full flex-shrink flex-grow"
                        labelClassName="font-semibold text-gray-800"
                      >
                        <AsyncSelect
                          name="mainprovider"
                          getOptions={getProviderOptions}
                          value={form.mainprovider}
                          onChange={(value) => setValue("mainprovider", value)}
                          placeholder="Search Main Facility..."
                          className="min-w-[167px]"
                        />
                      </FieldWrapper>
                      <FieldWrapper
                        name="branch"
                        label="Branch"
                        className="max-w-full flex-shrink flex-grow"
                        labelClassName="font-semibold text-gray-800"
                      >
                        <select
                          name="branch"
                          id="branch"
                          placeholder=""
                          style={{ fontSize: "12px" }}
                          value={branchSelected}
                          onChange={(e) => handleBranchSelected(e)}
                          className="btn mr-6 w-full justify-between border-gray-200 bg-white text-sm uppercase text-gray-400 hover:border-gray-300 hover:text-gray-600 md:text-[14px] lg:text-[14px]"
                        >
                          <option value="" selected disabled hidden>
                            Select Branch
                          </option>
                          {providerBranches?.map((branch, index) => (
                            <option
                              className={
                                "mr-6 flex w-full cursor-pointer items-center px-3 py-1  text-[12px] font-medium leading-6 text-gray-600 hover:bg-gray-50 md:text-[12px] lg:text-[12px] "
                              }
                              value={branch.providerId}
                              key={index}
                            >
                              {branch.providerName}
                            </option>
                          ))}
                        </select>
                      </FieldWrapper>
                      <FieldWrapper
                        name="vettingStatus"
                        label="Vetting Status"
                        className="max-w-full flex-shrink flex-grow"
                        labelClassName="font-semibold text-gray-800"
                      >
                        <select
                          {...register("vettingStatus")}
                          name="vettingStatus"
                          id="vettingStatus"
                          value={vettingStatusSelected}
                          style={{ fontSize: "12px" }}
                          placeholder=""
                          onChange={(e) => handleVettingStatusSelected(e)}
                          className="btn md:tex-[12px] mr-6 w-full justify-between border-gray-200 bg-white text-[12px] uppercase text-gray-400 hover:border-gray-300 hover:text-gray-600 lg:text-[12px]"
                        >
                          <option value="" selected disabled hidden>
                            Select Status
                          </option>
                          <option
                            className={
                              "mr-6 flex w-full cursor-pointer items-center px-3 py-1  text-[14px] font-medium text-gray-600 hover:bg-gray-50 md:text-[14px] lg:text-[14px] "
                            }
                            value="NEW"
                          >
                            NEW
                          </option>
                          <option
                            className={
                              "mr-6 flex w-full cursor-pointer items-center px-3 py-1  text-[14px] font-medium text-gray-500 hover:bg-gray-50 md:text-[14px] lg:text-[14px] "
                            }
                            value="VETTED"
                          >
                            VETTED
                          </option>
                          <option
                            className={
                              "mr-6 flex w-full cursor-pointer items-center px-3 py-1  text-[14px] font-medium text-gray-500 hover:bg-gray-50 md:text-[14px] lg:text-[14px] "
                            }
                            value="INCOMPLETE"
                          >
                            INCOMPLETE
                          </option>
                        </select>
                      </FieldWrapper>
                      {/* Start date */}

                      <FieldWrapper
                        name="startDate"
                        className={`max-w-xs flex-shrink flex-grow`}
                        label="Start Date"
                        labelClassName="font-semibold text-gray-800"
                      >
                        <DateInput
                          placeholder={new Date().toISOString().split("T")[0]}
                          maxDate="today"
                          className="w-auto text-sm"
                          selected={form.startDate ? new Date(form.startDate) : null}
                          onChange={(date) =>
                            setValue("startDate", date ? date.toISOString().substring(0, 10) : "")
                          }
                        />
                      </FieldWrapper>

                      <FieldWrapper
                        name="endDate"
                        className={`max-w-xs flex-shrink flex-grow`}
                        label="End Date"
                        labelClassName="font-semibold text-gray-800"
                      >
                        <DateInput
                          placeholder={new Date().toISOString().split("T")[0]}
                          maxDate="today"
                          className="w-auto text-sm"
                          selected={form.endDate ? new Date(form.endDate) : null}
                          onChange={(date) =>
                            setValue("endDate", date ? date.toISOString().substring(0, 10) : "")
                          }
                        />
                      </FieldWrapper>

                      {/* End Date */}
                      <FieldWrapper
                        name="resetFields"
                        label="  "
                        className="max-w-full flex-shrink flex-grow"
                        labelClassName="font-semibold text-gray-800"
                      >
                        <div
                          className="mt-5"
                          style={{
                            display:
                              criteriaSelected != "" && criteriaSelected !== "Search"
                                ? "block"
                                : "none",
                          }}
                        >
                          <button
                            className=" btn bg-blue-700 text-white"
                            title="Clear Fields"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleClearCriteria(e);
                              resetForm();
                            }}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-6 w-6"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M6 18L18 6M6 6l12 12"
                              />
                            </svg>
                            Clear /Reset
                          </button>
                        </div>
                        {/* <div className="mt-5">
                          {Object.keys(touchedFields).length > 0 && (
                            <button
                              className="btn bg-blue-700 text-white"
                              title="Clear Fields"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleClearCriteria(e);
                                resetForm();
                              }}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-6 w-6"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M6 18L18 6M6 6l12 12"
                                />
                              </svg>
                              Clear / Reset
                            </button>
                          )}
                        </div> */}
                      </FieldWrapper>
                    </div>
                  </div>
                </Form>
              </div>

              <div className="mb-4">
                {loadingClaimsToVet ? (
                  <div className="flex items-center justify-center py-8">
                    <p className="text-blue-700">Loading...</p>
                    <LoadingIcon className="h-6 w-6 text-blue-400" />
                  </div>
                ) : !visits?.length ? (
                  <Empty message={`No ${pluralize(0, label)} found`} />
                ) : (
                  <ClaimsVettingTable visits={visits} payerId={payerId} key={refreshKey} />
                )}
              </div>

              {visits && (
                <div className="pb-4">
                  <Pagination3
                    totalElements={visitsResponse?.data.totalElements}
                    totalPages={visitsResponse?.data.totalPages}
                    pageNumber={visitsResponse?.data.pageable.pageNumber}
                    OnPageNumberClick={handlePaginationChange}
                    OnSizeChange={handleSizeChange}
                    pageSize={pageSize}
                  />
                </div>
              )}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};
const mapDispatchToProps = (dispatch) => ({
  getClaimsToVet: (
    payerId,
    providerId,
    query,
    offSetValue,
    planSelected,
    vettingStatusSelected,
    pageSize,
    startDate,
    endDate,
  ) =>
    dispatch(
      getClaimsToVet(
        payerId,
        providerId,
        query,
        offSetValue,
        planSelected,
        vettingStatusSelected,
        pageSize,
        startDate,
        endDate,
      ),
    ),
  getPlanByPayerId: (payerId) => dispatch(getPlanByPayerId(payerId)),
  getProviderBranches: (providerId) => dispatch(getProviderBranches(providerId)),
});
export default connect(null, mapDispatchToProps)(ClaimsVetting);
