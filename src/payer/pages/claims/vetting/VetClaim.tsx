import { XMarkIcon } from "@heroicons/react/24/outline";
import { skipToken } from "@reduxjs/toolkit/query";
import { Fragment, useEffect, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import ReactTooltip from "react-tooltip";
import {
  useBillVisitMutation,
  useGetBeneficiaryProviderBenefitsQuery,
  useGetVisitQuery,
  useSaveLineItemMutation
} from "~lib/api";
import { BillVisitRequest } from "~lib/api/schema";
import {
  DeductibleType,
  VisitStatus,
  normalizedVisitTypeLabels,
  visitStatusLabels,
} from "~lib/api/types";
import { Confirm, DatePicker, Empty, FieldWrapper, Input } from "~lib/components";
import ErrorMessage from "~lib/components/Error";
import FieldSet from "~lib/components/FieldSet";
import { Form } from "~lib/components/Form";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import {
  capitalize,
  clsx,
  formatDateISO,
  formatDateTime,
  formatMoney,
  isErrorWithMessage,
  isFetchBaseQueryError,
  queryError,
  responseError,
  truncate,
  truncateDecimals,
} from "~lib/utils";
import UserService from "../../../services/UserService";
import { BillingInputs, BillingInvoiceInput } from "../types";
import styled from "styled-components";
import {
  getClaimsToVetById,
  getPreAuthToVetById,
  getLineItemsByVisitIdAndInvoiceNumber,
  getInvoicesByVisitId,
  getDocumentsByVisitId,
  vetClaimInvoice,
  DownloadDocument,
  getPlanByPayerId,
} from "../../../store/members/actions";
import { useDocumentDownloadQuery } from "~lib/api";
import { connect, useDispatch, useSelector } from "react-redux";
import { api_url, formatValue } from "../../../lib/Utils";
import * as notifications from "../../../lib/notifications.js";
import { baseUrl } from "~lib/constants";
import ModalSmall from "../../../components/ModalSmall";
import ModalEdit from "../../../components/ModalEdit";
import SupportDocumentsModal from "./SupportDocumentsModal";
import { RootState } from "../../../store";
import moment from "moment-timezone";
import AsyncSelect from "react-select/async";
import EditInvoice from "./EditInvoice";
import { NetworkPrimitive } from "~lib/service-fields";
import useNetworkPrimitiveOptions from "~lib/service-fields/useNetworkPrimitiveOptions";
import { InvoiceStatus } from "~lib/api/types";
import { useGetLineItemsByInvoiceIdQuery } from "~lib/api";

interface Params extends Record<string, string> {
  id: string;
  visitNumber: string;
}
const Tab = styled.button`
  font-size: 20px;
  padding: 5px 10px;
  cursor: pointer;
  opacity: 0.6;
  border: 0;
  outline: 0;
`;
const ButtonGroup = styled.div`
  display: flex;
`;
const types = ["Overview", "Documents", "Pre-authorization", "Invoice Details"];

interface Field {
  label: string;
  value: string;
}

interface Visit {
  id: number;
}

interface LineItem {
  itemDescription: string;
  itemCode: string;
}

const VetClaim = ({
  getClaimsToVetById,
  getPreAuthToVetById,
  getLineItemsByVisitIdAndInvoiceNumber,
  getInvoicesByVisitId,
  getDocumentsByVisitId,
}) => {
  const [showConfirm, setShowConfirm] = useState(false);
  const [active, setActive] = useState(types[0]);
  const [docs, setdocs] = useState([]);
  const [prediagnosisCodesState, setPrediagnosisCodesState] = useState([]);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const params = useParams<Params>();
  const [open, setOpen] = useState(false);
  const [openPreauth, setOpenPreauth] = useState(0);
  const [openInvoice, setOpenInvoice] = useState(0);
  const [refreshOnInvoiceChange, setRefreshOnInvoiceChange]= useState(false);
  const [partialReason, setPartialReason] = useState("");
  const [declineReason, setDeclineReason] = useState("");
  const [downloadDocument, setDownloadDocument] = useState(false);
  const [error, setError] = useState("");
  const [deductibleAmount, setDeductibleAmount] = useState("");
  const [submitClaim, setSubmitClaim] = useState(false);
  const [supportDocumentsModalOpen, setSupportDocumentsModalOpenOpen] = useState(false);
  const [editInvoiceModal, setEditInvoiceModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [refreshKey, setRefreshKey] = useState(0);
  const [selectedDocumentId, setSelectedDocumentId] = useState(null);
  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];


  //modal
  const [showModal, setShowModal]= useState(false);
  const [modalPosition, setModalPosition] = useState({ x: 0, y: 0 });


  const { id: idRaw } = params;

  const id = idRaw ? parseInt(idRaw) : undefined;

  console.log(id);

  function handleBack() {
    navigate(-1);
  }
  const visit = useSelector((state: RootState) => state.memberInfo.claim);
  const preauth = useSelector((state: RootState) => state.memberInfo.preauth);
  useEffect(() => {
    getClaimsToVetById(id);
    getPreAuthToVetById(id);
    getInvoicesByVisitId(id).then(() => {
      setRefreshKey((oldKey) => Math.random * 100000);
    });
    getDocumentsByVisitId(id);
  }, [id]);

  //load invoices
  const loadingInvoices = useSelector((state: RootState) => state.memberInfo.loadingInvoices);
  const vettingInvoices = useSelector((state: RootState) => state.memberInfo.vettingInvoices);
  //vetting action ---approve, decline
  const loadingVetInvoice = useSelector((state: RootState) => state.memberInfo.loadingVetInvoice);
  const vetStatusResponse = useSelector((state: RootState) => state.memberInfo.vetStatusResponse);

  //load Documents
  const loadingDocumentsToVet = useSelector(
    (state: RootState) => state.memberInfo.loadingDocumentsToVet,
  );
  const documents = useSelector((state: RootState) => state.memberInfo.documents);

  const preauthDocs = useSelector(
    (state: RootState) => state.memberInfo.preauth[0]?.supportingDocuments,
  );
  const preDiagnosisCodes = useSelector(
    (state: RootState) => state.memberInfo.preauth[0]?.preDiagnosisCodes,
  );
  const vettingInvoicelineItems = useSelector(
    (state: RootState) => state.memberInfo.vettingInvoicelineItems,
  );

  const downloadedDocurl = useSelector((state: RootState) => state.memberInfo.downloadedDocurl);

  useEffect(() => {
    setdocs([]);
    if (preauthDocs !== undefined) {
      Object.entries(preauthDocs).forEach(([key, value]) => {
        setdocs((docs) => [...docs, value]);
      });
    }
  }, [preauthDocs]);

  const handleSetOpenPreauth = (visitId) => {
    if (visitId === openPreauth) {
      setOpenPreauth(0);
    } else {
      setOpenPreauth(visitId);
    }
  };
  const handleSetOpenInvoice = (invoiceId, invoiceNumber) => {
    console.log("invoiceId",invoiceId);
    if (invoiceId === openInvoice) {
      setOpenInvoice(0);
    } else {
      getLineItemsByVisitIdAndInvoiceNumber(id, invoiceNumber);
      setOpenInvoice(invoiceId);
    }
  };

  const {data:lineItems,isFetching:isLineItemsFetching,isLoading:isLineItemsLoading,isError}=useGetLineItemsByInvoiceIdQuery(openInvoice);
  const fetchedLineItems =lineItems?.data;
  
  const [approval, setApproval] = useState("");

  const onOptionChange = (e) => {
    setApproval(e.target.value);
  };
  const onVettingDeclineReasonChange = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDeclineReason(e.target.value);
  };
  const onPayableAmountChange = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };
  const onPartialReasonChange = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setPartialReason(e.target.value);
  };
  const onPartialDeductibleAmountChange = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDeductibleAmount(e.target.value);
  };

  const handleSubmitVettingStatus = (e) => {
    e.preventDefault();
    setError("");

    if (approval == "") {
      setError("* Select Claim Approval Status ");
    } else if (approval == "approved") {
      //proceed
      const invoiceVetObj = {
        invoiceId: [openInvoice],
        vettingStatus: "APPROVED",
        declineReason: "",
        deductibleAmount: 0,
        partialReason: "",
        actionedBy: UserService?.getUsername(),
      };
      dispatch(vetClaimInvoice(invoiceVetObj));
      setSubmitClaim(true);
    } else if (approval == "decline") {
      if (declineReason == "") {
        setError("* Add a Reason for Decline ");
      } else {
        //proceed
        const invoiceVetObj = {
          invoiceId: [openInvoice],
          vettingStatus: "DECLINED",
          declineReason: declineReason,
          deductibleAmount: 0,
          partialReason: "",
          actionedBy: UserService?.getUsername(),
        };
        dispatch(vetClaimInvoice(invoiceVetObj));
        setSubmitClaim(true);
      }
    } else if (approval == "partial") {
      if (partialReason == "" || deductibleAmount == "") {
        setError("* Add a Reason and deductible amount for Partial Approval ");
      } else {
        //proceed
        const invoiceVetObj = {
          invoiceId: [openInvoice],
          vettingStatus: "PARTIAL",
          declineReason: "",
          deductibleAmount: deductibleAmount,
          partialReason: partialReason,
          actionedBy: UserService?.getUsername(),
        };
        dispatch(vetClaimInvoice(invoiceVetObj));
        setSubmitClaim(true);
      }
    }
  };

  //edit invoice 
  const editInvoice = (invoice) => {
    setSelectedInvoice(invoice);
    setEditInvoiceModal(true);
  };

  const openModal = (invoice) => {
    setSelectedInvoice(invoice);
    setEditInvoiceModal(true);
  };

  const closeModalEdit = () => {
    setEditInvoiceModal(false);
    setSelectedInvoice(null);
  };

  //logics for the add diagnosis and procedure form
  const [saveLineItem] = useSaveLineItemMutation();
  const networkPrimitiveOptions = useNetworkPrimitiveOptions(false);
  const getDiseaseOptions = networkPrimitiveOptions.get(NetworkPrimitive.Disease);
  const getProcedureOptions = networkPrimitiveOptions.get(NetworkPrimitive.Procedure);
  const[showForm, setShowForm] =useState(false);
  const [diagnosisFields, setDiagnosisFields] = useState([""]);
  const [procedureFields, setProcedureFields] = useState([""]);

  const addDiagnosisField = () => {
    setDiagnosisFields([...diagnosisFields, ""]);
  };

  const removeDiagnosisField = (index) => {
    if(index===0){
      return;
    }
    const newFields = [...diagnosisFields];
    newFields.splice(index, 1);
    setDiagnosisFields(newFields);
  };

  const addProcedureField = () => {
    setProcedureFields([...procedureFields, ""]);
  };

  const removeProcedureField = (index) => {
    if(index===0){
      return;
    }
    const newFields = [...procedureFields];
    newFields.splice(index, 1);
    setProcedureFields(newFields);
  };



    // Debounce function to limit the rate of API calls
    const debounce = (func, delay) => {
      let timeoutId;
      return (...args) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          func(...args);
        }, delay);
      };
    };

//Function to load options for diagnosis field
const loadDiagnosisOptions = debounce((inputValue, callback) => {
  if (getDiseaseOptions) {
    getDiseaseOptions(inputValue).then(options => {
      const mappedOptions = options.map((item) => ({
        value: item.value,
        label: item.label,
      }));
      callback(mappedOptions);
    }).catch(error => {
      console.error("Error fetching diagnosis options:", error);
      callback([]);
    });
  } else {
    callback([]);
  }
}, 300); // Adjust the debounce delay as needed

// Function to load options for procedure fields
const loadProcedureOptions = debounce((inputValue, callback) => {
  if (getProcedureOptions) {
    getProcedureOptions(inputValue).then(options => {
      const mappedOptions = options.map((item) => ({
        value: item.value,
        label: item.label,
      }));
      callback(mappedOptions);
    }).catch(error => {
      console.error("Error fetching procedure options:", error);
      callback([]);
    });
  } else {
    callback([]);
  }
}, 300);


  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>): Promise<void> => {
    event.preventDefault();
  
    // Prepare data to be sent
    const diagnosisData: LineItem[] = diagnosisFields.filter(Boolean).map((field) => ({
      itemDescription: field.label,
      itemCode: field.value,
      lineType:"DIAGNOSIS"
    }));
  
    const procedureData: LineItem[] = procedureFields.filter(Boolean).map((field) => ({
      itemDescription: field.label,
      itemCode: field.value,
      lineType:"MEDICALPROCEDURE"
    }));
  
    try {
      // Combine all line items into a single array
      const allLineItems = [...diagnosisData, ...procedureData];

      const payload = {
        visitId: visit.id,
        lineItems: allLineItems,
      };
  
      // Send all line items in a single request
      if (allLineItems.length > 0) {
        const response = await saveLineItem(payload).unwrap();

        //server response 
        if(response.success===true){
          toast.success("Data submitted successfully");
          location.reload();
        }
        else{
          toast.error(response.msg || "Error Submitting Data");
        }
      
      }

     

    } catch (error) {
      console.error('Error submitting data:', error);
      toast.error("Error submitting data");
    }
  };
  
  //End logics for the add diagnosis and procedure form

  useEffect(() => {
    if (vetStatusResponse && submitClaim) {
      setSubmitClaim(false);
      setOpenInvoice(0);
      notifications.Success({
        title: "Invoice Vetted Successfully",
      });
      getInvoicesByVisitId(id).then(() => {
        setRefreshKey((oldKey) => Math.random() * 1000);
      });
    }
  }, [vetStatusResponse, submitClaim, refreshKey]);

  const handleSupportingDocumentUpload = (e) => {
    e.stopPropagation();
    console.log("here");
    setSupportDocumentsModalOpenOpen(true);
  };

  const handleDownloadDocument = (url) => {
    console.log(url);
    setDownloadDocument(true);
    dispatch(DownloadDocument(url));
  };

  //download document logic

  const closeModal = () => {
    setShowModal(false);
  };

  const handleDownloadClick = () => {
    if (selectedDocumentId) {
      const url = `https://api.lctafrica.net/api/v1/visit/documentDownload?id=${selectedDocumentId}`;
      const a = document.createElement('a');
      a.href = url;
      a.download = `document-${selectedDocumentId}.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    } else {
      console.error("Document ID is not available");
    }
    closeModal();
  };

  const handleViewClick = () => {
    if (selectedDocumentId) {
      const url = `https://api.lctafrica.net/api/v1/visit/documentDownload?id=${selectedDocumentId}`;
      window.open(url, '_blank');
    } else {
      console.error("Document ID is not available");
    }
    closeModal();
  };

  const handleIconClick = async (event, documentId) => {
    setSelectedDocumentId(documentId); // Set the document ID directly
    const iconPosition = event.currentTarget.getBoundingClientRect();
    const modalX = iconPosition.left;
    const modalY = iconPosition.bottom + window.scrollY;
    setModalPosition({ x: modalX, y: modalY });
    setShowModal(true);
  };
  //end download doc logic

  useEffect(() => {
    if (downloadedDocurl !== "" && downloadDocument) {
      window.open(downloadedDocurl, "_blank")?.focus();
      setDownloadDocument(false);
    }
  }, [downloadedDocurl, downloadDocument]);

  useEffect(() => {
    dispatch(getPlanByPayerId(payerId));
  }, []);
  

  useEffect(() => {
    if (refreshOnInvoiceChange) {
      getInvoicesByVisitId(id);
      setRefreshOnInvoiceChange(false);
    }
  }, [refreshOnInvoiceChange, id]);

  const handleInvoiceChange = () => {
    setRefreshOnInvoiceChange(true);
  };

  //modal

  return (
    <main className="h-screen flex-1 bg-white">
      <div className="overflow-y-auto overflow-x-hidden text-gray-600">
        <div className="rounded-lg px-12 py-8 shadow">
          <div>
            <div>
              <ButtonGroup className="justify-between">
                <>
                  <button
                    className="rounded-md bg-gray-200 px-1 py-1"
                    onClick={() => {
                      handleBack();
                    }}
                    title="Go Back"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke-width="1.5"
                      stroke="currentColor"
                      className="h-6 w-6 rounded-md bg-gray-200 font-bold text-black"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"
                      />
                    </svg>
                  </button>

                  {types.map((type) => (
                    <Tab
                      key={type}
                      onClick={() => setActive(type)}
                      className={
                        active === type
                          ? "inline-flex justify-between rounded-md bg-blue-100 px-1 py-1 text-center text-xs font-normal text-blue-800"
                          : "inline-flex justify-between rounded-md  px-1  py-1 text-center text-xs font-normal text-gray-950"
                      }
                    >
                      {type}
                    </Tab>
                  ))}
                </>
              </ButtonGroup>

              <p />
              {active === "Overview" ? (
                <>
                  <div className="mt-5">
                    <div className="mx-10 mt-5">
                      <div className="font-bold leading-9 underline">Claim Details</div>
                      <div className="flex grid w-full grid-cols-3 justify-between gap-4">
                        <div className="col-span-1 flex-col">
                          <label htmlFor="claimId" className="leading-7 text-gray-400">
                            Claim Id
                          </label>
                          <div id="claimId" className="leading-7">
                            {visit.id}
                          </div>
                        </div>
                        <div className="col-span-1 flex-col">
                          <label htmlFor="claimId" className="leading-7 text-gray-400">
                            Claim Date
                          </label>
                          <div id="claimId" className="leading-7">
                            {/* {visit.createdAt?.split("T")[0]} */}
                            {visit.createdAt
                              ? new Date(visit.createdAt?.split("T")[0]).toLocaleDateString("en-GB")
                              : ""}
                          </div>
                        </div>
                        <div className="col-span-1 flex-col">
                          <label htmlFor="claimId" className="leading-7 text-gray-400">
                            Claim Age
                          </label>
                          <div id="claimId" className="leading-7">
                            {moment(visit.updatedAt).fromNow(true)}
                          </div>
                        </div>
                        <div className="col-span-1 flex-col">
                          <label htmlFor="claimId" className="leading-7 text-gray-400">
                            Claim Type
                          </label>
                          <div id="claimId" className="leading-7">
                            {visit.visitType}
                          </div>
                        </div>
                        <div className="col-span-1 flex-col">
                          <label htmlFor="claimId" className="leading-7 text-gray-400">
                            Claim Status
                          </label>
                          <div id="claimId" className="leading-7">
                            {visit.status}
                          </div>
                        </div>
                      </div>
                      <div className=" mt-5 font-bold leading-9  underline">Member Details</div>
                      <div className="flex grid w-full grid-cols-3 justify-between gap-4">
                        <div className="col-span-1 flex-col">
                          <label htmlFor="claimId" className="leading-7 text-gray-400">
                            Member Name
                          </label>
                          <div id="claimId" className="leading-7">
                            {visit.memberName}
                          </div>
                        </div>
                        <div className="col-span-1 flex-col">
                          <label htmlFor="claimId" className="leading-7 text-gray-400">
                            Member Number
                          </label>
                          <div id="claimId" className="leading-7">
                            {visit.memberNumber}
                          </div>
                        </div>
                        <div className="col-span-1 flex-col">
                          <label htmlFor="claimId" className="leading-7 text-gray-400">
                            Scheme
                          </label>
                          <div id="claimId" className="leading-7">
                            {visit.schemeName}
                          </div>
                        </div>
                      </div>
                      <div className=" mt-5  font-bold leading-9  underline">Benefit Details</div>
                      <div className="flex grid w-full grid-cols-3 justify-between gap-4">
                        <div className="col-span-1 flex-col">
                          <label htmlFor="claimId" className="leading-7 text-gray-400">
                            Benefit
                          </label>
                          <div id="claimId" className="leading-7">
                            {visit.benefitName}
                          </div>
                        </div>
                      </div>
                      <div className=" mt-5  font-bold leading-9  underline">
                        Diagnosis and Procedure Information
                      </div>
                      {visit?.diagnosis &&
                      visit.diagnosis.length < 1 &&
                      visit?.procedures &&
                      visit?.procedures.length < 1 ? (
                        <div className={`${showForm ? "hidden" : ""}`}>
                          <fieldset className="rounded-lg border border-gray-300 p-4">
                            <legend>Diagnosis & medical Procedure</legend>
                            <div className=" font-['Inter'] text-base font-normal leading-[23px] text-gray-400">
                              Please add diagnosis and medical procedure...
                            </div>
                            <button
                              onClick={() => setShowForm(true)}
                              className={`mt-7 rounded-lg bg-blue-500 px-[18px] py-2 text-white hover:cursor-pointer`}
                            >
                              Add Diagnosis & Medical Procedure
                            </button>
                          </fieldset>
                        </div>
                      ) : (
                        <div className="flex grid w-full grid-cols-2 justify-between gap-4">
                          <div className="col-span-2 flex-col">
                            {visit?.diagnosis && visit?.diagnosis.length > 0 ? (
                              <div>
                                <label htmlFor="claimId" className="leading-7 text-gray-400">
                                  Diagnosis
                                </label>
                                <div id="claimId" className="leading-7">
                                  {visit?.diagnosis?.map((lineItems, index) => (
                                    <div className="flex border border-l-0 border-r-0 border-t-0 border-b-gray-300">
                                      <div className="flex justify-between">
                                        <div>Code:</div>
                                        <div>{lineItems.code}</div>
                                      </div>
                                      <div className="">
                                        <div id="claimId" className="ml-4 leading-7">
                                          <div>Title:</div>
                                          <div>{lineItems.title}</div>
                                        </div>
                                      </div>
                                      {/* <div className="flex justify-between border border-2 border-gray-600">
                                
                              </div> */}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            ) : null}

                            {visit?.procedures && visit?.procedures?.length > 0 ? (
                              <div>
                                <label htmlFor="claimId" className="leading-7 text-gray-400">
                                  Procedures
                                </label>
                                <div id="claimId" className="leading-7">
                                  {visit?.procedures?.map((procedures, index) => (
                                    <div className="flex border border-l-0 border-r-0 border-t-0 border-b-gray-300">
                                      <div className="flex justify-between">
                                        <div>Code:</div>
                                        <div>{procedures.procedure_code}</div>
                                      </div>
                                      <div className="">
                                        <div id="claimId" className="ml-4 leading-7">
                                          <div>Title:</div>
                                          <div>{procedures.procedure_description}</div>
                                        </div>
                                      </div>
                                      {/* <div className="flex justify-between border border-2 border-gray-600">
                                
                              </div> */}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            ) : null}
                          </div>
                        </div>
                      )}

                      {/* Form edit diagnosis and procedure information  */}
                      {showForm ? (
                        <fieldset className="relative mt-3 w-full rounded-lg border border-gray-300 p-8 ">
                          <legend>Diagnosis & Medical Procedure</legend>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth={1.5}
                            stroke="#F87171"
                            className="absolute right-[-10px] top-[-21px] h-6 w-6 cursor-pointer"
                            onClick={() => {
                              setShowForm(false);
                              setDiagnosisFields([""]);
                              setProcedureFields([""]);
                            }}
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                            />
                          </svg>

                          <form onSubmit={handleSubmit}>
                            <div className="flex grid">
                              <div className="grid grid-cols-2">
                                <div>
                                  <label>
                                    Diagnosis<span className="text-red-500">*</span>
                                  </label>
                                  {diagnosisFields.map((field, index) => (
                                    <div key={index} className="mb-3 flex items-center">
                                      <AsyncSelect
                                        cacheOptions
                                        defaultOptions
                                        loadOptions={loadDiagnosisOptions}
                                        onChange={(selectedOption) => {
                                          const newFields = [...diagnosisFields];
                                          newFields[index] = {
                                            label: selectedOption.label, // Set label
                                            value: selectedOption.value, // Set value
                                          };
                                          setDiagnosisFields(newFields);
                                        }}
                                        className="w-3/4"
                                        required
                                      />
                                      <svg
                                        className="ml-4 h-8 w-8 hover:cursor-pointer"
                                        onClick={() => removeDiagnosisField(index)}
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                      >
                                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                        <g
                                          id="SVGRepo_tracerCarrier"
                                          stroke-linecap="round"
                                          stroke-linejoin="round"
                                        ></g>
                                        <g id="SVGRepo_iconCarrier">
                                          {" "}
                                          <path
                                            d="M4 6H20M16 6L15.7294 5.18807C15.4671 4.40125 15.3359 4.00784 15.0927 3.71698C14.8779 3.46013 14.6021 3.26132 14.2905 3.13878C13.9376 3 13.523 3 12.6936 3H11.3064C10.477 3 10.0624 3 9.70951 3.13878C9.39792 3.26132 9.12208 3.46013 8.90729 3.71698C8.66405 4.00784 8.53292 4.40125 8.27064 5.18807L8 6M18 6V16.2C18 17.8802 18 18.7202 17.673 19.362C17.3854 19.9265 16.9265 20.3854 16.362 20.673C15.7202 21 14.8802 21 13.2 21H10.8C9.11984 21 8.27976 21 7.63803 20.673C7.07354 20.3854 6.6146 19.9265 6.32698 19.362C6 18.7202 6 17.8802 6 16.2V6M14 10V17M10 10V17"
                                            stroke="#F87171"
                                            stroke-width="2"
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                          ></path>{" "}
                                        </g>
                                      </svg>
                                    </div>
                                  ))}
                                  <button
                                    className=" rounded-lg bg-blue-500 px-[18px] py-2 text-white"
                                    type="button"
                                    onClick={addDiagnosisField}
                                  >
                                    Add Diagnosis
                                  </button>
                                </div>
                                <div>
                                  <label>
                                    Procedure<span className="text-red-500">*</span>
                                  </label>
                                  {procedureFields.map((field, index) => (
                                    <div key={index} className="mb-3 flex items-center">
                                      <AsyncSelect
                                        cacheOptions
                                        defaultOptions
                                        loadOptions={loadProcedureOptions}
                                        onChange={(selectedOption) => {
                                          const newFields = [...procedureFields];
                                          newFields[index] = {
                                            label: selectedOption.label, // Set label
                                            value: selectedOption.value, // Set value
                                          };
                                          setProcedureFields(newFields);
                                        }}
                                        className="w-3/4"
                                        required
                                      />
                                      <svg
                                        className="ml-4 h-8 w-8 hover:cursor-pointer"
                                        onClick={() => removeProcedureField(index)}
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                      >
                                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                        <g
                                          id="SVGRepo_tracerCarrier"
                                          stroke-linecap="round"
                                          stroke-linejoin="round"
                                        ></g>
                                        <g id="SVGRepo_iconCarrier">
                                          {" "}
                                          <path
                                            d="M4 6H20M16 6L15.7294 5.18807C15.4671 4.40125 15.3359 4.00784 15.0927 3.71698C14.8779 3.46013 14.6021 3.26132 14.2905 3.13878C13.9376 3 13.523 3 12.6936 3H11.3064C10.477 3 10.0624 3 9.70951 3.13878C9.39792 3.26132 9.12208 3.46013 8.90729 3.71698C8.66405 4.00784 8.53292 4.40125 8.27064 5.18807L8 6M18 6V16.2C18 17.8802 18 18.7202 17.673 19.362C17.3854 19.9265 16.9265 20.3854 16.362 20.673C15.7202 21 14.8802 21 13.2 21H10.8C9.11984 21 8.27976 21 7.63803 20.673C7.07354 20.3854 6.6146 19.9265 6.32698 19.362C6 18.7202 6 17.8802 6 16.2V6M14 10V17M10 10V17"
                                            stroke="#F87171"
                                            stroke-width="2"
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                          ></path>{" "}
                                        </g>
                                      </svg>
                                    </div>
                                  ))}
                                  <button
                                    className=" rounded-lg bg-blue-500 px-[18px] py-2 text-white"
                                    type="button"
                                    onClick={addProcedureField}
                                  >
                                    Add Procedure
                                  </button>
                                </div>
                              </div>
                            </div>

                            <button
                              className=" mt-5 rounded-lg bg-blue-500 px-[18px] py-2 text-white"
                              type="submit"
                            >
                              Submit
                            </button>
                          </form>
                        </fieldset>
                      ) : null}

                      {/* End of the Form */}
                    </div>
                  </div>
                </>
              ) : (
                ""
              )}

              {active === "Documents" ? (
                <>
                  <div className="mt-5 flex justify-end px-5">
                    <button
                      className="btn-sm primary bg-blue-700 text-white "
                      onClick={(e) => handleSupportingDocumentUpload(e)}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                        className="h-6 w-6"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"
                        />
                      </svg>
                      Upload Documents
                    </button>
                  </div>
                  <div>
                    <div>
                      {!documents || documents.length < 1 ? (
                        "No Document(s) for this Claim"
                      ) : (
                        <ul className="mt-5">
                          <li className="  mt-2 rounded-md py-5 md:ml-16 md:mr-32">
                            <div className="flex grid grid-cols-1 justify-between  gap-6 px-5">
                              <div className="col-span-1 flex">
                                <div className="grid w-full flex-col place-items-center px-10">
                                  {!documents
                                    ? ""
                                    : documents.map((doc, index) => (
                                        <>
                                          <li className=" mb-5 flex w-full justify-between rounded-md border border-gray-200 px-5 py-5 text-center">
                                            <svg
                                              xmlns="http://www.w3.org/2000/svg"
                                              fill="currentColor"
                                              fill-rule="evenodd"
                                              clip-rule="evenodd"
                                              viewBox="0 0 512 512"
                                              className="col-span-1 h-10 w-10 text-red-500"
                                            >
                                              <path d="M118.5-.5h217a23232.886 23232.886 0 0 1 128.5 128c.667 112 .667 224 0 336-4.339 25.835-19.172 41.835-44.5 48h-301c-22.914-5.727-37.414-20.06-43.5-43a266.363 266.363 0 0 1-1.5-35c-12.018.749-20.518-4.251-25.5-15a1426.759 1426.759 0 0 1-1.5-74.5 1426.54 1426.54 0 0 1 1.5-74.5c4.895-11.034 13.395-15.701 25.5-14a7795.06 7795.06 0 0 1 1.5-213c6.13-22.95 20.63-37.284 43.5-43Zm6 34h197c-.167 25.336 0 50.669.5 76 3.624 18.624 14.791 29.457 33.5 32.5 24.664.5 49.331.667 74 .5-.317 102.507.017 205.007 1 307.5.693 8.915-1.973 16.581-8 23-2.626 2.146-5.626 3.479-9 4-96.333.667-192.667.667-289 0-6.324-1.995-11.157-5.828-14.5-11.5a189.33 189.33 0 0 1-1.5-32c89.364.485 178.697-.015 268-1.5 7.258-2.924 12.092-8.091 14.5-15.5.667-48.333.667-96.667 0-145-2.5-7.833-7.667-13-15.5-15.5a35626.8 35626.8 0 0 0-267-.5c-.167-68.667 0-137.334.5-206 2.685-7.85 7.852-13.184 15.5-16Zm-11 267c10.339-.166 20.672 0 31 .5 21.552 4.932 29.386 18.098 23.5 39.5-4.398 9.786-11.898 15.62-22.5 17.5a93.752 93.752 0 0 1-21 1.5v33h-19v-91c2.885.306 5.552-.027 8-1Zm80 0c11.005-.167 22.005 0 33 .5 28.373 4.891 41.207 21.725 38.5 50.5-3.7 24.364-17.866 37.864-42.5 40.5a158.905 158.905 0 0 1-38-.5v-90c3.213.31 6.213-.023 9-1Zm87 0h55v16h-35v22h33v16h-33v38h-20v-92Z"></path>
                                              <path d="M130.5 314.5c17.387-.194 23.22 7.806 17.5 24-6.561 5.677-14.061 7.344-22.5 5a196.869 196.869 0 0 1-1-28c2.235.295 4.235-.039 6-1Z"></path>
                                              <path d="M204.5 315.5c6.342-.166 12.675 0 19 .5 14.291 4.207 21.291 13.874 21 29-1.398 25.214-14.731 36.047-40 32.5v-62Z"></path>
                                            </svg>

                                            {/* <button onClick={()=>handleDownloadDocument(doc?.fileUrl)}>Documents</button> */}

                                            <div
                                              className="col-span-6 hover:underline"
                                              onClick={() => handleDownloadDocument(doc?.fileUrl)}
                                            >
                                              {doc?.fileUrl?.includes("other")
                                                ? `  Other Document`
                                                : doc?.fileUrl?.includes("claim")
                                                  ? `Claim Document`
                                                  : doc?.fileUrl?.includes("invoice")
                                                    ? `  Invoice Document`
                                                    : "Report Document"}
                                            </div>
                                            <div className="col-span-9"></div>
                                            <div
                                              className="col-span-1 cursor-pointer"
                                              onClick={(event) => handleIconClick(event, doc.id)}
                                            >
                                              <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                                className="h-6 w-6"
                                              >
                                                <path
                                                  stroke-linecap="round"
                                                  stroke-linejoin="round"
                                                  d="M6.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM12.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM18.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"
                                                />
                                              </svg>
                                            </div>
                                          </li>
                                        </>
                                      ))}
                                </div>
                              </div>
                            </div>
                          </li>
                        </ul>
                      )}
                    </div>
                  </div>
                </>
              ) : (
                ""
              )}

              {/* Start of modal */}
              {/* Modal */}
              {showModal && (
                <>
                  <div
                    className="fixed inset-0 flex items-center justify-center"
                    onClick={() => setShowModal(false)} // Close modal on click outside
                  >
                    <div
                      className="absolute z-10 rounded border bg-white p-4 shadow-lg"
                      style={{ top: modalPosition.y, left: modalPosition.x }}
                      onClick={(e) => e.stopPropagation()} // Prevent closing modal when clicking inside
                    >
                      <ul className="space-y-2">
                        <li
                          className="cursor-pointer p-2 hover:bg-gray-100"
                          onClick={handleDownloadClick}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth={1.5}
                            stroke="currentColor"
                            className="mr-3 inline h-6 w-6"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M12 9.75v6.75m0 0-3-3m3 3 3-3m-8.25 6a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18 19.5H6.75Z"
                            />
                          </svg>
                          Download Document
                        </li>
                        <li
                          className="cursor-pointer p-2 hover:bg-gray-100"
                          onClick={handleViewClick}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth={1.5}
                            stroke="currentColor"
                            className="mr-3 inline h-6 w-6"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"
                            />
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                            />
                          </svg>
                          View Document
                        </li>
                        <li
                          className="cursor-pointer p-2 hover:bg-gray-100"
                          onClick={() => console.log("Delete")}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth={1.5}
                            stroke="currentColor"
                            className="mr-3 inline h-6 w-6"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                            />
                          </svg>
                          Delete Document
                        </li>
                      </ul>
                    </div>
                  </div>
                </>
              )}

              {/* end of modal */}

              {active === "Pre-authorization" ? (
                <>
                  <div className="mt-5 w-full pb-8 text-gray-600">
                    <div className="max-w-full  rounded-md bg-white">
                      <div className="mb-4">
                        <div className="overflow-x-auto bg-white text-gray-600">
                          {!preauth || preauth.length < 1 ? (
                            " No Pre-authorization(s) for this claim"
                          ) : (
                            <table className="w-full">
                              <thead className="text-left">
                                <tr className="bg-gray-100">
                                  <th className="px-2 py-2 font-normal"></th>
                                  <th className="px-2 py-2 font-normal uppercase first:pl-8 last:pr-8">
                                    Visit Number
                                  </th>
                                  <th className="px-2 py-2 font-normal uppercase first:pl-8 last:pr-8">
                                    Member Details
                                  </th>
                                  <th className="px-2 py-2 font-normal uppercase first:pl-8 last:pr-8">
                                    Scheme
                                  </th>
                                  <th className="px-2 py-2 font-normal uppercase first:pl-8 last:pr-8">
                                    Benefit
                                  </th>
                                  <th className="px-2 py-2 font-normal uppercase first:pl-8 last:pr-8">
                                    Amount
                                  </th>
                                  <th className="px-2 py-2 font-normal uppercase first:pl-8 last:pr-8">
                                    Status
                                  </th>
                                </tr>
                              </thead>

                              <tbody>
                                {!preauth || preauth.length < 1
                                  ? " No Pre-authorization(s) for this claim"
                                  : preauth.map((visit, index) => (
                                      <>
                                        <tr className="cursor-pointer" key={visit.id}>
                                          <td className="w-px whitespace-nowrap px-2 py-3 first:pl-5 last:pr-5">
                                            <div className="flex items-center">
                                              <button
                                                className={`transform text-slate-400 hover:text-slate-500 ${
                                                  openPreauth === visit.id ? true : "rotate-180"
                                                }`}
                                                aria-expanded={open}
                                                onClick={() => handleSetOpenPreauth(visit.id)}
                                                aria-controls={`description-${visit.id}`}
                                              >
                                                <span className="sr-only">Menu</span>
                                                <svg
                                                  className="h-8 w-8 fill-current"
                                                  viewBox="0 0 32 32"
                                                >
                                                  <path d="M16 20l-5.4-5.4 1.4-1.4 4 4 4-4 1.4 1.4z" />
                                                </svg>
                                              </button>
                                            </div>
                                          </td>
                                          <td className="px-2 py-4 first:pl-8 last:pr-8">
                                            {visit.visit.id}
                                          </td>
                                          <td className="px-2 py-4 first:pl-8 last:pr-8">
                                            {visit.visit.memberNumber}
                                          </td>
                                          <td
                                            className="px-2 py-4 first:pl-8 last:pr-8"
                                            title={visit.schemeName || visit?.scheme?.name}
                                          >
                                            {truncate(visit.schemeName || visit?.scheme?.name, 30)}
                                          </td>
                                          <td
                                            className="px-2 py-4 first:pl-8 last:pr-8"
                                            title={visit.visit.benefitName}
                                          >
                                            {truncate(visit.visit.benefitName, 30)}
                                          </td>
                                          <td
                                            className="px-2 py-4 first:pl-8 last:pr-8"
                                            title={visit.authorizedAmount}
                                          >
                                            {formatValue(visit.authorizedAmount)}
                                          </td>
                                          <td
                                            className="p-5 px-2 py-4 first:pl-8 last:pr-8"
                                            title={visit.benefitName}
                                          >
                                            <div className="text-center">
                                              <p className="rounded-full  bg-green-100 p-1 text-sm text-green-800">
                                                {visit.status}
                                              </p>
                                            </div>
                                          </td>
                                        </tr>
                                        <tr
                                          id={`description-${visit.id}`}
                                          role="MoreDetails"
                                          className={`${
                                            openPreauth === visit.id ? true : "hidden"
                                          }`}
                                        >
                                          <td colSpan={7} className="px-2 first:pl-5 last:pr-5">
                                            <div className="px-12">
                                              <h1 className="font-bold">More Details...</h1>
                                            </div>
                                            <div className="mt-3 flex w-full justify-evenly bg-slate-50 p-1">
                                              <div className="-ml-9 mb-3 text-sm">
                                                <div className="mb-1 font-medium text-slate-800">
                                                  Request Type
                                                </div>
                                                {preauth[index]?.requestType}
                                              </div>
                                              <div className="mb-3 text-sm  ">
                                                <div className="mb-1 font-medium text-slate-800">
                                                  Reference Number
                                                </div>
                                                {preauth[index]?.reference}
                                              </div>
                                              <div className="mb-3 text-sm">
                                                <div className="mb-1 font-medium text-slate-800">
                                                  Service
                                                </div>
                                                {preauth[index]?.service}
                                              </div>
                                              <div className="mb-3 text-sm">
                                                <div className="mb-1 font-medium text-slate-800">
                                                  Doctor's Name
                                                </div>
                                                {preauth[index]?.doctorName}
                                              </div>
                                              <div className="mb-3 text-sm">
                                                <div className="mb-1 font-medium text-slate-800">
                                                  Pre-diagnosis
                                                </div>
                                                {preauth[index]?.preDiagnosisCodes[0]}
                                              </div>
                                              <div className="mb-3 text-sm">
                                                <div className="mb-1 font-medium text-slate-800">
                                                  Approve Amount
                                                </div>
                                                {preauth[index]?.authorizedAmount}
                                              </div>
                                            </div>
                                          </td>
                                        </tr>
                                        <tr
                                          id={`description-${visit.id}`}
                                          role="region"
                                          className={`${
                                            openPreauth === visit.id ? true : "hidden"
                                          } `}
                                        >
                                          <td
                                            colSpan={7}
                                            className="-py-3 px-12 first:pl-5 last:pr-5"
                                          >
                                            <div className="px-12">
                                              <h1 className="font-bold">Supporting Documents...</h1>
                                            </div>
                                            <div className="flex px-12 ">
                                              {!docs
                                                ? ""
                                                : docs.map((w, index) => (
                                                    <div className="grid content-center ">
                                                      <div className="mr-5 ">
                                                        <svg
                                                          xmlns="http://www.w3.org/2000/svg"
                                                          fill="currentColor"
                                                          fill-rule="evenodd"
                                                          clip-rule="evenodd"
                                                          viewBox="0 0 512 512"
                                                          className="h-10 w-10 text-red-500"
                                                        >
                                                          <path d="M118.5-.5h217a23232.886 23232.886 0 0 1 128.5 128c.667 112 .667 224 0 336-4.339 25.835-19.172 41.835-44.5 48h-301c-22.914-5.727-37.414-20.06-43.5-43a266.363 266.363 0 0 1-1.5-35c-12.018.749-20.518-4.251-25.5-15a1426.759 1426.759 0 0 1-1.5-74.5 1426.54 1426.54 0 0 1 1.5-74.5c4.895-11.034 13.395-15.701 25.5-14a7795.06 7795.06 0 0 1 1.5-213c6.13-22.95 20.63-37.284 43.5-43Zm6 34h197c-.167 25.336 0 50.669.5 76 3.624 18.624 14.791 29.457 33.5 32.5 24.664.5 49.331.667 74 .5-.317 102.507.017 205.007 1 307.5.693 8.915-1.973 16.581-8 23-2.626 2.146-5.626 3.479-9 4-96.333.667-192.667.667-289 0-6.324-1.995-11.157-5.828-14.5-11.5a189.33 189.33 0 0 1-1.5-32c89.364.485 178.697-.015 268-1.5 7.258-2.924 12.092-8.091 14.5-15.5.667-48.333.667-96.667 0-145-2.5-7.833-7.667-13-15.5-15.5a35626.8 35626.8 0 0 0-267-.5c-.167-68.667 0-137.334.5-206 2.685-7.85 7.852-13.184 15.5-16Zm-11 267c10.339-.166 20.672 0 31 .5 21.552 4.932 29.386 18.098 23.5 39.5-4.398 9.786-11.898 15.62-22.5 17.5a93.752 93.752 0 0 1-21 1.5v33h-19v-91c2.885.306 5.552-.027 8-1Zm80 0c11.005-.167 22.005 0 33 .5 28.373 4.891 41.207 21.725 38.5 50.5-3.7 24.364-17.866 37.864-42.5 40.5a158.905 158.905 0 0 1-38-.5v-90c3.213.31 6.213-.023 9-1Zm87 0h55v16h-35v22h33v16h-33v38h-20v-92Z"></path>
                                                          <path d="M130.5 314.5c17.387-.194 23.22 7.806 17.5 24-6.561 5.677-14.061 7.344-22.5 5a196.869 196.869 0 0 1-1-28c2.235.295 4.235-.039 6-1Z"></path>
                                                          <path d="M204.5 315.5c6.342-.166 12.675 0 19 .5 14.291 4.207 21.291 13.874 21 29-1.398 25.214-14.731 36.047-40 32.5v-62Z"></path>
                                                        </svg>
                                                      </div>

                                                      <a
                                                        className="hover:underline"
                                                        target="_blank"
                                                        href={`${baseUrl}/api/file/download?name=${w}`}
                                                      >
                                                        {docs[index]?.includes("other")
                                                          ? `  Other`
                                                          : docs[index]?.includes("claim")
                                                            ? `Claim `
                                                            : docs[index]?.includes("invoice")
                                                              ? `  Invoice `
                                                              : "Report "}
                                                      </a>
                                                    </div>
                                                  ))}
                                            </div>
                                          </td>
                                        </tr>
                                      </>
                                    ))}
                              </tbody>
                            </table>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                ""
              )}
              {active === "Invoice Details" ? (
                <>
                  <div className="mt-5 w-full pb-4 text-gray-600">
                    <div className="max-w-full rounded-md bg-white">
                      <div className="mb-1">
                        <div className="overflow-x-auto bg-white text-gray-600">
                          <table className="w-full" key={refreshKey}>
                            <thead className="text-left">
                              <tr className="bg-gray-100">
                                <th></th>
                                <th></th>
                                <th className="px-2 py-2 text-xs font-normal uppercase first:pl-8 last:pr-8">
                                  Invoice Number
                                </th>
                                <th className="px-2 py-2 text-xs font-normal uppercase first:pl-8 last:pr-8">
                                  Invoice Amount
                                </th>
                                <th className="px-2 py-2 text-xs font-normal uppercase first:pl-8 last:pr-8">
                                  Invoice Date
                                </th>
                                <th className="px-2 py-2 text-xs font-normal uppercase first:pl-8 last:pr-8">
                                  Provider Name
                                </th>
                                <th className="px-2 py-2 text-xs font-normal uppercase first:pl-8 last:pr-8">
                                  Payable Amount
                                </th>
                                <th className="px-2 py-2 text-xs font-normal uppercase first:pl-8 last:pr-8">
                                  Actioned By
                                </th>
                                <th className="px-2 py-2 text-xs font-normal uppercase first:pl-8 last:pr-8">
                                  Status
                                </th>
                                <th></th>
                              </tr>
                            </thead>
                            <tbody>
                              {vettingInvoices
                                ?.filter((inv) => inv.status === InvoiceStatus.BALANCE_DEDUCTED)
                                .map((invoice, index) => (
                                  <Fragment>
                                    <tr className="" key={invoice.id}>
                                      <td>
                                        {invoice.vettingStatus == "APPROVED" ||
                                        invoice.vettingStatus == "DECLINED" ||
                                        invoice.vettingStatus == "PARTIAL" ? (
                                          <div title={invoice.vettingStatus}>
                                            <svg
                                              width="24"
                                              height="24"
                                              viewBox="0 0 24 24"
                                              fill="none"
                                              xmlns="http://www.w3.org/2000/svg"
                                            >
                                              <g clip-path="url(#clip0_6711_2803)">
                                                <path
                                                  d="M23.625 12C23.625 18.4203 18.4203 23.625 12 23.625C5.57967 23.625 0.375 18.4203 0.375 12C0.375 5.57967 5.57967 0.375 12 0.375C18.4203 0.375 23.625 5.57967 23.625 12ZM10.6553 18.1553L19.2803 9.53034C19.5732 9.23747 19.5732 8.76258 19.2803 8.4697L18.2197 7.40906C17.9268 7.11614 17.4519 7.11614 17.159 7.40906L10.125 14.443L6.84098 11.159C6.54811 10.8661 6.07322 10.8661 5.7803 11.159L4.71966 12.2197C4.42678 12.5125 4.42678 12.9874 4.71966 13.2803L9.59466 18.1553C9.88758 18.4482 10.3624 18.4482 10.6553 18.1553Z"
                                                  fill="#15803D"
                                                />
                                              </g>
                                              <defs>
                                                <clipPath id="clip0_6711_2803">
                                                  <rect width="24" height="24" fill="white" />
                                                </clipPath>
                                              </defs>
                                            </svg>
                                          </div>
                                        ) : (
                                          ""
                                        )}
                                      </td>
                                      <td className="w-px whitespace-nowrap px-2 py-1">
                                        <div className="flex items-center">
                                          <button
                                            className={`transform text-slate-400 hover:text-slate-500 ${
                                              openPreauth === invoice.id
                                                ? true && "rotate-180"
                                                : "rotate-180"
                                            }`}
                                            aria-expanded={open}
                                            onClick={() =>
                                              handleSetOpenInvoice(
                                                invoice.id,
                                                invoice.invoiceNumber,
                                              )
                                            }
                                            aria-controls={`invoice-${invoice.id}`}
                                          >
                                            <span className="sr-only">Menu</span>
                                            <svg
                                              className="h-8 w-8 fill-current"
                                              viewBox="0 0 32 32"
                                            >
                                              <path d="M16 20l-5.4-5.4 1.4-1.4 4 4 4-4 1.4 1.4z" />
                                            </svg>
                                          </button>
                                        </div>
                                      </td>
                                      <td className="px-2 py-4 text-xs first:pl-8 last:pr-8">
                                        {invoice.invoiceNumber}
                                      </td>
                                      <td className="px-2 py-4 text-xs first:pl-8 last:pr-8">
                                        {formatValue(invoice.totalAmount)}
                                      </td>
                                      <td className="px-2 py-4 text-xs first:pl-8 last:pr-8">
                                        {invoice.createdAt?.split(" ")[0]}
                                      </td>
                                      <td className="px-2 py-4 text-xs first:pl-8 last:pr-8">
                                        {truncate(visit.providerName, 20)}
                                      </td>
                                      <td className="px-2 py-4 text-xs first:pl-8 last:pr-8">
                                        {formatValue(invoice.payableAmount)}
                                      </td>
                                      <td className="px-2 py-4 text-xs first:pl-8 last:pr-8">
                                        {invoice.actionedBy}
                                      </td>
                                      <td className="px-2 py-4 text-xs first:pl-8 last:pr-8 ">
                                        <p
                                          className={`${
                                            invoice.vettingStatus
                                              ? "rounded-full border-2 border-green-600 bg-green-300 p-1 text-center text-xs text-gray-800"
                                              : ""
                                          }`}
                                        >
                                          {invoice.vettingStatus}
                                        </p>
                                      </td>
                                      <td>
                                        {invoice.vettingStatus === "PENDING" ? (
                                          <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            strokeWidth={1.5}
                                            stroke="currentColor"
                                            className="h-5 w-5 hover:cursor-pointer"
                                            onClick={() => editInvoice(invoice)}
                                          >
                                            <title>Edit Invoice No.</title>
                                            <path
                                              strokeLinecap="round"
                                              strokeLinejoin="round"
                                              d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"
                                            />
                                          </svg>
                                        ) : null}
                                      </td>
                                    </tr>

                                    {/* {vettingInvoicelineItems.map((lineItems, index) => (
                                    <> */}
                                    <tr
                                      id={`invoice-${invoice.id}`}
                                      role="region"
                                      className={`${openInvoice === invoice.id ? true : "hidden"} `}
                                    >
                                      <td colSpan={7} className="px-2 py-1 first:pl-5 last:pr-5 ">
                                        <div>
                                          {/* <h1 className="font-bold pl-11">More Details...</h1> */}
                                        </div>
                                        {/* <div className="-mt-3  flex w-full justify-between p-3 pl-11">
                                            <div className="mb-3 text-xs font-normal">
                                              <div className="mb-1 bg-gray-100 text-xs font-normal text-slate-800">
                                                Service Type
                                              </div>
                                              {lineItems.lineType}
                                            </div>
                                            <div className="mb-3 text-xs font-normal">
                                              <div className="mb-1  bg-gray-100 text-xs font-normal  text-slate-800">
                                                Description
                                              </div>
                                              {truncate(lineItems.description, 20)}
                                            </div>
                                            <div className="mb-3 text-xs font-normal">
                                              <div className="mb-1 bg-gray-100 text-xs font-normal  text-slate-800">
                                                Unit Price
                                              </div>
                                              {formatValue(lineItems.unitPrice)}
                                            </div>
                                            <div className="mb-3 text-xs font-normal">
                                              <div className="mb-1 bg-gray-100 text-xs font-normal  text-slate-800">
                                                Quantity
                                              </div>
                                              {lineItems.quantity}
                                            </div>
                                            <div className="mb-3 text-xs font-normal">
                                              <div className="mb-1 bg-gray-100 text-xs font-normal  text-slate-800">
                                                Total Cost
                                              </div>
                                              {formatValue(lineItems.lineTotal)}
                                            </div>
                                          </div> */}

                                        <table className="w-full">
                                          <thead className="text-left">
                                            <tr className="bg-gray-100 text-xs font-normal  text-slate-800">
                                              <th></th>
                                              <th className="bg-gray-100 text-xs font-normal">
                                                Service Type
                                              </th>
                                              <th className="bg-gray-100 text-xs font-normal">
                                                Description
                                              </th>
                                              <th className="bg-gray-100 text-xs font-normal">
                                                Unit Price{" "}
                                              </th>
                                              <th className="bg-gray-100 text-xs font-normal">
                                                Quantity
                                              </th>
                                              <th className="bg-gray-100 text-xs font-normal">
                                                Total Cost
                                              </th>
                                            </tr>
                                          </thead>
                                          <tbody>
                                            {isLineItemsFetching || isLineItemsLoading ? (
                                              <div className="flex items-center justify-center py-8">
                                                <p className="text-blue-700">Loading...</p>
                                                <LoadingIcon className="h-6 w-6 text-blue-400" />
                                              </div>
                                            ) :!fetchedLineItems?.length ? (
                                              <Empty message="No invoice items found" />
                                            ): (
                                              fetchedLineItems?.map((lineItems, index) => (
                                                <>
                                                  <tr className="text-left">
                                                    <td className="text-white"></td>
                                                    <td className="py-2 text-xs font-normal">
                                                      {lineItems.lineType}
                                                    </td>
                                                    <td className="py-2 text-xs font-normal">
                                                      <span data-tip={lineItems.description}>
                                                        {truncate(
                                                          lineItems.description,
                                                          20,
                                                        )?.toUpperCase()}
                                                      </span>
                                                      <ReactTooltip
                                                        place="right"
                                                        type="light"
                                                        effect="float"
                                                      />
                                                    </td>
                                                    <td className="py-2 text-xs font-normal">
                                                      {" "}
                                                      {formatValue(lineItems.unitPrice)}
                                                    </td>
                                                    <td className="py-2 text-xs font-normal">
                                                      {lineItems.quantity}
                                                    </td>
                                                    <td className="py-2 text-xs font-normal">
                                                      {formatValue(lineItems.lineTotal)}
                                                    </td>
                                                  </tr>
                                                </>
                                              ))
                                            )}
                                          </tbody>
                                        </table>
                                      </td>
                                    </tr>
                                    {/* </>
                                  ))} */}
                                    <tr
                                      id={`invoice-${invoice.id}`}
                                      role="region"
                                      className={`${
                                        openInvoice === invoice.id ? true : "hidden"
                                      } border border-b-2 border-l-0 border-r-0 border-t-0 border-gray-200 py-1 `}
                                    >
                                      <td
                                        colSpan={7}
                                        //className="px-2 first:pl-5 last:pr-5 pl-11"
                                        className={`px-2 pl-11 first:pl-5 last:pr-5 ${
                                          invoice.vettingStatus == "APPROVED" ||
                                          invoice.vettingStatus == "DECLINED" ||
                                          invoice.vettingStatus == "PARTIAL"
                                            ? "hidden"
                                            : ""
                                        }`}
                                      >
                                        <div>
                                          <h1 className="mb-2 mt-6 pl-11 font-bold">Actions</h1>
                                        </div>
                                        <div className="-mt-3  flex w-full justify-between p-3 pl-11">
                                          <fieldset className="w-full border border-gray-200 p-3  ">
                                            <legend>Approve/Decline</legend>
                                            <div className="flex">
                                              Approval <p className="text-red-600">*</p>
                                            </div>
                                            <div className="flex">
                                              <div className="control mx-2">
                                                <input
                                                  id="approval_approve"
                                                  name="approval"
                                                  value="approved"
                                                  type="radio"
                                                  defaultChecked={approval === "approved"}
                                                  onChange={onOptionChange}
                                                />
                                                <label for="approval_approve" className="mx-2">
                                                  Approve
                                                </label>
                                              </div>
                                              <div className="control ml-5">
                                                <input
                                                  id="approval_decline"
                                                  name="approval"
                                                  type="radio"
                                                  value="decline"
                                                  defaultChecked={approval === "decline"}
                                                  onChange={onOptionChange}
                                                />
                                                <label for="approval_decline" className="mx-2">
                                                  Decline
                                                </label>
                                              </div>
                                              <div className="control ml-5">
                                                <input
                                                  id="approval_partial"
                                                  name="approval"
                                                  type="radio"
                                                  value="partial"
                                                  defaultChecked={approval === "partial"}
                                                  onChange={onOptionChange}
                                                />
                                                <label for="approval_partial" className="mx-2">
                                                  Partial Approval
                                                </label>
                                              </div>
                                            </div>
                                            <div className="flex justify-center text-red-600">
                                              {error}
                                            </div>
                                            <div className="flex justify-center text-green-600">
                                              {loadingVetInvoice ? "Loading..." : ""}
                                            </div>
                                          </fieldset>
                                        </div>
                                        {approval === "approved" ? (
                                          <div className="-mt-3  flex w-full justify-between p-3 pl-11">
                                            <fieldset className="w-full border border-gray-200 p-3   ">
                                              <legend>Terms</legend>
                                              <div className="flex">Payable Amount </div>
                                              <input
                                                id="payableAmountInputField"
                                                name="payableAmount"
                                                type="text"
                                                value={formatValue(invoice.totalAmount)}
                                                disabled={true}
                                                className="cursor-not-allowed rounded-md border-gray-400 text-gray-400"
                                              />
                                            </fieldset>
                                          </div>
                                        ) : approval === "decline" ? (
                                          <div className="-50 -mt-3 flex w-full justify-between p-3  pl-11">
                                            <fieldset className="w-full border border-gray-200 p-3  ">
                                              <legend>Terms</legend>
                                              <div className="flex">
                                                Reason <p className="text-red-600">*</p>
                                              </div>
                                              <input
                                                id="vettingReasonInputField"
                                                name="vettingReason"
                                                type="text"
                                                value={declineReason}
                                                onChange={onVettingDeclineReasonChange}
                                                className="rounded-md border-gray-400 text-gray-400"
                                              />
                                            </fieldset>
                                          </div>
                                        ) : approval === "partial" ? (
                                          <div className="-50 -mt-3 flex w-full justify-between p-3  pl-11">
                                            <fieldset className="w-full border border-gray-200 p-3  ">
                                              <legend>Terms</legend>
                                              <div className="flex ">
                                                <div className="flex-col">
                                                  <label className="text-gray-400">
                                                    Deductible Amount
                                                  </label>
                                                  <input
                                                    id="deductibleAmountInputField"
                                                    name="payableAmount"
                                                    type="text"
                                                    value={deductibleAmount}
                                                    placeholder="Enter deductible amount"
                                                    onChange={onPartialDeductibleAmountChange}
                                                    className="rounded-md border-gray-400 text-gray-400"
                                                  />
                                                </div>
                                                <div className="flex-col">
                                                  <label className=" ml-5 text-gray-400">
                                                    Payable Amount
                                                  </label>
                                                  <input
                                                    id="payableAmountInputField"
                                                    name="payableAmount"
                                                    type="text"
                                                    value={
                                                      deductibleAmount !== ""
                                                        ? formatValue(
                                                            parseFloat(invoice.totalAmount) -
                                                              parseFloat(deductibleAmount),
                                                          )
                                                        : formatValue(invoice.totalAmount)
                                                    }
                                                    disabled={true}
                                                    placeholder="Payable Amount"
                                                    className="ml-5 cursor-not-allowed rounded-md border-gray-400 text-gray-400"
                                                  />
                                                </div>
                                                <div className="flex-col">
                                                  <label className=" ml-5 text-gray-400">
                                                    Reason
                                                  </label>
                                                  <input
                                                    id="partialReasonInputField"
                                                    name="partialReason"
                                                    type="text"
                                                    value={partialReason}
                                                    placeholder="Enter the reason"
                                                    onChange={onPartialReasonChange}
                                                    className="ml-5 rounded-md border-gray-400 text-gray-400"
                                                  />
                                                </div>
                                              </div>
                                            </fieldset>
                                          </div>
                                        ) : (
                                          ""
                                        )}
                                        <div className="flex justify-end">
                                          <button
                                            className="btn-sm mr-10 rounded-md bg-blue-700 px-1 py-1 text-white"
                                            title="Submit"
                                            onClick={handleSubmitVettingStatus}
                                          >
                                            Submit
                                          </button>
                                        </div>
                                      </td>
                                    </tr>
                                  </Fragment>
                                ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                ""
              )}
              {active === "Action" ? (
                <>
                  <div>------------------ Action</div>
                </>
              ) : (
                ""
              )}
            </div>
          </div>
        </div>
      </div>
      <ModalSmall
        id="SupportDocuments-modal"
        modalOpen={supportDocumentsModalOpen}
        setModalOpen={setSupportDocumentsModalOpenOpen}
        title="Supporting Documents (Optional)"
      >
        <SupportDocumentsModal
          open={supportDocumentsModalOpen}
          onClose={() => setSupportDocumentsModalOpenOpen(false)}
        ></SupportDocumentsModal>
      </ModalSmall>
      <ModalEdit
        id="editInvoice-modal"
        modalOpen={editInvoiceModal}
        onClose={closeModalEdit}
        title="Edit Invoice Number"
      >
        {selectedInvoice && (
          <EditInvoice
            invoice={selectedInvoice}
            onClose={closeModalEdit}
            onInvoiceChange={handleInvoiceChange}
          />
        )}
      </ModalEdit>
    </main>
  );
};


const mapDispatchToProps = (dispatch) => ({
  getClaimsToVetById: (id) => dispatch(getClaimsToVetById(id)),
  getPreAuthToVetById: (id) => dispatch(getPreAuthToVetById(id)),
  getInvoicesByVisitId: (id) => dispatch(getInvoicesByVisitId(id)),
  getDocumentsByVisitId: (id) => dispatch(getDocumentsByVisitId(id)),
  getLineItemsByVisitIdAndInvoiceNumber: (id, invoiceNumber) =>
    dispatch(getLineItemsByVisitIdAndInvoiceNumber(id, invoiceNumber)),
});
export default connect(null, mapDispatchToProps)(VetClaim);
