import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { connect, useSelector } from "react-redux";
import styled from "styled-components";
import { VisitType } from "~lib/api/types";
import { AsyncSelect, DateInput, Empty, FieldWrapper } from "~lib/components";
import { Form } from "~lib/components/Form";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { PAGE_SIZES } from "~lib/constants";
import { useDebounce } from "~lib/hooks/useDebounce";
import useSearchMainProviderOptions from "~lib/hooks/useSearchMainProviderOptions";
import { Option } from "~lib/types";
import Reading from "../../../components/illustrations/Reading";
import ModalSmall from "../../../components/ModalSmall";
import Pagination2 from "../../../components/Pagination2";
import Pagination3 from "../../../components/Pagination3";
import { api_url_claim_report, formatValue } from "../../../lib/Utils";
import UserService from "../../../services/UserService";
import { RootState } from "../../../store";
import { useAppDispatch } from "../../../store/hooks";
import {
  getBatchedInvoices,
  getClaimsToBatch,
  getPayerRegionsByPayerId,
  getPlanByPayerId,
} from "../../../store/members/actions";
import BatchButton from "../../partials/pageItems/BatchButton";
import ClaimsBatchingTableItem from "../../partials/pageItems/ClaimsBatchingTableItem";
import BatchClaimsModal from "./BatchClaimsModal";
import BatchingTableDownload from "./BatchingTableDownload";

interface Inputs {
  view: View;
  provider?: Option<string> | undefined;
  query?: string;
  startDate?: string;
  endDate?: string;
}

enum View {
  ACTIVE = "ACTIVE",
  HISTORY = "HISTORY",
}

const viewLabels: Record<View, string> = {
  ACTIVE: "Active",
  HISTORY: "History",
};

interface Props {
  visitType: VisitType;
}
const Tab = styled.button`
  font-size: 20px;
  padding: 5px 10px;
  cursor: pointer;
  opacity: 0.6;
  border: 0;
  outline: 0;
`;
const ButtonGroup = styled.div`
  display: flex;
`;

const batchMenus = ["Individual Claims", "Batched Claims"];
const claimMap = new Map([]);

export const ClaimsBatchingTable = ({ getClaimsToBatch, getBatchedInvoices }) => {
  const [page, setPage] = useState(1);
  const [size, setSize] = useState<number>(PAGE_SIZES[0]);
  const [active, setActive] = useState(batchMenus[0]);
  const [isCheck, setIsCheck] = useState([]);
  const [isCheckMap, setIsCheckMap] = useState(new Map());
  const [list, setList] = useState([]);
  const [batchedList, setBatchedList] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [selectedItemsBtn, setSelectedItemsBtn] = useState([]);
  const { getProviderOptions } = useSearchMainProviderOptions();
  const [batchClaimsModalOpen, setBatchClaimsModalOpen] = useState(false);
  const [claimSum, setClaimSum] = useState(0);
  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const [offSetValue, setOffsetValue] = useState(0);
  const [planSelected, setPlanSelected] = useState("");
  const [regionSelected, setRegionSelected] = useState("");
  const [refreshListAfterBatching, setRefreshListAfterBatching] = useState(false);

  //modification
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");

  const [loadingPlan, setLoadingPlan] = useState(false);
  const [loadingRegion, setLoadingRegion] = useState(false);
  const [loadingProvider, setLoadingProvider] = useState(false);
  const [loadingStartDate, setLoadingStartDate] = useState(false);
  const [loadingEndDate, setLoadingEndDate] = useState(false);

  //Batch Button State
  const [buttonClicked, setButtonClicked] = useState(false);

  //end modification

  const claimsToBatch = useSelector(
    (state: RootState) => state.memberInfo.claimsToBatch?.data?.content,
  );
  const claimsToBatchResponse = useSelector((state: RootState) => state.memberInfo.claimsToBatch);
  const totalItemsToBatch = claimsToBatchResponse?.data?.totalElements;
  const batchedInvoices = useSelector(
    (state: RootState) => state.memberInfo.batchedInvoices?.data?.content,
  );
  const loadingClaimsToBatch = useSelector(
    (state: RootState) => state.memberInfo.loadingClaimsToBatch,
  );
  const loadingbatchedInvoices = useSelector(
    (state: RootState) => state.memberInfo.loadingbatchedInvoices,
  );
  const batchedInvoicesResponse = useSelector(
    (state: RootState) => state.memberInfo.batchedInvoices,
  );
  const schemes = useSelector((state: RootState) => state.memberInfo.plans);
  const payerRegions = useSelector((state: RootState) => state.memberInfo.payerRegions);
  const [open, setOpen] = useState(false);

  const [openInvoice, setOpenInvoice] = useState(0);
  const [openBatch, setOpenBatch] = useState(0);

  const dispatch = useAppDispatch();

  const methods = useForm<Inputs>({
    mode: "onBlur",
    defaultValues: {
      view: View.ACTIVE,
      provider: undefined,
      query: "",
      startDate: "",
      endDate: "",
    },
  });
  const { watch, setValue } = methods;

  const form = watch();
  const queryDebounced = useDebounce(form.query, 10);

  useEffect(() => {
    dispatch(getPlanByPayerId(payerId));
  }, []);
  useEffect(() => {
    dispatch(getPayerRegionsByPayerId(payerId));
  }, []);

  useEffect(() => {
    setList(claimsToBatch);
  }, [claimsToBatch]);

  const handleSelectAll = () => {
    setSelectAll(!selectAll);
    if (!selectAll) {
      const ids = list.map((item) => item.id);
      const totalAmount = list.reduce((acc, item) => acc + item.payableAmount, 0);
      setIsCheck(ids);
      setClaimSum(totalAmount);
    } else {
      setIsCheck([]);
      setClaimSum(0);
    }
  };

  const handleClick = (e, invoice) => {
    const { checked } = e.target;
    if (checked) {
      setIsCheck([...isCheck, invoice.id]);
      setClaimSum((prevSum) => prevSum + invoice.payableAmount);
    } else {
      setIsCheck(isCheck.filter((item) => item !== invoice.id));
      setClaimSum((prevSum) => prevSum - invoice.payableAmount);
    }
  };

  const onBatchBtnClick = (e) => {
    e.stopPropagation();
    setBatchClaimsModalOpen(true);
  };

  useEffect(() => {
    setSelectedItemsBtn([...new Set(isCheck)]);
  }, [isCheck]);

  const handlePlanSelected = (e: any) => {
    setLoadingPlan(true);
    setPlanSelected(e.target.value);
  };

  const handleRegionSelected = (e: any) => {
    setLoadingRegion(true);
    setRegionSelected(e.target.value);
  };

  const handlePaginationChange = (e) => {
    getClaimsToBatch(
      payerId,
      Number(form?.provider?.value),
      queryDebounced,
      e,
      planSelected,
      regionSelected,
      size,
      String(form?.startDate),
      String(form?.endDate),
    ).then(() => {
      setRefreshKey((oldKey) => oldKey + 1);
    });
  };

  // const handleSizeChange=(e)=>{
  //   setSize(e.target.value);
  //   setSelectAll(false);
  //   setIsCheck([]);
  //   getClaimsToBatch(
  //     payerId,
  //     Number(form?.provider?.value),
  //     queryDebounced,
  //     offSetValue,
  //     planSelected,
  //     regionSelected,
  //     e.target.value,
  //     String(form?.startDate),
  //     String(form?.endDate)
  //   );
  // }

  useEffect(() => {
    if (!loadingClaimsToBatch) {
      setRefreshListAfterBatching(false);
      setIsCheck([]);
      setSelectAll(false);

      getClaimsToBatch(
        payerId,
        Number(form?.provider?.value),
        queryDebounced,
        offSetValue,
        //page,
        planSelected,
        regionSelected,
        size,
        String(form?.startDate),
        String(form?.endDate),
      );
    }
    if (active == "Batched Claims") {
      getBatchedInvoices(payerId, undefined, 1, 10);
    }
  }, [
    form?.provider?.value,
    queryDebounced,
    offSetValue,
    planSelected,
    active,
    regionSelected,
    size,
    form?.startDate,
    form?.endDate,
    refreshListAfterBatching,
  ]);

  useEffect(() => {
    setSelectedItemsBtn([...new Set(isCheck)]);
  }, [isCheck]);

  useEffect(() => {
    setBatchedList(batchedInvoices);
  }, [batchedInvoices]);

  //Filter options
  const [selectedOption, setSelectedOption] = useState("");

  useEffect(() => {
    if (selectedOption === "") {
      setPlanSelected("");
      setRegionSelected("");
      setValue("provider", undefined);
      setValue("startDate", "");
      setValue("endDate", "");
      setIsCheck([]);
      setSelectAll(false);
      setClaimSum(0);
    }
  }, [setList, selectedOption]);

  const handleOptionChange = (e) => {
    setSelectedOption(e.target.value);
  };

  const selectStyle = {
    overflow: "hidden",
    whiteSpace: "nowrap",
    textOverflow: "ellipsis",
  };
  const firstName = UserService.getPayer()?.tokenParsed?.["given_name"];
  const lastName = UserService.getPayer()?.tokenParsed?.["family_name"];
  const userFullNames = firstName + " " + lastName;

  const resetUIAfterBatching = () => {
    setIsCheck([]);
    claimMap.clear();
    setRefreshListAfterBatching(true);
    setSelectedItemsBtn([]);
  };

  return (
    <div className="flex h-full flex-grow overflow-hidden bg-gray-50">
      <div className="flex flex-1 flex-col overflow-y-auto overflow-x-hidden">
        <main>
          <div className="w-full pb-4 text-gray-600">
            <div className="relative max-w-full rounded-md bg-white shadow-md">
              <div className="z-10  bg-gray-50 px-8 py-1">
                <Form
                  className="pt-2 text-sm"
                  methods={methods}
                  onSubmit={(_data, e) => {
                    e?.preventDefault();
                  }}
                >
                  <div className="flex items-center justify-between pb-2">
                    <hgroup className="flex items-center gap-2 ">
                      <span
                        className={`mr-1 cursor-pointer rounded-md bg-[#E1E8F0] p-2 ${
                          active === "Batched Claims" ? "hidden" : "block"
                        } ${
                          selectedOption === "mainProvider" ||
                          selectedOption === "region" ||
                          selectedOption === "scheme"
                            ? "block"
                            : "hidden"
                        }`}
                        onClick={() => setSelectedOption("")}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          strokeWidth={1.5}
                          stroke="currentColor"
                          className="h-5 w-5"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"
                          />
                        </svg>
                      </span>
                      <h2 className="text-lg font-bold font-medium capitalize">Claims List</h2>
                    </hgroup>
                  </div>
                  <div>
                    <div className="mb-6 mt-4 flex grid grid-cols-3 items-center justify-center gap-4">
                      <div className="col-span-1 flex whitespace-nowrap ">
                        <ButtonGroup className="border-1 rounded-md border border-gray-200 bg-white px-0 py-0 shadow-sm">
                          {batchMenus.map((batchMenu) => (
                            <Tab
                              key={batchMenu}
                              onClick={() => setActive(batchMenu)}
                              className={
                                active === batchMenu
                                  ? "my-0 inline-flex justify-between whitespace-nowrap rounded-md bg-blue-50 py-0 text-center text-xs font-normal text-blue-700"
                                  : "inline-flex justify-between whitespace-nowrap  rounded-md bg-white px-1  py-1 text-center text-xs font-normal  text-gray-900"
                              }
                            >
                              <div className="">
                                {batchMenu === "Individual Claims" ? (
                                  <>
                                    <div className="flex items-center justify-center border   border-b-0 border-l-0 border-t-0 border-r-blue-900  pr-2 text-xs">
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                        className="mr-2 h-6 w-6"
                                      >
                                        <path
                                          stroke-linecap="round"
                                          stroke-linejoin="round"
                                          d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                                        />
                                      </svg>{" "}
                                      {batchMenu}
                                    </div>
                                  </>
                                ) : (
                                  <>
                                    <div className="flex items-center justify-center rounded-md border border-b-0 border-l-0 border-r-0 border-t-0  pr-2 text-xs">
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                        className="mr-2 h-6  w-6"
                                      >
                                        <path
                                          stroke-linecap="round"
                                          stroke-linejoin="round"
                                          d="m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z"
                                        />
                                      </svg>
                                      {batchMenu}
                                    </div>
                                  </>
                                )}
                              </div>
                            </Tab>
                          ))}
                        </ButtonGroup>
                      </div>
                      <div className="col-span-2 flex items-center justify-end">
                        <div className="mt-4">
                          {selectedItemsBtn.length <
                          1 ? //     placeholder="Member number or invoice number or Member Name ..." //     type="search" //   <Input // > //   labelClassName="font-semibold text-gray-500" //   className="" //   label="" //   name="query" // <FieldWrapper
                          //     className="w-96 text-sm"
                          //   />
                          // </FieldWrapper>
                          null : (
                            <div className={`${active === "Batched Claims" ? "hidden" : "block"}`}>
                              <BatchButton
                                selectedItemsBtn={selectedItemsBtn}
                                isAllSelected={selectAll}
                                selectAllItems={totalItemsToBatch}
                                // handleBatchClick={(e) => onbatchBtnClick(e)}
                                handleBatchClick={(e) => onBatchBtnClick(e)}
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* select filter */}

                  <div
                    className={`mb-10 mt-10 w-full pl-10 pr-10 ${
                      active === "Batched Claims" ? "hidden" : "block"
                    } ${
                      selectedOption === "mainProvider" ||
                      selectedOption === "region" ||
                      selectedOption === "scheme"
                        ? "hidden"
                        : "block"
                    }`}
                  >
                    <div>
                      <label
                        htmlFor="filter"
                        className="mr-2 inline-block font-['Inter'] text-lg font-normal text-slate-700"
                      >
                        Select batching criteria
                      </label>
                      <select
                        id="filter"
                        value={selectedOption}
                        onChange={handleOptionChange}
                        className="w-[70%] rounded-lg border border-gray-300 px-4 py-2 shadow-sm hover:border-blue-400 focus:border-blue-400 focus:outline-none"
                      >
                        <option
                          className="font-['Inter'] text-base font-normal leading-normal text-gray-500"
                          value=""
                          disabled
                          selected
                          hidden
                        >
                          Select criteria
                        </option>
                        <option value="scheme">Scheme</option>
                        <option value="region">Region</option>
                        <option value="mainProvider">Main Provider</option>
                      </select>
                    </div>
                  </div>

                  {/* End Select Filter */}
                  <div className={`flex grid grid-cols-5 gap-2`}>
                    {selectedOption === "scheme" && (
                      <>
                        <FieldWrapper
                          name="plan"
                          label="Scheme"
                          className={`max-w-xs flex-shrink flex-grow ${
                            active === "Batched Claims" ? "hidden" : "block"
                          }`}
                          labelClassName="font-semibold text-gray-800"
                        >
                          <select
                            name="plan"
                            id="plan"
                            style={selectStyle}
                            placeholder="Select Scheme"
                            onChange={(e) => handlePlanSelected(e)}
                            className="btn mr-6 w-full justify-between border-gray-200 bg-white text-sm uppercase text-gray-400 hover:border-gray-300 hover:text-gray-600 md:text-base lg:text-base"
                          >
                            <option value="" selected disabled hidden>
                              Select Scheme
                            </option>
                            {schemes.map((scheme, index) => (
                              <option
                                className={
                                  "mr-6 flex w-full cursor-pointer items-center px-3 py-1  text-sm font-medium text-gray-600 hover:bg-gray-50 md:text-[14px] lg:text-[14px] "
                                }
                                value={scheme.id}
                                key={index}
                              >
                                {scheme.name}
                              </option>
                            ))}
                          </select>
                        </FieldWrapper>
                        <FieldWrapper
                          name="region"
                          label="Region"
                          className={`max-w-xs flex-shrink flex-grow ${
                            active === "Batched Claims" ? "hidden" : "block"
                          }`}
                          labelClassName="font-semibold text-gray-800"
                        >
                          <select
                            name="region"
                            id="region"
                            placeholder="Select Region"
                            onChange={(e) => handleRegionSelected(e)}
                            className="btn mr-6 w-full justify-between border-gray-200 bg-white text-sm uppercase text-gray-400 hover:border-gray-300 hover:text-gray-600 md:text-base lg:text-base"
                          >
                            <option value="" selected disabled hidden>
                              Select Region
                            </option>
                            {payerRegions.map((region, index) => (
                              <option
                                className={
                                  "mr-6 flex w-full cursor-pointer items-center px-3 py-1  text-sm font-medium text-gray-600 hover:bg-gray-50 md:text-[14px] lg:text-[14px] "
                                }
                                value={region.name}
                                key={index}
                              >
                                {region.name}
                              </option>
                            ))}
                          </select>
                        </FieldWrapper>
                        <FieldWrapper
                          name="provider"
                          label="Provider"
                          className={`max-w-xs flex-shrink flex-grow ${
                            active === "Batched Claims" ? "hidden" : "block"
                          }`}
                          labelClassName="font-semibold text-gray-800"
                        >
                          <AsyncSelect
                            name="provider"
                            getOptions={getProviderOptions}
                            placeholder="Search provider..."
                            className="min-w-48"
                          />
                        </FieldWrapper>
                        <FieldWrapper
                          name="startDate"
                          className={`max-w-xs flex-shrink flex-grow ${
                            active === "Batched Claims" ? "hidden" : "block"
                          }`}
                          label="Start Date"
                          labelClassName="font-semibold text-gray-800"
                        >
                          <DateInput
                            name="startDate"
                            placeholder={new Date().toISOString().split("T")[0]}
                            maxDate="today"
                            onChange={() => setLoadingStartDate(true)}
                            className="w-auto text-sm"
                          />
                        </FieldWrapper>

                        <FieldWrapper
                          name="endDate"
                          className={`max-w-xs flex-shrink flex-grow ${
                            active === "Batched Claims" ? "hidden" : "block"
                          }`}
                          label="End Date"
                          labelClassName="font-semibold text-gray-800"
                        >
                          <DateInput
                            name="endDate"
                            placeholder={new Date().toISOString().split("T")[0]}
                            maxDate="today"
                            onChange={() => setLoadingEndDate(true)}
                            className="w-auto text-sm"
                          />
                        </FieldWrapper>
                      </>
                    )}

                    {selectedOption === "region" && (
                      <>
                        <FieldWrapper
                          name="region"
                          label="Region"
                          className={`max-w-xs flex-shrink flex-grow ${
                            active === "Batched Claims" ? "hidden" : "block"
                          }`}
                          labelClassName="font-semibold text-gray-800"
                        >
                          <select
                            name="region"
                            id="region"
                            placeholder="Select Region"
                            onChange={(e) => handleRegionSelected(e)}
                            className="btn mr-6 w-full justify-between border-gray-200 bg-white text-sm uppercase text-gray-500 hover:border-gray-300 hover:text-gray-600 md:text-base lg:text-base"
                          >
                            <option value="" selected disabled hidden>
                              Select Region
                            </option>
                            {payerRegions.map((region, index) => (
                              <option
                                className={
                                  "mr-6 flex w-full cursor-pointer items-center px-3 py-1  text-sm font-medium text-gray-600 hover:bg-gray-50 md:text-sm lg:text-sm "
                                }
                                value={region.name}
                                key={index}
                              >
                                {region.name}
                              </option>
                            ))}
                          </select>
                        </FieldWrapper>
                        <FieldWrapper
                          name="plan"
                          label="Scheme"
                          className={`max-w-xs flex-shrink flex-grow ${
                            active === "Batched Claims" ? "hidden" : "block"
                          }`}
                          labelClassName="font-semibold text-gray-800"
                        >
                          <select
                            name="plan"
                            id="plan"
                            style={selectStyle}
                            placeholder="Select Scheme"
                            onChange={(e) => handlePlanSelected(e)}
                            className="btn mr-6 w-full justify-between border-gray-200 bg-white text-sm uppercase text-gray-500 hover:border-gray-300 hover:text-gray-600 md:text-base lg:text-base"
                          >
                            <option value="" selected disabled hidden>
                              Select Scheme
                            </option>
                            {schemes.map((scheme, index) => (
                              <option
                                className={
                                  "mr-6 flex w-full cursor-pointer items-center px-3 py-1  text-sm font-medium text-gray-600 hover:bg-gray-50 md:text-sm lg:text-sm "
                                }
                                value={scheme.id}
                                key={index}
                              >
                                {scheme.name}
                              </option>
                            ))}
                          </select>
                        </FieldWrapper>
                        <FieldWrapper
                          name="provider"
                          label="Provider"
                          className={`max-w-xs flex-shrink flex-grow ${
                            active === "Batched Claims" ? "hidden" : "block"
                          }`}
                          labelClassName="font-semibold text-gray-800"
                        >
                          <AsyncSelect
                            name="provider"
                            getOptions={getProviderOptions}
                            placeholder="Search provider..."
                            className="min-w-48"
                          />
                        </FieldWrapper>
                        <FieldWrapper
                          name="startDate"
                          className={`max-w-xs flex-shrink flex-grow ${
                            active === "Batched Claims" ? "hidden" : "block"
                          }`}
                          label="Start Date"
                          labelClassName="font-semibold text-gray-800"
                        >
                          <DateInput
                            placeholder={new Date().toISOString().split("T")[0]}
                            maxDate="today"
                            className="w-auto text-sm"
                          />
                        </FieldWrapper>

                        <FieldWrapper
                          name="endDate"
                          className={`max-w-xs flex-shrink flex-grow ${
                            active === "Batched Claims" ? "hidden" : "block"
                          }`}
                          label="End Date"
                          labelClassName="font-semibold text-gray-800"
                        >
                          <DateInput
                            placeholder={new Date().toISOString().split("T")[0]}
                            maxDate="today"
                            className="w-auto text-sm"
                          />
                        </FieldWrapper>
                      </>
                    )}
                    {selectedOption === "mainProvider" && (
                      <>
                        <FieldWrapper
                          name="provider"
                          label="Provider"
                          className={`max-w-xs flex-shrink flex-grow ${
                            active === "Batched Claims" ? "hidden" : "block"
                          }`}
                          labelClassName="font-semibold text-gray-800"
                        >
                          <AsyncSelect
                            name="provider"
                            getOptions={getProviderOptions}
                            placeholder="Search provider..."
                            className="min-w-48"
                          />
                        </FieldWrapper>
                        <FieldWrapper
                          name="region"
                          label="Region"
                          className={`max-w-xs flex-shrink flex-grow ${
                            active === "Batched Claims" ? "hidden" : "block"
                          }`}
                          labelClassName="font-semibold text-gray-800"
                        >
                          <select
                            name="region"
                            id="region"
                            placeholder="Select Region"
                            onChange={(e) => handleRegionSelected(e)}
                            className="btn mr-6 w-full justify-between border-gray-200 bg-white text-sm uppercase text-gray-500 hover:border-gray-300 hover:text-gray-600 md:text-sm lg:text-sm"
                          >
                            <option value="" selected disabled hidden>
                              Select Region
                            </option>
                            {payerRegions.map((region, index) => (
                              <option
                                className={
                                  "mr-6 flex w-full cursor-pointer items-center px-3 py-1  text-sm font-medium text-gray-600 hover:bg-gray-50 md:text-sm lg:text-sm "
                                }
                                value={region.name}
                                key={index}
                              >
                                {region.name}
                              </option>
                            ))}
                          </select>
                        </FieldWrapper>
                        <FieldWrapper
                          name="plan"
                          label="Scheme"
                          className={`max-w-xs flex-shrink flex-grow ${
                            active === "Batched Claims" ? "hidden" : "block"
                          }`}
                          labelClassName="font-semibold text-gray-800"
                        >
                          <select
                            name="plan"
                            id="plan"
                            style={selectStyle}
                            placeholder="Select Scheme"
                            onChange={(e) => handlePlanSelected(e)}
                            className="btn mr-6 w-full justify-between border-gray-200 bg-white text-[14px] uppercase text-gray-500 hover:border-gray-300 hover:text-gray-600 md:text-sm lg:text-sm"
                          >
                            <option value="" selected disabled hidden>
                              Select Scheme
                            </option>
                            {schemes.map((scheme, index) => (
                              <option
                                className={
                                  "md:text-am mr-6 flex w-full cursor-pointer items-center px-3  py-1 text-[14px] font-medium text-gray-600 hover:bg-gray-50 lg:text-sm "
                                }
                                value={scheme.id}
                                key={index}
                              >
                                {scheme.name}
                              </option>
                            ))}
                          </select>
                        </FieldWrapper>
                        <FieldWrapper
                          name="startDate"
                          className={`max-w-xs flex-shrink flex-grow ${
                            active === "Batched Claims" ? "hidden" : "block"
                          }`}
                          label="Start Date"
                          labelClassName="font-semibold text-gray-800"
                        >
                          <DateInput
                            placeholder={new Date().toISOString().split("T")[0]}
                            maxDate="today"
                            className="w-auto text-sm"
                          />
                        </FieldWrapper>

                        <FieldWrapper
                          name="endDate"
                          className={`max-w-xs flex-shrink flex-grow ${
                            active === "Batched Claims" ? "hidden" : "block"
                          }`}
                          label="End Date"
                          labelClassName="font-semibold text-gray-800"
                        >
                          <DateInput
                            placeholder={new Date().toISOString().split("T")[0]}
                            maxDate="today"
                            className="w-auto text-sm"
                          />
                        </FieldWrapper>
                      </>
                    )}
                  </div>
                </Form>
              </div>
              {((active === "Individual Claims" && selectedOption === "scheme") ||
                selectedOption === "region" ||
                selectedOption === "mainProvider") &&
              active !== "Batched Claims" ? (
                <>
                  <div className="mb-4">
                    <div className="overflow-x-auto bg-white text-gray-600">
                      <table className="w-full px-1">
                        <thead className="text-left">
                          <tr className="bg-gray-100 px-2 py-4 text-base">
                            <th className="whitespace-nowrap px-2 py-1 uppercase ">
                              <input
                                className="form-checkbox"
                                type="checkbox"
                                checked={selectAll}
                                onChange={handleSelectAll}
                                title="Select All"
                              />
                            </th>
                            <th className="whitespace-nowrap py-1  uppercase">Invoice Number</th>
                            <th className="whitespace-nowrap py-1  uppercase  last:pr-8">
                              Invoice Amount
                            </th>
                            <th className="whitespace-nowrap py-1  uppercase  last:pr-8">
                              Payable Amount
                            </th>
                            <th className="py-1  uppercase  last:pr-8">Date Vetted</th>
                            <th className="py-1  uppercase  last:pr-8">Provider</th>
                            <th className="flex items-center justify-center  py-1  uppercase last:pr-8">
                              Vetting Status
                            </th>
                          </tr>
                        </thead>

                        <tbody>
                          {loadingClaimsToBatch ? (
                            <tr>
                              {" "}
                              <td colSpan={6}>
                                {" "}
                                <div className="flex items-center justify-center py-8">
                                  <p className="text-blue-700">Loading...</p>
                                  <LoadingIcon className="h-6 w-6 text-blue-400" />
                                </div>
                              </td>
                            </tr>
                          ) : !list?.length && totalItemsToBatch == 0 ? (
                            <tr>
                              <td colSpan={6}>
                                <Empty message="Claims not Found" />
                              </td>
                            </tr>
                          ) : (
                            <>
                              {list?.map((invoice) => {
                                return (
                                  <ClaimsBatchingTableItem
                                    key={`claim-${invoice.id}`}
                                    id={invoice.id}
                                    invoice={invoice}
                                    handleClick={(e) => handleClick(e, invoice)}
                                    isChecked={isCheck.includes(invoice.id)}
                                  />
                                );
                              })}
                            </>
                          )}
                          {/* {!list?.length ? (
                            <tr>
                              <td colSpan={6}>
                                <Empty message="Claims not Found" />
                              </td>
                            </tr>
                          ) : (
                            ""
                          )}

                          {list?.map((invoice) => {
                            return (
                              <ClaimsBatchingTableItem
                                key={`claim-${invoice.invoice_id}`}
                                id={invoice.invoice_id}
                                invoice={invoice}
                                handleClick={(e) => handleClick(e, invoice)}
                                isChecked={isCheck.includes(invoice.invoice_id)}
                              />
                            );
                          })} */}
                        </tbody>
                      </table>
                    </div>
                  </div>
                  <Pagination3
                    totalElements={claimsToBatchResponse?.data?.totalElements}
                    totalPages={claimsToBatchResponse?.data?.totalPages}
                    pageNumber={claimsToBatchResponse?.data?.pageable?.pageNumber}
                    OnPageNumberClick={handlePaginationChange}
                    // OnSizeChange={handleSizeChange}
                    pageSize={size}
                  />
                </>
              ) : active === "Batched Claims" ? (
                <>
                  <div className="mb-4">
                    <div className="overflow-x-auto bg-white text-gray-600">
                      <table className="ml-8 w-full">
                        <thead className="text-left">
                          <tr className="bg-gray-100 py-5">
                            <th className="whitespace-nowrap  uppercase">BATCH NUMBER</th>
                            <th>REGION</th>

                            <th className="py-1  uppercase  last:pr-8">CREATED DATE</th>
                            <th className="py-1  uppercase  last:pr-8">TOTAL AMOUNT</th>
                            <th className="py-1  uppercase  last:pr-8">CREATED BY </th>
                            <th className="flex items-center justify-center  py-1  uppercase last:pr-8">
                              ACTION
                            </th>
                          </tr>
                        </thead>

                        <tbody>
                          {loadingbatchedInvoices ? (
                            <div className="flex items-center justify-center py-8">
                              <p className="text-blue-700">Loading...</p>
                              <LoadingIcon className="h-6 w-6 text-blue-400" />
                            </div>
                          ) : (
                            ""
                          )}
                          {batchedList?.map((batch) => {
                            return (
                              <>
                                <tr className="mb-4 ml-6 mt-4" key={batch.id}>
                                  <td className=" pb-4 pl-5 pt-4 ">BATCH-00{batch?.id}</td>
                                  <td>{batch?.region}</td>
                                  <td>
                                    {Intl.DateTimeFormat("en-GB").format(
                                      new Date(batch?.createdOn?.split(" ")[0]),
                                    )}
                                  </td>
                                  <td>{formatValue(batch?.totalAmount)}</td>
                                  <td>{batch?.actionedBy}</td>
                                  <td>
                                    <BatchingTableDownload
                                      batchId={batch?.id}
                                      payerId={payerId}
                                      userFullNames={userFullNames}
                                    />
                                  </td>
                                </tr>
                                <div className="flex items-center">
                                  {/* <div
                                    className={`transform text-slate-400 hover:text-slate-500 ${
                                      openInvoice === batch.id
                                        ? true && "rotate-180"
                                        : "rotate-180"
                                    }`}
                                    aria-expanded={openInvoice}
                                    onClick={() => handleSetOpenBatch(batch.id)}
                                    aria-controls={`batchinvoices-${batch.id}`}
                                  >
                                    <span className="sr-only">Menu</span>
                                    <svg className="h-8 w-8 fill-current" viewBox="0 0 32 32">
                                      <path d="M16 20l-5.4-5.4 1.4-1.4 4 4 4-4 1.4 1.4z" />
                                    </svg>
                                  </div> */}
                                </div>
                                {batch?.invoices?.map((invoice, index) => (
                                  <tr
                                    id={`batch-${batch.id}`}
                                    className={`${openBatch === batch.id ? true : "hidden"}  py-1 `}
                                  >
                                    <td className="flex items-center justify-center pb-4 pl-5 pt-4  text-xs"></td>
                                    <td className=" text-xs">{invoice.invoiceNumber}</td>
                                    <td className=" text-xs">{formatValue(invoice.totalAmount)}</td>
                                    <td className=" text-xs">
                                      {Intl.DateTimeFormat("en-GB").format(
                                        new Date(invoice?.createdAt?.split(" ")[0]),
                                      )}
                                    </td>
                                    <td className=" text-xs">{batch?.region}</td>
                                    <td className=" text-xs">{invoice.vettingStatus}</td>
                                  </tr>
                                ))}
                              </>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>
                  </div>
                  <Pagination2
                    totalElements={batchedInvoicesResponse?.data?.totalElements}
                    totalPages={batchedInvoicesResponse?.data?.totalPages}
                    pageNumber={batchedInvoicesResponse?.data?.pageable.pageNumber}
                    OnPageNumberClick={handlePaginationChange}
                  />
                </>
              ) : (
                <div className="mb-4">
                  <div className="overflow-x-auto bg-white text-gray-600">
                    <div className="flex w-full justify-center">
                      {/* SVG Icon */}
                      <Reading />
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </main>
      </div>
      <ModalSmall
        id="Batch-modal"
        modalOpen={batchClaimsModalOpen}
        setModalOpen={setBatchClaimsModalOpen}
        title="Batch Claims"
      >
        <BatchClaimsModal
          open={batchClaimsModalOpen}
          invoiceIds={isCheck}
          //  region={payerRegions.find((x) => x.id == regionSelected)?.name}
          region={regionSelected}
          claimSum={claimSum}
          onClose={() => setBatchClaimsModalOpen(false)}
          //   onBatch={() => resetUIAfterBatching()}
          planId={planSelected}
          providerIds={Number(form?.provider?.value)}
          startDate={String(form.startDate)}
          endDate={String(form.endDate)}
          isSelectAll={selectAll}
          onBatch={() => {
            setIsCheck([]);
            setSelectAll(false);
            setClaimSum(0);
            setRefreshListAfterBatching(true);
          }}
        ></BatchClaimsModal>
      </ModalSmall>
    </div>
  );
};
const mapDispatchToProps = (dispatch) => ({
  getClaimsToBatch: (
    payerId,
    providerId,
    query,
    offSetValue,
    size,
    page,
    planSelected,
    regionSelected,
    startDate,
    endDate,
  ) =>
    dispatch(
      getClaimsToBatch(
        payerId,
        providerId,
        query,
        offSetValue,
        size,
        page,
        planSelected,
        regionSelected,
        startDate,
        endDate,
      ),
    ),

  getBatchedInvoices: (payerId, query, offSetValue) =>
    dispatch(getBatchedInvoices(payerId, query, offSetValue)),
});
export default connect(null, mapDispatchToProps)(ClaimsBatchingTable);
