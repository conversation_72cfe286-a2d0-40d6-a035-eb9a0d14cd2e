// import React, { FC, useEffect, useState } from "react";
import { ChangeEvent, FC, DragEvent, useEffect, useState } from "react";
import { useFieldArray, useFormContext } from "react-hook-form";
import ReactSelect from "react-select";
import { toast } from "react-toastify";
import { DocumentType, documentTypeLabels } from "~lib/api/types";
import { MAX_FILE_SIZE, baseUrl } from "~lib/constants";
import { clsx, formatBytes, isErrorWithMessage } from "~lib/utils";
import { upload } from "~lib/utils/uploadFile";

interface Props {
  open: boolean;
  onClose: () => void;
  claimId?: any;
}

const allowedMimeTypes = new Map<string, string>([]);
export const SupportDocumentsModal: FC<Props> = (props) => {
  return (
    <div>
      <div className="space-y-4 p-5 px-10 pb-1 pt-4">
        <div className=" grid-col-3 grid gap-4">
          <div className="col-span-1"></div>
          <div className="col-span-1">
            <div className="flex ">
              <label htmlFor="bNo" className="italic">
                Feel Free to provide proof of payment by uploading the document here...
              </label>
            </div>
            <div className="font-bold">Upload and Attach Files </div>
            <div className=" justify-center rounded-md border border-gray-200 p-10">
              <div className="grid w-full place-items-center ">
                <svg
                  width="130"
                  height="130"
                  viewBox="0 0 130 130"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <rect x="5" y="5" width="120" height="120" rx="28" fill="#F3F4F6" />
                  <rect
                    x="5"
                    y="5"
                    width="120"
                    height="120"
                    rx="28"
                    stroke="#F9FAFB"
                    stroke-width="10"
                  />
                  <path
                    d="M75.0016 75L65.0016 65M65.0016 65L55.0016 75M65.0016 65V87.5M85.9766 80.975C88.415 79.6456 90.3412 77.5422 91.4513 74.9965C92.5614 72.4509 92.7921 69.608 92.1071 66.9167C91.4221 64.2253 89.8603 61.8387 87.6683 60.1336C85.4762 58.4284 82.7788 57.5018 80.0016 57.5H76.8516C76.0949 54.5731 74.6845 51.8558 72.7265 49.5524C70.7684 47.2491 68.3137 45.4196 65.5469 44.2015C62.78 42.9834 59.773 42.4084 56.7519 42.5197C53.7308 42.631 50.7743 43.4257 48.1046 44.8441C45.4348 46.2625 43.1214 48.2677 41.3382 50.7089C39.5549 53.1501 38.3483 55.9638 37.8091 58.9384C37.2698 61.9131 37.4119 64.9713 38.2246 67.8831C39.0374 70.7949 40.4997 73.4846 42.5016 75.75"
                    stroke="#4B5563"
                    stroke-width="1.66667"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <div>Click to upload or Drag and drop</div>
                <div className="text-gray-500">SVG , PNG, JPG or GIF(max 800 x 400px)</div>
                <label className="cursor-pointer px-2 text-blue-400 underline hover:text-blue-500">
                  browse
                  <input
                    type="file"
                    name="documents"
                    id="documents"
                    className="hidden"
                    multiple={true}
                    accept={Array.from(allowedMimeTypes.keys()).join(",")}
                    // onChange={handleChange}
                    onClick={(e: React.MouseEvent<HTMLInputElement>) => {
                      if (e.target instanceof HTMLInputElement) {
                        // Reset the input so that the onChange handler runs even when uploading the same file
                        e.target.value = "";
                      }
                    }}
                  />
                </label>
              </div>
            </div>
          </div>
          <div className="col-span-1"></div>
        </div>
        <div className="mt-10 flex items-center justify-between py-10 pt-10"></div>
      </div>
    </div>
  );
};

export default SupportDocumentsModal;
