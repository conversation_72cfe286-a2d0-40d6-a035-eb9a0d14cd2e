import { XMarkIcon } from "@heroicons/react/24/outline";
import { skipToken } from "@reduxjs/toolkit/query";
import { Fragment, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import {
  useBillVisitMutation,
  useGetBeneficiaryProviderBenefitsQuery,
  useGetVisitQuery,
} from "~lib/api";
import { BillVisitRequest } from "~lib/api/schema";
import {
  VisitStatus,
  VisitType,
  normalizedVisitTypeLabels,
  visitStatusLabels,
} from "~lib/api/types";
import { Confirm, DatePicker, Empty, FieldWrapper, Input } from "~lib/components";
import ErrorMessage from "~lib/components/Error";
import FieldSet from "~lib/components/FieldSet";
import { Form } from "~lib/components/Form";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import {
  capitalize,
  clsx,
  formatDateGB,
  formatDateISO,
  formatMoney,
  isErrorWithMessage,
  isFetchBaseQueryError,
  queryError,
  responseError,
  truncate,
  truncateDecimals,
} from "~lib/utils";
import UserService from "../../services/UserService";
import { BillingInputs, BillingInvoiceInput } from "./types";

interface Params extends Record<string, string> {
  id: string;
}

const EMPTY_LINE = "\u00A0";

// Arbitrary max size
const MAX_INVOICES_PER_VISIT = 12;

type Inputs = BillingInputs;

/**
 * TODO: Navigate to Active Visits page after billing or on back button
 */
const BillVisit = () => {
  const [showConfirm, setShowConfirm] = useState(false);

  const navigate = useNavigate();
  const params = useParams<Params>();

  const { id: idRaw } = params;

  const id = idRaw ? parseInt(idRaw) : undefined;

  const {
    data: visit,
    isLoading: isVisitLoading,
    error: visitError,
  } = useGetVisitQuery(id ? { id } : skipToken);

  const beneficiaryId = visit?.beneficiaryId;
  const providerId = visit?.hospitalProviderId;

  const {
    data: beneficiaryProviderBenefits,
    isLoading: isBeneficiaryProviderBenefitLoading,
    error: beneficiaryProviderBenefitError,
  } = useGetBeneficiaryProviderBenefitsQuery(
    providerId && beneficiaryId ? { providerId, beneficiaryId } : skipToken,
    {
      refetchOnFocus: true,
    },
  );

  const benefit = beneficiaryProviderBenefits?.data.find(
    (beneficiaryBenefit) => beneficiaryBenefit.benefitId == visit?.benefitId,
  );

  const maxInvoices = MAX_INVOICES_PER_VISIT;

  const visitActive = visit?.status == VisitStatus.ACTIVE;

  const defaultInvoice: BillingInvoiceInput = {
    date: undefined,
    invoiceNumber: "",
    amount: 0,
  };

  const methods = useForm<Inputs>({
    mode: "onBlur",
    defaultValues: {
      // Add one line by default
      invoices: [defaultInvoice],
    },
  });

  const { reset, watch, control, formState } = methods;

  const invoiceFields = useFieldArray({
    control,
    name: "invoices",
    rules: {
      required: "Invoice(s) are required",
      minLength: {
        value: 1,
        message: "At least one invoice is required",
      },
      maxLength: {
        value: maxInvoices,
        message: "At most one invoice is allowed",
      },
    },
  });

  const form = watch();

  const invoicesError = formState.errors?.["invoices"];

  const [billVisit, { isLoading: isBillVisitLoading }] = useBillVisitMutation();

  const tokenParsed = UserService.getPayer()?.tokenParsed;

  const staffId = tokenParsed?.["sub"];
  const staffName = tokenParsed?.["preferred_username"];

  function handleBack() {
    reset();
    navigate(-1);
  }

  function handleSubmit() {
    setShowConfirm(true);
  }

  const label =
    (visit?.visitType ? normalizedVisitTypeLabels.get(visit?.visitType) : "") || "visit";

  const totalAmount = truncateDecimals(
    form.invoices.reduce((acc, _invoice, index) => acc + (form.invoices[index]?.amount ?? 0), 0),
  );

  async function completeSubmission(form: Inputs) {
    if (!id) {
      throw new Error("Visit number not found");
    }

    if (!visit?.hospitalProviderId) {
      throw new Error("Provider id is invalid");
    }

    if (!staffId) {
      throw new Error("Staff id is invalid");
    }

    try {
      const payload: BillVisitRequest = {
        id,
        invoices: form.invoices.map(({ date, invoiceNumber, amount }) => ({
          invoiceDate: formatDateISO(date),
          invoiceNumber,
          amount,
        })),
        staffId,
        staffName,
        hospitalProviderId: visit?.hospitalProviderId,
        totalInvoiceAmount: totalAmount,
      };

      const response = await billVisit(payload);
      const message = responseError(response);

      if (message) {
        throw new Error(message);
      } else {
        toast.success(`${capitalize(label)} closed successfully`);
        handleBack();
      }
    } catch (error) {
      let message = "Something went wrong. Please try again.";
      if (isFetchBaseQueryError(error) || isErrorWithMessage(error)) {
        message = queryError(error) || message;
      }

      toast.error(message);
      console.error(error);
    }
  }

  interface DisplayFieldProps {
    label: string;
    value: string;
    className?: string;
    isBold?: boolean;
  }

  const DisplayField = ({ label, value, className, isBold }: DisplayFieldProps) => (
    <div className={clsx("flex items-baseline gap-2", className)}>
      <p className="mb-1 text-gray-500">{label}: </p>
      <p className={clsx(isBold && "text-lg font-medium")} title={value}>
        {truncate(value, 30) || EMPTY_LINE}
      </p>
    </div>
  );

  return (
    <main className="h-screen flex-1 bg-white">
      <div className="overflow-y-auto overflow-x-hidden text-gray-600">
        <div className="rounded-lg px-12 py-8 shadow">
          <div className="mb-4">
            <hgroup className="mb-4 flex justify-between gap-2">
              <h1 className="text-xl font-medium text-gray-700">Patient Details</h1>
              <button
                className="text-gray-500 enabled:hover:text-gray-600"
                onClick={() => {
                  handleBack();
                }}
                title="Go Back"
              >
                <XMarkIcon strokeWidth={1.5} className="h-6 w-6" />
              </button>
            </hgroup>

            {isVisitLoading ? (
              <div className="flex items-center justify-center py-8">
                <LoadingIcon className="h-8 w-8 text-blue-400" />
              </div>
            ) : visitError ? (
              <ErrorMessage
                title={queryError(visitError) || "Something wen't wrong"}
                message="Please refresh to retry"
              />
            ) : !visit ? (
              <Empty message="Visit not found" />
            ) : (
              <div className="grid grid-cols-2 gap-2">
                <DisplayField label="Member Name" value={visit?.memberName} />
                <DisplayField label="Member Number" value={visit?.memberNumber} />
                <DisplayField label="Benefit" value={visit?.benefitName} />
                <DisplayField label="Scheme" value={visit?.schemeName ?? ""} />

                <div className="flex items-baseline gap-2">
                  <p className="mb-1 text-gray-500">Balance: </p>

                  {isBeneficiaryProviderBenefitLoading ? (
                    <LoadingIcon className="h-5 w-5" />
                  ) : beneficiaryProviderBenefitError ? (
                    <p className="text-sm text-red-400">Error fetching balance</p>
                  ) : (
                    <p>{formatMoney(benefit?.balance) || EMPTY_LINE}</p>
                  )}
                </div>
              </div>
            )}
          </div>

          <div>
            {/* TODO: Fix label for reimbursements */}
            <h1 className="mb-4 text-xl font-medium capitalize text-gray-700">{label} Details</h1>

            {!visitActive && visit && (
              <p className="mb-4 text-sm text-red-400">
                Visit has status{" "}
                <span className="lowercase">
                  {visitStatusLabels.get(visit?.status) || "not active"}
                </span>
              </p>
            )}

            <Form methods={methods} onSubmit={handleSubmit}>
              <div className="flex justify-end px-2">
                <DisplayField label="Date" value={formatDateGB(new Date())} className="mb-2" />
              </div>

              <FieldSet className="mb-4 py-4" disabled={!visitActive} label="Invoices" required>
                <hgroup className="mb-2 flex flex-col gap-1">
                  <p className="text-xs text-red-500">
                    {invoicesError?.root?.message || <>&#8203;</>}
                  </p>
                </hgroup>

                {invoiceFields.fields.length ? (
                  <table className="table w-full table-auto">
                    <thead>
                      <tr>
                        <th className="px-1 py-2 text-left first:pl-2 last:pr-2">Date</th>
                        <th className="px-1 py-2 text-left first:pl-2 last:pr-2">Invoice Number</th>
                        <th className="px-1 py-2 text-left first:pl-2 last:pr-2">Amount</th>
                        <th className="px-1 py-2 text-left first:pl-2 last:pr-2">Remove</th>
                      </tr>
                    </thead>

                    <tbody>
                      {invoiceFields.fields.map((field, index) => (
                        <Fragment key={field.id}>
                          <tr>
                            <td className="px-1 py-2 first:pl-2 last:pr-2">
                              <FieldWrapper
                                name={`invoices.${index}.date`}
                                className="w-36 min-w-0 grow"
                                fieldError={invoicesError?.[index]?.date?.message}
                                required
                              >
                                <DatePicker
                                  max={formatDateISO(new Date())}
                                  {...(visit?.visitType == VisitType.REIMBURSEMENT &&
                                    benefit?.startDate && {
                                      min: benefit.startDate,
                                    })}
                                />
                              </FieldWrapper>
                            </td>
                            <td className="px-1 py-2 first:pl-2 last:pr-2">
                              <FieldWrapper
                                name={`invoices.${index}.invoiceNumber`}
                                label=""
                                className="min-w-0 grow"
                                fieldError={invoicesError?.[index]?.invoiceNumber?.message}
                                required
                              >
                                <Input
                                  type="text"
                                  options={{
                                    minLength: {
                                      value: 1,
                                      message: "Invoice number must be longer than 1 character",
                                    },
                                    validate: {
                                      length: (value: string) => {
                                        if (value.trim().length < 3) {
                                          return "Invoice number must be longer than 3 characters";
                                        }
                                        return true;
                                      },
                                      unique: (value: string) => {
                                        if (
                                          value &&
                                          form.invoices.some(
                                            (invoice, i) =>
                                              value &&
                                              invoice.invoiceNumber &&
                                              invoice.invoiceNumber == value &&
                                              index != i,
                                          )
                                        ) {
                                          return "Invoice number must be unique";
                                        }

                                        return true;
                                      },
                                    },
                                  }}
                                />
                              </FieldWrapper>
                            </td>
                            <td className="px-1 py-2 first:pl-2 last:pr-2">
                              <FieldWrapper
                                name={`invoices.${index}.amount`}
                                label=""
                                className="grow"
                                fieldError={invoicesError?.[index]?.amount?.message}
                                required
                              >
                                <Input
                                  type="number"
                                  options={{
                                    min: {
                                      value: 1,
                                      message: "Amount must be greater than 0",
                                    },
                                  }}
                                />
                              </FieldWrapper>
                            </td>
                            <td className="px-1 py-2 first:pl-2 last:pr-2">
                              <div className="flex flex-col gap-1">
                                <button
                                  type="button"
                                  onClick={() => invoiceFields.remove(index)}
                                  className="flex justify-center gap-2 rounded px-4 py-2 font-medium text-red-400 enabled:hover:text-red-500 disabled:cursor-not-allowed disabled:opacity-60"
                                  title="Remove"
                                  disabled={invoiceFields.fields.length < 2}
                                >
                                  <XMarkIcon className="h-6 w-6" />
                                </button>
                                {/* Filler element */}
                                <span>&#8203;</span>
                              </div>
                            </td>
                          </tr>

                          {/* Row error */}
                          {invoicesError?.[index]?.message && (
                            <tr>
                              <td colSpan={4} className="px-2 py-2 text-xs text-red-500">
                                {invoicesError?.[index]?.message}
                              </td>
                            </tr>
                          )}
                        </Fragment>
                      ))}
                    </tbody>
                  </table>
                ) : (
                  <div>
                    <Empty message="No invoices added" />
                  </div>
                )}

                <div className="flex justify-end">
                  <button
                    type="button"
                    onClick={() => invoiceFields.append(defaultInvoice)}
                    className="flex gap-2 rounded border border-blue-500 px-4 py-2 font-medium text-blue-500 enabled:hover:border-blue-600 enabled:hover:text-blue-600 disabled:cursor-not-allowed disabled:opacity-60"
                    disabled={invoiceFields.fields.length >= maxInvoices}
                  >
                    Add Invoice
                  </button>
                </div>
              </FieldSet>

              <div className="col-span-2 flex justify-end gap-2">
                <button
                  type="reset"
                  className="rounded border border-gray-200 px-4 py-2 font-medium text-gray-500 enabled:hover:border-gray-300 disabled:opacity-80"
                  onClick={() => {
                    reset();
                  }}
                  disabled={!visitActive}
                >
                  Reset
                </button>

                <button
                  type="submit"
                  className={clsx(
                    "flex gap-2 rounded bg-blue-500 px-4 py-2 font-medium text-white enabled:hover:bg-blue-700 disabled:opacity-80",
                  )}
                  disabled={isBillVisitLoading || !visitActive}
                >
                  {isBillVisitLoading && <LoadingIcon className="text-white" />}
                  <span>Submit</span>
                </button>
              </div>
            </Form>
          </div>
        </div>
      </div>

      <Confirm
        handleConfirm={async (ok) => {
          setShowConfirm(false);
          if (ok) {
            await completeSubmission(form);
          }
        }}
        modalOpen={showConfirm}
      >
        <div className="text-gray-600">
          <table className="table w-full table-auto">
            <thead>
              <tr>
                <th className="px-1 py-2 text-left first:pl-2 last:pr-2">Invoice Number</th>
                <th className="px-1 py-2 text-left first:pl-2 last:pr-2">Amount</th>
              </tr>
            </thead>

            <tbody>
              {invoiceFields.fields.map((field, index) => (
                <tr key={field.id}>
                  <td className="px-1 py-2 first:pl-2 last:pr-2">
                    {form.invoices[index]?.invoiceNumber}
                  </td>
                  <td className="px-1 py-2 first:pl-2 last:pr-2">
                    {formatMoney(form.invoices[index]?.amount)}
                  </td>
                </tr>
              ))}
              <tr>
                {/* TODO: Update colspan when showing copay */}
                <th className="px-1 py-2 text-left first:pl-2 last:pr-2" colSpan={1}>
                  Net Total
                </th>
                <th className="px-1 py-2 text-left first:pl-2 last:pr-2">
                  {formatMoney(totalAmount)}
                </th>
              </tr>
            </tbody>
          </table>
        </div>
      </Confirm>
    </main>
  );
};

export default BillVisit;
