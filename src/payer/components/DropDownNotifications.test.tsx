import { fireEvent, render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import DropdownNotifications from "../components/DropdownNotifications";
import { store } from "../store";

describe("DropdownNotifications Component", () => {
  test("renders the dropdown button", () => {
    render(
      <Provider store={store}>
        <DropdownNotifications align="right" notifications={[]} />
      </Provider>,
    );

    expect(screen.getByRole("button", { name: /notifications/i })).toBeInTheDocument();
  });

  test("toggles dropdown on button click", () => {
    render(
      <Provider store={store}>
        <DropdownNotifications align="right" notifications={[]} />
      </Provider>,
    );

    const button = screen.getByRole("button", { name: /notifications/i });
    fireEvent.click(button);

    expect(screen.getAllByText(/notifications/i).length).toBe(2);
  });

  test("displays notifications when present", () => {
    const notifications = [
      JSON.stringify({ requestType: "New Message" }),
      JSON.stringify({ requestType: "System Update" }),
    ];

    render(
      <Provider store={store}>
        <DropdownNotifications align="right" notifications={notifications} />
      </Provider>,
    );

    fireEvent.click(screen.getByRole("button"));

    expect(screen.getByText("New Message")).toBeInTheDocument();
    expect(screen.getByText("System Update")).toBeInTheDocument();
  });

  test("removes notifications when remove button is clicked", () => {
    const notifications = [JSON.stringify({ requestType: "Alert" })];
    render(
      <Provider store={store}>
        <DropdownNotifications align="right" notifications={notifications} />
      </Provider>,
    );

    fireEvent.click(screen.getByRole("button"));
    fireEvent.click(screen.getByText(/remove/i));

    expect(screen.queryByText("Alert")).not.toBeInTheDocument();
  });
});
