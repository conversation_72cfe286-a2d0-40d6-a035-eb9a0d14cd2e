import React, { useState, useRef, useEffect } from "react";
import { connect, useSelector } from "react-redux";
import { Link } from "react-router-dom";
import Transition from "../lib/Transition";
import { RootState } from "../store";

function DropdownNotifications({ align, notifications }) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [notificationIcon, setNotificationIcon] = useState(false);
  const [notificationMenu, setNotificationMenu] = useState(false);

  let finalArray = [];
  const trigger = useRef(null);
  const dropdown = useRef(null);

  // close on click outside
  useEffect(() => {
    const clickHandler = ({ target }) => {
      if (!dropdown.current) return;
      if (
        !dropdownOpen ||
        dropdown.current.contains(target) ||
        trigger.current.contains(target)
      )
        return;
      setDropdownOpen(false);
    };
    document.addEventListener("click", clickHandler);
    return () => document.removeEventListener("click", clickHandler);
  });
  if (notifications != null) {
    finalArray = notifications.map(function (obj) {
      return JSON.parse(obj);
    });
  }

  // close if the esc key is pressed
  useEffect(() => {
    const keyHandler = ({ keyCode }) => {
      if (!dropdownOpen || keyCode !== 27) return;
      setDropdownOpen(false);
    };
    document.addEventListener("keydown", keyHandler);
    return () => document.removeEventListener("keydown", keyHandler);
  });
  // console.log(notifications);
  useEffect(() => {
    finalArray.length > 0
      ? setNotificationIcon(true)
      : setNotificationIcon(false);

    finalArray.length > 0
      ? setNotificationMenu(true)
      : setNotificationMenu(false);

    console.log(notificationIcon);
    console.log(finalArray.length);
  }, [notifications]);

  const handleOnlistClick = (e) => {
    e.preventDefault();
    console.log("first");
    setNotificationIcon(false);
    notifications = [];
  };
  const handleOnBtnClick = (e) => {
    e.preventDefault();
    console.log("first");
    setNotificationIcon(false);
    setNotificationMenu(false);
  };

  console.log(finalArray);

  return (
    <div className="relative inline-flex">
      <button
        ref={trigger}
        className={`w-8 h-8 flex items-center justify-center bg-gray-100 hover:bg-gray-200 transition duration-150 rounded-full ${
          dropdownOpen && "bg-gray-200"
        }`}
        aria-haspopup="true"
        onClick={() => setDropdownOpen(!dropdownOpen)}
        aria-expanded={dropdownOpen}
      >
        <span className="sr-only">Notifications</span>
        <svg
          className="w-4 h-4"
          viewBox="0 0 16 16"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            className="fill-current text-gray-500"
            d="M6.5 0C2.91 0 0 2.462 0 5.5c0 1.075.37 2.074 1 2.922V12l2.699-1.542A7.454 7.454 0 006.5 11c3.59 0 6.5-2.462 6.5-5.5S10.09 0 6.5 0z"
          />
          <path
            className="fill-current text-gray-400"
            d="M16 9.5c0-.987-.429-1.897-1.147-2.639C14.124 10.348 10.66 13 6.5 13c-.103 0-.202-.018-.305-.021C7.231 13.617 8.556 14 10 14c.449 0 .886-.04 1.307-.11L15 16v-4h-.012C15.627 11.285 16 10.425 16 9.5z"
          />
        </svg>
        {notificationIcon ? (
          <div className="absolute top-0 right-0 w-2.5 h-2.5 bg-red-500 border-2 border-white rounded-full"></div>
        ) : (
          <div className="absolute top-0 right-0 w-2.5 h-2.5 bg-gray-50 border-2 border-white rounded-full"></div>
        )}
      </button>

      <Transition
        appear={true}
        className={`origin-top-right z-10 absolute top-full -mr-48 sm:mr-0 min-w-80 bg-white border border-gray-200 py-1.5 rounded shadow-lg overflow-hidden mt-1 ${
          align === "right" ? "right-0" : "left-0"
        }`}
        show={dropdownOpen}
        enter="transition ease-out duration-200 transform"
        enterStart="opacity-0 -translate-y-2"
        enterEnd="opacity-100 translate-y-0"
        leave="transition ease-out duration-200"
        leaveStart="opacity-100"
        leaveEnd="opacity-0"
      >
        {notificationMenu ? (
          <div
            ref={dropdown}
            onFocus={() => setDropdownOpen(true)}
            onBlur={() => setDropdownOpen(false)}
          >
            <div className="flex justify-between">
              <div className="text-xs font-semibold text-gray-400 uppercase pt-1.5 pb-2 px-4">
                Notifications
              </div>
              <div
                className="text-xs btn font-semibold text-gray-400 uppercase pt-1.5 pb-2 px-4"
                onClick={(e) => handleOnBtnClick(e)}
              >
                Remove
              </div>
            </div>

            <ul>
              {finalArray.map((notification, index) => (
                <li
                  key={index}
                  className="border-b border-dotted last:border-0"
                  onClick={(e) => handleOnlistClick(e)}
                >
                  <div className="text-sm px-3">
                    <span className="font-normal text-gray-800">
                      {notification.requestType}
                    </span>{" "}
                    <span className="block">
                      {/* {notification.schemeName + " has a new Update"} */}
                    </span>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <div
            ref={dropdown}
            onFocus={() => setDropdownOpen(true)}
            onBlur={() => setDropdownOpen(false)}
          >
            <div className="flex justify-between">
              <div className="text-xs font-semibold text-gray-400 uppercase pt-1.5 pb-2 px-4">
                Notifications
              </div>
            </div>

            <ul></ul>
          </div>
        )}
      </Transition>
    </div>
  );
}
export default connect()(DropdownNotifications);
