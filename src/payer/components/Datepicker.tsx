import React from "react";
import Flatpickr from "react-flatpickr";

function Datepicker() {
  const options = {
    mode: "single",
    static: true,
    monthSelectorType: "static",
    dateFormat: "j/m/y",
    defaultDate: [new Date()],
    prevArrow:
      '<svg class="fill-current" width="7" height="11" viewBox="0 0 7 11"><path d="M5.4 10.8l1.4-1.4-4-4 4-4L5.4 0 0 5.4z" /></svg>',
    nextArrow:
      '<svg class="fill-current" width="7" height="11" viewBox="0 0 7 11"><path d="M1.4 10.8L0 9.4l4-4-4-4L1.4 0l5.4 5.4z" /></svg>',
  };

  return (
    <div className="relative">
      <Flatpickr
        className="form-input w-60 pl-9 font-medium text-gray-500 hover:text-gray-600 focus:border-gray-300"
        options={options}
      />
      <div className="pointer-events-none absolute inset-0 right-auto flex items-center">
        <svg className="ml-3 h-4 w-4 fill-current text-gray-500" viewBox="0 0 16 16" role="img">
          <path d="M15 2h-2V0h-2v2H9V0H7v2H5V0H3v2H1a1 1 0 00-1 1v12a1 1 0 001 1h14a1 1 0 001-1V3a1 1 0 00-1-1zm-1 12H2V6h12v8z" />
        </svg>
      </div>
    </div>
  );
}

export default Datepicker;
