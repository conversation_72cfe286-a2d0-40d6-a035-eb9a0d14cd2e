import React from "react";

export default function CsvFileIcon() {
  return (
    <svg
      width="71"
      height="71"
      viewBox="0 0 71 71"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      data-testid="csv-icon"
    >
      <rect x="0.515625" y="0.40625" width="70" height="70" fill="url(#pattern0_11146_72223)" />
      <defs>
        <pattern
          id="pattern0_11146_72223"
          patternContentUnits="objectBoundingBox"
          width="1"
          height="1"
        >
          <use xlinkHref="#image0_11146_72223" transform="scale(0.00195313)" />
        </pattern>
        <image
          id="image0_11146_72223"
          width="512"
          height="512"
          xlinkHref="data:image/png;base64,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"
        />
      </defs>
    </svg>
  );
}
