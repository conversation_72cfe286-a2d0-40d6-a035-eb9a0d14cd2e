type Props = {
  width?: number;
  height?: number;
  size?: number;
  fill?: string;
};

export default function DownloadIcon({ width, height, size, fill }: Props) {
  return (
    <svg
      width={size ?? width ?? "20"}
      height={size ?? height ?? "21"}
      viewBox="0 0 20 21"
      fill=""
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.4375 17.7588C2.85734 17.7588 2.30094 17.5283 1.8907 17.1181C1.48047 16.7078 1.25 16.1515 1.25 15.5713V12.4463C1.25 12.1976 1.34877 11.9592 1.52459 11.7834C1.7004 11.6076 1.93886 11.5088 2.1875 11.5088C2.43614 11.5088 2.6746 11.6076 2.85041 11.7834C3.02623 11.9592 3.125 12.1976 3.125 12.4463V15.5713C3.125 15.7438 3.265 15.8838 3.4375 15.8838H16.5625C16.6454 15.8838 16.7249 15.8509 16.7835 15.7923C16.8421 15.7337 16.875 15.6542 16.875 15.5713V12.4463C16.875 12.1976 16.9738 11.9592 17.1496 11.7834C17.3254 11.6076 17.5639 11.5088 17.8125 11.5088C18.0611 11.5088 18.2996 11.6076 18.4754 11.7834C18.6512 11.9592 18.75 12.1976 18.75 12.4463V15.5713C18.75 16.1515 18.5195 16.7078 18.1093 17.1181C17.6991 17.5283 17.1427 17.7588 16.5625 17.7588H3.4375Z"
        fill={fill || "white"}
      />
      <path
        d="M9.06192 9.87004V2.75879C9.06192 2.51015 9.16069 2.27169 9.3365 2.09588C9.51232 1.92006 9.75078 1.82129 9.99942 1.82129C10.2481 1.82129 10.4865 1.92006 10.6623 2.09588C10.8381 2.27169 10.9369 2.51015 10.9369 2.75879V9.87004L13.3994 7.40879C13.4864 7.32179 13.5897 7.25278 13.7034 7.20569C13.817 7.15861 13.9389 7.13437 14.0619 7.13437C14.185 7.13437 14.3068 7.15861 14.4205 7.20569C14.5341 7.25278 14.6374 7.32179 14.7244 7.40879C14.8114 7.49579 14.8804 7.59907 14.9275 7.71275C14.9746 7.82642 14.9988 7.94825 14.9988 8.07129C14.9988 8.19433 14.9746 8.31616 14.9275 8.42983C14.8804 8.5435 14.8114 8.64679 14.7244 8.73379L10.6619 12.7963C10.575 12.8834 10.4717 12.9525 10.358 12.9996C10.2443 13.0467 10.1225 13.071 9.99942 13.071C9.87635 13.071 9.7545 13.0467 9.64082 12.9996C9.52714 12.9525 9.42387 12.8834 9.33692 12.7963L5.27442 8.73379C5.18742 8.64679 5.1184 8.5435 5.07132 8.42983C5.02423 8.31616 5 8.19433 5 8.07129C5 7.94825 5.02423 7.82642 5.07132 7.71275C5.1184 7.59907 5.18742 7.49579 5.27442 7.40879C5.36142 7.32179 5.4647 7.25278 5.57837 7.20569C5.69205 7.15861 5.81388 7.13437 5.93692 7.13437C6.05995 7.13437 6.18179 7.15861 6.29546 7.20569C6.40913 7.25278 6.51242 7.32179 6.59942 7.40879L9.06192 9.87004Z"
        fill={fill || "white"}
      />
    </svg>
  );
}
