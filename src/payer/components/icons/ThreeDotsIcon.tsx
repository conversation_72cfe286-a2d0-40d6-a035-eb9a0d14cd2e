type Props = {
  width?: number;
  height?: number;
};

export default function ThreeDotsIcon({ height = 31, width = 30 }: Props) {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 30 31"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M21 15.6338C21 15.1365 21.1975 14.6596 21.5492 14.308C21.9008 13.9563 22.3777 13.7588 22.875 13.7588C23.3723 13.7588 23.8492 13.9563 24.2008 14.308C24.5525 14.6596 24.75 15.1365 24.75 15.6338C24.75 16.1311 24.5525 16.608 24.2008 16.9596C23.8492 17.3112 23.3723 17.5088 22.875 17.5088C22.3777 17.5088 21.9008 17.3112 21.5492 16.9596C21.1975 16.608 21 16.1311 21 15.6338ZM13.5 15.6338C13.5 15.1365 13.6975 14.6596 14.0492 14.308C14.4008 13.9563 14.8777 13.7588 15.375 13.7588C15.8723 13.7588 16.3492 13.9563 16.7008 14.308C17.0525 14.6596 17.25 15.1365 17.25 15.6338C17.25 16.1311 17.0525 16.608 16.7008 16.9596C16.3492 17.3112 15.8723 17.5088 15.375 17.5088C14.8777 17.5088 14.4008 17.3112 14.0492 16.9596C13.6975 16.608 13.5 16.1311 13.5 15.6338ZM6 15.6338C6 15.1365 6.19754 14.6596 6.54917 14.308C6.90081 13.9563 7.37772 13.7588 7.875 13.7588C8.37228 13.7588 8.84919 13.9563 9.20083 14.308C9.55246 14.6596 9.75 15.1365 9.75 15.6338C9.75 16.1311 9.55246 16.608 9.20083 16.9596C8.84919 17.3112 8.37228 17.5088 7.875 17.5088C7.37772 17.5088 6.90081 17.3112 6.54917 16.9596C6.19754 16.608 6 16.1311 6 15.6338Z"
        fill="#1F2937"
      />
    </svg>
  );
}
