type Props = {
  width?: number;
  height?: number;
  stroke?: string;
  size?: number;
};

export default function UploadCloudIcon({ height, width, stroke = "#304254", size }: Props) {
  return (
    <svg
      width={size ?? width ?? "24"}
      height={size ?? height ?? "25"}
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M18 18.2588C20.25 18.0098 22 16.0978 22 13.7778C22 11.2878 19.985 9.26979 17.5 9.26979H17.478C17.492 9.10446 17.4993 8.93779 17.5 8.76979C17.5008 8.04678 17.3591 7.33069 17.0832 6.66243C16.8072 5.99416 16.4022 5.38681 15.8915 4.87505C15.3808 4.3633 14.7742 3.95716 14.1065 3.67984C13.4388 3.40252 12.723 3.25945 12 3.25879C10.6227 3.25995 9.2958 3.77738 8.2813 4.70895C7.26679 5.64052 6.63834 6.91855 6.52 8.29079M6.52 8.29079C5.28171 8.41158 4.13278 8.98915 3.29715 9.91093C2.46152 10.8327 1.99909 12.0326 2 13.2768C1.99934 14.4307 2.397 15.5494 3.12579 16.444C3.85457 17.3386 4.86982 17.9542 6 18.1868M6.52 8.29079C6.678 8.27546 6.838 8.26779 7 8.26779C8.126 8.26779 9.165 8.64079 10 9.26979M17.478 9.27079C17.3783 10.374 16.9479 11.4213 16.243 12.2758M12 13.2588V21.2588M12 13.2588C11.3 13.2588 9.992 15.2528 9.5 15.7588M12 13.2588C12.7 13.2588 14.008 15.2528 14.5 15.7588"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
