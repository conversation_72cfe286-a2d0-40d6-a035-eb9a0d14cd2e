import React from "react";

export default function ReceiptIcon() {
  return (
    <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M18.5625 7.75879H9.1875C9.0825 7.75879 9 7.67629 9 7.57129V6.44629C9 6.34129 9.0825 6.25879 9.1875 6.25879H18.5625C18.6675 6.25879 18.75 6.34129 18.75 6.44629V7.57129C18.75 7.67629 18.6675 7.75879 18.5625 7.75879ZM9 9.63379C9 9.53433 9.03951 9.43895 9.10983 9.36862C9.18016 9.2983 9.27554 9.25879 9.375 9.25879H15.375C15.4745 9.25879 15.5698 9.2983 15.6402 9.36862C15.7105 9.43895 15.75 9.53433 15.75 9.63379C15.75 9.73325 15.7105 9.82863 15.6402 9.89895C15.5698 9.96928 15.4745 10.0088 15.375 10.0088H9.375C9.27554 10.0088 9.18016 9.96928 9.10983 9.89895C9.03951 9.82863 9 9.73325 9 9.63379ZM9 11.8838C9 11.7843 9.03951 11.689 9.10983 11.6186C9.18016 11.5483 9.27554 11.5088 9.375 11.5088H15.375C15.4745 11.5088 15.5698 11.5483 15.6402 11.6186C15.7105 11.689 15.75 11.7843 15.75 11.8838C15.75 11.9832 15.7105 12.0786 15.6402 12.149C15.5698 12.2193 15.4745 12.2588 15.375 12.2588H9.375C9.27554 12.2588 9.18016 12.2193 9.10983 12.149C9.03951 12.0786 9 11.9832 9 11.8838ZM9.375 13.7588C9.27554 13.7588 9.18016 13.7983 9.10983 13.8686C9.03951 13.939 9 14.0343 9 14.1338C9 14.2332 9.03951 14.3286 9.10983 14.399C9.18016 14.4693 9.27554 14.5088 9.375 14.5088H15.375C15.4745 14.5088 15.5698 14.4693 15.6402 14.399C15.7105 14.3286 15.75 14.2332 15.75 14.1338C15.75 14.0343 15.7105 13.939 15.6402 13.8686C15.5698 13.7983 15.4745 13.7588 15.375 13.7588H9.375ZM9 16.3838C9 16.2843 9.03951 16.1889 9.10983 16.1186C9.18016 16.0483 9.27554 16.0088 9.375 16.0088H15.375C15.4745 16.0088 15.5698 16.0483 15.6402 16.1186C15.7105 16.1889 15.75 16.2843 15.75 16.3838C15.75 16.4832 15.7105 16.5786 15.6402 16.649C15.5698 16.7193 15.4745 16.7588 15.375 16.7588H9.375C9.27554 16.7588 9.18016 16.7193 9.10983 16.649C9.03951 16.5786 9 16.4832 9 16.3838ZM16.875 9.25879C16.7755 9.25879 16.6802 9.2983 16.6098 9.36862C16.5395 9.43895 16.5 9.53433 16.5 9.63379C16.5 9.73325 16.5395 9.82863 16.6098 9.89895C16.6802 9.96928 16.7755 10.0088 16.875 10.0088H18.375C18.4745 10.0088 18.5698 9.96928 18.6402 9.89895C18.7105 9.82863 18.75 9.73325 18.75 9.63379C18.75 9.53433 18.7105 9.43895 18.6402 9.36862C18.5698 9.2983 18.4745 9.25879 18.375 9.25879H16.875ZM16.5 11.8838C16.5 11.7843 16.5395 11.689 16.6098 11.6186C16.6802 11.5483 16.7755 11.5088 16.875 11.5088H18.375C18.4745 11.5088 18.5698 11.5483 18.6402 11.6186C18.7105 11.689 18.75 11.7843 18.75 11.8838C18.75 11.9832 18.7105 12.0786 18.6402 12.149C18.5698 12.2193 18.4745 12.2588 18.375 12.2588H16.875C16.7755 12.2588 16.6802 12.2193 16.6098 12.149C16.5395 12.0786 16.5 11.9832 16.5 11.8838ZM16.875 13.7588C16.7755 13.7588 16.6802 13.7983 16.6098 13.8686C16.5395 13.939 16.5 14.0343 16.5 14.1338C16.5 14.2332 16.5395 14.3286 16.6098 14.399C16.6802 14.4693 16.7755 14.5088 16.875 14.5088H18.375C18.4745 14.5088 18.5698 14.4693 18.6402 14.399C18.7105 14.3286 18.75 14.2332 18.75 14.1338C18.75 14.0343 18.7105 13.939 18.6402 13.8686C18.5698 13.7983 18.4745 13.7588 18.375 13.7588H16.875ZM16.5 16.3838C16.5 16.2843 16.5395 16.1889 16.6098 16.1186C16.6802 16.0483 16.7755 16.0088 16.875 16.0088H18.375C18.4745 16.0088 18.5698 16.0483 18.6402 16.1186C18.7105 16.1889 18.75 16.2843 18.75 16.3838C18.75 16.4832 18.7105 16.5786 18.6402 16.649C18.5698 16.7193 18.4745 16.7588 18.375 16.7588H16.875C16.7755 16.7588 16.6802 16.7193 16.6098 16.649C16.5395 16.5786 16.5 16.4832 16.5 16.3838Z"
        fill="#304254"
      />
      <path
        d="M21.75 3.51775C21.75 2.04325 20.0422 1.3285 18.9788 2.22625C18.6225 1.92443 18.1707 1.75879 17.7037 1.75879C17.2368 1.75879 16.785 1.92443 16.4288 2.22625C16.0725 1.92443 15.6207 1.75879 15.1537 1.75879C14.6868 1.75879 14.235 1.92443 13.8787 2.22625C13.5218 1.92397 13.069 1.75839 12.6012 1.75907C12.1334 1.75976 11.6811 1.92667 11.325 2.23C10.9697 1.92733 10.5185 1.76048 10.0517 1.7591C9.58498 1.75773 9.13286 1.92193 8.77575 2.2225C7.6305 1.24 6 2.161 6 3.51775V18.2553H4.455C4.065 18.2553 3.75 18.5703 3.75 18.9603V21.6303C3.75 22.1275 3.94754 22.6044 4.29917 22.9561C4.65081 23.3077 5.12772 23.5053 5.625 23.5053L11.625 23.509L11.6415 23.5053H18.4575C19.3307 23.5053 20.1682 23.1584 20.7856 22.5409C21.4031 21.9234 21.75 21.086 21.75 20.2128V3.51775ZM18.375 22.0053C17.8777 22.0053 17.4008 21.8077 17.0492 21.4561C16.6975 21.1044 16.5 20.6275 16.5 20.1303V18.9603C16.5 18.5703 16.185 18.2553 15.795 18.2553H7.5V3.51775C7.5 3.35275 7.695 3.26275 7.815 3.37525L8.58 4.08775C8.6324 4.13519 8.70057 4.16145 8.77125 4.16145C8.84193 4.16145 8.91009 4.13519 8.9625 4.08775L9.7275 3.38275C9.81458 3.30317 9.92828 3.25904 10.0463 3.25904C10.1642 3.25904 10.2779 3.30317 10.365 3.38275L11.13 4.08775C11.235 4.18525 11.4075 4.18525 11.5125 4.08775L12.285 3.38275C12.3721 3.30317 12.4858 3.25904 12.6038 3.25904C12.7217 3.25904 12.8354 3.30317 12.9225 3.38275L13.6875 4.08775C13.7925 4.18525 13.965 4.18525 14.07 4.08775L14.835 3.38275C14.9221 3.30317 15.0358 3.25904 15.1537 3.25904C15.2717 3.25904 15.3854 3.30317 15.4725 3.38275L16.2375 4.08775C16.3425 4.18525 16.515 4.18525 16.62 4.08775L17.385 3.38275C17.4721 3.30317 17.5858 3.25904 17.7037 3.25904C17.8217 3.25904 17.9354 3.30317 18.0225 3.38275L18.7875 4.08775C18.8385 4.13417 18.9046 4.16051 18.9736 4.16189C19.0425 4.16326 19.1097 4.13959 19.1625 4.09525L19.935 3.38275C20.055 3.27025 20.25 3.35275 20.25 3.51775V20.2128C20.25 20.6882 20.0611 21.1441 19.725 21.4802C19.3888 21.8164 18.9329 22.0053 18.4575 22.0053H18.375Z"
        fill="#304254"
      />
    </svg>
  );
}
