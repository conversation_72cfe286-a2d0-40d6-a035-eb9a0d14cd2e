type Props = {
  fill?: string;
  size?: number;
};
export default function EyeIcon({ fill, size }: Props) {
  return (
    <svg
      width={size ?? "24"}
      height={size ?? "25"}
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M23.1853 11.955C23.1525 11.881 22.3584 10.1194 20.5931 8.3541C18.2409 6.00191 15.27 4.75879 12 4.75879C8.72999 4.75879 5.75905 6.00191 3.40687 8.3541C1.64155 10.1194 0.843741 11.8838 0.814679 11.955C0.772035 12.051 0.75 12.1548 0.75 12.2597C0.75 12.3647 0.772035 12.4685 0.814679 12.5644C0.847491 12.6385 1.64155 14.3991 3.40687 16.1644C5.75905 18.5157 8.72999 19.7588 12 19.7588C15.27 19.7588 18.2409 18.5157 20.5931 16.1644C22.3584 14.3991 23.1525 12.6385 23.1853 12.5644C23.2279 12.4685 23.25 12.3647 23.25 12.2597C23.25 12.1548 23.2279 12.051 23.1853 11.955ZM12 18.2588C9.11437 18.2588 6.59343 17.2097 4.50655 15.1416C3.65028 14.2901 2.92179 13.3191 2.34374 12.2588C2.92164 11.1984 3.65014 10.2274 4.50655 9.37598C6.59343 7.30785 9.11437 6.25879 12 6.25879C14.8856 6.25879 17.4066 7.30785 19.4934 9.37598C20.3514 10.2272 21.0815 11.1982 21.6609 12.2588C20.985 13.5207 18.0403 18.2588 12 18.2588ZM12 7.75879C11.11 7.75879 10.2399 8.02271 9.49993 8.51718C8.7599 9.01164 8.18313 9.71445 7.84253 10.5367C7.50194 11.359 7.41282 12.2638 7.58646 13.1367C7.76009 14.0096 8.18867 14.8114 8.81801 15.4408C9.44735 16.0701 10.2492 16.4987 11.1221 16.6723C11.995 16.846 12.8998 16.7568 13.7221 16.4162C14.5443 16.0757 15.2471 15.4989 15.7416 14.7589C16.2361 14.0188 16.5 13.1488 16.5 12.2588C16.4988 11.0657 16.0242 9.92182 15.1806 9.07818C14.337 8.23453 13.1931 7.76003 12 7.75879ZM12 15.2588C11.4066 15.2588 10.8266 15.0828 10.3333 14.7532C9.83993 14.4236 9.45542 13.955 9.22835 13.4068C9.00129 12.8587 8.94188 12.2555 9.05764 11.6735C9.17339 11.0916 9.45911 10.557 9.87867 10.1375C10.2982 9.71791 10.8328 9.43219 11.4147 9.31643C11.9967 9.20068 12.5999 9.26009 13.148 9.48715C13.6962 9.71421 14.1648 10.0987 14.4944 10.5921C14.824 11.0854 15 11.6654 15 12.2588C15 13.0544 14.6839 13.8175 14.1213 14.3801C13.5587 14.9427 12.7956 15.2588 12 15.2588Z"
        fill={fill ? fill : "#304254"}
      />
    </svg>
  );
}
