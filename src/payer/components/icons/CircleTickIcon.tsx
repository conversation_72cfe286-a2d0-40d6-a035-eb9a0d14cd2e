import React from "react";

type Props = {
  height?: number;
  width?: number;
};
export default function CircleTickIcon({ height, width }: Props) {
  return (
    <svg
      width={width ?? "33"}
      height={height ?? "34"}
      viewBox="0 0 33 34"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M28.7338 18.3511C27.7867 23.3614 24.0667 28.109 18.7881 29.2169C16.2137 29.758 13.5342 29.473 11.1311 28.4026C8.72803 27.3322 6.72392 25.531 5.40412 23.2553C4.08433 20.9796 3.51614 18.3456 3.78045 15.7282C4.04477 13.1108 5.12811 10.6436 6.87624 8.67773C10.4618 4.64358 16.5758 3.47885 21.5967 5.42585"
        stroke="#16A34A"
        strokeWidth="2.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.7135 16.5308L16.7661 21.4776L28.628 8.35145"
        stroke="#16A34A"
        strokeWidth="2.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
