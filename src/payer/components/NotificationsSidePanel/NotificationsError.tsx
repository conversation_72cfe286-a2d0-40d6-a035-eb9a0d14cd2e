type Props = {
  variant: "network" | "other";
};
export default function NotificationsError({ variant }: Props) {
  let title, description;

  switch (variant) {
    case "network":
      title = "Problem connecting";
      description = "Check your internet connection and try again.";
      break;
    default:
      title = "Something went wrong";
      description = "Try refreshing the page or come back later";
  }

  return (
    <div className="flex h-full flex-col items-center justify-center gap-4">
      <p className="text-xl font-medium">{title}</p>
      <p className="text-gray-light">{description}</p>
    </div>
  );
}
