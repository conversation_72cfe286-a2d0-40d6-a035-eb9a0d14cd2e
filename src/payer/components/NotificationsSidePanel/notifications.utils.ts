import { differenceInCalendarDays, isThisMonth, isToday, isYesterday, parseISO } from "date-fns";
import { UserNotification } from "~lib/api/types";
import { BadgeColor } from "../ui/Badge";

export function formatToNotificationTimeStamp(input: string) {
  const date = new Date(input);

  const options: Intl.DateTimeFormatOptions = {
    month: "long",
    day: "numeric",
    year: "numeric",
  };

  const datePart = date.toLocaleDateString("en-US", options);

  let hours = date.getHours();
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const ampm = hours >= 12 ? "PM" : "AM";

  hours = hours % 12;
  hours = hours ? hours : 12; // convert 0 to 12
  const timePart = `${hours.toString().padStart(2, "0")}:${minutes} ${ampm}`;

  return `${datePart} at ${timePart}`;
}

export function getNotificationBadgeData(status: UserNotification["refStatus"]): {
  text: string;
  color: BadgeColor;
} {
  switch (status) {
    case "AUTHORIZED":
      return { color: "green", text: "Approved" };
    case "PENDING":
      return { color: "yellow", text: "Pending" };
    case "DRAFT":
      return { color: "yellow", text: "Notification" };
    case "REJECTED":
      return { color: "red", text: "Rejected" };
    default:
      return { color: "gray", text: status || "NA" };
  }
}

export function isPast7Days(date: Date) {
  const today = new Date();
  const diff = differenceInCalendarDays(today, date);
  return diff >= 2 && diff < 7;
}

export function getTimeSlottedNotifications(notifications: UserNotification[]): {
  title: string;
  notifications: UserNotification[];
}[] {
  const today: UserNotification[] = [];
  const yesterday: UserNotification[] = [];
  const thisPast7Days: UserNotification[] = [];
  const thisMonth: UserNotification[] = [];
  const lastMonth: UserNotification[] = [];

  notifications.forEach((notification) => {
    const date =
      typeof notification.createdAt === "string"
        ? parseISO(notification.createdAt)
        : notification.createdAt;

    if (isToday(date)) {
      today.push(notification);
    } else if (isYesterday(date)) {
      yesterday.push(notification);
    } else if (isPast7Days(date)) {
      thisPast7Days.push(notification);
    } else if (isThisMonth(date)) {
      thisMonth.push(notification);
    } else {
      lastMonth.push(notification);
    }
  });

  const allSlots = [
    { title: "Today", notifications: today },
    { title: "Yesterday", notifications: yesterday },
    { title: "Past 7 days", notifications: thisPast7Days },
    { title: "This Month", notifications: thisMonth },
    { title: "Last Month", notifications: lastMonth },
  ];

  return allSlots.filter((slot) => slot.notifications.length > 0);
}

export function checkIsLastItemInSlotsMapping(
  slotsArray: unknown[],
  parentSlotIndex: number,
  parentSlotItems: unknown[],
  itemIndex: number,
): boolean {
  const isInLastSlot = parentSlotIndex === slotsArray.length - 1;
  const isLastItemInParentSlot = itemIndex === parentSlotItems.length - 1;

  return isInLastSlot && isLastItemInParentSlot;
}
