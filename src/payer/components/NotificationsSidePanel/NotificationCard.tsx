import { ChevronRightIcon } from "@heroicons/react/24/outline";
import { forwardRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { UserNotification } from "~lib/api/types";
import { cn } from "~lib/utils/cn";
import { useNotifications } from "../../context/NotificationsContext";
import getTimeBetweenAsString from "../../utils/getTimeBetweenAsString";
import DotIcon from "../icons/DotIcon";
import Avatar from "../ui/Avatar";
import Badge from "../ui/Badge";
import { formatToNotificationTimeStamp, getNotificationBadgeData } from "./notifications.utils";

type Props = {
  notification: UserNotification;
};

const NotificationCard = forwardRef<HTMLElement, Props>(({ notification }, ref) => {
  // state
  const [shouldShowActionButton, setShouldShowActionButton] = useState(false);

  // utility hooks
  const { closeNotificationsPanel, setActiveNotification } = useNotifications();
  const navigate = useNavigate();

  // derived values
  const isActioned = notification.markAsRead;
  const timePassed = getTimeBetweenAsString(notification.createdAt + "Z");
  const notificationTimeStamp = formatToNotificationTimeStamp(notification.createdAt + "Z");

  // functions
  function handleActionButtonClick() {
    setActiveNotification(notification);
    navigate("/preauth");
    closeNotificationsPanel();
  }

  function handleMouseEnterCard() {
    setShouldShowActionButton(true);
  }

  function handleMouseLeaveCard() {
    setShouldShowActionButton(false);
  }

  return (
    <article
      ref={ref}
      onMouseEnter={handleMouseEnterCard}
      onMouseLeave={handleMouseLeaveCard}
      className={cn("flex gap-6  px-8 py-6", !isActioned && "bg-primary-lightest")}
    >
      <Avatar
        className="h-20 w-20 shrink-0"
        fallbackClassName="text-[40px] bg-primary text-white"
        name={notification.triggeredBy}
      />

      <div className="relative flex grow flex-col gap-1">
        <section className="flex items-center justify-between">
          <div className="flex items-center gap-4 overflow-x-hidden">
            <p className=" text-lg font-medium">{notification.triggeredBy}</p>
            <Badge
              textClassName="text-xs"
              className="h-fit rounded-full px-2 py-[2px]"
              hasDot
              {...getNotificationBadgeData(notification.refStatus)}
            />
          </div>

          <div className="flex items-center gap-4 ">
            <p className="text-gray-light">{timePassed}</p>

            {!isActioned && <DotIcon fill="red" height="15" width="15" />}
          </div>
        </section>

        <section className="flex items-center gap-2 overflow-x-hidden">
          <p className="font-medium text-gray-default">{notification.heading}</p>
          <span>·</span>
          <p className="text-sm text-gray-default">{notificationTimeStamp}</p>
        </section>

        <p className="line-clamp-3 text-sm text-gray-light">{notification.body}</p>

        <div
          className={cn(
            "absolute bottom-0 right-0 top-[20%] flex w-1/2 items-center justify-end bg-gradient-to-r transition-opacity duration-300",
            {
              "pointer-events-auto opacity-100": shouldShowActionButton,
              "pointer-events-none opacity-0": !shouldShowActionButton,
              "from-primary-lightest/0 via-primary-lightest to-primary-lightest": !isActioned,
              "from-white/0 via-white to-white": isActioned,
            },
          )}
        >
          <button
            onClick={handleActionButtonClick}
            className="flex h-14 w-14 items-center justify-center rounded-lg border bg-white shadow-md"
          >
            <ChevronRightIcon className="w-6" />
          </button>
        </div>
      </div>
    </article>
  );
});

export default NotificationCard;
