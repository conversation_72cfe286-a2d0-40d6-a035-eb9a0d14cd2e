import { cn } from "~lib/utils/cn";
import { useNotifications } from "../../context/NotificationsContext";
import DoubleTickIcon from "../icons/DoubleTickIcon";

type Props = {
  markedAsRead: false | null;
  changeActiveNotificationFilter(markedAsRead: false | null): void;
  handleMarkAllAsRead(): Promise<void>;
};

export default function NotificationsFilters({
  markedAsRead,
  changeActiveNotificationFilter,
  handleMarkAllAsRead,
}: Props) {
  const { notificationsCount } = useNotifications();

  return (
    <div className="flex gap-8">
      <button
        onClick={() => changeActiveNotificationFilter(null)}
        className={cn("flex h-10 flex-col p-1 text-lg text-gray-500 ", {
          "border-b-2 border-primary text-primary": markedAsRead == null,
        })}
      >
        All
      </button>
      <button
        onClick={() => changeActiveNotificationFilter(false)}
        className={cn("flex h-10 flex-col p-1 text-lg text-gray-500 ", {
          "border-b-2 border-primary text-primary": markedAsRead == false,
        })}
      >
        Unread ({Number(notificationsCount) || "0"})
      </button>

      <button
        onClick={handleMarkAllAsRead}
        className="ml-auto flex items-center gap-1 text-[#374151] disabled:cursor-not-allowed disabled:opacity-50"
        disabled={Number(notificationsCount) < 1}
      >
        <DoubleTickIcon />
        <span>Mark all as read</span>
      </button>
    </div>
  );
}
