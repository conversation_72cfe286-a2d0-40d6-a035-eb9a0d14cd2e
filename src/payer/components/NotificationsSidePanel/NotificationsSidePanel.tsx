import { XMarkIcon } from "@heroicons/react/24/outline";
import { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "react-toastify";
import { useGetEventsNotificationsQuery, useMarkAllNotificationsAsReadMutation } from "~lib/api";
import { UserNotification } from "~lib/api/types";
import { cn } from "~lib/utils/cn";
import { useNotifications } from "../../context/NotificationsContext";
import { handleTryCatchError } from "../../utils/handleTryCatchError";
import BouncingEllipsis from "../animations/BouncingEllipsis";
import EmptyNotifications from "./EmptyNotifications";
import LoadingNotifications from "./LoadingNotifications";
import NotificationCard from "./NotificationCard";
import NotificationsError from "./NotificationsError";
import NotificationsFilters from "./NotificationsFilters";
import { checkIsLastItemInSlotsMapping, getTimeSlottedNotifications } from "./notifications.utils";

const PAGE_SIZE = 6;

export default function NotificationsSidePanel() {
  const { isNotificationsPanelOpen, setIsNotificationsPanelOpen } = useNotifications();

  // notifications filters states
  const [currentPage, setCurrentPage] = useState(1);
  const [markedAsRead, setMarkedAsRead] = useState<false | null>(null);
  const [notifications, setNotifications] = useState<UserNotification[]>([]);

  const observerRef = useRef<IntersectionObserver>();
  const scrollContainerRef = useRef<HTMLElement>(null);

  const {
    data: notificationsPayload,
    isLoading: isLoadingNotifications,
    isFetching: isFetchingNotifications,
    isError: isNotificationsError,
  } = useGetEventsNotificationsQuery(
    {
      pathParameters: { page: currentPage, size: PAGE_SIZE },
      queryParameters: { markAsRead: markedAsRead },
    },
    { skip: !isNotificationsPanelOpen, pollingInterval: 60000 },
  );
  const [markAllNotificationsAsRead] = useMarkAllNotificationsAsReadMutation();

  // derived values
  const shouldShowError = isNotificationsError;
  const notificationSlots = getTimeSlottedNotifications(notifications);
  const totalPages = notificationsPayload?.data.totalPages;

  const setLastNotificationCardRef = useCallback(
    (node: HTMLElement | null) => {
      if (observerRef.current) observerRef.current.disconnect();

      observerRef.current = new IntersectionObserver(
        (entries) => {
          const hasMoreNotifications = currentPage < Number(totalPages);
          const shouldFetchMore = !isFetchingNotifications && hasMoreNotifications;
          if (entries[0]?.isIntersecting && shouldFetchMore) {
            setCurrentPage((prev) => prev + 1);
          }
        },
        {
          root: scrollContainerRef.current,
          threshold: 0.1,
        },
      );

      if (node) observerRef.current.observe(node);
    },
    [currentPage, isFetchingNotifications, totalPages],
  );

  // useEffects
  useEffect(() => {
    const newNotifications = notificationsPayload?.data.content || [];

    setNotifications((prevNotifications) => {
      if (currentPage == 1) return newNotifications;

      const existingIds = new Set(prevNotifications.map((notification) => notification.id));

      // Only append notifications that aren't already in the list
      const filteredNotifications = newNotifications.filter((n) => !existingIds.has(n.id));

      // If nothing new, return previous to avoid triggering re-render
      if (filteredNotifications.length === 0) return prevNotifications;

      return [...prevNotifications, ...filteredNotifications];
    });
  }, [notificationsPayload?.data.content, currentPage]);

  // functions
  function changeActiveNotificationFilter(markedAsRead: false | null) {
    handleResetNotifications();
    setMarkedAsRead(markedAsRead);
  }
  function handleCloseNotificationsPanel() {
    setIsNotificationsPanelOpen(false);
  }

  function handleStopPropagation(e: React.MouseEvent<HTMLElement, MouseEvent>) {
    e.stopPropagation();
  }
  async function handleMarkAllAsRead() {
    const toastId = toast.loading("Marking notifications as read...");
    try {
      setCurrentPage(1);
      await markAllNotificationsAsRead().unwrap();

      toast.success("Marked all notifications as read successfully!");
    } catch (error) {
      handleTryCatchError(error);
    } finally {
      toast.dismiss(toastId);
    }
  }

  function handleResetNotifications() {
    setCurrentPage(1);
  }

  return (
    <div
      className={cn(
        "fixed inset-0 z-[50] bg-black/20 transition-opacity duration-300",
        isNotificationsPanelOpen
          ? "pointer-events-auto opacity-100"
          : "pointer-events-none opacity-0",
      )}
      onClick={handleCloseNotificationsPanel}
    >
      <aside
        className={cn(
          "ml-auto flex h-full w-[45rem] flex-col overflow-hidden bg-white pb-1 pt-7 shadow-lg transition-all duration-300 ",
          isNotificationsPanelOpen ? "translate-x-0" : "translate-x-full",
        )}
        onClick={handleStopPropagation}
      >
        <section className="flex flex-col gap-5 border-b px-10 ">
          <div className="flex items-center justify-between">
            <h3 className="text-gray-darker text-xl font-semibold">Your notifications</h3>
            <button onClick={handleCloseNotificationsPanel}>
              <XMarkIcon className="w-6" />
            </button>
          </div>

          <NotificationsFilters
            markedAsRead={markedAsRead}
            changeActiveNotificationFilter={changeActiveNotificationFilter}
            handleMarkAllAsRead={handleMarkAllAsRead}
          />
        </section>

        <section
          className="flex grow flex-col gap-10 overflow-hidden overflow-y-auto "
          ref={scrollContainerRef}
        >
          {isLoadingNotifications ? (
            <LoadingNotifications />
          ) : shouldShowError ? (
            <NotificationsError variant="other" />
          ) : notifications.length === 0 ? (
            <EmptyNotifications />
          ) : (
            <>
              {notificationSlots.map((slot, slotIndex) => (
                <div key={slot.title}>
                  <div className=" px-10 py-4">
                    <p className="font-medium text-gray-light">{slot.title}</p>
                  </div>
                  {slot.notifications.map((notification, notificationIndex) => (
                    <NotificationCard
                      ref={
                        checkIsLastItemInSlotsMapping(
                          notificationSlots,
                          slotIndex,
                          slot.notifications,
                          notificationIndex,
                        )
                          ? setLastNotificationCardRef
                          : null
                      }
                      key={notification.id}
                      notification={notification}
                    />
                  ))}
                </div>
              ))}

              {isFetchingNotifications && <BouncingEllipsis className="mx-auto" />}
            </>
          )}
        </section>
      </aside>
    </div>
  );
}
