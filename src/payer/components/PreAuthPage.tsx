import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

import { isError } from "util";
import { RootState } from "../store";
import Flatpickr from "react-flatpickr";
import { AsyncPaginate } from "react-select-async-paginate";
import { formatValue } from "../lib/Utils";
import { approvePreAuth, declinePreAuth } from "../store/members/actions";
import UserService from "../services/UserService";
import * as notifications from "../lib/notifications.js";

interface Props {
  visit?: any;
  submitPreAuth?: () => void;
  setModalOpen?: () => void;
}

export const PreAuthPage: React.FC<Props> = (props) => {
  //log(props.member);
  const [preAuthDate, setPreAuthDate] = useState(new Date());
  const [benefitId, setBenefitId] = useState(0);
  const [totalAmount, setTotalAmount] = useState(0);
  const [authorizedAmount, setAuthorizedAmount] = useState(0);
  const [actionPreauth, setActionPreauth] = useState(false);
  const [diagnosisDescription, setDiagnosisDescription] = useState("");
  const [authNotes, setAuthNotes] = useState("");
  const [reason, setReason] = useState("");
  const dispatch = useDispatch();
  const [error, setError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  const regex_exp = /^[+-]?\d*(?:[.,]\d*)?$/;

  const index = 3;
  ///s = s.substring(0, index) + "x" + s.substring(index + 1);
  const options = {
    mode: "single",
    static: true,
    monthSelectorType: "static",
    dateFormat: "Y-m-d",
    defaultDate: null,
    enableTime: false,
    minDate: "today",

    // altInput: true,
    prevArrow:
      '<svg class="fill-current" width="7" height="11" viewBox="0 0 7 11"><path d="M5.4 10.8l1.4-1.4-4-4 4-4L5.4 0 0 5.4z" /></svg>',
    nextArrow:
      '<svg class="fill-current" width="7" height="11" viewBox="0 0 7 11"><path d="M1.4 10.8L0 9.4l4-4-4-4L1.4 0l5.4 5.4z" /></svg>',
  };

  const handleBenefitChange = (e) => {
    console.log(e.target.value);
    setBenefitId(Number(e.target.value.id));
  };
  const handleAuthNotes = (e) => {
    console.log(e.target.value);
    setAuthNotes(e.target.value);
  };
  const handleAddReason = (e) => {
    console.log(e.target.value);
    setReason(e.target.value);
  };
  const handleAmountChange = (e: any) => {
    setAuthorizedAmount(e.target.value.replace(/^[-+]?[0-9]+\.[^0-9]+$/, ""));
  };
  const handleDiagnosisChange = (value, e) => {
    setDiagnosisDescription(value);
    console.log(value);
    console.log(e);
  };
  const payerName = useSelector((state: RootState) => state.payers.payer.name);

  const preAuthReply = {
    id: props.visit?.id,
    amount: authorizedAmount,
    notes: authNotes,
    reason: reason,
    type: "PREAUTH_UPDATE",
    user: UserService.getUsername(),
    organisation: payerName,
    memberNumber: props.visit?.memberNumber,
  };
  const handleApprove = (e: React.MouseEvent) => {
    e.preventDefault();
    setActionPreauth(true);
    dispatch(approvePreAuth(preAuthReply));
    props.setModalOpen();
  };
  const handleDecline = (e: React.MouseEvent) => {
    e.preventDefault();
    setActionPreauth(true);
    dispatch(declinePreAuth(preAuthReply));
    props.setModalOpen();
  };
  const preAuthSubmitted: any = useSelector(
    (state: RootState) => state.memberInfo.preAuthSubmitted,
  );
  const preAuthApprovedError: any = useSelector(
    (state: RootState) => state.memberInfo.preAuthApprovedError,
  );

  useEffect(() => {
    if (actionPreauth && preAuthSubmitted) {
      notifications.Success({
        title: "Pre-Authorization updated Successfully",
      });
    }
    if (actionPreauth && preAuthApprovedError) {
      notifications.Error({
        title: preAuthApprovedError,
      });
    }
  }, [preAuthSubmitted, preAuthApprovedError]);

  useEffect(() => {
    if (!reason) {
      setError(true);
      setErrorMessage("*Add a Reason ");
    } else {
      setError(false);
    }
  }, [reason]);

  return (
    <div className="min-w-2 bg-white p-4">
      <header></header>
      <article>
        <div className="flex justify-center">
          <div className="grid grid-cols-12 gap-1">
            <div className="col-span-1"></div>
            <div className="col-span-4 ">
              <div className=" mb-4 grid grid-cols-2 justify-between">
                <div className="col-span-1 mb-2 block w-full text-sm  font-bold text-gray-700 sm:w-24">
                  Reference Number:
                </div>
                <div className="col-span-1 w-full sm:w-24">{props.visit.reference}</div>
              </div>
              <div className=" mb-4 grid grid-cols-2 justify-between">
                <div className="col-span-1 mb-2 block w-full text-sm font-bold text-gray-700 sm:w-24">
                  Procedure Date:
                </div>
                <div className="col-span-1 w-full sm:w-24">{props.visit.createdAt}</div>
              </div>
              <div className="mb-4 grid grid-cols-2  justify-between">
                <div className="col-span-1 mb-2 block text-sm font-bold text-gray-700">
                  Service:
                </div>
                <div className="col-span-1 w-full sm:w-24">{props.visit.service}</div>
              </div>
              <div className="mb-4 grid grid-cols-2  justify-between">
                <label className="mb-2 block text-sm font-bold text-gray-700">
                  Requested Amount:
                </label>
                <div className="col-span-1 w-full sm:w-24">
                  {formatValue(props.visit.requestAmount)}
                </div>
              </div>
              <div className="mb-4 grid grid-cols-2  justify-between">
                <label className="col-span-1 mb-2 block text-sm font-bold text-gray-700">
                  Member Number:
                </label>
                <div className="col-span-1 w-full sm:w-24">{props.visit.memberNumber}</div>
              </div>
            </div>
            <div className="col-span-1"></div>
            <div className="col-span-4">
              <div className="mb-4 grid grid-cols-2  justify-between">
                <div className="col-span-1 mb-2 block text-sm font-bold text-gray-700">
                  Benefit:
                </div>
                <div className="col-span-1 w-full sm:w-24">{props.visit.benefitName}</div>
              </div>
              <div className="mb-4 grid grid-cols-2  justify-between">
                <label className="col-span-1 mb-2 block text-sm font-bold text-gray-700">
                  Pre- Diagnosis:
                </label>
                <div className="col-span-1 w-full sm:w-24">{props.visit.diagnosis}</div>
              </div>
              <div className="mb-4 grid grid-cols-2  justify-between">
                <label className="col-span-1 mb-2 block text-sm font-bold text-gray-700">
                  Request Type:
                </label>
                <div className="col-span-1 w-full sm:w-24">{props.visit.requestType}</div>
              </div>
              <div className="mb-4 grid grid-cols-2  justify-between">
                <label className="col-span-1 mb-2 block text-sm font-bold text-gray-700">
                  Procedure:
                </label>
                <div>{props.visit.medProcedure}</div>
              </div>
              <div className="mb-4 flex grid-cols-2  justify-between">
                <label className="mb-2 block text-sm font-bold text-gray-700">Notes:</label>
                <div className="col-span-1 w-full sm:w-24">{props.visit.notes}</div>
              </div>
              <div className="mb-4 flex grid-cols-2  justify-between">
                <label className="mb-2 block text-sm font-bold text-gray-700">
                  Approved Amount:
                </label>
                <div className="ml-5 ">
                  <input
                    type="text"
                    maxLength={9}
                    pattern="[+-]?\d+(?:[.,]\d+)?"
                    onChange={(e) => handleAmountChange(e)}
                    name="totalAmount"
                    placeholder="Enter Approved Amount"
                    className="form-input rounded-md sm:w-auto"
                  />
                </div>
              </div>
            </div>

            <div className="col-span-2"></div>
          </div>
        </div>
        <div className="grid grid-cols-11 gap-1">
          <div className="col-span-1"></div>

          <div className="col-span-9">
            <label className="mb-2 block text-sm font-bold text-gray-700">Approval Notes</label>
            <textarea
              className="border-1 focus:shadow-outline w-full appearance-none rounded border border-gray-300 px-3 py-2
       leading-tight text-gray-700 shadow focus:outline-none"
              id="requestType"
              cols={50}
              rows={1}
              required
              value={authNotes}
              onChange={(e) => handleAuthNotes(e)}
            />
          </div>
          <div className="col-span-1"></div>
        </div>
        <div className="grid grid-cols-11 gap-1">
          <div className="col-span-1"></div>

          <div className="col-span-9">
            <label className="mb-2 block text-sm font-bold text-gray-700">
              Reason<span className="text-red-500">*</span>
            </label>
            <textarea
              className="border-1 focus:shadow-outline w-full appearance-none rounded border border-gray-300 px-3 py-2
       leading-tight text-gray-700 shadow focus:outline-none"
              id="reason"
              cols={50}
              rows={2}
              required
              value={reason}
              onChange={(e) => handleAddReason(e)}
            />
          </div>
          <div className="col-span-1"></div>
        </div>
      </article>
      <footer>
        <div className="m-5 mr-10 flex justify-between">
          <button
            className={`mb-2 rounded bg-gray-500 px-10 py-3 text-gray-50 ${
              error ? "bg-gray-500" : "bg-light-blue-900"
            } `}
            onClick={(e) => {
              handleDecline(e);
            }}
            disabled={error}
          >
            Decline
          </button>
          <button
            className={`mb-2 rounded bg-gray-500 px-10 py-3 text-gray-50 ${
              error ? "bg-gray-500" : "bg-light-blue-900"
            } `}
            onClick={(e) => {
              handleApprove(e);
            }}
            disabled={error}
          >
            Approve
          </button>
        </div>
      </footer>
    </div>
  );
};
