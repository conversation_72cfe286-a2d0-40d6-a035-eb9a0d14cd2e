import { useState } from "react";
import { toast } from "react-toastify";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import Text from "../../components/ui/typography/Text";
import Button from "../../components/ui/Button";
import LoadingIcon from "~lib/components/icons/LoadingIcon";

interface DeleteGroupConfirmModalProps {
  show: boolean;
  onClose: () => void;
  groupName: string;
  onDelete: () => Promise<void>;
}

export default function DeleteGroupConfirmModal({
  show,
  onClose,
  groupName,
  onDelete,
}: DeleteGroupConfirmModalProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      await onDelete();
    } catch (error) {
      console.error("Error deleting group:", error);
      toast.error("Failed to delete group. Please try again.");
      setIsDeleting(false);
    }
  };

  return (
    <DialogWrapper show={show} onClose={onClose} maxWidth="max-w-xl">
      <div className="p-6">
        <div className="mb-6">
          <Text variant="paragraph">
            Are you sure you want to delete the group <strong>{groupName}</strong>? This action
            cannot be undone.
          </Text>
          <Text variant="description" className="mt-2">
            Users in this group will lose access to any permissions granted through this group.
          </Text>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outlined" onClick={onClose} disabled={isDeleting}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting}
            className="flex items-center gap-2"
          >
            {isDeleting && <LoadingIcon className="h-4 w-4 text-white" />}
            Delete Group
          </Button>
        </div>
      </div>
    </DialogWrapper>
  );
}
