import { PlusIcon, TrashIcon } from "@heroicons/react/24/outline";
import { useEffect, useRef, useState } from "react";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { UserGroupMemberDto } from "../../lib/types/access-control/role";
import Button from "../ui/Button";
import TableDataItem from "../ui/table/TableDataItem";
import TableHeaderItem from "../ui/table/TableHeaderItem";
import Text from "../ui/typography/Text";
import RemoveUsersConfirmModal from "./remove-users-confirm-modal";
import PrimaryPagination from "../ui/pagination/PrimaryPagination";

interface GroupMembersListProps {
  members?: UserGroupMemberDto[] | undefined;
  isLoading: boolean;
  onAddUsers: () => void;
  onRemoveMultipleUsers: (userIds: string[]) => void;
  groupName?: string | undefined;
  page: number;
  size: number;
  totalElements?: number;
  totalPages?: number;
  onPageChange: (page: number) => void;
  onSizeChange: (size: number) => void;
  isRemovingUsers?: boolean;
}

export default function GroupMembersList({
  members,
  isLoading,
  onAddUsers,
  onRemoveMultipleUsers,
  groupName,
  page,
  size,
  totalElements = 0,
  totalPages = 1,
  onPageChange,
  onSizeChange,
  isRemovingUsers,
}: GroupMembersListProps) {
  const validTotalElements = totalElements || 0;
  const validTotalPages = Math.max(totalPages || 1, 1);

  const [selectedUserIds, setSelectedUserIds] = useState<Set<string>>(new Set());
  const [showRemoveConfirm, setShowRemoveConfirm] = useState(false);
  const selectAllCheckboxRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setSelectedUserIds(new Set());
  }, [page, members]);

  useEffect(() => {
    if (selectAllCheckboxRef.current && members) {
      const someSelected = members.some((member) => selectedUserIds.has(member.userId));
      const allSelected = members.every((member) => selectedUserIds.has(member.userId));
      selectAllCheckboxRef.current.indeterminate = someSelected && !allSelected;
    }
  }, [selectedUserIds, members]);

  const handleToggleUser = (userId: string) => {
    setSelectedUserIds((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(userId)) {
        newSet.delete(userId);
      } else {
        newSet.add(userId);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    if (members && members.length > 0) {
      const currentPageUserIds = members.map((member) => member.userId);
      const allCurrentPageSelected = currentPageUserIds.every((id) => selectedUserIds.has(id));

      if (allCurrentPageSelected) {
        const newSet = new Set(selectedUserIds);
        currentPageUserIds.forEach((id) => newSet.delete(id));
        setSelectedUserIds(newSet);
      } else {
        const newSet = new Set(selectedUserIds);
        currentPageUserIds.forEach((id) => newSet.add(id));
        setSelectedUserIds(newSet);
      }
    }
  };

  const handleRemoveSelected = () => {
    if (selectedUserIds.size > 0) {
      setShowRemoveConfirm(true);
    }
  };

  const confirmRemoveUsers = async () => {
    onRemoveMultipleUsers(Array.from(selectedUserIds));
    setSelectedUserIds(new Set());
    setShowRemoveConfirm(false);
  };

  return (
    <div className="rounded-md border border-gray-200 bg-white p-6">
      <div className="mb-4 flex items-center justify-between">
        <div>
          <Text variant="subheading">Group Members</Text>
          <Text variant="description" className="text-gray-600">
            {validTotalElements} member{validTotalElements !== 1 ? "s" : ""} in total
          </Text>
        </div>
        <div className="flex gap-2">
          {selectedUserIds.size > 0 && (
            <Button
              variant="destructive"
              className="flex items-center gap-1 text-xs"
              onClick={handleRemoveSelected}
            >
              <TrashIcon className="h-4 w-4" />
              Remove Selected ({selectedUserIds.size})
            </Button>
          )}
          <Button
            variant="outlined"
            className="flex items-center gap-1 text-xs"
            onClick={onAddUsers}
          >
            <PlusIcon className="h-4 w-4" /> Add Users
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center py-10">
          <LoadingIcon className="h-5 w-5 text-blue-400" />
          <p className="ml-2 text-blue-700">Loading members...</p>
        </div>
      ) : members && members.length > 0 ? (
        <>
          <div className="mb-4 overflow-hidden rounded-md border border-gray-200">
            <table className="w-full table-auto">
              <thead className="bg-gray-50 text-left">
                <tr>
                  <th className="px-4 py-3">
                    <div className="flex items-center gap-3">
                      <input
                        ref={selectAllCheckboxRef}
                        type="checkbox"
                        className="h-4 w-4 rounded border-gray-300"
                        checked={
                          members &&
                          members.length > 0 &&
                          members.every((member) => selectedUserIds.has(member.userId))
                        }
                        onChange={handleSelectAll}
                      />
                    </div>
                  </th>
                  <TableHeaderItem item="Name" />
                  <TableHeaderItem item="Username" />
                  <TableHeaderItem item="Email" />
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {members.map((member) => (
                  <tr
                    key={member.userId}
                    className={`hover:bg-gray-50 ${selectedUserIds.has(member.userId) ? "bg-blue-50" : ""}`}
                  >
                    <TableDataItem>
                      <input
                        type="checkbox"
                        className="h-4 w-4 rounded border-gray-300"
                        checked={selectedUserIds.has(member.userId)}
                        onChange={() => handleToggleUser(member.userId)}
                      />
                    </TableDataItem>
                    <TableDataItem>{member?.name || "-"}</TableDataItem>
                    <TableDataItem>{member.userName}</TableDataItem>
                    <TableDataItem>{member.userEmail || "-"}</TableDataItem>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {members && members.length > 0 && (
            <div className="mt-4">
              <PrimaryPagination
                pageNumber={page}
                pageSize={size}
                totalElements={validTotalElements}
                totalPages={validTotalPages}
                onPageNumberClick={onPageChange}
                onSizeChange={onSizeChange}
              />
            </div>
          )}
        </>
      ) : (
        <Text variant="paragraph" className="text-gray-500">
          No users in this group.
        </Text>
      )}

      <RemoveUsersConfirmModal
        show={showRemoveConfirm}
        onClose={() => setShowRemoveConfirm(false)}
        users={members?.filter((member) => selectedUserIds.has(member.userId)) || []}
        groupName={groupName || ""}
        onRemove={confirmRemoveUsers}
        isLoading={isRemovingUsers ?? undefined}
      />
    </div>
  );
}
