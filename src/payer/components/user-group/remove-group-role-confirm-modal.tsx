import { Dialog } from "@headlessui/react";
import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";
import Button from "../../components/ui/Button";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import LoadingIcon from "~lib/components/icons/LoadingIcon";

interface RemoveRoleConfirmModalProps {
  show: boolean;
  onClose: () => void;
  onConfirm: () => void;
  roleName: string;
  memberCount?: number;
  isRemoving?: boolean;
}

export default function RemoveGroupRoleConfirmModal({
  show,
  onClose,
  onConfirm,
  roleName,
  memberCount = 0,
  isRemoving = false,
}: RemoveRoleConfirmModalProps) {
  const handleConfirm = () => {
    onConfirm();
  };

  return (
    <DialogWrapper show={show} onClose={onClose} maxWidth="max-w-xl">
      <div className="p-6">
        <div className="flex items-center justify-center">
          <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100">
            <ExclamationTriangleIcon className="h-6 w-6 text-red-600" aria-hidden="true" />
          </div>
        </div>
        <div className="mt-3 text-center sm:mt-5">
          <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
            Remove Role
          </Dialog.Title>
          <div className="mt-2">
            <p className="text-sm text-gray-500">
              Are you sure you want to remove the role{" "}
              <span className="font-medium">{roleName}</span> from this group? This action cannot be
              undone.
            </p>
            {memberCount > 0 && (
              <p className="mt-2 text-sm text-gray-500">
                This will remove the role for the {memberCount}{" "}
                {memberCount === 1 ? "member" : "members"} in this group.
              </p>
            )}
          </div>
        </div>
        <div className="mt-5 flex justify-center gap-3 sm:mt-6">
          <Button variant="outlined" onClick={onClose} disabled={isRemoving}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={isRemoving}
            className="flex items-center gap-2"
          >
            {isRemoving ? (
              <>
                <LoadingIcon className="h-4 w-4 text-white" />
                Removing...
              </>
            ) : (
              "Remove"
            )}
          </Button>
        </div>
      </div>
    </DialogWrapper>
  );
}
