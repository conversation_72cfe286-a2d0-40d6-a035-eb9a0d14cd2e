import { useState } from "react";
import { CustomRoleResponse } from "../../../lib/types/access-control/role";
import { predefinedRoles } from "../../access-management/data";
import ButtonGroup from "../../../components/ui/ButtonGroup";
import Text from "../../../components/ui/typography/Text";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import Button from "../../ui/Button";
import { XMarkIcon } from "@heroicons/react/24/solid";

interface RoleSelectionStepProps {
  selectedRoles: Set<string>;
  selectedCustomRoleIds: Set<number>;
  customRoles: CustomRoleResponse[];
  isLoadingCustomRoles: boolean;
  onPredefinedRoleToggle: (slug: string) => void;
  onCustomRoleToggle: (id: number) => void;
  onClearAllRoles?: () => void;
}

export default function CreateGroupRoleSelectionStep({
  selectedRoles,
  selectedCustomRoleIds,
  customRoles,
  isLoadingCustomRoles,
  onPredefinedRoleToggle,
  onCustomRoleToggle,
  onClearAllRoles,
}: RoleSelectionStepProps) {
  const [selectedTab, setSelectedTab] = useState<"Predefined Roles" | "Custom Roles">(
    "Predefined Roles",
  );

  const tabOptions = [{ title: "Predefined Roles" }, { title: "Custom Roles" }];

  return (
    <div className="rounded-lg border p-6 shadow-sm">
      <div className="mb-4 flex items-center justify-between">
        <Text variant="subheading">Assign Roles</Text>
        {(selectedRoles.size > 0 || selectedCustomRoleIds.size > 0) && onClearAllRoles && (
          <Button
            variant="destructive"
            onClick={onClearAllRoles}
            type="button"
            className="flex items-center text-sm"
          >
            <XMarkIcon className="mr-1 h-4 w-4" />
            Clear Selection
          </Button>
        )}
      </div>

      <div className="mb-4 rounded-md">
        <p className="text-sm text-gray-700">
          <strong>Note:</strong> You can select both predefined and custom roles. Use the tabs to
          switch between the two options.
        </p>
      </div>

      <div className="mb-4">
        <ButtonGroup
          options={tabOptions}
          setActiveOption={(title) => setSelectedTab(title as "Predefined Roles" | "Custom Roles")}
          activeOption={selectedTab}
        />
      </div>

      {selectedTab === "Predefined Roles" ? (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          {predefinedRoles.map((role) => (
            <div key={role.slug} className="flex items-start space-x-2">
              <input
                type="checkbox"
                id={`role-${role.slug}`}
                checked={selectedRoles.has(role.role)}
                onChange={() => onPredefinedRoleToggle(role.role)}
                className="form-checkbox mt-1"
              />
              <label htmlFor={`role-${role.slug}`} className="flex flex-col">
                <span className="font-medium">{role.name}</span>
                <span className="text-sm text-gray-600">{role.description}</span>
              </label>
            </div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          {isLoadingCustomRoles ? (
            <div className="col-span-full flex items-center justify-center py-4">
              <LoadingIcon className="h-6 w-6 text-blue-400" />
              <span className="ml-2 text-gray-600">Loading custom roles...</span>
            </div>
          ) : customRoles.length > 0 ? (
            customRoles.map((role) => (
              <div key={role.id} className="flex items-start space-x-2">
                <input
                  type="checkbox"
                  id={`custom-role-${role.id}`}
                  checked={selectedCustomRoleIds.has(role.id)}
                  onChange={() => onCustomRoleToggle(role.id)}
                  className="form-checkbox mt-1"
                />
                <label htmlFor={`custom-role-${role.id}`} className="flex flex-col">
                  <span className="font-medium">{role.name}</span>
                  <span className="text-sm text-gray-600">{role.description}</span>
                </label>
              </div>
            ))
          ) : (
            <Text variant="paragraph" className="col-span-full text-gray-500">
              No custom roles available. Custom roles can be created in the Roles section.
            </Text>
          )}
        </div>
      )}

      {/* Display summary of selected roles */}
      {(selectedRoles.size > 0 || selectedCustomRoleIds.size > 0) && (
        <section className="mt-6 border-t pt-4">
          <div className="flex items-center justify-between">
            <Text variant="subheading" className="mb-4">
              Selected Roles Summary
            </Text>
          </div>

          {selectedRoles.size > 0 && (
            <div className="">
              <Text variant="paragraph" className="mb-2 text-base">
                Predefined Roles ({selectedRoles.size})
              </Text>
            </div>
          )}

          {selectedCustomRoleIds.size > 0 && (
            <div>
              <Text variant="paragraph" className="mb-2 text-base">
                Custom Roles ({selectedCustomRoleIds.size})
              </Text>
            </div>
          )}
        </section>
      )}
    </div>
  );
}
