import { PlusIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { UserRepresentation } from "../../../lib/types/access-control/user";
import Button from "../../../components/ui/Button";
import Text from "../../../components/ui/typography/Text";

interface UserSelectionStepProps {
  selectedUsers: UserRepresentation[];
  onRemoveUser: (userId: string) => void;
  onShowAddUserModal: () => void;
}

export default function CreateGroupUserSelectionStep({
  selectedUsers,
  onRemoveUser,
  onShowAddUserModal,
}: UserSelectionStepProps) {
  return (
    <div className="rounded-lg border p-6 shadow-sm">
      <div className="mb-4 flex items-center justify-between">
        <Text variant="subheading">Users in Group</Text>
        <Button onClick={onShowAddUserModal} type="button">
          <PlusIcon className="mr-2 h-5 w-5" />
          Add Users
        </Button>
      </div>

      {selectedUsers.length > 0 ? (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          {selectedUsers.map((user) => (
            <div
              key={user.id}
              className="flex items-center justify-between rounded-md bg-gray-50 p-3"
            >
              <div>
                <Text variant="paragraph" className="text-xs font-medium">
                  {user.firstName && user.lastName
                    ? `${user.firstName} ${user.lastName}`
                    : user.username}
                </Text>
                {user.email && (
                  <Text variant="description" className="text-xs text-gray-600">
                    {user.email}
                  </Text>
                )}
              </div>
              <button
                type="button"
                onClick={() => onRemoveUser(user.id)}
                className="ml-1 rounded-full p-0.5 hover:bg-red-200"
                title="Remove User"
                aria-label="Remove User"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            </div>
          ))}
        </div>
      ) : (
        <Text variant="paragraph" className="text-gray-500">
          No users in this group. Click "Add User" to add users to the group.
        </Text>
      )}
    </div>
  );
}
