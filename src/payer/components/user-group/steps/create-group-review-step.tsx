import { UserRepresentation } from "../../../lib/types/access-control/user";
import { CustomRoleResponse } from "../../../lib/types/access-control/role";
import { predefinedRoles } from "../../access-management/data";
import Text from "../../../components/ui/typography/Text";

interface ReviewStepProps {
  name: string;
  description: string;
  selectedRoles: Set<string>;
  selectedCustomRoleIds: Set<number>;
  selectedUsers: UserRepresentation[];
  customRoles: CustomRoleResponse[];
}

export default function CreateGroupReviewStep({
  name,
  description,
  selectedRoles,
  selectedCustomRoleIds,
  selectedUsers,
  customRoles,
}: ReviewStepProps) {
  return (
    <div className="space-y-6">
      <div className="rounded-lg border p-6 shadow-sm">
        <Text variant="subheading" className="mb-4">
          Group Information
        </Text>
        <div className="space-y-4">
          <div>
            <Text variant="description" className="text-sm font-medium text-gray-700">
              Group Name
            </Text>
            <Text variant="paragraph" className="text-gray-900">
              {name}
            </Text>
          </div>
          <div>
            <Text variant="description" className="text-sm font-medium text-gray-700">
              Description
            </Text>
            <Text variant="paragraph" className="text-gray-900">
              {description}
            </Text>
          </div>
        </div>
      </div>

      <div className="rounded-lg border p-6 shadow-sm">
        <Text variant="subheading" className="mb-4">
          Selected Roles
        </Text>
        {selectedRoles.size > 0 && (
          <div className="mb-4">
            <Text variant="description" className="mb-2 text-sm font-medium text-gray-700">
              Predefined Roles ({selectedRoles.size})
            </Text>
            <ul className="list-inside list-disc">
              {Array.from(selectedRoles).map((roleId) => {
                const role = predefinedRoles.find((r) => r.role === roleId);
                return <li key={roleId}>{role ? role.name : roleId}</li>;
              })}
            </ul>
          </div>
        )}

        {selectedCustomRoleIds.size > 0 && (
          <div>
            <Text variant="description" className="mb-2 text-sm font-medium text-gray-700">
              Custom Roles ({selectedCustomRoleIds.size})
            </Text>
            <ul className="list-inside list-disc">
              {Array.from(selectedCustomRoleIds).map((roleId) => {
                const role = customRoles.find((r) => r.id === roleId);
                return <li key={roleId}>{role ? role.name : `Role ID: ${roleId}`}</li>;
              })}
            </ul>
          </div>
        )}

        {selectedRoles.size === 0 && selectedCustomRoleIds.size === 0 && (
          <Text variant="paragraph" className="text-gray-500">
            No roles selected. Please go back to the Roles step to select roles.
          </Text>
        )}
      </div>

      <div className="rounded-lg border p-6 shadow-sm">
        <Text variant="subheading" className="mb-4">
          Selected Users ({selectedUsers.length})
        </Text>
        {selectedUsers.length > 0 ? (
          <div className="grid grid-cols-1 gap-2 md:grid-cols-2 lg:grid-cols-3">
            {selectedUsers.map((user) => (
              <div key={user.id} className="rounded-md bg-gray-50 p-2">
                <Text variant="paragraph" className="font-medium">
                  {user.firstName && user.lastName
                    ? `${user.firstName} ${user.lastName}`
                    : user.username}
                </Text>
                {user.email && (
                  <Text variant="description" className="text-xs text-gray-600">
                    {user.email}
                  </Text>
                )}
              </div>
            ))}
          </div>
        ) : (
          <Text variant="paragraph" className="text-gray-500">
            No users selected. The group will be created without any users.
          </Text>
        )}
      </div>
    </div>
  );
}
