import { FieldErrors, UseFormClearErrors, UseFormRegister, UseFormWatch } from "react-hook-form";
import Text from "../../../components/ui/typography/Text";
import { useEffect } from "react";
import CharacterCount from "../../ui/character-count";
import { NAME_MAX_LENGTH, DESCRIPTION_MAX_LENGTH } from "../../../utils/validation-schemas";

interface FormInputs {
  name: string;
  description: string;
  createdBy: string;
  payerId: number;
}

interface GroupInformationStepProps {
  errors: FieldErrors<FormInputs>;
  register: UseFormRegister<FormInputs>;
  clearErrors?: UseFormClearErrors<FormInputs>;
  watch?: UseFormWatch<FormInputs>;
}

export default function GroupInformationStep({
  errors,
  register,
  clearErrors,
  watch,
}: GroupInformationStepProps) {
  const watchedName = watch?.("name") || "";
  const watchedDescription = watch?.("description") || "";

  useEffect(() => {
    if (!watch) return;

    const subscription = watch((value, { name }) => {
      if (name === "name" && value.name && errors.name) {
        clearErrors?.("name");
      }
      if (name === "description" && value.description && errors.description) {
        clearErrors?.("description");
      }
    });

    return () => subscription.unsubscribe();
  }, [watch, clearErrors, errors]);

  return (
    <div className="rounded-lg border p-6 shadow-sm">
      <Text variant="subheading" className="mb-4">
        Group Information
      </Text>

      <div className="space-y-4">
        <div>
          <label htmlFor="name" className="mb-1 block text-sm font-medium text-gray-700">
            Group Name <span className="text-red-500">*</span>
          </label>
          <input
            id="name"
            type="text"
            className="w-full rounded-md border border-gray-300 p-2"
            {...register("name", {
              required: "Group name is required",
              minLength: {
                value: 3,
                message: "Group name must be at least 3 characters",
              },
              maxLength: {
                value: 100,
                message: "Group name must not exceed 100 characters",
              },
              onChange: (e) => {
                if (e.target.value.trim() && errors.name) {
                  clearErrors?.("name");
                }
              },
            })}
            maxLength={NAME_MAX_LENGTH}
          />
          <div className="mt-1 flex items-center justify-between">
            {errors.name && <p className="text-xs text-red-600">{errors.name.message}</p>}
            <CharacterCount
              current={watchedName.length}
              max={NAME_MAX_LENGTH}
              className="ml-auto"
            />
          </div>
        </div>

        <div>
          <label htmlFor="description" className="mb-1 block text-sm font-medium text-gray-700">
            Description <span className="text-red-500">*</span>
          </label>
          <textarea
            id="description"
            className="h-20 w-full resize-none rounded-md border border-gray-300 p-2"
            {...register("description", {
              required: "Group description is required",
              minLength: {
                value: 3,
                message: "Group description must be at least 3 characters",
              },
              maxLength: {
                value: 500,
                message: "Group description must not exceed 500 characters",
              },
              onChange: (e) => {
                if (e.target.value.trim() && errors.description) {
                  clearErrors?.("description");
                }
              },
            })}
            maxLength={DESCRIPTION_MAX_LENGTH}
          />
          <div className="mt-1 flex items-center justify-between">
            {errors.description && (
              <p className="text-xs text-red-600">{errors.description.message}</p>
            )}
            <CharacterCount
              current={watchedDescription.length}
              max={DESCRIPTION_MAX_LENGTH}
              className="ml-auto"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
