import { PlusIcon, XMarkIcon } from "@heroicons/react/24/solid";
import { toast } from "react-toastify";
import { useUpdateUserGroupMutation } from "../../api/features/membershipApi";
import {
  setIsRemovingCustomRole,
  setIsRemovingPredefinedRole,
  setRoleToRemove,
  setShowRemoveRoleConfirm,
} from "../../api/features/role-management/user-groups-slice";
import UserService from "../../services/UserService";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import { mapRoleNameToDescriptiveName } from "../../utils/user-management-utils";
import { predefinedRoles } from "../access-management/data";
import Button from "../ui/Button";
import ProgressModal from "../ui/modal/ProgressModal";
import Text from "../ui/typography/Text";
import RemoveGroupRoleConfirmModal from "./remove-group-role-confirm-modal";

interface CustomRole {
  id: number;
  name: string;
  description?: string;
}

interface GroupRolesListProps {
  predefinedRoleNames?: string[];
  customRoles?: CustomRole[];
  onAddRoles?: () => void;
  groupId: number;
  memberCount: number;
}

export default function GroupRolesList({
  predefinedRoleNames = [],
  customRoles = [],
  onAddRoles,
  groupId,
  memberCount,
}: GroupRolesListProps) {
  const dispatch = useAppDispatch();
  const [removePredefinedGroupRole] = useUpdateUserGroupMutation();
  const [removeCustomGroupRole] = useUpdateUserGroupMutation();

  const showRemoveConfirm = useAppSelector((state) => state.userGroups.showRemoveRoleConfirm);
  const roleToRemove = useAppSelector((state) => state.userGroups.roleToRemove);
  const isRemovingPredefinedRoles = useAppSelector(
    (state) => state.userGroups.isRemovingPredefinedRole,
  );
  const isRemovingCustomRoles = useAppSelector((state) => state.userGroups.isRemovingCustomRole);

  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const username = UserService.getUsername();

  const handleRemovePredefinedRoleClick = (roleName: string) => {
    dispatch(setRoleToRemove({ name: roleName }));
    dispatch(setShowRemoveRoleConfirm(true));
  };

  const handleRemoveCustomRoleClick = (roleId: number) => {
    dispatch(setRoleToRemove({ id: roleId }));
    dispatch(setShowRemoveRoleConfirm(true));
  };

  const removePredefinedRole = async (roleName: string) => {
    try {
      dispatch(setIsRemovingPredefinedRole(true));
      await removePredefinedGroupRole({
        id: groupId,
        payerId: payerId,
        body: {
          predefinedRolesToRemove: [roleName],
          updatedBy: username,
        },
      }).unwrap();
      toast.success(`Role removed from group successfully`);
      dispatch(setIsRemovingPredefinedRole(false));
      return true;
    } catch (error) {
      toast.error(`Failed to remove role from group`);
      console.error("Error removing predefined role from group:", error);
      dispatch(setIsRemovingPredefinedRole(false));
      return false;
    }
  };

  const removeCustomRole = async (roleId: number) => {
    try {
      dispatch(setIsRemovingCustomRole(true));
      await removeCustomGroupRole({
        id: groupId,
        payerId: payerId,
        body: {
          customRolesToRemove: [roleId],
          updatedBy: username,
        },
      }).unwrap();
      toast.success(`Custom role removed from group successfully`);
      dispatch(setIsRemovingCustomRole(false));
      return true;
    } catch (error) {
      toast.error(`Failed to remove custom role from group`);
      console.error("Error removing custom role from group:", error);
      dispatch(setIsRemovingCustomRole(false));
      return false;
    }
  };

  const confirmRemoveRole = async () => {
    if (!roleToRemove) return;

    let success = false;

    if (roleToRemove.name) {
      success = await removePredefinedRole(roleToRemove.name);
    } else if (roleToRemove.id !== undefined) {
      success = await removeCustomRole(roleToRemove.id);
    }

    if (success) {
      dispatch(setShowRemoveRoleConfirm(false));
    }

    dispatch(setRoleToRemove(null));
  };

  return (
    <div className="rounded-md border border-gray-200 bg-white p-6">
      <div className="mb-4 flex items-center justify-between">
        <Text variant="subheading">Roles Assigned</Text>
        {onAddRoles && (
          <Button
            variant="outlined"
            className="flex items-center gap-1 text-xs"
            onClick={onAddRoles}
          >
            <PlusIcon className="h-4 w-4" /> Add Roles
          </Button>
        )}
      </div>

      <div className="space-y-4">
        {predefinedRoleNames.length > 0 && (
          <div>
            <Text variant="paragraph" className="mb-2 font-medium">
              Predefined Roles
            </Text>
            <div className="flex flex-wrap gap-2">
              {predefinedRoleNames.map((roleName) => {
                const role = predefinedRoles.find((r) => r.role === roleName);
                return (
                  <div
                    key={roleName}
                    className="flex items-center gap-1 rounded-full bg-gray-100 px-3 py-1"
                  >
                    <span className="text-sm">
                      {mapRoleNameToDescriptiveName(role?.name || roleName) || ""}
                    </span>
                    <button
                      onClick={() => handleRemovePredefinedRoleClick(roleName)}
                      className="ml-1 rounded-full p-0.5 hover:bg-red-200"
                      title="Remove role"
                    >
                      <XMarkIcon className="h-3 w-3" />
                    </button>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {customRoles.length > 0 && (
          <div className="pt-4">
            <Text variant="paragraph" className="mb-2 font-medium">
              Custom Roles
            </Text>
            <div className="flex flex-wrap gap-2">
              {customRoles.map((role) => (
                <div
                  key={role.id}
                  className="flex items-center gap-1 rounded-full bg-gray-100 px-3 py-1"
                >
                  <span className="text-sm font-medium">{role.name}</span>
                  <button
                    onClick={() => handleRemoveCustomRoleClick(role.id)}
                    className="ml-1 rounded-full p-0.5 hover:bg-red-200"
                    title="Remove custom role"
                  >
                    <XMarkIcon className="h-3 w-3" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {predefinedRoleNames.length === 0 && customRoles.length === 0 && (
          <div className="py-4 text-center text-gray-500">No roles assigned to this group yet.</div>
        )}
      </div>

      <RemoveGroupRoleConfirmModal
        show={showRemoveConfirm}
        onClose={() => dispatch(setShowRemoveRoleConfirm(false))}
        onConfirm={confirmRemoveRole}
        roleName={
          roleToRemove?.name
            ? mapRoleNameToDescriptiveName(roleToRemove.name)
            : roleToRemove?.id
              ? customRoles.find((r) => r.id === roleToRemove.id)?.name || ""
              : ""
        }
        memberCount={memberCount}
        isRemoving={roleToRemove?.name ? isRemovingPredefinedRoles : isRemovingCustomRoles}
      />

      <ProgressModal
        isProgressModalOpen={isRemovingCustomRoles}
        onClose={() => null}
        title="Removing Custom Role"
        description="Please wait while we remove the role from the group..."
      />

      <ProgressModal
        isProgressModalOpen={isRemovingPredefinedRoles}
        onClose={() => null}
        title="Removing Predefined Role"
        description="Please wait while we remove the role from the group..."
      />
    </div>
  );
}
