import { useState } from "react";
import { CheckIcon, XMarkIcon } from "@heroicons/react/24/outline";
import Text from "../ui/typography/Text";
import Button from "../ui/Button";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { predefinedRoles } from "../access-management/data";
import Badge from "../ui/Badge";

interface GroupDetailsFormProps {
  isEditing: boolean;
  description: string;
  setDescription: (description: string) => void;
  groupRoles: string[];
  onSave: () => Promise<void>;
  onUpdateRoles?: () => Promise<void>;
  onCancel: () => void;
  isUpdating: boolean;
  isUpdatingDescription?: boolean;
}

export default function GroupDetailsForm({
  isEditing,
  description,
  setDescription,
  groupRoles,
  onSave,
  onCancel,
  isUpdating,
  isUpdatingDescription = false,
  onUpdateRoles,
}: GroupDetailsFormProps) {
  return (
    <div className="rounded-lg border border-gray-200 p-6">
      <div className="mb-4 flex items-center justify-between">
        <Text variant="subheading">Group Details</Text>
        {isEditing && (
          <div className="flex items-center gap-2">
            <Button
              variant="outlined"
              className="flex items-center gap-2"
              onClick={onCancel}
              disabled={isUpdating}
            >
              <XMarkIcon className="h-4 w-4" />
              Cancel
            </Button>
            <Button
              variant="filled"
              className="flex items-center gap-2"
              onClick={onSave}
              disabled={isUpdatingDescription}
            >
              {isUpdatingDescription ? (
                <>
                  <LoadingIcon className="h-4 w-4 text-white" />
                  Saving...
                </>
              ) : (
                <>
                  <CheckIcon className="h-4 w-4" />
                  Save
                </>
              )}
            </Button>
          </div>
        )}
      </div>

      <div className="space-y-4">
        <div>
          <Text variant="description" className="mb-1 text-gray-600">
            Description
          </Text>
          {isEditing ? (
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
              rows={3}
              placeholder="Enter group description"
            />
          ) : (
            <Text variant="paragraph" className="text-gray-800">
              {description || "No description provided."}
            </Text>
          )}
        </div>

        <div>
          <Text variant="description" className="mb-1 text-gray-600">
            Assigned Roles
          </Text>
          <div className="flex flex-wrap gap-2">
            {groupRoles && groupRoles.length > 0 ? (
              groupRoles.map((role) => {
                const roleInfo = predefinedRoles.find((r) => r.role === role);
                return (
                  <Badge key={role} color="blue">
                    {roleInfo ? roleInfo.name : role}
                  </Badge>
                );
              })
            ) : (
              <Text variant="paragraph" className="text-gray-500">
                No roles assigned to this group.
              </Text>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
