import { UserGroupResponse } from "../../lib/types/access-control/role";
import Text from "../../components/ui/typography/Text";
import Badge from "../../components/ui/Badge";
import Button from "../../components/ui/Button";
import { format, parseISO } from "date-fns";
import { PencilIcon } from "@heroicons/react/24/outline";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { useState } from "react";
import {
  nameSchema,
  descriptionSchema,
  NAME_MAX_LENGTH,
  DESCRIPTION_MAX_LENGTH,
} from "../../utils/validation-schemas";
import CharacterCount from "../ui/character-count";

interface GroupDetailsInfoProps {
  group: UserGroupResponse;
  isEditingName: boolean;
  setIsEditingName: (isEditing: boolean) => void;
  isEditingDescription: boolean;
  setIsEditingDescription: (isEditing: boolean) => void;
  name: string;
  setName: (name: string) => void;
  description: string;
  setDescription: (description: string) => void;
  onSaveName: () => void;
  onSaveDescription: () => void;
  isUpdating: boolean;
}

export default function GroupDetailsInfo({
  group,
  isEditingName,
  setIsEditingName,
  isEditingDescription,
  setIsEditingDescription,
  name,
  setName,
  description,
  setDescription,
  onSaveName,
  onSaveDescription,
  isUpdating,
}: GroupDetailsInfoProps) {
  const [nameError, setNameError] = useState<string>("");
  const [descriptionError, setDescriptionError] = useState<string>("");

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    try {
      return format(parseISO(dateString), "MMM dd, yyyy");
    } catch (e) {
      return dateString;
    }
  };

  const validateName = (value: string) => {
    try {
      nameSchema.parse({ name: value });
      setNameError("");
      return true;
    } catch (error: any) {
      setNameError(error.errors?.[0]?.message || "Invalid name");
      return false;
    }
  };

  const validateDescription = (value: string) => {
    try {
      descriptionSchema.parse({ description: value });
      setDescriptionError("");
      return true;
    } catch (error: any) {
      setDescriptionError(error.errors?.[0]?.message || "Invalid description");
      return false;
    }
  };

  const handleNameChange = (value: string) => {
    setName(value);
    validateName(value);
  };

  const handleDescriptionChange = (value: string) => {
    setDescription(value);
    validateDescription(value);
  };

  const isNameValid = !nameError && name.trim() !== "" && name !== group.name;
  const isDescriptionValid =
    !descriptionError && description.trim() !== "" && description !== group.description;

  return (
    <div className="rounded-md border border-gray-200 bg-white p-6">
      <div className="mb-4 flex items-center justify-between">
        <Text variant="subheading">Details</Text>
        {(isEditingName || isEditingDescription) && <Badge color="blue" text="Edit Mode" />}
      </div>

      <div className="space-y-4">
        <div>
          <p className="text-sm font-medium text-gray-500">Group Name</p>
          {isEditingName ? (
            <div className="mt-1 flex flex-col gap-2">
              <div className="relative">
                <input
                  type="text"
                  className={`w-full rounded-md border p-2 text-sm ${
                    nameError ? "border-red-300 focus:border-red-500" : "border-gray-300"
                  }`}
                  value={name}
                  onChange={(e) => handleNameChange(e.target.value)}
                  maxLength={NAME_MAX_LENGTH}
                />
                <div className="mt-1 flex items-center justify-between">
                  {nameError && <p className="text-xs text-red-600">{nameError}</p>}
                  <CharacterCount current={name.length} max={NAME_MAX_LENGTH} className="ml-auto" />
                </div>
              </div>
              <div className="flex items-center gap-2 self-end">
                <Button
                  variant="outlined"
                  onClick={() => {
                    setIsEditingName(false);
                    setName(group.name);
                    setNameError("");
                  }}
                >
                  Cancel
                </Button>
                <Button
                  variant="filled"
                  onClick={onSaveName}
                  disabled={isUpdating || !isNameValid}
                  className="flex items-center gap-2"
                >
                  {isUpdating && <LoadingIcon className="h-3 w-3 text-white" />}
                  Save
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <p className="break-words text-gray-900">{group.name}</p>
              <button
                onClick={() => {
                  if (isEditingDescription) {
                    setIsEditingDescription(false);
                    setDescription(group.description || "");
                    setDescriptionError("");
                  }
                  setIsEditingName(true);
                }}
                className="rounded-full p-1 hover:bg-gray-100"
                title="Edit Group Name"
              >
                <PencilIcon className="h-4 w-4 text-gray-500" />
              </button>
            </div>
          )}
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500">Description</p>
          {isEditingDescription ? (
            <div className="mt-1 flex flex-col gap-2">
              <div className="relative">
                <textarea
                  className={`h-20 w-full resize-none rounded-md border p-2 text-sm ${
                    descriptionError ? "border-red-300 focus:border-red-500" : "border-gray-300"
                  }`}
                  value={description}
                  onChange={(e) => handleDescriptionChange(e.target.value)}
                  maxLength={DESCRIPTION_MAX_LENGTH}
                />
                <div className="mt-1 flex items-center justify-between">
                  {descriptionError && <p className="text-xs text-red-600">{descriptionError}</p>}
                  <CharacterCount
                    current={description.length}
                    max={DESCRIPTION_MAX_LENGTH}
                    className="ml-auto"
                  />
                </div>
              </div>
              <div className="flex items-center gap-2 self-end">
                <Button
                  variant="outlined"
                  onClick={() => {
                    setIsEditingDescription(false);
                    setDescription(group.description || "");
                    setDescriptionError("");
                  }}
                >
                  Cancel
                </Button>
                <Button
                  variant="filled"
                  onClick={onSaveDescription}
                  disabled={isUpdating || !isDescriptionValid}
                  className="flex items-center gap-2"
                >
                  {isUpdating && <LoadingIcon className="h-3 w-3 text-white" />}
                  Save
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <p className="whitespace-pre-wrap break-words text-gray-900">
                {group.description || "-"}
              </p>
              <button
                onClick={() => {
                  if (isEditingName) {
                    setIsEditingName(false);
                    setName(group.name);
                    setNameError("");
                  }
                  setIsEditingDescription(true);
                }}
                className="rounded-full p-1 hover:bg-gray-100"
                title="Edit Description"
              >
                <PencilIcon className="h-4 w-4 text-gray-500" />
              </button>
            </div>
          )}
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500">Created By</p>
          <p className="text-gray-900">{group.createdBy || "N/A"}</p>
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500">Created At</p>
          <p className="text-gray-900">{formatDate(group.createdAt)}</p>
        </div>

        {group.updatedAt && (
          <div>
            <p className="text-sm font-medium text-gray-500">Last Updated</p>
            <p className="text-gray-900">{formatDate(group.updatedAt)}</p>
          </div>
        )}
      </div>
    </div>
  );
}
