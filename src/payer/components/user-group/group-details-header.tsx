import { TrashIcon } from "@heroicons/react/24/outline";
import Text from "../ui/typography/Text";
import Button from "../ui/Button";
import TopBackButton from "../ui/TopBackButton";

interface GroupDetailsHeaderProps {
  groupName: string;
  onBack: () => void;
  onDelete: () => void;
}

export default function GroupDetailsHeader({
  groupName,
  onBack,
  onDelete,
}: GroupDetailsHeaderProps) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-4">
        <TopBackButton onClick={onBack} className="mt-1" />
        <Text variant="heading">{groupName}</Text>
      </div>
      <div className="flex items-center gap-2">
        <Button variant="destructive" className="flex items-center gap-2" onClick={onDelete}>
          <TrashIcon className="h-4 w-4" />
          Delete
        </Button>
      </div>
    </div>
  );
}
