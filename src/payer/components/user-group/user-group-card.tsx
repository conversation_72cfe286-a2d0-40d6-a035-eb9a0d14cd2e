import { useNavigate } from "react-router-dom";
import { UserGroupResponse } from "../../lib/types/access-control/role";
import { format, parseISO } from "date-fns";
import Button from "../ui/Button";
import { EyeIcon } from "@heroicons/react/24/outline";
import CardWrapper from "../ui/CardWrapper";

interface UserGroupSummaryItemProps {
  label: string;
  value: string | number | JSX.Element;
}

const UserGroupSummaryItem = ({ label, value }: UserGroupSummaryItemProps) => {
  return (
    <div className="flex space-x-2 text-sm">
      <p className="whitespace-nowrap text-gray-500">{label}:</p>
      <div className="text-gray-700">{value}</div>
    </div>
  );
};

interface UserGroupCardProps {
  group: UserGroupResponse;
}

export default function UserGroupCard({ group }: UserGroupCardProps) {
  const navigate = useNavigate();

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    try {
      return format(parseISO(dateString), "MMM dd, yyyy");
    } catch (e) {
      return dateString;
    }
  };

  const handleViewGroup = () => {
    navigate(`/users/groups/${group.id}`);
  };

  return (
    <CardWrapper className="flex h-full flex-col justify-between">
      <div className="mb-4">
        <h3 className="mb-2 text-lg font-semibold text-gray-800">{group.name}</h3>
        <p className="mb-4 line-clamp-2 text-sm text-gray-600">{group.description}</p>
        <div className="space-y-1">
          {/*<UserGroupSummaryItem label="Members" value={group.memberCount} />*/}
          <UserGroupSummaryItem label="Created By" value={group.createdBy || "System"} />
          <UserGroupSummaryItem label="Created On" value={formatDate(group.createdAt)} />
          {group.updatedAt && (
            <UserGroupSummaryItem label="Last Updated" value={formatDate(group.updatedAt)} />
          )}
        </div>
      </div>
      <div className="flex justify-end">
        <Button variant="outlined" className="flex items-center gap-2" onClick={handleViewGroup}>
          <EyeIcon className="h-4 w-4" />
          View Group
        </Button>
      </div>
    </CardWrapper>
  );
}
