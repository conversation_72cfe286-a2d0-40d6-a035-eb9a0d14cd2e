import styles from "./LoadingAnimation.module.css";

type Props = {
  /**
   * The size (width and height) of the loading animation in pixels.
   * @default 200
   */
  size?: number;
};

/**
 * LoadingAnimation is a functional component that renders a rotating SVG loading animation.
 *
 * @param {number} props.size - The size (width and height) of the loading animation in pixels.
 * @returns {JSX.Element} A rotating loading animation SVG.
 */
export default function LoadingAnimation({ size = 200 }: Props): JSX.Element {
  return (
    <svg
      className={styles["loading-animation"]}
      data-testid="animated-loading-icon"
      width={size}
      height={size}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="45.832" width="8.33333" height="25" rx="4.16667" fill="#1E3A8A" />
      <rect
        y="54.1667"
        width="8.33333"
        height="25"
        rx="4.16667"
        transform="rotate(-90 0 54.1667)"
        fill="#1D4ED8"
      />
      <rect
        x="54.168"
        y="100"
        width="8.33333"
        height="25"
        rx="4.16667"
        transform="rotate(180 54.168 100)"
        fill="#3B82F6"
      />
      <rect
        x="100"
        y="45.8333"
        width="8.33333"
        height="25"
        rx="4.16667"
        transform="rotate(90 100 45.8333)"
        fill="#93C5FD"
      />
      <rect
        x="11.7031"
        y="17.5908"
        width="8.33333"
        height="25"
        rx="4.16667"
        transform="rotate(-45 11.7031 17.5908)"
        fill="#1E40AF"
      />
      <rect
        x="17.5938"
        y="88.3015"
        width="8.33333"
        height="25"
        rx="4.16667"
        transform="rotate(-135 17.5938 88.3015)"
        fill="#2563EB"
      />
      <rect
        x="88.2969"
        y="82.4092"
        width="8.33333"
        height="25"
        rx="4.16667"
        transform="rotate(135 88.2969 82.4092)"
        fill="#60A5FA"
      />
      <rect
        x="82.4062"
        y="11.6985"
        width="8.33333"
        height="25"
        rx="4.16667"
        transform="rotate(45 82.4062 11.6985)"
        fill="#BFDBFE"
      />
    </svg>
  );
}
