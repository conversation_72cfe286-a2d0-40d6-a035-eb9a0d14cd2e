import { cn } from "~lib/utils/cn";

type Props = {
  className?: string;
};

export default function BouncingEllipsis({ className }: Props) {
  return (
    <div className={cn("flex gap-4", className)}>
      <div className="h-4 w-4 animate-bounce rounded-full bg-primary [animation-delay:160ms]"></div>
      <div className="h-4 w-4 animate-bounce rounded-full bg-primary [animation-delay:320ms]"></div>
      <div className="h-4 w-4 animate-bounce rounded-full bg-primary [animation-delay:500ms]"></div>
    </div>
  );
}
