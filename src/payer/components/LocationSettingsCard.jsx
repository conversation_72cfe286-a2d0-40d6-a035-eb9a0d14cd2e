import React from "react";
import { Link } from "react-router-dom";

function LocationSettingsCard(props) {
  return (
    <div className="col-span-full sm:col-span-6 xl:col-span-3 bg-white shadow-none rounded-sm ">
      <div className="flex flex-col h-full">
        {/* Card top */}
        <div className="grow p-5">
          {/* Menu button */}
          <div className="relative"></div>
          {/* Image + name */}
          <header>
            <div className="flex justify-center mb-2">
              <div className="relative inline-flex items-start">
                <div
                  className="absolute top-0 right-0 -mr-2 bg-white rounded-full shadow "
                  aria-hidden="true"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-yellow-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                </div>
              </div>
            </div>
            <div className="text-center mt-10">
              <div className="inline-flex text-gray-800 hover:text-gray-900 cursor-pointer">
                <h2
                  className="text-xl leading-snug justify-center font-semibold"
                  onClick={(e) => {
                    e.stopPropagation();
                    props.handleClick(props.link);
                  }}
                >
                  {props.name}
                </h2>
              </div>
            </div>
            <div className="flex justify-center items-center">
              <span className="text-sm font-medium text-gray-400 -mt-0.5 mr-1"></span>{" "}
              <span>{props.location}</span>
            </div>
          </header>
          {/* Bio */}
          <div className="text-center mt-2">
            <div className="text-sm">{props.content}</div>
          </div>
        </div>
        {/* Card footer */}
        <div className=" flex justify-center">
          <button
            className="block  text-center text-sm text-indigo-500 hover:text-indigo-600 font-medium px-3 py-4"
            onClick={(e) => {
              e.stopPropagation();
              props.handleClick(props.link);
            }}
          >
            <div className="flex items-center justify-end">
              {JSON.stringify(props.message).includes("Currency") ? (
                ""
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-6 w-6 fill-current shrink-0 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <path d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )}

              {props.message}
            </div>
          </button>
        </div>
      </div>
    </div>
  );
}

export default LocationSettingsCard;
