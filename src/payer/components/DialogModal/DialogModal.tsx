import { Dialog, Transition } from "@headlessui/react";
import { createContext, Fragment, ReactNode } from "react";
import styles from "./DialogModal.module.css";

type Props = {
  children: ReactNode;
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
};

type DialogModalContextType = {
  closeModal(): void;
};

export const DialogModalContext = createContext<DialogModalContextType | null>(null);

/**
 * A modal component using Headless UI's `Dialog` and `Transition` components.
 *
 * @param {Props} props - The props object.
 * @returns {JSX.Element} The rendered `DialogModal` component.
 *
 * @example
 * <DialogModal isOpen={isOpen} setIsOpen={setIsOpen}>
 *   <p>Hello, world!</p>
 * </DialogModal>
 */
export default function DialogModal({ children, setIsOpen, isOpen }: Props) {
  function closeModal() {
    setIsOpen(false);
  }

  return (
    <DialogModalContext.Provider value={{ closeModal }}>
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={closeModal}>
          {/* Back drop */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/25" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel
                  className={` ${styles["scroll-container"]}  max-h-[90vh] transform overflow-hidden overflow-y-auto bg-white p-6 text-left align-middle shadow-xl transition-all`}
                >
                  {children}
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </DialogModalContext.Provider>
  );
}
