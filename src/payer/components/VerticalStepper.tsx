import React from "react";
import clsx from "../utils";

interface Section {
  title: string;
  description?: string;
}

interface Props {
  sections: Array<Section>;
  active: number;
}

export default function VerticalStepper({ sections, active }: Props) {
  return (
    <div className="px-2">
      <ol className="relative border-l border-gray-200 dark:border-gray-700">              
        {sections.map((section, index) => (
            <li className="mb-10 ml-6" key={index}>
              {/* bg-blue-100 ring-white */}
              {/* ring-8 ring-white dark:ring-gray-900 */}
              <span className={clsx(
                "absolute border text-xs font-semibold flex items-center justify-center w-8 h-8 bg-white rounded-full -left-4",
                index === active ? "bg-blue-900 border-none text-white" : "border-gray-300 text-gray-400"
              )}
              >
                {index+1}
              </span>

              <h3 className={clsx(
                "flex items-center mb-1 pt-1 font-semibold dark:text-white",
                index === active ? "text-blue-900" : "text-gray-400"
              )}>
                {section.title}
              </h3>

              {section.description && (
                <p className={clsx(
                  "mb-4 text-sm font-normal",
                  index === active ? "text-gray-500" : "text-gray-400"
                )}>
                  {section.description}
                </p>
              )}
            </li>
          )
        )}
      </ol>
    </div>
  )
}
