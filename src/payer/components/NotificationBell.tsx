import { BellIcon } from "@heroicons/react/24/outline";
import { cn } from "~lib/utils/cn";
import { useNotifications } from "../context/NotificationsContext";

export default function NotificationBell() {
  const { setIsNotificationsPanelOpen, notificationsCount } = useNotifications();

  const areThereManyNotifications = Number(notificationsCount) > 9;

  const displayCount: string = areThereManyNotifications
    ? "9+"
    : Number(notificationsCount).toString();

  function handleOpenNotificationsPanel() {
    setIsNotificationsPanelOpen(true);
  }

  return (
    <button
      onClick={handleOpenNotificationsPanel}
      className={cn(areThereManyNotifications && "animate-bounce")}
    >
      <div className="relative w-fit">
        {Number(notificationsCount) > 0 && (
          <span className="absolute right-0 top-0 flex aspect-square min-h-[2ch] min-w-[2ch] -translate-y-1/3 translate-x-1/3 items-center justify-center rounded-full bg-primary p-[4px] text-xs text-white">
            {displayCount}
          </span>
        )}
        <BellIcon className="w-7 text-gray-light" strokeWidth={2} />
      </div>
    </button>
  );
}
