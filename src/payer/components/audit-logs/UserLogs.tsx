import { useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { useGetPayerUsersAuditLogsQuery } from "../../api/features/membershipApi";
import expandIcon from "../../assets/expand_icon.png";
import NoAuditLogs from "../illustrations/no-audit-logs";
import { GetPayerUserAuditLogsQuery, UserAuditLog } from "../../lib/types/access-control/user";
import { useAppSelector } from "../../store/hooks";
import { mapRoleNameToDescriptiveName } from "../../utils/user-management-utils";
import { DEFAULT_ROLES } from "../access-management/data";
import EmptyState from "../ui/EmptyState";
import MainWrapper from "../ui/MainWrapper";
import TopBackButton from "../ui/TopBackButton";
import Text from "../ui/typography/Text";

export default function UserLogs() {
  const navigate = useNavigate();
  const selectedUser = useAppSelector((state) => state.accessControl.selectedAuditLogsUser);
  const [expandedLogPeriods, setExpandedLogsPeriods] = useState<string[]>([]);
  const [userAuditLogs, setUserAuditLogs] = useState<UserAuditLog[]>([]);

  const handleBackClick = () => {
    navigate(-1);
  };

  const handleLogExpand = (logPeriod: string) => {
    if (expandedLogPeriods.includes(logPeriod)) {
      setExpandedLogsPeriods(expandedLogPeriods.filter((period) => period !== logPeriod));
    } else {
      setExpandedLogsPeriods([...expandedLogPeriods, logPeriod]);
    }
  };

  const getUserAuditLogsParams: GetPayerUserAuditLogsQuery = {
    userId: selectedUser.id,
    page: 1,
    size: 100_0000,
  };

  const { data, isLoading, isFetching, error } =
    useGetPayerUsersAuditLogsQuery(getUserAuditLogsParams);

  const groupedLogs = useMemo(() => {
    return userAuditLogs.reduce<{ [monthYear: string]: UserAuditLog[] }>((acc, log) => {
      if (!acc[log.createdAt]) {
        acc[log.createdAt] = [];
      }
      acc[log.createdAt]?.push(log);
      return acc;
    }, {});
  }, [userAuditLogs]);

  // sort logs by month-year and then by timestamp
  const sortedGroupedLogs = useMemo(() => {
    const sorted: { [monthYear: string]: UserAuditLog[] } = {};

    Object.keys(groupedLogs)
      .sort()
      .reverse()
      .forEach((monthYear) => {
        // sort logs within each month by timestamp in descending order
        sorted[monthYear] =
          groupedLogs[monthYear]?.sort(
            (a, b) => new Date(b.eventTimestamp).getTime() - new Date(a.eventTimestamp).getTime(),
          ) ?? [];
      });

    return sorted;
  }, [groupedLogs]);

  useEffect(() => {
    if (data?.content) {
      setUserAuditLogs(data.content);
    }
  }, [data]);

  const isLogDataLoading = isLoading || isFetching;
  const shouldDisplayData = !isLogDataLoading && !error && userAuditLogs.length > 0;

  return (
    <MainWrapper>
      <section className="flex flex-col gap-6">
        <div className="flex gap-6">
          <TopBackButton className="mt-2" onClick={handleBackClick} />
          <div className="flex flex-col gap-2">
            <Text variant="heading">
              User Audit Logs / <span className="text-customGray">{selectedUser.username}</span>
            </Text>
            <Text variant="description" className="italic">
              Please click on the card to view the complete audit log of role assignments and
              revocations.
            </Text>
          </div>
        </div>
        <div className="flex flex-col space-y-4 pt-4">
          {error ? (
            <div className="flex items-center justify-center self-center py-44">
              <p className="text-base text-red-700">
                Error loading user audit logs. Try again later.
              </p>
            </div>
          ) : isLogDataLoading ? (
            <div className="flex items-center justify-center space-x-2 py-44">
              <LoadingIcon className="h-6 w-6 text-blue-700" />
              <p className="text-base text-blue-700">Loading User Audit Logs...</p>
            </div>
          ) : shouldDisplayData ? (
            <section className="flex flex-col space-y-5">
              {Object.entries(sortedGroupedLogs)
                .map(([monthYear, logs]) => {
                  // check if this month has any meaningful logs after filtering
                  const hasValidLogs = logs.some((log) => {
                    const filteredAddedRoles = log.addedRoles.filter(
                      (role) => !DEFAULT_ROLES.includes(role),
                    );
                    const filteredRemovedRoles = log.removedRoles.filter(
                      (role) => !DEFAULT_ROLES.includes(role),
                    );
                    return filteredAddedRoles.length > 0 || filteredRemovedRoles.length > 0;
                  });

                  if (!hasValidLogs) {
                    return null;
                  }

                  return (
                    <div
                      key={monthYear}
                      className={`mr-8 flex flex-col space-y-4 rounded-md border border-[#AFAFAF4D] p-6 shadow-md`}
                    >
                      <div className="flex items-center justify-between space-x-2">
                        <div className="flex flex-col gap-1">
                          <h3 className="text-xl font-medium text-darkBlue">
                            {monthYear.replace("-", ", ")}
                          </h3>
                          <p className="text base text-customGray">
                            {`View the complete log of user role assignments and revocations for ${monthYear.replace("-", ", ")}.`}
                          </p>
                        </div>
                        <button onClick={() => handleLogExpand(monthYear)}>
                          <img src={expandIcon} alt="expand" className="h-6 w-6" />
                        </button>
                      </div>
                      {expandedLogPeriods.includes(monthYear) && (
                        <section className="flex flex-col space-y-4">
                          {logs
                            .map((log) => {
                              // filter out default roles from both added and removed roles
                              const filteredAddedRoles = log.addedRoles.filter(
                                (role) => !DEFAULT_ROLES.includes(role),
                              );
                              const filteredRemovedRoles = log.removedRoles.filter(
                                (role) => !DEFAULT_ROLES.includes(role),
                              );

                              // only return logs that have meaningful roles after filtering
                              if (
                                filteredAddedRoles.length === 0 &&
                                filteredRemovedRoles.length === 0
                              ) {
                                return null;
                              }

                              return (
                                <div className="flex flex-col space-y-3" key={log.id}>
                                  <h2 className="text-lg text-midGray">{log.eventTimestamp}</h2>
                                  <section className="flex flex-col gap-6 pb-2">
                                    {filteredAddedRoles.length > 0 && (
                                      <div className="flex flex-col gap-3">
                                        <h2 className="text-lg font-medium text-[#16A34A]">
                                          Roles assigned
                                        </h2>
                                        {filteredAddedRoles.map((roleName) => (
                                          <div key={roleName}>
                                            <p className="text-base font-medium text-darkGray">
                                              {mapRoleNameToDescriptiveName(roleName)}
                                            </p>
                                          </div>
                                        ))}
                                      </div>
                                    )}
                                    {filteredRemovedRoles.length > 0 && (
                                      <div className="flex flex-col gap-3">
                                        <h2 className="text-lg font-medium text-[#CA8A04]">
                                          Roles revoked
                                        </h2>
                                        {filteredRemovedRoles.map((roleName) => (
                                          <div key={roleName}>
                                            <p className="text-base font-medium text-darkGray">
                                              {mapRoleNameToDescriptiveName(roleName)}
                                            </p>
                                          </div>
                                        ))}
                                      </div>
                                    )}
                                  </section>
                                  <h2 className="text-customGray">
                                    Performed by:{" "}
                                    <span className="font-medium text-black">{log.actionedBy}</span>
                                  </h2>
                                  <hr className="text-gray-200" />
                                </div>
                              );
                            })
                            .filter(Boolean)}
                        </section>
                      )}
                    </div>
                  );
                })
                .filter(Boolean)}
            </section>
          ) : (
            <EmptyState
              illustration={<NoAuditLogs />}
              message={{
                title: "No logs found.",
                description:
                  "Looks like there are no logs to display yet. Once activity starts, you'll see logs here.",
              }}
            />
          )}
        </div>
      </section>
    </MainWrapper>
  );
}
