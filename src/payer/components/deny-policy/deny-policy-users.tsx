import { useState } from "react";
import { useGetDenyPolicyUsersQuery } from "../../api/features/membershipApi";
import { DenyPolicyDto } from "../../lib/types/access-control/role";
import UserService from "../../services/UserService";
import DenyPolicyMembersList from "./deny-policy-members-list";

interface DenyPolicyUsersProps {
  policy: DenyPolicyDto;
  onAddUsers: () => void;
  onRemoveMultipleUsers: (userIds: string[]) => void;
}

export default function DenyPolicyUsers({
  policy,
  onAddUsers,
  onRemoveMultipleUsers,
}: DenyPolicyUsersProps) {
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(5);

  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];

  const { data, isLoading } = useGetDenyPolicyUsersQuery({
    id: policy.id,
    params: {
      payerId,
      page,
      size,
    },
  });

  const users = data?.content || [];
  const totalElements = data?.totalElements || 0;
  const totalPages = data?.totalPages || 1;

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleSizeChange = (newSize: number) => {
    setSize(newSize);
    setPage(1);
  };

  return (
    <DenyPolicyMembersList
      policy={{ ...policy, users }}
      isLoading={isLoading}
      onAddUsers={onAddUsers}
      onRemoveMultipleUsers={onRemoveMultipleUsers}
      page={page}
      size={size}
      totalElements={totalElements}
      totalPages={totalPages}
      onPageChange={handlePageChange}
      onSizeChange={handleSizeChange}
    />
  );
}
