import { DenyPolicyDto } from "../../lib/types/access-control/role";
import Text from "../../components/ui/typography/Text";
import Badge from "../../components/ui/Badge";
import Button from "../../components/ui/Button";
import { format, parseISO } from "date-fns";
import { PencilIcon } from "@heroicons/react/24/outline";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { useState } from "react";
import {
  nameSchema,
  reasonSchema,
  NAME_MAX_LENGTH,
  DESCRIPTION_MAX_LENGTH,
} from "../../utils/validation-schemas";
import CharacterCount from "../ui/character-count";

interface DenyPolicyDetailsProps {
  policy: DenyPolicyDto;
  isEditingName: boolean;
  setIsEditingName: (isEditing: boolean) => void;
  isEditingReason: boolean;
  setIsEditingReason: (isEditing: boolean) => void;
  name: string;
  setName: (name: string) => void;
  reason: string;
  setReason: (reason: string) => void;
  onSaveName: () => void;
  onSaveReason: () => void;
  isUpdating: boolean;
}

export default function DenyPolicyInfo({
  policy,
  isEditingName,
  setIsEditingName,
  isEditingReason,
  setIsEditingReason,
  name,
  setName,
  reason,
  setReason,
  onSaveName,
  onSaveReason,
  isUpdating,
}: DenyPolicyDetailsProps) {
  const [nameError, setNameError] = useState<string>("");
  const [reasonError, setReasonError] = useState<string>("");

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    try {
      return format(parseISO(dateString), "MMM dd, yyyy");
    } catch (e) {
      return dateString;
    }
  };

  const validateName = (value: string) => {
    try {
      nameSchema.parse({ name: value });
      setNameError("");
      return true;
    } catch (error: any) {
      setNameError(error.errors?.[0]?.message || "Invalid name");
      return false;
    }
  };

  const validateReason = (value: string) => {
    try {
      reasonSchema.parse({ reason: value });
      setReasonError("");
      return true;
    } catch (error: any) {
      setReasonError(error.errors?.[0]?.message || "Invalid reason");
      return false;
    }
  };

  const handleNameChange = (value: string) => {
    setName(value);
    validateName(value);
  };

  const handleReasonChange = (value: string) => {
    setReason(value);
    validateReason(value);
  };

  const isNameValid = !nameError && name.trim() !== "" && name !== policy.name;
  const isReasonValid = !reasonError && reason.trim() !== "" && reason !== policy.reason;

  return (
    <div className="rounded-md border border-gray-200 bg-white p-6">
      <div className="mb-4 flex items-center justify-between">
        <Text variant="subheading">Details</Text>
        {(isEditingName || isEditingReason) && <Badge color="blue" text="Edit Mode" />}
      </div>

      <div className="space-y-4">
        <div>
          <p className="text-sm font-medium text-gray-500">Policy Name</p>
          {isEditingName ? (
            <div className="mt-1 flex flex-col gap-2">
              <div className="relative">
                <input
                  type="text"
                  className={`w-full rounded-md border p-2 text-sm ${
                    nameError ? "border-red-300 focus:border-red-500" : "border-gray-300"
                  }`}
                  value={name}
                  onChange={(e) => handleNameChange(e.target.value)}
                  maxLength={NAME_MAX_LENGTH}
                />
                <div className="mt-1 flex items-center justify-between">
                  {nameError && <p className="text-xs text-red-600">{nameError}</p>}
                  <CharacterCount current={name.length} max={NAME_MAX_LENGTH} className="ml-auto" />
                </div>
              </div>
              <div className="flex items-center gap-2 self-end">
                <Button
                  variant="outlined"
                  onClick={() => {
                    setIsEditingName(false);
                    setName(policy.name);
                    setNameError("");
                  }}
                >
                  Cancel
                </Button>
                <Button
                  variant="filled"
                  onClick={onSaveName}
                  disabled={isUpdating || !isNameValid}
                  className="flex items-center gap-2"
                >
                  {isUpdating && <LoadingIcon className="h-3 w-3 text-white" />}
                  Save
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <p className="break-words text-gray-900">{policy.name}</p>
              <button
                onClick={() => {
                  if (isEditingReason) {
                    setIsEditingReason(false);
                    setReason(policy.reason || "");
                    setReasonError("");
                  }
                  setIsEditingName(true);
                }}
                className="rounded-full p-1 hover:bg-gray-100"
                title="Edit Policy Name"
              >
                <PencilIcon className="h-4 w-4 text-gray-500" />
              </button>
            </div>
          )}
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500">Reason</p>
          {isEditingReason ? (
            <div className="mt-1 flex flex-col gap-2">
              <div className="relative">
                <textarea
                  className={`h-20 w-full resize-none rounded-md border p-2 text-sm ${
                    reasonError ? "border-red-300 focus:border-red-500" : "border-gray-300"
                  }`}
                  value={reason}
                  onChange={(e) => handleReasonChange(e.target.value)}
                  maxLength={DESCRIPTION_MAX_LENGTH}
                />
                <div className="mt-1 flex items-center justify-between">
                  {reasonError && <p className="text-xs text-red-600">{reasonError}</p>}
                  <CharacterCount
                    current={reason.length}
                    max={DESCRIPTION_MAX_LENGTH}
                    className="ml-auto"
                  />
                </div>
              </div>
              <div className="flex items-center gap-2 self-end">
                <Button
                  variant="outlined"
                  onClick={() => {
                    setIsEditingReason(false);
                    setReason(policy.reason || "");
                    setReasonError("");
                  }}
                >
                  Cancel
                </Button>
                <Button
                  variant="filled"
                  onClick={onSaveReason}
                  disabled={isUpdating || !isReasonValid}
                  className="flex items-center gap-2"
                >
                  {isUpdating && <LoadingIcon className="h-3 w-3 text-white" />}
                  Save
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <p className="whitespace-pre-wrap break-words text-gray-900">
                {policy.reason || "-"}
              </p>
              <button
                onClick={() => {
                  if (isEditingName) {
                    setIsEditingName(false);
                    setName(policy.name);
                    setNameError("");
                  }
                  setIsEditingReason(true);
                }}
                className="rounded-full p-1 hover:bg-gray-100"
                title="Edit Reason"
              >
                <PencilIcon className="h-4 w-4 text-gray-500" />
              </button>
            </div>
          )}
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500">Created By</p>
          <p className="text-gray-900">{policy.createdBy}</p>
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500">Created At</p>
          <p className="text-gray-900">{formatDate(policy.createdAt)}</p>
        </div>

        {policy.updatedAt && (
          <div>
            <p className="text-sm font-medium text-gray-500">Last Updated</p>
            <p className="text-gray-900">{formatDate(policy.updatedAt)}</p>
          </div>
        )}
      </div>
    </div>
  );
}
