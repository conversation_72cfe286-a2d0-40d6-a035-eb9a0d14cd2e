import { useState } from "react";
import { Dialog } from "@headlessui/react";
import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";
import DialogWrapper from "../ui/modal/DialogWrapper";
import Button from "../ui/Button";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { mapRoleNameToDescriptiveName } from "../../utils/user-management-utils";

interface RemovePermissionConfirmModalProps {
  show: boolean;
  onClose: () => void;
  permission: string;
  policyName: string;
  onRemove: () => Promise<void>;
  isRemoving?: boolean;
}

export default function RemoveDenyPolicyPermissionConfirmModal({
  show,
  onClose,
  permission,
  policyName,
  onRemove,
  isRemoving: externalIsRemoving,
}: RemovePermissionConfirmModalProps) {
  const [localIsRemoving, setLocalIsRemoving] = useState(false);

  const isRemoving = externalIsRemoving !== undefined ? externalIsRemoving : localIsRemoving;
  const permissionDisplayName = mapRoleNameToDescriptiveName(permission);

  const handleRemove = async () => {
    try {
      if (externalIsRemoving === undefined) {
        setLocalIsRemoving(true);
      }
      await onRemove();
      if (externalIsRemoving === undefined) {
        setLocalIsRemoving(false);
      }
      onClose();
    } catch (error) {
      console.error("Error removing permission:", error);
      if (externalIsRemoving === undefined) {
        setLocalIsRemoving(false);
      }
    }
  };

  return (
    <DialogWrapper show={show} onClose={onClose} maxWidth="max-w-xl">
      <div className="p-6">
        <div className="flex items-center justify-center">
          <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100">
            <ExclamationTriangleIcon className="h-6 w-6 text-red-600" aria-hidden="true" />
          </div>
        </div>
        <div className="mt-3 text-center sm:mt-5">
          <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
            Remove Permission
          </Dialog.Title>
          <div className="mt-2">
            <p className="text-sm text-gray-500">
              Are you sure you want to remove the permission{" "}
              <strong>{permissionDisplayName}</strong> from the deny policy{" "}
              <strong>{policyName}</strong>?
            </p>
            <p className="mt-2 text-sm text-gray-500">
              Users affected by this deny policy will regain access to this permission if they have
              it granted through roles or groups.
            </p>
          </div>
        </div>
        <div className="mt-5 flex justify-center gap-3 sm:mt-6">
          <Button variant="outlined" onClick={onClose} disabled={isRemoving}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleRemove}
            disabled={isRemoving}
            className="flex items-center gap-2"
          >
            {isRemoving ? (
              <>
                <LoadingIcon className="h-4 w-4 text-white" />
                Removing...
              </>
            ) : (
              "Remove Permission"
            )}
          </Button>
        </div>
      </div>
    </DialogWrapper>
  );
}
