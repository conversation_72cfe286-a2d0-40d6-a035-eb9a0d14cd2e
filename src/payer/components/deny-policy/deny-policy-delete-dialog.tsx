import React from "react";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import { Dialog } from "@headlessui/react";
import Button from "../../components/ui/Button";
import LoadingIcon from "~lib/components/icons/LoadingIcon";

interface DenyPolicyDeleteDialogProps {
  show: boolean;
  onClose: () => void;
  onDelete: () => void;
  policyName: string;
  isDeleting: boolean;
}

export default function DenyPolicyDeleteDialog({
  show,
  onClose,
  onDelete,
  policyName,
  isDeleting,
}: DenyPolicyDeleteDialogProps) {
  return (
    <DialogWrapper show={show} onClose={onClose} maxWidth="max-w-xl">
      <div>
        <div className="border-b border-gray-200 px-6 py-4">
          <Dialog.Title as="h3" className="text-lg font-medium text-gray-900">
            Delete Deny Policy
          </Dialog.Title>
        </div>
        <div className="p-6">
          <div className="flex flex-col gap-2">
            <p className="text-sm text-gray-500">
              Are you sure you want to delete the deny policy "{policyName}"? This action cannot be
              undone.{" "}
            </p>
            <p className="text-sm text-gray-500">
              Users affected by this deny policy will regain access to the restricted permissions if
              they have them granted through roles or groups.
            </p>
          </div>
          <div className="mt-6 flex justify-end space-x-3">
            <Button variant="outlined" onClick={onClose}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={onDelete}
              disabled={isDeleting}
              className="flex items-center gap-2"
            >
              {isDeleting && <LoadingIcon className="h-4 w-4 text-white" />}
              Delete
            </Button>
          </div>
        </div>
      </div>
    </DialogWrapper>
  );
}
