import { useEffect, useMemo, useRef, useState } from "react";
import { PlusIcon, TrashIcon } from "@heroicons/react/24/outline";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import Text from "../../components/ui/typography/Text";
import Button from "../../components/ui/Button";
import TableHeaderItem from "../../components/ui/table/TableHeaderItem";
import TableDataItem from "../../components/ui/table/TableDataItem";
import { DenyPolicyDto } from "../../lib/types/access-control/role";
import RemoveDenyPolicyUsersModal from "./remove-users-confirm-modal";
import PrimaryPagination from "../ui/pagination/PrimaryPagination";

interface DenyPolicyMembersListProps {
  policy: DenyPolicyDto;
  isLoading: boolean;
  onAddUsers: () => void;
  onRemoveMultipleUsers: (userIds: string[]) => void;
  page: number;
  size: number;
  totalElements?: number | undefined;
  totalPages?: number | undefined;
  onPageChange: (page: number) => void;
  onSizeChange: (size: number) => void;
}

export default function DenyPolicyMembersList({
  policy,
  isLoading,
  onAddUsers,
  onRemoveMultipleUsers,
  page,
  size,
  totalElements = 0,
  totalPages = 1,
  onPageChange,
  onSizeChange,
}: DenyPolicyMembersListProps) {
  const validTotalElements = totalElements || 0;
  const validTotalPages = Math.max(totalPages || 1, 1);
  const [selectedUserIds, setSelectedUserIds] = useState<Set<string>>(new Set());
  const [showRemoveConfirm, setShowRemoveConfirm] = useState(false);
  const selectAllCheckboxRef = useRef<HTMLInputElement>(null);

  const users = useMemo(() => policy.users || [], [policy.users]);

  useEffect(() => {
    setSelectedUserIds(new Set());
  }, [page, users]);

  useEffect(() => {
    if (selectAllCheckboxRef.current && users) {
      const someSelected = users.some((user) => selectedUserIds.has(user.userId));
      const allSelected = users.every((user) => selectedUserIds.has(user.userId));
      selectAllCheckboxRef.current.indeterminate = someSelected && !allSelected;
    }
  }, [selectedUserIds, users]);

  const handleToggleUser = (userId: string) => {
    setSelectedUserIds((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(userId)) {
        newSet.delete(userId);
      } else {
        newSet.add(userId);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    if (users && users.length > 0) {
      const currentPageUserIds = users.map((user) => user.userId);
      const allCurrentPageSelected = currentPageUserIds.every((id) => selectedUserIds.has(id));

      if (allCurrentPageSelected) {
        const newSet = new Set(selectedUserIds);
        currentPageUserIds.forEach((id) => newSet.delete(id));
        setSelectedUserIds(newSet);
      } else {
        const newSet = new Set(selectedUserIds);
        currentPageUserIds.forEach((id) => newSet.add(id));
        setSelectedUserIds(newSet);
      }
    }
  };

  const handleRemoveSelected = () => {
    if (selectedUserIds.size > 0) {
      setShowRemoveConfirm(true);
    }
  };

  const confirmRemoveUsers = async () => {
    onRemoveMultipleUsers(Array.from(selectedUserIds));
    setSelectedUserIds(new Set());
    setShowRemoveConfirm(false);
  };

  return (
    <div className="rounded-lg border border-gray-200 p-6">
      <div className="mb-4 flex items-center justify-between">
        <div>
          <Text variant="subheading">Deny Policy Members</Text>
          <Text variant="description" className="text-gray-600">
            {validTotalElements} {validTotalElements === 1 ? "member" : "members"} in total
          </Text>
        </div>
        <div className="flex gap-2">
          {selectedUserIds.size > 0 && (
            <Button
              variant="destructive"
              className="flex items-center gap-2"
              onClick={handleRemoveSelected}
            >
              <TrashIcon className="h-4 w-4" />
              Remove Selected ({selectedUserIds.size})
            </Button>
          )}
          <Button variant="outlined" className="flex items-center gap-2" onClick={onAddUsers}>
            <PlusIcon className="h-4 w-4" />
            Add Users
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center py-10">
          <LoadingIcon className="h-5 w-5 text-blue-400" />
          <p className="ml-2 text-blue-700">Loading members...</p>
        </div>
      ) : users.length > 0 ? (
        <>
          <div className="mb-4 overflow-hidden rounded-md border border-gray-200">
            <table className="w-full table-auto">
              <thead className="bg-gray-50 text-left">
                <tr>
                  <th className="px-4 py-3">
                    <div className="flex items-center gap-3">
                      <input
                        ref={selectAllCheckboxRef}
                        type="checkbox"
                        className="h-4 w-4 rounded border-gray-300"
                        checked={
                          users &&
                          users.length > 0 &&
                          users.every((user) => selectedUserIds.has(user.userId))
                        }
                        onChange={handleSelectAll}
                      />
                    </div>
                  </th>
                  <TableHeaderItem item="Name" />
                  <TableHeaderItem item="Username" />
                  <TableHeaderItem item="Email" />
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {users.map((user) => (
                  <tr
                    key={user.userId}
                    className={`hover:bg-gray-50 ${selectedUserIds.has(user.userId) ? "bg-blue-50" : ""}`}
                  >
                    <TableDataItem>
                      <input
                        type="checkbox"
                        className="h-4 w-4 rounded border-gray-300"
                        checked={selectedUserIds.has(user.userId)}
                        onChange={() => handleToggleUser(user.userId)}
                      />
                    </TableDataItem>
                    <TableDataItem>{user.name || "-"}</TableDataItem>
                    <TableDataItem>{user.userName || "-"}</TableDataItem>
                    <TableDataItem>{user.userEmail || "-"}</TableDataItem>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {policy.users.length > 0 && (
            <div className="mt-4">
              <PrimaryPagination
                pageNumber={page}
                pageSize={size}
                totalElements={validTotalElements}
                totalPages={validTotalPages}
                onPageNumberClick={onPageChange}
                onSizeChange={onSizeChange}
              />
            </div>
          )}
        </>
      ) : (
        <Text variant="paragraph" className="text-gray-500">
          No users in this deny policy.
        </Text>
      )}

      <RemoveDenyPolicyUsersModal
        show={showRemoveConfirm}
        onClose={() => setShowRemoveConfirm(false)}
        users={policy.users.filter((user) => selectedUserIds.has(user.userId))}
        policyName={policy.name}
        onRemove={confirmRemoveUsers}
      />
    </div>
  );
}
