import { useNavigate } from "react-router-dom";
import { DenyPolicyDto } from "../../lib/types/access-control/role";
import { format, parseISO } from "date-fns";
import Badge from "../ui/Badge";
import Button from "../ui/Button";
import { EyeIcon } from "@heroicons/react/24/outline";
import CardWrapper from "../ui/CardWrapper";
import DenyPolicySummaryItem from "./deny-policy-summary-item";

interface DenyPolicyCardProps {
  policy: DenyPolicyDto;
}

export default function DenyPolicyCard({ policy }: DenyPolicyCardProps) {
  const navigate = useNavigate();

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    try {
      return format(parseISO(dateString), "MMM dd, yyyy");
    } catch (e) {
      return dateString;
    }
  };

  const handleViewPolicy = () => {
    navigate(`/users/deny-policies/${policy.id}`);
  };

  return (
    <CardWrapper className="flex h-full w-full flex-col gap-6">
      <div className="flex flex-grow flex-col space-y-2">
        <h2 className="text-lg font-medium text-gray-900">{policy.name}</h2>
        {policy.reason && (
          <DenyPolicySummaryItem
            label="Reason"
            value={
              policy.reason.length > 50 ? `${policy.reason.substring(0, 50)}...` : policy.reason
            }
          />
        )}
        <DenyPolicySummaryItem label="Created At" value={formatDate(policy.createdAt)} />
        {policy.expiresAt && (
          <DenyPolicySummaryItem
            label="Expires At"
            value={
              <span className="flex items-center">
                {formatDate(policy.expiresAt)}
                {new Date(policy.expiresAt) < new Date() && (
                  <Badge color="red" text="Expired" className="ml-2" />
                )}
              </span>
            }
          />
        )}
        <DenyPolicySummaryItem label="Created By" value={policy.createdBy} />
      </div>
      <div className="flex flex-col justify-between self-end">
        <Button variant="outlined" className="flex items-center gap-2" onClick={handleViewPolicy}>
          <EyeIcon className="h-4 w-4" />
          View Details
        </Button>
      </div>
    </CardWrapper>
  );
}
