import { useState } from "react";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import Text from "../../components/ui/typography/Text";
import Button from "../../components/ui/Button";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { UserBasicDto } from "../../lib/types/access-control/role";

interface RemoveDenyPolicyUsersModalProps {
  show: boolean;
  onClose: () => void;
  users: UserBasicDto[];
  policyName: string;
  onRemove: () => Promise<void>;
}

export default function RemoveDenyPolicyUsersModal({
  show,
  onClose,
  users,
  policyName,
  onRemove,
}: RemoveDenyPolicyUsersModalProps) {
  const userCount = users.length;
  const [isRemoving, setIsRemoving] = useState(false);

  const handleRemove = async () => {
    try {
      setIsRemoving(true);
      await onRemove();
      setIsRemoving(false);
      onClose();
    } catch (error) {
      console.error("Error removing users from deny policy:", error);
      setIsRemoving(false);
    }
  };

  return (
    <DialogWrapper show={show} onClose={onClose} maxWidth="max-w-3xl">
      <div className="p-6">
        <div className="mb-6">
          <Text variant="paragraph">
            Are you sure you want to remove {userCount} {userCount === 1 ? "user" : "users"} from
            the deny policy <strong>{policyName}</strong>?
          </Text>
          <Text variant="description" className="mt-2">
            These users will no longer be subject to the permission restrictions defined in this
            deny policy.
          </Text>

          {userCount > 0 && (
            <div className="mt-4 max-h-60 overflow-y-auto rounded border border-gray-200">
              <table className="w-full table-auto">
                <thead className="bg-gray-50 text-left">
                  <tr>
                    <th className="px-4 py-2">
                      <Text variant="paragraph" className="font-medium">
                        Name
                      </Text>
                    </th>
                    <th className="px-4 py-2">
                      <Text variant="paragraph" className="font-medium">
                        Email
                      </Text>
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {users.map((user) => (
                    <tr key={user.userId}>
                      <td className="px-4 py-2">
                        <Text variant="paragraph">{user.name || user.userName}</Text>
                      </td>
                      <td className="px-4 py-2">
                        <Text variant="description" className="text-gray-600">
                          {user.userEmail || "-"}
                        </Text>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outlined" onClick={onClose} disabled={isRemoving}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleRemove}
            disabled={isRemoving}
            className="flex items-center gap-2"
          >
            {isRemoving && <LoadingIcon className="h-4 w-4 text-white" />}
            Remove {userCount} {userCount === 1 ? "User" : "Users"}
          </Button>
        </div>
      </div>
    </DialogWrapper>
  );
}
