import { useState } from "react";
import { DenyPolicyDto } from "../../lib/types/access-control/role";
import Text from "../../components/ui/typography/Text";
import Button from "../../components/ui/Button";
import { PlusIcon, XMarkIcon } from "@heroicons/react/24/solid";
import { mapRoleNameToDescriptiveName } from "../../utils/user-management-utils";
import RemoveDenyPolicyPermissionConfirmModal from "./remove-deny-policy-permission-confirm-modal";

interface DenyPolicyPermissionsProps {
  policy: DenyPolicyDto;
  onAddPermissions: () => void;
  onRemovePermission: (permission: string) => void;
  isDeletingPermission?: boolean;
}

export default function DenyPolicyPermissions({
  policy,
  onAddPermissions,
  onRemovePermission,
  isDeletingPermission = false,
}: DenyPolicyPermissionsProps) {
  const [showRemoveConfirm, setShowRemoveConfirm] = useState(false);
  const [permissionToRemove, setPermissionToRemove] = useState<string | null>(null);

  const handleRemoveClick = (permission: string) => {
    setPermissionToRemove(permission);
    setShowRemoveConfirm(true);
  };

  const confirmRemovePermission = async () => {
    if (permissionToRemove) {
      onRemovePermission(permissionToRemove);
      setPermissionToRemove(null);
    }
  };
  return (
    <div className="rounded-md border border-gray-200 bg-white p-6">
      <div className="mb-4 flex items-center justify-between">
        <Text variant="subheading">Denied Permissions</Text>
        <Button
          variant="outlined"
          className="flex items-center gap-1 text-xs"
          onClick={onAddPermissions}
        >
          <PlusIcon className="h-4 w-4" /> Add Permissions
        </Button>
      </div>

      {policy.deniedPermissions.length > 0 ? (
        <div className="flex flex-wrap gap-2">
          {policy.deniedPermissions.map((permission) => (
            <div
              key={permission}
              className="flex items-center gap-1 rounded-full bg-gray-100 px-3 py-1"
            >
              <p className="text-sm">{mapRoleNameToDescriptiveName(permission)}</p>
              <button
                type="button"
                onClick={() => handleRemoveClick(permission)}
                className="ml-1 rounded-full p-0.5 hover:bg-red-200"
                title="Remove Permission"
                aria-label="Remove Permission"
              >
                <XMarkIcon className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-sm text-gray-500">No permissions denied by this policy.</p>
      )}

      {permissionToRemove && (
        <RemoveDenyPolicyPermissionConfirmModal
          show={showRemoveConfirm}
          onClose={() => setShowRemoveConfirm(false)}
          permission={permissionToRemove}
          policyName={policy.name}
          onRemove={confirmRemovePermission}
          isRemoving={isDeletingPermission}
        />
      )}
    </div>
  );
}
