import { useAppDispatch, useAppSelector } from "../../store/hooks";
import { setActiveClaimsMenu } from "../../features/claims/claimsBatchingSlice";
import { ClockIcon } from "@heroicons/react/24/outline";
import { ArchiveBoxIcon } from "@heroicons/react/24/outline";
import ButtonGroup from "../ui/ButtonGroup";

export enum ClaimsMenuView {
  IndividualClaims = "Individual Claims",
  BatchedClaims = "Batched Claims",
}

interface ClaimsMenuViewItem {
  title: ClaimsMenuView;
  icon: JSX.Element;
}

const claimsMenuViewItems: ClaimsMenuViewItem[] = [
  {
    title: ClaimsMenuView.IndividualClaims,
    icon: <ClockIcon className="h-5 w-5" />,
  },
  {
    title: ClaimsMenuView.BatchedClaims,
    icon: <ArchiveBoxIcon className="h-5 w-5" />,
  },
];

export default function ClaimsViewSelector() {
  const dispatch = useAppDispatch();
  const activeClaimsMenu = useAppSelector((state) => state.claimsBatching.activeClaimsMenu);
  const handleVoucheringProgressClick = (title: ClaimsMenuView) => {
    dispatch(setActiveClaimsMenu(title));
  };

  return (
    <ButtonGroup
      activeOption={activeClaimsMenu}
      options={claimsMenuViewItems}
      setActiveOption={handleVoucheringProgressClick}
      titleClassName="text-sm"
    />
  );
}
