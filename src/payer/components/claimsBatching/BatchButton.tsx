import { MouseEvent } from "react";
import { setIsBatchClaimsModalOpen } from "../../features/claims/claimsBatchingSlice";
import { useAppDispatch, useAppSelector } from "../../store/hooks";

const BatchButton = () => {
  const dispatch = useAppDispatch();
  const checkedClaimsIds = useAppSelector((state) => state.claimsBatching.checkedClaimsIds);
  const handleClick = (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    dispatch(setIsBatchClaimsModalOpen(true));
  };
  return (
    <button
      className={`${
        checkedClaimsIds.length < 1
          ? "hidden"
          : "w-fit cursor-pointer rounded bg-btnBlue px-3 py-1.5 text-sm font-semibold text-white"
      }`}
      onClick={handleClick}
    >
      Batch Claims
    </button>
  );
};

export default BatchButton;
