import { useEffect, useState } from "react";
import { Empty } from "~lib/components";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { PAGE_SIZES } from "~lib/constants";
import { useGetAllClaimsQuery } from "../../api/features/claimsApi";
import cancel from "../../assets/material-symbols_close.png";
import {
  setAllClaimsToBeBatched,
  setCheckedClaimsIds,
  setIsClaimsBatchedSuccessfullyModalOpen,
  setIsSelectAllActive,
  setSelectedClaimsSum,
  setTotalSystemClaims,
} from "../../features/claims/claimsBatchingSlice";
import {
  GetAllClaimsParams,
  InvoiceBatchStatus,
  InvoiceData,
  InvoiceServiceGroup,
  InvoiceStatus,
} from "../../lib/types/claims/invoice";
import UserService from "../../services/UserService";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import PrimaryPagination from "../ui/pagination/PrimaryPagination";
import BatchButton from "./BatchButton";
import ClaimsAdjudicationBatchingFilters from "./ClaimsAdjudicationBatchingFilters";
import IndividualClaimsTableHeader from "./IndividualClaimsTableHeader";
import AdjudicationBatchingClaimsTableRow from "./AdjudicationBatchingClaimsTableRow";
import BatchClaimsModal from "./modals/BatchClaimsModal";
import ClaimsBatchedSuccessfullyModal from "./modals/ClaimsBatchedSuccessfullyModal";
import { isValidFilterName } from "../../utils/utils";

export default function AdjudicationBatchingClaimsTable() {
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(PAGE_SIZES[0] as number);
  const dispatch = useAppDispatch();
  const isClaimsBatchedSuccessfullyModalOpen = useAppSelector(
    (state) => state.claimsBatching.isClaimsBatchedSuccessfullyModalOpen,
  );

  const handleSuccessModalClose = () => {
    dispatch(setIsClaimsBatchedSuccessfullyModalOpen(false));
  };

  /*claims batching state*/
  const checkedClaimsIds = useAppSelector((state) => state.claimsBatching.checkedClaimsIds);
  const allClaimsToBeBatched = useAppSelector((state) => state.claimsBatching.allClaimsToBeBatched);
  const selectedProviderId = useAppSelector((state) => state.claimsBatching.selectedProviderId);
  const selectedRegion = useAppSelector((state) => state.claimsBatching.selectedRegion);
  const selectedAgeBandId = useAppSelector((state) => state.claimsBatching.selectedAgeBandId);

  const selectedScheme = useAppSelector((state) => state.claimsBatching.selectedScheme);
  const selectedBatchingServiceGroup = useAppSelector(
    (state) => state.claimsBatching.selectedBatchingServiceGroup,
  );
  const startDate = useAppSelector((state) => state.claimsBatching.adjudicationBatchingStartDate);
  const endDate = useAppSelector((state) => state.claimsBatching.adjudicationBatchingEndDate);

  const payerId: string = UserService.getPayer()?.tokenParsed?.["payerId"];
  const batchStatus: InvoiceBatchStatus = "NOT_BATCHED";
  const invoiceStatuses: InvoiceStatus[] = ["BALANCE_DEDUCTED"];

  const claimsToBeBatchedParams: GetAllClaimsParams = {
    payerId,
    providerId: Number(selectedProviderId),
    ageBandId: Number(selectedAgeBandId),
    region: selectedRegion,
    batchStatus,
    size,
    page,
    invoiceStatuses,
    ...(startDate && { startDate: (startDate as string).split("T")[0] as string }),
    ...(endDate && { endDate: (endDate as string).split("T")[0] as string }),
    ...(selectedScheme?.id && { planIds: [selectedScheme.id] }),
    ...(isValidFilterName(selectedBatchingServiceGroup) && {
      serviceGroups: [selectedBatchingServiceGroup as InvoiceServiceGroup],
    }),
  };

  const {
    data,
    isLoading,
    isFetching,
    error,
    refetch: refetchIndividualClaims,
  } = useGetAllClaimsQuery(claimsToBeBatchedParams);

  const claims = data?.data.content;
  const totalElements = data?.data.totalElements as number;
  const totalPages = data?.data.totalPages as number;

  /* fetching total system claims */
  const totalSystemClaimsParams: GetAllClaimsParams = {
    payerId,
    providerId: Number(selectedProviderId),
    ageBandId: Number(selectedAgeBandId),
    region: selectedRegion,
    batchStatus,
    size: 100_000_000,
    page: 1,
    invoiceStatuses,
    ...(startDate && { startDate: (startDate as string).split("T")[0] as string }),
    ...(endDate && { endDate: (endDate as string).split("T")[0] as string }),
    ...(selectedScheme?.id && { planIds: [selectedScheme.id] }),
    ...(isValidFilterName(selectedBatchingServiceGroup) && {
      serviceGroups: [selectedBatchingServiceGroup as InvoiceServiceGroup],
    }),
  };

  const { data: totalSystemClaimsResponse } = useGetAllClaimsQuery(totalSystemClaimsParams);

  useEffect(() => {
    refetchIndividualClaims();
  }, [page, refetchIndividualClaims]);

  const handlePageNumberClick = (page: number) => {
    setPage(page);
  };

  const handleSizeChange = (size: number) => {
    setSize(size);
    setPage(1);
  };

  const handleCancelSelection = () => {
    dispatch(setCheckedClaimsIds([]));
    dispatch(setSelectedClaimsSum(0));
    dispatch(setIsSelectAllActive(false));
  };

  useEffect(() => {
    dispatch(setAllClaimsToBeBatched(claims as InvoiceData[]));
    dispatch(setTotalSystemClaims(totalSystemClaimsResponse?.data.content as InvoiceData[]));
  }, [claims, dispatch, totalSystemClaimsResponse]);

  return (
    <section className="flex min-h-full flex-col overflow-x-auto bg-white">
      {checkedClaimsIds.length > 0 && (
        <div className="flex items-center justify-between space-x-12 pt-4">
          <div className="flex items-center gap-2 text-darkBlue">
            <button onClick={handleCancelSelection}>
              <img src={cancel} alt="cancel selection" className="w-4" />
            </button>
            <p className="flex items-center gap-1 text-base">
              <span>{checkedClaimsIds.length}</span> <span>selected</span>
            </p>
          </div>
          <BatchButton />
        </div>
      )}
      <div className="pb-4">
        <ClaimsAdjudicationBatchingFilters />
      </div>
      <section className={`${(isLoading && "py-40") || (isFetching && "py-40")}`}>
        {isLoading || isFetching ? (
          <div className="flex items-center justify-center space-x-2 self-center py-8">
            <LoadingIcon className="h-6 w-6 text-blue-400" />
            <p className="text-blue-700">Loading...</p>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center self-center py-8">
            <p className="text-red-700">Error loading claims. Try again later.</p>
          </div>
        ) : (
          allClaimsToBeBatched?.length === 0 && (
            <div className="self-center py-20">
              <Empty message="Claims not Found" />
            </div>
          )
        )}
      </section>
      {allClaimsToBeBatched?.length > 0 && !isLoading && !isFetching && !error && (
        <>
          <table className="table-auto">
            <IndividualClaimsTableHeader />
            <tbody>
              {allClaimsToBeBatched?.map((invoice: InvoiceData) => (
                <AdjudicationBatchingClaimsTableRow invoice={invoice} key={invoice.id} />
              ))}
            </tbody>
          </table>
          <div className="self-start py-4">
            <PrimaryPagination
              pageSize={size}
              totalElements={totalElements}
              totalPages={totalPages ?? 1}
              pageNumber={page}
              onPageNumberClick={handlePageNumberClick}
              onSizeChange={handleSizeChange}
            />
          </div>
        </>
      )}
      <BatchClaimsModal />
      <ClaimsBatchedSuccessfullyModal
        isSuccessModalOpen={isClaimsBatchedSuccessfullyModalOpen}
        onClose={handleSuccessModalClose}
      />
    </section>
  );
}
