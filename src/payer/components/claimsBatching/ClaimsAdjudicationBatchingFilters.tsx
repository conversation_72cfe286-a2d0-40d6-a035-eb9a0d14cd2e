import { useState } from "react";
import { useGetAllAgeBandsQuery } from "../../api/features/claimsApi";
import {
  useGetMembershipBenefitsCatalogQuery,
  useGetMembershipProvidersQuery,
  useGetMembershipRegionsQuery,
  useGetMembershipSchemesQuery,
} from "../../api/features/membershipApi";
import {
  setAdjudicationBatchingEndDate,
  setAdjudicationBatchingStartDate,
  setCatalogIds,
  setCheckedClaimsIds,
  setIsSelectAllActive,
  setSelectedAgeBandId,
  setSelectedBatchingServiceGroup,
  setSelectedBenefitId,
  setSelectedClaimsSum,
  setSelectedProviderId,
  setSelectedRegion,
  setSelectedScheme,
} from "../../features/claims/claimsBatchingSlice";
import { useHandleFilterSelectClear } from "../../hooks/useHandleFilterSelectClear";
import { AgeBand, AgeBandType } from "../../lib/types/claims/ageBand";
import { MembershipBenefitCatalog } from "../../lib/types/membership/memberBenefit";
import { MembershipProvider } from "../../lib/types/membership/memberProvider";
import { MembershipRegion } from "../../lib/types/membership/memberRegion";
import { MembershipScheme } from "../../lib/types/membership/memberScheme";
import UserService from "../../services/UserService";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import { isValidId } from "../../utils/utils";
import Button from "../ui/Button";
import AutoSuggestSelect from "../ui/input/AutoSuggestSelect";
import Calendar from "../ui/input/Calender";
import { Select } from "../ui/input/Select";
import { BatchingCriteria } from "./BatchingCriteriaSelector";

interface BatchingCriteriaSelect<OptType, ValType> {
  name: BatchingCriteria;
  options: Array<string | OptType>;
  value: ValType;
}

export default function ClaimsAdjudicationBatchingFilters() {
  const [providerQuery, setProviderQuery] = useState("");

  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];

  const { data: membershipRegionsResponse } = useGetMembershipRegionsQuery(payerId);
  const payerRegions = membershipRegionsResponse?.data || [];

  const { data: membershipBenefitsResponse } = useGetMembershipBenefitsCatalogQuery(payerId);
  const payerBenefitCatalogs = membershipBenefitsResponse?.data || [];

  const { data: membershipSchemesResponse } = useGetMembershipSchemesQuery(payerId);
  const payerSchemes = membershipSchemesResponse?.data || [];

  const bandType: AgeBandType = "BATCHING";
  const { data: ageBandsResponse } = useGetAllAgeBandsQuery({ bandType });
  const payerAgeBands = ageBandsResponse?.data || [];

  const { data: membershipProvidersResponse } = useGetMembershipProvidersQuery({
    payerId,
    ...(providerQuery.length > 0 && { query: providerQuery }),
  });
  const payerProviders = membershipProvidersResponse?.data.content || [];

  const dispatch = useAppDispatch();

  /** claims batching state */
  const selectedAgeBandId = useAppSelector((state) => state.claimsBatching.selectedAgeBandId);
  const selectedProviderId = useAppSelector((state) => state.claimsBatching.selectedProviderId);
  const selectedRegion = useAppSelector((state) => state.claimsBatching.selectedRegion);
  const selectedOption = useAppSelector((state) => state.claimsBatching.selectedBatchingOption);
  const selectedScheme = useAppSelector((state) => state.claimsBatching.selectedScheme);
  const selectedBatchingServiceGroup = useAppSelector(
    (state) => state.claimsBatching.selectedBatchingServiceGroup,
  );
  const fromDate = useAppSelector((state) => state.claimsBatching.adjudicationBatchingStartDate);
  const toDate = useAppSelector((state) => state.claimsBatching.adjudicationBatchingEndDate);

  const agingBatchingCriteria: BatchingCriteriaSelect<AgeBand, string> = {
    name: BatchingCriteria.AGING,
    options: ["Select the aging criteria", ...payerAgeBands],
    value: selectedAgeBandId,
  };

  const benefitsBatchingCriteria: BatchingCriteriaSelect<MembershipBenefitCatalog, string> = {
    name: BatchingCriteria.BENEFIT,
    options: ["Select the benefit", ...payerBenefitCatalogs],
    value: selectedBatchingServiceGroup,
  };

  const providerBatchingCriteria: BatchingCriteriaSelect<MembershipProvider, string> = {
    name: BatchingCriteria.PROVIDER,
    options: ["Select the main provider", ...payerProviders],
    value: selectedProviderId,
  };

  const regionBatchingCriteria: BatchingCriteriaSelect<MembershipRegion, string> = {
    name: BatchingCriteria.REGION,
    options: ["Select the region", ...payerRegions],
    value: selectedRegion,
  };

  const schemeBatchingCriteria: BatchingCriteriaSelect<MembershipScheme, string> = {
    name: BatchingCriteria.SCHEME,
    options: ["Select the scheme", ...payerSchemes],
    value: selectedScheme?.id || "",
  };

  const batchingCriteriaList = [
    agingBatchingCriteria,
    benefitsBatchingCriteria,
    providerBatchingCriteria,
    regionBatchingCriteria,
    schemeBatchingCriteria,
  ];

  const selectedCriteria = batchingCriteriaList.find(
    (criteria) => criteria.name === selectedOption,
  );
  const otherCriteria = batchingCriteriaList.filter((criteria) => criteria.name !== selectedOption);

  function handleSelect(
    criteria: BatchingCriteria,
    selectedOption:
      | AgeBand
      | MembershipProvider
      | MembershipRegion
      | MembershipBenefitCatalog
      | MembershipScheme
      | Option
      | null,
  ) {
    switch (criteria) {
      case BatchingCriteria.REGION:
        dispatch(setSelectedRegion((selectedOption as MembershipRegion)?.name || ""));
        break;
      case BatchingCriteria.PROVIDER:
        dispatch(
          setSelectedProviderId(((selectedOption as MembershipProvider)?.id as string) || ""),
        );
        break;
      case BatchingCriteria.BENEFIT:
        dispatch(setSelectedBatchingServiceGroup(selectedOption?.name || ""));
        break;
      case BatchingCriteria.AGING:
        dispatch(setSelectedAgeBandId(((selectedOption as AgeBand)?.id as string) || ""));
        break;
      case BatchingCriteria.SCHEME:
        dispatch(setSelectedScheme(selectedOption as MembershipScheme));
        break;
    }
    dispatch(setIsSelectAllActive(false));
    resetSelectionState();
  }

  const filterSelected =
    selectedAgeBandId ||
    selectedBatchingServiceGroup ||
    selectedProviderId ||
    selectedRegion ||
    isValidId(selectedScheme?.id) ||
    fromDate ||
    toDate;

  const handleFilterSelectClearAging = useHandleFilterSelectClear(
    `filter-select-${BatchingCriteria.AGING}`,
  );
  const handleFilterSelectClearBenefit = useHandleFilterSelectClear(
    `filter-select-${BatchingCriteria.BENEFIT}`,
  );
  const handleFilterSelectClearProvider = useHandleFilterSelectClear(
    `filter-select-${BatchingCriteria.PROVIDER}`,
  );
  const handleFilterSelectClearRegion = useHandleFilterSelectClear(
    `filter-select-${BatchingCriteria.REGION}`,
  );
  const handleFilterSelectClearScheme = useHandleFilterSelectClear(
    `filter-select-${BatchingCriteria.SCHEME}`,
  );

  const clearFilterSelectValues = () => {
    handleFilterSelectClearAging();
    handleFilterSelectClearBenefit();
    handleFilterSelectClearProvider();
    handleFilterSelectClearRegion();
    handleFilterSelectClearScheme();
  };

  const clearFilters = () => {
    dispatch(setSelectedAgeBandId(""));
    dispatch(setSelectedBenefitId(""));
    dispatch(setSelectedProviderId(""));
    dispatch(setSelectedRegion(""));
    dispatch(setCatalogIds([]));
    dispatch(setIsSelectAllActive(false));
    dispatch(setCheckedClaimsIds([]));
    dispatch(setSelectedClaimsSum(0));
    dispatch(setSelectedScheme({} as MembershipScheme));
    dispatch(setSelectedBatchingServiceGroup(""));
    dispatch(setAdjudicationBatchingStartDate(""));
    dispatch(setAdjudicationBatchingEndDate(""));
    clearFilterSelectValues();
  };

  const resetSelectionState = () => {
    dispatch(setIsSelectAllActive(false));
    dispatch(setCheckedClaimsIds([]));
    dispatch(setSelectedClaimsSum(0));
  };

  function convertToUniqueServiceGroups(catalogItems: Array<MembershipBenefitCatalog>): Option[] {
    const uniqueServiceGroups = new Map<string, string>();

    catalogItems.forEach((item) => {
      if (!uniqueServiceGroups.has(item.serviceGroup)) {
        uniqueServiceGroups.set(item.serviceGroup, item.serviceGroup);
      }
    });

    return Array.from(uniqueServiceGroups).map(([serviceGroup, id]) => ({
      id: id,
      name: serviceGroup,
    }));
  }

  const uniqueBenefitServiceGroups = convertToUniqueServiceGroups(payerBenefitCatalogs);
  const getSelectOptions = (criteria: BatchingCriteria) => {
    const optionsMap = {
      [BatchingCriteria.REGION]: payerRegions,
      [BatchingCriteria.PROVIDER]: payerProviders,
      [BatchingCriteria.BENEFIT]: uniqueBenefitServiceGroups,
      [BatchingCriteria.AGING]: payerAgeBands,
      [BatchingCriteria.SCHEME]: payerSchemes,
    };
    return optionsMap[criteria];
  };

  interface Option {
    id: string;
    name: string;
  }

  const handleStartDateChange = (value: string) => {
    dispatch(setAdjudicationBatchingStartDate(value));
    dispatch(setIsSelectAllActive(false));
    resetSelectionState();
  };

  const handleEndDateChange = (value: string) => {
    dispatch(setAdjudicationBatchingEndDate(value));
    dispatch(setIsSelectAllActive(false));
    resetSelectionState();
  };

  // Helper to map options to SelectOption[]
  function mapToSelectOptions(options: Option[] | undefined) {
    if (!options) return [];
    return options.map((option) => ({
      key: option.id,
      value: option.id,
      label: option.name,
    }));
  }

  // Helper to get selected value for Select
  function getSelectedValue(criteria: BatchingCriteriaSelect<unknown, string>): string {
    if (
      typeof criteria.value === "object" &&
      criteria.value !== null &&
      "id" in (criteria.value as object)
    ) {
      return (criteria.value as { id: string }).id;
    }
    return criteria.value;
  }

  return (
    <section className="grid w-full grid-cols-5 flex-wrap items-center gap-4 py-6">
      {selectedCriteria && (
        <div className="flex flex-col space-y-2">
          <label htmlFor={selectedCriteria.name} className="text-sm font-medium text-midGray">
            {selectedCriteria.name}
          </label>
          {selectedCriteria.name === BatchingCriteria.PROVIDER ? (
            <AutoSuggestSelect
              value={getSelectedValue(selectedCriteria)}
              options={mapToSelectOptions(getSelectOptions(selectedCriteria.name) as Option[])}
              placeholder={selectedCriteria.options[0] as string}
              query={providerQuery}
              onQueryChange={setProviderQuery}
              onChange={(selectedId) => {
                const found = (getSelectOptions(selectedCriteria.name) as Option[]).find(
                  (opt) => opt.id === selectedId,
                );
                handleSelect(selectedCriteria.name, found || null);
              }}
            />
          ) : (
            <Select
              value={getSelectedValue(selectedCriteria)}
              options={mapToSelectOptions(getSelectOptions(selectedCriteria.name) as Option[])}
              placeholder={selectedCriteria.options[0] as string}
              onChange={(selectedId) => {
                const found = (getSelectOptions(selectedCriteria.name) as Option[]).find(
                  (opt) => opt.id === selectedId,
                );
                handleSelect(selectedCriteria.name, found || null);
              }}
            />
          )}
        </div>
      )}
      {otherCriteria.map((criteria) => (
        <div className="flex flex-col space-y-2" key={criteria.name}>
          <label htmlFor={criteria.name} className="text-sm font-medium text-midGray">
            {criteria.name}
          </label>
          {criteria.name === BatchingCriteria.PROVIDER ? (
            <AutoSuggestSelect
              value={getSelectedValue(criteria)}
              options={mapToSelectOptions(getSelectOptions(criteria.name) as Option[])}
              placeholder={criteria.options[0] as string}
              query={providerQuery}
              onQueryChange={setProviderQuery}
              onChange={(selectedId) => {
                const found = (getSelectOptions(criteria.name) as Option[]).find(
                  (opt) => opt.id === selectedId,
                );
                handleSelect(criteria.name, found || null);
              }}
            />
          ) : (
            <Select
              value={getSelectedValue(criteria)}
              options={mapToSelectOptions(getSelectOptions(criteria.name) as Option[])}
              placeholder={criteria.options[0] as string}
              onChange={(selectedId) => {
                const found = (getSelectOptions(criteria.name) as Option[]).find(
                  (opt) => opt.id === selectedId,
                );
                handleSelect(criteria.name, found || null);
              }}
            />
          )}
        </div>
      ))}
      <section className="flex flex-col space-y-2">
        <label htmlFor={"sign-off-date"} className="text-sm font-medium text-midGray">
          <p>Start Date</p>
        </label>
        <Calendar
          placeholder="Start Date"
          value={fromDate as string}
          onChange={(value) => handleStartDateChange(value)}
        />
      </section>
      <section className="flex flex-col space-y-2">
        <label htmlFor={"sign-off-date"} className="text-sm font-medium text-midGray">
          <p>End Date</p>
        </label>
        <Calendar
          placeholder="End Date"
          value={toDate as string}
          onChange={(value) => handleEndDateChange(value)}
        />
      </section>
      <Button
        className="self-end whitespace-nowrap"
        disabled={!filterSelected}
        onClick={clearFilters}
      >
        Clear Filter
      </Button>
    </section>
  );
}
