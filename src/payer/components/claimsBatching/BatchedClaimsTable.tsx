import { useEffect, useState } from "react";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { PAGE_SIZES } from "~lib/constants";
import { useGetBatchedInvoicesQuery } from "../../api/features/claimsApi";
import { setIsUsersAssignedSuccessfullyModalOpen } from "../../features/claims/claimsBatchingSlice";
import { BatchedInvoicesList } from "../../lib/types/claims/batchedInvoicesList";
import UserService from "../../services/UserService";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import NoBatches from "../illustrations/NoBatches";
import EmptyState from "../ui/EmptyState";
import PrimaryPagination from "../ui/pagination/PrimaryPagination";
import BatchedClaimsTableRow from "./BatchedClaimsTableRow";
import BatchAllocatedSuccessfullyModal from "./modals/BatchAllocatedSuccessfullyModal";

export default function BatchedClaimsTable() {
  const [batchedInvoicesList, setBatchedInvoicesList] = useState<BatchedInvoicesList[]>([]);
  const [pageNumber, setPageNumber] = useState(1);
  const [pageSize, setPageSize] = useState(PAGE_SIZES[0] as number);
  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const batchAllocationStatus = useAppSelector(
    (state) => state.claimsBatching.batchAllocationStatus,
  );
  const batchVettingStatus = useAppSelector((state) => state.claimsBatching.batchVettingStatus);
  const dispatch = useAppDispatch();
  const isUsersAssignedSuccessfullyModalOpen = useAppSelector(
    (state) => state.claimsBatching.isUsersAssignedSuccessfullyModalOpen,
  );
  const assignedBatchId = useAppSelector((state) => state.claimsBatching.assignedBatchId);

  const {
    data,
    isLoading,
    isFetching,
    error,
    refetch: refetchBatchedClaims,
  } = useGetBatchedInvoicesQuery({
    payerId,
    batchAllocationStatus: batchAllocationStatus,
    invoiceBatchStatus: batchVettingStatus,
    pageNumber,
    pageSize,
  });

  const totalElements = data?.data.totalElements as number;
  const totalPages = data?.data.totalPages as number;

  const handlePageNumberClick = (page: number) => {
    setPageNumber(page);
  };

  const handleSizeChange = (size: number) => {
    setPageSize(size);
    setPageNumber(1);
  };

  useEffect(() => {
    if (data?.data.content) {
      setBatchedInvoicesList(data?.data.content as BatchedInvoicesList[]);
    }
  }, [data?.data.content, setBatchedInvoicesList]);

  useEffect(() => {
    refetchBatchedClaims();
  }, [pageNumber, refetchBatchedClaims]);

  return (
    <section className="">
      <section className="mb-4 flex flex-col">
        {isLoading || isFetching ? (
          <div className="flex items-center justify-center space-x-2 py-8">
            <LoadingIcon className="h-6 w-6 text-blue-400" />
            <p className="text-blue-700">Loading...</p>
          </div>
        ) : (
          error && (
            <div className="flex items-center justify-center py-32">
              <p className="text-red-700">Error loading batched claims. Try again later.</p>
            </div>
          )
        )}
        <table>
          <tbody>
            {batchedInvoicesList?.length === 0 && !isFetching && !isLoading && !error ? (
              <EmptyState
                illustration={<NoBatches />}
                message={{
                  title: "No Batches Created Yet",
                  description:
                    "You haven't created any batches. Click on the individual claims to get started",
                }}
              />
            ) : (
              !isLoading &&
              !isFetching &&
              !error && (
                <>
                  {batchedInvoicesList?.map((batch) => (
                    <BatchedClaimsTableRow
                      batch={batch}
                      refetchBatchedClaims={refetchBatchedClaims}
                      setPageNumber={setPageNumber}
                    />
                  ))}
                </>
              )
            )}
          </tbody>
        </table>
        {batchedInvoicesList?.length > 0 && !isFetching && !isLoading && !error && (
          <div className="self-start">
            <PrimaryPagination
              pageSize={pageSize}
              totalElements={totalElements}
              totalPages={totalPages}
              pageNumber={pageNumber}
              onPageNumberClick={handlePageNumberClick}
              onSizeChange={handleSizeChange}
            />
          </div>
        )}
      </section>
      <BatchAllocatedSuccessfullyModal
        batchId={assignedBatchId}
        isSuccessModalOpen={isUsersAssignedSuccessfullyModalOpen}
        onClose={() => dispatch(setIsUsersAssignedSuccessfullyModalOpen(false))}
      />
    </section>
  );
}
