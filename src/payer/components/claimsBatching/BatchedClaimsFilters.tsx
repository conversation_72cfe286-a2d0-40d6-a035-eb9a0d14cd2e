import { ChangeEvent } from "react";
import {
  setBatchAllocationStatus,
  setBatchVettingStatus,
} from "../../features/claims/claimsBatchingSlice";
import { BatchAllocationStatus, InvoiceBatchStatus } from "../../lib/types/claims/batchedInvoices";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import { AllocationStatus } from "./BatchedClaimsTableWrapper";

const allocationFilter: { name: string; options: Array<string> } = {
  name: "Allocation Status",
  options: ["Select the allocation status", "UnAllocated", "Allocated"],
};

const vettingFilter: { name: string; options: Array<string> } = {
  name: "Batch Status",
  options: ["Select the batch status", "Completed", "UnVetted", "InProgress"],
};

export default function BatchedClaimsFilters() {
  const dispatch = useAppDispatch();
  const batchAllocationStatus = useAppSelector(
    (state) => state.claimsBatching.batchAllocationStatus,
  );
  const batchVettingStatus = useAppSelector((state) => state.claimsBatching.batchVettingStatus);

  const handleFilterChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    switch (name) {
      case allocationFilter.name:
        if (value === AllocationStatus.UNALLOCATED) {
          dispatch(setBatchVettingStatus(""));
          dispatch(setBatchAllocationStatus(value as BatchAllocationStatus));
        } else {
          dispatch(setBatchAllocationStatus(value as BatchAllocationStatus));
        }
        break;
      case vettingFilter.name:
        dispatch(setBatchVettingStatus(value as InvoiceBatchStatus));
        break;
    }
  };

  return (
    <form className="flex w-[70%] items-center space-x-6 px-8 py-6">
      <div key={allocationFilter.name} className="flex basis-1/2 flex-col space-y-2">
        <h2 className="text-sm font-medium text-midGray">{allocationFilter.name}</h2>
        <select
          name={allocationFilter.name}
          id={allocationFilter.name}
          value={batchAllocationStatus}
          className="rounded-lg border border-[#D1D5DB] px-2 py-2 text-sm"
          onChange={handleFilterChange}
        >
          {allocationFilter.options.map((option) => (
            <option value={option} className="text-sm">
              {option}
            </option>
          ))}
        </select>
      </div>
      <div
        key={vettingFilter.name}
        className={`flex basis-1/2 flex-col space-y-2 ${
          batchAllocationStatus !== AllocationStatus.ALLOCATED && "cursor-not-allowed"
        }`}
      >
        <h2
          className={`text-sm ${
            batchAllocationStatus !== AllocationStatus.ALLOCATED
              ? "text-lightGray"
              : "font-medium text-midGray"
          }`}
        >
          {vettingFilter.name}
        </h2>
        <select
          disabled={batchAllocationStatus !== AllocationStatus.ALLOCATED}
          name={vettingFilter.name}
          id={vettingFilter.name}
          value={batchVettingStatus}
          className="rounded-lg border border-[#D1D5DB] px-2 py-2 text-sm disabled:cursor-not-allowed disabled:text-lightGray"
          onChange={handleFilterChange}
        >
          {vettingFilter.options.map((option) => (
            <option value={option} className="text-sm">
              {option === "InProgress"
                ? "In Progress"
                : option === "UnVetted"
                  ? "Unvetted"
                  : option}
            </option>
          ))}
        </select>
      </div>
    </form>
  );
}
