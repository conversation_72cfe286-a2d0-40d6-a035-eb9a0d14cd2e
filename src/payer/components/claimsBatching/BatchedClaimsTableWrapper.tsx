import { useAppSelector } from "../../store/hooks";
import { isValidBatchAllocationStatus, isValidInvoiceBatchStatus } from "../../utils/utils";
import NoBatches from "../illustrations/NoBatches";
import EmptyState from "../ui/EmptyState";
import BatchedClaimsFilters from "./BatchedClaimsFilters";
import BatchedClaimsTable from "./BatchedClaimsTable";

export enum AllocationStatus {
  UNALLOCATED = "UnAllocated",
  ALLOCATED = "Allocated",
}

export default function BatchedClaimsTableWrapper() {
  const batchAllocationStatus = useAppSelector(
    (state) => state.claimsBatching.batchAllocationStatus,
  );
  const batchVettingStatus = useAppSelector((state) => state.claimsBatching.batchVettingStatus);

  const vettingStatusSelected = isValidInvoiceBatchStatus(batchVettingStatus);
  const allocationStatusSelected = isValidBatchAllocationStatus(batchAllocationStatus);

  return (
    <section className="flex flex-col">
      <BatchedClaimsFilters />
      {batchAllocationStatus === "Allocated" && !vettingStatusSelected ? (
        <EmptyState
          illustration={<NoBatches />}
          message={{
            title: "No Batches",
            description: "Please select the batch status to view the list of available batches...",
          }}
        />
      ) : allocationStatusSelected && vettingStatusSelected ? (
        <BatchedClaimsTable />
      ) : allocationStatusSelected ? (
        <BatchedClaimsTable />
      ) : (
        !allocationStatusSelected && (
          <EmptyState
            illustration={<NoBatches />}
            message={{
              title: "No Batches",
              description:
                "Please select the allocation status to view the list of available batches...",
            }}
          />
        )
      )}
    </section>
  );
}
