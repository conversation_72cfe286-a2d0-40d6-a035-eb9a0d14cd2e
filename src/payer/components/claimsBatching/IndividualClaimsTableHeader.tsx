import {
  setCheckedClaimsIds,
  setIsSelectAllActive,
  setSelectedClaimsSum,
} from "../../features/claims/claimsBatchingSlice";
import { useAppDispatch, useAppSelector } from "../../store/hooks";

import TableHeaderItem from "../ui/table/TableHeaderItem";

export default function IndividualClaimsTableHeader() {
  const dispatch = useAppDispatch();
  const isSelectAllActive = useAppSelector((state) => state.claimsBatching.isSelectAllActive);
  const totalSystemClaims = useAppSelector((state) => state.claimsBatching.totalSystemClaims);

  const handleSelectAll = () => {
    if (isSelectAllActive) {
      dispatch(setCheckedClaimsIds([]));
      dispatch(setSelectedClaimsSum(0));
    } else {
      const currentIds = totalSystemClaims.map((claim) => claim.id);
      const totalAmount = totalSystemClaims.reduce(
        (acc, claim) => acc + (claim.totalAmount ?? 0),
        0,
      );

      dispatch(setCheckedClaimsIds(currentIds as string[]));
      dispatch(setSelectedClaimsSum(totalAmount));
    }
    dispatch(setIsSelectAllActive(!isSelectAllActive));
  };

  return (
    <thead className="text-left">
      <tr className="bg-[#F9FAFB] text-[13px]">
        <th className="whitespace-nowrap px-4 py-2 uppercase">
          <input
            className="form-checkbox h-5 w-5"
            type="checkbox"
            checked={isSelectAllActive}
            onChange={handleSelectAll}
            title="Select All"
          />
        </th>
        <TableHeaderItem className="text-xs" item="Member No" />
        <TableHeaderItem className="text-xs" item="Member Name" />
        <TableHeaderItem className="text-xs" item="Scheme" />
        <TableHeaderItem className="text-xs" item="Provider" />
        <TableHeaderItem className="text-xs" item="Invoice No" />
        <TableHeaderItem className="text-xs" item="Invoice Amount" />
        <TableHeaderItem className="text-xs" item="Invoice Date" />
        <TableHeaderItem className="text-xs" item="Age" />
      </tr>
    </thead>
  );
}
