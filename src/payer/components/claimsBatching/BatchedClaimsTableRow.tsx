import { useState } from "react";
import activeUser from "../../assets/active_user.png";
import disabledSettings from "../../assets/disabled_settings.png";
import solarSettingsLinear from "../../assets/solar_settings-linear.png";
import userPlus from "../../assets/user-plus.png";
import { BatchedInvoicesList } from "../../lib/types/claims/batchedInvoicesList";
import { formatDate, formatValue } from "../../lib/Utils";
import Badge from "../ui/Badge";
import BatchIdTableDataItem from "../ui/table/BatchIdTableDataItem";
import BatchTableDataItem from "../ui/table/BatchTableDataItem";
import AssignUsersListModal from "./modals/batchSettings/AssignUsersListModal";
import BatchSettingsModal from "./modals/BatchSettingsModal";
import { useAppDispatch } from "../../store/hooks";
import { setSelectedBatchId } from "../../features/claims/claimsBatchingSlice";
import { claimsApi } from "../../api/features/claimsApi";
import { getDifferenceInDays } from "../../utils/utils";

export default function BatchedClaimsTableRow({
  batch,
  refetchBatchedClaims,
  setPageNumber,
}: {
  batch: BatchedInvoicesList;
  refetchBatchedClaims: () => void;
  setPageNumber: (pageNumber: number) => void;
}) {
  const [isBatchSettingsModalOpen, setIsBatchSettingsModalOpen] = useState(false);
  const [isUsersListModalOpen, setIsUsersListModalOpen] = useState(false);
  const dispatch = useAppDispatch();

  const handleUsersListModalOpen = () => {
    setIsUsersListModalOpen(true);
  };

  const handleBatchSettingsModalOpen = () => {
    dispatch(setSelectedBatchId(Number(batch.id)));
    dispatch(claimsApi.endpoints.getBatchById.initiate(batch.id));
    setIsBatchSettingsModalOpen(true);
  };

  const handleBatchSettingsModalClose = () => {
    setIsBatchSettingsModalOpen(false);
  };

  const handleUsersListModalClose = () => {
    setIsUsersListModalOpen(false);
  };

  const batchAge = getDifferenceInDays(
    batch.createdOn.replaceAll("/", "."),
    formatDate(new Date(Date.now())),
  );

  const batchAgeString = `${batchAge} ${batchAge === 1 ? "Day" : "Days"}`;

  return (
    <tr className="mb-4 ml-6 mt-4" key={batch.id}>
      <BatchIdTableDataItem batchId={batch?.id} />
      <BatchTableDataItem title={"Batch Criteria"} value={batch.batchCriteria as string} />
      <BatchTableDataItem
        title={"Invoice Count"}
        value={`${batch.invoiceCount} ${batch.invoiceCount > 1 ? "invoices" : "invoice"}`}
      />
      <BatchTableDataItem title={"Total Amount"} value={formatValue(batch.totalAmount)} />
      <BatchTableDataItem
        title={"Created By"}
        value={
          batch?.createdBy?.firstName && batch?.createdBy.lastName
            ? `${batch?.createdBy?.firstName} ${batch?.createdBy?.lastName}`
            : batch?.createdBy?.userName
        }
      />
      <BatchTableDataItem title={"Created On"} value={batch.createdOn} />
      <BatchTableDataItem title={"Batch Age"} value={batchAgeString} />
      <td className="pb-4 pl-8 pt-4">
        <Badge
          hasDot
          color={
            batch?.allocationStatus === "Allocated" && batch?.batchStatus === "InProgress"
              ? "yellow"
              : batch?.batchStatus === "Completed"
                ? "green"
                : "red"
          }
          text={
            batch?.allocationStatus === "Allocated" && batch?.batchStatus === "InProgress"
              ? "In Progress"
              : batch?.batchStatus === "Completed"
                ? "Vetted"
                : batch?.batchStatus === "UnVetted" || batch?.batchStatus === null
                  ? "Unvetted"
                  : "Unallocated"
          }
          textClassName="text-xs"
        />
      </td>
      <td className="pb-4 pl-8 pt-4">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleUsersListModalOpen}
            disabled={batch?.allocationStatus === "Allocated"}
            className="cursor-pointer disabled:cursor-not-allowed"
          >
            {batch?.allocationStatus === "Allocated" ? (
              <img src={userPlus} alt="add user" className="w-5" />
            ) : (
              <img src={activeUser} alt="add user" className="w-5" />
            )}
          </button>
          <button
            onClick={handleBatchSettingsModalOpen}
            disabled={batch?.allocationStatus === "UnAllocated" || batch?.allocationStatus === null}
            className="cursor-pointer disabled:cursor-not-allowed"
          >
            {batch.allocationStatus === "UnAllocated" || batch.allocationStatus === null ? (
              <img src={disabledSettings} alt="solar settings" className="w-5" />
            ) : (
              <img src={solarSettingsLinear} alt="solar settings" className="w-5" />
            )}
          </button>
        </div>
      </td>
      <BatchSettingsModal
        isBatchSettingsModalOpen={isBatchSettingsModalOpen}
        onClose={handleBatchSettingsModalClose}
      />
      <AssignUsersListModal
        setPageNumber={setPageNumber}
        refetchBatchedClaims={refetchBatchedClaims}
        batchId={batch?.id}
        isUsersListModalOpen={isUsersListModalOpen}
        onClose={handleUsersListModalClose}
      />
    </tr>
  );
}
