import { ChangeEvent } from "react";
import { setSelectedBatchingOption } from "../../features/claims/claimsBatchingSlice";
import { useAppDispatch, useAppSelector } from "../../store/hooks";

export enum BatchingCriteria {
  AGING = "Aging",
  REGION = "Region",
  PROVIDER = "Provider",
  BENEFIT = "Benefit",
  SCHEME = "Scheme",
}

export default function BatchingCriteriaSelector() {
  const dispatch = useAppDispatch();
  const selectedBatchingOption = useAppSelector(
    (state) => state.claimsBatching.selectedBatchingOption,
  );

  const handleOptionChange = (e: ChangeEvent<HTMLSelectElement>) => {
    dispatch(setSelectedBatchingOption(e.target.value));
  };

  return (
    <div className="flex items-center space-x-6">
      <label htmlFor="filter" className="mr-2 inline-block  text-darkGray">
        Select the main batching criteria
      </label>
      <select
        id="filter"
        value={selectedBatchingOption as string}
        onChange={handleOptionChange}
        className="w-[50%] rounded-md border border-gray-300 px-4 py-2"
      >
        <option className="whitespace-nowrap  text-darkGray" value="" disabled selected hidden>
          Select criteria
        </option>
        {Object.values(BatchingCriteria).map((criteria) => (
          <option key={criteria} value={criteria} className="text-base">
            {criteria}
          </option>
        ))}
      </select>
    </div>
  );
}
