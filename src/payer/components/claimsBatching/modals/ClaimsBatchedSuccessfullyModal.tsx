import { Dialog, Transition } from "@headlessui/react";
import { Fragment } from "react";
import Button from "../../ui/Button";
import SuccessAnimation from "../../ui/modal/SuccessAnimation";
import { useAppDispatch } from "../../../store/hooks";
import { claimsApi } from "../../../api/features/claimsApi";

export default function ClaimsBatchedSuccessfullyModal({
  onClose,
  isSuccessModalOpen,
}: {
  onClose: () => void;
  isSuccessModalOpen: boolean;
}) {
  const dispatch = useAppDispatch();
  const handleDoneClick = () => {
    onClose();
    dispatch(claimsApi.util.invalidateTags(["Claims", "BatchedInvoices"]));
  };

  return (
    <>
      <Transition appear show={isSuccessModalOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={onClose}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/25" />
          </Transition.Child>
          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-[35rem] transform overflow-hidden rounded-md bg-white py-8 text-left align-middle shadow-xl transition-all">
                  <div className="mt-2 flex flex-col space-y-4">
                    <div className="self-center pb-4">
                      <SuccessAnimation />
                    </div>
                    <h2 className="whitespace-nowrap text-center text-2xl font-semibold text-darkGray">
                      Batch Created Successfully
                    </h2>
                    <div className="text-gray flex flex-col items-center justify-center self-center text-center text-sm">
                      <p className="mx-8">Your claims have been batched</p>
                      <p>successfully.</p>
                      <p>Thank you!!!</p>
                    </div>

                    <Button className="w-[20%] self-center" onClick={handleDoneClick}>
                      Done
                    </Button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}
