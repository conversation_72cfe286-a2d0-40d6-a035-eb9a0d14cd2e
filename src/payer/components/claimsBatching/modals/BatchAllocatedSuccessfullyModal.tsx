import { claimsApi } from "../../../api/features/claimsApi";
import { useAppDispatch } from "../../../store/hooks";
import Button from "../../ui/Button";
import DialogWrapper from "../../ui/modal/DialogWrapper";
import SuccessAnimation from "../../ui/modal/SuccessAnimation";

export default function BatchAllocatedSuccessfullyModal({
  onClose,
  isSuccessModalOpen,
  batchId,
}: {
  onClose: () => void;
  isSuccessModalOpen: boolean;
  batchId: number;
}) {
  const dispatch = useAppDispatch();

  const handleClose = () => {
    dispatch(claimsApi.util.invalidateTags([{ type: "BatchedInvoice", id: batchId }]));
    dispatch(claimsApi.endpoints.getBatchById.initiate(batchId));
    onClose();
  };

  return (
    <>
      <DialogWrapper
        show={isSuccessModalOpen}
        onClose={onClose}
        maxWidth="max-w-[35rem]"
        className="py-8"
      >
        <div className="mt-2 flex flex-col space-y-4">
          <div className="self-center pb-4">
            <SuccessAnimation />
          </div>
          <h2 className="whitespace-nowrap text-center text-2xl font-semibold text-darkGray">
            Batch Allocated Successfully
          </h2>
          <div className="text-gray flex flex-col items-center justify-center self-center text-center text-sm">
            <p className="mx-8">The batch has been assigned to the selected users.</p>
            <p>Thank you!!!</p>
          </div>
          <div className="w-[15%] self-center">
            <Button className="w-full" onClick={handleClose}>
              Done
            </Button>
          </div>
        </div>
      </DialogWrapper>
    </>
  );
}
