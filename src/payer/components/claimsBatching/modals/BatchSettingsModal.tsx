import { XMarkIcon } from "@heroicons/react/24/outline";
import { useEffect, useState } from "react";
import { claimsApi, useGetBatchByIdQuery } from "../../../api/features/claimsApi";
import changeLog from "../../../assets/changelog.png";
import batchDetails from "../../../assets/folder_open.png";
import userDetails from "../../../assets/settings_user.png";
import { setActiveBatchSettingsItem } from "../../../features/claims/claimsBatchingSlice";
import { Batch } from "../../../lib/types/claims/batch";
import { useAppDispatch, useAppSelector } from "../../../store/hooks";
import DialogWrapper from "../../ui/modal/DialogWrapper";
import BatchChangeLog from "./batchSettings/BatchChangeLog";
import BatchDetails from "./batchSettings/BatchDetails";
import BatchSettingsItem, { SettingItem, SettingItems } from "./batchSettings/BatchSettingsItem";
import UserDetails from "./batchSettings/UserDetails";

const settingItems: Array<SettingItem> = [
  {
    title: SettingItems.BatchDetails,
    icon: batchDetails,
  },
  {
    title: SettingItems.UserDetails,
    icon: userDetails,
  },
  {
    title: SettingItems.ChangeLog,
    icon: changeLog,
  },
];

export default function BatchSettingsModal({
  onClose,
  isBatchSettingsModalOpen,
}: {
  onClose: () => void;
  isBatchSettingsModalOpen: boolean;
}) {
  const activeSettingsItem = useAppSelector(
    (state) => state.claimsBatching.activeBatchSettingsItem,
  );

  const selectedBatchId = useAppSelector((state) => state.claimsBatching.selectedBatchId);
  const [batch, setBatch] = useState<Batch>({} as Batch);

  const { data } = useGetBatchByIdQuery(selectedBatchId);

  useEffect(() => {
    if (data?.data) {
      setBatch(data?.data as Batch);
    }
  }, [data, selectedBatchId]);

  const dispatch = useAppDispatch();

  const handleSettingItemClick = (settingItem: SettingItems) => {
    dispatch(setActiveBatchSettingsItem(settingItem));
  };

  const handleClose = () => {
    dispatch(setActiveBatchSettingsItem(SettingItems.BatchDetails));
    dispatch(claimsApi.util.invalidateTags([{ type: "BatchedInvoice", id: selectedBatchId }]));
    onClose();
  };

  return (
    <>
      <DialogWrapper show={isBatchSettingsModalOpen} onClose={onClose} maxWidth="max-w-[50rem]">
        <section className="flex">
          <section className="flex basis-1/3 flex-col space-y-5 bg-[#F6F7F8] py-16">
            {settingItems.map((item) => (
              <BatchSettingsItem
                key={item.title}
                settingItem={item}
                activeSettingItem={activeSettingsItem}
                handleSettingItemClick={handleSettingItemClick}
              />
            ))}
          </section>
          <section className="flex basis-2/3 flex-col py-2 pl-4">
            <div className="self-end py-2 pr-4">
              <button onClick={handleClose}>
                <XMarkIcon className="h-4 w-4" />
              </button>
            </div>
            <h2 className="py-4 text-base font-medium text-darkBlue">Batch Settings</h2>
            <div className="pl-4">
              {activeSettingsItem === SettingItems.BatchDetails && <BatchDetails />}
              {activeSettingsItem === SettingItems.UserDetails && <UserDetails batch={batch} />}
              {activeSettingsItem === SettingItems.ChangeLog && <BatchChangeLog batch={batch} />}
            </div>
          </section>
        </section>
      </DialogWrapper>
    </>
  );
}
