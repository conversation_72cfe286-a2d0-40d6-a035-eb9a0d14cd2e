import { Dialog, Transition } from "@headlessui/react";
import { Fragment, SetStateAction } from "react";
import { toast } from "react-toastify";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { useDeleteBatchMutation } from "../../../api/features/claimsApi";
import warningIcon from "../../../assets/delete_batch.png";
import cancel from "../../../assets/material-symbols_close.png";

export default function DeleteBatchModal({
  onClose,
  isDeleteBatchModalOpen,
  id,
  setIsDeleteBatchModalOpen,
}: {
  onClose: () => void;
  isDeleteBatchModalOpen: boolean;
  id: string | number;
  setIsDeleteBatchModalOpen: React.Dispatch<SetStateAction<boolean>>;
}) {
  const [deleteBatch, { isLoading: batchDeleting }] = useDeleteBatchMutation();

  const handleDeleteBatch = async () => {
    try {
      await deleteBatch(id).unwrap();
      setIsDeleteBatchModalOpen(false);
      toast.success("Batch deleted successfully.");
    } catch (error) {
      console.error("Failed to delete batch", error);
      toast.error("Failed to delete batch. Please try again later.");
    }
    setIsDeleteBatchModalOpen(false);
  };

  return (
    <>
      <Transition appear show={isDeleteBatchModalOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={onClose}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/25" />
          </Transition.Child>
          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-xl transform overflow-hidden rounded-md bg-white px-8 py-4 text-left align-middle shadow-xl transition-all">
                  <section className="flex flex-col space-y-4">
                    <div className="self-end">
                      <button onClick={onClose}>
                        <img src={cancel} alt="close delete modal" className="w-4" />
                      </button>
                    </div>
                    <div className="flex items-center space-x-4">
                      <img src={warningIcon} alt="warning icon" className="w-8" />
                      <h2 className="whitespace-nowrap text-xl text-[#3F495F]">
                        Are you sure you want to delete this batch?
                      </h2>
                    </div>
                    <div className="pl-12">
                      <p className="text-sm text-customGray">
                        Deleting this batch will remove all associated data permanently. Are you
                        sure you want to proceed? Click 'No' to cancel and make changes or 'Yes' to
                        delete.
                      </p>
                    </div>
                    {batchDeleting && (
                      <div className="flex items-center space-x-4 self-center">
                        <LoadingIcon className="h-6 w-6 text-blue-400" />
                        <p className="text-blue-700">Deleting...</p>
                      </div>
                    )}
                    <div className="flex items-center space-x-4 self-end">
                      <button
                        className="rounded-[4px] bg-customRed   px-3 py-1 text-[10px] text-white"
                        onClick={onClose}
                      >
                        No
                      </button>
                      <button
                        className="px-2 py-1 text-[10px] text-[#667085]"
                        onClick={handleDeleteBatch}
                      >
                        Yes
                      </button>
                    </div>
                  </section>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}
