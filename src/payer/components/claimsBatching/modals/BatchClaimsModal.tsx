import { XMarkIcon } from "@heroicons/react/24/solid";
import { MouseEvent, useEffect } from "react";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { useBatchInvoicesMutation } from "../../../api/features/claimsApi";
import {
  setCatalogIds,
  setIsBatchClaimsModalOpen,
  setIsClaimsBatchedSuccessfullyModalOpen,
  setSelectedAgeBandId,
  setSelectedBenefitId,
  setSelectedProviderId,
  setSelectedRegion,
  setSelectedScheme,
} from "../../../features/claims/claimsBatchingSlice";
import { useHandleFilterSelectClear } from "../../../hooks/useHandleFilterSelectClear";
import useResetBatchingState from "../../../hooks/useResetBatchingState";
import { BatchedInvoices } from "../../../lib/types/claims/batchedInvoices";
import { MembershipScheme } from "../../../lib/types/membership/memberScheme";
import { formatValue } from "../../../lib/Utils";
import UserService from "../../../services/UserService";
import { useAppDispatch, useAppSelector } from "../../../store/hooks";
import DialogWrapper from "../../ui/modal/DialogWrapper";
import { BatchingCriteria } from "../BatchingCriteriaSelector";

export default function BatchClaimsModal() {
  const username = UserService.getUsername()?.toString();
  const payerId: number = UserService.getPayer()?.tokenParsed?.["payerId"];
  const dispatch = useAppDispatch();
  const resetBatchingState = useResetBatchingState();

  /* claims batching state */
  const selectedClaimsSum = useAppSelector((state) => state.claimsBatching.selectedClaimsSum);
  const checkedClaimsIds = useAppSelector((state) => state.claimsBatching.checkedClaimsIds);
  const selectedBatchingOption = useAppSelector(
    (state) => state.claimsBatching.selectedBatchingOption,
  );
  const isBatchClaimsModalOpen = useAppSelector(
    (state) => state.claimsBatching.isBatchClaimsModalOpen,
  );

  const invoiceCount = checkedClaimsIds.length;

  const batchedInvoices: BatchedInvoices = {
    invoiceIds: [...checkedClaimsIds],
    payerId,
    actionedBy: username,
    batchCriteria: selectedBatchingOption as string,
  };

  const [batchInvoices, { isLoading: isBatching, isSuccess }] = useBatchInvoicesMutation();

  const handleFilterSelectClearAging = useHandleFilterSelectClear(
    `filter-select-${BatchingCriteria.AGING}`,
  );
  const handleFilterSelectClearBenefit = useHandleFilterSelectClear(
    `filter-select-${BatchingCriteria.BENEFIT}`,
  );
  const handleFilterSelectClearProvider = useHandleFilterSelectClear(
    `filter-select-${BatchingCriteria.PROVIDER}`,
  );
  const handleFilterSelectClearRegion = useHandleFilterSelectClear(
    `filter-select-${BatchingCriteria.REGION}`,
  );
  const handleFilterSelectClearScheme = useHandleFilterSelectClear(
    `filter-select-${BatchingCriteria.SCHEME}`,
  );

  const clearFilterSelectValues = () => {
    handleFilterSelectClearAging();
    handleFilterSelectClearBenefit();
    handleFilterSelectClearProvider();
    handleFilterSelectClearRegion();
    handleFilterSelectClearScheme();
    dispatch(setSelectedAgeBandId(""));
    dispatch(setSelectedBenefitId(""));
    dispatch(setSelectedProviderId(""));
    dispatch(setSelectedRegion(""));
    dispatch(setCatalogIds([]));
    dispatch(setSelectedScheme({} as MembershipScheme));
  };

  const batchForVetting = async (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    try {
      await batchInvoices(batchedInvoices).unwrap();
      resetBatchingState();
      clearFilterSelectValues();
    } catch (error) {
      console.error("Failed to batch invoices", error);
    }
  };

  useEffect(() => {
    if (isSuccess) {
      dispatch(setIsBatchClaimsModalOpen(false));
      dispatch(setIsClaimsBatchedSuccessfullyModalOpen(true));
    }
  }, [isSuccess, dispatch]);

  const handleModalClose = () => {
    dispatch(setIsBatchClaimsModalOpen(false));
  };

  return (
    <DialogWrapper
      show={isBatchClaimsModalOpen}
      onClose={handleModalClose}
      maxWidth="max-w-[40rem]"
    >
      <section className="flex flex-col bg-white px-8 py-4">
        <button onClick={handleModalClose} className="self-end">
          <XMarkIcon className="h-4 w-4" />
        </button>
        <div className="flex flex-col gap-4 py-1 text-sm">
          <div className="flex items-center space-x-6">
            <p className="text-customGray">Invoice Count</p>:{" "}
            <p className="text-darkBlue">{invoiceCount}</p>
          </div>
          <div className="flex items-center space-x-6">
            <p className="text-customGray">Total Payable Amount</p>:{" "}
            <p className="text-darkBlue">{formatValue(selectedClaimsSum)}</p>
          </div>
        </div>
        <div
          className={`flex items-center space-x-8 pb-4 pt-10 text-xs ${
            isBatching ? "self-center" : "self-end"
          }`}
        >
          {isBatching ? (
            <section className="flex items-center space-x-2">
              <LoadingIcon className="mr-2" /> <p>Batching...</p>
            </section>
          ) : (
            <button
              className="w-fit rounded bg-btnBlue px-3 py-1.5 font-semibold text-white disabled:bg-[#E5E7EB] disabled:text-black"
              onClick={batchForVetting}
              disabled={isBatching}
            >
              Batch for vetting
            </button>
          )}
          <button
            className="w-fit self-end bg-[#F9FAFB] px-3 py-1.5 text-midGray"
            onClick={handleModalClose}
          >
            Cancel
          </button>
        </div>
      </section>
    </DialogWrapper>
  );
}
