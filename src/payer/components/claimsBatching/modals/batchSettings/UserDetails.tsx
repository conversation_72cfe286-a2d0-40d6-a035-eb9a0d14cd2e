import { ArrowPathIcon, CheckCircleIcon, PlusIcon } from "@heroicons/react/24/solid";
import { useState } from "react";
import multipleUsers from "../../../../assets/multiple_users.svg";
import singleUser from "../../../../assets/single_user.svg";
import {
  setReassignMultipleUsersReason,
  setReassignSingleUserReason,
} from "../../../../features/claims/claimsBatchingSlice";
import { Batch } from "../../../../lib/types/claims/batch";
import { useAppDispatch, useAppSelector } from "../../../../store/hooks";
import Button from "../../../ui/Button";
import FieldSet from "../../../ui/input/FieldSet";
import TextArea from "../../../ui/input/TextArea";
import ReassignMultipleUsersModal from "./ReassignMultipleUsersModal";
import ReassignSingleUserModal from "./ReassignSingleUserModal";

export enum UserType {
  SingleUser = "SINGLE_USER",
  MultipleUsers = "MULTIPLE_USERS",
}

export default function UserDetails({ batch }: { batch: Batch }) {
  const [userType, setUserType] = useState(batch.vettingAssignmentMode ?? "");
  const [isReassignSingleUserModalOpen, setIsReassignSingleUserModalOpen] = useState(false);
  const [isReassignMultipleUsersModalOpen, setIsReassignMultipleUsersModalOpen] = useState(false);

  const dispatch = useAppDispatch();

  const reassignSingleUserReason = useAppSelector(
    (state) => state.claimsBatching.reassignSingleUserReason,
  );

  const reassignMultipleUsersReason = useAppSelector(
    (state) => state.claimsBatching.reassignMultipleUsersReason,
  );

  const handleReasonChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (userType === UserType.SingleUser) {
      dispatch(setReassignSingleUserReason(e.target.value));
    } else {
      dispatch(setReassignMultipleUsersReason(e.target.value));
    }
  };

  const handleReassignSingleUserModalOpen = () => {
    setIsReassignSingleUserModalOpen(true);
  };

  const handleReassignSingleUserModalClose = () => {
    setIsReassignSingleUserModalOpen(false);
  };

  const handleReassignMultipleUsersModalOpen = () => {
    setIsReassignMultipleUsersModalOpen(true);
  };

  const handleReassignMultipleUsersModalClose = () => {
    setIsReassignMultipleUsersModalOpen(false);
  };

  const handleSingleUserClick = () => {
    setUserType("");
    setUserType(UserType.SingleUser);
  };

  const handleMultipleUsersClick = () => {
    setUserType("");
    setUserType(UserType.MultipleUsers);
  };

  return (
    <section className="flex h-[540px] flex-col space-y-4 pb-8 pr-8">
      {batch.batchStatus === "Completed" ? (
        <section className="flex flex-col space-y-4">
          <h2 className="text-base font-medium text-darkGray">{`Users (${batch.assignedTo.length})`}</h2>
          <ul className="flex flex-col space-y-2 text-sm">
            {batch.assignedTo.map((user, index) => (
              <li key={user.id} className="flex w-[60%] items-center justify-between space-x-2">
                <h2 className="text-[#1F2937]">
                  {`${index + 1}. ${
                    user.firstName && user.lastName
                      ? `${user.firstName} ${user.lastName}`
                      : user.userName
                  }`}
                </h2>
                <p className="text-lightGray">{`${user.totalVettedInvoices} invoices`}</p>
              </li>
            ))}
          </ul>
        </section>
      ) : (
        <section className="flex flex-col space-y-4 pb-8 pr-8">
          <div className="flex flex-col space-y-4">
            <h2 className="text-xs font-medium text-[#4B5563]">Change User Type</h2>
            <div className="grid grid-cols-2 gap-16">
              <button
                className={`flex h-[200px] flex-col space-y-4 rounded-md border px-4 py-2 shadow-md ${
                  userType === UserType.SingleUser
                    ? "bg-[#EFF6FF border-[#3B82F6] text-[#1E3A8A]"
                    : "border-[#D1D5DB] bg-white text-lightGray"
                }`}
                onClick={handleSingleUserClick}
              >
                {userType === UserType.SingleUser && (
                  <CheckCircleIcon className="h-5 w-5 self-end text-[#3B82F6]" />
                )}
                <img src={singleUser} alt="single user" className="h-36" />
                <p className="self-center text-center text-xs">Single User</p>
              </button>
              <button
                className={`flex h-[200px] flex-col space-y-4 rounded-md border px-4 py-2 shadow-md ${
                  userType === UserType.MultipleUsers
                    ? "bg-[#EFF6FF border-[#3B82F6] text-[#1E3A8A]"
                    : "border-[#D1D5DB] bg-white text-lightGray"
                }`}
                onClick={handleMultipleUsersClick}
              >
                {userType === UserType.MultipleUsers && (
                  <CheckCircleIcon className="h-5 w-5 self-end text-[#3B82F6]" />
                )}

                <img src={multipleUsers} alt="single user" className="h-36" />
                <p className="self-center text-center text-xs">Multiple users</p>
              </button>
            </div>
          </div>
          <div className="flex flex-col space-y-1 pt-8">
            <h2 className="text-xs font-medium text-[#4B5563]">Number of users</h2>
            <p className="text-xs font-medium text-[#111827]">{`${batch.assignedTo.length} ${
              batch.assignedTo.length > 1 ? "Users" : "User"
            }`}</p>
          </div>
          <section className="py-4">
            <FieldSet
              title={
                <div className="flex items-center space-x-1">
                  <p>Reason</p>
                  <p className="text-sm text-customRed">*</p>
                </div>
              }
            >
              <TextArea
                value={
                  userType === UserType.SingleUser
                    ? reassignSingleUserReason
                    : reassignMultipleUsersReason
                }
                onChange={handleReasonChange}
                placeholder={`${
                  userType === UserType.MultipleUsers
                    ? "Add a reason for adding more users."
                    : "Add a reason for changing user."
                }`}
              />
            </FieldSet>
          </section>
          <div className="flex items-center space-x-2 self-end">
            {userType === UserType.MultipleUsers && (
              <Button
                onClick={handleReassignMultipleUsersModalOpen}
                className={`self-end`}
                disabled={reassignMultipleUsersReason === ""}
              >
                <PlusIcon className="h-3 w-3 font-bold" />
                <p>Add Users</p>
              </Button>
            )}
            {userType === UserType.SingleUser && (
              <Button
                className="self-end"
                onClick={handleReassignSingleUserModalOpen}
                disabled={reassignSingleUserReason === ""}
              >
                <ArrowPathIcon className="h-3 w-3" />
                <p>Change User</p>
              </Button>
            )}
          </div>
          <ReassignSingleUserModal
            reason={reassignSingleUserReason}
            batch={batch}
            onClose={handleReassignSingleUserModalClose}
            isReassignSingleUserModalOpen={isReassignSingleUserModalOpen}
          />
          <ReassignMultipleUsersModal
            reason={reassignMultipleUsersReason}
            batch={batch}
            onClose={handleReassignMultipleUsersModalClose}
            isReassignMultipleUsersModalOpen={isReassignMultipleUsersModalOpen}
          />
        </section>
      )}
    </section>
  );
}
