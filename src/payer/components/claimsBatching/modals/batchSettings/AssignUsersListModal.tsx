import { XMarkIcon } from "@heroicons/react/24/outline";
import { MouseEvent, useEffect, useState } from "react";
import { toast } from "react-toastify";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import {
  useAssignBatchToPayersMutation,
  useGetClaimsUsersQuery,
} from "../../../../api/features/claimsApi";
import blueCheckbox from "../../../../assets/blue_checkbox.png";
import {
  setAssignedBatchId,
  setIsUsersAssignedSuccessfullyModalOpen,
} from "../../../../features/claims/claimsBatchingSlice";
import { ClaimsUser } from "../../../../lib/types/claims/claimsUser";
import { AssignBatchToPayerPayload } from "../../../../lib/types/claims/invoice";
import UserService from "../../../../services/UserService";
import { useAppDispatch } from "../../../../store/hooks";
import DialogWrapper from "../../../ui/modal/DialogWrapper";
import SecondaryPagination from "../../../ui/pagination/SecondaryPagination";

export default function AssignUsersListModal({
  batchId,
  onClose,
  isUsersListModalOpen,
  refetchBatchedClaims,
  setPageNumber,
}: {
  onClose: () => void;
  batchId: string | number;
  isUsersListModalOpen: boolean;
  refetchBatchedClaims: () => void;
  setPageNumber: (pageNumber: number) => void;
}) {
  const [page, setPage] = useState(1);
  const [users, setUsers] = useState<ClaimsUser[]>([]);
  const [usersChecked, setUsersChecked] = useState<{ [key: string]: boolean }>({});
  const [selectedUsersUsernames, setSelectedUsersUsernames] = useState<string[]>([]);

  const dispatch = useAppDispatch();

  const payerId: string = UserService.getPayer()?.tokenParsed?.["payerId"];

  const { data, isLoading, error } = useGetClaimsUsersQuery({ payerId, page });

  const totalElements = data?.data?.totalElements as number;
  const totalPages = data?.data?.totalPages as number;

  const handleUserClick = (user: ClaimsUser) => {
    setUsersChecked({ ...usersChecked, [user.id]: !usersChecked[user.id] });
    setSelectedUsersUsernames([...selectedUsersUsernames, user.userName]);
  };

  const assignBatchPayload: AssignBatchToPayerPayload = {
    assignedBy: UserService.getUsername()?.toString(),
    assignedTo: [...selectedUsersUsernames],
    batchId: batchId,
  };

  const [assignBatch, { isLoading: isAssigningBatch, isSuccess: isBatchAssignedSuccessfully }] =
    useAssignBatchToPayersMutation();

  const handleAssignBatch = async (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    try {
      await assignBatch(assignBatchPayload).unwrap();
      setUsersChecked({});
      setSelectedUsersUsernames([]);
      setPageNumber(1);
      refetchBatchedClaims();
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      console.error("Failed to batch invoices", error.data.error);
      toast.error(`Failed to assign batch: ${error.data.error}`);
    }
  };

  const handleCancel = () => {
    onClose();
    setSelectedUsersUsernames([]);
    setUsersChecked({});
  };

  useEffect(() => {
    if (data?.data?.content) {
      setUsers(data?.data?.content as ClaimsUser[]);
    }
  }, [data]);

  useEffect(() => {
    if (isBatchAssignedSuccessfully) {
      dispatch(setAssignedBatchId(Number(batchId)));
      dispatch(setIsUsersAssignedSuccessfullyModalOpen(true));
    }
  }, [isBatchAssignedSuccessfully, dispatch, batchId]);

  return (
    <>
      <DialogWrapper
        show={isUsersListModalOpen}
        onClose={onClose}
        maxWidth="max-w-[50rem]"
        className="pb-10"
      >
        <section className="flex h-[600px] flex-col space-y-1 overflow-y-auto p-4">
          <div className="self-end">
            <button onClick={handleCancel}>
              <XMarkIcon className="h-4 w-4" />
            </button>
          </div>
          <section className="flex flex-col space-y-1 px-4">
            <h2 className="text-lg font-medium text-darkBlue">Users</h2>
            <div className="flex items-center justify-between pb-2.5 text-xs font-medium text-lightGray">
              <p>Please select the user(s) to assign this batch for vetting</p>
              <SecondaryPagination
                totalElements={totalElements}
                totalPages={totalPages}
                currentPage={page}
                setCurrentPage={setPage}
                size={20}
              />
            </div>
            {isLoading ? (
              <div className="flex items-center justify-center space-x-2 py-8">
                <LoadingIcon className="h-6 w-6 text-blue-400" />
                <p className="text-xs text-blue-700">Loading users...</p>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center py-8">
                <p className="text-red-700">Error loading users. Try again later.</p>
              </div>
            ) : isAssigningBatch ? (
              <section className="flex items-center justify-center space-x-2 py-44">
                <LoadingIcon className="mr-2" /> <p>Allocating...</p>
              </section>
            ) : (
              <section className="flex flex-col space-y-4 overflow-y-auto">
                {users.map((user) => (
                  <section
                    className="flex items-center justify-between rounded-md border border-[#E5E7EB] px-4 py-2"
                    key={user.id}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-[#E5E7EB] text-sm text-btnBlue">
                        {user.firstName && user.lastName
                          ? `${user.firstName.charAt(0).toUpperCase()}${user.lastName
                              .charAt(0)
                              .toUpperCase()}`
                          : `${user.userName.charAt(0).toUpperCase()}P`}
                      </div>
                      <div className="flex flex-col">
                        <h3 className="text-sm font-medium text-midGray">
                          {user.firstName && user.lastName
                            ? `${user.firstName} ${user.lastName}`
                            : user.userName}
                        </h3>
                        <p className="text-xs text-lightGray">
                          {user.totalUnVettedInvoices} pending invoices
                        </p>
                      </div>
                    </div>
                    <div onClick={() => handleUserClick(user)}>
                      {usersChecked[user.id] ? (
                        <img src={blueCheckbox} alt="blue checkbox" className="h-4 w-4" />
                      ) : (
                        <div className="cursor-pointer rounded-full border border-[#E5E7EB] p-2"></div>
                      )}
                    </div>
                  </section>
                ))}
              </section>
            )}
          </section>
          <div className="flex items-center justify-center space-x-4 pt-4">
            <button
              className="rounded-md  border border-[#D1D5DB] px-6 py-1.5 text-xs text-midGray"
              onClick={handleCancel}
            >
              <p>Cancel</p>
            </button>
            <button
              className="rounded-md bg-btnBlue px-6 py-1.5 text-xs text-white"
              onClick={handleAssignBatch}
            >
              <p>Allocate</p>
            </button>
          </div>
        </section>
      </DialogWrapper>
    </>
  );
}
