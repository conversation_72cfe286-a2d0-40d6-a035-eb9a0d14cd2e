interface Input {
  id?: string | number;
  name?: string;
  disabled: boolean;
  value: string | number | null;
  label: string;
}
export default function BatchDetailsInput({ input }: { input: Input }) {
  const { name, disabled, value, label } = input;
  return (
    <div className="flex flex-col space-y-1">
      <label className="text-xs font-medium text-[#4B5563]">{label}</label>
      <input
        name={name}
        value={!value ? "None" : value}
        type="text"
        disabled={disabled}
        className="w-[80%] rounded-md border border-[#D1D5DB] px-2 py-[2px] text-[11px] text-[#111827] placeholder:text-lightGray"
      />
    </div>
  );
}
