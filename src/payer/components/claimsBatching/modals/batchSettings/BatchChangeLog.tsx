import { useEffect, useState } from "react";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { useGetBachAuditLogsQuery } from "../../../../api/features/claimsApi";
import expandIcon from "../../../../assets/expand_icon.png";
import { AuditLog } from "../../../../lib/types/claims/auditLogs";
import { Batch } from "../../../../lib/types/claims/batch";
import { formatDate, formatLogDate } from "../../../../lib/Utils";
import { formatLogDateToMonthYear } from "../../../../utils/utils";

export default function BatchChangeLog({ batch }: { batch: Batch }) {
  const [batchAuditLogs, setBatchAuditLogs] = useState<AuditLog[]>([]);
  const { data, isLoading } = useGetBachAuditLogsQuery(batch.id);
  const [expandedLogPeriods, setExpandedLogsPeriods] = useState<string[]>([]);

  const handleLogExpand = (logPeriod: string) => {
    if (expandedLogPeriods.includes(logPeriod)) {
      setExpandedLogsPeriods(expandedLogPeriods.filter((period) => period !== logPeriod));
    } else {
      setExpandedLogsPeriods([...expandedLogPeriods, logPeriod]);
    }
  };

  function groupLogsByMonth(logs: AuditLog[]) {
    return logs.reduce<{ [monthYear: string]: AuditLog[] }>((acc, log) => {
      const logDate = formatDate(log.createdOn);
      const formattedDate = formatLogDateToMonthYear(logDate);
      if (!acc[formattedDate]) {
        acc[formattedDate] = [];
      }
      acc[formattedDate].push(log);
      return acc;
    }, {});
  }

  useEffect(() => {
    if (data?.data?.content) {
      setBatchAuditLogs(data?.data.content as AuditLog[]);
    }
  }, [data?.data.content]);

  const groupedLogs = groupLogsByMonth(batchAuditLogs);

  return (
    <section className="h-[540px] overflow-y-auto pb-8">
      <div className="flex flex-col space-y-4">
        {isLoading ? (
          <div className="flex items-center justify-center space-x-2 py-8">
            <LoadingIcon className="h-6 w-6 text-blue-700" />
            <p className="text-xs text-blue-700">Loading Batch Audit Logs...</p>
          </div>
        ) : batchAuditLogs.length > 0 ? (
          <section className="flex flex-col space-y-5">
            {Object.entries(groupedLogs).map(([monthYear, logs]) => (
              <div
                key={monthYear}
                className={`mr-8 flex flex-col space-y-4 p-4 ${
                  !expandedLogPeriods.includes(monthYear) &&
                  "rounded-md border border-[#AFAFAF4D] shadow-md"
                }`}
              >
                <div className="flex items-center justify-between space-x-2">
                  <h3 className="text-[14px] font-medium text-darkBlue">{monthYear}</h3>
                  <button onClick={() => handleLogExpand(monthYear)}>
                    <img src={expandIcon} alt="expand" className="h-4 w-4" />
                  </button>
                </div>
                {expandedLogPeriods.includes(monthYear) && (
                  <section className="flex flex-col space-y-4">
                    {logs.map((log) => (
                      <div className="flex items-start space-x-8" key={log.id}>
                        <div className="flex items-center space-x-2">
                          <div className="rounded-full border border-[#F7941D] p-1"></div>
                          <p className="text-xs font-medium text-darkGray">
                            {formatLogDate(log.createdOn)}
                          </p>
                        </div>
                        <div className="flex flex-col space-y-1">
                          <p className="text-gray text-sm font-medium">{log.eventName}</p>
                          <p className="text-xs text-[#9CA3AF]">{log.actionByUser}</p>
                        </div>
                      </div>
                    ))}
                  </section>
                )}
              </div>
            ))}
          </section>
        ) : (
          <p className="text-xs text-darkGray">No Logs Found for this batch</p>
        )}
      </div>
    </section>
  );
}
