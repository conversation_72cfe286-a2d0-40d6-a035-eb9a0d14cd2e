import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { Batch } from "../../../../lib/types/claims/batch";
import { formatDate, formatValue } from "../../../../lib/Utils";
import BatchDetailsInput from "./BatchDetailsInput";
import { useAppSelector } from "../../../../store/hooks";
import { useEffect, useState } from "react";
import { useGetBatchByIdQuery } from "../../../../api/features/claimsApi";
import { getDifferenceInDays } from "../../../../utils/utils";

export default function BatchDetails() {
  const selectedBatchId = useAppSelector((state) => state.claimsBatching.selectedBatchId);
  const [batch, setBatch] = useState<Batch>({} as Batch);
  const [batchAgeString, setBatchAgeString] = useState<string>("");

  const { data, isLoading } = useGetBatchByIdQuery(selectedBatchId);

  function formatToDateString(dateString: string): string {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear();

    return `${day}.${month}.${year}`;
  }

  useEffect(() => {
    if (data?.data) {
      setBatch(data?.data as Batch);

      if (data.data.createdOn) {
        const batchAge = getDifferenceInDays(
          formatToDateString(data.data.createdOn),
          formatDate(new Date(Date.now())),
        );

        const batchAgeString = `${batchAge} ${batchAge === 1 ? "Day" : "Days"}`;
        setBatchAgeString(batchAgeString);
      }
    }
  }, [data?.data]);

  return (
    <section className="flex h-[540px] flex-col space-y-4 pb-8">
      {isLoading ? (
        <div className="flex items-center justify-center space-x-2 py-8">
          <LoadingIcon className="h-6 w-6 text-blue-400" />
          <p className="text-blue-700">Loading Batch Details...</p>
        </div>
      ) : (
        <form action="" className="flex flex-col space-y-4">
          <BatchDetailsInput
            input={{
              disabled: true,
              value: batch.id,
              label: "Batch Number",
            }}
          />
          <BatchDetailsInput
            input={{
              disabled: true,
              value: batch.allocationStatus,
              label: "Allocation Status",
            }}
          />
          <BatchDetailsInput
            input={{
              disabled: true,
              value: batch.batchStatus,
              label: "Batch Status",
            }}
          />
          <BatchDetailsInput
            input={{
              disabled: true,
              value: formatDate(batch.createdOn).replace(/\./g, "-"),
              label: "Date Created",
            }}
          />
          <BatchDetailsInput
            input={{
              disabled: true,
              value: batchAgeString,
              label: "Batch Age",
            }}
          />
          <BatchDetailsInput
            input={{
              disabled: true,
              value: batch.actionedBy,
              label: "Created By",
            }}
          />
          <BatchDetailsInput
            input={{
              disabled: true,
              value: `${batch.invoiceCount} ${batch.invoiceCount === 1 ? "invoice" : "invoices"}`,
              label: "Invoice Count",
            }}
          />
          <BatchDetailsInput
            input={{
              disabled: true,
              value: formatValue(batch.totalAmount),
              label: "Total Amount",
            }}
          />
        </form>
      )}
    </section>
  );
}
