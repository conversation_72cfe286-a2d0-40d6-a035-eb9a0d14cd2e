export enum SettingItems {
  BatchDetails = "Batch Details",
  UserDetails = "User Details",
  ChangeLog = "Change Log",
}

export interface SettingItem {
  title: SettingItems;
  icon: string;
}

const BatchSettingsItem = ({
  settingItem,
  activeSettingItem,
  handleSettingItemClick,
}: {
  settingItem: SettingItem;
  activeSettingItem: SettingItems;
  handleSettingItemClick: (settingItem: SettingItems) => void;
}) => {
  const { title, icon } = settingItem;
  return (
    <div
      className={`flex cursor-pointer text-sm ${
        activeSettingItem === title ? "bg-white text-midGray" : "bg-inherit text-lightGray"
      }`}
      onClick={() => handleSettingItemClick(title)}
    >
      <div
        className={`inline-block h-12 w-1 rounded-r-lg ${
          activeSettingItem === title ? "bg-[#3B82F6]" : "bg-white"
        } `}
      ></div>
      <div className="mx-4 flex items-center space-x-4 py-2">
        <img src={icon} alt={title} className="w-5" />
        <h2>{title}</h2>
      </div>
    </div>
  );
};

export default BatchSettingsItem;
