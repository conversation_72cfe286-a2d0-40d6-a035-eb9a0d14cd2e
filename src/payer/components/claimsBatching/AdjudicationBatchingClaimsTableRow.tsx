import { ChangeEvent } from "react";
import { InvoiceData } from "../../lib/types/claims/invoice";
import { formatDate, formatValue } from "../../lib/Utils";
import { getDifferenceInDays } from "../../utils/utils";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import {
  setCheckedClaimsIds,
  setIsSelectAllActive,
  setSelectedClaimsSum,
} from "../../features/claims/claimsBatchingSlice";
import TableDataItem from "../ui/table/TableDataItem";

export default function AdjudicationBatchingClaimsTableRow({ invoice }: { invoice: InvoiceData }) {
  const dispatch = useAppDispatch();
  const checkedClaimsIds = useAppSelector((state) => state.claimsBatching.checkedClaimsIds);
  const selectedClaimsSum = useAppSelector((state) => state.claimsBatching.selectedClaimsSum);

  const handleClick = (e: ChangeEvent<HTMLInputElement>, invoice: InvoiceData) => {
    const { checked } = e.target;
    if (checked) {
      dispatch(setCheckedClaimsIds([...checkedClaimsIds, invoice.id as string]));
      dispatch(setSelectedClaimsSum(selectedClaimsSum + (invoice.totalAmount ?? 0)));
    } else {
      dispatch(setCheckedClaimsIds(checkedClaimsIds.filter((item) => item !== invoice.id)));
      dispatch(setSelectedClaimsSum(selectedClaimsSum - (invoice.totalAmount ?? 0)));
      dispatch(setIsSelectAllActive(false));
    }
  };

  const daysDifference = getDifferenceInDays(
    formatDate(invoice.invoiceDate),
    formatDate(new Date(Date.now())),
  );

  return (
    <tr key={invoice.id} className="">
      <td className="whitespace-nowrap border-b border-[#EAECF0] px-4 py-2 uppercase">
        <input
          className="form-checkbox h-5 w-5"
          type="checkbox"
          checked={checkedClaimsIds.includes(invoice.id as string)}
          onChange={(e) => handleClick(e, invoice)}
        />
      </td>
      <TableDataItem className="text-xs" item={invoice?.memberNumber} />
      <TableDataItem className="text-xs" item={invoice?.memberName} />
      <TableDataItem className="text-xs" item={invoice?.schemeName} />
      <TableDataItem className="text-xs" item={invoice?.providerName} />
      <TableDataItem className="text-xs" item={invoice?.invoiceNumber} />
      <TableDataItem className="text-xs" item={formatValue(invoice?.totalAmount)} />
      <TableDataItem className="text-xs" item={formatDate(invoice?.invoiceDate)} />
      <TableDataItem
        className="text-sm"
        item={`${daysDifference < 0 ? "" : daysDifference}${" "}
        ${daysDifference < 0 ? "Not Due" : daysDifference === 1 ? "Day" : "Days"}
        `}
      />
    </tr>
  );
}
