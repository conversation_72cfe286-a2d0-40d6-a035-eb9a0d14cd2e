import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { vi } from "vitest";
import Datepicker from "./Datepicker";

vi.mock("react-flatpickr", () => {
  return {
    __esModule: true,
    default: ({ className, options, ...props }) => (
      <input data-testid="flatpickr-input" className={className} {...props} />
    ),
  };
});

describe("Datepicker Component", () => {
  it("renders without crashing", () => {
    render(<Datepicker />);
    const input = screen.getByTestId("flatpickr-input");
    expect(input).toBeInTheDocument();
  });

  it("renders the calendar icon", () => {
    render(<Datepicker />);
    const icon = screen.getByRole("img");
    expect(icon).toBeInTheDocument();
  });

  it("should allow user to focus on input", async () => {
    render(<Datepicker />);
    const input = screen.getByTestId("flatpickr-input");
    await userEvent.click(input);
    expect(document.activeElement).toBe(input);
  });
});
