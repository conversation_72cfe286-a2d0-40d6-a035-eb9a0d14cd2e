import React, { useEffect, useState } from "react";

// TODO: Add ellipsis on multiple pages
function PaginationControlled({
  totalElements,
  totalPages,
  pageNumber,
  OnPageNumberClick: handlePage,
}) {
  const [list, setList] = useState([]);

  useEffect(() => {
    setList(Array.from(Array(totalPages).keys()));
  }, [totalPages, pageNumber]);

  return (
    <div className="flex justify-center">
      <nav className="flex items-center" role="navigation" aria-label="Navigation">
        <div className="mr-2">Total {totalElements}</div>

        <div className="mr-2">
          {pageNumber === 1 ? (
            ""
          ) : (
            <button
              onClick={(e) => {
                e.stopPropagation();
                handlePage(pageNumber - 1);
              }}
              className="inline-flex items-center justify-center rounded border border-gray-200 bg-white px-2.5 py-2 font-semibold leading-5 text-gray-600 shadow-sm hover:bg-blue-800 hover:text-white"
            >
              <span className="sr-only">Previous</span>

              <svg className="h-4 w-4 fill-current" viewBox="0 0 16 16">
                <path d="M9.4 13.4l1.4-1.4-4-4 4-4-1.4-1.4L4 8z" />
              </svg>
            </button>
          )}
        </div>

        <ul
          className={`inline-flex -space-x-px text-sm font-medium shadow-sm ${
            totalPages > 20 ? "hidden" : "block"
          }`}
        >
          {list.map((item, index) => (
            <li key={index} className="mx-2">
              {pageNumber === Number(item + 1) ? (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handlePage(item + 1);
                  }}
                  className="inline-flex items-center justify-center rounded border border-gray-200 bg-white px-3.5 py-2 font-semibold leading-5 text-gray-600  hover:text-blue-800"
                >
                  {item + 1}
                </button>
              ) : (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handlePage(item + 1);
                  }}
                  className="inline-flex items-center justify-center rounded border border-gray-200 bg-white px-3.5 py-2 font-semibold leading-5 text-gray-600 hover:text-blue-800 "
                >
                  {item + 1}
                </button>
              )}
            </li>
          ))}
        </ul>
        <div className="ml-2">
          {totalPages === pageNumber ? (
            ""
          ) : (
            <button
              onClick={(e) => {
                e.stopPropagation();
                handlePage(pageNumber + 1);
              }}
              className="inline-flex items-center justify-center rounded border border-gray-200 bg-white px-2.5 py-2 font-semibold leading-5 text-gray-600 shadow-sm hover:bg-blue-800 hover:text-white"
            >
              <span className="sr-only">Next</span>
              <svg className="h-4 w-4 fill-current" viewBox="0 0 16 16">
                <path d="M6.6 13.4L5.2 12l4-4-4-4 1.4-1.4L12 8z" />
              </svg>
            </button>
          )}
        </div>
        <div className="ml-2">
          <p className="">
            <wbr />
            Page No. {pageNumber}
          </p>
        </div>
      </nav>
    </div>
  );
}

export default PaginationControlled;
