import { render, screen, fireEvent } from "@testing-library/react";
import { vi } from "vitest";
import DropdownEditMenu from "./DropdownEditMenu";

// Mock Transition component (if necessary, depending on how it's implemented)
vi.mock("../lib/Transition", () => ({
  default: ({ children }) => children,
}));

describe("DropdownEditMenu", () => {
  test("renders without crashing", () => {
    render(
      <DropdownEditMenu>
        <li>Option 1</li>
        <li>Option 2</li>
      </DropdownEditMenu>,
    );
    expect(screen.getByRole("button")).toBeInTheDocument();
  });

  test("opens dropdown on button click", () => {
    render(
      <DropdownEditMenu>
        <li>Option 1</li>
      </DropdownEditMenu>,
    );

    const button = screen.getByRole("button");
    fireEvent.click(button);
    expect(screen.getByText("Option 1")).toBeVisible();
  });

  //     render(
  //       <DropdownEditMenu>
  //         <li>Option 1</li>
  //       </DropdownEditMenu>
  //     );

  //     fireEvent.click(screen.getByRole("button"));
  //     expect(screen.getByText("Option 1")).toBeVisible();

  //     fireEvent.focus(screen.getByText("Option 1"));
  //     expect(screen.getByText("Option 1")).toBeVisible();

  //     fireEvent.blur(screen.getByText("Option 1"));
  //     expect(screen.queryByText("Option 1")).not.toBeInTheDocument();
  //   });
});
