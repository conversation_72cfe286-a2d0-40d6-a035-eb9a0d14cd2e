import { useState } from "react";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import Text from "../../components/ui/typography/Text";
import Button from "../../components/ui/Button";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { UserBasicDto } from "../../lib/types/access-control/role";

interface RemoveCustomRoleUsersModalProps {
  show: boolean;
  onClose: () => void;
  users: UserBasicDto[];
  roleName: string;
  onRemove: () => Promise<void>;
  isLoading?: boolean;
}

export default function RemoveCustomRoleUsersModal({
  show,
  onClose,
  users,
  roleName,
  onRemove,
  isLoading,
}: RemoveCustomRoleUsersModalProps) {
  const userCount = users.length;
  const [localIsRemoving, setLocalIsRemoving] = useState(false);

  // Use parent loading state if provided, otherwise use local state
  const isRemoving = isLoading !== undefined ? isLoading : localIsRemoving;

  const handleRemove = async () => {
    // Only manage local state if parent loading state is not provided
    if (isLoading === undefined) {
      try {
        setLocalIsRemoving(true);
        await onRemove();
        setLocalIsRemoving(false);
      } catch (error) {
        console.error("Error removing users:", error);
        setLocalIsRemoving(false);
      }
    } else {
      // If parent is managing loading state, just call onRemove
      await onRemove();
    }
  };

  return (
    <DialogWrapper show={show} onClose={onClose} maxWidth="max-w-3xl">
      <div className="p-6">
        <div className="mb-6">
          <Text variant="paragraph">
            Are you sure you want to remove {userCount} {userCount === 1 ? "user" : "users"} from
            the role <strong>{roleName}</strong>?
          </Text>
          <Text variant="description" className="mt-2">
            These users will lose access to any permissions granted through this role.
          </Text>

          {userCount > 0 && (
            <div className="mt-4 max-h-60 overflow-y-auto rounded border border-gray-200">
              <table className="w-full table-auto">
                <thead className="bg-gray-50 text-left">
                  <tr>
                    <th className="px-4 py-2">
                      <Text variant="paragraph" className="font-medium">
                        Name
                      </Text>
                    </th>
                    <th className="px-4 py-2">
                      <Text variant="paragraph" className="font-medium">
                        Email
                      </Text>
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {users.map((user) => (
                    <tr key={user.userId}>
                      <td className="px-4 py-2">
                        <Text variant="paragraph">{user.name || user.userName}</Text>
                      </td>
                      <td className="px-4 py-2">
                        <Text variant="description" className="text-gray-600">
                          {user.userEmail || "-"}
                        </Text>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outlined" onClick={onClose} disabled={isRemoving}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleRemove}
            disabled={isRemoving}
            className="flex items-center gap-2"
          >
            {isRemoving && <LoadingIcon className="h-4 w-4 text-white" />}
            Remove {userCount} {userCount === 1 ? "User" : "Users"}
          </Button>
        </div>
      </div>
    </DialogWrapper>
  );
}
