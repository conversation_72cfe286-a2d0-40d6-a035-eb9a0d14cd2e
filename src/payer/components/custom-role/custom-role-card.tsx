import { useNavigate } from "react-router-dom";
import { CustomRoleResponse } from "../../lib/types/access-control/role";
import { format, parseISO } from "date-fns";
import Button from "../ui/Button";
import { EyeIcon } from "@heroicons/react/24/outline";
import CardWrapper from "../ui/CardWrapper";

interface CustomRoleSummaryItemProps {
  label: string;
  value: string | number | JSX.Element;
}

const CustomRoleSummaryItem = ({ label, value }: CustomRoleSummaryItemProps) => {
  return (
    <div className="flex space-x-2 text-sm">
      <p className="whitespace-nowrap text-gray-500">{label}:</p>
      <div className="text-gray-700">{value}</div>
    </div>
  );
};

interface CustomRoleCardProps {
  role: CustomRoleResponse;
}

export default function CustomRoleCard({ role }: CustomRoleCardProps) {
  const navigate = useNavigate();

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    try {
      return format(parseISO(dateString), "MMM dd, yyyy");
    } catch (e) {
      return dateString;
    }
  };

  const handleViewRole = () => {
    navigate(`/users/roles/custom/${role.id}`);
  };

  return (
    <CardWrapper className="flex h-full w-full flex-col gap-6">
      <div className="flex flex-grow flex-col space-y-2">
        <h2 className="text-lg font-medium text-gray-900">{role.name}</h2>
        <CustomRoleSummaryItem
          label="Description"
          value={role.description || "No description provided"}
        />
        <CustomRoleSummaryItem label="Created By" value={role.createdBy} />
        <CustomRoleSummaryItem label="Created At" value={formatDate(role.createdAt)} />
        {role.updatedAt && (
          <CustomRoleSummaryItem label="Updated At" value={formatDate(role.updatedAt)} />
        )}
      </div>
      <div className="flex flex-col justify-between self-end">
        <Button variant="outlined" className="flex items-center gap-2" onClick={handleViewRole}>
          <EyeIcon className="h-4 w-4" />
          View Details
        </Button>
      </div>
    </CardWrapper>
  );
}
