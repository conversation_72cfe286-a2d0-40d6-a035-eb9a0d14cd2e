import { useState } from "react";
import { CustomRoleResponse, PermissionResponse } from "../../lib/types/access-control/role";
import Text from "../ui/typography/Text";
import Button from "../ui/Button";
import { PlusIcon } from "@heroicons/react/24/solid";
import RemoveCustomRolePermissionConfirmModal from "./remove-custom-role-permission-confirm-modal";
import AssignedPermission from "../ui/assigned-permission";

interface CustomRolePermissionsProps {
  role: CustomRoleResponse;
  onAddPermissions: () => void;
  onRemovePermission: (permission: string) => Promise<void>;
}

export default function CustomRolePermissions({
  role,
  onAddPermissions,
  onRemovePermission,
}: CustomRolePermissionsProps) {
  const [showRemoveConfirm, setShowRemoveConfirm] = useState(false);
  const [permissionToRemove, setPermissionToRemove] = useState<string | null>(null);

  const handleRemoveClick = (permission: string) => {
    setPermissionToRemove(permission);
    setShowRemoveConfirm(true);
  };

  const confirmRemovePermission = async () => {
    if (permissionToRemove) {
      await onRemovePermission(permissionToRemove);
      setPermissionToRemove(null);
      setShowRemoveConfirm(false);
    }
  };

  const handleCloseRemoveModal = () => {
    setShowRemoveConfirm(false);
    setPermissionToRemove(null);
  };

  return (
    <div className="rounded-md border border-gray-200 bg-white p-6">
      <div className="mb-4 flex items-center justify-between">
        <Text variant="subheading">Role Permissions</Text>
        {!role.isPredefined && (
          <Button
            variant="outlined"
            className="flex items-center gap-1 text-xs"
            onClick={onAddPermissions}
          >
            <PlusIcon className="h-4 w-4" /> Add Permissions
          </Button>
        )}
      </div>

      {role.permissions.length > 0 ? (
        <div className="flex flex-wrap gap-2">
          {role.permissions.map((permission: PermissionResponse) => (
            <AssignedPermission
              key={permission.name}
              permission={permission}
              onRemove={handleRemoveClick}
            />
          ))}
        </div>
      ) : (
        <p className="text-sm text-gray-500">No permissions assigned to this role.</p>
      )}

      {permissionToRemove && (
        <RemoveCustomRolePermissionConfirmModal
          show={showRemoveConfirm}
          onClose={handleCloseRemoveModal}
          permission={permissionToRemove}
          roleName={role.name}
          onRemove={confirmRemovePermission}
        />
      )}
    </div>
  );
}
