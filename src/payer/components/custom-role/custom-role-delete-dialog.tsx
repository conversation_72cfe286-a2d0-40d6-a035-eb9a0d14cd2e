import { Dialog } from "@headlessui/react";
import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";
import Button from "../ui/Button";
import DialogWrapper from "../ui/modal/DialogWrapper";
import LoadingIcon from "~lib/components/icons/LoadingIcon";

interface CustomRoleDeleteDialogProps {
  show: boolean;
  onClose: () => void;
  onDelete: () => void;
  roleName: string;
  isDeleting: boolean;
}

export default function CustomRoleDeleteDialog({
  show,
  onClose,
  onDelete,
  roleName,
  isDeleting,
}: CustomRoleDeleteDialogProps) {
  return (
    <DialogWrapper show={show} onClose={onClose} maxWidth="max-w-xl">
      <div className="p-6">
        <div className="flex items-center justify-center">
          <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100">
            <ExclamationTriangleIcon className="h-6 w-6 text-red-600" aria-hidden="true" />
          </div>
        </div>
        <div className="mt-3 text-center sm:mt-5">
          <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
            Delete Custom Role
          </Dialog.Title>
          <div className="mt-2">
            <p className="text-sm text-gray-500">
              Are you sure you want to delete the custom role{" "}
              <span className="font-semibold">{roleName}</span>? This action cannot be undone.
            </p>
          </div>
        </div>
        <div className="mt-5 flex justify-center gap-3 sm:mt-6">
          <Button variant="outlined" onClick={onClose} disabled={isDeleting}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={onDelete}
            disabled={isDeleting}
            className="flex items-center gap-2"
          >
            {isDeleting ? (
              <>
                <LoadingIcon className="h-4 w-4 text-white" />
                Deleting...
              </>
            ) : (
              "Delete"
            )}
          </Button>
        </div>
      </div>
    </DialogWrapper>
  );
}
