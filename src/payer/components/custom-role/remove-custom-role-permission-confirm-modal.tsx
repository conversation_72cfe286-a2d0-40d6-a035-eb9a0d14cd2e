import { Dialog } from "@headlessui/react";
import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";
import { useState } from "react";
import Button from "../ui/Button";
import DialogWrapper from "../ui/modal/DialogWrapper";
import { mapRoleNameToDescriptiveName } from "../../utils/user-management-utils";

interface RemovePermissionConfirmModalProps {
  show: boolean;
  onClose: () => void;
  permission: string;
  roleName: string;
  onRemove: () => Promise<void>;
}

export default function RemoveCustomRolePermissionConfirmModal({
  show,
  onClose,
  permission,
  roleName,
  onRemove,
}: RemovePermissionConfirmModalProps) {
  const [isRemoving, setIsRemoving] = useState(false);

  const handleRemove = async () => {
    setIsRemoving(true);
    try {
      await onRemove();
      onClose();
    } catch (error) {
      console.error("Error removing permission:", error);
    } finally {
      setIsRemoving(false);
    }
  };

  return (
    <DialogWrapper show={show} onClose={onClose} maxWidth="max-w-xl">
      <div className="p-6">
        <div className="flex items-center justify-center">
          <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100">
            <ExclamationTriangleIcon className="h-6 w-6 text-red-600" aria-hidden="true" />
          </div>
        </div>
        <div className="mt-3 text-center sm:mt-5">
          <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
            Remove Permission
          </Dialog.Title>
          <div className="mt-2">
            <p className="text-sm text-gray-500">
              Are you sure you want to remove the permission{" "}
              <span className="font-semibold">{mapRoleNameToDescriptiveName(permission)}</span> from
              the role <span className="font-semibold">{roleName}</span>?
            </p>
          </div>
        </div>
        <div className="mt-5 flex justify-center gap-3 sm:mt-6">
          <Button variant="outlined" onClick={onClose} disabled={isRemoving}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleRemove}
            disabled={isRemoving}
            className="flex items-center gap-2"
          >
            {isRemoving ? "Removing..." : "Remove"}
          </Button>
        </div>
      </div>
    </DialogWrapper>
  );
}
