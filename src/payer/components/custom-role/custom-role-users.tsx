import { useEffect, useMemo, useRef, useState } from "react";
import {
  useGetCustomRoleUsersQuery,
  useUpdateCustomRoleMutation,
} from "../../api/features/membershipApi";
import { CustomRoleResponse } from "../../lib/types/access-control/role";
import { UserRepresentation } from "../../lib/types/access-control/user";
import Text from "../ui/typography/Text";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import PrimaryPagination from "../ui/pagination/PrimaryPagination";
import Button from "../ui/Button";
import { PlusIcon, TrashIcon } from "@heroicons/react/24/outline";
import AddCustomRoleUsersModal from "./add-custom-role-users-modal";
import RemoveCustomRoleUsersModal from "./remove-custom-role-users-modal";
import ProgressModal from "../ui/modal/ProgressModal";
import { toast } from "react-toastify";
import UserService from "../../services/UserService";

interface CustomRoleUsersProps {
  role: CustomRoleResponse;
  payerId: number;
}

export default function CustomRoleUsers({ role, payerId }: CustomRoleUsersProps) {
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(5);
  const [selectedUserIds, setSelectedUserIds] = useState<Set<string>>(new Set());
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [showRemoveConfirm, setShowRemoveConfirm] = useState(false);
  const [isAddingUsers, setIsAddingUsers] = useState(false);
  const [isRemovingUsers, setIsRemovingUsers] = useState(false);
  const selectAllCheckboxRef = useRef<HTMLInputElement>(null);

  const [updateCustomRole] = useUpdateCustomRoleMutation();

  const handlePageNumberClick = (newPage: number) => {
    setPage(newPage);
    setSelectedUserIds(new Set());
  };

  const handleSizeChange = (newSize: number) => {
    setSize(newSize);
    setPage(1);
    setSelectedUserIds(new Set());
  };

  const { data, isLoading, error } = useGetCustomRoleUsersQuery({
    id: role.id,
    params: {
      payerId,
      page,
      size,
    },
  });

  const users = useMemo(() => data?.content || [], [data?.content]);
  const totalElements = data?.totalElements || 0;
  const totalPages = data?.totalPages || 1;

  useEffect(() => {
    setSelectedUserIds(new Set());
  }, [users]);

  useEffect(() => {
    if (selectAllCheckboxRef.current && users) {
      const someSelected = users.some((user) => selectedUserIds.has(user.userId));
      const allSelected = users.every((user) => selectedUserIds.has(user.userId));
      selectAllCheckboxRef.current.indeterminate = someSelected && !allSelected;
    }
  }, [selectedUserIds, users]);

  const handleToggleUser = (userId: string) => {
    setSelectedUserIds((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(userId)) {
        newSet.delete(userId);
      } else {
        newSet.add(userId);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    if (!users || users.length === 0) return;

    const currentPageUserIds = users.map((user) => user.userId);
    const allCurrentPageSelected = currentPageUserIds.every((id) => selectedUserIds.has(id));

    if (allCurrentPageSelected) {
      const newSet = new Set(selectedUserIds);
      currentPageUserIds.forEach((id) => newSet.delete(id));
      setSelectedUserIds(newSet);
    } else {
      const newSet = new Set(selectedUserIds);
      currentPageUserIds.forEach((id) => newSet.add(id));
      setSelectedUserIds(newSet);
    }
  };

  const handleAddUsers = async (users: UserRepresentation[]) => {
    if (users.length === 0) return;

    try {
      setIsAddingUsers(true);
      const userIds = users.map((user) => user.id);
      await updateCustomRole({
        id: role.id,
        payerId,
        body: {
          usersToAdd: userIds,
          updatedBy: UserService.getUsername() || "",
        },
      }).unwrap();
      toast.success("Users added to role successfully");
    } catch (error) {
      toast.error("Failed to add users to role");
      console.error("Error adding users to role:", error);
    } finally {
      setIsAddingUsers(false);
    }
  };

  const handleRemoveSelected = () => {
    if (selectedUserIds.size === 0) return;
    setShowRemoveConfirm(true);
  };

  const handleRemoveUsers = async () => {
    if (selectedUserIds.size === 0) return Promise.resolve();

    try {
      setIsRemovingUsers(true);
      await updateCustomRole({
        id: role.id,
        payerId,
        body: {
          usersToRemove: Array.from(selectedUserIds),
          updatedBy: UserService.getUsername() || "",
        },
      }).unwrap();
      toast.success(`${selectedUserIds.size} user(s) removed from role successfully`);
      setSelectedUserIds(new Set());
      return Promise.resolve();
    } catch (error) {
      toast.error("Failed to remove users from role");
      console.error("Error removing users from role:", error);
      return Promise.reject(error);
    } finally {
      setIsRemovingUsers(false);
      setShowRemoveConfirm(false);
    }
  };

  return (
    <div className="rounded-md border border-gray-200 bg-white p-6">
      <div className="mb-4 flex items-center justify-between">
        <div>
          <Text variant="subheading">Role Users</Text>
          {totalElements > 0 && (
            <Text variant="description" className="text-gray-600">
              {totalElements} user{totalElements !== 1 ? "s" : ""} in total
            </Text>
          )}
        </div>
        <div className="flex gap-2">
          {selectedUserIds.size > 0 && (
            <Button
              variant="destructive"
              className="flex items-center gap-1 text-xs"
              onClick={handleRemoveSelected}
            >
              <TrashIcon className="h-4 w-4" />
              Revoke Access ({selectedUserIds.size})
            </Button>
          )}
          <Button
            variant="outlined"
            className="flex items-center gap-1 text-xs"
            onClick={() => setShowAddUserModal(true)}
          >
            <PlusIcon className="h-4 w-4" /> Assign Users
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center py-10">
          <LoadingIcon className="h-5 w-5 text-blue-400" />
          <p className="ml-2 text-blue-700">Loading users...</p>
        </div>
      ) : error ? (
        <div className="flex items-center justify-center py-10">
          <p className="text-red-700">Error loading users. Try again later.</p>
        </div>
      ) : users && users.length > 0 ? (
        <>
          <div className="mb-4 overflow-hidden rounded-md border border-gray-200">
            <table className="w-full table-auto">
              <thead className="bg-gray-50 text-left">
                <tr>
                  <th className="px-4 py-3">
                    <div className="flex items-center gap-3">
                      <input
                        ref={selectAllCheckboxRef}
                        type="checkbox"
                        className="h-4 w-4 rounded border-gray-300"
                        checked={
                          users &&
                          users.length > 0 &&
                          users.every((user) => selectedUserIds.has(user.userId))
                        }
                        onChange={handleSelectAll}
                      />
                    </div>
                  </th>
                  <th className="px-4 py-3 text-sm font-medium text-gray-500">Name</th>
                  <th className="px-4 py-3 text-sm font-medium text-gray-500">Username</th>
                  <th className="px-4 py-3 text-sm font-medium text-gray-500">Email</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {users.map((user) => (
                  <tr
                    key={user.userId}
                    className={`hover:bg-gray-50 ${selectedUserIds.has(user.userId) ? "bg-blue-50" : ""}`}
                  >
                    <td className="px-4 py-3">
                      <input
                        type="checkbox"
                        className="h-4 w-4 rounded border-gray-300"
                        checked={selectedUserIds.has(user.userId)}
                        onChange={() => handleToggleUser(user.userId)}
                      />
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">{user.name || "-"}</td>
                    <td className="px-4 py-3 text-sm text-gray-900">{user.userName || "-"}</td>
                    <td className="px-4 py-3 text-sm text-gray-900">{user.userEmail || "-"}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="self-start py-4">
            <PrimaryPagination
              pageSize={size}
              totalElements={totalElements}
              totalPages={totalPages}
              pageNumber={page}
              onPageNumberClick={handlePageNumberClick}
              onSizeChange={handleSizeChange}
            />
          </div>
        </>
      ) : (
        <Text variant="paragraph" className="text-gray-500">
          No users assigned to this custom role.
        </Text>
      )}

      {/* Modals */}
      <AddCustomRoleUsersModal
        show={showAddUserModal}
        onClose={() => setShowAddUserModal(false)}
        selectedUserIds={new Set()}
        onAddUsers={handleAddUsers}
        payerId={payerId.toString()}
        existingUserIds={new Set(users.map((user) => user.userId))}
      />

      <RemoveCustomRoleUsersModal
        show={showRemoveConfirm}
        onClose={() => setShowRemoveConfirm(false)}
        users={users.filter((user) => selectedUserIds.has(user.userId))}
        roleName={role.name}
        onRemove={handleRemoveUsers}
        isLoading={isRemovingUsers}
      />

      <ProgressModal
        isProgressModalOpen={isAddingUsers}
        onClose={() => null}
        title="Adding Users"
        description="Please wait while we add the selected users to the role..."
      />

      <ProgressModal
        isProgressModalOpen={isRemovingUsers && !showRemoveConfirm}
        onClose={() => null}
        title="Removing Users"
        description="Please wait while we remove the selected users from the role..."
      />
    </div>
  );
}
