import { XMarkIcon } from "@heroicons/react/24/outline";
import { useEffect, useState } from "react";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { useGetPayerUsersQuery } from "../../api/features/membershipApi";
import Button from "../../components/ui/Button";
import SearchInput from "../../components/ui/input/SearchInput";
import DialogWrapper from "../../components/ui/modal/DialogWrapper";
import SecondaryPagination from "../../components/ui/pagination/SecondaryPagination";
import Text from "../../components/ui/typography/Text";
import { UserRepresentation } from "../../lib/types/access-control/user";

interface AddCustomRoleUsersModalProps {
  show: boolean;
  onClose: () => void;
  selectedUserIds: Set<string>;
  onAddUsers: (users: UserRepresentation[]) => void;
  payerId: string;
  existingUserIds?: Set<string>;
}

export default function AddCustomRoleUsersModal({
  show,
  onClose,
  selectedUserIds,
  onAddUsers,
  payerId,
  existingUserIds = new Set(),
}: AddCustomRoleUsersModalProps) {
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [localSelectedUserIds, setLocalSelectedUserIds] = useState<Set<string>>(new Set());
  const [localSelectedUsers, setLocalSelectedUsers] = useState<UserRepresentation[]>([]);

  const { data: userData, isLoading } = useGetPayerUsersQuery({
    payerId,
    page,
    size,
    search: searchTerm,
  });

  useEffect(() => {
    if (show) {
      setLocalSelectedUserIds(new Set(selectedUserIds));
      setLocalSelectedUsers([]);
    }
  }, [show, selectedUserIds]);

  const handleToggleUser = (user: UserRepresentation) => {
    setLocalSelectedUserIds((prev) => {
      const newIds = new Set(prev);
      if (newIds.has(user.id)) {
        newIds.delete(user.id);
        setLocalSelectedUsers(localSelectedUsers.filter((u) => u.id !== user.id));
      } else {
        newIds.add(user.id);
        setLocalSelectedUsers([...localSelectedUsers, user]);
      }
      return newIds;
    });
  };

  const handleAddUsers = () => {
    onAddUsers(localSelectedUsers);
    onClose();
  };

  const handleCancel = () => {
    setLocalSelectedUserIds(new Set());
    setLocalSelectedUsers([]);
  };

  const handleClose = () => {
    setLocalSelectedUserIds(new Set());
    setLocalSelectedUsers([]);
    onClose();
  };

  const totalPages = userData?.totalPages || 1;
  const totalElements = userData?.totalElements || 0;

  const isAllNonExistingUsersSelected =
    userData &&
    userData.content.filter((user) => !existingUserIds.has(user.id)).length > 0 &&
    userData.content
      .filter((user) => !existingUserIds.has(user.id))
      .every((user) => localSelectedUserIds.has(user.id));

  const handleSelectAllToggle = () => {
    if (isAllNonExistingUsersSelected) {
      setLocalSelectedUserIds((prev) => {
        const newIds = new Set(prev);
        userData.content
          .filter((user) => !existingUserIds.has(user.id))
          .forEach((user) => {
            newIds.delete(user.id);
          });
        setLocalSelectedUsers(
          localSelectedUsers.filter(
            (u) =>
              !userData.content
                .filter((user) => !existingUserIds.has(user.id))
                .some((user) => user.id === u.id),
          ),
        );
        return newIds;
      });
    } else {
      setLocalSelectedUserIds((prev) => {
        const newIds = new Set(prev);
        userData?.content
          .filter((user) => !existingUserIds.has(user.id))
          .forEach((user) => {
            newIds.add(user.id);
          });
        const newSelectedUsers = [...localSelectedUsers];
        userData?.content
          .filter((user) => !existingUserIds.has(user.id))
          .forEach((user) => {
            if (!localSelectedUserIds.has(user.id)) {
              newSelectedUsers.push(user);
            }
          });
        setLocalSelectedUsers(newSelectedUsers);
        return newIds;
      });
    }
  };

  const getUserCheckboxProps = (user: UserRepresentation) => {
    const isExistingUser = existingUserIds.has(user.id);
    return {
      checked: localSelectedUserIds.has(user.id) || isExistingUser,
      onChange: () => !isExistingUser && handleToggleUser(user),
      disabled: isExistingUser,
      className: `h-4 w-4 rounded border-gray-300 text-blue-600 ${isExistingUser ? "cursor-not-allowed opacity-60" : ""}`,
    };
  };

  return (
    <DialogWrapper show={show} onClose={handleClose} maxWidth="max-w-5xl">
      <div>
        <div className="relative border-b border-gray-200 px-6 py-4">
          <Text variant="subheading">Assign Users Role</Text>
          <button
            onClick={handleClose}
            className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
            aria-label="Close modal"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        <div className="p-6">
          <div className="mb-4">
            <SearchInput
              placeholder="Search users by name, username, or email"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              title="Search users by name, username, or email"
              className="h-fit w-[45ch]"
            />
          </div>

          <div className="mb-4">
            <Text variant="paragraph" className="text-gray-700">
              {localSelectedUserIds.size} user(s) selected
            </Text>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-10">
              <LoadingIcon className="h-5 w-5 text-blue-400" />
              <p className="ml-2 text-blue-700">Loading users...</p>
            </div>
          ) : userData?.content && userData.content.length > 0 ? (
            <>
              <div className="mb-4 overflow-hidden rounded-md border border-gray-200">
                <table className="w-full table-auto">
                  <thead className="bg-gray-50 text-left">
                    <tr>
                      <th className="px-4 py-2">
                        <input
                          type="checkbox"
                          checked={isAllNonExistingUsersSelected}
                          onChange={handleSelectAllToggle}
                          className="h-4 w-4 rounded border-gray-300 text-blue-600"
                        />
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-medium uppercase text-gray-500">
                        Name
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-medium uppercase text-gray-500">
                        Username
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-medium uppercase text-gray-500">
                        Email
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {userData.content.map((user) => {
                      const isExistingUser = existingUserIds.has(user.id);
                      return (
                        <tr
                          key={user.id}
                          className={`border-b border-gray-200 hover:bg-gray-50 ${isExistingUser ? "bg-gray-50" : ""}`}
                        >
                          <td className="px-4 py-2">
                            <input type="checkbox" {...getUserCheckboxProps(user)} />
                          </td>
                          <td className="px-4 py-2 text-xs">
                            <div className="flex items-center">
                              {user.firstName && user.lastName
                                ? `${user.firstName} ${user.lastName}`
                                : "-"}
                              {isExistingUser && (
                                <span className="ml-2 rounded-full bg-blue-100 px-2 py-0.5 text-xs text-blue-800">
                                  Already has role
                                </span>
                              )}
                            </div>
                          </td>
                          <td className="px-4 py-2 text-xs">{user.username || "-"}</td>
                          <td className="px-4 py-2 text-xs">{user.email || "-"}</td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>

              <div className="mb-4">
                <SecondaryPagination
                  currentPage={page}
                  size={size}
                  totalElements={totalElements}
                  totalPages={totalPages}
                  setCurrentPage={setPage}
                />
              </div>
            </>
          ) : (
            <div className="py-8 text-center text-gray-500">No users found</div>
          )}

          <div className="mt-6 flex justify-end space-x-3">
            <Button
              variant="outlined"
              onClick={handleCancel}
              disabled={localSelectedUserIds.size === 0}
              className="disabled:cursor-not-allowed"
            >
              Clear Selection
            </Button>
            <Button
              type="button"
              variant="filled"
              onClick={handleAddUsers}
              disabled={localSelectedUserIds.size === 0}
            >
              Add {localSelectedUserIds.size} {localSelectedUserIds.size === 1 ? "User" : "Users"}
            </Button>
          </div>
        </div>
      </div>
    </DialogWrapper>
  );
}
