import React, { useEffect, useState } from "react";
import { JsxElement } from "typescript";

const PAGE_SIZES = [10, 20, 50, 100];
function Pagination3({
  totalElements,
  totalPages,
  pageNumber,
  OnPageNumberClick,
  OnSizeChange,
  pageSize,
}) {
  const [totalItems, setTotalItems] = useState(0);
  const [totalPageItems, setTotalPageItems] = useState(0);
  const [pageNo, setPageNo] = useState(0);
  const [list, setList] = useState([]);

  useEffect(() => {
    setTotalItems(Number(totalElements));
    setTotalPageItems(Number(totalPages));
    setPageNo(Number(pageNumber) + 1);
    setList(Array.from(Array(totalPages).keys()));

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [totalPages, pageNumber]);

  function handleSizeChange(e) {
    OnSizeChange(e);
  }

  return (
    <div className="flex justify-center">
      <nav className="flex items-center" role="navigation" aria-label="Navigation">
        <div className="ml-2 mr-2 items-center text-gray-400">{totalElements} items</div>

        <select
          id="size"
          name="size"
          onChange={(e) => handleSizeChange(e)}
          className="rounded border-gray-200 text-sm text-gray-500"
        >
          {PAGE_SIZES.map((size, index) => (
            <option value={size} key={index} selected={size == pageSize}>
              {size} per page
            </option>
          ))}
        </select>

        <div className="mr-2">
          {pageNo === 1 ? (
            ""
          ) : (
            <span
              onClick={(e) => {
                e.stopPropagation();
                OnPageNumberClick(pageNo - 1);
              }}
              className="inline-flex items-center justify-center border border-gray-200 bg-white px-3.5 py-2 font-bold leading-5 text-gray-400 hover:bg-blue-800 hover:text-white"
            >
              <span className="sr-only">Previous</span>
              <wbr />
              <svg className="h-4 w-4 fill-current" viewBox="0 0 16 16">
                <path d="M9.4 13.4l1.4-1.4-4-4 4-4-1.4-1.4L4 8z" />
              </svg>
            </span>
          )}
        </div>

        <ul
          className={`inline-flex -space-x-px text-sm font-medium shadow-sm ${
            totalPages > 20 ? "hidden" : "block"
          }`}
        >
          {list.map((item) => (
            <li key={item.id} className="mx-2">
              {pageNo === Number(item + 1) ? (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    OnPageNumberClick(item + 1);
                  }}
                  className="inline-flex items-center justify-center rounded-l border border-gray-200 bg-white px-3.5 py-2 font-semibold leading-5 text-blue-800"
                >
                  {item + 1}
                </button>
              ) : (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    OnPageNumberClick(item + 1);
                  }}
                  className="inline-flex items-center justify-center border border-gray-200 bg-white px-3.5 py-2 font-semibold leading-5 text-gray-600 hover:text-blue-800 "
                >
                  {item + 1}
                </button>
              )}
            </li>
          ))}
        </ul>

        <div className="ml-2">
          {totalPages == pageNo ? (
            ""
          ) : (
            <button
              onClick={(e) => {
                e.stopPropagation();
                OnPageNumberClick(pageNo + 1);
              }}
              className="inline-flex items-center justify-center rounded border border-gray-200 bg-white px-2.5 py-2 font-semibold leading-5 text-gray-400 shadow-sm hover:bg-blue-800 hover:text-white"
            >
              <span className="sr-only">Next</span>
              <wbr />
              <svg className="h-4 w-4 fill-current" viewBox="0 0 16 16">
                <path d="M6.6 13.4L5.2 12l4-4-4-4 1.4-1.4L12 8z" />
              </svg>
            </button>
          )}
        </div>
        <p className="items-center text-gray-400">
          {isNaN(pageNo) ? "" : `Page ${pageNo} of ${totalPages}`}
        </p>
      </nav>
    </div>
  );
}

export default Pagination3;
