import { useEffect, useRef } from "react";
import { useSelector } from "react-redux";
import { Route, Routes, useLocation } from "react-router-dom";
import ComingSoon from "../pages/ComingSoon";
import ContactUs from "../pages/ContactUs";
import Dashboard from "../pages/Dashboard";
import AIAdjudication from "../pages/ai/AIAdjudication";
import ClinicalReports from "../pages/care/ClinicalReports";
import PreAuthorizations from "../pages/care/PreAuthorizations";
import VisitReactivation from "../pages/care/VisitReactivation";
import PreAuthReports from "../pages/care/pre-auth-reports";
import BillVisit from "../pages/claims/BillVisit";
import OfflineVisits from "../pages/claims/OfflineVisits";
import Reimbursements from "../pages/claims/Reimbursements";
import StartVisit from "../pages/claims/StartVisit";
import ClaimsBatchingAdjudication from "../pages/claims/batching/ClaimsBatchingAdjudication";
import BatchClaim from "../pages/claims/claim-vetting/batch-claim/BatchClaim";
import BatchList from "../pages/claims/claim-vetting/batch-list/BatchList";
import Batch from "../pages/claims/claim-vetting/individual-batch/Batch";
import ClaimsReports from "../pages/claims/reports/ClaimsReports";
import Reversal from "../pages/claims/reversal/Reversal";
import ClaimsBatching from "../pages/claims/vetting/ClaimsBatching";
import ClaimsVetting from "../pages/claims/vetting/ClaimsVetting";
import VetClaim from "../pages/claims/vetting/VetClaim";
import Paid from "../pages/finance-and-accounting-settlement/Paid/Paid";
import Paying from "../pages/finance-and-accounting-settlement/Paying/Paying";
import VoucherList from "../pages/finance-and-accounting-settlement/VoucherList/VoucherList";
import FindMember from "../pages/members/FindMember";
import MemberDetails, { MemberDetailsAction } from "../pages/members/MemberDetails";
import MemberPage from "../pages/members/MemberPage";
import MemberReports from "../pages/members/MemberReports";
import Help from "../pages/members/mass-actions/Help";
import MassActions from "../pages/members/mass-actions/MassActions";
import Header from "../pages/partials/Header";
import Sidebar from "../pages/partials/sidebar/Sidebar";
import PolicyOverview from "../pages/policies/PolicyOverview";
import PolicyCategoryOverview from "../pages/policies/category/PolicyCategoryOverview";
import Providers from "../pages/providers/Providers";
import Restrictions from "../pages/providers/Restrictions";
import Reconciliation, { ReconciliationReport } from "../pages/reconciliation";
import Remittance from "../pages/remittance";
import Schemes from "../pages/schemes/Schemes";
import SchemesPolicies from "../pages/schemes/SchemesPolicies";
import SignOff from "../pages/signoff/SignOff";
import UserAuditLogs from "../pages/users/UserAuditLogs";
import UserManagementTable from "../pages/users/UserManagementTable";
import UserProfileInfo from "../pages/users/components/user-profile-info";
import CreateDenyPolicy from "../pages/users/create-deny-policy";
import CreateGroup from "../pages/users/create-group";
import CreateCustomRole from "../pages/users/create-custom-role";
import CustomRoleDetail from "../pages/users/custom-role-detail";
import CustomRoles from "../pages/users/custom-roles";
import DenyPolicies from "../pages/users/deny-policies";
import DenyPolicyDetail from "../pages/users/deny-policy-detail";
import GroupDetail from "../pages/users/group-detail";
import Groups from "../pages/users/groups";
import PredefinedRoles from "../pages/users/predefined-roles";
import RoleDetail from "../pages/users/role-detail";
import Vouchering from "../pages/vouchering/Vouchering";
import UserService from "../services/UserService";
import { RootState } from "../store";
import {
  FIRST_ASSURANCE_PAYER_ID,
  KPLC_PAYER_ID,
  LCT_PAYER_ID,
  LIAISON_HEALTHCARE_PAYER_ID,
} from "../utils/constants/payerIds";
import AccessManagement from "./access-management/AccessManagement";
import { ALL_CARE_ROLES } from "./access-management/data";
import UserLogs from "./audit-logs/UserLogs";
import VoucherEditingTable from "./vouchering/VoucherEditingTable";
import { useUserEffectivePermissions } from "../utils/user-management-utils";
import SuperAdminUserProfile from "../pages/users/super-admin-user-profile";
import NotificationsSidePanel from "./NotificationsSidePanel/NotificationsSidePanel";

export default function MenuBox() {
  const location = useLocation();
  const payerName = useSelector((state: RootState) => state.payers.payer.name);

  const sidebarToggleRef = useRef<HTMLInputElement | null>(null);
  const toggleSidebar = () => sidebarToggleRef.current?.click();

  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];

  const { hasPermission } = useUserEffectivePermissions();

  const hasFinanceAccess = payerId === LCT_PAYER_ID;
  const hasAdjudicationAccess =
    payerId === LCT_PAYER_ID ||
    payerId === LIAISON_HEALTHCARE_PAYER_ID ||
    payerId === FIRST_ASSURANCE_PAYER_ID;
  const hasVettingAndBatchingAccess =
    payerId === KPLC_PAYER_ID || payerId === LIAISON_HEALTHCARE_PAYER_ID;
  const hasAiAccess =
    payerId === LCT_PAYER_ID ||
    payerId === LIAISON_HEALTHCARE_PAYER_ID ||
    payerId === FIRST_ASSURANCE_PAYER_ID;

  const _USER_HAS_ROLE_HR = hasPermission(["HR"]);
  const _USER_HAS_ROLE_HR_ONE = hasPermission(["HR_LVL_1"]);

  const USER_HAS_SCHEME_OVERVIEW_ROLE = hasPermission(["SCHEME_OVERVIEW_ROLE"]);

  const USER_HAS_MEMBERSHIP_ROLE = hasPermission(["MEMBERSHIP_ROLE"]);
  const USER_HAS_MEMBERSHIP_INQUIRY_ROLE = hasPermission(["MEMBERSHIP_INQUIRY_ROLE"]);
  const USER_HAS_MEMBERSHIP_EDIT_ROLE = hasPermission(["MEMBERSHIP_EDIT_ROLE"]);
  const USER_HAS_MEMBERSHIP_ACTIVATE_ROLE = hasPermission(["MEMBERSHIP_ACTIVATE_ROLE"]);
  const USER_HAS_MEMBERSHIP_SUSPEND_ROLE = hasPermission(["MEMBERSHIP_SUSPEND_ROLE"]);
  const USER_HAS_MEMBERSHIP_DEACTIVATE_ROLE = hasPermission(["MEMBERSHIP_DEACTIVATE_ROLE"]);
  const USER_HAS_MEMBERSHIP_STATUS_UPDATE_ROLE =
    USER_HAS_MEMBERSHIP_ACTIVATE_ROLE ||
    USER_HAS_MEMBERSHIP_DEACTIVATE_ROLE ||
    USER_HAS_MEMBERSHIP_SUSPEND_ROLE;
  const USER_HAS_MEMBERSHIP_DETACH_BIOMETRICS_ROLE = hasPermission([
    "MEMBERSHIP_DETACH_BIOMETRICS_ROLE",
  ]);
  const USER_HAS_MEMBERSHIP_BENEFIT_SUSPEND_ROLE = hasPermission([
    "MEMBERSHIP_BENEFIT_SUSPEND_ROLE",
  ]);
  const USER_HAS_MEMBERSHIP_BENEFIT_TOPUP_ROLE = hasPermission(["MEMBERSHIP_BENEFIT_TOPUP_ROLE"]);
  const USER_HAS_MEMBERSHIP_BENEFIT_TRANSFER_ROLE = hasPermission([
    "MEMBERSHIP_BENEFIT_TRANSFER_ROLE",
  ]);
  const USER_HAS_BENEFIT_ENHANCEMENT_ROLE =
    USER_HAS_MEMBERSHIP_BENEFIT_SUSPEND_ROLE ||
    USER_HAS_MEMBERSHIP_BENEFIT_TOPUP_ROLE ||
    USER_HAS_MEMBERSHIP_BENEFIT_TRANSFER_ROLE;
  const USER_HAS_MEMBERSHIP_MASS_DEACTIVATION_ROLE = hasPermission([
    "MEMBERSHIP_MASS_DEACTIVATION_ROLE",
  ]);
  const USER_HAS_MEMBERSHIP_MASS_ACTIVATION_ROLE = hasPermission([
    "MEMBERSHIP_MASS_ACTIVATION_ROLE",
  ]);
  const USER_HAS_MEMBERSHIP_MASS_EDIT_ROLE = hasPermission(["MEMBERSHIP_MASS_EDIT_ROLE"]);
  const USER_HAS_MASS_ACTIONS_ROLE =
    USER_HAS_MEMBERSHIP_MASS_DEACTIVATION_ROLE ||
    USER_HAS_MEMBERSHIP_MASS_ACTIVATION_ROLE ||
    USER_HAS_MEMBERSHIP_MASS_EDIT_ROLE;
  const USER_HAS_MEMBERSHIP_REPORTS_ROLE = hasPermission(["MEMBERSHIP_REPORTS_ROLE"]);

  const USER_HAS_ANY_MEMBERSHIP_ROLE =
    USER_HAS_MEMBERSHIP_ROLE ||
    USER_HAS_MEMBERSHIP_INQUIRY_ROLE ||
    USER_HAS_MEMBERSHIP_EDIT_ROLE ||
    USER_HAS_MEMBERSHIP_ACTIVATE_ROLE ||
    USER_HAS_MEMBERSHIP_SUSPEND_ROLE ||
    USER_HAS_MEMBERSHIP_DEACTIVATE_ROLE ||
    USER_HAS_MEMBERSHIP_DETACH_BIOMETRICS_ROLE ||
    USER_HAS_MEMBERSHIP_BENEFIT_SUSPEND_ROLE ||
    USER_HAS_MEMBERSHIP_BENEFIT_TOPUP_ROLE ||
    USER_HAS_MEMBERSHIP_BENEFIT_TRANSFER_ROLE ||
    USER_HAS_MEMBERSHIP_MASS_DEACTIVATION_ROLE ||
    USER_HAS_MEMBERSHIP_MASS_ACTIVATION_ROLE ||
    USER_HAS_MEMBERSHIP_MASS_EDIT_ROLE ||
    USER_HAS_MEMBERSHIP_REPORTS_ROLE;

  /* provider roles */
  const USER_HAS_PROVIDER_ROLE = hasPermission(["PROVIDER_ROLE"]);
  const USER_HAS_PROVIDER_VIEW_ROLE = hasPermission(["PROVIDER_VIEW_ROLE"]);
  const USER_HAS_PROVIDER_RESTRICTIONS_ROLE = hasPermission(["PROVIDER_RESTRICTIONS_ROLE"]);

  const USER_HAS_ANY_PROVIDER_ROLE =
    USER_HAS_PROVIDER_ROLE || USER_HAS_PROVIDER_VIEW_ROLE || USER_HAS_PROVIDER_RESTRICTIONS_ROLE;

  /* care roles */
  const USER_HAS_CARE_PREAUTH_REVIEW_ROLE_BASE = hasPermission(["CARE_PREAUTH_REVIEW_ROLE"]);
  const USER_HAS_CARE_PREAUTH_VIEW_ALL_ROLE = hasPermission(["CARE_PREAUTH_VIEW_ALL_ROLE"]);
  const USER_HAS_CARE_PREAUTH_VIEW_DENTAL_ROLE = hasPermission(["CARE_PREAUTH_VIEW_DENTAL_ROLE"]);
  const USER_HAS_CARE_PREAUTH_VIEW_OPTICAL_ROLE = hasPermission(["CARE_PREAUTH_VIEW_OPTICAL_ROLE"]);
  const USER_HAS_CARE_PREAUTH_VIEW_OUTPATIENT_ROLE = hasPermission([
    "CARE_PREAUTH_VIEW_OUTPATIENT_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_VIEW_INPATIENT_ROLE = hasPermission([
    "CARE_PREAUTH_VIEW_INPATIENT_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_VIEW_MATERNITY_ROLE = hasPermission([
    "CARE_PREAUTH_VIEW_MATERNITY_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_APPROVE_ALL_ROLE = hasPermission(["CARE_PREAUTH_APPROVE_ALL_ROLE"]);
  const USER_HAS_CARE_PREAUTH_APPROVE_DENTAL_ROLE = hasPermission([
    "CARE_PREAUTH_APPROVE_DENTAL_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_APPROVE_OPTICAL_ROLE = hasPermission([
    "CARE_PREAUTH_APPROVE_OPTICAL_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_APPROVE_OUTPATIENT_ROLE = hasPermission([
    "CARE_PREAUTH_APPROVE_OUTPATIENT_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_APPROVE_INPATIENT_ROLE = hasPermission([
    "CARE_PREAUTH_APPROVE_INPATIENT_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_APPROVE_MATERNITY_ROLE = hasPermission([
    "CARE_PREAUTH_APPROVE_MATERNITY_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_TOPUP_ALL_ROLE = hasPermission(["CARE_PREAUTH_TOPUP_ALL_ROLE"]);
  const USER_HAS_CARE_PREAUTH_TOPUP_DENTAL_ROLE = hasPermission(["CARE_PREAUTH_TOPUP_DENTAL_ROLE"]);
  const USER_HAS_CARE_PREAUTH_TOPUP_OPTICAL_ROLE = hasPermission([
    "CARE_PREAUTH_TOPUP_OPTICAL_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_TOPUP_OUTPATIENT_ROLE = hasPermission([
    "CARE_PREAUTH_TOPUP_OUTPATIENT_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_TOPUP_INPATIENT_ROLE = hasPermission([
    "CARE_PREAUTH_TOPUP_INPATIENT_ROLE",
  ]);
  const USER_HAS_CARE_PREAUTH_TOPUP_MATERNITY_ROLE = hasPermission([
    "CARE_PREAUTH_TOPUP_MATERNITY_ROLE",
  ]);

  const USER_HAS_CARE_PREAUTH_REVIEW_ROLE =
    USER_HAS_CARE_PREAUTH_REVIEW_ROLE_BASE ||
    USER_HAS_CARE_PREAUTH_VIEW_ALL_ROLE ||
    USER_HAS_CARE_PREAUTH_VIEW_DENTAL_ROLE ||
    USER_HAS_CARE_PREAUTH_VIEW_OPTICAL_ROLE ||
    USER_HAS_CARE_PREAUTH_VIEW_OUTPATIENT_ROLE ||
    USER_HAS_CARE_PREAUTH_VIEW_INPATIENT_ROLE ||
    USER_HAS_CARE_PREAUTH_VIEW_MATERNITY_ROLE ||
    USER_HAS_CARE_PREAUTH_APPROVE_ALL_ROLE ||
    USER_HAS_CARE_PREAUTH_APPROVE_DENTAL_ROLE ||
    USER_HAS_CARE_PREAUTH_APPROVE_OPTICAL_ROLE ||
    USER_HAS_CARE_PREAUTH_APPROVE_OUTPATIENT_ROLE ||
    USER_HAS_CARE_PREAUTH_APPROVE_INPATIENT_ROLE ||
    USER_HAS_CARE_PREAUTH_APPROVE_MATERNITY_ROLE ||
    USER_HAS_CARE_PREAUTH_TOPUP_ALL_ROLE ||
    USER_HAS_CARE_PREAUTH_TOPUP_DENTAL_ROLE ||
    USER_HAS_CARE_PREAUTH_TOPUP_OPTICAL_ROLE ||
    USER_HAS_CARE_PREAUTH_TOPUP_OUTPATIENT_ROLE ||
    USER_HAS_CARE_PREAUTH_TOPUP_INPATIENT_ROLE ||
    USER_HAS_CARE_PREAUTH_TOPUP_MATERNITY_ROLE;

  const USER_HAS_CARE_VISIT_REACTIVATION_ROLE = hasPermission(["CARE_VISIT_REACTIVATION_ROLE"]);
  const USER_HAS_CARE_REPORTS_RETRIEVAL_ROLE = hasPermission(["CARE_REPORTS_RETRIEVAL_ROLE"]);
  const USER_HAS_CARE_DIAGNOSIS_REPORTS_ROLE = hasPermission([
    "CARE_PREAUTH_GET_DIAGNOSIS_REPORTS_ROLE",
  ]);

  const USER_HAS_ANY_CARE_ROLE = hasPermission(ALL_CARE_ROLES);

  /* claims roles */
  const USER_HAS_CLAIMS_ROLE = hasPermission(["CLAIMS_ROLE"]);
  const USER_HAS_CLAIMS_REIMBURSEMENT_ROLE = hasPermission(["CLAIMS_REIMBURSEMENT_ROLE"]);
  const USER_HAS_CLAIMS_OFFLCT_ROLE = hasPermission(["CLAIMS_OFFLCT_ROLE"]);
  const USER_HAS_CLAIMS_VETTING_ROLE = hasPermission(["CLAIMS_VETTING_ROLE"]);
  const USER_HAS_CLAIMS_BATCHING_ROLE = hasPermission(["CLAIMS_BATCHING_ROLE"]);
  const USER_HAS_CLAIMS_REPORTS_ROLE = hasPermission(["CLAIMS_REPORTS_ROLE"]);
  const USER_HAS_CLAIMS_REVERSAL_ROLE = hasPermission(["CLAIMS_REVERSAL_ROLE"]);

  const USER_HAS_ANY_CLAIMS_ROLE =
    USER_HAS_CLAIMS_ROLE ||
    USER_HAS_CLAIMS_REIMBURSEMENT_ROLE ||
    USER_HAS_CLAIMS_OFFLCT_ROLE ||
    USER_HAS_CLAIMS_VETTING_ROLE ||
    USER_HAS_CLAIMS_BATCHING_ROLE ||
    USER_HAS_CLAIMS_REPORTS_ROLE ||
    USER_HAS_CLAIMS_REVERSAL_ROLE;

  /* claims adjudication roles */
  const USER_HAS_CLAIMS_ADJUDICATION_ROLE = hasPermission(["CLAIMS_ADJUDICATION_ROLE"]);
  const USER_HAS_CLAIMS_ADJUDICATION_BATCHING_ROLE = hasPermission([
    "CLAIMS_ADJUDICATION_BATCHING_ROLE",
  ]);
  const USER_HAS_CLAIMS_ADJUDICATION_VETTING_ROLE = hasPermission([
    "CLAIMS_ADJUDICATION_VETTING_ROLE",
  ]);
  const USER_HAS_CLAIMS_ADJUDICATION_AI_ROLE = hasPermission(["CLAIMS_ADJUDICATION_AI_ROLE"]);

  const USER_HAS_ANY_ADJUDICATION_ROLE =
    USER_HAS_CLAIMS_ADJUDICATION_ROLE ||
    USER_HAS_CLAIMS_ADJUDICATION_BATCHING_ROLE ||
    USER_HAS_CLAIMS_ADJUDICATION_VETTING_ROLE ||
    USER_HAS_CLAIMS_ADJUDICATION_AI_ROLE;

  /* finance roles */
  const USER_HAS_FINANCE_AND_ACCOUNTING_ROLE = hasPermission(["FINANCE_AND_ACCOUNTING_ROLE"]);
  const USER_HAS_FINANCE_AND_ACCOUNTING_VOUCHERING_ROLE = hasPermission([
    "FINANCE_AND_ACCOUNTING_VOUCHERING_ROLE",
  ]);
  const USER_HAS_FINANCE_AND_ACCOUNTING_SETTLEMENT_ROLE = hasPermission([
    "FINANCE_AND_ACCOUNTING_SETTLEMENT_ROLE",
  ]);
  const USER_HAS_FINANCE_AND_ACCOUNTING_REMITTANCE_ROLE = hasPermission([
    "FINANCE_AND_ACCOUNTING_REMITTANCE_ROLE",
  ]);
  const USER_HAS_FINANCE_AND_ACCOUNTING_CREDIT_ANALYSIS_ROLE = hasPermission([
    "FINANCE_AND_ACCOUNTING_CREDIT_ANALYSIS_ROLE",
  ]);

  const USER_HAS_ANY_FINANCE_AND_ACCOUNTING_ROLE =
    USER_HAS_FINANCE_AND_ACCOUNTING_ROLE ||
    USER_HAS_FINANCE_AND_ACCOUNTING_VOUCHERING_ROLE ||
    USER_HAS_FINANCE_AND_ACCOUNTING_SETTLEMENT_ROLE ||
    USER_HAS_FINANCE_AND_ACCOUNTING_REMITTANCE_ROLE ||
    USER_HAS_FINANCE_AND_ACCOUNTING_CREDIT_ANALYSIS_ROLE;

  /** user management roles*/
  const USER_HAS_USER_MANAGEMENT_ROLES_ASSIGNMENT_ROLE = hasPermission([
    "USER_MANAGEMENT_ROLES_ASSIGNMENT_ROLE",
  ]);
  const USER_HAS_USER_MANAGEMENT_AUDIT_LOGS_ROLE = hasPermission([
    "USER_MANAGEMENT_AUDIT_LOGS_ROLE",
  ]);

  useEffect(() => {
    window.scroll({ top: 0 });
  }, [location.pathname, payerName]); // triggered on route change

  return (
    <div className="drawer lg:drawer-open lg:red">
      <input id="sidebar-drawer" type="checkbox" className="drawer-toggle" ref={sidebarToggleRef} />
      <div className="drawer-content flex min-h-screen flex-col">
        {/* Page content */}
        <Header />
        <NotificationsSidePanel />
        <Routes>
          <Route path="/" element={<Dashboard />} />
          {USER_HAS_SCHEME_OVERVIEW_ROLE && (
            <>
              <Route path="/schemes" element={<Schemes />} />
              <Route path="/schemes/:id/policies" element={<SchemesPolicies />} />
              <Route path="/policies/overview/:id" element={<PolicyOverview />} />
              <Route path="/policies/category/overview/:id" element={<PolicyCategoryOverview />} />
            </>
          )}
          {USER_HAS_ANY_PROVIDER_ROLE && (
            <>
              {USER_HAS_PROVIDER_VIEW_ROLE && (
                <>
                  <Route path="/providers" element={<Providers />} />
                </>
              )}
              {USER_HAS_PROVIDER_RESTRICTIONS_ROLE && (
                <Route path="/restrictions" element={<Restrictions />} />
              )}
            </>
          )}
          /* ------------------------------- Membership ------------------------------- */
          {USER_HAS_ANY_MEMBERSHIP_ROLE && (
            <>
              <Route path="/members" element={<MemberPage />}>
                <Route path=":id" element={<MemberDetails />} />
              </Route>
              {/* Route expects redirect URL param and/or location state */}
              {USER_HAS_MEMBERSHIP_REPORTS_ROLE && (
                <Route path="/member/reports" element={<MemberReports />} />
              )}
              {USER_HAS_MEMBERSHIP_INQUIRY_ROLE && (
                <>
                  <Route
                    path="/members/find"
                    element={<FindMember next={(b) => [`/members/${b.id}`]} />}
                  />
                </>
              )}
              <Route path="/members/search" element={<FindMember />} />
              {USER_HAS_MEMBERSHIP_EDIT_ROLE && (
                <Route
                  path="/members/edit"
                  element={
                    <FindMember
                      next={(b) => [`/members/${b.id}?action=${MemberDetailsAction.EDIT}`]}
                    />
                  }
                />
              )}
              {USER_HAS_MEMBERSHIP_STATUS_UPDATE_ROLE && (
                <Route
                  path="/members/update-status"
                  element={
                    <FindMember
                      next={(b) => [`/members/${b.id}?action=${MemberDetailsAction.UPDATE_STATUS}`]}
                    />
                  }
                />
              )}
              {USER_HAS_MEMBERSHIP_DETACH_BIOMETRICS_ROLE && (
                <Route
                  path="/members/detach-biometrics"
                  element={
                    <FindMember
                      next={(b) => [
                        `/members/${b.id}?action=${MemberDetailsAction.DETACH_BIOMETRICS}`,
                      ]}
                    />
                  }
                />
              )}
              {USER_HAS_BENEFIT_ENHANCEMENT_ROLE && (
                <Route
                  path="/members/benefit-enhancement"
                  element={
                    <FindMember
                      next={(b) => [
                        `/members/${b.id}?action=${MemberDetailsAction.BENEFIT_ENHANCEMENT}`,
                      ]}
                    />
                  }
                />
              )}
              {USER_HAS_MASS_ACTIONS_ROLE && (
                <>
                  <Route path="/members/batch" element={<MassActions />} />
                  <Route path="/members/batch/help" element={<Help />} />
                </>
              )}
            </>
          )}
          /*---------------------------Finance and Accounting--------------------*/
          {hasFinanceAccess && USER_HAS_ANY_FINANCE_AND_ACCOUNTING_ROLE && (
            <>
              {USER_HAS_FINANCE_AND_ACCOUNTING_VOUCHERING_ROLE && (
                <>
                  <Route path="/finance-and-accounting/vouchering" element={<Vouchering />} />
                  <Route
                    path="/finance-and-accounting/vouchering/edit/:voucherId/:editType"
                    element={<VoucherEditingTable />}
                  />
                </>
              )}
              {USER_HAS_FINANCE_AND_ACCOUNTING_SETTLEMENT_ROLE && (
                <Route path="/finance-and-accounting/settlement/">
                  <Route index element={<VoucherList />} />
                  <Route path="paying" element={<Paying />} />
                  <Route path="paid" element={<Paid />} />
                </Route>
              )}
              {USER_HAS_FINANCE_AND_ACCOUNTING_CREDIT_ANALYSIS_ROLE && (
                <>
                  <Route path="/finance-and-accounting/reconciliation">
                    <Route index element={<Reconciliation />} />
                    <Route path="report/:reconciliationId" element={<ReconciliationReport />} />
                  </Route>
                  <Route path="/finance-and-accounting/sign-offs" element={<SignOff />} />
                </>
              )}
              {USER_HAS_FINANCE_AND_ACCOUNTING_REMITTANCE_ROLE && (
                <Route path="/finance-and-accounting/remittance" element={<Remittance />} />
              )}
            </>
          )}
          /* ----------------------------------Care------------------------------------- */
          {USER_HAS_ANY_CARE_ROLE && (
            <>
              {USER_HAS_CARE_PREAUTH_REVIEW_ROLE && (
                <Route path="/preauth" element={<PreAuthorizations />} />
              )}
              {USER_HAS_CARE_VISIT_REACTIVATION_ROLE && (
                <Route path="/visit-reactivation" element={<VisitReactivation />} />
              )}
              {USER_HAS_CARE_REPORTS_RETRIEVAL_ROLE && (
                <Route path="/care/reports" element={<ClinicalReports />} />
              )}
              {USER_HAS_CARE_DIAGNOSIS_REPORTS_ROLE && (
                <Route path="/care/pre-auth-reports" element={<PreAuthReports />} />
              )}
            </>
          )}
          /* --------------------------------Claims------------------------------------- */
          {USER_HAS_ANY_CLAIMS_ROLE && (
            <>
              {USER_HAS_CLAIMS_REIMBURSEMENT_ROLE && (
                <Route path="/reimbursements" element={<Reimbursements />} />
              )}
              {USER_HAS_CLAIMS_OFFLCT_ROLE && (
                <>
                  <Route path="/offline-visits" element={<OfflineVisits />} />
                  <Route path="/visits/new" element={<StartVisit />} />
                  <Route path="/visits/:id/bill" element={<BillVisit />} />
                  <Route path="/visits/:id/vet" element={<VetClaim />}></Route>
                </>
              )}
              {USER_HAS_CLAIMS_REPORTS_ROLE && (
                <Route path="/claims/reports" element={<ClaimsReports />} />
              )}
              {USER_HAS_CLAIMS_REVERSAL_ROLE && <Route path="/reversal" element={<Reversal />} />}
              {hasVettingAndBatchingAccess && (
                <>
                  {USER_HAS_CLAIMS_BATCHING_ROLE && (
                    <Route path="/claims/batching" element={<ClaimsBatching />} />
                  )}
                  {USER_HAS_CLAIMS_VETTING_ROLE && (
                    <Route path="/claims/vetting" element={<ClaimsVetting />} />
                  )}
                </>
              )}
            </>
          )}
          /* -----------------------------------Adjudication---------------------------------- */
          {hasAdjudicationAccess && USER_HAS_ANY_ADJUDICATION_ROLE && (
            <>
              {USER_HAS_CLAIMS_ADJUDICATION_BATCHING_ROLE && (
                <Route path="/adjudication/batching" element={<ClaimsBatchingAdjudication />} />
              )}
              {USER_HAS_CLAIMS_ADJUDICATION_VETTING_ROLE && (
                <>
                  <Route path="/adjudication/vetting" element={<BatchList />} />
                  <Route path="/adjudication/vetting/batch/:batchId" element={<Batch />} />
                  <Route
                    path="/adjudication/vetting/batch/:batchId/claim/:claimId"
                    element={<BatchClaim />}
                  />
                </>
              )}
              {hasAiAccess && USER_HAS_CLAIMS_ADJUDICATION_AI_ROLE && (
                <>
                  <Route path="/adjudication/ai" element={<AIAdjudication />} />
                </>
              )}
            </>
          )}
          --------------------------------User Management------------------------------ */
          {USER_HAS_USER_MANAGEMENT_ROLES_ASSIGNMENT_ROLE && (
            <>
              <Route path="/users" element={<UserManagementTable />} />
              <Route path="/users/profile/:userId" element={<UserProfileInfo />} />
              <Route
                path="/users/super-admin/profile/:userId"
                element={<SuperAdminUserProfile />}
              />
              <Route path="/users/access-control" element={<AccessManagement />} />
              <Route path="/users/roles/predefined" element={<PredefinedRoles />} />
              <Route path="/users/roles/custom" element={<CustomRoles />} />
              <Route path="/users/roles/custom/:id" element={<CustomRoleDetail />} />
              <Route path="/users/roles/custom/create" element={<CreateCustomRole />} />
              <Route path="/users/roles/predefined/:slug" element={<RoleDetail />} />
              <Route path="/users/groups" element={<Groups />} />
              <Route path="/users/groups/create" element={<CreateGroup />} />
              <Route path="/users/groups/:id" element={<GroupDetail />} />
              <Route path="/users/deny-policies" element={<DenyPolicies />} />
              <Route path="/users/deny-policies/create" element={<CreateDenyPolicy />} />
              <Route path="/users/deny-policies/:id" element={<DenyPolicyDetail />} />
            </>
          )}
          {USER_HAS_USER_MANAGEMENT_AUDIT_LOGS_ROLE && (
            <>
              <Route path="/users/audit-logs" element={<UserAuditLogs />} />
              <Route path="/users/audit-logs/view" element={<UserLogs />} />
            </>
          )}
          /* --------------------------------Features Coming Soon------------------------------ */
          <Route path="/cards" element={<ComingSoon />} />
          /* --------------------------------Contact Info------------------------------ */
          <Route path="/contact-us" element={<ContactUs />} />
        </Routes>
      </div>
      <div className="drawer-side">
        <label
          htmlFor="sidebar-drawer"
          aria-label="close sidebar"
          className="drawer-overlay"
        ></label>
        <Sidebar className="min-h-full w-72 p-2 lg:p-4" toggleSidebar={toggleSidebar} />
      </div>
    </div>
  );
}
