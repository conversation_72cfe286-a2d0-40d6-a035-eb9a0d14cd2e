import { RequestType } from "~lib/api/types";

export function requestTypeIcon(requestType: RequestType) {
  switch (requestType) {
    case RequestType.DENTAL:
      return (
        // prettier-ignore
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 72 96" className="w-12 h-12">
          <g fill="currentColor">
            <path d="M64.77 19.13c0 3.95-1.22 7.6-2.42 11.28a94.96 94.96 0 0 0-4.21 19.66c-.64 6.17-1.4 12.33-3.2 18.3-.96 3.22-2.45 6.1-5.42 7.86-4.42 2.63-8.77.55-9.57-4.38a53.74 53.74 0 0 1-.35-5.78c-.21-4.08-.67-8.13-2.1-12a11.44 11.44 0 0 0-1.54-2.92c-1.88-2.5-5.34-2.39-7.16.17a14.97 14.97 0 0 0-2.46 6.38c-.48 2.95-.74 5.97-1.05 8.93-.21 2-.21 4.04-.55 6a6.84 6.84 0 0 1-1.45 2.83c-1.95 2.48-5.36 2.23-7.73.75-3.04-1.89-4.63-4.83-5.58-8.14A83.27 83.27 0 0 1 7.2 53.11a124.51 124.51 0 0 0-5.82-26.68C-.66 20.2-.43 13.95 3.2 8.25 6.75 2.69 12.02.3 18.6.76c2.22.2 4.4.75 6.47 1.63 2.71 1.09 5.57 2.04 8.52 1.55a45.58 45.58 0 0 0 7.5-2.1c4.73-1.63 9.4-1.81 13.95.44 5.2 2.58 8.14 6.9 9.23 12.53.29 1.43.35 2.9.5 4.32Zm-60.14.12c0 2.26.39 4.5 1.14 6.64 3.04 8.97 5 18.27 5.86 27.7a72.56 72.56 0 0 0 2.72 14.07 9.32 9.32 0 0 0 2.71 4.26c.65.59 1.53 1.17 2.28.75.55-.32.77-1.28 1.09-1.97.05-.17.06-.34.04-.5.12-2.3.19-4.6.36-6.9.2-3.53.89-7.03 2.03-10.39.86-2.44 2-4.75 4.11-6.33a8.86 8.86 0 0 1 12.78 2.24c2.52 3.74 3.4 8.01 3.87 12.38.32 3 .36 6.05.57 9.08.05.65.24 1.28.54 1.86.16.3.78.65 1.05.54a6.51 6.51 0 0 0 2.25-1.2c1.74-1.63 2.39-3.8 2.95-6.03 1.42-5.56 2.12-11.24 2.71-16.93.62-5.23 1.63-10.41 3.03-15.5 1.03-3.87 2.3-7.7 3.16-11.62.74-3.34.23-6.83-1.43-9.82a11.6 11.6 0 0 0-9.68-6.35c-2.68-.22-5.16.45-7.6 1.41a25.44 25.44 0 0 1-7.84 1.85c-2.83.18-5.51-.46-8.18-1.29-2.4-.74-4.68-1.89-7.25-2.05-4.04-.26-7.43 1.04-10.07 4.11-2.32 2.68-3.2 5.93-3.2 9.99Z" />
            <path d="M22.4 11.86c0 2.13-1.43 3.13-3.1 2.67-.84-.25-1.7-.4-2.58-.43-1.47-.05-2.37.71-2.77 2.13-.4 1.43-1.14 2-2.39 1.96a2.17 2.17 0 0 1-2.06-2.56 7.4 7.4 0 0 1 7.37-6.15c1.43.11 2.85.4 4.21.84a1.78 1.78 0 0 1 1.31 1.54Z" />
          </g>
        </svg>
      );
    case RequestType.OPTICAL:
      return (
        // prettier-ignore
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 117 45" className="w-16 h-16">
          <path fill="currentColor" d="M3.5 17c-2.2-.2-3.1-.7-3.1-2 0-1.2.8-1.7 3.3-2 .5-1.4.8-2.8 1.4-4.1 1.9-4.3 5.5-6.3 9.8-7C19 1.5 23.4 1 27.7 1l61.1.1c4.8 0 9.5 0 14.2 1.3 5.1 1.5 8.6 4.5 9.5 10a1.4 1.4 0 0 0 1 .8c1.8.2 2.5.7 2.5 1.9s-.8 2-2.6 2l-.7.1v6.6a20.8 20.8 0 0 1-19.1 20.9c-6 .7-11.9.2-17-3.4-4.4-3-7-7.1-9-11.9-.8-1.8-1.3-3.7-2-5.6-.8-2.7-2.2-3.8-5-3.8h-5c-2.6 0-4.2 1.2-5 3.9-1.2 4.4-3 8.6-5.7 12.3a20 20 0 0 1-14.2 8.4c-6.3.8-12.5 0-18-3.7A19.3 19.3 0 0 1 4 26.3c-.4-3-.3-6.2-.4-9.3ZM109 20.2c-2 .9-4 1.3-5.7 2.4a13 13 0 0 0-3.7 4c-1.4 2-2.5 4.3-3.9 6.5-.7 1.1-1.8 1.4-2.7.8-1-.5-1-1.6-.5-2.7l.5-.9 3.7-6.4c2-3.5 5-5.6 8.6-6.6l3.8-.8v-2c-.6-4.5-2-7.2-6.6-8.3A73.6 73.6 0 0 0 75 5.8c-4.8 1-7 3.4-6.7 8.3A36 36 0 0 0 75 34.4c2.1 2.9 5.1 5 8.6 5.8 3.2.9 6.5 1 9.8.6 10.9-1.6 16.6-9.3 15.6-20.6Zm-101.5 0-.3.8.1 3.2c.5 6.5 3.3 11.5 9.1 14.5 4.5 2.2 9.2 2.6 14 2 4.5-.6 8.2-2.6 10.9-6.2 4.8-6.3 7-13.6 7.1-21.5 0-3-1.5-5.2-4.3-6.3-1-.4-2-.7-3.2-.9a87 87 0 0 0-24.5-.2c-6.4.7-9.4 4.5-9 10.8l2.2.5a14 14 0 0 1 9.7 6.2c1.6 2.5 3 5.2 4.5 7.8.7 1.2.6 2.3-.4 3-1 .6-2 .2-2.8-1-1.4-2.4-2.7-4.9-4.3-7.1-.8-1.3-1.9-2.4-3.1-3.3-1.8-1-3.8-1.5-5.7-2.3ZM48.7 4.8C52.3 8 52.5 12 52.3 16h12c-.2-4.2.3-8 3.8-11.2H48.7Z" />
        </svg>
      );
    case RequestType.MATERNITY:
      return (
        // prettier-ignore
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 97 85" className="w-14 h-14">
          <path fill="currentColor" d="M48.3 13.3c1.6-2 3-3.9 4.7-5.6A25.3 25.3 0 0 1 93.7 15a27 27 0 0 1 1.7 17.7A63 63 0 0 1 80 61.3a112.3 112.3 0 0 1-31.6 23.4c-.4 0-.7 0-1-.3-11.3-6-21.6-13.3-30.2-22.7A65.3 65.3 0 0 1 3 38.6 34 34 0 0 1 .9 23.2C2 14 6.7 7.2 15 3a24.6 24.6 0 0 1 23.4.5c3.6 1.9 6.6 4.7 8.8 8l1 1.7Zm-22-9.5h-2.5A21.8 21.8 0 0 0 5 19.4c-1.8 5.6-1 11 .7 16.5a61.4 61.4 0 0 0 15.2 25A108.3 108.3 0 0 0 48.4 81h.6c9.2-5.2 18-11 25.4-18.6 8-8 14.3-17 17.3-28a27 27 0 0 0 .7-13.8A22 22 0 0 0 73.7 4c-8.3-1-15.2 1.8-20.5 8.3C52 14 51.1 16 50 17.7c-.4.5-1 1-1.6 1.1-.5 0-1.1-.6-1.5-1l-.8-1.8A22 22 0 0 0 26.3 3.8Z" />
          <path fill="currentColor" d="m38 41.6 2.7-2-.1-.1c-1.4-1-2-2.5-2-4.2l-.3-1.8c0-.5-.3-1-.7-1.5a6.4 6.4 0 1 1 10.5-2.4c-.2.5-.3 1.1-.2 1.7 0 1 .6 1.7 1.6 2.1l4.4 2.2.3-3.2a5.8 5.8 0 0 1 3-4.1 1.5 1.5 0 0 0 1-1.3 13 13 0 0 1 4.5-8.6c2.4-1.9 5.2-2.5 8.2-2.3a14 14 0 0 1 12.5 7.2c1.7 3 2 6.4 1.7 9.9a12 12 0 0 1-2.5 7 11.7 11.7 0 0 1-5.5 3.5L73 45a8 8 0 0 0-5.3 5.8c-1.7 6.8-6.3 10.8-12.5 13.3a26.3 26.3 0 0 1-17.9 1.3c-5.7-1.6-8.8-5.5-9.8-11.7a17 17 0 0 1 0-3.5c0-.7-.4-.8-.9-.9a249 249 0 0 1-5-.8 4.7 4.7 0 0 1-4-3.7 291 291 0 0 1-1.8-7.1 5 5 0 0 1 3.3-6.1 5 5 0 0 1 6.2 3c.2.5.4.8 1 .8 5 0 9.1 1.5 11.6 6.2Zm26-20-.6.6a12 12 0 0 0-2.2 6.5 2 2 0 0 1-1.6 2c-2.5 1-3.4 5.3-1.5 7.3 2.2 2.1 2.3 5.1 1.3 7.6a1.4 1.4 0 0 1-2 .9 1.5 1.5 0 0 1-1-1.8c0-.5.3-1 .4-1.4.3-1.7-.3-2.8-1.8-3.6l-6.2-3.1c-4-2-3-.9-4.3-5.3a2.8 2.8 0 0 1 .3-2.3 2.9 2.9 0 0 0-.1-3.2 3 3 0 0 0-3.2-1.4 3 3 0 0 0-2.5 2.2 3 3 0 0 0 1.1 3.4c.8.5 1.3 1.2 1.3 2.2 0 .4 0 .9.2 1.3.2 1 .1 2.2.6 3a11 11 0 0 0 3 2.7c2.1 1.5 4.5 2.8 6.7 4.1 1.2.7 1.6 1.5 1.1 2.4-.5 1-1.3 1.1-2.6.4a122 122 0 0 1-4.7-2.7c-2.6-1.6-2.9-1.6-5.3.4l-.7.7c-.4.6-.7 1.2-.8 2V51c0 1.2-.6 2-1.6 2s-1.6-.8-1.6-2v-5.2a6.7 6.7 0 0 0-2.3-5c-2.2-2-5-2.3-7.8-2.2-2 0-2.2 0-2.8-2l-.2-.5c-.4-1.2-1.3-1.8-2.2-1.5-1.1.3-1.6 1.2-1.3 2.5l1.6 6.6a2 2 0 0 0 1.9 1.8l6.7 1c1.3.3 1.7.9 1.6 2.2 0 1.6-.3 3.3 0 4.9.9 5 3.2 7.6 7.7 8.9 5.8 1.5 11.3.6 16.6-1.8 4.8-2.1 8.3-5.4 9.5-10.7a11 11 0 0 1 2.8-5 1.4 1.4 0 0 0 .3-1.1l-1-3.3c-.5-2.4 0-4 1.6-5 2.8-1.7 2.9-3 .3-5h-.1a8 8 0 0 1-3.4-4.4c-.5-1.5-.8-3-1.2-4.6Zm6.6 21 6.8-2.5a6.4 6.4 0 0 0 4.1-4.9l.4-2c.3-3.7 0-7.3-2.6-10.2-3-3.4-7-4.2-11.4-3.5a1.1 1.1 0 0 0-.5.9c.1 3.2.6 6.3 3.8 8.2l.4.4c3 2.7 2.8 6.3-.4 8.5-1.4 1-1.5 2-1 3.3 0 .5.2 1.1.4 1.7Z" />
        </svg>
      );
    case RequestType.INPATIENT:
      return (
        // prettier-ignore
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 94 70" className="w-12 h-12">
          <path fill="currentColor" d="M4.4 52.1v14.3c0 1.6-1 2.7-2.3 2.6-1.3 0-2-1-2-2.6V12.1c0-.7.2-1.4.7-2 1-1.2 3-.6 3.4 1 .1.4.2.8.1 1.3v9.8h23.2c5.4 0 9.5 3.2 10.6 8.4.3 1.4.3 2.8.4 4.4h52.3c2.3 0 3 .6 3 3v28.4c0 1.5-.5 2.2-1.7 2.5a2 2 0 0 1-2.5-1.4l-.1-1.4V52H4.4Zm85-12.7h-85v8.3h85v-8.3Zm-85-4.5h29.9c-.2-1.3-.1-2.6-.4-3.9-.8-2.8-3.2-4.5-6.5-4.5H4.5V35Zm68.1-21.2v5.8c0 .9-.4 1.7-1 2.3-1.2 1-3.1.1-3.2-1.5v-6.6H62c-1 0-1.9-.3-2.2-1.4-.5-1.5.4-2.8 2.2-2.9h6.2v-6c0-.7.3-1.4.8-2 1.4-1.1 3.3-.1 3.4 1.8v6.2h5c.6 0 1.2 0 1.9.2a2.1 2.1 0 0 1 1.6 2.1 2 2 0 0 1-2 2h-6.5Z" />
        </svg>
      );
    case RequestType.OUTPATIENT:
      return (
        // prettier-ignore
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 82 85" className="w-12 h-12">
          <path fill="currentColor" d="M9.8 9.6c-2.7 1.1-5 2.5-5.5 5.6a58.1 58.1 0 0 0 5.6 21.5c2 3.8 4.4 7 8 9.4l.3.1c3-4 8.2-3.3 10.4 0 2.6-1.4 4.6-3.5 6.2-5.8a44.3 44.3 0 0 0 6.6-16 74 74 0 0 0 1.3-7.4c.4-3.7-1.7-5.7-4.6-6.9l-.8.4a6.1 6.1 0 0 1-8 1.3 6.2 6.2 0 1 1 9.4-6c0 .7.4 1 .9 1.1 1.8.8 3.5 2 4.7 3.6 1.8 2.3 2.2 5 1.8 7.7a69.5 69.5 0 0 1-3.7 15.1 36.4 36.4 0 0 1-7 12c-1.5 1.7-3.2 3-5.2 4.2-.3.1-.5.4-.6.8a6.4 6.4 0 0 1-4.4 5.2v14.7c0 4 1.5 7.2 4.9 9.3A10.5 10.5 0 0 0 46 73l.3-3V53.7c0-3.1.7-6 2.5-8.6 2.5-3.6 5.9-5.6 10.2-6 5.7-.5 10.1 1.8 13.2 6.5 1.6 2.4 2.2 5.2 2.2 8V63a.8.8 0 0 0 .7 1 8.8 8.8 0 0 1 6.3 9 9 9 0 0 1-7.7 8c-3.2.5-7-1.2-8.6-4.1a8.5 8.5 0 0 1 .4-9.6c1.2-1.8 3.1-3 5.3-3.5v-.2c0-4 .3-8-.1-12a10.4 10.4 0 0 0-15.8-7.5 10.3 10.3 0 0 0-5.1 9.4V71c0 3.8-1.6 7-4.3 9.6A14 14 0 0 1 21.7 70V56.4c0-.6 0-1-.7-1.2a6 6 0 0 1-3.6-4.7 2 2 0 0 0-.6-1A26.2 26.2 0 0 1 6.6 38.2a59.6 59.6 0 0 1-5.4-17.6C.9 18.7.4 16.9.7 15c.5-3 2-5.2 4.5-6.8C6 7.5 7 7 8 6.6a1.7 1.7 0 0 0 1.2-1.4A6 6 0 0 1 14.4.5a6 6 0 0 1 6 3 6.2 6.2 0 0 1-1.8 8.2 6.2 6.2 0 0 1-8.4-1.4l-.4-.7Zm62.8 68a5.2 5.2 0 0 0 4.9-7.2 5.3 5.3 0 0 0-5-3.3 5.4 5.4 0 0 0-5.1 5.3 5.3 5.3 0 0 0 5.2 5.3Zm-40-68.4a2.6 2.6 0 0 0 2-4.4 3 3 0 0 0-2-.8A2.7 2.7 0 0 0 30 6.6a2.6 2.6 0 0 0 2.6 2.6ZM17.7 6.6a2.6 2.6 0 1 0-5.2 0A2.6 2.6 0 0 0 15 9.2a2.6 2.6 0 0 0 2.6-2.6Zm5.8 45.6a2.6 2.6 0 1 0 0-5.2 2.6 2.6 0 0 0-1 5l1 .2Z" />
        </svg>
      );
    default:
      return (
        <>
          {/* prettier-ignore */}
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-12 h-12">
            <path strokeLinecap="round" strokeLinejoin="round" d="M20.25 7.5l-.625 10.632a2.25 2.25 0 01-2.247 2.118H6.622a2.25 2.25 0 01-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125z" />
          </svg>
        </>
      );
  }
}
