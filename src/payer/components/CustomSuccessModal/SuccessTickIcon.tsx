import React from "react";

type Props = {
  height?: number;
  width?: number;
};

export default function SuccessTickIcon({ height, width }: Props) {
  return (
    <svg
      width={width ?? "206"}
      height={height ?? "207"}
      viewBox="0 0 206 207"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M69.3926 103.259L91.7975 125.664L136.607 80.8539M56.3417 21.4472C64.3773 20.8059 72.0059 17.6462 78.1417 12.418C85.0751 6.50614 93.8884 3.25879 103 3.25879C112.112 3.25879 120.925 6.50614 127.858 12.418C133.994 17.6462 141.623 20.8059 149.658 21.4472C158.741 22.1698 167.268 26.1055 173.711 32.548C180.153 38.9905 184.089 47.5181 184.812 56.6005C185.453 64.6361 188.613 72.2647 193.841 78.4005C199.753 85.3339 203 94.1472 203 103.259C203 112.37 199.753 121.184 193.841 128.117C188.613 134.253 185.453 141.881 184.812 149.917C184.089 158.999 180.153 167.527 173.711 173.97C167.268 180.412 158.741 184.348 149.658 185.07C141.623 185.712 133.994 188.871 127.858 194.1C120.925 200.011 112.112 203.259 103 203.259C93.8884 203.259 85.0751 200.011 78.1417 194.1C72.0059 188.871 64.3773 185.712 56.3417 185.07C47.2593 184.348 38.7317 180.412 32.2892 173.97C25.8467 167.527 21.911 158.999 21.1884 149.917C20.5471 141.881 17.3874 134.253 12.1592 128.117C6.24735 121.184 3 112.37 3 103.259C3 94.1472 6.24735 85.3339 12.1592 78.4005C17.3874 72.2647 20.5471 64.6361 21.1884 56.6005C21.911 47.5181 25.8467 38.9905 32.2892 32.548C38.7317 26.1055 47.2593 22.1698 56.3417 21.4472Z"
        stroke="white"
        strokeWidth="6"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
