import { useEffect } from "react";
import AnimatedTick from "./AnimatedTick";
import styles from "./CustomSuccessModal.module.css";
import XIcon from "./XICon";

type Props = {
  isOpen: boolean;
  setIsOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  title: string;
  description: string;
  isShowCloseButton?: boolean;
};

export default function SuccessModal({
  isOpen,
  title,
  description,
  setIsOpen = () => null,
  isShowCloseButton = false,
}: Props) {
  useEffect(() => {
    const dialog: HTMLDialogElement | null = document.querySelector("#success-dialog");

    if (isOpen) {
      dialog?.showModal();
    } else {
      dialog?.close();
    }
  }, [isOpen]);

  return isOpen ? (
    <div
      className={`fixed inset-0 z-20 flex items-center justify-center overflow-y-auto bg-black/20 ${styles["back-drop"]}`}
    >
      {/* ----------------- */}

      <section
        className={`relative w-[800px] border border-slate-200 bg-white p-12 shadow-md shadow-slate-500 ${styles["modal"]}`}
      >
        {isShowCloseButton && (
          <button className="absolute right-4 top-4" onClick={() => setIsOpen(false)}>
            <XIcon />
          </button>
        )}

        <AnimatedTick />

        <div className="text-center">
          <p className="mt-4 text-4xl font-bold text-slate-800">{title}</p>

          <p className="mt-4 text-xl font-medium text-slate-600">{description}</p>
        </div>
      </section>
    </div>
  ) : null;
}
