import React from "react";

type Props = {
  width?: number;
  height?: number;
};

export default function XIcon({ height, width }: Props) {
  return (
    <svg
      width={width ?? "30"}
      height={height ?? "31"}
      viewBox="0 0 30 31"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8 24.0088L6.25 22.2588L13.25 15.2588L6.25 8.25879L8 6.50879L15 13.5088L22 6.50879L23.75 8.25879L16.75 15.2588L23.75 22.2588L22 24.0088L15 17.0088L8 24.0088Z"
        fill="#1A2853"
      />
    </svg>
  );
}
