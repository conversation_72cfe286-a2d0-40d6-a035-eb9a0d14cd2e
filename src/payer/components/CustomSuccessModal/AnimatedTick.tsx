import styles from "./CustomSuccessModal.module.css";
import SuccessTickIcon from "./SuccessTickIcon";

export default function AnimatedTick() {
  return (
    <div className="relative h-[320px]  ">
      {/* --------------- */}

      <div
        className={`absolute right-1/2 top-1/2 h-[250px] w-[250px]  rounded-full bg-[rgba(68,158,218,0.2)] ${styles["circle-one"]}`}
      ></div>

      <div
        className={`absolute right-1/2 top-1/2 h-[200px] w-[200px]  rounded-full bg-[rgba(68,158,218,0.2)] ${styles["circle-two"]}`}
      ></div>

      <div
        className={`absolute right-1/2 top-1/2 h-[200px] w-[200px]  rounded-full bg-lightBlue ${styles["circle-three"]}`}
      ></div>

      <div className="absolute right-1/2 top-1/2 h-fit w-fit -translate-y-1/2 translate-x-1/2 ">
        <SuccessTickIcon height={120} width={120} />
      </div>

      {/* --------------- */}
    </div>
  );
}
