import moment from "moment";
import React, { useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { formatValue } from "../lib/Utils";
import { RootState } from "../store";

////import Image from "../../images/transactions-image-04.svg";

function PreAuthTransactionPanel({
  preauthtransactionPanelOpen,
  setPreauthTransactionPanelOpen,
}) {
  const closeBtn = useRef(null);
  const dispatch = useDispatch();
  const panelContent = useRef(null);
  const preAuthItem: any = useSelector(
    (state: RootState) => state.memberInfo.preAuthItem
  );

  // const lineItemsList: any = useSelector(
  //   (state: RootState) => state.dashboard.lineItemsList
  // );
  // const diagnosisItemsList: any = useSelector(
  //   (state: RootState) => state.dashboard.diagnosisList
  // );
  // const loadingData = useSelector(
  //   (state: RootState) => state.dashboard.loadingData
  // );

  //log(visitHistoryItem);

  // useEffect(() => {
  //   if (visitHistoryItem?.visit?.id) {
  //     if (visitHistoryItem?.visit?.id !== undefined) {
  //       dispatch(getDiagnosisItems(visitHistoryItem?.visit?.id));
  //       dispatch(getLineItems(visitHistoryItem?.visit?.id));
  //     }
  //   }
  // }, [visitHistoryItem]);

  //close on click outside
  // useEffect(() => {
  //   const clickHandler = ({ target }) => {
  //     setPreauthTransactionPanelOpen(false);
  //   };
  //   document.addEventListener("click", clickHandler);
  //   return () => document.removeEventListener("click", clickHandler);
  // });

  // close if the esc key is pressed
  useEffect(() => {
    const keyHandler = ({ keyCode }) => {
      if (!preauthtransactionPanelOpen || keyCode !== 27) return;
      setPreauthTransactionPanelOpen(false);
    };
    document.addEventListener("keydown", keyHandler);
    return () => document.removeEventListener("keydown", keyHandler);
  });
  // if (visitHistoryItem.visit === null || visitHistoryItem.visit === undefined) {
  //   return <div></div>;
  // }
  console.log(preAuthItem);
  return (
    <div
      ref={panelContent}
      className={`absolute bg-white inset-0 sm:left-auto z-20 transform shadow-xl transition-transform duration-200 mt-10 ease-in-out ${
        preauthtransactionPanelOpen ? "translate-x-" : "translate-x-full"
      }`}
    >
      <div className="sticky top-16 bg-slate-50 overflow-x-hidden overflow-y-auto no-scrollbar shrink-0 border-l border-slate-200 w-full sm:w-[390px] h-[calc(100vh-64px)]">
        <button
          ref={closeBtn}
          onClick={() => setPreauthTransactionPanelOpen(false)}
          className="absolute top-0 right-0 mt-6 mr-6 group p-2"
        >
          <svg
            className="w-4 h-4 fill-slate-400 group-hover:fill-slate-600 pointer-events-none"
            viewBox="0 0 16 16"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="m7.95 6.536 4.242-4.243a1 1 0 1 1 1.415 1.414L9.364 7.95l4.243 4.242a1 1 0 1 1-1.415 1.415L7.95 9.364l-4.243 4.243a1 1 0 0 1-1.414-1.415L6.536 7.95 2.293 3.707a1 1 0 0 1 1.414-1.414L7.95 6.536Z" />
          </svg>
        </button>
        <div className="py-8 px-4 lg:px-8 bg-white">
          <div className="max-w-sm mx-auto lg:max-w-none bg-white">
            <div className="text-slate-800 font-semibold text-center mb-1">
              Pre-Authorization History
            </div>
            <div className="text-sm text-center italic">
              Created On :
              {moment(preAuthItem?.visit?.createdAt)
                .add(3, "hours")
                .format("LLL")}
            </div>
            {/* Details */}
            <div className="drop-shadow-lg mt-12">
              {/* Top */}
              <div className="bg-white rounded-t-xl px-5 pb-2.5 text-center">
                <div className="mb-3 text-center">
                  Member Number: {preAuthItem.visit?.memberNumber}
                </div>
                <div className="mb-3 text-center">
                  Request Amount:{formatValue(preAuthItem.visit?.requestAmount)}
                </div>
                <div className="mb-3 text-center">
                  Authorized Amount:
                  {formatValue(preAuthItem.visit?.authorizedAmount)}
                </div>

                <div className="text-xs  font-bold bg-slate-100 text-slate-500 rounded-full text-center px-2.5 py-1">
                  Status: {preAuthItem.visit?.status}
                </div>
                <div className="text-xs  font-medium bg-slate-100 text-slate-500 rounded-full text-center px-2.5 py-1">
                  Scheme: {preAuthItem.visit?.schemeName}
                </div>
                <div className="text-xs  font-medium bg-slate-100 text-slate-500 rounded-full text-center px-2.5 py-1">
                  Payer: {preAuthItem.visit?.payerName}
                </div>
              </div>
              {/* Divider */}
              <div
                className="flex justify-between items-center"
                aria-hidden="true"
              >
                <div className="grow w-full h-5 bg-white flex flex-col justify-center">
                  <div className="h-px w-full border-t border-solid border-slate-200" />
                </div>
              </div>
              {/* Bottom */}
              <div className="bg-white rounded-b-xl p-5 pt-2.5 text-sm space-y-3">
                <div className="flex justify-between space-x-1">
                  <span className="italic">Benefit:</span>
                  <span className="font-medium text-slate-700 text-right">
                    {preAuthItem.visit?.benefitName}
                  </span>
                </div>
                <div className="flex justify-between space-x-1">
                  <span className="italic">Request Type:</span>
                  <span className="font-medium text-slate-700 text-right">
                    {preAuthItem.visit?.requestType}
                  </span>
                </div>
                <div className="flex justify-between space-x-1">
                  <span className="italic">Service:</span>
                  <span className="font-medium text-slate-700 text-right">
                    {preAuthItem.visit?.service}
                  </span>
                </div>
              </div>
              {/* Divider */}
              <div
                className="flex justify-between items-center"
                aria-hidden="true"
              >
                <div className="grow w-full h-5 bg-white flex flex-col justify-center">
                  <div className="h-px w-full border-t border-solid border-slate-200" />
                </div>
              </div>
              {/*  */}
            </div>
            {/* Receipts */}
            <div className="mt-2">
              <div className="text-xs flex-inline font-medium bg-slate-100 text-slate-500 rounded-full text-center px-2.5 py-1">
                Medical Procedure Code: {preAuthItem.visit?.medProcedure}
              </div>
              <div className="text-xs flex-inline font-medium bg-slate-100 text-slate-500 rounded-full text-center px-2.5 py-1">
                Diagnosis Code: {preAuthItem.visit?.diagnosis}
              </div>
            </div>
            {/* Notes */}
            <div className="mt-6 bg-white">
              <div className="text-sm font-semibold text-slate-800 mb-2">
                Notes
              </div>
              <form className="rounded bg-slate-100 border border-dashed border-slate-300 text-center px-5 py-8">
                {preAuthItem.visit?.notes}
              </form>
              <div className="text-sm font-semibold text-slate-800 mb-2">
                Authorization Notes
              </div>
              <form className="rounded bg-slate-100 border border-dashed border-slate-300 text-center px-5 py-8">
                {preAuthItem.visit?.authorizationNotes}
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PreAuthTransactionPanel;
