import { Menu, Transition } from "@headlessui/react";
import { CSSProperties, ReactNode } from "react";

type Props = {
  optionsContainerClass?: string;
  buttonClass?: string;
  buttonChild: ReactNode;
  options: ReactNode[];
  containerStyle?: CSSProperties;
};

/**
 * A dropdown menu component built with Headless UI's `Menu` and `Transition` components.
 * This component renders a button that, when clicked, shows a list of options inside a dropdown.
 *
 * **Important:**
 * The `buttonChild` prop must be content that does not contain
 * another button component. Assigning buttons, nesting buttons or components with buttons inside `buttonChild`
 * will cause accessibility and usability issues.
 *
 * @component
 *
 * @param {Object} props - Component properties.
 * @param {string} [props.containerClass] - CSS class for the outer container.
 * @param {string} [props.optionsContainerClass] - CSS class for the dropdown options container.
 * @param {string} [props.buttonClass] - CSS class for the button element.
 * @param {ReactNode} props.buttonChild - A child element for the button. Must be content without a button or nested buttons.
 * @param {ReactNode[]} props.options - An array of React nodes representing the dropdown options.
 *
 * @example
 * ```jsx
 * <MenuDropdown
 *   buttonChild={<span>Open Menu</span>}
 *   options={[
 *     <div key="1">Option 1</div>,
 *     <div key="2">Option 2</div>,
 *   ]}
 *   buttonClass="btn-class"
 *   containerClass="menu-container"
 *   optionsContainerClass="options-container"
 * />
 * ```
 */

export default function MenuDropdown({
  buttonChild,
  options,
  buttonClass,
  optionsContainerClass,
  containerStyle,
}: Props) {
  const defaultContainerStyle: CSSProperties = {
    position: "relative",
    maxWidth: "fit-content",
  };
  const derivedContainerStyle: CSSProperties = { ...defaultContainerStyle, ...containerStyle };

  return (
    <Menu as={`div`} style={derivedContainerStyle}>
      <Menu.Button className={buttonClass}>{buttonChild} </Menu.Button>

      <Transition
        style={{
          top: "100%",
          right: 0,
        }}
        className={`absolute z-10 mt-2 min-w-fit  whitespace-nowrap rounded-lg  border bg-white p-4 text-xs font-medium shadow-md`}
        enter="transition duration-100 ease-out"
        enterFrom="transform scale-95 opacity-0"
        enterTo="transform scale-100 opacity-100"
        leave="transition duration-75 ease-out"
        leaveFrom="transform scale-100 opacity-100"
        leaveTo="transform scale-95 opacity-0"
      >
        <Menu.Items className={`flex  flex-col gap-4 ${optionsContainerClass}`}>
          {options.map((option, i) => (
            <Menu.Item key={i}>{option}</Menu.Item>
          ))}
        </Menu.Items>
      </Transition>
    </Menu>
  );
}
