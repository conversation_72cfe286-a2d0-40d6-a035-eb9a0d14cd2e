import { fireEvent, render, screen } from "@testing-library/react";
import DateSelect from "./DateSelect";

describe("DateSelect Component", () => {
  it("should render the button with the selected period", () => {
    render(<DateSelect />);

    // Check that the button initially displays "Last Month" (default selected)
    expect(screen.getByRole("button")).toHaveTextContent("Last Month");
  });

  it("should open the dropdown when the button is clicked", () => {
    render(<DateSelect />);

    // Click the button to open the dropdown
    fireEvent.click(screen.getByRole("button"));

    // Check that the dropdown is displayed
    expect(screen.getByText("Today")).toBeInTheDocument();
  });

  it("should select an option when clicked", () => {
    render(<DateSelect />);

    // Open the dropdown
    fireEvent.click(screen.getByRole("button"));

    // Click on the "Last 7 Days" option
    fireEvent.click(screen.getByText("Last 7 Days"));
  });
});
