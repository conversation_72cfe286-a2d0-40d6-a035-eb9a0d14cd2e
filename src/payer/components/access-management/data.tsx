import { AccessModuleDetail, AccessModuleName } from "./AccessModuleCard";
import {
  BanknotesIcon,
  ClipboardDocumentListIcon,
  DocumentTextIcon,
  HeartIcon,
  HomeModernIcon,
  ScaleIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";

export const ALL_SCHEME_ROLES = ["SCHEME_OVERVIEW_ROLE"];

export type CompositeRole =
  | "SCHEME_OVERVIEW_ROLE"
  | "ALL_MEMBERSHIP_ROLE"
  | "ALL_PROVIDERS_ROLE"
  | "ALL_CARE_ROLE"
  | "ALL_CLAIMS_ROLE"
  | "ALL_CLAIMS_ADJUDICATION_ROLE"
  | "ALL_FINANCE_AND_ACCOUNTING_ROLE";

export const ALL_MODULES_ALL_ROLES_MAPPING: Record<AccessModuleName, CompositeRole> = {
  "Schemes Module": "SCHEME_OVERVIEW_ROLE",
  "Membership Module": "ALL_MEMBERSHIP_ROLE",
  "Provider Module": "ALL_PROVIDERS_ROLE",
  "Care Module": "ALL_CARE_ROLE",
  "Claims Module": "ALL_CLAIMS_ROLE",
  "Claims Adjudication Module": "ALL_CLAIMS_ADJUDICATION_ROLE",
  "Finance & Accounting Module": "ALL_FINANCE_AND_ACCOUNTING_ROLE",
};

export const ALL_MEMBERSHIP_ROLES = [
  "MEMBERSHIP_ACTIVATE_ROLE",
  "MEMBERSHIP_DEACTIVATE_ROLE",
  "MEMBERSHIP_SUSPEND_ROLE",
  "MEMBERSHIP_BENEFIT_SUSPEND_ROLE",
  "MEMBERSHIP_BENEFIT_TOPUP_ROLE",
  "MEMBERSHIP_BENEFIT_TRANSFER_ROLE",
  "MEMBERSHIP_EDIT_ROLE",
  "MEMBERSHIP_MASS_EDIT_ROLE",
  "MEMBERSHIP_DETACH_BIOMETRICS_ROLE",
  "MEMBERSHIP_INQUIRY_ROLE",
  "MEMBERSHIP_MASS_ACTIVATION_ROLE",
  "MEMBERSHIP_MASS_DEACTIVATION_ROLE",
  "MEMBERSHIP_REPORTS_ROLE",
  "MEMBERSHIP_ROLE",
];

export const ALL_CARE_ROLES = [
  "CARE_PREAUTH_REVIEW_ROLE",
  "CARE_ROLE",
  "CARE_REPORTS_RETRIEVAL_ROLE",
  "CARE_VISIT_REACTIVATION_ROLE",
  "CARE_PREAUTH_VIEW_ALL_ROLE",
  "CARE_PREAUTH_VIEW_DENTAL_ROLE",
  "CARE_PREAUTH_VIEW_OPTICAL_ROLE",
  "CARE_PREAUTH_VIEW_OUTPATIENT_ROLE",
  "CARE_PREAUTH_VIEW_INPATIENT_ROLE",
  "CARE_PREAUTH_VIEW_MATERNITY_ROLE",
  "CARE_PREAUTH_APPROVE_ALL_ROLE",
  "CARE_PREAUTH_APPROVE_DENTAL_ROLE",
  "CARE_PREAUTH_APPROVE_OPTICAL_ROLE",
  "CARE_PREAUTH_APPROVE_OUTPATIENT_ROLE",
  "CARE_PREAUTH_APPROVE_INPATIENT_ROLE",
  "CARE_PREAUTH_APPROVE_MATERNITY_ROLE",
  "CARE_PREAUTH_TOPUP_ALL_ROLE",
  "CARE_PREAUTH_TOPUP_DENTAL_ROLE",
  "CARE_PREAUTH_TOPUP_OPTICAL_ROLE",
  "CARE_PREAUTH_TOPUP_OUTPATIENT_ROLE",
  "CARE_PREAUTH_TOPUP_INPATIENT_ROLE",
  "CARE_PREAUTH_TOPUP_MATERNITY_ROLE",
  "CARE_PREAUTH_GET_DIAGNOSIS_REPORTS_ROLE",
];

export const ALL_CLAIMS_ROLES = [
  "CLAIMS_ROLE",
  "CLAIMS_BATCHING_ROLE",
  "CLAIMS_VETTING_ROLE",
  "CLAIMS_REVERSAL_ROLE",
  "CLAIMS_REPORTS_ROLE",
  "CLAIMS_OFFLCT_ROLE",
  "CLAIMS_REIMBURSEMENT_ROLE",
];

export const ALL_CLAIMS_ADJUDICATION_ROLES = [
  "CLAIMS_ADJUDICATION_ROLE",
  "CLAIMS_ADJUDICATION_BATCHING_ROLE",
  "CLAIMS_ADJUDICATION_VETTING_ROLE",
  "CLAIMS_ADJUDICATION_AI_ROLE",
];

export const ALL_FINANCE_AND_ACCOUNTING_ROLES = [
  "FINANCE_AND_ACCOUNTING_ROLE",
  "FINANCE_AND_ACCOUNTING_VOUCHERING_ROLE",
  "FINANCE_AND_ACCOUNTING_SETTLEMENT_ROLE",
  "FINANCE_AND_ACCOUNTING_REMITTANCE_ROLE",
  "FINANCE_AND_ACCOUNTING_CREDIT_ANALYSIS_ROLE",
];

export const ALL_PROVIDERS_ROLES = [
  "PROVIDER_ROLE",
  "PROVIDER_VIEW_ROLE",
  "PROVIDER_RESTRICTIONS_ROLE",
];

export const accessModuleDetails: Array<AccessModuleDetail> = [
  {
    name: "Schemes Module",
    realmRoles: [
      {
        name: "Scheme overview role",
        description: "Can view all onboarded schemes under the payer.",
        roleNames: ALL_SCHEME_ROLES,
      },
    ],
  },
  {
    name: "Membership Module",
    realmRoles: [
      {
        name: "All membership roles",
        description: "Full access to all features and actions within the Membership module.",
        roleNames: ALL_MEMBERSHIP_ROLES,
      },
      {
        name: "Member Activation",
        description: "Can activate a member.",
        roleNames: ["MEMBERSHIP_ACTIVATE_ROLE"],
      },
      {
        name: "Member Deactivation",
        description: "Can deactivate a member.",
        roleNames: ["MEMBERSHIP_DEACTIVATE_ROLE"],
      },
      {
        name: "Member Suspension",
        description: "Can suspend a member.",
        roleNames: ["MEMBERSHIP_SUSPEND_ROLE"],
      },
      {
        name: "Benefit Topup role",
        description: "Can top up benefit amounts from one member to another",
        roleNames: ["MEMBERSHIP_BENEFIT_TOPUP_ROLE"],
      },
      {
        name: "Benefit Transfer role",
        description: "Can transfer benefit amounts from one member to another.",
        roleNames: ["MEMBERSHIP_BENEFIT_TRANSFER_ROLE"],
      },
      {
        name: "Benefit Suspend role",
        description: "Can suspend benefit.",
        roleNames: ["MEMBERSHIP_BENEFIT_SUSPEND_ROLE"],
      },
      {
        name: "Biometrics Detachment role",
        description: "Can detach a member's biometrics upon request.",
        roleNames: ["MEMBERSHIP_DETACH_BIOMETRICS_ROLE"],
      },
      {
        name: "Member Inquiry",
        description:
          "Can search for members, view their details, download both member and family statements.",
        roleNames: ["MEMBERSHIP_INQUIRY_ROLE"],
      },
      {
        name: "Member Editing",
        description: "Can edit and update a member's details.",
        roleNames: ["MEMBERSHIP_EDIT_ROLE"],
      },
      {
        name: "Mass Activation",
        description: "Can activate all or multiple members at once.",
        roleNames: ["MEMBERSHIP_MASS_ACTIVATION_ROLE"],
      },
      {
        name: "Mass Deactivation",
        description: "Can deactivate all or multiple members at once.",
        roleNames: ["MEMBERSHIP_MASS_DEACTIVATION_ROLE"],
      },
      {
        name: "Mass Edit",
        description: "Can edit all or multiple members at once.",
        roleNames: ["MEMBERSHIP_MASS_EDIT_ROLE"],
      },
      {
        name: "Reports",
        description: "Can download all membership reports.",
        roleNames: ["MEMBERSHIP_REPORTS_ROLE"],
      },
    ],
  },
  {
    name: "Provider Module",
    realmRoles: [
      {
        name: "All Provider role",
        description: "Full access to all the features and actions within the Provider module.",
        roleNames: ALL_PROVIDERS_ROLES,
      },
      {
        name: "Provider view role",
        description: "Can view a list of all onboarded providers that are mapped to the payer.",
        roleNames: ["PROVIDER_VIEW_ROLE"],
      },
      {
        name: "Provider Restriction role",
        description: "Can see all providers that have restrictions.",
        roleNames: ["PROVIDER_RESTRICTIONS_ROLE"],
      },
    ],
  },
  {
    name: "Care Module",
    realmRoles: [
      {
        name: "All Care roles",
        description: "Full access to all features and actions within the Care module.",
        roleNames: ALL_CARE_ROLES,
      },
      {
        name: "Preauth review role",
        description: "Can review pre-authorization details and approve / decline the request.",
        roleNames: ["CARE_PREAUTH_REVIEW_ROLE"],
      },
      {
        name: "Visit reactivation role",
        description: "Can reactivate a visit.",
        roleNames: ["CARE_VISIT_REACTIVATION_ROLE"],
      },
      {
        name: "Reports retrieval role",
        description: "Can download scheme and provider reports.",
        roleNames: ["CARE_REPORTS_RETRIEVAL_ROLE"],
      },
      {
        name: "Preauth view all role",
        description: "Can view all pre-authorizations.",
        roleNames: ["CARE_PREAUTH_VIEW_ALL_ROLE"],
      },
      {
        name: "Preauth view dental role",
        description: "Can view dental pre-authorizations.",
        roleNames: ["CARE_PREAUTH_VIEW_DENTAL_ROLE"],
      },
      {
        name: "Preauth view optical role",
        description: "Can view optical pre-authorizations.",
        roleNames: ["CARE_PREAUTH_VIEW_OPTICAL_ROLE"],
      },
      {
        name: "Preauth view outpatient role",
        description: "Can view outpatient pre-authorizations.",
        roleNames: ["CARE_PREAUTH_VIEW_OUTPATIENT_ROLE"],
      },
      {
        name: "Preauth view inpatient role",
        description: "Can view inpatient pre-authorizations.",
        roleNames: ["CARE_PREAUTH_VIEW_INPATIENT_ROLE"],
      },
      {
        name: "Preauth view maternity role",
        description: "Can view maternity pre-authorizations.",
        roleNames: ["CARE_PREAUTH_VIEW_MATERNITY_ROLE"],
      },
      {
        name: "Preauth approve all role",
        description: "Can approve all pre-authorizations.",
        roleNames: ["CARE_PREAUTH_APPROVE_ALL_ROLE"],
      },
      {
        name: "Preauth approve dental role",
        description: "Can approve dental pre-authorizations.",
        roleNames: ["CARE_PREAUTH_APPROVE_DENTAL_ROLE"],
      },
      {
        name: "Preauth approve optical role",
        description: "Can approve optical pre-authorizations.",
        roleNames: ["CARE_PREAUTH_APPROVE_OPTICAL_ROLE"],
      },
      {
        name: "Preauth approve outpatient role",
        description: "Can approve outpatient pre-authorizations.",
        roleNames: ["CARE_PREAUTH_APPROVE_OUTPATIENT_ROLE"],
      },
      {
        name: "Preauth approve inpatient role",
        description: "Can approve inpatient pre-authorizations.",
        roleNames: ["CARE_PREAUTH_APPROVE_INPATIENT_ROLE"],
      },
      {
        name: "Preauth approve maternity role",
        description: "Can approve maternity pre-authorizations.",
        roleNames: ["CARE_PREAUTH_APPROVE_MATERNITY_ROLE"],
      },
      {
        name: "Preauth topup all role",
        description: "Can topup all pre-authorizations.",
        roleNames: ["CARE_PREAUTH_TOPUP_ALL_ROLE"],
      },
      {
        name: "Preauth topup dental role",
        description: "Can topup dental pre-authorizations.",
        roleNames: ["CARE_PREAUTH_TOPUP_DENTAL_ROLE"],
      },
      {
        name: "Preauth topup optical role",
        description: "Can topup optical pre-authorizations.",
        roleNames: ["CARE_PREAUTH_TOPUP_OPTICAL_ROLE"],
      },
      {
        name: "Preauth topup outpatient role",
        description: "Can topup outpatient pre-authorizations.",
        roleNames: ["CARE_PREAUTH_TOPUP_OUTPATIENT_ROLE"],
      },
      {
        name: "Preauth topup inpatient role",
        description: "Can topup inpatient pre-authorizations.",
        roleNames: ["CARE_PREAUTH_TOPUP_INPATIENT_ROLE"],
      },
      {
        name: "Preauth topup maternity role",
        description: "Can topup maternity pre-authorizations.",
        roleNames: ["CARE_PREAUTH_TOPUP_MATERNITY_ROLE"],
      },
      {
        name: "Preauth get diagnosis reports role",
        description: "Can get diagnosis reports.",
        roleNames: ["CARE_PREAUTH_GET_DIAGNOSIS_REPORTS_ROLE"],
      },
      {
        name: "Preauth get care reports role",
        description: "Can get care reports.",
        roleNames: ["CARE_REPORTS_RETRIEVAL_ROLE"],
      },
      {
        name: "Preauth approve and view all role",
        description: "Can approve and view all pre-authorizations.",
        roleNames: ["CARE_PREAUTH_APPROVE_AND_VIEW_ALL_ROLE"],
      },
      {
        name: "Preauth approve and view dental role",
        description: "Can approve and view dental pre-authorizations.",
        roleNames: ["CARE_PREAUTH_APPROVE_AND_VIEW_DENTAL_ROLE"],
      },
      {
        name: "Preauth approve and view optical role",
        description: "Can approve and view optical pre-authorizations.",
        roleNames: ["CARE_PREAUTH_APPROVE_AND_VIEW_OPTICAL_ROLE"],
      },
      {
        name: "Preauth approve and view outpatient role",
        description: "Can approve and view outpatient pre-authorizations.",
        roleNames: ["CARE_PREAUTH_APPROVE_AND_VIEW_OUTPATIENT_ROLE"],
      },
      {
        name: "Preauth approve and view inpatient role",
        description: "Can approve and view inpatient pre-authorizations.",
        roleNames: ["CARE_PREAUTH_APPROVE_AND_VIEW_INPATIENT_ROLE"],
      },
      {
        name: "Preauth approve and view maternity role",
        description: "Can approve and view maternity pre-authorizations.",
        roleNames: ["CARE_PREAUTH_APPROVE_AND_VIEW_MATERNITY_ROLE"],
      },
      {
        name: "Preauth topup and view all role",
        description: "Can topup and view all pre-authorizations.",
        roleNames: ["CARE_PREAUTH_TOPUP_AND_VIEW_ALL_ROLE"],
      },
      {
        name: "Preauth topup and view dental role",
        description: "Can topup and view dental pre-authorizations.",
        roleNames: ["CARE_PREAUTH_TOPUP_AND_VIEW_DENTAL_ROLE"],
      },
      {
        name: "Preauth topup and view optical role",
        description: "Can topup and view optical pre-authorizations.",
        roleNames: ["CARE_PREAUTH_TOPUP_AND_VIEW_OPTICAL_ROLE"],
      },
      {
        name: "Preauth topup and view outpatient role",
        description: "Can topup and view outpatient pre-authorizations.",
        roleNames: ["CARE_PREAUTH_TOPUP_AND_VIEW_OUTPATIENT_ROLE"],
      },
      {
        name: "Preauth topup and view inpatient role",
        description: "Can topup and view inpatient pre-authorizations.",
        roleNames: ["CARE_PREAUTH_TOPUP_AND_VIEW_INPATIENT_ROLE"],
      },
      {
        name: "Preauth topup and view maternity role",
        description: "Can topup and view maternity pre-authorizations.",
        roleNames: ["CARE_PREAUTH_TOPUP_AND_VIEW_MATERNITY_ROLE"],
      },
    ],
  },
  {
    name: "Claims Module",
    realmRoles: [
      {
        name: "All Claims roles",
        description: "Full access to all features and actions within the Claims module.",
        roleNames: ALL_CLAIMS_ROLES,
      },
      {
        name: "Reimbursement",
        description: "Can view and initiate a reimbursement request for a member.",
        roleNames: ["CLAIMS_REIMBURSEMENT_ROLE"],
      },
      {
        name: "Offline Visits (OFF LCTs)",
        description: "Can view and start offline visits for a member.",
        roleNames: ["CLAIMS_OFFLCT_ROLE"],
      },
      {
        name: "Claims Vetting",
        description: "Can view and vet claims.",
        roleNames: ["CLAIMS_VETTING_ROLE"],
      },
      {
        name: "Claims Batching",
        description: "Can view and batch all vetted claims",
        roleNames: ["CLAIMS_BATCHING_ROLE"],
      },
      {
        name: "Reversals",
        description: "Can initiate a reversal against a benefit upon request.",
        roleNames: ["CLAIMS_REVERSAL_ROLE"],
      },
      {
        name: "Reports",
        description:
          "Can download scheme utilization, provider utilization and delivery status reports.",
        roleNames: ["CLAIMS_REPORTS_ROLE"],
      },
    ],
  },
  {
    name: "Claims Adjudication Module",
    realmRoles: [
      {
        name: "All Claims Adjudication role",
        description:
          "Full access to all features and actions within the Claims Adjudication module.",
        roleNames: ALL_CLAIMS_ADJUDICATION_ROLES,
      },
      {
        name: "Claim Batching",
        description: "Creation of batches and batch allocation.",
        roleNames: ["CLAIMS_ADJUDICATION_BATCHING_ROLE"],
      },
      {
        name: "Claim Vetting",
        description: "Can vet invoices based on the assigned batches.",
        roleNames: ["CLAIMS_ADJUDICATION_VETTING_ROLE"],
      },
      {
        name: "AI Adjudication",
        description: "Vitraya claim adjudication; review adjudicated claims.",
        roleNames: ["CLAIMS_ADJUDICATION_AI_ROLE"],
      },
    ],
  },
  {
    name: "Finance & Accounting Module",
    realmRoles: [
      {
        name: "All Finance & Accounting role",
        description:
          "Full access to all features and actions within the Finance & Accounting module.",
        roleNames: ALL_FINANCE_AND_ACCOUNTING_ROLES,
      },
      {
        name: "Vouchering",
        description: "Can create vouchers for payments.",
        roleNames: ["FINANCE_AND_ACCOUNTING_VOUCHERING_ROLE"],
      },
      {
        name: "Settlement",
        description: "Can make payments of the vouchers created.",
        roleNames: ["FINANCE_AND_ACCOUNTING_SETTLEMENT_ROLE"],
      },
      {
        name: "Remittance",
        description: "Can download provider remittances and share the same with providers.",
        roleNames: ["FINANCE_AND_ACCOUNTING_REMITTANCE_ROLE"],
      },
      {
        name: "Credit Analysis",
        description: "Can perform account reconciliation and initiate Sign-offs.",
        roleNames: ["FINANCE_AND_ACCOUNTING_CREDIT_ANALYSIS_ROLE"],
      },
    ],
  },
];

export const DEFAULT_ROLES = ["uma_authorization", "offline_access", "default-roles-lct", "PAYER"];

export const PAYER_SUPER_ADMIN_ASSIGNMENT_ROLE = "PAYER_SUPER_ADMIN";

export const PAYER_SUPER_ADMIN_EFFECTIVE_ROLES = [
  "PAYER_SUPER_ADMIN",
  "USER_MANAGEMENT_ROLES_ASSIGNMENT_ROLE",
  "USER_MANAGEMENT_AUDIT_LOGS_ROLE",
];

export const LEGACY_MODULE_ACTIVATION_ROLES = [
  "CARE_ROLE",
  "CLAIMS_ROLE",
  "FINANCE_AND_ACCOUNTING_ROLE",
  "PROVIDER_ROLE",
  "MEMBERSHIP_ROLE",
];

export const FULL_ACCESS_ROLES = [
  "ALL_CARE_ROLE",
  "ALL_MEMBERSHIP_ROLE",
  "ALL_PROVIDERS_ROLE",
  "ALL_CLAIMS_ROLE",
  "ALL_CLAIMS_ADJUDICATION_ROLE",
  "ALL_FINANCE_AND_ACCOUNTING_ROLE",
];

export const SKIP_CHECK_ROLES = [...DEFAULT_ROLES, ...FULL_ACCESS_ROLES];

export interface PredefinedRole {
  name: string;
  description: string;
  slug: string;
  moduleName: AccessModuleName;
  role: CompositeRole;
  icon: JSX.Element;
}

export const predefinedRoles: PredefinedRole[] = [
  {
    name: "Scheme Management Role",
    description: "Provides access to the Schemes Module functionality",
    slug: "scheme-management",
    moduleName: "Schemes Module",
    role: "SCHEME_OVERVIEW_ROLE",
    icon: <ClipboardDocumentListIcon className="h-8 w-8" />,
  },
  {
    name: "Membership Management Role",
    description: "Provides access to the Membership Module functionality",
    slug: "membership-management",
    moduleName: "Membership Module",
    role: "ALL_MEMBERSHIP_ROLE",
    icon: <UserGroupIcon className="h-8 w-8" />,
  },
  {
    name: "Provider Management Role",
    description: "Provides access to the Provider Module functionality",
    slug: "provider-management",
    moduleName: "Provider Module",
    role: "ALL_PROVIDERS_ROLE",
    icon: <HomeModernIcon className="h-8 w-8" />,
  },
  {
    name: "Care Management Role",
    description: "Provides access to the Care Module functionality",
    slug: "care-management",
    moduleName: "Care Module",
    role: "ALL_CARE_ROLE",
    icon: <HeartIcon className="h-8 w-8" />,
  },
  {
    name: "Claims Management Role",
    description: "Provides access to the Claims Module functionality",
    slug: "claims-management",
    moduleName: "Claims Module",
    role: "ALL_CLAIMS_ROLE",
    icon: <DocumentTextIcon className="h-8 w-8" />,
  },
  {
    name: "Claims Adjudication Role",
    description: "Provides access to the Claims Adjudication Module functionality",
    slug: "claims-adjudication",
    moduleName: "Claims Adjudication Module",
    role: "ALL_CLAIMS_ADJUDICATION_ROLE",
    icon: <ScaleIcon className="h-8 w-8" />,
  },
  {
    name: "Finance & Accounting Role",
    description: "Provides access to the Finance & Accounting Module functionality",
    slug: "finance-accounting",
    moduleName: "Finance & Accounting Module",
    role: "ALL_FINANCE_AND_ACCOUNTING_ROLE",
    icon: <BanknotesIcon className="h-8 w-8" />,
  },
];

export const NON_PAYER_PORTAL_ROLES = [
  "ADMIN",
  "ADMIN_1_NET",
  "Cashier",
  "GROUP_ROLE_1",
  "SUPER_ADMIN",
  "SUPER_ADMIN_LVL_1",
  "SUPER_ADMIN_LVL_2",
  "CREDIT_CONTROL",
  "CONTACT_CENTRE",
];

export const PREAUTH_ROLES_NOT_DENIABLE_VIA_POLICY = [
  "CARE_PREAUTH_VIEW_ALL_ROLE",
  "CARE_PREAUTH_VIEW_DENTAL_ROLE",
  "CARE_PREAUTH_VIEW_OPTICAL_ROLE",
  "CARE_PREAUTH_VIEW_OUTPATIENT_ROLE",
  "CARE_PREAUTH_VIEW_INPATIENT_ROLE",
  "CARE_PREAUTH_VIEW_MATERNITY_ROLE",

  // Composite Approval & View Roles (these roles grant view, so they shouldn't be denied via view policy)
  "CARE_PREAUTH_APPROVE_AND_VIEW_ALL_ROLE",
  "CARE_PREAUTH_APPROVE_AND_VIEW_DENTAL_ROLE",
  "CARE_PREAUTH_APPROVE_AND_VIEW_OPTICAL_ROLE",
  "CARE_PREAUTH_APPROVE_AND_VIEW_OUTPATIENT_ROLE",
  "CARE_PREAUTH_APPROVE_AND_VIEW_INPATIENT_ROLE",
  "CARE_PREAUTH_APPROVE_AND_VIEW_MATERNITY_ROLE",

  // Composite Top-Up & View Roles (these roles grant view, so they shouldn't be denied via view policy)
  "CARE_PREAUTH_TOPUP_AND_VIEW_ALL_ROLE",
  "CARE_PREAUTH_TOPUP_AND_VIEW_DENTAL_ROLE",
  "CARE_PREAUTH_TOPUP_AND_VIEW_OPTICAL_ROLE",
  "CARE_PREAUTH_TOPUP_AND_VIEW_OUTPATIENT_ROLE",
  "CARE_PREAUTH_TOPUP_AND_VIEW_INPATIENT_ROLE",
  "CARE_PREAUTH_TOPUP_AND_VIEW_MATERNITY_ROLE",

  // Composite Roles for assigning or topping up benefits
  // (in the spirit of the essence of deny policies, denial should not be done in bulk)
  "CARE_PREAUTH_APPROVE_ALL_ROLE",
  "CARE_PREAUTH_TOPUP_ALL_ROLE",

  // legacy approval and topup access role
  "CARE_PREAUTH_REVIEW_ROLE",
];

export const PREAUTH_ROLES_TO_NOT_ASSIGN_DIRECTLY = [
  "CARE_PREAUTH_APPROVE_ALL_ROLE",
  "CARE_PREAUTH_TOPUP_ALL_ROLE",
  "CARE_PREAUTH_APPROVE_DENTAL_ROLE",
  "CARE_PREAUTH_APPROVE_OPTICAL_ROLE",
  "CARE_PREAUTH_APPROVE_OUTPATIENT_ROLE",
  "CARE_PREAUTH_APPROVE_INPATIENT_ROLE",
  "CARE_PREAUTH_APPROVE_MATERNITY_ROLE",
  "CARE_PREAUTH_TOPUP_DENTAL_ROLE",
  "CARE_PREAUTH_TOPUP_OPTICAL_ROLE",
  "CARE_PREAUTH_TOPUP_OUTPATIENT_ROLE",
  "CARE_PREAUTH_TOPUP_INPATIENT_ROLE",
  "CARE_PREAUTH_TOPUP_MATERNITY_ROLE",

  // legacy approval and topup access role
  "CARE_PREAUTH_REVIEW_ROLE",
];
