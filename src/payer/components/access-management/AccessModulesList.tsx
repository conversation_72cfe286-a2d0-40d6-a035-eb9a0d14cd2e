import {
  BanknotesIcon,
  ClipboardDocumentListIcon,
  DocumentTextIcon,
  HeartIcon,
  HomeModernIcon,
  ScaleIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";
import { useAppSelector } from "../../store/hooks";
import AccessModuleListCard, { AccessModule } from "./AccessModuleListCard";
import {
  ALL_CARE_ROLES,
  ALL_CLAIMS_ADJUDICATION_ROLES,
  ALL_CLAIMS_ROLES,
  ALL_FINANCE_AND_ACCOUNTING_ROLES,
  ALL_MEMBERSHIP_ROLES,
  ALL_PROVIDERS_ROLES,
  ALL_SCHEME_ROLES,
} from "./data";

export function AccessModulesList() {
  const selectedUserEffectiveRoles = useAppSelector(
    (state) => state.accessControl.selectedUserRoles,
  ).map((role) => role.name);
  const effectiveUserRolesSet = new Set(selectedUserEffectiveRoles);
  const isUserAssignedAnySchemeRole = ALL_SCHEME_ROLES.some((role) =>
    effectiveUserRolesSet.has(role),
  );
  const isUserAssignedAnyMembershipRole = ALL_MEMBERSHIP_ROLES.some((role) =>
    effectiveUserRolesSet.has(role),
  );
  const isUserAssignedAnyProviderRole = ALL_PROVIDERS_ROLES.some((role) =>
    effectiveUserRolesSet.has(role),
  );
  const isUserAssignedAnyCareRole = ALL_CARE_ROLES.some((role) => effectiveUserRolesSet.has(role));
  const isUserAssignedAnyClaimsRole = ALL_CLAIMS_ROLES.some((role) =>
    effectiveUserRolesSet.has(role),
  );

  const isUserAssignedAnyClaimsAdjudicationRole = ALL_CLAIMS_ADJUDICATION_ROLES.some((role) =>
    effectiveUserRolesSet.has(role),
  );

  const isUserAssignedAnyFinanceAndAccountingRole = ALL_FINANCE_AND_ACCOUNTING_ROLES.some((role) =>
    effectiveUserRolesSet.has(role),
  );

  const accessModulesList: Array<AccessModule> = [
    {
      name: "Schemes Module",
      description: "Schemes Menu : Grant or Remove User Access to the Scheme Module.",
      isCurrentUserAssignedAnyRole: isUserAssignedAnySchemeRole,
      icon: <ClipboardDocumentListIcon className="h-8 w-8" />,
      realmRoles: ALL_SCHEME_ROLES,
    },
    {
      name: "Membership Module",
      description: "Membership Menu : Grant or Remove User Access to the Membership Module.",
      isCurrentUserAssignedAnyRole: isUserAssignedAnyMembershipRole,
      icon: <UserGroupIcon className="h-8 w-8" />,
      realmRoles: ALL_MEMBERSHIP_ROLES,
    },
    {
      name: "Provider Module",
      description: "Providers Menu : Grant or Remove User Access to the Providers Module.",
      isCurrentUserAssignedAnyRole: isUserAssignedAnyProviderRole,
      icon: <HomeModernIcon className="h-8 w-8" />,
      realmRoles: ALL_PROVIDERS_ROLES,
    },
    {
      name: "Care Module",
      description: "Care Menu : Grant or Remove User Access to the Care Module.",
      isCurrentUserAssignedAnyRole: isUserAssignedAnyCareRole,
      icon: <HeartIcon className="h-8 w-8" />,
      realmRoles: ALL_CARE_ROLES,
    },
    {
      name: "Claims Module",
      description: "Claims Menu : Grant or Remove User Access to the Claims Module.",
      isCurrentUserAssignedAnyRole: isUserAssignedAnyClaimsRole,
      icon: <DocumentTextIcon className="h-8 w-8" />,
      realmRoles: ALL_CLAIMS_ROLES,
    },
    {
      name: "Claims Adjudication Module",
      description:
        "Claims Adjudication Menu :Grant or Remove User Access to the Claims Adjudication Module.",
      isCurrentUserAssignedAnyRole: isUserAssignedAnyClaimsAdjudicationRole,
      icon: <ScaleIcon className="h-8 w-8" />,
      realmRoles: ALL_CLAIMS_ADJUDICATION_ROLES,
    },
    {
      name: "Finance & Accounting Module",
      description:
        "Finance & Accounting Menu : Grant or Remove User Access to the Finance & Accounting Module",
      isCurrentUserAssignedAnyRole: isUserAssignedAnyFinanceAndAccountingRole,
      icon: <BanknotesIcon className="h-8 w-8" />,
      realmRoles: ALL_FINANCE_AND_ACCOUNTING_ROLES,
    },
  ];

  return (
    <section className="flex flex-col gap-4 pt-8">
      {accessModulesList.map((accessModule) => (
        <AccessModuleListCard accessModule={accessModule} />
      ))}
    </section>
  );
}
