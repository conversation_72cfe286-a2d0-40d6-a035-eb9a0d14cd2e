import { JSX } from "react";
import { useAppDispatch } from "../../store/hooks";
import Badge from "../ui/Badge";
import Card<PERSON>rapper from "../ui/CardWrapper";
import { AccessModuleName } from "./AccessModuleCard";
import { setSelectedModule } from "../../features/access-control/accessControlSlice";
import useClearAccessControlState from "../../hooks/useClearAccessControlState";

export interface AccessModule {
  name: AccessModuleName;
  description: string;
  icon: JSX.Element;
  realmRoles: string[];
  isCurrentUserAssignedAnyRole: boolean;
}

interface AccessModuleCardProps {
  accessModule: AccessModule;
}

export default function AccessModuleListCard({ accessModule }: AccessModuleCardProps) {
  const dispatch = useAppDispatch();
  const clearAccessControlState = useClearAccessControlState();

  const handleAccessModuleClick = (module: AccessModuleName) => {
    clearAccessControlState();
    dispatch(setSelectedModule(module));
  };

  return (
    <CardWrapper
      className="flex cursor-pointer justify-between"
      onClick={() => handleAccessModuleClick(accessModule.name)}
    >
      <section className="flex gap-4">
        <div className="pt-2">{accessModule.icon}</div>
        <div className="flex flex-col gap-2">
          <h3 className="text-sm font-semibold text-darkGray">{accessModule.name}</h3>
          <p className="text-base font-medium text-customGray">{accessModule.description}</p>
        </div>
      </section>
      {accessModule.isCurrentUserAssignedAnyRole ? (
        <Badge color="green" text="Assigned" hasDot className="self-center" />
      ) : (
        <Badge color="yellow" text="Not Assigned" hasDot className="self-center" />
      )}
    </CardWrapper>
  );
}
