import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useGetEffectiveUserRealmRolesQuery } from "../../api/features/membershipApi";
import {
  setIsConfirmRolesModalOpen,
  setIsUpdatingUserRoles,
  setSelectedModule,
  setSelectedUserRoles,
  setTemporaryUserRoles,
} from "../../features/access-control/accessControlSlice";
import useClearAccessControlState from "../../hooks/useClearAccessControlState";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import Button from "../ui/Button";
import MainWrapper from "../ui/MainWrapper";
import TopBackButton from "../ui/TopBackButton";
import Text from "../ui/typography/Text";
import AccessModuleCard, { AccessModuleDetail } from "./AccessModuleCard";
import { AccessModulesList } from "./AccessModulesList";
import { accessModuleDetails } from "./data";

export default function AccessManagement() {
  const effectiveUserRoles = useAppSelector((state) => state.accessControl.selectedUserRoles).map(
    (role) => role.name,
  );
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const selectedUserId = useAppSelector((state) => state.accessControl.selectedAccessControlUserId);
  const selectedUserUsername = useAppSelector(
    (state) => state.accessControl.selectedAccessControlUsername,
  );
  const clearAccessControlState = useClearAccessControlState();

  const { data } = useGetEffectiveUserRealmRolesQuery(selectedUserId);

  const selectedModule = useAppSelector((state) => state.accessControl.selectedModule);

  const handleBackClick = () => {
    if (selectedModule) {
      dispatch(setSelectedModule(null));
      clearAccessControlState();
    } else {
      navigate(-1);
    }
  };

  useEffect(() => {
    if (data) {
      dispatch(setSelectedUserRoles(data));
      dispatch(setTemporaryUserRoles(data.map((role) => role.name)));
    }
  }, [selectedUserId, data, dispatch]);

  const userAddedRoles = useAppSelector((state) => state.accessControl.userAddedRoles).filter(
    (role) => !effectiveUserRoles.includes(role),
  );
  const userRemovedRoles = useAppSelector((state) => state.accessControl.userRemovedRoles).filter(
    (role) => effectiveUserRoles.includes(role),
  );
  const handleUpdateRoles = () => {
    dispatch(setIsConfirmRolesModalOpen(true));
  };

  const isUpdatingUserRoles = useAppSelector((state) => state.accessControl.isUpdatingUserRoles);
  const userRolesModified = userAddedRoles.length > 0 || userRemovedRoles.length > 0;

  const showUpdateRolesButtons = userRolesModified && isUpdatingUserRoles;

  const handleCancelClick = () => {
    clearAccessControlState();
    dispatch(setIsUpdatingUserRoles(false));
  };

  return (
    <MainWrapper>
      <section className="flex justify-between gap-6">
        <div className="flex gap-6">
          <TopBackButton className="mt-2" onClick={handleBackClick} />
          <div className="flex flex-col gap-2">
            <Text variant="heading">
              User Management / <span className="text-customGray">{selectedUserUsername}</span>
            </Text>
            <Text variant="description" className="italic">
              Please click on the card to view all available roles within the module.
            </Text>
          </div>
        </div>
        {showUpdateRolesButtons && (
          <div className="flex items-center gap-6">
            <Button variant="outlined" className="h-fit w-fit" onClick={handleCancelClick}>
              Cancel
            </Button>
            <Button onClick={handleUpdateRoles} className="h-fit w-fit">
              Update Roles
            </Button>
          </div>
        )}
      </section>
      {selectedModule ? (
        <section className="pt-8">
          <AccessModuleCard
            module={
              accessModuleDetails.find(
                (module) => module.name === selectedModule,
              ) as AccessModuleDetail
            }
          />
        </section>
      ) : (
        <AccessModulesList />
      )}
    </MainWrapper>
  );
}
