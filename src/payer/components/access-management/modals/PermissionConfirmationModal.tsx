import { XMarkIcon } from "@heroicons/react/24/outline";
import { useEffect } from "react";
import { toast } from "react-toastify";
import { useUpdateUserRolesMutation } from "../../../api/features/membershipApi";
import {
  setIsConfirmRolesModalOpen,
  setIsUserRolesUpdatedSuccessfullyModalOpen,
} from "../../../features/access-control/accessControlSlice";
import { UpdateUserPayload } from "../../../lib/types/access-control/user";
import UserService from "../../../services/UserService";
import { useAppDispatch, useAppSelector } from "../../../store/hooks";
import { findModifiedRealmRoles, getModuleRoleNames } from "../../../utils/utils";
import Button from "../../ui/Button";
import DialogWrapper from "../../ui/modal/DialogWrapper";
import ProgressModal from "../../ui/modal/ProgressModal";
import { AccessModuleName } from "../AccessModuleCard";
import { ALL_MODULES_ALL_ROLES_MAPPING } from "../data";

interface PermissionConfirmationModalProps {
  isConfirmRolesModalOpen: boolean;
  onClose: () => void;
}

export default function PermissionConfirmationModal({
  isConfirmRolesModalOpen,
  onClose,
}: PermissionConfirmationModalProps) {
  const effectiveUserRoles = useAppSelector((state) => state.accessControl.selectedUserRoles).map(
    (role) => role.name,
  );
  const assignedRoles = useAppSelector((state) => state.accessControl.userAddedRoles);
  const revokedRoles = useAppSelector((state) => state.accessControl.userRemovedRoles).filter(
    (role) => effectiveUserRoles.includes(role),
  );
  const moduleName = useAppSelector((state) => state.accessControl.selectedModule);
  const userId = useAppSelector((state) => state.accessControl.selectedAccessControlUserId);

  const dispatch = useAppDispatch();

  const moduleAllRoleNames = getModuleRoleNames(moduleName as AccessModuleName);

  const moduleFullRolesAccessPermission =
    ALL_MODULES_ALL_ROLES_MAPPING[moduleName as AccessModuleName];

  const userHasModuleFullRolesAccess = effectiveUserRoles.includes(moduleFullRolesAccessPermission);

  const effectiveAddedRoles =
    userHasModuleFullRolesAccess && revokedRoles.length > 0
      ? assignedRoles.concat(
          effectiveUserRoles.filter(
            (role) => !revokedRoles.includes(role) && moduleAllRoleNames.includes(role),
          ),
        )
      : assignedRoles.filter((role) => !effectiveUserRoles.includes(role));

  const effectiveRemovedRoles = userHasModuleFullRolesAccess
    ? revokedRoles.concat([moduleFullRolesAccessPermission])
    : revokedRoles.filter((role) => effectiveUserRoles.includes(role));

  const [updateRoles, { isLoading, isSuccess }] = useUpdateUserRolesMutation();

  const username = UserService.getUsername()?.toString();

  const { addedRoles, removedRoles } = findModifiedRealmRoles(
    effectiveAddedRoles,
    effectiveRemovedRoles,
  );

  const updateUserRolesPayload: UpdateUserPayload = {
    userId,
    payload: {
      addedRoles: effectiveAddedRoles,
      removedRoles: effectiveRemovedRoles.filter((role) => effectiveUserRoles.includes(role)),
      actionedBy: username,
    },
  };

  const handleUserRolesUpdate = async () => {
    try {
      await updateRoles(updateUserRolesPayload).unwrap();
      toast.success("User Roles Updated Successfully!");
    } catch (error) {
      toast.error("An error occurred while updating user roles!");
    }
  };

  useEffect(() => {
    if (isSuccess) {
      dispatch(setIsUserRolesUpdatedSuccessfullyModalOpen(true));
      dispatch(setIsConfirmRolesModalOpen(false));
    }
  }, [isSuccess, dispatch]);

  return (
    <DialogWrapper show={isConfirmRolesModalOpen} onClose={onClose} maxWidth="max-w-[50rem]">
      <section className="flex flex-col gap-4">
        <div className="flex items-center justify-between px-4 py-2">
          <h2 className="text-lg font-medium text-darkBlue">Grant Permissions</h2>
          <button onClick={onClose}>
            <XMarkIcon className="h-4 w-4 text-black" />
          </button>
        </div>
        <hr className="text-gray-200" />
        <section className="flex flex-col gap-6 px-4 pb-2">
          {addedRoles.length > 0 && (
            <div className="flex flex-col gap-3">
              <h2 className="text-lg font-medium text-[#16A34A]">Roles assigned</h2>
              {addedRoles.map((role) => (
                <div key={role.name}>
                  <p className="text-base font-medium text-darkGray">{role.name}</p>
                  <p className="text-base text-customGray">{role.description}</p>
                </div>
              ))}
            </div>
          )}
          {removedRoles.length > 0 && (
            <div className="flex flex-col gap-3">
              <h2 className="text-lg font-medium text-[#CA8A04]">Roles revoked</h2>
              {removedRoles.map((role) => (
                <div key={role.name}>
                  <p className="text-base font-medium text-darkGray">{role.name}</p>
                  <p className="text-base text-customGray">{role.description}</p>
                </div>
              ))}
            </div>
          )}
        </section>
        <div className="flex items-center gap-4 self-end px-4 pb-4">
          <Button variant="outlined" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleUserRolesUpdate}>Apply Roles</Button>
        </div>
      </section>
      <ProgressModal
        isProgressModalOpen={isLoading}
        onClose={onClose}
        description="Updating user roles..."
      />
    </DialogWrapper>
  );
}
