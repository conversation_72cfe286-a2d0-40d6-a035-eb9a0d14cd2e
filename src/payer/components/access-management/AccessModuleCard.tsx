import { membershipApi } from "../../api/features/membershipApi";
import {
  setIsConfirmRolesModalOpen,
  setIsUpdatingUserRoles,
  setIsUserRolesUpdatedSuccessfullyModalOpen,
} from "../../features/access-control/accessControlSlice";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import { getModuleRoleNames } from "../../utils/utils";
import AccessRoleSwitch from "../ui/AccessRoleSwitch";
import CardWrapper from "../ui/CardWrapper";
import SuccessModal from "../ui/modal/SuccessModal";
import { SKIP_CHECK_ROLES } from "./data";
import PermissionConfirmationModal from "./modals/PermissionConfirmationModal";

export type AccessModuleName =
  | "Schemes Module"
  | "Membership Module"
  | "Provider Module"
  | "Care Module"
  | "Claims Module"
  | "Claims Adjudication Module"
  | "Finance & Accounting Module";

export interface RealmRole {
  name: string;
  description: string;
  roleNames: Array<string>;
}

export interface AccessModuleDetail {
  name: AccessModuleName;
  realmRoles: Array<RealmRole>;
}

export default function AccessModuleCard({ module }: { module: AccessModuleDetail }) {
  const allModuleRoleNames = getModuleRoleNames(module.name);
  const dispatch = useAppDispatch();
  const effectiveUserRoleNames = useAppSelector((state) => state.accessControl.selectedUserRoles)
    .map((role) => role.name)
    .filter((role) => allModuleRoleNames.includes(role) && !SKIP_CHECK_ROLES.includes(role));

  const addedUserRoles = useAppSelector((state) => state.accessControl.userAddedRoles);
  const removedRoles = useAppSelector((state) => state.accessControl.userRemovedRoles);

  const temporaryUserRoleNames = useAppSelector((state) => state.accessControl.temporaryUserRoles)
    .concat(addedUserRoles)
    .filter((role) => !removedRoles.includes(role));

  const isConfirmRolesModalOpen = useAppSelector(
    (state) => state.accessControl.isConfirmRolesModalOpen,
  );

  const isUpdatingUserRoles = useAppSelector((state) => state.accessControl.isUpdatingUserRoles);

  const selectedRoleNames = isUpdatingUserRoles ? temporaryUserRoleNames : effectiveUserRoleNames;

  const isUserRolesUpdatedSuccessfullyModalOpen = useAppSelector(
    (state) => state.accessControl.isUserRolesUpdatedSuccessfullyModalOpen,
  );

  const handleDoneClick = () => {
    dispatch(membershipApi.util.invalidateTags(["UserRoles"]));
    dispatch(membershipApi.util.invalidateTags(["PayerUsersAuditLogs"]));
    dispatch(setIsConfirmRolesModalOpen(false));
    dispatch(setIsUpdatingUserRoles(false));
    dispatch(setIsUserRolesUpdatedSuccessfullyModalOpen(false));
  };

  return (
    <CardWrapper>
      <section className="flex gap-24 p-4">
        <h2 className="text-lg font-semibold text-darkBlue">{module?.name}</h2>
        <section className="flex flex-grow flex-col gap-4">
          {module?.realmRoles.map((role) => (
            <section className="flex justify-between gap-8" key={role?.name}>
              <div className="flex flex-col gap-1">
                <h2 className="text-sm font-semibold text-darkGray">{role?.name}</h2>
                <p className="text-base text-customGray">{role?.description}</p>
              </div>
              <AccessRoleSwitch
                roleNames={role.roleNames}
                className="self-center"
                checked={role.roleNames.every((roleName) => selectedRoleNames.includes(roleName))}
              />
            </section>
          ))}
        </section>
      </section>
      <PermissionConfirmationModal
        isConfirmRolesModalOpen={isConfirmRolesModalOpen}
        onClose={() => dispatch(setIsConfirmRolesModalOpen(false))}
      />
      <SuccessModal
        maxWidthRem="max-w-[50rem]"
        title="User Roles Updated Successfully!"
        description="The user has been successfully assigned roles with the required access."
        isSuccessModalOpen={isUserRolesUpdatedSuccessfullyModalOpen}
        onClose={handleDoneClick}
      />
    </CardWrapper>
  );
}
