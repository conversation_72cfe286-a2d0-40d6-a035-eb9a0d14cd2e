import { useLocation } from "react-router-dom";
import { formatValue } from "../../lib/Utils";
import { useAppSelector } from "../../store/hooks";
import UserService from "../../services/UserService";
import {
  InvoiceBatchStatus,
  InvoiceStatus,
  InvoiceVettingStatus,
} from "../../lib/types/claims/invoice";
import { useGetAllClaimsQuery } from "../../api/features/claimsApi";
import { useEffect, useState } from "react";

const SummaryItem = ({
  label,
  value,
  isCalculatingVoucherAmount,
}: {
  label: string;
  value: string;
  isCalculatingVoucherAmount?: boolean;
}) => {
  return (
    <div className="flex flex-col">
      <p className={`${isCalculatingVoucherAmount ? "text-blue-800" : "text-txtBlue"}`}>{value}</p>
      <p className={`text-sm text-lightGray`}>{label}</p>
    </div>
  );
};

const isValidSummaryItem = (summaryItem: string) => {
  return (
    !summaryItem.toLocaleLowerCase().includes("select") &&
    summaryItem !== "" &&
    summaryItem !== undefined
  );
};

interface ClaimSummaryProps {
  handleAllocate?: () => void;
  isCalculatingVoucherAmount: boolean;
  isEditingVoucher?: boolean;
}

export default function ClaimSummary({
  handleAllocate,
  isCalculatingVoucherAmount,
  isEditingVoucher,
}: ClaimSummaryProps) {
  const { pathname } = useLocation();
  const pattern = /^\/finance-and-accounting\/vouchering\/edit\/(\d+)\/(\w+)$/;

  const allocatedAmount = useAppSelector((state) => state.vouchering.allocatedAmount);
  const voucheringPayableAmount = useAppSelector(
    (state) => state.vouchering.voucheringPayableAmount,
  );
  const voucherAmount = useAppSelector((state) => state.vouchering.voucherAmount);
  const selectedSchemeName = useAppSelector((state) => state.vouchering.selectedSchemeName);
  const selectedBenefitName = useAppSelector((state) => state.vouchering.selectedBenefitName);
  const selectedAgeBandName = useAppSelector((state) => state.vouchering.selectedAgeBandName);
  const region = useAppSelector((state) => state.vouchering.selectedRegion);
  const selectedAccountName = useAppSelector((state) => state.vouchering.selectedAccountName);
  const voucherAccountName = useAppSelector((state) => state.voucherEditing.voucherAccountName);
  const allocatedAmountExceeded = allocatedAmount
    ? voucheringPayableAmount > allocatedAmount
    : false;
  const uniqueCheckedClaimsIds = useAppSelector((state) => state.vouchering.uniqueCheckedClaimsIds);
  const currentVoucherAmount = useAppSelector((state) => state.voucherEditing.currentVoucherAmount);
  const accountToDisplay = pattern.test(pathname) ? voucherAccountName : selectedAccountName;

  // allocate button active state
  const [isAllocateButtonActive, setIsAllocateButtonActive] = useState(true);
  const payerId: string = UserService.getPayer()?.tokenParsed?.["payerId"];
  const batchStatus: InvoiceBatchStatus = "BATCHED";
  const vettingStatuses: Array<InvoiceVettingStatus> = ["PARTIAL", "APPROVED"];
  const invoiceStatuses: Array<InvoiceStatus> = ["BALANCE_DEDUCTED"];
  const withVoucher = false;
  const onlyProviderWithAccounts = true;

  const { data } = useGetAllClaimsQuery({
    payerId,
    vettingStatuses,
    batchStatus,
    invoiceStatuses,
    withVoucher,
    onlyProviderWithAccounts,
    page: 1,
    size: 10,
  });

  useEffect(() => {
    if (data?.data?.totalElements === 0) {
      setIsAllocateButtonActive(false);
    }
  }, [data?.data]);

  return (
    <section className="flex min-h-[400px] flex-col justify-between rounded-lg border border-[#AFAFAF4D] bg-white p-4 shadow-md">
      <div>
        <h1 className="py-2 text-center text-base text-darkBlue">Claim Summary</h1>
        <section className="flex flex-col space-y-3 py-4">
          {accountToDisplay && isValidSummaryItem(accountToDisplay) && (
            <SummaryItem label="Account Name" value={accountToDisplay} />
          )}
          {selectedSchemeName && isValidSummaryItem(selectedSchemeName) && (
            <SummaryItem label="Scheme Name" value={selectedSchemeName} />
          )}
          {region && isValidSummaryItem(region) && <SummaryItem label="Region" value={region} />}
          {selectedBenefitName && isValidSummaryItem(selectedBenefitName) && (
            <SummaryItem label="Benefit Name" value={selectedBenefitName} />
          )}
          {selectedAgeBandName && isValidSummaryItem(selectedAgeBandName) && (
            <SummaryItem label="Aging" value={selectedAgeBandName} />
          )}
          {!isEditingVoucher && (
            <SummaryItem
              label="Voucher Amount"
              value={isCalculatingVoucherAmount ? "Calculating..." : formatValue(voucherAmount)}
              isCalculatingVoucherAmount={isCalculatingVoucherAmount}
            />
          )}
          {!isEditingVoucher && (
            <SummaryItem label="Allocated Amount" value={formatValue(allocatedAmount)} />
          )}
          <section className="flex flex-col space-y-1">
            <div className={`flex flex-col space-y-2`}>
              {isEditingVoucher ? (
                <p className={`text-txtBlue`}>{formatValue(currentVoucherAmount)}</p>
              ) : (
                <p className={`${allocatedAmountExceeded ? "text-[#DC2626]" : "text-txtBlue"} `}>
                  {formatValue(voucheringPayableAmount)}
                </p>
              )}
            </div>
            {!isEditingVoucher && allocatedAmountExceeded && (
              <p className="text-[10px] text-[#DC2626]">
                The payable amount is more than the allocated amount
              </p>
            )}
            <p
              className={`text-sm ${allocatedAmountExceeded ? "text-[#DC2626]" : "text-lightGray"}`}
            >
              Payable Amount:
            </p>
          </section>
        </section>
      </div>
      {!isEditingVoucher && (
        <div className="self-end pb-8">
          <button
            className="w-fit rounded bg-btnBlue px-4 py-2 text-sm font-semibold text-white disabled:cursor-not-allowed disabled:bg-skyBlue"
            onClick={handleAllocate}
            disabled={uniqueCheckedClaimsIds.length > 0 || !isAllocateButtonActive}
          >
            Allocate
          </button>
        </div>
      )}
    </section>
  );
}
