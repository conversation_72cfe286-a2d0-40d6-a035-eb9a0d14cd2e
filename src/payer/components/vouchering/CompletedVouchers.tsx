import { useEffect, useState } from "react";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { useGetAllVouchersQuery } from "../../api/features/claimsApi";
import { Voucher } from "../../lib/types/vouchering/voucher";
import UserService from "../../services/UserService";
import NoClaimsVouchering from "../illustrations/NoClaimsVouchering";
import EmptyState from "../ui/EmptyState";
import PrimaryPagination from "../ui/pagination/PrimaryPagination";
import VoucherCard from "./VoucherCard";

export default function CompletedVouchers() {
  const [pageNumber, setPageNumber] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [vouchers, setVouchers] = useState<Array<Voucher>>([]);

  const handlePageNumberClick = (page: number) => {
    setPageNumber(page);
  };

  const handleSizeChange = (size: number) => {
    setPageSize(size);
    setPageNumber(1);
  };

  const payerId: string = UserService.getPayer()?.tokenParsed?.["payerId"];

  const { data, isLoading, isFetching, error } = useGetAllVouchersQuery({
    payerId,
    page: pageNumber,
    size: pageSize,
  });

  const totalElements = data?.data.totalElements as number;
  const totalPages = data?.data.totalPages as number;

  useEffect(() => {
    if (data?.data.content) {
      setVouchers(data?.data.content as Voucher[]);
    }
  }, [data?.data.content]);

  return (
    <div className={`flex flex-col ${vouchers.length === 0 && "pt-16"}`}>
      {isLoading || isFetching ? (
        <div className="flex items-center justify-center space-x-2 py-8">
          <LoadingIcon className="h-6 w-6 text-blue-400" />
          <p className="text-blue-700">Loading...</p>
        </div>
      ) : error ? (
        <div className="flex items-center justify-center py-8">
          <p className="text-red-700">Error loading vouchers. Try again later.</p>
        </div>
      ) : vouchers?.length === 0 ? (
        <EmptyState
          illustration={<NoClaimsVouchering />}
          message={{
            title: "No vouchers available",
            description:
              "You haven't created any vouchers yet. Once you start creating vouchers, they'll be displayed here for your review and further actions...",
          }}
        />
      ) : (
        !isLoading &&
        !isFetching && (
          <section className="flex flex-col space-y-4">
            <div className="flex flex-col space-y-4 pt-8">
              {vouchers.map((voucher) => (
                <VoucherCard key={voucher.id} voucher={voucher} isVoucherComplete={true} />
              ))}
            </div>
            <div className="self-start py-4">
              <PrimaryPagination
                totalElements={totalElements}
                totalPages={totalPages ?? 1}
                pageNumber={pageNumber}
                pageSize={pageSize}
                onPageNumberClick={handlePageNumberClick}
                onSizeChange={handleSizeChange}
              />
            </div>
          </section>
        )
      )}
    </div>
  );
}
