import React, { PropsWithChildren } from "react";
import { VoucherEditingType } from "./modals/VoucherEditingModal";

interface VoucherBtnProps {
  uniqueCheckedClaimsIds: Array<string | number>;
  handleBatchClick: () => void;
  isEditingVoucher?: boolean;
  voucherEditingType?: VoucherEditingType;
}

const VoucherButton: React.FC<VoucherBtnProps> = ({
  uniqueCheckedClaimsIds,
  handleBatchClick,
  isEditingVoucher,
  voucherEditingType,
}: PropsWithChildren<VoucherBtnProps>) => {
  return (
    <button
      className={`${
        uniqueCheckedClaimsIds?.length < 1
          ? "hidden"
          : `w-fit cursor-pointer rounded-[4px] ${voucherEditingType === VoucherEditingType.Add ? "bg-btnBlue" : "bg-customRed"} px-3 py-1.5 text-[10px] font-semibold text-white`
      }`}
      onClick={handleBatchClick}
    >
      {isEditingVoucher && voucherEditingType
        ? `${voucherEditingType === VoucherEditingType.Add ? "Add Invoices" : "Remove Invoices"}`
        : `Voucher Claims`}
    </button>
  );
};

export default VoucherButton;
