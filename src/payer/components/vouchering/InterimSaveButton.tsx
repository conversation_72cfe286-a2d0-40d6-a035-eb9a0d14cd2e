import React, { PropsWithChildren } from "react";

interface InterimSaveBtnProps {
  uniqueCheckedClaimsIds: Array<string | number>;
  handleInterimSave: () => void;
}

const InterimSaveButton: React.FC<InterimSaveBtnProps> = ({
  uniqueCheckedClaimsIds,
  handleInterimSave,
}: PropsWithChildren<InterimSaveBtnProps>) => {
  return (
    <button
      className={`${
        uniqueCheckedClaimsIds?.length < 1
          ? "hidden"
          : "w-fit cursor-pointer rounded-[4px] border border-btnBlue bg-white px-3 py-1.5 text-[10px] font-semibold text-btnBlue"
      }`}
      onClick={handleInterimSave}
    >
      Interim Save
    </button>
  );
};

export default InterimSaveButton;
