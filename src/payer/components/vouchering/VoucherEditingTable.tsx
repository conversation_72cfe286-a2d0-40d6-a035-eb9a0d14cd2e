import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import {
  useAddVoucherInvoicesMutation,
  useGetVoucherByIdQuery,
  useRemoveVoucherInvoicesMutation,
} from "../../api/features/claimsApi";
import cancel from "../../assets/material-symbols_close.png";
import ClaimSummary from "../../components/vouchering/ClaimSummary";
import ClaimsVoucheringFilter from "../../components/vouchering/ClaimsVoucheringFilter";
import VoucheringVettedClaimsTable from "./VoucheringVettedClaimsTable";
import VoucherButton from "../../components/vouchering/VoucherButton";
import {
  setCurrentVoucherAmount,
  setIsUpdatingVoucherSuccess,
} from "../../features/claims/voucherEditingSlice";
import {
  setIsVoucheringSelectAllActive,
  setUniqueCheckedClaimsIds,
  setVoucheringCheckedClaimsIds,
  setVoucheringPayableAmount,
} from "../../features/claims/voucheringSlice";
import { VoucherEditingPayload } from "../../lib/types/claims/invoice";
import UserService from "../../services/UserService";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import ProcessingVouchersModal from "./modals/ProcessingVouchersModal";
import { VoucherEditingType } from "./modals/VoucherEditingModal";
import VoucherUpdatedSuccessfullyModal from "./modals/VoucherUpdatedSuccessfullyModal";
import TopBackButton from "../ui/TopBackButton";
import Button from "../ui/Button";
import ProgressModal from "../ui/modal/ProgressModal";
import CompleteInterimVoucherWarningModal from "./modals/complete-interim-voucher-warning-modal";

export default function VoucherEditingTable() {
  const [isCompleteVoucherWarningModalOpen, setIsCompleteVoucherWarningModalOpen] = useState(false);
  const { editType, voucherId } = useParams();
  const uniqueCheckedClaimsIds = useAppSelector((state) => state.vouchering.uniqueCheckedClaimsIds);
  const isCalculatingVoucherAmount = useAppSelector(
    (state) => state.vouchering.isCalculatingVoucherAmount,
  );
  const isUpdatingVoucherSuccess = useAppSelector(
    (state) => state.voucherEditing.isUpdatingVoucherSuccess,
  );

  const dispatch = useAppDispatch();
  const username = UserService.getUsername()?.toString();
  const navigate = useNavigate();

  const handleBack = () => {
    navigate(-1);
  };

  const currentVoucherId = useAppSelector((state) => state.voucherEditing.voucherId);

  const [untouchedVoucherAmount, setUntouchedVoucherAmount] = useState(0);
  const { data: voucherData } = useGetVoucherByIdQuery(currentVoucherId);

  const handleCancelSelection = () => {
    dispatch(setVoucheringCheckedClaimsIds([]));
    dispatch(setVoucheringPayableAmount(0));
    dispatch(setUniqueCheckedClaimsIds([]));
    dispatch(setIsVoucheringSelectAllActive(false));
    dispatch(setCurrentVoucherAmount(voucherData?.data.amount ?? untouchedVoucherAmount));
  };

  // voucher editing
  const [
    removeVoucherInvoices,
    { isLoading: isRemovingVoucherInvoices, isSuccess: isRemovingVoucherInvoicesSuccess },
  ] = useRemoveVoucherInvoicesMutation();
  const [
    addInvoicesToVoucher,
    { isLoading: isAddingInvoicesToVoucher, isSuccess: isAddingInvoicesToVoucherSuccess },
  ] = useAddVoucherInvoicesMutation();

  const [completeVoucher, { isLoading: isCompletingVoucher }] = useAddVoucherInvoicesMutation();

  const removeVoucherInvoicesPayload: VoucherEditingPayload = {
    voucherId: Number(voucherId),
    body: {
      invoiceIds: uniqueCheckedClaimsIds,
      interimSave: true,
      actionByUser: username,
    },
  };

  const addInvoicesToVoucherPayload: VoucherEditingPayload = {
    voucherId: Number(voucherId),
    body: {
      invoiceIds: uniqueCheckedClaimsIds,
      interimSave: true,
      actionByUser: username,
    },
  };

  const completeVoucherPayload: VoucherEditingPayload = {
    voucherId: Number(voucherId),
    body: {
      interimSave: false,
      actionByUser: username,
    },
  };

  const handleVoucherComplete = async () => {
    try {
      await completeVoucher(completeVoucherPayload).unwrap();
      setIsCompleteVoucherWarningModalOpen(false);
      toast.success("Voucher completed successfully!", {
        autoClose: 1000,
        onClose: () => navigate(-1),
      });
    } catch (error) {
      console.error(error);
      toast.error("Failed to complete voucher. Please try again later.");
    }
  };

  const handleVoucherEdit = async () => {
    if (editType === VoucherEditingType.Add) {
      try {
        await addInvoicesToVoucher(addInvoicesToVoucherPayload).unwrap();
        handleCancelSelection();
      } catch (error) {
        console.error(error);
        toast.error(error as string);
      }
    }
    if (editType === VoucherEditingType.Remove) {
      try {
        await removeVoucherInvoices(removeVoucherInvoicesPayload).unwrap();
        handleCancelSelection();
      } catch (error) {
        console.error(error);
        toast.error(error as string);
      }
    }
  };

  useEffect(() => {
    if (isRemovingVoucherInvoicesSuccess || isAddingInvoicesToVoucherSuccess) {
      dispatch(setIsUpdatingVoucherSuccess(true));
    }
  }, [dispatch, isRemovingVoucherInvoicesSuccess, isAddingInvoicesToVoucherSuccess]);

  useEffect(() => {
    if (voucherData?.data.amount) {
      setUntouchedVoucherAmount(voucherData?.data.amount);
    }
  }, [voucherData]);

  return (
    <section className="m-6 flex h-full flex-col space-y-0 overflow-hidden rounded-lg border border-gray-200 py-4 pl-6 pr-4 shadow-xl">
      {uniqueCheckedClaimsIds.length > 0 ? (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-darkBlue">
            <button onClick={handleCancelSelection}>
              <img src={cancel} alt="cancel selection" className="w-4" />
            </button>
            <p className="flex items-center gap-1 text-sm">
              <span>{uniqueCheckedClaimsIds.length}</span> <span>selected</span>
            </p>
          </div>
          <VoucherButton
            isEditingVoucher={true}
            voucherEditingType={editType as VoucherEditingType}
            uniqueCheckedClaimsIds={uniqueCheckedClaimsIds}
            handleBatchClick={handleVoucherEdit}
          />
        </div>
      ) : (
        <div className="flex items-center justify-between space-x-3">
          <div className="flex items-center space-x-3">
            <TopBackButton onClick={handleBack} />
            <h1 className="text-lg font-medium text-[#111827]">
              {editType === VoucherEditingType.Add ? "Add" : "Remove"} Invoices
            </h1>
          </div>
          <Button
            onClick={() => setIsCompleteVoucherWarningModalOpen(true)}
            className="px-3 py-1.5 text-[10px] font-semibold"
          >
            Complete Voucher
          </Button>
        </div>
      )}
      <section>
        <ClaimsVoucheringFilter />
        <section className="flex w-full space-x-12">
          <div className="min-w-[70%] basis-2/3">
            <VoucheringVettedClaimsTable isEditing={true} />
          </div>
          <div className="basis-1/3">
            <ClaimSummary
              isEditingVoucher={true}
              isCalculatingVoucherAmount={isCalculatingVoucherAmount}
            />
          </div>
        </section>
      </section>
      {/* modals */}
      <section>
        <ProgressModal
          onClose={() => null}
          isProgressModalOpen={isCompletingVoucher}
          title="Completing Voucher..."
          description="Please wait while we complete the voucher..."
        />
        <ProcessingVouchersModal
          isProcessingVouchersModalOpen={isAddingInvoicesToVoucher || isRemovingVoucherInvoices}
          onClose={() => true}
        />
        <VoucherUpdatedSuccessfullyModal
          isSuccessModalOpen={isUpdatingVoucherSuccess}
          onClose={() => dispatch(setIsUpdatingVoucherSuccess(false))}
        />
        <CompleteInterimVoucherWarningModal
          onClose={() => setIsCompleteVoucherWarningModalOpen(false)}
          isSuccessModalOpen={isCompleteVoucherWarningModalOpen}
          handleCompleteVoucher={handleVoucherComplete}
        />
      </section>
    </section>
  );
}
