import { useEffect } from "react";
import { useGetAllClaimsQuery } from "../../api/features/claimsApi";
import {
  setCurrentVoucherAmount,
  setInvoiceIds,
  setIsVoucherEditingModalOpen,
  setVoucherAccountId,
  setVoucherAccountName,
  setVoucherId,
} from "../../features/claims/voucherEditingSlice";
import { Voucher } from "../../lib/types/vouchering/voucher";
import { formatDate, formatValue } from "../../lib/Utils";
import { useAppDispatch } from "../../store/hooks";
import { formatDateVerbose, getDifferenceInDays } from "../../utils/utils";

const VoucherSummaryItem = ({ label, value }: { label: string; value: string | number }) => {
  return (
    <div className="flex space-x-2 text-sm">
      <p className="text-gray whitespace-nowrap">{label}:</p>
      <p className="text-darkGray">{value}</p>
    </div>
  );
};

interface VoucherCardProps {
  voucher: Voucher;
  isVoucherComplete: boolean;
}

export default function VoucherCard({ voucher, isVoucherComplete }: VoucherCardProps) {
  const dispatch = useAppDispatch();

  const { data } = useGetAllClaimsQuery({
    payerId: voucher.payerId,
    voucherIds: [voucher.id],
  });

  const handleVoucherEditing = (id: number) => {
    dispatch(setVoucherId(id));
    dispatch(setIsVoucherEditingModalOpen(true));
    dispatch(setVoucherAccountId(voucher.providerAccount?.accountId.toString()));
    if (voucher.providerAccount?.accountName) {
      dispatch(setVoucherAccountName(voucher.providerAccount?.accountName));
    }
    dispatch(setCurrentVoucherAmount(voucher.amount));
  };

  useEffect(() => {
    if (data?.data.content) {
      dispatch(setInvoiceIds(data?.data.content.map((invoice) => invoice.id)));
    }
  }, [data?.data.content, dispatch]);

  return (
    <div className="flex justify-between rounded-md border border-gray-300 bg-white p-4 shadow-md">
      <div className="flex flex-col space-y-2">
        <h2>{voucher.voucherNo}</h2>
        {voucher.providerAccount?.accountName && (
          <VoucherSummaryItem label="Account Name" value={voucher.providerAccount?.accountName} />
        )}
        <VoucherSummaryItem label="Voucher Amount" value={formatValue(voucher.amount)} />
        <div className="flex items-center space-x-2">
          <VoucherSummaryItem
            label="Date Created"
            value={formatDateVerbose(new Date(voucher.createdOn))}
          />
          <div className="flex items-center space-x-2 text-sm">
            <div className="rounded-full bg-lightGray p-[3px]"></div>
            <p>
              {`${getDifferenceInDays(
                formatDate(voucher.createdOn),
                formatDate(new Date(Date.now())),
              )}${" "}
                ${
                  getDifferenceInDays(
                    formatDate(voucher.createdOn),
                    formatDate(new Date(Date.now())),
                  ) === 1
                    ? "day"
                    : "days"
                } ago`}
            </p>
          </div>
        </div>
        {voucher.createdBy?.userName && (
          <VoucherSummaryItem label="Created By" value={voucher.createdBy?.userName} />
        )}
      </div>
      {!isVoucherComplete && (
        <button
          className="mb-2 w-fit self-end rounded-[4px] bg-btnBlue px-2 py-1.5 text-xs font-semibold text-white disabled:cursor-not-allowed disabled:bg-skyBlue"
          onClick={() => handleVoucherEditing(voucher.id)}
        >
          Continue
        </button>
      )}
    </div>
  );
}
