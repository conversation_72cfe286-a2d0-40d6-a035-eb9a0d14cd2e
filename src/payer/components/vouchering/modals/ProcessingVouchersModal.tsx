import LoadingAnimation from "../../animations/LoadingAnimation/LoadingAnimation";
import DialogWrapper from "../../ui/modal/DialogWrapper";

interface ProcessingVouchersModalProps {
  onClose: () => void;
  isProcessingVouchersModalOpen: boolean;
}

export default function ProcessingVouchersModal({
  onClose,
  isProcessingVouchersModalOpen,
}: ProcessingVouchersModalProps) {
  return (
    <DialogWrapper
      show={isProcessingVouchersModalOpen}
      maxWidth="max-w-[40rem]"
      onClose={onClose}
      className="px-8 py-10"
    >
      <section className="flex flex-col space-y-5">
        <div className="self-center">
          <LoadingAnimation size={60} />
        </div>
        <div className="flex flex-col space-y-5">
          <h2 className="self-center text-base text-btnBlue">Processing your vouchers...</h2>
          <p className="text-gray text-center text-sm">
            Your vouchers are being processed in the background. This may take a few moments. Please
            don't close the window or navigate away until the process is complete.
          </p>
        </div>
      </section>
    </DialogWrapper>
  );
}
