import { Dialog, Transition } from "@headlessui/react";
import { Fragment } from "react";
import SuccessAnimation from "../../ui/modal/SuccessAnimation";
import { useAppDispatch } from "../../../store/hooks";
import { claimsApi } from "../../../api/features/claimsApi";

export default function VoucherCreatedSuccessfullyModal({
  onClose,
  isSuccessModalOpen,
}: {
  onClose: () => void;
  isSuccessModalOpen: boolean;
}) {
  const dispatch = useAppDispatch();
  const handleDoneClick = () => {
    onClose();
    dispatch(claimsApi.util.invalidateTags(["Claims", "Vouchers"]));
  };

  return (
    <>
      <Transition appear show={isSuccessModalOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={onClose}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/25" />
          </Transition.Child>
          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-[35rem] transform overflow-hidden rounded-md bg-white py-8 text-left align-middle shadow-xl transition-all">
                  <div className="mt-2 flex flex-col space-y-4">
                    <div className="self-center pb-4">
                      <SuccessAnimation />
                    </div>
                    <h2 className="whitespace-nowrap text-center text-2xl font-semibold text-darkGray">
                      Voucher created successfully
                    </h2>
                    <div className="text-gray flex flex-col items-center justify-center self-center text-center text-sm">
                      <p className="mx-8">The payment has been</p>
                      <p>created successfully.</p>
                      <p>Thank you!!!</p>
                    </div>
                    <button
                      className="w-fit self-center rounded-md bg-btnBlue px-4 py-1 text-xs text-white"
                      onClick={handleDoneClick}
                    >
                      Done
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}
