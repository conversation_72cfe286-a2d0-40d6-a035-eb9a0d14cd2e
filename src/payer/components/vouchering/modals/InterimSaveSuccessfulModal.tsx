import cancel from "../../../assets/material-symbols_close.png";
import systemAllocationComplete from "../../../assets/system_allocation_complete.png";
import DialogWrapper from "../../ui/modal/DialogWrapper";

interface InterimSaveSuccessfulModalProps {
  onClose: () => void;
  isSuccessModalOpen: boolean;
}

export default function InterimSaveSuccessfulModal({
  onClose,
  isSuccessModalOpen,
}: InterimSaveSuccessfulModalProps) {
  return (
    <>
      <DialogWrapper
        onClose={onClose}
        show={isSuccessModalOpen}
        maxWidth="max-w-[35rem]"
        className="p-4"
      >
        <section className="flex flex-col">
          <button onClick={onClose} className="self-end">
            <img src={cancel} alt="close delete modal" className="w-4" />
          </button>
          <section className="flex flex-col space-y-4 px-2 pb-4">
            <div className="flex space-x-3">
              <img
                src={systemAllocationComplete}
                alt="system allocation complete"
                className="h-9 w-9"
              />
              <div className="flex flex-col space-y-4 pt-2">
                <h2 className="text-base text-[#3F495F]">Interim save successful.</h2>
              </div>
            </div>
            <p className="text-gray pe-8 ps-2 pt-8 text-sm">
              The selected invoices have been added to a voucher and temporarily saved as drafts. To
              continue the vouchering process, click on the 'In Progress' tab to access the voucher.
            </p>
            <button
              className="w-fit self-end rounded-md bg-btnBlue px-3 py-1 text-[11px] font-semibold text-white"
              onClick={onClose}
            >
              Close
            </button>
          </section>
        </section>
      </DialogWrapper>
    </>
  );
}
