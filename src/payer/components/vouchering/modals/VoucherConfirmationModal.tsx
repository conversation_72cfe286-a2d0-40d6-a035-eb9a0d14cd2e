import cancel from "../../../assets/material-symbols_close.png";
import systemAllocationComplete from "../../../assets/system_allocation_complete.png";
import { formatValue } from "../../../lib/Utils";
import { useAppSelector } from "../../../store/hooks";
import DialogWrapper from "../../ui/modal/DialogWrapper";

export default function VoucherConfirmationModal({
  onClose,
  isSuccessModalOpen,
}: {
  onClose: () => void;
  isSuccessModalOpen: boolean;
}) {
  const voucheringPayableAmount = useAppSelector(
    (state) => state.vouchering.voucheringPayableAmount,
  );
  const allocatedAmount = useAppSelector((state) => state.vouchering.allocatedAmount);
  const canAddMoreInvoices = voucheringPayableAmount < allocatedAmount;
  return (
    <>
      <DialogWrapper
        onClose={onClose}
        show={isSuccessModalOpen}
        maxWidth="max-w-[35rem]"
        className="p-4"
      >
        <section className="flex flex-col">
          <button onClick={onClose} className="self-end">
            <img src={cancel} alt="close delete modal" className="w-4" />
          </button>
          <section className="flex flex-col space-y-4 px-2 pb-4">
            <div className="flex space-x-3">
              <img
                src={systemAllocationComplete}
                alt="system allocation complete"
                className="h-9 w-9"
              />
              <div className="flex flex-col space-y-4 pt-2">
                <h2 className="text-base text-[#3F495F]">System Allocation Completed</h2>
                <div className="flex items-center space-x-2 text-sm">
                  <p className="text-gray">Payable Amount:</p>
                  <p className="text-darkBlue">{formatValue(voucheringPayableAmount)}</p>
                </div>
                {canAddMoreInvoices && (
                  <div className="flex items-center space-x-2 text-sm">
                    <p className="text-gray">Balance:</p>
                    <p className="text-darkBlue">
                      {formatValue(allocatedAmount - voucheringPayableAmount)}
                    </p>
                  </div>
                )}
              </div>
            </div>
            {canAddMoreInvoices ? (
              <>
                <p className="text-gray pe-8 ps-2 pt-8 text-sm">
                  Would you like to add more invoices to balance the total payable amount?
                </p>
                <div className="flex items-center space-x-8 self-end pb-4 pr-24">
                  <button className="w-fit rounded-[4px] bg-btnBlue px-3 py-1 text-[11px] font-medium text-white">
                    Yes
                  </button>
                  <button
                    className="w-fit rounded-[4px] bg-[#F9FAFB] px-4 py-1 text-[11px] text-midGray"
                    onClick={onClose}
                  >
                    No
                  </button>
                </div>
              </>
            ) : (
              <>
                <p className="text-gray py-8 pe-8 ps-2 text-sm">
                  The amount has been fully allocated. Please click 'Continue' to complete the
                  allocation process.
                </p>
                <button
                  className="w-fit self-end rounded-md bg-btnBlue px-3 py-1 text-[11px] font-semibold text-white"
                  onClick={onClose}
                >
                  Continue
                </button>
              </>
            )}
          </section>
        </section>
      </DialogWrapper>
    </>
  );
}
