import { Dialog, Transition } from "@headlessui/react";
import { Fragment, useEffect, useState } from "react";
import { toast } from "react-toastify";
import { useVoucherVettedClaimsMutation } from "../../../api/features/claimsApi";
import {
  setAmountNotAllocated,
  setIsPartialAllocation,
  setSelectedAllocationMethod,
  setSelectedAllocationSelectionMethod,
} from "../../../features/claims/voucheringAllocationSlice";
import {
  setAllocatedAmount,
  setIsAllocationConfirmationModalOpen,
  setIsVoucheringSelectAllActive,
  setUniqueCheckedClaimsIds,
  setVoucheringCheckedClaimsIds,
  setVoucheringPayableAmount,
} from "../../../features/claims/voucheringSlice";
import { useClearFilters } from "../../../hooks/useClearFilter";
import { VoucherClaimsPayload } from "../../../lib/types/claims/invoice";
import { formatValue } from "../../../lib/Utils";
import UserService from "../../../services/UserService";
import { useAppDispatch, useAppSelector } from "../../../store/hooks";
import { isValidBatchingCriteria, isValidFilterName, isValidId } from "../../../utils/utils";
import ProcessingVouchersModal from "./ProcessingVouchersModal";
import VoucherCreatedSuccessfullyModal from "./VoucherCreatedSuccessfullyModal";

const SummaryItem = ({ label, value }: { label: string; value: string | number }) => {
  return (
    <div className="flex items-center space-x-2 text-sm">
      <p className="text-gray">{label}:</p>
      <p className="text-darkBlue">{value}</p>
    </div>
  );
};

export default function AllocationConfirmationModal({
  onClose,
  isSuccessModalOpen,
}: {
  onClose: () => void;
  isSuccessModalOpen: boolean;
}) {
  const [voucherDescription, setVoucherDescription] = useState("");
  const voucheringPayableAmount = useAppSelector(
    (state) => state.vouchering.voucheringPayableAmount,
  );
  const uniqueCheckedClaimsIds = useAppSelector((state) => state.vouchering.uniqueCheckedClaimsIds);
  const mainVoucheringCriteria = useAppSelector((state) => state.vouchering.mainVoucheringCriteria);
  const [isVoucherCreatedSuccessfullyModalOpen, setIsVoucherCreatedSuccessfullyModalOpen] =
    useState(false);
  const selectedRegion = useAppSelector((state) => state.vouchering.selectedRegion);
  const selectedAccountName = useAppSelector((state) => state.vouchering.selectedAccountName);
  const selectedAccount = useAppSelector((state) => state.vouchering.selectedAccount);
  const selectedBenefitId = useAppSelector((state) => state.vouchering.selectedBenefitId);
  const selectedAgeBandId = useAppSelector((state) => state.vouchering.selectedAgeBandId);
  const selectedSchemeIds = useAppSelector((state) => state.vouchering.selectedSchemeIds);

  const clearFilters = useClearFilters();

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setVoucherDescription(e.target.value);
  };

  const dispatch = useAppDispatch();

  // manual vouchering
  const payerId: string = UserService.getPayer()?.tokenParsed?.["payerId"];
  const username = UserService.getUsername()?.toString();

  const handleCancelSelection = () => {
    dispatch(setVoucheringCheckedClaimsIds([]));
    dispatch(setVoucheringPayableAmount(0));
    dispatch(setUniqueCheckedClaimsIds([]));
    dispatch(setIsVoucheringSelectAllActive(false));
  };

  const [voucherClaims, { isLoading: isVoucheringClaims, isSuccess }] =
    useVoucherVettedClaimsMutation();

  const resetAllocation = () => {
    dispatch(setSelectedAllocationMethod(null));
    dispatch(setSelectedAllocationSelectionMethod(null));
    dispatch(setIsPartialAllocation(false));
    dispatch(setAmountNotAllocated(true));
    dispatch(setAllocatedAmount(0));
  };

  const userSelectionVoucheringPayload: VoucherClaimsPayload = {
    payerId: payerId,
    ...(isValidBatchingCriteria(mainVoucheringCriteria) && {
      batchCriteria: mainVoucheringCriteria,
    }),
    batchStatus: "BATCHED",
    actionedBy: username,
    invoiceIds: uniqueCheckedClaimsIds,
    ...(voucherDescription !== "" && { description: voucherDescription }),
    ...(isValidFilterName(selectedRegion) && { region: selectedRegion }),
    onlyProviderWithAccounts: true,
    withVoucher: false,
    ...(isValidId(selectedAccount) && { providerAccountIds: [Number(selectedAccount)] }),
    ...(isValidId(selectedBenefitId) && { catalogIds: [selectedBenefitId] }),
    ...(isValidId(selectedAgeBandId) && { ageBandId: selectedAgeBandId }),
    ...(selectedSchemeIds.length > 0 && { planIds: selectedSchemeIds }),
  };

  const handleVouchering = async () => {
    try {
      dispatch(setIsAllocationConfirmationModalOpen(false));
      await voucherClaims(userSelectionVoucheringPayload).unwrap();
      setVoucherDescription("");
      resetAllocation();
      handleCancelSelection();
      clearFilters();
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      toast.error(error.data.error || "Failed to voucher invoices. Please try again later.");
      setVoucherDescription("");
      handleCancelSelection();
      console.error(error);
    }
  };

  useEffect(() => {
    if (isSuccess) {
      setIsVoucherCreatedSuccessfullyModalOpen(true);
    }
  }, [isSuccess]);

  return (
    <>
      <Transition appear show={isSuccessModalOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={onClose}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/25" />
          </Transition.Child>
          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-[40rem] transform overflow-hidden rounded-md bg-white p-8 text-left align-middle shadow-xl transition-all">
                  <div className="mt-2 flex flex-col space-y-4">
                    <SummaryItem
                      label="Total Payable Amount"
                      value={formatValue(voucheringPayableAmount)}
                    />
                    {isValidFilterName(selectedAccountName) && (
                      <SummaryItem label="Account Name" value={selectedAccountName} />
                    )}
                    <div className="flex flex-col space-y-2">
                      <h2 className="text-gray text-sm">Voucher Description:</h2>
                      <fieldset className="rounded-md border border-[#D1D5DB] px-2 outline-none">
                        <legend className="px-0.5 text-[10px] font-medium text-[#4B5563]">
                          Description
                        </legend>
                        <textarea
                          name="voucherDescription"
                          id="voucher-description"
                          value={voucherDescription}
                          onChange={handleChange}
                          placeholder="Add voucher description"
                          className="h-20 w-full rounded-md border-0 border-none px-1 py-2 text-xs text-gray-700 outline-none placeholder:text-xs focus:border-none focus:outline-none focus:ring-0"
                        ></textarea>
                      </fieldset>
                    </div>
                    <div className="flex items-center space-x-8 self-end pb-4">
                      <button
                        className="w-fit rounded-md bg-btnBlue px-2 py-1 text-[11px] text-white"
                        onClick={handleVouchering}
                      >
                        Confirm
                      </button>
                      <button
                        className="w-fit rounded-md bg-[#F9FAFB] px-2 py-1 text-[11px] text-midGray"
                        onClick={onClose}
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
      {/* modals */}
      <section>
        <ProcessingVouchersModal
          isProcessingVouchersModalOpen={isVoucheringClaims}
          onClose={() => true}
        />
        <VoucherCreatedSuccessfullyModal
          isSuccessModalOpen={isVoucherCreatedSuccessfullyModalOpen}
          onClose={() => setIsVoucherCreatedSuccessfullyModalOpen(false)}
        />
      </section>
    </>
  );
}
