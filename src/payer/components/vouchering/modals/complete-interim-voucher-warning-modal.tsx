import DialogWrapper from "../../ui/modal/DialogWrapper";
import { XMarkIcon } from "@heroicons/react/24/outline";

interface CompleteInterimVoucherWarningModalProps {
  onClose: () => void;
  isSuccessModalOpen: boolean;
  handleCompleteVoucher: () => void;
}

export default function CompleteInterimVoucherWarningModal({
  onClose,
  isSuccessModalOpen,
  handleCompleteVoucher,
}: CompleteInterimVoucherWarningModalProps) {
  return (
    <DialogWrapper
      onClose={onClose}
      show={isSuccessModalOpen}
      maxWidth="max-w-[35rem]"
      className="p-4"
    >
      <section className="flex flex-col">
        <button onClick={onClose} className="self-end">
          <XMarkIcon className="h-4 w-4" />
        </button>
        <section className="flex flex-col space-y-4 px-2 pb-4">
          <div className="flex space-x-6">
            <div className="flex flex-col space-y-4 pt-1">
              <h2 className="text-base text-[#3F495F]">
                Are you sure you want to complete this voucher?
              </h2>
              <p className="text-sm text-customGray">
                Completing this voucher will move the voucher from 'In Progress' to 'Completed'. Are
                you sure you want to proceed? Click 'No' to cancel and make changes or 'Yes' to
                complete.
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-4 self-end">
            <button
              className="rounded-[4px] bg-customRed   px-3 py-1 text-[10px] text-white"
              onClick={onClose}
            >
              No
            </button>
            <button
              className="px-2 py-1 text-[10px] text-[#667085]"
              onClick={handleCompleteVoucher}
            >
              Yes
            </button>
          </div>
        </section>
      </section>
    </DialogWrapper>
  );
}
