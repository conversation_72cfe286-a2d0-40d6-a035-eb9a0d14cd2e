import { MouseEvent, useEffect } from "react";
import { toast } from "react-toastify";
import { useVoucherVettedClaimsMutation } from "../../../api/features/claimsApi";
import cancel from "../../../assets/material-symbols_close.png";
import {
  setAmountNotAllocated,
  setIsPartialAllocation,
  setSelectedAllocationMethod,
  setSelectedAllocationSelectionMethod,
} from "../../../features/claims/voucheringAllocationSlice";
import {
  setAllocatedAmount,
  setIsVoucheringAgingCriteriaSuccessfulModalOpen,
  setIsVoucheringAllocationModalOpen,
  setIsVoucheringEntireInvoicesSuccessfulModalOpen,
} from "../../../features/claims/voucheringSlice";
import { useClearFilters } from "../../../hooks/useClearFilter";
import useResetVoucheringAllocation from "../../../hooks/useResetVoucheringAllocation";
import { VoucherClaimsPayload } from "../../../lib/types/claims/invoice";
import { VoucheringCriteria } from "../../../lib/types/vouchering/claimVoucheringFilter";
import UserService from "../../../services/UserService";
import { useAppDispatch, useAppSelector } from "../../../store/hooks";
import { isValidBatchingCriteria, isValidFilterName, isValidId } from "../../../utils/utils";
import DialogWrapper from "../../ui/modal/DialogWrapper";
import ProcessingVouchersModal from "./ProcessingVouchersModal";
import AllocationInput from "./ui/AllocationInput";
import ContinueAllocationButton from "./ui/ContinueAllocationButton";
import DetailedCheckbox, {
  AllocationMethod,
  AllocationSelectionMethod,
} from "./ui/DetailedCheckbox";
import VoucherCreatedSuccessfullyModal from "./VoucherCreatedSuccessfullyModal";

interface ClaimsVoucheringAllocationModalProps {
  onClose: () => void;
  isSuccessModalOpen: boolean;
}

export default function ClaimsVoucheringAllocationModal({
  onClose,
  isSuccessModalOpen,
}: ClaimsVoucheringAllocationModalProps) {
  const dispatch = useAppDispatch();
  const clearFilters = useClearFilters();
  const resetAllocation = useResetVoucheringAllocation();

  // voucheringAllocation state
  const selectedAllocationMethod = useAppSelector(
    (state) => state.voucheringAllocation.selectedAllocationMethod,
  );
  const selectedAllocationSelectionMethod = useAppSelector(
    (state) => state.voucheringAllocation.selectedAllocationSelectionMethod,
  );
  const isPartialAllocation = useAppSelector(
    (state) => state.voucheringAllocation.isPartialAllocation,
  );
  const amountNotAllocated = useAppSelector(
    (state) => state.voucheringAllocation.amountNotAllocated,
  );

  // vouchering state
  const mainVoucheringCriteria = useAppSelector((state) => state.vouchering.mainVoucheringCriteria);
  const allocatedAmount = useAppSelector((state) => state.vouchering.allocatedAmount);
  const selectedAgeBandId = useAppSelector((state) => state.vouchering.selectedAgeBandId);
  const selectedBenefitId = useAppSelector((state) => state.vouchering.selectedBenefitId);
  const selectedRegion = useAppSelector((state) => state.vouchering.selectedRegion);
  const selectedScheme = useAppSelector((state) => state.vouchering.selectedScheme);
  const selectedAccount = useAppSelector((state) => state.vouchering.selectedAccount);
  const isVoucheringAgingCriteriaSuccessfulModalOpen = useAppSelector(
    (state) => state.vouchering.isVoucheringAgingCriteriaSuccessfulModalOpen,
  );
  const isVoucheringEntireInvoicesSuccessfulModalOpen = useAppSelector(
    (state) => state.vouchering.isVoucheringEntireInvoicesSuccessfulModalOpen,
  );
  const selectedSchemeIds = useAppSelector((state) => state.vouchering.selectedSchemeIds);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    if (name === "vouchering-allocation") {
      dispatch(setSelectedAllocationMethod(value as AllocationMethod));
    } else if (name === "partial-allocation-amount") {
      if (value === "" || /^[0-9]*$/.test(value)) {
        dispatch(setAllocatedAmount(Number(value)));
      }
    } else if (name === "selection-allocation" && value) {
      dispatch(setSelectedAllocationSelectionMethod(value as AllocationSelectionMethod));
    }
  };

  const payerId: string = UserService.getPayer()?.tokenParsed?.["payerId"];
  const username = UserService.getUsername()?.toString();

  /* partial allocation */
  const handlePartialAllocation = () => {
    dispatch(setIsPartialAllocation(true));
    dispatch(setAmountNotAllocated(false));
    dispatch(setSelectedAllocationMethod(null));
  };

  const handleClose = () => {
    resetAllocation();
    onClose();
  };

  /* user selection */
  const handleUserSelectionAllocation = () => {
    onClose();
  };

  /* vouchering functions */
  /* entire invoices */
  const [
    voucherEntireInvoices,
    { isLoading: isVoucheringEntireInvoices, isSuccess: isVoucherEntireInvoicesSuccess },
  ] = useVoucherVettedClaimsMutation();

  const allClaimsVoucheringPayload: VoucherClaimsPayload = {
    payerId,
    ...(isValidBatchingCriteria(mainVoucheringCriteria) && {
      batchCriteria: mainVoucheringCriteria,
    }),
    actionedBy: username,
    withVoucher: false,
    onlyProviderWithAccounts: true,
  };

  const handleEntireInvoicesVouchering = async (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    try {
      onClose();
      resetAllocation();
      await voucherEntireInvoices(allClaimsVoucheringPayload).unwrap();
      setSelectedAllocationMethod(null);
      clearFilters();
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      console.error(error);
      toast.error(error.data.error || "Failed to voucher entire invoices. Please try again later.");
    }
  };

  /* partial vouchering aging criteria */
  const [
    voucherPartialInvoicesAgingCriteria,
    {
      isLoading: isVoucherPartialInvoicesAgingCriteria,
      isSuccess: isPartialVoucheringAgingSuccess,
    },
  ] = useVoucherVettedClaimsMutation();

  const partialAllocationAgingCriteriaPayload: VoucherClaimsPayload = {
    payerId,
    batchCriteria: VoucheringCriteria.AGING,
    allocatedAmount,
    ...(isValidId(selectedAgeBandId) && { ageBandId: selectedAgeBandId }),
    ...(isValidFilterName(selectedRegion) && { region: selectedRegion }),
    ...(isValidId(selectedBenefitId) && { catalogIds: [selectedBenefitId] }),
    ...(isValidId(selectedScheme) && { planIds: [selectedScheme] }),
    ...(isValidId(selectedAccount) && { providerAccountIds: [Number(selectedAccount)] }),
    actionedBy: username,
    withVoucher: false,
    onlyProviderWithAccounts: true,
    ...(isValidFilterName(selectedRegion) && { region: selectedRegion }),
    ...(selectedSchemeIds.length > 0 && { planIds: selectedSchemeIds }),
  };

  const handleAgingCriteriaVouchering = async () => {
    try {
      onClose();
      resetAllocation();
      setSelectedAllocationSelectionMethod(null);
      await voucherPartialInvoicesAgingCriteria(partialAllocationAgingCriteriaPayload).unwrap();
      clearFilters();
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      console.error(error);
      toast.error(error.data.error || "Failed to voucher invoices. Please try again later.");
    }
  };

  useEffect(() => {
    if (isPartialVoucheringAgingSuccess) {
      dispatch(setIsVoucheringAllocationModalOpen(false));
      dispatch(setIsVoucheringAgingCriteriaSuccessfulModalOpen(true));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isPartialVoucheringAgingSuccess]);

  useEffect(() => {
    if (isVoucherEntireInvoicesSuccess) {
      dispatch(setIsVoucheringAllocationModalOpen(false));
      dispatch(setIsVoucheringEntireInvoicesSuccessfulModalOpen(true));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isVoucherEntireInvoicesSuccess]);

  return (
    <>
      <DialogWrapper
        onClose={onClose}
        show={isSuccessModalOpen}
        maxWidth="max-w-[47rem]"
        className="p-4"
      >
        <section className="flex flex-col">
          <button onClick={handleClose} className="self-end">
            <img src={cancel} alt="close delete modal" className="w-5" />
          </button>
          <div className="flex flex-col space-y-4 px-2 pb-4">
            <h2 className="self-center pb-4 text-xl font-semibold text-darkBlue">
              Claims Vouchering
            </h2>
            <div className="flex flex-col space-y-0.5">
              <h3 className="text-base font-medium text-darkGray">{`${
                isPartialAllocation ? `Partial` : ``
              } Allocation Method`}</h3>
              <p className="text-gray text-sm italic">
                Please select the allocation method you want to use to create a payment voucher.
              </p>
            </div>
            <section>
              {!isPartialAllocation ? (
                <fieldset className="flex flex-col space-y-6 py-2">
                  <DetailedCheckbox
                    id="entire-invoices"
                    name="vouchering-allocation"
                    value={AllocationMethod.EntireInvoices}
                    checked={selectedAllocationMethod === AllocationMethod.EntireInvoices}
                    handleInputChange={handleInputChange}
                    label="Pay entire set of invoices"
                    description="This option will create a voucher matching the total amount of the claims that meet the filter criteria."
                  />
                  <DetailedCheckbox
                    id="partial-allocation"
                    name="vouchering-allocation"
                    value={AllocationMethod.PartialAllocation}
                    checked={selectedAllocationMethod === AllocationMethod.PartialAllocation}
                    handleInputChange={handleInputChange}
                    label="Partial Allocation"
                    description="This option requires you to enter the voucher amount you want to pay"
                  />
                </fieldset>
              ) : (
                <fieldset className="flex flex-col space-y-6 py-2">
                  <DetailedCheckbox
                    id="user-selection"
                    name="selection-allocation"
                    value={AllocationSelectionMethod.UserSelection}
                    checked={
                      selectedAllocationSelectionMethod === AllocationSelectionMethod.UserSelection
                    }
                    handleInputChange={handleInputChange}
                    label="User Selection"
                    description="This option requires you to manually select the invoices that add up to the payable amount you entered."
                  />
                  <DetailedCheckbox
                    id="aging-criteria"
                    name="selection-allocation"
                    value={AllocationSelectionMethod.AgingCriteria}
                    checked={
                      selectedAllocationSelectionMethod === AllocationSelectionMethod.AgingCriteria
                    }
                    handleInputChange={handleInputChange}
                    label="Aging Criteria"
                    description="The system will automatically select invoices that add up to the payable amount you entered based on aging analysis."
                  />
                </fieldset>
              )}
            </section>
            {selectedAllocationMethod === AllocationMethod.PartialAllocation &&
              amountNotAllocated && (
                <AllocationInput
                  allocatedAmount={allocatedAmount}
                  handleInputChange={handleInputChange}
                />
              )}
            {selectedAllocationMethod && (
              <div className="flex items-center space-x-8 self-end">
                {selectedAllocationMethod === AllocationMethod.EntireInvoices ? (
                  <ContinueAllocationButton
                    handleAllocation={handleEntireInvoicesVouchering}
                    disabled={isVoucheringEntireInvoices}
                  />
                ) : (
                  <ContinueAllocationButton
                    handleAllocation={handlePartialAllocation}
                    disabled={allocatedAmount < 1}
                  />
                )}
                <button
                  className="w-fit rounded-md bg-[#F9FAFB] px-2 py-1.5 text-sm text-midGray"
                  onClick={onClose}
                >
                  Cancel
                </button>
              </div>
            )}
            {selectedAllocationSelectionMethod && (
              <div className="flex items-center space-x-8 self-end">
                {selectedAllocationSelectionMethod === AllocationSelectionMethod.UserSelection ? (
                  <ContinueAllocationButton handleAllocation={handleUserSelectionAllocation} />
                ) : (
                  <ContinueAllocationButton handleAllocation={handleAgingCriteriaVouchering} />
                )}
                <button
                  className="w-fit rounded-md bg-[#F9FAFB] px-2 py-1.5 text-sm text-midGray"
                  onClick={handleClose}
                >
                  Cancel
                </button>
              </div>
            )}
          </div>
        </section>
      </DialogWrapper>
      <section>
        <ProcessingVouchersModal
          isProcessingVouchersModalOpen={isVoucherPartialInvoicesAgingCriteria}
          onClose={() => true}
        />
        <ProcessingVouchersModal
          isProcessingVouchersModalOpen={isVoucheringEntireInvoices}
          onClose={() => true}
        />
        <VoucherCreatedSuccessfullyModal
          isSuccessModalOpen={isVoucheringAgingCriteriaSuccessfulModalOpen}
          onClose={() => dispatch(setIsVoucheringAgingCriteriaSuccessfulModalOpen(false))}
        />
        <VoucherCreatedSuccessfullyModal
          isSuccessModalOpen={isVoucheringEntireInvoicesSuccessfulModalOpen}
          onClose={() => dispatch(setIsVoucheringEntireInvoicesSuccessfulModalOpen(false))}
        />
      </section>
    </>
  );
}
