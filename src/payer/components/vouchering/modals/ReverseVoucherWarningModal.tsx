import { Dialog, Transition } from "@headlessui/react";
import { Fragment } from "react";
import warningIcon from "../../../assets/delete_batch.png";

interface ReverseVoucherWarningModalProps {
  onClose: () => void;
  isDeleteBatchModalOpen: boolean;
  handleReverseVoucher: () => void;
}

export default function ReverseVoucherWarningModal({
  onClose,
  isDeleteBatchModalOpen,
  handleReverseVoucher,
}: ReverseVoucherWarningModalProps) {
  return (
    <>
      <Transition appear show={isDeleteBatchModalOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={onClose}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/25" />
          </Transition.Child>
          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-xl transform overflow-hidden rounded-md bg-white px-8 py-8 text-left align-middle shadow-xl transition-all">
                  <section className="flex flex-col space-y-4">
                    <div className="flex items-center space-x-4">
                      <img src={warningIcon} alt="warning icon" className="w-8" />
                      <h2 className="whitespace-nowrap text-xl text-[#3F495F]">
                        Are you sure you want to reverse this voucher?
                      </h2>
                    </div>
                    <div className="pl-12">
                      <p className="text-sm text-customGray">
                        Reversing this voucher will remove all associated claims from the voucher
                        permanently. Are you sure you want to proceed? Click 'No' to cancel and make
                        changes or 'Yes' to delete.
                      </p>
                    </div>
                    <div className="flex items-center space-x-4 self-end">
                      <button
                        className="rounded-[4px] bg-customRed   px-3 py-1 text-[10px] text-white"
                        onClick={onClose}
                      >
                        No
                      </button>
                      <button
                        className="px-2 py-1 text-[10px] text-[#667085]"
                        onClick={handleReverseVoucher}
                      >
                        Yes
                      </button>
                    </div>
                  </section>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}
