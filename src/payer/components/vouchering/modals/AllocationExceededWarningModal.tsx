import cancel from "../../../assets/material-symbols_close.png";
import systemAllocationError from "../../../assets/system_allocation_error.png";
import { formatValue } from "../../../lib/Utils";
import { useAppSelector } from "../../../store/hooks";
import DialogWrapper from "../../ui/modal/DialogWrapper";

export default function AllocationExceededWarningModal({
  onClose,
  isSuccessModalOpen,
}: {
  onClose: () => void;
  isSuccessModalOpen: boolean;
}) {
  const voucheringPayableAmount = useAppSelector(
    (state) => state.vouchering.voucheringPayableAmount,
  );
  const allocatedAmount = useAppSelector((state) => state.vouchering.allocatedAmount);
  return (
    <>
      <DialogWrapper
        onClose={onClose}
        show={isSuccessModalOpen}
        maxWidth="max-w-[35rem]"
        className="p-4"
      >
        <section className="flex flex-col">
          <button onClick={onClose} className="self-end">
            <img src={cancel} alt="close delete modal" className="w-4" />
          </button>
          <section className="flex flex-col space-y-4 px-2 pb-4">
            <div className="flex space-x-6">
              <img
                src={systemAllocationError}
                alt="system allocation complete"
                className="h-9 w-9"
              />
              <div className="flex flex-col space-y-4 pt-1">
                <h2 className="text-base text-[#3F495F]">
                  The payable amount is more than the allocated amount.
                </h2>
                <div className="flex items-center space-x-2 text-sm">
                  <p className="text-gray">Payable Amount :</p>
                  <p className="text-darkBlue">{formatValue(voucheringPayableAmount)}</p>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  <p className="text-gray">Allocated Amount :</p>
                  <p className="text-darkBlue">{formatValue(allocatedAmount)}</p>
                </div>
              </div>
            </div>
            <p className="text-gray pe-8 ps-2 pt-4 text-sm leading-7">
              The payable amount cannot exceed the allocated amount. Please select an invoice with a
              lower value to match the total allocated amount.
            </p>
            <button
              className="w-fit self-end rounded-[4px] bg-btnBlue px-3 py-1 text-[11px] font-semibold text-white"
              onClick={onClose}
            >
              Adjust Selection
            </button>
          </section>
        </section>
      </DialogWrapper>
    </>
  );
}
