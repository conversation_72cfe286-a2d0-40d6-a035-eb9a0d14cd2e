import RadioButton from "../../../ui/input/RadioButton";

export enum AllocationMethod {
  EntireInvoices = "entire",
  PartialAllocation = "partial",
}

export enum AllocationSelectionMethod {
  UserSelection = "user-selection",
  AgingCriteria = "aging-criteria",
}

export enum VoucherEditingMethod {
  Add = "Add",
  Remove = "Remove",
  Reverse = "Reverse",
}

interface Props {
  id: string;
  name: string;
  value: AllocationMethod | AllocationSelectionMethod | VoucherEditingMethod;
  checked: boolean;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  label: string;
  description: string;
}

export default function DetailedCheckbox({
  id,
  name,
  value,
  checked,
  handleInputChange,
  label,
  description,
}: Props) {
  return (
    <section className="flex flex-col space-y-1">
      <div className="flex items-center space-x-2">
        <RadioButton
          id={id}
          name={name}
          value={value}
          checked={checked}
          onChange={handleInputChange}
        />
        <label htmlFor={id} className="text-sm font-medium text-[#111827]">
          {label}
        </label>
      </div>
      <p className="pl-4 text-sm text-[#4B5563]">{description}</p>
    </section>
  );
}
