import { MouseEvent } from "react";

interface ContinueVoucherEditingButtonProps {
  disabled?: boolean;
  handleEditing: (e: MouseEvent<HTMLButtonElement>) => void;
}

export default function ContinueVoucherEditingButton({
  handleEditing,
  disabled,
}: ContinueVoucherEditingButtonProps) {
  return (
    <button
      className="w-fit rounded-[4px] bg-btnBlue px-2 py-1 text-[11px] font-semibold text-white disabled:cursor-not-allowed disabled:bg-skyBlue"
      disabled={disabled}
      onClick={handleEditing}
    >
      Continue
    </button>
  );
}
