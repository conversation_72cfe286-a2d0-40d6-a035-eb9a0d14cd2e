export default function AllocationInput({
  allocatedAmount,
  handleInputChange,
}: {
  allocatedAmount: number;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}) {
  return (
    <div className="flex flex-col space-y-1.5 pl-8 pt-2">
      <h3 className="text-sm">Allocated Amount</h3>
      <div className="relative bg-white">
        <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-2 text-sm text-[#111827]">
          KES
        </div>
        <input
          type="number"
          id="amount"
          inputMode="numeric"
          name="partial-allocation-amount"
          value={allocatedAmount === 0 ? "" : allocatedAmount}
          onChange={handleInputChange}
          className="block w-fit rounded-md border border-[#D1D5DB] bg-white py-1.5 ps-10 text-sm text-[#4B5563] focus:outline-none focus:ring-0"
        />
      </div>
    </div>
  );
}
