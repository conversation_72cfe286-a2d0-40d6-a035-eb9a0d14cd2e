import { MouseEvent } from "react";

export default function ContinueAllocationButton({
  handleAllocation,
  disabled,
}: {
  handleAllocation: (e: MouseEvent<HTMLButtonElement>) => void;
  disabled?: boolean;
}) {
  return (
    <button
      className="w-fit rounded bg-btnBlue px-2 py-1.5 text-sm font-semibold text-white disabled:cursor-not-allowed disabled:bg-skyBlue"
      disabled={disabled}
      onClick={handleAllocation}
    >
      Continue
    </button>
  );
}
