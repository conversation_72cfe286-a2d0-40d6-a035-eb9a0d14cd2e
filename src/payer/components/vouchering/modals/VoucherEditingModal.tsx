import { ChangeEvent } from "react";
import { useNavigate } from "react-router-dom";
import cancel from "../../../assets/material-symbols_close.png";
import {
  setIsUpdatingVoucherSuccess,
  setIsVoucherEditingModalOpen,
  setSelectedEditingMethod,
  setVoucherReversalWarningModalOpen,
} from "../../../features/claims/voucherEditingSlice";
import { setUniqueCheckedClaimsIds } from "../../../features/claims/voucheringSlice";
import { useAppDispatch, useAppSelector } from "../../../store/hooks";
import DialogWrapper from "../../ui/modal/DialogWrapper";
import ContinueVoucherEditingButton from "./ui/ContinueVoucherEditingButton";
import DetailedCheckbox, { VoucherEditingMethod } from "./ui/DetailedCheckbox";
import VoucherUpdatedSuccessfullyModal from "./VoucherUpdatedSuccessfullyModal";

interface VoucherEditingModalProps {
  onClose: () => void;
  isSuccessModalOpen: boolean;
}

export enum VoucherEditingType {
  Add = "add",
  Remove = "remove",
}

export default function VoucherEditingModal({
  onClose,
  isSuccessModalOpen,
}: VoucherEditingModalProps) {
  const selectedEditingMethod = useAppSelector(
    (state) => state.voucherEditing.selectedEditingMethod,
  );
  const selectedVoucherId = useAppSelector((state) => state.voucherEditing.voucherId);
  const invoiceIds = useAppSelector((state) => state.voucherEditing.invoiceIds);
  const isUpdatingVoucherSuccess = useAppSelector(
    (state) => state.voucherEditing.isUpdatingVoucherSuccess,
  );

  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    dispatch(setSelectedEditingMethod(e.target.value as VoucherEditingMethod));
  };

  const handleVoucherAddInvoices = () => {
    dispatch(setUniqueCheckedClaimsIds([]));
    dispatch(setSelectedEditingMethod(null));
    dispatch(setIsVoucherEditingModalOpen(false));
    navigate(
      `/finance-and-accounting/vouchering/edit/${selectedVoucherId}/${VoucherEditingType.Add}`,
    );
  };

  const handleVoucherRemoveInvoices = () => {
    dispatch(setUniqueCheckedClaimsIds(invoiceIds));
    dispatch(setSelectedEditingMethod(null));
    dispatch(setIsVoucherEditingModalOpen(false));
    navigate(
      `/finance-and-accounting/vouchering/edit/${selectedVoucherId}/${VoucherEditingType.Remove}`,
    );
  };

  const handleVoucherReverseModalOpen = () => {
    dispatch(setVoucherReversalWarningModalOpen(true));
    dispatch(setIsVoucherEditingModalOpen(false));
  };

  const handleClose = () => {
    dispatch(setSelectedEditingMethod(null));
    onClose();
  };

  return (
    <>
      <DialogWrapper
        onClose={onClose}
        show={isSuccessModalOpen}
        maxWidth="max-w-[44rem]"
        className="px-6 py-4"
      >
        <section className="flex flex-col">
          <button onClick={handleClose} className="self-end">
            <img src={cancel} alt="close delete modal" className="w-4" />
          </button>
          <div className="flex flex-col space-y-4 px-2 pb-4">
            <div className="flex flex-col space-y-0.5">
              <h3 className="text-sm font-medium text-darkGray">Voucher Editing</h3>
              <p className="text-gray text-xs italic">
                Please select the action you'd like to perform on the voucher.
              </p>
            </div>
            <section className="flex flex-col space-y-6">
              <fieldset className="flex flex-col space-y-3 py-2">
                <DetailedCheckbox
                  id="add"
                  name="voucher-editing"
                  value={VoucherEditingMethod.Add}
                  checked={selectedEditingMethod === VoucherEditingMethod.Add}
                  handleInputChange={handleInputChange}
                  label="Add Invoices"
                  description="This option allows you to add invoices to the voucher."
                />
                <DetailedCheckbox
                  id="remove"
                  name="voucher-editing"
                  value={VoucherEditingMethod.Remove}
                  checked={selectedEditingMethod === VoucherEditingMethod.Remove}
                  handleInputChange={handleInputChange}
                  label="Remove Invoices"
                  description="This option allows you to remove invoices from the voucher."
                />
                <DetailedCheckbox
                  id="reverse"
                  name="voucher-editing"
                  value={VoucherEditingMethod.Reverse}
                  checked={selectedEditingMethod === VoucherEditingMethod.Reverse}
                  handleInputChange={handleInputChange}
                  label="Reverse Voucher"
                  description="This option allows you to remove all invoices from the voucher."
                />
              </fieldset>
              {selectedEditingMethod && (
                <div className="flex items-center space-x-3 self-end">
                  {selectedEditingMethod === VoucherEditingMethod.Remove && (
                    <ContinueVoucherEditingButton
                      handleEditing={() => handleVoucherRemoveInvoices()}
                    />
                  )}
                  {selectedEditingMethod === VoucherEditingMethod.Add && (
                    <ContinueVoucherEditingButton
                      handleEditing={() => handleVoucherAddInvoices()}
                    />
                  )}
                  {selectedEditingMethod === VoucherEditingMethod.Reverse && (
                    <ContinueVoucherEditingButton
                      handleEditing={() => handleVoucherReverseModalOpen()}
                    />
                  )}
                  <button
                    className="w-fit rounded-md bg-[#F9FAFB] px-2 py-1 text-xs text-midGray"
                    onClick={handleClose}
                  >
                    Cancel
                  </button>
                </div>
              )}
            </section>
          </div>
        </section>
        {/*modals*/}
        <section>
          <VoucherUpdatedSuccessfullyModal
            isSuccessModalOpen={isUpdatingVoucherSuccess}
            onClose={() => dispatch(setIsUpdatingVoucherSuccess(false))}
          />
        </section>
      </DialogWrapper>
    </>
  );
}
