import {
  setIsVoucheringSelectAllActive,
  setVoucheringCheckedClaimsIds,
  setVoucheringPayableAmount,
} from "../../features/claims/voucheringSlice";
import { InvoiceData } from "../../lib/types/claims/invoice";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import TableHeaderItem from "../ui/table/TableHeaderItem";
import { AllocationSelectionMethod } from "./modals/ui/DetailedCheckbox";

export default function VettedClaimsTableHeader({
  isEditing,
}: {
  isEditing?: boolean | undefined;
}) {
  const dispatch = useAppDispatch();
  const isVoucheringSelectAllActive = useAppSelector(
    (state) => state.vouchering.isVoucheringSelectAllActive,
  );
  const voucheringCheckedClaimsIds = useAppSelector(
    (state) => state.vouchering.voucheringCheckedClaimsIds,
  );
  const voucheringPayableAmount = useAppSelector(
    (state) => state.vouchering.voucheringPayableAmount,
  );
  const totalSystemClaimsVouchering = useAppSelector(
    (state) => state.vouchering.totalSystemClaimsVouchering,
  );

  const handleSelectAll = () => {
    if (isVoucheringSelectAllActive) {
      dispatch(setVoucheringCheckedClaimsIds([]));
      dispatch(setVoucheringPayableAmount(0));
    } else {
      const currentIds = totalSystemClaimsVouchering.map((item: InvoiceData) => item.id);
      const updatedSelectedIds = [...new Set([...voucheringCheckedClaimsIds, ...currentIds])];

      const totalAmount = totalSystemClaimsVouchering.reduce(
        (acc: number, item: InvoiceData) => acc + (item.totalAmount ?? 0),
        0,
      );

      dispatch(setVoucheringCheckedClaimsIds(updatedSelectedIds as string[]));
      dispatch(setVoucheringPayableAmount(voucheringPayableAmount + totalAmount));
    }
    dispatch(setIsVoucheringSelectAllActive(!isVoucheringSelectAllActive));
  };

  const allocatedAmount = useAppSelector((state) => state.vouchering.allocatedAmount);
  const selectedAllocationSelectionMethod = useAppSelector(
    (state) => state.voucheringAllocation.selectedAllocationSelectionMethod,
  );

  const isUserSelection =
    selectedAllocationSelectionMethod === AllocationSelectionMethod.UserSelection ||
    allocatedAmount > 0;

  const shouldDisplayCheckBox = isUserSelection || isEditing;

  return (
    <thead className="text-left">
      <tr className="bg-[#F9FAFB] text-[13px]">
        {shouldDisplayCheckBox && (
          <th className="whitespace-nowrap px-4 py-2 uppercase">
            <input
              className="form-checkbox"
              type="checkbox"
              checked={isVoucheringSelectAllActive}
              onChange={handleSelectAll}
              title="Select All"
            />
          </th>
        )}
        <TableHeaderItem className="text-xs" item="Invoice No" />
        <TableHeaderItem className="text-xs" item="Account Name" />
        <TableHeaderItem className="text-xs" item="Amount" />
        <TableHeaderItem className="text-xs" item="Date" />
        <TableHeaderItem className="text-xs" item="Age" />
      </tr>
    </thead>
  );
}
