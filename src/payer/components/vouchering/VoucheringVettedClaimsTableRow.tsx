import { ChangeEvent } from "react";
import { InvoiceData } from "../../lib/types/claims/invoice";
import { formatDate, formatValue } from "../../lib/Utils";
import { getDifferenceInDaysFinance } from "../../utils/utils";
import TableDataItem from "../ui/table/TableDataItem";
import { useAppSelector } from "../../store/hooks";
import { AllocationSelectionMethod } from "./modals/ui/DetailedCheckbox";

interface VoucheringVettedClaimsTableRowProps {
  invoice: InvoiceData;
  checkedClaimsIds: string[];
  handleClick: (e: ChangeEvent<HTMLInputElement>, invoice: InvoiceData) => void;
  isEditing?: boolean | undefined;
}

export default function VoucheringVettedClaimsTableRow({
  invoice,
  checkedClaimsIds,
  handleClick,
  isEditing = false,
}: VoucheringVettedClaimsTableRowProps) {
  const allocatedAmount = useAppSelector((state) => state.vouchering.allocatedAmount);
  const selectedAllocationSelectionMethod = useAppSelector(
    (state) => state.voucheringAllocation.selectedAllocationSelectionMethod,
  );

  const daysDifference = getDifferenceInDaysFinance(
    formatDate(invoice.invoiceDate),
    formatDate(new Date(Date.now())),
  );

  const isUserSelection =
    selectedAllocationSelectionMethod === AllocationSelectionMethod.UserSelection ||
    allocatedAmount > 0;

  const shouldDisplayCheckBox = isUserSelection || isEditing;

  return (
    <tr key={invoice.id} className="">
      {shouldDisplayCheckBox && (
        <td className="whitespace-nowrap border-b border-[#EAECF0] px-4 py-2 uppercase">
          <div className="flex items-center">
            <label className="inline-flex">
              <span className="sr-only">Select</span>
              <input
                id={invoice?.id as string}
                className="form-checkbox"
                type="checkbox"
                onChange={(e) => handleClick(e, invoice)}
                checked={checkedClaimsIds.includes(invoice.id as string)}
              />
            </label>
          </div>
        </td>
      )}
      <TableDataItem className="text-xs" item={invoice?.invoiceNumber} />
      <TableDataItem className="text-xs" item={invoice?.providerName} />
      <TableDataItem className="text-xs" item={formatValue(invoice?.totalAmount)} />
      <TableDataItem className="text-xs" item={formatDate(invoice?.invoiceDate)} />
      <TableDataItem
        className="text-sm"
        item={`${daysDifference > 0 ? daysDifference : ""}${" "}
        ${daysDifference <= 0 ? "Not Due" : daysDifference === 1 ? "Day" : "Days"}
        `}
      />
    </tr>
  );
}
