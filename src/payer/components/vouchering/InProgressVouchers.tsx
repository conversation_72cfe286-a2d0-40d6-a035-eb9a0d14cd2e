import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { useDeleteVoucherMutation, useGetAllVouchersQuery } from "../../api/features/claimsApi";
import {
  setIsUpdatingVoucherSuccess,
  setIsVoucherEditingModalOpen,
  setSelectedEditingMethod,
  setVoucherReversalWarningModalOpen,
} from "../../features/claims/voucherEditingSlice";
import { DeleteVoucherPayload, Voucher } from "../../lib/types/vouchering/voucher";
import UserService from "../../services/UserService";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import NoClaimsVouchering from "../illustrations/NoClaimsVouchering";
import EmptyState from "../ui/EmptyState";
import PrimaryPagination from "../ui/pagination/PrimaryPagination";
import ProcessingVouchersModal from "./modals/ProcessingVouchersModal";
import ReverseVoucherWarningModal from "./modals/ReverseVoucherWarningModal";
import VoucherEditingModal from "./modals/VoucherEditingModal";
import VoucherUpdatedSuccessfullyModal from "./modals/VoucherUpdatedSuccessfullyModal";
import VoucherCard from "./VoucherCard";

export default function InProgressVouchers() {
  const [pageNumber, setPageNumber] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [vouchers, setVouchers] = useState<Array<Voucher>>([]);
  const isVoucherEditingModalOpen = useAppSelector(
    (state) => state.voucherEditing.isVoucherEditingModalOpen,
  );
  const selectedVoucherId = useAppSelector((state) => state.voucherEditing.voucherId);
  const voucherReversalWarningModalOpen = useAppSelector(
    (state) => state.voucherEditing.voucherReversalWarningModalOpen,
  );
  const isUpdatingVoucherSuccess = useAppSelector(
    (state) => state.voucherEditing.isUpdatingVoucherSuccess,
  );

  const username = UserService.getUsername()?.toString();
  const dispatch = useAppDispatch();

  const [reverseVoucher, { isLoading: isReversingVoucher, isSuccess: isReversingVoucherSuccess }] =
    useDeleteVoucherMutation();

  const reverseVoucherPayload: DeleteVoucherPayload = {
    voucherId: selectedVoucherId,
    body: {
      actionedBy: username,
    },
  };

  const handleVoucherReverse = async () => {
    try {
      dispatch(setVoucherReversalWarningModalOpen(false));
      await reverseVoucher(reverseVoucherPayload).unwrap();
      dispatch(setSelectedEditingMethod(null));
    } catch (error) {
      toast.error(error as string);
      console.error(error);
    }
  };

  const handlePageNumberClick = (page: number) => {
    setPageNumber(page);
  };

  const handleSizeChange = (size: number) => {
    setPageSize(size);
    setPageNumber(1);
  };

  const handleVoucherEditingModalClose = () => {
    dispatch(setIsVoucherEditingModalOpen(false));
  };

  const payerId: string = UserService.getPayer()?.tokenParsed?.["payerId"];

  const { data, isLoading, isFetching, error } = useGetAllVouchersQuery({
    payerId,
    page: pageNumber,
    size: pageSize,
    interimSave: true,
  });

  const totalElements = data?.data.totalElements as number;
  const totalPages = data?.data.totalPages as number;

  useEffect(() => {
    if (data?.data.content) {
      setVouchers(data?.data.content as Voucher[]);
    }
  }, [data?.data.content]);

  useEffect(() => {
    if (isReversingVoucherSuccess) {
      dispatch(setIsUpdatingVoucherSuccess(true));
    }
  }, [dispatch, isReversingVoucherSuccess]);

  return (
    <div className={`flex flex-col ${vouchers.length === 0 && "pt-16"}`}>
      {isLoading || isFetching ? (
        <div className="flex items-center justify-center space-x-2 self-center py-8">
          <LoadingIcon className="h-6 w-6 text-blue-400" />
          <p className="text-blue-700">Loading...</p>
        </div>
      ) : error ? (
        <div className="flex items-center justify-center self-center py-8">
          <p className="text-red-700">Error loading vouchers. Try again later.</p>
        </div>
      ) : vouchers?.length === 0 ? (
        <EmptyState
          illustration={<NoClaimsVouchering />}
          message={{
            title: "No vouchers available",
            description:
              "You haven't created any vouchers yet. Once you start creating vouchers, they'll be displayed here for your review and further actions...",
          }}
        />
      ) : (
        !isLoading &&
        !isFetching && (
          <section className="flex flex-col space-y-4">
            <div className="flex flex-col space-y-4 pt-8">
              {vouchers.map((voucher) => (
                <VoucherCard voucher={voucher} isVoucherComplete={false} />
              ))}
            </div>
            <div className="self-start py-4">
              <PrimaryPagination
                totalElements={totalElements}
                totalPages={totalPages ?? 1}
                pageNumber={pageNumber}
                pageSize={pageSize}
                onPageNumberClick={handlePageNumberClick}
                onSizeChange={handleSizeChange}
              />
            </div>
          </section>
        )
      )}
      {/*modals*/}
      <section>
        <VoucherEditingModal
          onClose={handleVoucherEditingModalClose}
          isSuccessModalOpen={isVoucherEditingModalOpen}
        />
        <ProcessingVouchersModal
          isProcessingVouchersModalOpen={isReversingVoucher}
          onClose={() => true}
        />
        <ReverseVoucherWarningModal
          onClose={() => dispatch(setVoucherReversalWarningModalOpen(false))}
          isDeleteBatchModalOpen={voucherReversalWarningModalOpen}
          handleReverseVoucher={() => handleVoucherReverse()}
        />
        <VoucherUpdatedSuccessfullyModal
          isSuccessModalOpen={isUpdatingVoucherSuccess}
          isReversal
          onClose={() => dispatch(setIsUpdatingVoucherSuccess(false))}
        />
      </section>
    </div>
  );
}
