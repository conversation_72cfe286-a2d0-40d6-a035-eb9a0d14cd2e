import { CheckCircleIcon, ClockIcon } from "@heroicons/react/24/outline";
import progressClock from "../../assets/progress_clock.svg";
import {
  setIsBackButtonVisible,
  setVoucheringProgress,
} from "../../features/claims/voucheringSlice";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import ButtonGroup from "../ui/ButtonGroup";

export enum VoucheringProgress {
  Unallocated = "Unallocated",
  InProgress = "InProgress",
  Completed = "Completed",
}

interface VoucheringProgressItem {
  title: VoucheringProgress;
  icon: JSX.Element;
}

const voucheringProgressItems: VoucheringProgressItem[] = [
  {
    title: VoucheringProgress.Unallocated,
    icon: <ClockIcon className="h-5 w-5" />,
  },
  {
    title: VoucheringProgress.InProgress,
    icon: <img src={progressClock} alt="progress clock" className="h-5 w-5" />,
  },
  {
    title: VoucheringProgress.Completed,
    icon: <CheckCircleIcon className="h-5 w-5" />,
  },
];

export default function VoucheringProgressBar() {
  const dispatch = useAppDispatch();
  const voucheringProgress = useAppSelector((state) => state.vouchering.voucheringProgress);
  const mainVoucheringCriteria = useAppSelector((state) => state.vouchering.mainVoucheringCriteria);

  const handleVoucheringProgressClick = (title: VoucheringProgress) => {
    dispatch(setVoucheringProgress(title));
    if (title !== VoucheringProgress.Unallocated) {
      dispatch(setIsBackButtonVisible(false));
    } else if (mainVoucheringCriteria) {
      dispatch(setIsBackButtonVisible(true));
    }
  };
  return (
    <div className="flex w-fit items-center">
      <ButtonGroup
        options={voucheringProgressItems}
        setActiveOption={handleVoucheringProgressClick}
        activeOption={voucheringProgress}
        titleClassName="text-sm"
      />
    </div>
  );
}
