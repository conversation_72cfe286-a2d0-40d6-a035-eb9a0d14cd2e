import { ChangeEvent, useEffect, useState } from "react";
import { useLocation, useParams } from "react-router-dom";
import { Empty } from "~lib/components";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { PAGE_SIZES } from "~lib/constants";
import {
  useCalculateInvoicesAmountMutation,
  useGetAllClaimsQuery,
} from "../../api/features/claimsApi";
import { setCurrentVoucherAmount } from "../../features/claims/voucherEditingSlice";
import {
  setIsCalculatingVoucherAmount,
  setIsVoucheringSelectAllActive,
  setTotalSystemClaimsVouchering,
  setUniqueCheckedClaimsIds,
  setVettedClaims,
  setVoucherAmount,
  setVoucheringCheckedClaimsIds,
  setVoucheringPayableAmount,
} from "../../features/claims/voucheringSlice";
import {
  InvoiceBatchStatus,
  InvoiceData,
  InvoiceStatus,
  InvoiceVettingStatus,
} from "../../lib/types/claims/invoice";
import UserService from "../../services/UserService";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import { isValidFilterName, isValidId } from "../../utils/utils";
import PrimaryPagination from "../ui/pagination/PrimaryPagination";
import { VoucherEditingType } from "./modals/VoucherEditingModal";
import VettedClaimsTableHeader from "./VettedClaimsTableHeader";
import VoucheringVettedClaimsTableRow from "./VoucheringVettedClaimsTableRow";

export default function VoucheringVettedClaimsTable({ isEditing }: { isEditing?: boolean }) {
  const { pathname } = useLocation();
  const { voucherId, editType } = useParams();
  const pattern = /^\/finance-and-accounting\/vouchering\/edit\/(\d+)\/(\w+)$/;

  const [page, setPage] = useState(1);
  const [size, setSize] = useState(PAGE_SIZES[0] as number);
  const dispatch = useAppDispatch();

  const catalogIds = useAppSelector((state) => state.vouchering.catalogIds);
  const selectedAgeBandId = useAppSelector((state) => state.vouchering.selectedAgeBandId);
  const selectedRegion = useAppSelector((state) => state.vouchering.selectedRegion);
  const selectedAccount = useAppSelector((state) => state.vouchering.selectedAccount);
  const vettedClaims = useAppSelector((state) => state.vouchering.vettedClaims);
  const voucheringCheckedClaimsIds = useAppSelector(
    (state) => state.vouchering.voucheringCheckedClaimsIds,
  );
  const voucheringPayableAmount = useAppSelector(
    (state) => state.vouchering.voucheringPayableAmount,
  );
  const selectedBenefitId = useAppSelector((state) => state.vouchering.selectedBenefitId);
  const selectedScheme = useAppSelector((state) => state.vouchering.selectedScheme);
  const currentVoucherAmount = useAppSelector((state) => state.voucherEditing.currentVoucherAmount);

  const handleClick = (e: ChangeEvent<HTMLInputElement>, invoice: InvoiceData) => {
    const { checked } = e.target;
    if (checked) {
      dispatch(
        setVoucheringCheckedClaimsIds([...voucheringCheckedClaimsIds, invoice.id as string]),
      );
      pattern.test(pathname)
        ? editType === VoucherEditingType.Add
          ? dispatch(setCurrentVoucherAmount(currentVoucherAmount + (invoice.totalAmount ?? 0)))
          : dispatch(setCurrentVoucherAmount(currentVoucherAmount - (invoice.totalAmount ?? 0)))
        : dispatch(
            setVoucheringPayableAmount(voucheringPayableAmount + (invoice.totalAmount ?? 0)),
          );
    } else {
      dispatch(
        setVoucheringCheckedClaimsIds(
          voucheringCheckedClaimsIds.filter((item: string) => item !== invoice.id),
        ),
      );
      pattern.test(pathname)
        ? editType === VoucherEditingType.Add
          ? dispatch(setCurrentVoucherAmount(currentVoucherAmount - (invoice.totalAmount ?? 0)))
          : dispatch(setCurrentVoucherAmount(currentVoucherAmount + (invoice.totalAmount ?? 0)))
        : dispatch(
            setVoucheringPayableAmount(voucheringPayableAmount - (invoice.totalAmount ?? 0)),
          );
      dispatch(setIsVoucheringSelectAllActive(false));
    }
  };

  const payerId: string = UserService.getPayer()?.tokenParsed?.["payerId"];
  const batchStatus: InvoiceBatchStatus = "BATCHED";
  const vettingStatuses: Array<InvoiceVettingStatus> = ["PARTIAL", "APPROVED"];
  const invoiceStatuses: Array<InvoiceStatus> = ["BALANCE_DEDUCTED"];
  const withVoucher = editType === VoucherEditingType.Remove;
  const onlyProviderWithAccounts = true;

  /* paginated claims **/
  const { data, isLoading, isFetching, error } = useGetAllClaimsQuery({
    payerId,
    ...(isValidId(selectedAgeBandId) && {
      ageBandId: selectedAgeBandId,
    }),
    ...(isValidId(selectedScheme) && {
      planIds: [selectedScheme],
    }),
    ...(isValidId(selectedBenefitId) && {
      catalogIds: [selectedBenefitId],
    }),
    region: selectedRegion,
    vettingStatuses,
    batchStatus,
    invoiceStatuses,
    withVoucher,
    onlyProviderWithAccounts,
    size,
    page,
    providerAccountIds: selectedAccount ? [Number(selectedAccount)] : [],
    ...(pattern.test(pathname) &&
      editType === VoucherEditingType.Remove && { voucherIds: [Number(voucherId)] }),
  });

  const totalElements = data?.data.totalElements as number;
  const totalPages = data?.data.totalPages as number;

  /* all system claims **/
  const { data: totalSystemClaimsVoucheringResponse } = useGetAllClaimsQuery({
    payerId,
    ...(isValidId(selectedAgeBandId) && {
      ageBandId: selectedAgeBandId,
    }),
    ...(isValidId(selectedScheme) && {
      planIds: [selectedScheme],
    }),
    ...(isValidId(selectedBenefitId) && {
      catalogIds: [selectedBenefitId],
    }),
    region: selectedRegion,
    vettingStatuses,
    batchStatus,
    invoiceStatuses,
    withVoucher,
    onlyProviderWithAccounts,
    size: 100_000_000,
    page: 1,
    providerAccountIds: selectedAccount ? [Number(selectedAccount)] : [],
    ...(pattern.test(pathname) &&
      editType === VoucherEditingType.Remove && { voucherIds: [Number(voucherId)] }),
  });

  const handlePageNumberClick = (page: number) => {
    setPage(page);
    dispatch(setIsVoucheringSelectAllActive(false));
  };

  const handleSizeChange = (size: number) => {
    setSize(size);
    setPage(1);
    dispatch(setIsVoucheringSelectAllActive(false));
  };

  /* invoice total calculation */
  const [calculateInvoicesTotal, { isLoading: isCalculatingVoucherAmount }] =
    useCalculateInvoicesAmountMutation();

  const calculateInvoicesAmount = async () => {
    dispatch(setVoucherAmount(0));
    try {
      const res = await calculateInvoicesTotal({
        payerId,
        vettingStatuses,
        catalogIds,
        batchStatus,
        invoiceStatuses,
        withVoucher,
        onlyProviderWithAccounts,
        providerAccountIds: selectedAccount ? [Number(selectedAccount)] : [],
        ...(isValidFilterName(selectedRegion) && { region: selectedRegion }),
        ...(isValidId(selectedAgeBandId) && { ageBandId: selectedAgeBandId }),
        ...(isValidId(selectedBenefitId) && { catalogIds: [selectedBenefitId] }),
        ...(isValidId(selectedScheme) && { planIds: [selectedScheme] }),
        ...(isValidId(selectedAccount) && { providerAccountIds: [Number(selectedAccount)] }),
        ...(voucherId && isValidId(voucherId) && { voucherIds: [Number(voucherId)] }),
        ...(pattern.test(pathname) &&
          editType === VoucherEditingType.Remove && { voucherIds: [Number(voucherId)] }),
      }).unwrap();
      dispatch(setVoucherAmount(res.data));
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (data?.data.content) {
      dispatch(setVettedClaims(data?.data.content));
    }
  }, [data?.data.content, dispatch]);

  useEffect(() => {
    dispatch(setUniqueCheckedClaimsIds([...new Set(voucheringCheckedClaimsIds)] as string[]));
  }, [voucheringCheckedClaimsIds, dispatch]);

  useEffect(() => {
    calculateInvoicesAmount();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data?.data.content]);

  useEffect(() => {
    dispatch(setIsCalculatingVoucherAmount(isCalculatingVoucherAmount));
  }, [isCalculatingVoucherAmount, dispatch]);

  useEffect(() => {
    if (totalSystemClaimsVoucheringResponse?.data?.content) {
      dispatch(
        setTotalSystemClaimsVouchering(
          totalSystemClaimsVoucheringResponse?.data?.content as Array<InvoiceData>,
        ),
      );
    }
  }, [totalSystemClaimsVoucheringResponse, dispatch]);

  return (
    <section>
      <section className={`${isLoading || (isFetching && "py-40")}`}>
        {isLoading || isFetching ? (
          <div className="flex items-center justify-center space-x-2 self-center py-8">
            <LoadingIcon className="h-6 w-6 text-blue-400" />
            <p className="text-blue-700">Loading...</p>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center self-center py-8">
            <p className="text-red-700">Error loading claims. Try again later.</p>
          </div>
        ) : (
          vettedClaims?.length === 0 && (
            <div className="self-center py-20">
              <Empty message="Claims not Found" />
            </div>
          )
        )}
      </section>
      {vettedClaims?.length > 0 && !isLoading && !isFetching && !error && (
        <>
          <table className="w-full table-auto">
            <VettedClaimsTableHeader isEditing={isEditing} />
            <tbody>
              {vettedClaims?.map((invoice: InvoiceData) => (
                <VoucheringVettedClaimsTableRow
                  key={invoice.id}
                  handleClick={handleClick}
                  checkedClaimsIds={voucheringCheckedClaimsIds}
                  invoice={invoice}
                  isEditing={isEditing}
                />
              ))}
            </tbody>
          </table>
          <div className="self-start py-4">
            <PrimaryPagination
              pageSize={size}
              totalElements={totalElements}
              totalPages={totalPages ?? 1}
              pageNumber={page}
              onPageNumberClick={handlePageNumberClick}
              onSizeChange={handleSizeChange}
            />
          </div>
        </>
      )}
    </section>
  );
}
