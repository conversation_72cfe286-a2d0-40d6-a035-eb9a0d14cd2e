import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { useGetAllAgeBandsQuery } from "../../api/features/claimsApi";
import {
  useGetMembershipAccountsQuery,
  useGetMembershipBenefitsCatalogQuery,
  useGetMembershipRegionsQuery,
  useGetMembershipSchemesQuery,
} from "../../api/features/membershipApi";
import {
  setCatalogIds,
  setIsVoucheringSelectAllActive,
  setSelectedAccount,
  setSelectedAccountName,
  setSelectedAgeBandId,
  setSelectedAgeBandName,
  setSelectedBenefitId,
  setSelectedBenefitName,
  setSelectedRegion,
  setSelectedScheme,
  setSelectedSchemeIds,
  setSelectedSchemeName,
  setVoucherAmount,
  setVoucheringCheckedClaimsIds,
} from "../../features/claims/voucheringSlice";
import { AgeBand, AgeBandType } from "../../lib/types/claims/ageBand";
import { MembershipAccount } from "../../lib/types/membership/memberAccount";
import { MembershipBenefitCatalog } from "../../lib/types/membership/memberBenefit";
import { MembershipRegion } from "../../lib/types/membership/memberRegion";
import { MembershipScheme } from "../../lib/types/membership/memberScheme";
import { VoucheringCriteria } from "../../lib/types/vouchering/claimVoucheringFilter";
import UserService from "../../services/UserService";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import Button from "../ui/Button";
import { Select, SelectOption } from "../ui/input/Select";

export default function ClaimsVoucheringFilter() {
  const { pathname } = useLocation();
  const pattern = /^\/finance-and-accounting\/vouchering\/edit\/(\d+)\/(\w+)$/;
  const _isEditingVoucher = pattern.test(pathname);
  const selectedScheme = useAppSelector((state) => state.vouchering.selectedScheme);
  const selectedAccount = useAppSelector((state) => state.vouchering.selectedAccount);
  const selectedRegion = useAppSelector((state) => state.vouchering.selectedRegion);
  const selectedAgeBandId = useAppSelector((state) => state.vouchering.selectedAgeBandId);
  const selectedBenefitId = useAppSelector((state) => state.vouchering.selectedBenefitId);
  const [payerRegions, setPayerRegions] = useState<MembershipRegion[]>([]);
  const [payerBenefits, setPayerBenefits] = useState<MembershipBenefitCatalog[]>([]);
  const [payerAgeBands, setPayerAgeBands] = useState<AgeBand[]>([]);
  const [payerAccounts, setPayerAccounts] = useState<MembershipAccount[]>([]);
  const [payerSchemes, setPayerSchemes] = useState<MembershipScheme[]>([]);
  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const { data: membershipRegionsResponse } = useGetMembershipRegionsQuery(payerId);
  const { data: membershipBenefitsResponse } = useGetMembershipBenefitsCatalogQuery(payerId);
  const { data: membershipAccountsResponse } = useGetMembershipAccountsQuery(payerId);
  const { data: membershipSchemesResponse } = useGetMembershipSchemesQuery(payerId);
  const bandType: AgeBandType = "FINANCIAL";
  const { data: ageBandsResponse } = useGetAllAgeBandsQuery({ bandType });

  const dispatch = useAppDispatch();

  function mapToSelectOptions<T extends { id: string | number; name: string }>(
    options: T[] | undefined,
    _placeholder: string,
  ): SelectOption[] {
    return options
      ? options.map((option) => ({
          key: option.id,
          value: option.id,
          label: option.name,
        }))
      : [];
  }

  useEffect(() => {
    if (membershipRegionsResponse?.data) {
      setPayerRegions(membershipRegionsResponse?.data as MembershipRegion[]);
    }
    if (membershipBenefitsResponse?.data) {
      setPayerBenefits(membershipBenefitsResponse?.data);
    }
    if (ageBandsResponse?.data) {
      setPayerAgeBands(ageBandsResponse?.data as AgeBand[]);
    }
    if (membershipAccountsResponse?.data) {
      setPayerAccounts(membershipAccountsResponse?.data as MembershipAccount[]);
    }
    if (membershipSchemesResponse?.data) {
      setPayerSchemes(membershipSchemesResponse?.data as MembershipScheme[]);
    }
  }, [
    membershipRegionsResponse,
    setPayerRegions,
    membershipBenefitsResponse,
    setPayerBenefits,
    ageBandsResponse,
    setPayerAgeBands,
    membershipAccountsResponse,
    setPayerAccounts,
    membershipSchemesResponse,
    setPayerSchemes,
  ]);

  const filterSelected =
    selectedAgeBandId || selectedAccount || selectedScheme || selectedBenefitId || selectedRegion;

  const clearFilters = () => {
    dispatch(setSelectedAgeBandId(0));
    dispatch(setSelectedBenefitId(0));
    dispatch(setSelectedScheme(""));
    dispatch(setSelectedAccount(""));
    dispatch(setVoucherAmount(0));
    dispatch(setSelectedSchemeIds([]));
    dispatch(setSelectedRegion(""));
    dispatch(setCatalogIds([]));
    dispatch(setSelectedAccount(""));
    dispatch(setSelectedSchemeName(""));
    dispatch(setSelectedBenefitName(""));
    dispatch(setSelectedAgeBandName(""));
    dispatch(setSelectedAccountName(""));
    dispatch(setVoucheringCheckedClaimsIds([]));
  };

  const handleSelect = (criteria: VoucheringCriteria, selectedId: string | number) => {
    switch (criteria) {
      case VoucheringCriteria.REGION: {
        const found = payerRegions.find((r) => String(r.id) === String(selectedId));
        dispatch(setSelectedRegion(found?.name || ""));
        break;
      }
      case VoucheringCriteria.ACCOUNT: {
        const found = payerAccounts.find((a) => String(a.id) === String(selectedId));
        dispatch(setSelectedAccount(found?.id || ""));
        dispatch(setSelectedAccountName(found?.accountName || ""));
        break;
      }
      case VoucheringCriteria.BENEFIT: {
        const found = payerBenefits.find((b) => String(b.id) === String(selectedId));
        dispatch(setSelectedBenefitId(Number(found?.id) || 0));
        dispatch(setSelectedBenefitName(found?.name || ""));
        dispatch(setCatalogIds([Number(found?.id)]));
        break;
      }
      case VoucheringCriteria.AGING: {
        const found = payerAgeBands.find((a) => String(a.id) === String(selectedId));
        dispatch(setSelectedAgeBandName(found?.name || ""));
        dispatch(setSelectedAgeBandId(Number(found?.id) || 0));
        break;
      }
      case VoucheringCriteria.SCHEME: {
        const found = payerSchemes.find((s) => String(s.id) === String(selectedId));
        dispatch(setSelectedSchemeName(found?.name || ""));
        dispatch(setSelectedSchemeIds(found?.id ? [found.id] : []));
        dispatch(setSelectedScheme(found?.id || ""));
        break;
      }
    }
    dispatch(setIsVoucheringSelectAllActive(false));
  };

  const mainVoucheringCriteria = useAppSelector((state) => state.vouchering.mainVoucheringCriteria);

  function mapVoucheringCriteriaToNumber(criteria: VoucheringCriteria) {
    switch (criteria) {
      case VoucheringCriteria.AGING:
        return 1;
      case VoucheringCriteria.BENEFIT:
        return 2;
      case VoucheringCriteria.ACCOUNT:
        return 3;
      case VoucheringCriteria.SCHEME:
        return 4;
      case VoucheringCriteria.REGION:
        return 5;
    }
  }

  const criteriaComponents = {
    [VoucheringCriteria.REGION]: (
      <div className="flex flex-col space-y-1.5" key="region">
        <label htmlFor="region" className="text-sm font-medium text-midGray">
          {VoucheringCriteria.REGION}
        </label>
        <Select
          value={payerRegions.find((r) => r.name === selectedRegion)?.id || ""}
          options={mapToSelectOptions(payerRegions, "Select the region")}
          placeholder="Select the region"
          onChange={(selectedId) => handleSelect(VoucheringCriteria.REGION, selectedId)}
        />
      </div>
    ),
    [VoucheringCriteria.ACCOUNT]: (
      <div className="flex flex-col space-y-1.5" key="account">
        <label htmlFor="account" className="text-sm font-medium text-midGray">
          {VoucheringCriteria.ACCOUNT}
        </label>
        <Select
          value={selectedAccount || ""}
          options={mapToSelectOptions(
            payerAccounts.map((a) => ({ id: a.id, name: a.accountName })),
            "Select the account",
          )}
          placeholder="Select the account"
          onChange={(selectedId) => handleSelect(VoucheringCriteria.ACCOUNT, selectedId)}
        />
      </div>
    ),
    [VoucheringCriteria.AGING]: (
      <div className="flex flex-col space-y-1.5" key="aging">
        <label htmlFor="aging" className="text-sm font-medium text-midGray">
          {VoucheringCriteria.AGING}
        </label>
        <Select
          value={selectedAgeBandId || ""}
          options={mapToSelectOptions(payerAgeBands, "Select the aging criteria")}
          placeholder="Select the aging criteria"
          onChange={(selectedId) => handleSelect(VoucheringCriteria.AGING, selectedId)}
        />
      </div>
    ),
    [VoucheringCriteria.SCHEME]: (
      <div className="flex flex-col space-y-1.5" key="scheme">
        <label htmlFor="scheme" className="text-sm font-medium text-midGray">
          {VoucheringCriteria.SCHEME}
        </label>
        <Select
          value={selectedScheme || ""}
          options={mapToSelectOptions(payerSchemes, "Select the scheme")}
          placeholder="Select the scheme"
          onChange={(selectedId) => handleSelect(VoucheringCriteria.SCHEME, selectedId)}
        />
      </div>
    ),
    [VoucheringCriteria.BENEFIT]: (
      <div className="flex flex-col space-y-1.5" key="benefit">
        <label htmlFor="benefit" className="text-sm font-medium text-midGray">
          {VoucheringCriteria.BENEFIT}
        </label>
        <Select
          value={selectedBenefitId || ""}
          options={mapToSelectOptions(payerBenefits, "Select the benefit")}
          placeholder="Select the benefit"
          onChange={(selectedId) => handleSelect(VoucheringCriteria.BENEFIT, selectedId)}
        />
      </div>
    ),
  };

  const components = Object.entries(criteriaComponents).map(([key, component]) => ({
    key: key as VoucheringCriteria,
    component,
  }));

  const sortedComponents = components.sort((a, b) => {
    const aValue = a.key === mainVoucheringCriteria ? 0 : mapVoucheringCriteriaToNumber(a.key);
    const bValue = b.key === mainVoucheringCriteria ? 0 : mapVoucheringCriteriaToNumber(b.key);
    return aValue - bValue;
  });

  return (
    <section className="grid grid-cols-6  items-center space-x-2 py-6">
      {sortedComponents.map(({ component }) => component)}
      <Button
        className="self-end whitespace-nowrap"
        disabled={!filterSelected}
        onClick={clearFilters}
      >
        Clear Filter
      </Button>
    </section>
  );
}
