import { ChangeEvent } from "react";
import {
  setIsBackButtonVisible,
  setMainVoucheringCriteria,
} from "../../features/claims/voucheringSlice";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import { VoucheringCriteria } from "../../lib/types/vouchering/claimVoucheringFilter";

export default function VoucheringMainCriteriaFilter() {
  const dispatch = useAppDispatch();
  const mainVoucheringCriteria = useAppSelector((state) => state.vouchering.mainVoucheringCriteria);
  const handleMainVoucheringCriteriaChange = (e: ChangeEvent<HTMLSelectElement>) => {
    dispatch(setMainVoucheringCriteria(e.target.value as VoucheringCriteria));
    dispatch(setIsBackButtonVisible(true));
  };

  return (
    <div className="flex items-center space-x-6">
      <label htmlFor="filter" className="mr-2 inline-block text-lg text-darkGray">
        Select the main vouchering criteria
      </label>
      <select
        id="filter"
        value={mainVoucheringCriteria as string}
        onChange={handleMainVoucheringCriteriaChange}
        className="w-[50%] rounded-lg border border-gray-300 px-4 py-1.5 text-base"
      >
        <option className="whitespace-nowrap text-base text-darkGray" value="" hidden>
          Select criteria
        </option>
        {Object.values(VoucheringCriteria).map((criteria) => (
          <option key={criteria} value={criteria} className="whitespace-nowrap text-base">
            {criteria}
          </option>
        ))}
      </select>
    </div>
  );
}
