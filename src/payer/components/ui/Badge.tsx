import { cn } from "~lib/utils/cn";

export type BadgeColor = "red" | "blue" | "green" | "yellow" | "gray";

type Props = {
  color: BadgeColor;
  text: string;
  hasDot?: boolean;
  className?: string;
  textClassName?: string;
  title?: string | undefined;
};

export default function Badge({
  color,
  text,
  hasDot = false,
  className,
  textClassName,
  title,
}: Props) {
  return (
    <div
      title={title}
      data-testid="badge"
      className={cn(
        "flex h-8 w-fit items-center space-x-1 rounded-md px-3",
        {
          "bg-faintRed": color === "red",
          "bg-faintGreen": color === "green",
          "bg-faintYellow": color === "yellow",
          "bg-faintBlue": color === "blue",
          "bg-lightGray/20": color === "gray",
        },
        className,
      )}
    >
      {hasDot && (
        <div
          className={`rounded-full p-1 
            ${color === "red" && "bg-customRed"} 
            ${color === "green" && "bg-customGreen"}
            ${color === "yellow" && "bg-goldenYellow"}
            ${color === "blue" && "bg-btnBlue"}
            ${color === "gray" && "bg-midGray"}
          `}
        ></div>
      )}
      <p
        className={cn(
          `text-sm font-medium`,
          color === "red" && "text-darkRed",
          color === "green" && "text-darkGreen",
          color === "yellow" && "text-goldenBrown",
          color === "blue" && "text-txtBlue",
          color === "gray" && "text-darkGray",
          textClassName,
        )}
      >
        {text}
      </p>
    </div>
  );
}
