import {
  setIsUpdatingUserRoles,
  toggleUserSwitchRole,
} from "../../features/access-control/accessControlSlice";
import { useAppDispatch } from "../../store/hooks";

interface SwitchProps {
  checked?: boolean;
  onChange?: (checked: boolean) => void;
  disabled?: boolean;
  label?: string;
  className?: string;
  roleNames: Array<string>;
}

export default function AccessRoleSwitch({
  checked = false,
  onChange,
  disabled = false,
  label,
  className = "",
  roleNames,
}: SwitchProps) {
  const dispatch = useAppDispatch();

  const handleToggle = () => {
    if (disabled) return;

    const newChecked = !checked;
    dispatch(toggleUserSwitchRole({ roleNames: roleNames, isAdding: newChecked }));
    dispatch(setIsUpdatingUserRoles(true));
    if (onChange) {
      onChange(newChecked);
    }
  };

  return (
    <div className={`flex items-center ${className}`}>
      {label && <label className="mr-2">{label}</label>}
      <div
        role="switch"
        aria-checked={checked}
        tabIndex={0}
        onClick={handleToggle}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            handleToggle();
            // Prevent scrolling when space is pressed
            e.preventDefault();
          }
        }}
        className={`relative inline-flex cursor-pointer items-center ${
          disabled ? "cursor-not-allowed opacity-50" : ""
        }`}
      >
        <input
          type="checkbox"
          checked={checked}
          onChange={handleToggle}
          className="sr-only"
          disabled={disabled}
        />
        <div
          className={`h-5 w-10 rounded-full transition-colors duration-200 ease-in-out ${
            checked ? "bg-blue-600" : "bg-gray-300"
          }`}
        >
          <span
            className={`absolute left-1 top-[2px] h-4 w-4 rounded-full transition-transform duration-200 ease-in-out ${
              checked ? "translate-x-4 transform bg-white" : "bg-white"
            }`}
          />
        </div>
      </div>
    </div>
  );
}
