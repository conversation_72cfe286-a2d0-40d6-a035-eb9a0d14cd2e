import { fireEvent, render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import {
  setIsOptionsListOpen,
  setSearchTerm,
  //   setSelectedOption,
} from "../../../features/filters/filterSelectSlice";
import { store } from "../../../store";
import FilterSelect from "./FilterSelect";

describe("FilterSelect Component", () => {
  let options: { id: number; name: string }[];

  beforeEach(() => {
    options = [
      { id: 1, name: "Option 1" },
      { id: 2, name: "Option 2" },
    ];

    // Mock store.dispatch to track dispatch calls
    vi.spyOn(store, "dispatch").mockImplementation(vi.fn());
  });

  afterEach(() => {
    vi.restoreAllMocks(); // Clean up mocks after each test
  });

  it("renders input field with placeholder", () => {
    render(
      <Provider store={store}>
        <FilterSelect
          id="testSelect"
          options={options}
          placeholder="Select an option"
          onSelect={vi.fn()}
          name="test"
          idSelector={(opt) => opt.id}
          nameSelector={(opt) => opt.name}
        />
      </Provider>,
    );

    expect(screen.getByPlaceholderText("Select an option")).toBeInTheDocument();
  });

  it("opens dropdown when input is focused", () => {
    render(
      <Provider store={store}>
        <FilterSelect
          id="testSelect"
          options={options}
          placeholder="Select an option"
          onSelect={vi.fn()}
          name="test"
          idSelector={(opt) => opt.id}
          nameSelector={(opt) => opt.name}
        />
      </Provider>,
    );

    const input = screen.getByPlaceholderText("Select an option");
    fireEvent.focus(input);

    expect(store.dispatch).toHaveBeenCalledWith(
      setIsOptionsListOpen({ id: "testSelect", isOptionsListOpen: true }),
    );
  });

  it("filters options based on input value", () => {
    render(
      <Provider store={store}>
        <FilterSelect
          id="testSelect"
          options={options}
          placeholder="Select an option"
          onSelect={vi.fn()}
          name="test"
          idSelector={(opt) => opt.id}
          nameSelector={(opt) => opt.name}
        />
      </Provider>,
    );

    const input = screen.getByPlaceholderText("Select an option");
    fireEvent.change(input, { target: { value: "Option 1" } });

    expect(store.dispatch).toHaveBeenCalledWith(
      setSearchTerm({ id: "testSelect", searchTerm: "Option 1" }),
    );
  });

  it("selects an option when clicked");
});
