import { CalendarIcon, XMarkIcon } from "@heroicons/react/24/outline";
import Flatpickr from "react-flatpickr";

interface CalendarProps {
  placeholder: string;
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

export default function Calendar({ placeholder, onChange, className, value }: CalendarProps) {
  return (
    <div className={`flex min-w-[150px] rounded-md border border-slate-200 pl-1 pr-2 ${className}`}>
      <Flatpickr
        value={value}
        placeholder={placeholder}
        onChange={(selectedDates) => {
          const dateString = selectedDates[0]?.toISOString() || "";
          onChange(dateString);
        }}
        options={{
          dateFormat: "d-m-Y",
          maxDate: "today",
        }}
        className={`focus-0 w-full cursor-pointer rounded-md border-none bg-inherit p-[8px] text-sm focus:outline-none focus:ring-0`}
      />
      <span className="flex items-center">
        {value !== "" ? (
          <XMarkIcon
            data-testid="x-mark-icon"
            className="h-4 w-4 cursor-pointer"
            onClick={() => onChange("")}
          />
        ) : (
          <CalendarIcon data-testid="calender-icon" className="h-4 w-4" />
        )}
      </span>
    </div>
  );
}
