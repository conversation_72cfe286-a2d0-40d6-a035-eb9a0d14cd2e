interface FieldSetProps {
  title: JSX.Element | string;
  className?: string;
  children: React.ReactNode;
}

export default function FieldSet({ className, children, title }: FieldSetProps) {
  return (
    <fieldset className={`rounded-md border border-[#D1D5DB] px-3 ${className}`}>
      <legend className="px-0.5 text-[10px] font-medium text-[#4B5563]">{title}</legend>
      {children}
    </fieldset>
  );
}
