import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it, vi } from "vitest";
import SearchInput from "./SearchInput";

describe("SearchInput", () => {
  it("should render the input with the correct value and placeholder", () => {
    const { getByPlaceholderText } = render(
      <SearchInput value="test" placeholder="Search..." onChange={vi.fn()} />,
    );

    const input = getByPlaceholderText("Search...");
    expect(input).toHaveValue("test");
  });

  it("should call onChange when input value changes", async () => {
    const handleChange = vi.fn();

    render(<SearchInput value="test" placeholder="Search..." onChange={handleChange} />);

    const input = screen.getByPlaceholderText("Search...");

    const user = userEvent.setup();

    await user.type(input, "random");
    expect(handleChange).toHaveBeenCalled();
  });
});
