interface RadioButtonProps {
  id: string;
  name: string;
  value: string;
  checked: boolean;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export default function RadioButton({ id, name, value, checked, onChange }: RadioButtonProps) {
  return (
    <input
      id={id}
      type="radio"
      name={name}
      value={value}
      checked={checked}
      className="h-5 w-5 cursor-pointer focus:border-none focus:outline-none focus:ring-0"
      onChange={onChange}
    />
  );
}
