import { fireEvent, render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import AutoSuggestSelect, { AutoSuggestSelectOption } from "./AutoSuggestSelect";

// AutoSuggetSelect.test.tsx

const options: AutoSuggestSelectOption[] = [
  { key: 1, value: 1, label: "Apple" },
  { key: 2, value: 2, label: "Banana" },
  { key: 3, value: 3, label: "Cherry" },
];

function renderComponent(props = {}) {
  const defaultProps = {
    value: undefined,
    options,
    placeholder: "Select fruit",
    onChange: vi.fn(),
    containerClassName: "",
    query: "",
    onQueryChange: vi.fn(),
    isFetchingOptions: false,
    ...props,
  };
  return render(<AutoSuggestSelect {...defaultProps} />);
}

describe("AutoSuggestSelect", () => {
  it("renders with placeholder and options", () => {
    renderComponent();
    expect(screen.getByPlaceholderText("Select fruit")).toBeInTheDocument();
    // Open dropdown
    fireEvent.click(screen.getByRole("button"));
    expect(screen.getByText("Apple")).toBeInTheDocument();
    expect(screen.getByText("Banana")).toBeInTheDocument();
    expect(screen.getByText("Cherry")).toBeInTheDocument();
  });

  it('shows "Nothing found" when no match', () => {
    renderComponent({ query: "xyz" });
    fireEvent.click(screen.getByRole("button"));
    expect(screen.getByText("Nothing found.")).toBeInTheDocument();
  });

  it("calls onChange when an option is selected", async () => {
    const onChange = vi.fn();
    renderComponent({ onChange });
    fireEvent.click(screen.getByRole("button"));
    const option = screen.getByText("Banana");
    fireEvent.click(option);
    expect(onChange).toHaveBeenCalledWith(2);
  });

  it("calls onQueryChange when input changes", () => {
    const onQueryChange = vi.fn();
    renderComponent({ onQueryChange });
    const input = screen.getByPlaceholderText("Select fruit");
    fireEvent.change(input, { target: { value: "Che" } });
    expect(onQueryChange).toHaveBeenCalledWith("Che");
  });

  it("shows clear button when value or query is present and resets on click", async () => {
    const onChange = vi.fn();
    const onQueryChange = vi.fn();
    renderComponent({ value: 1, query: "App", onChange, onQueryChange });
    // Clear button (XMarkIcon) should be present
    const clearBtn = screen.getByRole("button").querySelector("svg");
    expect(clearBtn).toBeInTheDocument();
    fireEvent.click(clearBtn!);
    expect(onChange).toHaveBeenCalledWith("");
    expect(onQueryChange).toHaveBeenCalledWith("");
  });

  it("shows loading state if isFetchingOptions is true and hides 'Nothing found'", () => {
    renderComponent({ query: "xyz", isFetchingOptions: true });
    fireEvent.click(screen.getByRole("button"));
    expect(screen.queryByText("Nothing found.")).not.toBeInTheDocument();
  });
});
