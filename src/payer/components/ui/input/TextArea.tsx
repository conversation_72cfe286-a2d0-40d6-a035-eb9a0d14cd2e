import { ChangeEvent } from "react";

interface TextAreaProps {
  value: string;
  placeholder?: string;
  className?: string;
  name?: string;
  id?: string;
  required?: boolean;
  onChange: (e: ChangeEvent<HTMLTextAreaElement>) => void;
}

export default function TextArea({
  value,
  placeholder,
  className,
  required,
  onChange,
  name,
  id,
}: TextAreaProps) {
  return (
    <textarea
      name={name}
      required={required}
      id={id}
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      className={`min-h-20 max-h-20 w-full rounded-md border-0 border-none px-1 py-2 text-xs text-gray-700 outline-none placeholder:text-xs focus:border-none focus:outline-none focus:ring-0 ${className}`}
    ></textarea>
  );
}
