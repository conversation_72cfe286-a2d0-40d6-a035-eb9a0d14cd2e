import { Combobox, Transition } from "@headlessui/react";
import { ChevronDownIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { Fragment, useEffect, useState } from "react";

export type AutoSuggestSelectOption = {
  key: string | number;
  value: string | number;
  label: string;
};

type AutoSuggestSelectProps = {
  value: number | string | undefined;
  options: AutoSuggestSelectOption[];
  placeholder?: string;
  onChange(value: string | number): void;
  containerClassName?: string;
  query: string;
  onQueryChange(value: string): void;
  isFetchingOptions?: boolean;
};

export default function AutoSuggestSelect({
  onChange,
  options,
  value,
  containerClassName,
  placeholder,
  onQueryChange,
  query,
  isFetchingOptions = false,
}: AutoSuggestSelectProps) {
  const [displayValue, setDisplayValue] = useState("");

  useEffect(() => {
    // Update displayValue whenever the value prop changes
    const label = options.find((option) => option.value === value)?.label || "";
    setDisplayValue(label);
  }, [value, options]);

  const filteredOptions =
    query === ""
      ? options
      : options.filter((option) =>
          option.label
            .toLowerCase()
            .replace(/\s+/g, "")
            .includes(query.toLowerCase().replace(/\s+/g, "")),
        );

  function handleChange(selectedValue: string | number | undefined = "") {
    const label = options.find((option) => option.value == selectedValue)?.label || "";
    setDisplayValue(label);

    onChange(selectedValue);
  }

  function handleResetValue() {
    handleChange("");
    onQueryChange("");
  }

  const isNothingFound = filteredOptions.length === 0 && query !== "" && !isFetchingOptions;
  const shouldShowClearButton = value || query;

  return (
    <Combobox as={"div"} className={containerClassName} value={value} onChange={handleChange}>
      <div className="relative">
        <div
          className={
            "flex w-full items-center justify-between rounded-md  text-sm leading-5 text-gray-500"
          }
        >
          <Combobox.Input
            placeholder={placeholder}
            className="w-full rounded-md border border-gray-300 py-2 pl-3 pr-7 text-sm  text-gray-900 outline-none focus:border-black"
            displayValue={() => displayValue}
            onChange={(event) => onQueryChange(event.target.value)}
          />
          <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
            {shouldShowClearButton ? (
              <XMarkIcon onClick={handleResetValue} className="w-4" />
            ) : (
              <ChevronDownIcon className="w-4" />
            )}
          </Combobox.Button>
        </div>
        <Transition
          as={Fragment}
          leave="transition ease-in duration-100"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <Combobox.Options className="absolute z-50 mt-1 max-h-60 w-full overflow-y-auto overflow-x-hidden break-words rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm">
            {isNothingFound ? (
              <div className="relative cursor-default select-none px-4 py-2 text-gray-700">
                Nothing found.
              </div>
            ) : (
              filteredOptions.map((option) => (
                <Combobox.Option
                  key={option.key}
                  className={({ active }) =>
                    `px-4 py-2  ${
                      active ? "bg-[#EFF6FF]" : "bg-white text-black"
                    }  cursor-pointer px-4 py-2 text-sm`
                  }
                  value={option.value}
                >
                  {({ selected }) => (
                    <span className={`block  ${selected ? "font-medium" : "font-normal"}`}>
                      {option.label}
                    </span>
                  )}
                </Combobox.Option>
              ))
            )}
          </Combobox.Options>
        </Transition>
      </div>
    </Combobox>
  );
}
