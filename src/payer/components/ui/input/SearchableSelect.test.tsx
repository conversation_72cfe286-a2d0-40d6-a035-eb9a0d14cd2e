import { render, screen, fireEvent } from "@testing-library/react";
import { vi } from "vitest";
import SearchableSelect, { SearchableOption } from "./SearchableSelect";

describe("SearchableSelect", () => {
  const options: SearchableOption[] = [
    { id: 1, value: "apple", name: "<PERSON>" },
    { id: 2, value: "banana", name: "Banana" },
    { id: 3, value: "cherry", name: "<PERSON>" },
  ];

  let onChange: (value: string) => void;

  beforeEach(() => {
    onChange = vi.fn();
  });

  it("renders without crashing", () => {
    render(
      <SearchableSelect
        value=""
        onChange={onChange}
        options={options}
        placeHolder="Select a fruit"
      />,
    );
    expect(screen.getByPlaceholderText("Select a fruit")).toBeInTheDocument();
  });

  it("shows filtered options when typing", async () => {
    render(<SearchableSelect value="" onChange={onChange} options={options} />);

    const input = screen.getByRole("combobox");

    fireEvent.change(input, { target: { value: "ap" } });

    expect(await screen.findByText("Apple")).toBeInTheDocument();
    expect(screen.queryByText("Banana")).not.toBeInTheDocument();
    expect(screen.queryByText("Cherry")).not.toBeInTheDocument();
  });

  it("selects an option when clicked", async () => {
    render(<SearchableSelect value="" onChange={onChange} options={options} />);

    const input = screen.getByRole("combobox");

    fireEvent.change(input, { target: { value: "ch" } });

    const option = await screen.findByText("Cherry");
    fireEvent.click(option);

    expect(onChange).toHaveBeenCalledWith("cherry");
  });

  it("clears the input when clear button is clicked", () => {
    render(<SearchableSelect value="apple" onChange={onChange} options={options} />);

    const clearButton = screen.getByRole("button");
    fireEvent.click(clearButton);

    expect(onChange).toHaveBeenCalledWith("");
  });

  it("displays 'Nothing found.' when no match exists", async () => {
    render(<SearchableSelect value="" onChange={onChange} options={options} />);

    const input = screen.getByRole("combobox");

    fireEvent.change(input, { target: { value: "xyz" } });

    expect(await screen.findByText("Nothing found.")).toBeInTheDocument();
  });
});
