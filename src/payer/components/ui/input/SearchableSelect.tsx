import { Combobox, Transition } from "@headlessui/react";
import { ChevronDownIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { Fragment, useEffect, useRef, useState } from "react";

export type SearchableOption = {
  id: string | number;
  value: string | number;
  name: string;
};

type Props = {
  value: string;
  onChange: (value: string) => void;
  options: SearchableOption[];
  placeHolder?: string;
};

/**
 * A searchable dropdown component that allows users to filter and select options.
 * Utilizes `@headlessui/react`'s `Combobox` for accessibility and flexibility.
 *
 * @param {Object} props - Component properties.
 * @param {string} props.value - The currently selected value.
 * @param {function(string): void} props.onChange - Callback function triggered when the value changes.
 * @param {Array<{id: string | number, value: string | number, name: string}>} props.options -
 *   List of options to display in the dropdown.
 * @param {string} [props.placeHolder] - Placeholder text for the input field (optional).
 *
 * @returns {JSX.Element} The rendered `SearchableSelect` component.
 */
export default function SearchableSelect({ value, onChange, options, placeHolder }: Props) {
  const [query, setQuery] = useState("");
  const [isDropdownAbove, setIsDropdownAbove] = useState(false);
  const comboboxRef = useRef<HTMLDivElement>(null);

  const filteredOptions =
    query === ""
      ? options
      : options.filter((option) =>
          option.name
            .toLowerCase()
            .replace(/\s+/g, "")
            .includes(query.toLowerCase().replace(/\s+/g, "")),
        );

  function clearSelectInput() {
    setQuery("");
    onChange("");
  }

  // Determine space availability
  useEffect(() => {
    function updateDropdownPosition() {
      if (comboboxRef.current) {
        const rect = comboboxRef.current.getBoundingClientRect();
        const spaceBelow = window.innerHeight - rect.bottom;
        const spaceAbove = rect.top;
        setIsDropdownAbove(spaceBelow < 200 && spaceAbove > spaceBelow);
      }
    }
    updateDropdownPosition();
    window.addEventListener("resize", updateDropdownPosition);
    return () => window.removeEventListener("resize", updateDropdownPosition);
  }, []);

  return (
    <Combobox value={value} onChange={onChange}>
      <div className="relative mt-1" ref={comboboxRef}>
        <div className="relative w-full cursor-default overflow-hidden rounded-md border border-[#D1D5DB] bg-white text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-txtBlue sm:text-sm">
          <Combobox.Input
            placeholder={placeHolder}
            className="w-full overflow-hidden truncate border-none pl-3 pr-6 text-xs leading-5 focus:ring-0"
            displayValue={(value) => options.find((option) => option.value === value)?.name || ""}
            onChange={(event) => setQuery(event.target.value)}
          />

          {value ? (
            <button
              className="absolute inset-y-0 right-0 flex items-center pr-2"
              onClick={clearSelectInput}
            >
              <XMarkIcon className="h-5 w-5 text-gray-400" />
            </button>
          ) : (
            <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
              <ChevronDownIcon className="h-4 w-4 text-gray-400" aria-hidden="true" />
            </Combobox.Button>
          )}
        </div>
        <Transition
          as={Fragment}
          leave="transition ease-in duration-100"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
          afterLeave={() => setQuery("")}
        >
          <Combobox.Options
            className={`absolute z-10 max-h-60 w-full overflow-y-auto rounded-md border border-gray-300 bg-white shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm ${
              isDropdownAbove ? "bottom-full mb-1" : "top-full mt-1"
            }`}
          >
            {filteredOptions?.length === 0 && query !== "" ? (
              <div className="relative cursor-default select-none px-4 py-2 text-gray-700">
                Nothing found.
              </div>
            ) : (
              filteredOptions?.map((option) => (
                <Combobox.Option
                  key={option.id}
                  className={({ active }) =>
                    `relative cursor-default select-none px-3 py-2 text-xs ${
                      active && "bg-faintBlue"
                    }`
                  }
                  value={option.value}
                >
                  {({ selected }) => (
                    <>
                      <span className={`block ${selected ? "font-medium" : "font-normal"}`}>
                        {option.name}
                      </span>
                    </>
                  )}
                </Combobox.Option>
              ))
            )}
          </Combobox.Options>
        </Transition>
      </div>
    </Combobox>
  );
}
