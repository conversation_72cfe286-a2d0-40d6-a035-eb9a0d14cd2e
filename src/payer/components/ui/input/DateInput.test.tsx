import { render, screen } from "@testing-library/react";
import DateInput from "./DateInput";
import { describe, it, expect, vi } from "vitest";
import userEvent from "@testing-library/user-event";

describe("DateInput Component", () => {
  it("renders the DateInput component", () => {
    render(<DateInput placeholder="Select a date" value="" onChange={() => null} />);

    expect(screen.getByPlaceholderText("Select a date")).toBeInTheDocument();
  });

  it("renders the calendar icon", () => {
    render(<DateInput placeholder="Select a date" value="" onChange={() => null} />);

    expect(screen.getByTestId("calender-icon")).toBeInTheDocument();
  });

  it("applies additional className if provided", () => {
    const { container } = render(
      <DateInput
        placeholder="Select a date"
        value=""
        onChange={() => null}
        className="custom-class"
      />,
    );

    expect(container.firstChild).toHaveClass("custom-class");
  });
});
