import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import { ChangeEvent } from "react";

interface SearchInputProps {
  value: string;
  placeholder?: string;
  className?: string;
  title?: string;
  onChange: (e: ChangeEvent<HTMLInputElement>) => void;
}

export default function SearchInput({
  value,
  placeholder,
  className,
  title,
  onChange,
}: SearchInputProps) {
  return (
    <div className="relative">
      <span className="absolute left-2 flex h-full items-center">
        <MagnifyingGlassIcon className="h-[18px] w-[18px] text-customGray" />
      </span>
      <input
        className={`rounded-md border-slate-300 pl-8 text-xs ${className}`}
        placeholder={placeholder}
        title={title}
        type="text"
        value={value}
        onChange={onChange}
      />
    </div>
  );
}
