import { render, screen } from "@testing-library/react";
import FieldSet from "./FieldSet";
import { describe, it, expect } from "vitest";

describe("FieldSet Component", () => {
  it("renders the fieldset with the correct title", () => {
    render(<FieldSet title="Test Title">Test Content</FieldSet>);

    expect(screen.getByText("Test Title")).toBeInTheDocument();
  });

  it("renders children inside the fieldset", () => {
    render(
      <FieldSet title="Test Title">
        <p>Child Content</p>
      </FieldSet>,
    );

    expect(screen.getByText("Child Content")).toBeInTheDocument();
  });

  it("applies additional className if provided", () => {
    const { container } = render(
      <FieldSet title="Test Title" className="custom-class">
        Test Content
      </FieldSet>,
    );

    expect(container.firstChild).toHaveClass("custom-class");
  });
});
