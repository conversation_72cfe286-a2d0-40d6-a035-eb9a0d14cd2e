import { render, screen, fireEvent } from "@testing-library/react";
import RadioButton from "./RadioButton";

describe("RadioButton component", () => {
  const onChangeMock = vi.fn();

  const defaultProps = {
    id: "test-radio",
    name: "test-name",
    value: "test-value",
    checked: false,
    onChange: onChangeMock,
  };

  it("renders with correct props", () => {
    render(<RadioButton {...defaultProps} />);

    const radioButton = screen.getByRole("radio");
    expect(radioButton).toBeInTheDocument();
    expect(radioButton).toHaveAttribute("id", "test-radio");
    expect(radioButton).toHaveAttribute("name", "test-name");
    expect(radioButton).toHaveAttribute("value", "test-value");
    expect(radioButton).not.toBeChecked();
  });

  it("fires onChange when clicked", () => {
    render(<RadioButton {...defaultProps} />);

    const radioButton = screen.getByRole("radio");
    fireEvent.click(radioButton);

    expect(onChangeMock).toHaveBeenCalledTimes(1);
  });

  it("shows checked when checked prop is true", () => {
    render(<RadioButton {...defaultProps} checked={true} />);

    const radioButton = screen.getByRole("radio");
    expect(radioButton).toBeChecked();
  });
});
