import { render, screen, fireEvent } from "@testing-library/react";
import CheckBox from "./CheckBox";
import { vi } from "vitest";

describe("CheckBox component", () => {
  test("renders checkbox input", () => {
    render(<CheckBox onChange={() => null} />);
    const checkbox = screen.getByRole("checkbox");
    expect(checkbox).toBeInTheDocument();
  });

  test("applies props correctly", () => {
    render(
      <CheckBox
        id="test-id"
        name="test-name"
        value="test-value"
        checked={true}
        onChange={() => null}
      />,
    );
    const checkbox = screen.getByRole("checkbox");
    expect(checkbox).toHaveAttribute("id", "test-id");
    expect(checkbox).toHaveAttribute("name", "test-name");
    expect(checkbox).toHaveAttribute("value", "test-value");
    expect(checkbox).toBeChecked();
  });

  test("calls onChange when clicked", () => {
    const handleChange = vi.fn();
    render(<CheckBox onChange={handleChange} />);
    const checkbox = screen.getByRole("checkbox");
    fireEvent.click(checkbox);
    expect(handleChange).toHaveBeenCalledTimes(1);
  });
});
