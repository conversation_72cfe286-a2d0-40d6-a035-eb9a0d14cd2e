import { ChevronDownIcon, ChevronUpIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { ChangeEvent, useEffect, useRef } from "react";
import {
  initializeSelect,
  setHighlightedIndex,
  setIsOptionsListOpen,
  setSearchTerm,
  setSelectedOption,
} from "../../../features/filters/filterSelectSlice";
import { useAppDispatch, useAppSelector } from "../../../store/hooks";

interface FilterSelectProps<T> {
  id: string;
  options: T[];
  placeholder?: string;
  onSelect: (selected: T | null) => void;
  name: string;
  idSelector: (option: T) => string | number;
  nameSelector: (option: T) => string;
  handleExtraAction?: () => void;
  handleExternalSearch?: ((searchTerm: string) => void) | undefined;
  /** This is to keep the text styling consistent with the Pre-Authorization implementation
   *  Should not be used for the others.
   */
  hasLargeText?: boolean;
}

export default function FilterSelect<T>({
  id,
  options,
  placeholder,
  name,
  onSelect,
  nameSelector,
  idSelector,
  handleExtraAction,
  handleExternalSearch,
  hasLargeText = true,
}: FilterSelectProps<T>) {
  const dispatch = useAppDispatch();
  const isOptionsListOpen = useAppSelector((state) => state.filterSelect[id]?.isOptionsListOpen);
  const searchTerm = useAppSelector((state) => state.filterSelect[id]?.searchTerm) as string;
  const selectedOption = useAppSelector((state) => state.filterSelect[id]?.selectedOption);
  const highlightedIndex = useAppSelector(
    (state) => state.filterSelect[id]?.highlightedIndex,
  ) as number;

  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLUListElement>(null);

  const filteredOptions = options.filter((option) =>
    nameSelector(option)?.toLowerCase().includes(searchTerm?.toLowerCase()),
  );

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        dispatch(setIsOptionsListOpen({ id, isOptionsListOpen: false }));
        dispatch(setSearchTerm({ id, searchTerm: "" }));
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [dispatch, id]);

  const handleSelect = (option: T) => {
    dispatch(setSelectedOption({ id, selectedOption: option }));
    dispatch(
      setSearchTerm({
        id,
        searchTerm: nameSelector(option),
      }),
    );
    if (handleExternalSearch) {
      handleExternalSearch("");
    }
    dispatch(setIsOptionsListOpen({ id, isOptionsListOpen: false }));
    onSelect(option);
  };

  const handleClear = () => {
    dispatch(setSelectedOption({ id, selectedOption: null }));
    dispatch(setSearchTerm({ id, searchTerm: "" }));
    dispatch(setHighlightedIndex({ id, index: -1 }));
    onSelect(null);
    handleExtraAction?.();
  };

  useEffect(() => {
    dispatch(initializeSelect({ id }));
  }, [dispatch, id]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case "ArrowDown":
        dispatch(
          setHighlightedIndex({
            id,
            index: Math.min(highlightedIndex + 1, filteredOptions.length - 1),
          }),
        );
        break;
      case "ArrowUp":
        dispatch(setHighlightedIndex({ id, index: Math.max(highlightedIndex - 1, 0) }));
        break;
      case "Enter":
        if (highlightedIndex >= 0 && highlightedIndex < filteredOptions.length) {
          handleSelect(filteredOptions[highlightedIndex] as T);
        }
        break;
      case "Escape":
        dispatch(setIsOptionsListOpen({ id, isOptionsListOpen: false }));
        break;
    }
  };

  const handleSearchValueChange = (e: ChangeEvent<HTMLInputElement>) => {
    dispatch(setSearchTerm({ id, searchTerm: e.target.value }));
    if (handleExternalSearch) {
      handleExternalSearch(e.target.value);
    }
  };

  return (
    <div className="relative max-w-full">
      <div className="flex items-center overflow-hidden rounded-md border border-[#D1D5DB] px-1">
        <input
          ref={inputRef}
          name={name}
          type="text"
          placeholder={placeholder || "Select an option"}
          value={
            searchTerm
              ? searchTerm.includes(" to ")
                ? `${searchTerm} Days`
                : searchTerm.length > 18
                  ? searchTerm.slice(0, 18) + "..."
                  : searchTerm
              : ""
          }
          onChange={handleSearchValueChange}
          onFocus={() => dispatch(setIsOptionsListOpen({ id, isOptionsListOpen: true }))}
          onKeyDown={handleKeyDown}
          className={`flex-grow border-0 focus:border-0 focus:ring-0 ${hasLargeText ? "text-sm placeholder:text-sm" : "text-sm placeholder:text-sm"}`}
          aria-expanded={isOptionsListOpen}
          aria-controls="filter-select-list"
          autoComplete="off"
        />
        {selectedOption ? (
          <button onClick={handleClear} className="absolute right-2 bg-white pl-1 text-midGray">
            <XMarkIcon className="h-4 w-4" />
          </button>
        ) : (
          <>
            {!isOptionsListOpen ? (
              <button
                onClick={() => dispatch(setIsOptionsListOpen({ id, isOptionsListOpen: true }))}
                className="absolute right-2 text-midGray"
              >
                <ChevronDownIcon className="h-[14px] w-[14px]" />
              </button>
            ) : (
              <button
                onClick={() => dispatch(setIsOptionsListOpen({ id, isOptionsListOpen: false }))}
                className="absolute right-2 text-midGray"
              >
                <ChevronUpIcon className="h-[14px] w-[14px]" />
              </button>
            )}
          </>
        )}
      </div>
      {isOptionsListOpen && (
        <ul
          ref={dropdownRef}
          id="filter-select-list"
          className="absolute z-10 mt-1 max-h-80 w-full overflow-y-auto rounded-md border border-gray-300 bg-white shadow-lg transition-all duration-200"
          role="listbox"
        >
          {filteredOptions.length > 0 ? (
            filteredOptions.map((option, index) => (
              <li
                key={idSelector(option)}
                onClick={() => handleSelect(option)}
                onMouseEnter={() => dispatch(setHighlightedIndex({ id, index }))}
                className={`cursor-pointer px-3 py-2 hover:bg-faintBlue ${
                  highlightedIndex === index ? "bg-faintBlue" : ""
                } ${hasLargeText ? "text-sm" : "text-xs"}`}
                role="option"
                aria-selected={highlightedIndex === index}
              >
                {nameSelector(option).includes(" to ")
                  ? `${nameSelector(option)} Days`
                  : `${nameSelector(option)}`}
              </li>
            ))
          ) : (
            <li className={`px-3 py-2 text-midGray ${hasLargeText ? "text-sm" : "text-xs"}`}>
              No options found
            </li>
          )}
        </ul>
      )}
    </div>
  );
}
