import { Listbox } from "@headlessui/react";
import { ChevronDownIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { Fragment } from "react";
import { cn } from "~lib/utils/cn";

export type SelectOption = {
  key: string | number;
  value: string | number;
  label: string;
};
type SelectProps = {
  value: number | string | undefined;
  options: SelectOption[];
  placeholder?: string;
  onChange(value: string | number): void;
  containerClassName?: string;
};

export function Select({
  options,
  value,
  placeholder = "",
  onChange,
  containerClassName,
}: SelectProps) {
  const selectedOption = options.find((option) => option.value == value);

  function handleResetValue() {
    onChange("");
  }

  return (
    <Listbox as={"div"} value={value} onChange={onChange} className={containerClassName}>
      <Listbox.Button
        className={
          "flex w-full items-center justify-between rounded-md border border-gray-300 py-2 pl-4 pr-2 text-sm leading-5 text-gray-500"
        }
      >
        <span
          title={selectedOption?.label || placeholder}
          className={cn(
            "max-w-[90%] overflow-hidden text-ellipsis whitespace-nowrap",
            value && "text-black",
          )}
        >
          {selectedOption?.label || placeholder}
        </span>
        {value ? (
          <XMarkIcon onClick={handleResetValue} className="w-4" />
        ) : (
          <ChevronDownIcon className="w-4" />
        )}
      </Listbox.Button>

      <div className="relative z-40">
        <Listbox.Options
          className={
            "absolute mt-1 flex max-h-[320px] w-full min-w-fit flex-col overflow-y-auto overflow-x-hidden rounded-md border bg-white shadow-md "
          }
        >
          {options.map((option) => (
            <Listbox.Option key={option.key} value={option.value} as={Fragment}>
              {({ active, selected }) => (
                <li
                  className={`${active || selected ? "bg-[#EFF6FF] " : "bg-white text-black"} cursor-pointer px-4 py-2 text-sm`}
                >
                  {option.label}
                </li>
              )}
            </Listbox.Option>
          ))}

          {options.length === 0 && (
            <p className="px-4 py-2 text-sm text-gray-500">No options available</p>
          )}
        </Listbox.Options>
      </div>
    </Listbox>
  );
}
