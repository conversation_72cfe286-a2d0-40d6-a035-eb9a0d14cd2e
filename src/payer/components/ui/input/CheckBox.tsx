interface CheckBoxProps {
  id?: string;
  name?: string;
  value?: string;
  checked?: boolean;
  ref?: React.Ref<HTMLInputElement>;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export default function CheckBox({ id, name, value, checked, ref, onChange }: CheckBoxProps) {
  return (
    <input
      id={id}
      ref={ref}
      type="checkbox"
      name={name}
      value={value}
      checked={checked}
      className="rounded"
      onChange={onChange}
    />
  );
}
