import { XMarkIcon } from "@heroicons/react/24/outline";
import Flatpickr, { DateTimePickerProps } from "react-flatpickr";
import CalenderIcon from "../../icons/CalenderIcon";

const dateFlatPickrOptions: DateTimePickerProps["options"] = {
  mode: "single",
  monthSelectorType: "static",
  enableTime: false,
  prevArrow:
    '<svg class="fill-current" width="7" height="11" viewBox="0 0 7 11"><path d="M5.4 10.8l1.4-1.4-4-4 4-4L5.4 0 0 5.4z" /></svg>',
  nextArrow:
    '<svg class="fill-current" width="7" height="11" viewBox="0 0 7 11"><path d="M1.4 10.8L0 9.4l4-4-4-4L1.4 0l5.4 5.4z" /></svg>',
} as const;

type Props = {
  placeholder: string;
  value: string;
  onChange: (value: string) => void;
  className?: string;
  maxDate?: string | Date;
  position?:
    | "auto"
    | "above"
    | "below"
    | "auto left"
    | "auto center"
    | "auto right"
    | "above left"
    | "above center"
    | "above right"
    | "below left"
    | "below center"
    | "below right";
};

export default function DateInput({
  placeholder,
  onChange,
  className,
  value,
  maxDate,
  position = "auto",
}: Props) {
  const parsedMaxDate = maxDate ? new Date(maxDate) : "";

  function handleResetValue() {
    onChange("");
  }

  return (
    <div className={`relative rounded-md border border-gray-300 ${className}`}>
      <Flatpickr
        value={value}
        placeholder={placeholder}
        onChange={(selectedDates) => {
          const dateString = selectedDates[0]?.toISOString() || "";
          onChange(dateString);
        }}
        options={{
          dateFormat: "d-m-Y",
          maxDate: parsedMaxDate,
          ...dateFlatPickrOptions,
          position,
        }}
        className={`w-full cursor-pointer rounded-md border-none bg-inherit p-[8px] text-sm font-normal outline-none`}
      />
      <span className="absolute bottom-0 right-2 top-0 flex items-center">
        {value ? (
          <XMarkIcon className="w-4 cursor-pointer text-gray-500" onClick={handleResetValue} />
        ) : (
          <CalenderIcon />
        )}
      </span>
    </div>
  );
}
