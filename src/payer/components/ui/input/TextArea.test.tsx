import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { vi } from "vitest";
import TextArea from "./TextArea";

describe("TextArea", () => {
  it("renders the text area with the correct value and placeholder", () => {
    const mockOnChange = vi.fn();
    const placeholderText = "Enter text here";
    const value = "Test value";

    render(<TextArea value={value} placeholder={placeholderText} onChange={mockOnChange} />);

    const textArea = screen.getByPlaceholderText(placeholderText);
    expect(textArea).toBeInTheDocument();
    expect(textArea).toHaveValue(value);
  });

  it("calls the onChange handler when the value changes", async () => {
    const mockOnChange = vi.fn();
    const initialValue = "Test value";

    render(<TextArea value={initialValue} onChange={mockOnChange} />);

    const textArea = screen.getByRole("textbox");

    const newValue = "Updated value";

    await userEvent.type(textArea, newValue);

    expect(mockOnChange).toHaveBeenCalled();
  });

  it("renders with custom class name", () => {
    const mockOnChange = vi.fn();
    const customClass = "custom-class";

    render(<TextArea value="value" onChange={mockOnChange} className={customClass} />);

    const textArea = screen.getByRole("textbox");
    expect(textArea).toHaveClass(customClass);
  });

  it("renders the text area with the required attribute when required prop is true", () => {
    const mockOnChange = vi.fn();

    render(<TextArea value="value" onChange={mockOnChange} required />);

    const textArea = screen.getByRole("textbox");
    expect(textArea).toBeRequired();
  });
});
