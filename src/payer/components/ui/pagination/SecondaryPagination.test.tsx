import { fireEvent, render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { vi } from "vitest";
import SecondaryPagination from "./SecondaryPagination"; // adjust import path

describe("SecondaryPagination", () => {
  const setCurrentPage = vi.fn();

  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should render the correct pagination information", () => {
    render(
      <SecondaryPagination
        currentPage={2}
        size={10}
        totalElements={25}
        totalPages={3}
        setCurrentPage={setCurrentPage}
      />,
    );

    expect(screen.getByText("11")).toBeInTheDocument();
    expect(screen.getByText("20")).toBeInTheDocument();
    expect(screen.getByText("25")).toBeInTheDocument();
  });

  it("should call setCurrentPage when next button is clicked", async () => {
    render(
      <SecondaryPagination
        currentPage={2}
        size={10}
        totalElements={25}
        totalPages={3}
        setCurrentPage={setCurrentPage}
      />,
    );

    await userEvent.click(screen.getByTestId("right-button"));

    expect(setCurrentPage).toHaveBeenCalled();
  });

  it("should call setCurrentPage when previous button is clicked", () => {
    render(
      <SecondaryPagination
        currentPage={2}
        size={10}
        totalElements={25}
        totalPages={3}
        setCurrentPage={setCurrentPage}
      />,
    );

    fireEvent.click(screen.getByTestId("left-button"));

    expect(setCurrentPage).toHaveBeenCalled();
  });

  it("should not call setCurrentPage when next button is disabled", async () => {
    render(
      <SecondaryPagination
        currentPage={3}
        size={10}
        totalElements={25}
        totalPages={3}
        setCurrentPage={setCurrentPage}
      />,
    );

    await userEvent.click(screen.getByTestId("right-button"));

    expect(setCurrentPage).not.toHaveBeenCalled();
  });

  it("should not call setCurrentPage when previous button is disabled", () => {
    render(
      <SecondaryPagination
        currentPage={1}
        size={10}
        totalElements={25}
        totalPages={3}
        setCurrentPage={setCurrentPage}
      />,
    );

    fireEvent.click(screen.getByTestId("left-button"));

    expect(setCurrentPage).not.toHaveBeenCalled();
  });
});
