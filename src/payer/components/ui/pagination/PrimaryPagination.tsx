import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/solid";
import { ChangeEvent } from "react";

const PAGE_SIZES = [5, 10, 20, 50, 100, 200, 500, 1000];

interface PaginationProps {
  totalElements: number;
  totalPages: number;
  pageNumber: number;
  onPageNumberClick: (page: number) => void;
  onSizeChange: (size: number) => void;
  pageSize: number;
}

const PrimaryPagination: React.FC<PaginationProps> = ({
  totalElements,
  totalPages,
  pageNumber,
  onPageNumberClick,
  onSizeChange,
  pageSize,
}) => {
  const createPageArray = () => {
    const pages = [];
    const maxPagesToShow = 5;
    const halfMaxPagesToShow = Math.floor(maxPagesToShow / 2);

    pages.push(1);

    let startPage = Math.max(2, pageNumber - halfMaxPagesToShow);
    let endPage = Math.min(totalPages - 1, pageNumber + halfMaxPagesToShow);

    if (pageNumber <= halfMaxPagesToShow) {
      endPage = Math.min(totalPages - 1, maxPagesToShow);
    }

    if (pageNumber > totalPages - halfMaxPagesToShow) {
      startPage = Math.max(2, totalPages - maxPagesToShow + 1);
    }

    if (startPage > 2) {
      pages.push("...");
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    if (endPage < totalPages - 1) {
      pages.push("...");
    }

    if (totalPages > 1) {
      pages.push(totalPages);
    }

    return pages;
  };

  const pages = createPageArray();

  const handleSizeChange = (e: ChangeEvent<HTMLSelectElement>) => {
    onSizeChange(Number(e.target.value));
  };

  return (
    <nav className="flex items-center space-x-3 py-4" aria-label="pagination">
      <p className="whitespace-nowrap text-sm text-[#333333]">Total {totalElements}</p>
      <p className="flex items-center rounded-[4px] border border-gray-200 px-3 py-1 text-sm text-gray-400">
        {`${pageNumber < 10 ? `0${pageNumber}` : pageNumber}/${totalPages}`}
      </p>
      <select
        id="size"
        name="size"
        onChange={handleSizeChange}
        className="w-[140px] rounded border-gray-200 px-2 py-1.5 text-sm text-gray-500"
        defaultValue={pageSize}
      >
        {PAGE_SIZES.map((size) => (
          <option value={size as number} key={size}>
            {size} per page
          </option>
        ))}
      </select>
      <div className="flex items-center space-x-1">
        <button
          onClick={() => onPageNumberClick(pageNumber - 1)}
          disabled={pageNumber <= 1}
          aria-disabled={pageNumber <= 1}
          className="flex items-center justify-center rounded-md bg-[#DBEAFB] px-2.5 py-2 font-medium text-[#144397] disabled:cursor-not-allowed disabled:bg-gray-300 disabled:text-gray-700"
        >
          <span className="sr-only">Previous</span>
          <ChevronLeftIcon className="h-4 w-4 " />
        </button>
        <ul className={`flex items-center space-x-2 text-sm font-medium shadow-sm`}>
          {pages.map((item) => (
            <li key={item} className="">
              {item === "..." ? (
                <span className="rounded-md border border-[#BABBBF] px-3 py-1.5 font-semibold ">
                  ...
                </span>
              ) : (
                <button
                  onClick={() => onPageNumberClick(item as number)}
                  className={`inline-flex min-w-[30px] items-center justify-center rounded-md px-3 py-1.5 font-semibold ${
                    pageNumber === item
                      ? "bg-customBlue text-white"
                      : "border border-[#BABBBF] text-[#BABDC0] hover:bg-customBlue hover:text-white"
                  }`}
                >
                  {item}
                </button>
              )}
            </li>
          ))}
        </ul>
        <button
          onClick={() => onPageNumberClick(pageNumber + 1)}
          disabled={totalPages <= pageNumber}
          aria-disabled={totalPages <= pageNumber}
          className="flex items-center justify-center rounded-md bg-[#DBEAFB] px-2.5 py-2 font-medium text-[#144397] disabled:cursor-not-allowed  disabled:bg-gray-300 disabled:text-gray-700"
        >
          <span className="sr-only">Next</span>
          <ChevronRightIcon className="h-4 w-4" />
        </button>
      </div>
    </nav>
  );
};

export default PrimaryPagination;
