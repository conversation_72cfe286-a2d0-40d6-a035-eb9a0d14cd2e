import { fireEvent, render, screen } from "@testing-library/react";
import PrimaryPagination from "./PrimaryPagination";
import { vi } from "vitest";

describe("PrimaryPagination", () => {
  const mockOnPageNumberClick = vi.fn();
  const mockOnSizeChange = vi.fn();

  const defaultProps = {
    totalElements: 100,
    totalPages: 10,
    pageNumber: 1,
    onPageNumberClick: mockOnPageNumberClick,
    onSizeChange: mockOnSizeChange,
    pageSize: 20,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders the correct total elements and pagination controls", () => {
    render(<PrimaryPagination {...defaultProps} />);

    expect(screen.getByText("Total 100")).toBeInTheDocument();
    expect(screen.getByText("01/10")).toBeInTheDocument(); // Since pageNumber is 1
    expect(screen.getByRole("button", { name: /previous/i })).toBeDisabled();
    expect(screen.getByRole("button", { name: /next/i })).toBeEnabled();
  });

  it("calls onSizeChange when size is changed", () => {
    render(<PrimaryPagination {...defaultProps} />);

    fireEvent.change(screen.getByRole("combobox"), { target: { value: "50" } });

    expect(mockOnSizeChange).toHaveBeenCalledWith(50);
  });

  it("calls onPageNumberClick when a page number is clicked", () => {
    render(<PrimaryPagination {...defaultProps} />);

    fireEvent.click(screen.getByText("2"));

    expect(mockOnPageNumberClick).toHaveBeenCalledWith(2);
  });

  it("disables the previous button when on the first page", () => {
    render(<PrimaryPagination {...defaultProps} pageNumber={1} />);

    expect(screen.getByRole("button", { name: /previous/i })).toBeDisabled();
  });

  it("disables the next button when on the last page", () => {
    render(<PrimaryPagination {...defaultProps} pageNumber={10} />);

    expect(screen.getByRole("button", { name: /next/i })).toBeDisabled();
  });

  test('shows "..." when there are skipped pages', () => {
    render(<PrimaryPagination {...defaultProps} pageNumber={5} />);

    const dots = screen.queryAllByText("...");

    expect(dots).toHaveLength(2);
    expect(dots[0]).toBeInTheDocument();
    expect(dots[1]).toBeInTheDocument();
  });

  it("disables the next button when the page number is at the last page", () => {
    render(<PrimaryPagination {...defaultProps} pageNumber={10} />);

    expect(screen.getByRole("button", { name: /next/i })).toBeDisabled();
  });

  it("renders pagination when there is only one page", () => {
    const singlePageProps = {
      ...defaultProps,
      totalElements: 5,
      totalPages: 1,
      pageNumber: 1,
    };

    render(<PrimaryPagination {...singlePageProps} />);

    expect(screen.getByText("Total 5")).toBeInTheDocument();
    expect(screen.getByText("01/1")).toBeInTheDocument();
    expect(screen.getByRole("combobox")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /previous/i })).toBeDisabled();
    expect(screen.getByRole("button", { name: /next/i })).toBeDisabled();
  });
});
