import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import React from "react";

type Props = {
  currentPage: number;
  totalPages: number;
  size: number;
  totalElements: number;
  setCurrentPage: React.Dispatch<React.SetStateAction<number>>;
};

export default function SecondaryPagination({
  currentPage,
  size,
  totalElements,
  setCurrentPage,
  totalPages,
}: Props) {
  function handleIncrementPage() {
    if (currentPage >= totalPages) return;

    setCurrentPage((prev) => prev + 1);
  }

  function handleDecrementPage() {
    if (currentPage <= 1) return;

    setCurrentPage((prev) => prev - 1);
  }

  return (
    <div className="flex items-center gap-4">
      <div className="text-xs text-[#1F2937]">
        <span>{currentPage * size - (size - 1)}</span> -{" "}
        <span>{currentPage * size > totalElements ? totalElements : currentPage * size}</span> of{" "}
        <span>{totalElements}</span>
      </div>
      <div className="flex items-center space-x-3 font-medium">
        <button
          data-testid="left-button"
          onClick={handleDecrementPage}
          disabled={currentPage === 1}
          type="button"
          className="rounded-full bg-faintBlue p-1 text-midGray disabled:cursor-not-allowed disabled:text-lightGray disabled:opacity-70"
        >
          <ChevronLeftIcon className="h-3 w-3" />
        </button>
        <button
          data-testid="right-button"
          type="button"
          onClick={handleIncrementPage}
          disabled={totalPages === currentPage}
          className="rounded-full bg-faintBlue p-1 text-midGray disabled:cursor-not-allowed disabled:text-lightGray disabled:opacity-70"
        >
          <ChevronRightIcon className="h-3 w-3" />
        </button>
      </div>
    </div>
  );
}
