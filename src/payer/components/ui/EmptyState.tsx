interface EmptyStateProps {
  illustration: JSX.Element;
  message: {
    title: string;
    description?: string;
  };
}

export default function EmptyState({ illustration, message }: EmptyStateProps) {
  return (
    <div className="self-center py-4">
      <div className="flex flex-col space-y-3 overflow-x-auto bg-white text-gray-600">
        <div className="flex w-full justify-center">{illustration}</div>
        <div className="flex flex-col items-center justify-center space-y-2 text-center">
          <h2 className="text-2xl font-bold text-darkBlue">{message.title}</h2>
          <p className="mx-60 text-base text-customGray">{message?.description}</p>
        </div>
      </div>
    </div>
  );
}
