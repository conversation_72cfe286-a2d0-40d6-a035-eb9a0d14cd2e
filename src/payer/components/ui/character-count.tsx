interface CharacterCountProps {
  current: number;
  max: number;
  className?: string;
}

export default function CharacterCount({ current, max, className = "" }: CharacterCountProps) {
  const isNearLimit = current > max * 0.8;
  const isOverLimit = current > max;

  return (
    <div
      className={`text-xs ${
        isOverLimit ? "text-red-600" : isNearLimit ? "text-yellow-600" : "text-gray-500"
      } ${className}`}
    >
      {current}/{max}
    </div>
  );
}
