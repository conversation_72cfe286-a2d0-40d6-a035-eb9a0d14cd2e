import { ReactNode } from "react";
import { cn } from "~lib/utils/cn";

interface TableDataItemProps {
  item?: string | number | undefined;
  title?: string | number | undefined;
  children?: ReactNode;
  className?: string;
}

const TableDataItem = ({ item, title, children, className }: TableDataItemProps) => {
  return (
    <td
      title={(title || item) as string}
      className={cn(
        "overflow-hidden overflow-ellipsis border-b border-[#EAECF0] px-4 py-2.5 text-left text-base text-customGray",
        className,
      )}
    >
      {children || item}
    </td>
  );
};

export default TableDataItem;
