import { render, screen } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import BatchIdTableDataItem from "./BatchIdTableDataItem";

describe("BatchIdTableDataItem", () => {
  it("renders the batch ID with correct formatting", () => {
    const testBatchId = "123";
    render(<BatchIdTableDataItem batchId={testBatchId} />);

    expect(screen.getByText(`BATCH-00${testBatchId}`)).toBeInTheDocument();
  });

  it("renders the vertical divider", () => {
    const testBatchId = "123";
    render(<BatchIdTableDataItem batchId={testBatchId} />);

    const dividerElement = screen.getByTestId("separator");
    expect(dividerElement).toBeInTheDocument();
  });
});
