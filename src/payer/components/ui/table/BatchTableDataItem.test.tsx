import { render, screen } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import BatchTableDataItem from "./BatchTableDataItem";

describe("BatchTableDataItem", () => {
  it("renders the table data item with provided title and value", () => {
    const testTitle = "Test Title";
    const testValue = "Test Value";
    render(<BatchTableDataItem title={testTitle} value={testValue} />);

    expect(screen.getByText(testTitle)).toBeInTheDocument();
    expect(screen.getByText(testValue)).toBeInTheDocument();
  });

  it("renders 'None' when value is empty", () => {
    const testTitle = "Test Title";
    render(<BatchTableDataItem title={testTitle} value="" />);

    expect(screen.getByText("None")).toBeInTheDocument();
  });

  it("applies correct styling classes", () => {
    const testTitle = "Test Title";
    const testValue = "Test Value";
    const { container } = render(<BatchTableDataItem title={testTitle} value={testValue} />);

    const tdElement = container.querySelector("td");
    expect(tdElement).toHaveClass("pb-4", "pl-8", "pt-4");
  });
});
