import { render, screen } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import TableHeaderItem from "./TableHeaderItem";

describe("TableHeaderItem", () => {
  it("renders the table header item with provided text", () => {
    const testText = "Test Header";
    render(<TableHeaderItem item={testText} />);

    const thElement = screen.getByText(testText);
    expect(thElement).toBeInTheDocument();
    expect(thElement.tagName).toBe("TH");
  });
});
