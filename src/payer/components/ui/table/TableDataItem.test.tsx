import { render, screen } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import TableDataItem from "./TableDataItem";

describe("TableDataItem", () => {
  it("renders the table data item with provided text", () => {
    const testText = "Test Data";
    render(<TableDataItem item={testText} />);

    const tdElement = screen.getByText(testText);
    expect(tdElement).toBeInTheDocument();
    expect(tdElement.tagName).toBe("TD");
  });

  it("renders the table data item with a title attribute", () => {
    const testText = "Test Data";
    const testTitle = "Tooltip Title";
    render(<TableDataItem item={testText} title={testTitle} />);

    const tdElement = screen.getByText(testText);
    expect(tdElement).toHaveAttribute("title", testTitle);
  });
});
