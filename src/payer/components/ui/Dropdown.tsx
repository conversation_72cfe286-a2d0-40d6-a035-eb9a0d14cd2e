import { Menu } from "@headlessui/react";
import { ReactNode } from "react";
import { cn } from "~lib/utils/cn";

export type DropDownItem = {
  label: string;
  disabled?: boolean;
  onClick?(): void;
};
type DropdownProps = {
  buttonLabel?: string;
  LeftIcon?: ReactNode;
  RightIcon?: ReactNode;
  buttonClassName?: string;
  menuItemsClassName?: string;
  itemsClassName?: string;
  items?: DropDownItem[];
  disabled?: boolean;
  children?: ReactNode;
};

export default function Dropdown({
  LeftIcon,
  RightIcon,
  buttonLabel,
  buttonClassName,
  menuItemsClassName,
  items = [],
  disabled = false,
  children,
  itemsClassName,
}: DropdownProps) {
  return (
    <Menu as={"div"} className={"relative w-fit"}>
      <Menu.Button
        disabled={disabled}
        className={cn(
          "flex items-center gap-2 rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white disabled:cursor-not-allowed disabled:opacity-50",
          buttonClassName,
        )}
      >
        {LeftIcon}
        {buttonLabel || children}
        {RightIcon}
      </Menu.Button>
      <Menu.Items
        className={cn(
          "absolute z-10 mt-1 flex w-full flex-col items-start overflow-hidden rounded-lg border bg-white shadow-lg",
          menuItemsClassName,
        )}
      >
        {items.map(({ label, disabled = false, onClick }) => (
          <Menu.Item key={label} disabled={disabled}>
            {({ active }) => (
              <button
                onClick={onClick}
                disabled={disabled}
                className={cn(
                  `w-full px-4 py-2 text-left text-sm  `,
                  active && "bg-blue-50",
                  itemsClassName,
                )}
              >
                {label}
              </button>
            )}
          </Menu.Item>
        ))}
      </Menu.Items>
    </Menu>
  );
}
