import { Tab } from "@headlessui/react";
import { ReactNode, useState } from "react";
import { cn } from "~lib/utils/cn";

export type TabItem = {
  title: string;
  content: ReactNode;
  disabled?: boolean;
};

type TabGroupProps = {
  tabs: TabItem[];
  /**
   * Default selected tab index
   * @default 0
   */
  defaultIndex?: number;
  onChange?: (index: number) => void;
  vertical?: boolean;
  tabListClassName?: string;
  tabPanelsClassName?: string;
  tabClassName?: string;
  tabPanelClassName?: string;
};

export default function TabGroup({
  tabs,
  defaultIndex = 0,
  onChange,
  vertical = false,
  tabListClassName,
  tabPanelsClassName,
  tabClassName,
  tabPanelClassName,
}: TabGroupProps) {
  const [selectedIndex, setSelectedIndex] = useState(defaultIndex);

  const handleChange = (index: number) => {
    setSelectedIndex(index);
    onChange?.(index);
  };

  return (
    <Tab.Group selectedIndex={selectedIndex} onChange={handleChange} vertical={vertical}>
      <Tab.List
        className={cn(
          "mb-4 flex space-x-1 rounded-xl bg-gray-100 p-1",
          vertical && "flex-col space-x-0 space-y-1",
          tabListClassName,
        )}
      >
        {tabs.map((tab, index) => (
          <Tab
            key={index}
            disabled={tab.disabled}
            className={({ selected }) =>
              cn(
                `w-full rounded-lg py-2.5 text-sm font-medium leading-5
                ${
                  selected
                    ? "bg-white text-primary shadow"
                    : "text-gray-700 hover:bg-white/[0.12] hover:text-gray-900"
                }`,
                tab.disabled && "cursor-not-allowed opacity-50",
                tabClassName,
              )
            }
          >
            {tab.title}
          </Tab>
        ))}
      </Tab.List>
      <Tab.Panels className={tabPanelsClassName}>
        {tabs.map((tab, index) => (
          <Tab.Panel key={index} className={cn("rounded-xl bg-white p-3", tabPanelClassName)}>
            {tab.content}
          </Tab.Panel>
        ))}
      </Tab.Panels>
    </Tab.Group>
  );
}
