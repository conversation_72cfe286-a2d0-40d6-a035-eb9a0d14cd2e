import { ReactNode } from "react";
import { cn } from "~lib/utils/cn";

//* other text variant to be added to the union type as necessary
type TextVariant = "heading" | "description" | "paragraph" | "subheading" | "label";
type Props = {
  variant?: TextVariant;
  children: ReactNode;
  className?: string;
};

export default function Text({ variant = "paragraph", children, className }: Props) {
  switch (variant) {
    case "heading":
      return <h1 className={cn("text-2xl font-medium text-darkBlue", className)}>{children}</h1>;

    case "subheading":
      return <h2 className={cn("text-lg font-semibold text-darkBlue", className)}>{children}</h2>;

    case "description":
      return <p className={cn("text-base text-customGray", className)}>{children}</p>;

    case "paragraph":
      return <p className={cn("text-base text-darkGray", className)}>{children}</p>;

    case "label":
      return <p className={cn("text-sm font-semibold text-darkBlue", className)}>{children}</p>;

    default:
      return <p className={cn("text-base text-darkGray", className)}>{children}</p>;
  }
}
