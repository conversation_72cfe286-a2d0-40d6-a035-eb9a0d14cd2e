import { Switch } from "@headlessui/react";

type ToggleProps = {
  isOn: boolean;
  onChange(isOn: boolean): void;
};

export default function Toggle({ isOn, onChange }: ToggleProps) {
  return (
    <Switch
      checked={isOn}
      onChange={onChange}
      className={`${
        isOn ? "bg-[#2563EB]" : "bg-gray-300"
      } relative inline-flex h-[30px] w-[50px] items-center rounded-full`}
    >
      <span
        className={`${
          isOn ? "translate-x-6" : "translate-x-1"
        } inline-block h-6 w-6 transform rounded-full bg-white shadow-md transition`}
      />
    </Switch>
  );
}
