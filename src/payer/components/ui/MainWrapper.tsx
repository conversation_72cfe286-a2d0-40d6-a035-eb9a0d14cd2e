import { ReactNode } from "react";
import { cn } from "~lib/utils/cn";

interface Props {
  children: ReactNode;
  className?: string;
}

export default function MainWrapper({ children, className }: Props) {
  return (
    <main
      className={cn(
        `mx-6 mb-6 flex h-full flex-col space-y-3 overflow-hidden rounded-lg border border-gray-200 py-6 pl-6 pr-4 shadow-xl`,
        className,
      )}
    >
      {children}
    </main>
  );
}
