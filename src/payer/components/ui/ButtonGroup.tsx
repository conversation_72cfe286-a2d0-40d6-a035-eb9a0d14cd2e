import { cn } from "~lib/utils/cn";

export interface ButtonGroupOption<T> {
  title: T;
  icon?: JSX.Element;
}

type Props<T> = {
  options: ButtonGroupOption<T>[];
  setActiveOption: (title: T) => void;
  activeOption: T;
  titleClassName?: string;
};

export default function ButtonGroup<T>({
  options,
  setActiveOption,
  activeOption,
  titleClassName,
}: Props<T>) {
  const handleActiveOption = (title: T) => {
    setActiveOption(title);
  };

  return (
    <div className="flex w-fit items-center">
      {options.map((item, index) => (
        <div
          key={index}
          onClick={() => handleActiveOption(item.title)}
          className={`flex cursor-pointer items-center space-x-2 border border-gray-200 px-4 py-1.5 ${
            index === 0 && "rounded-l-md"
          } ${index === options.length - 1 && "rounded-r-md"} ${
            activeOption == item.title ? "bg-faintBlue text-txtBlue" : "bg-white text-midGray"
          }`}
        >
          <div className={`${activeOption == item.title ? "text-txtBlue" : "text-midGray"}`}>
            {item.icon}
          </div>
          <p className={cn("text-base", titleClassName)}>{item.title as string}</p>
        </div>
      ))}
    </div>
  );
}
