import { ReactNode } from "react";
import { cn } from "~lib/utils/cn";

type ButtonVariant = "outlined" | "filled" | "destructive";

type Props = {
  onClick?: () => void;
  children: ReactNode;
  variant?: ButtonVariant;
  disabled?: boolean;
  className?: string;
  type?: "submit" | "button" | "reset";
  title?: string;
};

export default function Button({
  onClick,
  children,
  variant = "filled",
  disabled,
  className,
  type,
  title,
}: Props) {
  return (
    <button
      title={title}
      className={cn(
        `flex w-fit items-center justify-center gap-2 rounded px-3 py-[6px] text-sm font-medium disabled:cursor-not-allowed disabled:opacity-60`,
        variant === "filled" && "bg-btnBlue text-white disabled:bg-lightGray",
        variant === "destructive" && "bg-error text-white disabled:bg-red-400",
        variant === "outlined" && "border border-lightGray text-darkGray",
        className,
      )}
      onClick={onClick}
      disabled={disabled}
      type={type}
    >
      {children}
    </button>
  );
}
