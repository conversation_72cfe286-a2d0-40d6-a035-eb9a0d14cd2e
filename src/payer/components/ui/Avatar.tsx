import * as AvatarPrimitive from "@radix-ui/react-avatar";
import { cn } from "~lib/utils/cn";

type Props = {
  initials?: string;
  name?: string;
  initialsCount?: 1 | 2;
  className?: string;
  fallbackClassName?: string;
};
export default function Avatar({
  initials = "A",
  name,
  initialsCount = 2,
  className,
  fallbackClassName,
}: Props) {
  const nameInitials = name
    ?.split(" ")
    .slice(0, initialsCount)
    .map((name) => name.at(0))
    .join("");

  const slicedInitials = initials.slice(0, initialsCount);

  const initialsToDisplay = nameInitials || slicedInitials;

  return (
    <AvatarPrimitive.Root className={cn("h-10 w-10 overflow-hidden rounded-full", className)}>
      <AvatarPrimitive.Fallback
        className={cn(
          "flex h-full w-full items-center justify-center rounded-full bg-black/5 text-sm capitalize text-black/70",
          fallbackClassName,
        )}
      >
        {initialsToDisplay}
      </AvatarPrimitive.Fallback>
    </AvatarPrimitive.Root>
  );
}
