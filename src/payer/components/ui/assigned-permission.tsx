import { mapRoleNameToDescriptiveName } from "../../utils/user-management-utils";
import { XMarkIcon } from "@heroicons/react/24/solid";
import { PermissionResponse } from "../../lib/types/access-control/role";

interface AssignedRoleProps {
  permission: PermissionResponse;
  onRemove: (name: string) => void;
}

export default function AssignedPermission({ permission, onRemove }: AssignedRoleProps) {
  return (
    <div
      key={permission.name}
      className="flex items-center gap-1 rounded-full bg-gray-100 px-3 py-1"
    >
      <p className="text-sm">{mapRoleNameToDescriptiveName(permission.name)}</p>
      <button
        type="button"
        onClick={() => onRemove(permission.name)}
        className="ml-1 rounded-full p-0.5 hover:bg-red-200"
        title="Remove Permission"
        aria-label="Remove Permission"
      >
        <XMarkIcon className="h-3 w-3" />
      </button>
    </div>
  );
}
