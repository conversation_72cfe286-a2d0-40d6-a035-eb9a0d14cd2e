import { fireEvent, render, screen } from "@testing-library/react";
import SuccessModal from "./SuccessModal";

global.ResizeObserver = class ResizeObserver {
  observe = () => null;
  unobserve = () => null;
  disconnect = () => null;
};

describe("SuccessModal", () => {
  const mockOnClose = vi.fn();

  const defaultProps = {
    onClose: mockOnClose,
    isSuccessModalOpen: true,
    title: "Success",
    description: "Your action was successful.",
    hasThankYou: true,
  };

  it("renders the modal with the correct title and description", () => {
    render(<SuccessModal {...defaultProps} />);

    expect(screen.getByText("Success")).toBeInTheDocument();
    expect(screen.getByText("Your action was successful.")).toBeInTheDocument();
  });

  it("renders the 'Thank you!!!' message when hasThankYou is true", () => {
    render(<SuccessModal {...defaultProps} />);

    expect(screen.getByText("Thank you!!!")).toBeInTheDocument();
  });

  it("does not render the 'Thank you!!!' message when hasThankYou is false", () => {
    render(<SuccessModal {...{ ...defaultProps, hasThankYou: false }} />);

    expect(screen.queryByText("Thank you!!!")).not.toBeInTheDocument();
  });

  it("calls onClose when the Done button is clicked", () => {
    render(<SuccessModal {...defaultProps} />);

    const button = screen.getByRole("button", { name: "Done" });
    fireEvent.click(button);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it("does not render the modal when isSuccessModalOpen is false", () => {
    render(<SuccessModal {...{ ...defaultProps, isSuccessModalOpen: false }} />);

    expect(screen.queryByText("Success")).not.toBeInTheDocument();
  });
});
