import { XMarkIcon } from "@heroicons/react/24/outline";
import DialogWrapper from "./DialogWrapper";

export default function BackButtonWarningOnEditModal({
  onClose,
  isBackButtonWarningModalOpen,
  onConfirm,
}: {
  onClose: () => void;
  isBackButtonWarningModalOpen: boolean;
  onConfirm: () => void;
}) {
  const handleClose = () => {
    onClose();
    onConfirm();
  };

  return (
    <DialogWrapper
      show={isBackButtonWarningModalOpen}
      onClose={onClose}
      maxWidth="max-w-[35rem]"
      className="p-4"
    >
      <section className="flex flex-col">
        <button onClick={onClose} className="self-end">
          <XMarkIcon className="h-4 w-4" />
        </button>
        <section className="flex flex-col space-y-4 px-2 pb-4">
          <div className="flex space-x-6">
            <div className="flex flex-col space-y-4 pt-1">
              <h2 className="text-base font-medium text-[#3F495F]">
                Are you sure you want to leave this page?
              </h2>
              <p className="text-sm text-customGray">
                You may have unsaved changes. Click 'No' to cancel and make changes or 'Yes' to
                leave.
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-4 self-end">
            <button
              className="rounded-[4px] bg-customRed   px-3 py-1 text-[10px] text-white"
              onClick={onClose}
            >
              No
            </button>
            <button className="px-2 py-1 text-[10px] text-[#667085]" onClick={handleClose}>
              Yes
            </button>
          </div>
        </section>
      </section>
    </DialogWrapper>
  );
}
