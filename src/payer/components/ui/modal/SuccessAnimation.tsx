import styles from "../../CustomSuccessModal/CustomSuccessModal.module.css";
import SuccessTickIcon from "../../CustomSuccessModal/SuccessTickIcon";

export default function SuccessAnimation() {
  return (
    <div className="relative h-[150px]">
      <div
        className={`absolute right-1/2 top-1/2 h-[160px] w-[160px]  rounded-full bg-[rgba(68,158,218,0.2)] ${styles["circle-one"]}`}
      ></div>
      <div
        className={`absolute right-1/2 top-1/2 h-[110px] w-[110px]  rounded-full bg-[rgba(68,158,218,0.2)] ${styles["circle-two"]}`}
      ></div>
      <div
        className={`absolute right-1/2 top-1/2 h-[110px] w-[110px]  rounded-full bg-lightBlue ${styles["circle-three"]}`}
      ></div>

      <div className="absolute right-1/2 top-1/2 h-fit w-fit -translate-y-1/2 translate-x-1/2 ">
        <SuccessTickIcon height={70} width={70} />
      </div>
    </div>
  );
}
