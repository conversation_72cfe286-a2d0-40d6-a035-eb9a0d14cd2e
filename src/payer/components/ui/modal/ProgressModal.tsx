import LoadingAnimation from "../../animations/LoadingAnimation/LoadingAnimation";
import DialogWrapper from "./DialogWrapper";

interface ProgressModalProps {
  onClose: () => void;
  isProgressModalOpen: boolean;
  title?: string;
  description?: string;
}

export default function ProgressModal({
  onClose,
  isProgressModalOpen,
  title,
  description,
}: ProgressModalProps) {
  return (
    <DialogWrapper
      show={isProgressModalOpen}
      maxWidth="max-w-[40rem]"
      onClose={onClose}
      className="px-8 py-10"
    >
      <section className="flex flex-col space-y-5">
        <div className="self-center">
          <LoadingAnimation size={60} />
        </div>
        <div className="flex flex-col space-y-5">
          {title && <h2 className="self-center text-lg font-medium text-btnBlue">{title}</h2>}
          {description && <p className="text-center text-base text-customGray">{description}</p>}
        </div>
      </section>
    </DialogWrapper>
  );
}
