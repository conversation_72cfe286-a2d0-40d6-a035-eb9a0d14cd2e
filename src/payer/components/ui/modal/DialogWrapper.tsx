/**
 * A wrapper component for the Headless UI Dialog component (v1.7).
 *
 * This component wraps the Headless UI Dialog component to provide consistent styling and functionality
 * across the application. It supports additional customizations like setting the maximum width and adding
 * custom class names.
 *
 * @component
 *
 * @property {() => void} onClose - Function to call when the dialog should close.
 * @property {boolean} show - Whether the dialog is visible or not.
 * @property {string} maxWidthRem - The maximum width of the dialog (e.g., "max-w-[40rem]").
 * @property {string} className - Additional classes to apply to the dialog.
 * @property {React.ReactNode} children - Content to render inside the dialog.
 *
 * @example
 * ```jsx
 * <DialogWrapper maxWidthRem="40rem" className="custom-class" onClose={handleClose} show={isOpen}>
 *   // Dialog content goes here
 * </DialogWrapper>
 * ```
 *
 * @returns {JSX.Element} The rendered dialog component.
 */
import { Dialog, Transition } from "@headlessui/react";
import { Fragment } from "react";
import { cn } from "~lib/utils/cn";
export default function DialogWrapper({
  onClose,
  show,
  maxWidth,
  className,
  children,
}: {
  onClose: () => void;
  show: boolean;
  maxWidth?: string;
  className?: string;
  children: React.ReactNode;
}) {
  return (
    <>
      <Transition appear show={show} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={onClose}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/25" />
          </Transition.Child>
          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel
                  className={cn(
                    `w-full ${maxWidth} transform overflow-hidden rounded-md bg-white text-left align-middle shadow-xl transition-all`,
                    className,
                  )}
                >
                  {children}
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}
