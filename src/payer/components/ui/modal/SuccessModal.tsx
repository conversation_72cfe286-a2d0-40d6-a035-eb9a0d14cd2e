import Button from "../Button";
import DialogWrapper from "./DialogWrapper";
import SuccessAnimation from "./SuccessAnimation";

interface CustomSuccessModalProps {
  onClose: () => void;
  isSuccessModalOpen: boolean;
  title: string;
  description: string;
  hasThankYou?: boolean;
  maxWidthRem?: string;
}

export default function SuccessModal({
  onClose,
  isSuccessModalOpen,
  title,
  description,
  hasThankYou = false,
  maxWidthRem = "max-w-[35rem]",
}: CustomSuccessModalProps) {
  return (
    <DialogWrapper show={isSuccessModalOpen} onClose={onClose} maxWidth={maxWidthRem}>
      <div className="mt-2 flex flex-col space-y-4 p-8">
        <div className="self-center pb-4">
          <SuccessAnimation />
        </div>
        <h2 className="whitespace-nowrap text-center text-2xl font-semibold text-darkGray">
          {title}
        </h2>
        <div className="flex flex-col items-center justify-center self-center text-center text-base text-customGray">
          <p className="mx-8">{description}</p>
          {hasThankYou && <p>Thank you!!!</p>}
        </div>
        <Button className="w-[20%] self-center" onClick={onClose}>
          Done
        </Button>
      </div>
    </DialogWrapper>
  );
}
