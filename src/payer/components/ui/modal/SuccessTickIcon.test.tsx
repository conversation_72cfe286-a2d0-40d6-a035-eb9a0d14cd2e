import { render } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import SuccessTickIcon from "./SuccessTickIcon";

describe("SuccessTickIcon", () => {
  it("renders the SVG with default dimensions", () => {
    const { container } = render(<SuccessTickIcon />);
    const svgElement = container.querySelector("svg");

    expect(svgElement).toBeInTheDocument();
    expect(svgElement).toHaveAttribute("width", "206");
    expect(svgElement).toHaveAttribute("height", "207");
  });

  it("renders the SVG with custom dimensions", () => {
    const { container } = render(<SuccessTickIcon width={100} height={100} />);
    const svgElement = container.querySelector("svg");

    expect(svgElement).toBeInTheDocument();
    expect(svgElement).toHaveAttribute("width", "100");
    expect(svgElement).toHaveAttribute("height", "100");
  });
});
