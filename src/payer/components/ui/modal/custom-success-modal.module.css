/* Circle styles */
.circle-one {
  transform: translate(50%, -50%);
  animation: pulse-one 1.2s infinite;
}

.circle-two {
  animation: pulse-two 1.2s infinite;
}

.circle-three {
  transform: translate(50%, -50%);
  animation: pulse-three 1.2s infinite;
}

/* Modal styles */
.back-drop {
  animation: fade-in 0.2s ease-in;
}

.modal {
  animation: scale-up 0.2s ease-in;
}

/* Keyframes */
@keyframes scale-up {
  from {
    transform: scale(0.5);
  }
  to {
    transform: scale(1);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse-one {
  0% {
    transform: translate(50%, -50%);
  }
  100% {
    transform: translate(50%, -50%) scale(1.25);
  }
}

@keyframes pulse-two {
  0% {
    transform: translate(50%, -50%);
  }
  60% {
    transform: translate(50%, -50%);
  }
  100% {
    transform: translate(52%, -48%) scale(1.5);
  }
}

@keyframes pulse-three {
  0% {
    transform: translate(50%, -50%);
  }
  100% {
    transform: translate(52%, -48%);
  }
}
