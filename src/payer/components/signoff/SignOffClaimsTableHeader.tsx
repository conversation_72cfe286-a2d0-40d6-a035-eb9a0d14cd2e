import {
  setIsSignOffSelectAllActive,
  setSignOffCheckedClaimsIds,
  setSignOffSelectedClaimsSum,
} from "../../features/finance/signoff/signOffSlice";
import { useAppDispatch, useAppSelector } from "../../store/hooks";

import TableHeaderItem from "../ui/table/TableHeaderItem";

export default function SignOffClaimsTableHeader() {
  const dispatch = useAppDispatch();
  const isSignOffSelectAllActive = useAppSelector(
    (state) => state.signOff.isSignOffSelectAllActive,
  );
  const totalSystemSignOffClaims = useAppSelector(
    (state) => state.signOff.totalSystemSignOffClaims,
  );

  const handleSelectAll = () => {
    if (!isSignOffSelectAllActive) {
      const currentIds = totalSystemSignOffClaims.map((claim) => claim.id);

      const totalAmount = totalSystemSignOffClaims.reduce(
        (acc, claim) => acc + (claim.totalAmount ?? 0),
        0,
      );

      dispatch(setSignOffCheckedClaimsIds(currentIds as string[]));
      dispatch(setSignOffSelectedClaimsSum(totalAmount));
    } else {
      dispatch(setSignOffCheckedClaimsIds([]));
      dispatch(setSignOffSelectedClaimsSum(0));
    }
    dispatch(setIsSignOffSelectAllActive(!isSignOffSelectAllActive));
  };

  return (
    <thead className="text-left">
      <tr className="bg-[#F9FAFB] text-[13px]">
        <th className="whitespace-nowrap px-4 py-2 uppercase">
          <input
            className="form-checkbox"
            type="checkbox"
            checked={isSignOffSelectAllActive}
            onChange={handleSelectAll}
            title="Select All"
          />
        </th>
        <TableHeaderItem className="text-xs" item="Member No" />
        <TableHeaderItem className="text-xs" item="Member Name" />
        <TableHeaderItem className="text-xs" item="Invoice No" />
        <TableHeaderItem className="text-xs" item="Invoice Date" />
        <TableHeaderItem className="text-xs" item="Amount" />
        <TableHeaderItem className="text-xs" item="Status" />
      </tr>
    </thead>
  );
}
