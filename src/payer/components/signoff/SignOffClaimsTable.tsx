import { XMarkIcon } from "@heroicons/react/24/outline";
import { useEffect, useState } from "react";
import { Empty } from "~lib/components";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { PAGE_SIZES } from "~lib/constants";
import { useGetAllClaimsQuery } from "../../api/features/claimsApi";
import { setIsSelectAllActive } from "../../features/claims/claimsBatchingSlice";
import {
  setAllClaimsToBeSignedOff,
  setIsSignOffSelectAllActive,
  setSignOffCheckedClaimsIds,
  setSignOffSelectedClaimsSum,
  setTotalSystemSignOffClaims,
} from "../../features/finance/signoff/signOffSlice";
import {
  GetAllClaimsParams,
  InvoiceBatchStatus,
  InvoiceData,
  InvoiceStatus,
} from "../../lib/types/claims/invoice";
import UserService from "../../services/UserService";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import SuccessModal from "../ui/modal/SuccessModal";
import PrimaryPagination from "../ui/pagination/PrimaryPagination";
import SignOffButton from "./SignOffButton";
import SignOffClaimsTableHeader from "./SignOffClaimsTableHeader";
import SignOffClaimsTableRow from "./SignOffClaimsTableRow";

export default function SignOffClaimsTable() {
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(PAGE_SIZES[0] as number);
  const dispatch = useAppDispatch();

  const { shouldShowSuccessModal, setShouldShowSuccessModal } = useHandleSuccessModal();

  /*claims batching state*/
  const signOffCheckedClaimsIds = useAppSelector((state) => state.signOff.signOffCheckedClaimsIds);
  const allClaimsToBeSignedOff = useAppSelector((state) => state.signOff.allClaimsToBeSignedOff);
  const selectedSignOffAccount = useAppSelector((state) => state.signOff.selectedSignOffAccount);
  const selectedSignOffScheme = useAppSelector((state) => state.signOff.selectedSignOffScheme);

  const fromDate = useAppSelector((state) => state.signOff.fromDate);
  const toDate = useAppSelector((state) => state.signOff.toDate);

  const payerId: string = UserService.getPayer()?.tokenParsed?.["payerId"];
  const batchStatus: InvoiceBatchStatus = "NOT_BATCHED";
  const invoiceStatuses: Array<InvoiceStatus> = ["BALANCE_DEDUCTED"];

  const claimsToBeBatchedParams: GetAllClaimsParams = {
    payerId,
    batchStatus,
    size,
    page,
    providerAccountIds: selectedSignOffAccount ? [Number(selectedSignOffAccount)] : [],
    planIds: selectedSignOffScheme ? [Number(selectedSignOffScheme)] : [],
    withVoucher: false,
    ...(fromDate && { startDate: (fromDate as string).split("T")[0] as string }),
    ...(toDate && { endDate: (toDate as string).split("T")[0] as string }),
    onlyProviderWithAccounts: true,
    invoiceStatuses,
  };

  const totalSystemSignOffClaimsParams: GetAllClaimsParams = {
    payerId,
    batchStatus,
    size: 100_000_000,
    page: 1,
    providerAccountIds: selectedSignOffAccount ? [Number(selectedSignOffAccount)] : [],
    planIds: selectedSignOffScheme ? [Number(selectedSignOffScheme)] : [],
    withVoucher: false,
    ...(fromDate && { startDate: (fromDate as string).split("T")[0] as string }),
    ...(toDate && { endDate: (toDate as string).split("T")[0] as string }),
    onlyProviderWithAccounts: true,
    invoiceStatuses,
  };

  const {
    data,
    isLoading,
    isFetching,
    error,
    refetch: refetchIndividualClaims,
  } = useGetAllClaimsQuery(claimsToBeBatchedParams);

  const { data: totalSystemSignOffClaimsResponse } = useGetAllClaimsQuery(
    totalSystemSignOffClaimsParams,
  );

  const claims = data?.data.content;
  const totalElements = data?.data.totalElements as number;
  const totalPages = data?.data.totalPages as number;

  useEffect(() => {
    refetchIndividualClaims();
  }, [page, refetchIndividualClaims]);

  const handlePageNumberClick = (page: number) => {
    setPage(page);
    dispatch(setIsSelectAllActive(false));
  };

  const handleSizeChange = (size: number) => {
    setSize(size);
    setPage(1);
    dispatch(setIsSignOffSelectAllActive(false));
  };

  const handleCancelSelection = () => {
    dispatch(setSignOffCheckedClaimsIds([]));
    dispatch(setSignOffSelectedClaimsSum(0));
    dispatch(setIsSignOffSelectAllActive(false));
  };

  useEffect(() => {
    dispatch(setAllClaimsToBeSignedOff(claims as InvoiceData[]));
  }, [claims, dispatch]);

  useEffect(() => {
    if (totalSystemSignOffClaimsResponse?.data?.content) {
      dispatch(setTotalSystemSignOffClaims(totalSystemSignOffClaimsResponse.data.content));
    }
  }, [totalSystemSignOffClaimsResponse, dispatch]);

  return (
    <section className="flex min-h-full flex-col overflow-x-auto bg-white">
      {signOffCheckedClaimsIds.length > 0 && (
        <div className="flex items-center justify-between pb-5">
          <div className="flex items-center gap-2 text-darkBlue">
            <button onClick={handleCancelSelection}>
              <XMarkIcon className="h-4 w-4" />
            </button>
            <p className="flex items-center gap-1 text-sm">
              <span>{signOffCheckedClaimsIds.length}</span> <span>selected</span>
            </p>
          </div>
          <SignOffButton setShouldShowSuccessModal={setShouldShowSuccessModal} />
        </div>
      )}
      <section className={`${isLoading || (isFetching && "py-40")}`}>
        {isLoading || isFetching ? (
          <div className="flex items-center justify-center space-x-2 self-center py-8">
            <LoadingIcon className="h-6 w-6 text-blue-400" />
            <p className="text-blue-700">Loading...</p>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center self-center py-8">
            <p className="text-red-700">Error loading claims. Try again later.</p>
          </div>
        ) : (
          allClaimsToBeSignedOff?.length === 0 && (
            <div className="self-center">
              <Empty message="Claims not Found" />
            </div>
          )
        )}
      </section>
      {allClaimsToBeSignedOff?.length > 0 && !isLoading && !isFetching && (
        <>
          <table className="table-auto">
            <SignOffClaimsTableHeader />
            <tbody>
              {allClaimsToBeSignedOff?.map((invoice: InvoiceData) => (
                <SignOffClaimsTableRow invoice={invoice} />
              ))}
            </tbody>
          </table>
          <div className="self-start py-4">
            <PrimaryPagination
              pageSize={size}
              totalElements={totalElements}
              totalPages={totalPages ?? 1}
              pageNumber={page}
              onPageNumberClick={handlePageNumberClick}
              onSizeChange={handleSizeChange}
            />
          </div>
        </>
      )}
      <SuccessModal
        description="Your payment voucher has been created successfully."
        hasThankYou
        title="Voucher created successfully "
        isSuccessModalOpen={shouldShowSuccessModal}
        onClose={() => null}
      />
    </section>
  );
}

function useHandleSuccessModal() {
  const [shouldShowSuccessModal, setShouldShowSuccessModal] = useState(false);

  useEffect(() => {
    if (!shouldShowSuccessModal) return;

    const timeout = setTimeout(() => {
      setShouldShowSuccessModal(false);
    }, 2500);

    return () => clearTimeout(timeout);
  }, [shouldShowSuccessModal]);

  return { shouldShowSuccessModal, setShouldShowSuccessModal };
}
