import { useEffect, useRef } from "react";
import Calendar from "react-calendar";
import {
  setFromDate,
  setIsEndDatePickerOpen,
  setIsStartDatePickerOpen,
  setToDate,
} from "../../features/finance/signoff/signOffSlice";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import "../../css/calendar/react-calendar.css";

type DatePickerType = "start" | "end";

export default function SignOffDatePicker({ type }: { type: DatePickerType }) {
  const dispatch = useAppDispatch();
  const dropdownRef = useRef<HTMLDivElement>(null);
  const fromDate = useAppSelector((state) => state.signOff.fromDate);
  const toDate = useAppSelector((state) => state.signOff.toDate);

  const handleCalendarDateChange = (date: Date | null) => {
    switch (type) {
      case "start":
        dispatch(setFromDate(date));
        dispatch(setIsStartDatePickerOpen(false));
        break;
      case "end":
        dispatch(setToDate(date));
        dispatch(setIsEndDatePickerOpen(false));
        break;
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        switch (type) {
          case "start":
            dispatch(setIsStartDatePickerOpen(false));
            break;
          case "end":
            dispatch(setIsEndDatePickerOpen(false));
            break;
        }
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [dispatch, type]);

  return (
    <section
      ref={dropdownRef}
      className="absolute z-10 mt-1 flex w-[350px] overflow-y-auto rounded-md p-3 px-2 transition-all duration-200"
    >
      <Calendar
        className={"border border-gray-200 text-sm"}
        value={type === "start" ? fromDate : toDate}
        onChange={(date) => handleCalendarDateChange(date as Date)}
      />
    </section>
  );
}
