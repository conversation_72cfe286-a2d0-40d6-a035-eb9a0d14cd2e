import { useEffect, useState } from "react";
import {
  useGetMembershipAccountsQuery,
  useGetMembershipSchemesQuery,
} from "../../api/features/membershipApi";
import {
  setFromDate,
  setIsSignOffSelectAllActive,
  setSelectedSignOffAccount,
  setSelectedSignOffAccountName,
  setSelectedSignOffScheme,
  setSelectedSignOffSchemeName,
  setSignOffCheckedClaimsIds,
  setSignOffSelectedClaimsSum,
  setToDate,
} from "../../features/finance/signoff/signOffSlice";
import { useHandleFilterSelectClear } from "../../hooks/useHandleFilterSelectClear";
import { MembershipAccount } from "../../lib/types/membership/memberAccount";
import { MembershipScheme } from "../../lib/types/membership/memberScheme";
import UserService from "../../services/UserService";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import Button from "../ui/Button";
import Calendar from "../ui/input/Calender";
import { Select, SelectOption } from "../ui/input/Select";

export default function SignOffFilters() {
  const [payerAccounts, setPayerAccounts] = useState<MembershipAccount[]>([]);
  const [payerSchemes, setPayerSchemes] = useState<MembershipScheme[]>([]);
  const signoffPayerAccountsFilterId = "signoff-payer-accounts";
  const signoffPayerSchemesFilterId = "signoff-payer-schemes";

  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const { data: membershipAccountsResponse } = useGetMembershipAccountsQuery(payerId);
  const { data: membershipSchemesResponse } = useGetMembershipSchemesQuery(payerId);

  const dispatch = useAppDispatch();

  const selectedSignOffAccount = useAppSelector((state) => state.signOff.selectedSignOffAccount);
  const selectedSignOffScheme = useAppSelector((state) => state.signOff.selectedSignOffScheme);

  const fromDate = useAppSelector((state) => state.signOff.fromDate);
  const toDate = useAppSelector((state) => state.signOff.toDate);

  enum SignOffSelectFilters {
    ACCOUNT = "Account",
    SCHEME = "Scheme",
  }

  function mapToSelectOptions<T extends { id: string | number; name: string }>(
    options: T[] | undefined,
  ): SelectOption[] {
    return options
      ? options.map((option) => ({
          key: option.id,
          value: option.id,
          label: option.name,
        }))
      : [];
  }

  const handleSelect = (criteria: SignOffSelectFilters, selectedId: string | number) => {
    switch (criteria) {
      case SignOffSelectFilters.ACCOUNT: {
        const found = payerAccounts.find((a) => String(a.id) === String(selectedId));
        dispatch(setSelectedSignOffAccount(found?.id || ""));
        dispatch(setSelectedSignOffAccountName(found?.accountName));
        break;
      }
      case SignOffSelectFilters.SCHEME: {
        const found = payerSchemes.find((s) => String(s.id) === String(selectedId));
        dispatch(setSelectedSignOffScheme(found?.id || ""));
        dispatch(setSelectedSignOffSchemeName(found?.name));
        break;
      }
    }
    dispatch(setIsSignOffSelectAllActive(false));
  };

  const isFilterSelected = selectedSignOffAccount || selectedSignOffScheme || fromDate || toDate;

  const handleFilterSelectClearScheme = useHandleFilterSelectClear(signoffPayerSchemesFilterId);
  const handleFilterSelectClearAccount = useHandleFilterSelectClear(signoffPayerAccountsFilterId);

  const handleClear = () => {
    dispatch(setSelectedSignOffAccount(""));
    dispatch(setSelectedSignOffScheme(""));
    dispatch(setFromDate(""));
    dispatch(setToDate(""));
    handleFilterSelectClearScheme();
    handleFilterSelectClearAccount();
    resetClaimsSelection();
  };

  const resetClaimsSelection = () => {
    dispatch(setIsSignOffSelectAllActive(false));
    dispatch(setSignOffCheckedClaimsIds([]));
    dispatch(setSignOffSelectedClaimsSum(0));
  };

  useEffect(() => {
    if (membershipAccountsResponse?.data) {
      setPayerAccounts(membershipAccountsResponse?.data as MembershipAccount[]);
    }
    if (membershipSchemesResponse?.data) {
      setPayerSchemes(membershipSchemesResponse?.data as MembershipScheme[]);
    }
  }, [membershipAccountsResponse, membershipSchemesResponse]);

  return (
    <div className="flex items-center space-x-8 pt-2">
      <div className="grid grid-cols-4 gap-8">
        <div className="flex flex-col space-y-2">
          <label
            htmlFor={signoffPayerAccountsFilterId}
            className="flex items-center space-x-1 text-sm font-medium text-midGray"
          >
            <p>{SignOffSelectFilters.ACCOUNT}</p>
            <p className="text-customRed">*</p>
          </label>
          <Select
            value={selectedSignOffAccount || ""}
            options={mapToSelectOptions(
              payerAccounts.map((a) => ({ id: a.id, name: a.accountName })),
            )}
            placeholder="Select the account"
            onChange={(selectedId) => handleSelect(SignOffSelectFilters.ACCOUNT, selectedId)}
          />
        </div>
        <div className="flex flex-col space-y-2">
          <label htmlFor={signoffPayerSchemesFilterId} className="text-sm font-medium text-midGray">
            {SignOffSelectFilters.SCHEME}
          </label>
          <Select
            value={selectedSignOffScheme || ""}
            options={mapToSelectOptions(payerSchemes.map((s) => ({ id: s.id, name: s.name })))}
            placeholder="Select the scheme"
            onChange={(selectedId) => handleSelect(SignOffSelectFilters.SCHEME, selectedId)}
          />
        </div>

        <section className="flex flex-col space-y-2">
          <label
            htmlFor={"sign-off-date"}
            className="flex items-center space-x-1 text-sm font-medium text-midGray"
          >
            <p>Start Date</p>
            <p className="text-customRed">*</p>
          </label>

          <Calendar
            placeholder="Select the start date"
            value={fromDate as string}
            onChange={(value) => dispatch(setFromDate(value))}
          />
        </section>
        <section className="flex flex-col space-y-2">
          <label
            htmlFor={"sign-off-date"}
            className="flex items-center space-x-1 text-sm font-medium text-midGray"
          >
            <p>End Date</p>
            <p className="text-customRed">*</p>
          </label>
          <Calendar
            placeholder="Select the end date"
            value={toDate as string}
            onChange={(value) => dispatch(setToDate(value))}
          />
        </section>
      </div>
      <Button
        className="self-end whitespace-nowrap"
        disabled={!isFilterSelected}
        onClick={handleClear}
      >
        Clear Filters
      </Button>
    </div>
  );
}
