import { useAppSelector } from "../../store/hooks";

export enum DateRange {
  TODAY = "Today",
  YESTERDAY = "Yesterday",
  THIS_WEEK = "This Week",
  LAST_WEEK = "Last Week",
  THIS_MONTH = "This Month",
  LAST_MONTH = "Last Month",
  THIS_YEAR = "This Year",
  LAST_YEAR = "Last Year",
  ALL_TIME = "All time",
}

export default function DurationButton({
  text,
  onClick,
}: {
  text: DateRange;
  onClick?: () => void;
}) {
  const activeDateRange = useAppSelector((state) => state.signOff.activeDateRange);
  const isRangeActive = activeDateRange === text;
  return (
    <button
      className={`p-2 text-left text-xs text-midGray hover:bg-faintBlue hover:text-txtBlue ${isRangeActive && "bg-faintBlue text-txtBlue"}`}
      onClick={onClick}
    >
      {text}
    </button>
  );
}
