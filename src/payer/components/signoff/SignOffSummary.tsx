import { formatDate, formatValue } from "../../lib/Utils";
import { useAppSelector } from "../../store/hooks";

const SummaryItem = ({ label, value }: { label: string; value: string }) => {
  return (
    <div className="flex flex-col">
      <p className="whitespace-nowrap text-base text-txtBlue">{value}</p>
      <p className={`text-sm text-lightGray`}>{label}</p>
    </div>
  );
};

export default function SignOffSummary() {
  const signOffSelectedAccountName = useAppSelector(
    (state) => state.signOff.signOffSelectedAccountName,
  );
  const signOffSelectedClaimsSum = useAppSelector(
    (state) => state.signOff.signOffSelectedClaimsSum,
  );
  const fromDate = useAppSelector((state) => state.signOff.fromDate);
  const toDate = useAppSelector((state) => state.signOff.toDate);

  return (
    <section className="mt-8 flex min-h-[400px] w-full flex-col justify-between self-center rounded-lg border border-[#AFAFAF4D] bg-white p-4 shadow-md">
      <div>
        <h1 className="self-center py-2 text-center text-xl text-darkBlue">Claim Summary</h1>
        <div className="flex flex-col space-y-4 pt-4">
          <SummaryItem label="Account Name" value={signOffSelectedAccountName} />
          <SummaryItem label="Aggregate Amount" value={formatValue(signOffSelectedClaimsSum)} />
          <SummaryItem
            label="Cut-Off Dates"
            value={`${formatDate(fromDate)} - ${formatDate(toDate)}`}
          />
          <SummaryItem label="Sign-Off Date" value={formatDate(new Date().toLocaleDateString())} />
        </div>
      </div>
    </section>
  );
}
