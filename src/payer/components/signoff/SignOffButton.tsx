import { toast } from "react-toastify";
import { useVoucherVettedClaimsMutation } from "../../api/features/claimsApi";
import {
  setIsSignOffSelectAllActive,
  setSignOffCheckedClaimsIds,
  setSignOffSelectedClaimsSum,
} from "../../features/finance/signoff/signOffSlice";
import { VoucherClaimsPayload } from "../../lib/types/claims/invoice";
import UserService from "../../services/UserService";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import SignOffLoadingModal from "./SignOffLoadingModal";

type Props = {
  setShouldShowSuccessModal: React.Dispatch<React.SetStateAction<boolean>>;
};

function SignOffButton({ setShouldShowSuccessModal }: Props) {
  const payerId: string = UserService.getPayer()?.tokenParsed?.["payerId"];

  const selectedSignOffAccount = useAppSelector((state) => state.signOff.selectedSignOffAccount);
  const selectedSignOffScheme = useAppSelector((state) => state.signOff.selectedSignOffScheme);
  const fromDate = useAppSelector((state) => state.signOff.fromDate);
  const toDate = useAppSelector((state) => state.signOff.toDate);
  const signOffCheckedClaimsIds = useAppSelector((state) => state.signOff.signOffCheckedClaimsIds);

  const dispatch = useAppDispatch();

  const voucherClaimsPayload: VoucherClaimsPayload = {
    payerId,
    providerAccountIds: [Number(selectedSignOffAccount)],
    ...(selectedSignOffScheme && { planIds: [Number(selectedSignOffScheme)] }),
    startDate: (fromDate as string).split("T")[0] as string,
    endDate: (toDate as string).split("T")[0] as string,
    invoiceIds: signOffCheckedClaimsIds,
    actionedBy: UserService.getUsername(),
    isSignOff: true,
  };

  const [voucherClaims, { isLoading }] = useVoucherVettedClaimsMutation();

  async function handleSignOffClick() {
    try {
      if (signOffCheckedClaimsIds.length === 0) return;

      await voucherClaims(voucherClaimsPayload).unwrap();

      dispatch(setSignOffCheckedClaimsIds([]));
      dispatch(setSignOffSelectedClaimsSum(0));
      dispatch(setIsSignOffSelectAllActive(false));

      setShouldShowSuccessModal(true);
    } catch (error) {
      if (
        error &&
        typeof error === "object" &&
        "data" in error &&
        error.data &&
        typeof error.data === "object" &&
        "error" in error.data &&
        typeof error.data.error === "string"
      ) {
        toast.error(error.data.error);
      } else {
        toast.error("An error occurred while performing signoff");
      }
      console.error("Error: ", error);
    }
  }

  return (
    <>
      <button
        className={`${
          signOffCheckedClaimsIds.length < 1
            ? "hidden"
            : "w-fit cursor-pointer rounded-md bg-btnBlue px-3 py-1.5 text-[10px] font-semibold text-white"
        }`}
        onClick={handleSignOffClick}
      >
        Voucher Claims
      </button>

      <SignOffLoadingModal show={isLoading} />
    </>
  );
}

export default SignOffButton;
