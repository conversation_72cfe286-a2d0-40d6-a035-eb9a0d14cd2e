import { ChangeEvent } from "react";
import {
  setIsSignOffSelectAllActive,
  setSignOffCheckedClaimsIds,
  setSignOffSelectedClaimsSum,
} from "../../features/finance/signoff/signOffSlice";
import { InvoiceData } from "../../lib/types/claims/invoice";
import { formatDate, formatValue } from "../../lib/Utils";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import Badge from "../ui/Badge";
import TableDataItem from "../ui/table/TableDataItem";

export default function SignOffClaimsTableRow({ invoice }: { invoice: InvoiceData }) {
  const dispatch = useAppDispatch();
  const signOffCheckedClaimsIds = useAppSelector((state) => state.signOff.signOffCheckedClaimsIds);
  const signOffSelectedClaimsSum = useAppSelector(
    (state) => state.signOff.signOffSelectedClaimsSum,
  );

  const handleClick = (e: ChangeEvent<HTMLInputElement>, invoice: InvoiceData) => {
    const { checked } = e.target;
    if (checked) {
      dispatch(setSignOffCheckedClaimsIds([...signOffCheckedClaimsIds, invoice.id as string]));
      dispatch(setSignOffSelectedClaimsSum(signOffSelectedClaimsSum + (invoice.totalAmount ?? 0)));
    } else {
      dispatch(
        setSignOffCheckedClaimsIds(signOffCheckedClaimsIds.filter((item) => item !== invoice.id)),
      );
      dispatch(setSignOffSelectedClaimsSum(signOffSelectedClaimsSum - (invoice.totalAmount ?? 0)));
      dispatch(setIsSignOffSelectAllActive(false));
    }
  };

  return (
    <tr key={invoice.id} className="">
      <td className="whitespace-nowrap border-b border-[#EAECF0] px-4 py-2 uppercase">
        <input
          className="form-checkbox"
          type="checkbox"
          checked={signOffCheckedClaimsIds.includes(invoice.id as string)}
          onChange={(e) => handleClick(e, invoice)}
        />
      </td>
      <TableDataItem className="text-xs" item={invoice?.memberNumber} />
      <TableDataItem className="text-xs" item={invoice?.memberName} />
      <TableDataItem className="text-xs" item={invoice?.invoiceNumber} />
      <TableDataItem className="text-xs" item={formatDate(invoice?.invoiceDate)} />
      <TableDataItem className="text-xs" item={formatValue(invoice?.totalAmount)} />
      <td>
        {invoice?.vettingStatus == "APPROVED" ? (
          <Badge textClassName="text-xs" color="green" text="Vetted" hasDot />
        ) : invoice?.vettingStatus == "PENDING" ? (
          <Badge textClassName="text-xs" color="red" text="Unvetted" hasDot />
        ) : invoice?.vettingStatus == "PARTIAL" ? (
          <Badge textClassName="text-xs" color="green" text="Vetted" hasDot />
        ) : (
          invoice?.vettingStatus == "DECLINED" && (
            <Badge textClassName="text-xs" color="green" text="Vetted" hasDot />
          )
        )}
      </td>
    </tr>
  );
}
