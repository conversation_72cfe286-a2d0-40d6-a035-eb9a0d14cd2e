import LoadingAnimation from "../animations/LoadingAnimation/LoadingAnimation";
import DialogWrapper from "../ui/modal/DialogWrapper";

type Props = {
  show: boolean;
};
export default function SignOffLoadingModal({ show }: Props) {
  return (
    <DialogWrapper
      className="flex flex-col items-center gap-4 p-6"
      maxWidth="max-w-[750px]"
      show={show}
      onClose={() => null}
    >
      <LoadingAnimation size={80} />
      <p className="mt-4 text-2xl font-medium text-[#2563EB]">Performing Sign off...</p>
      <p className="text-center text-sm text-[#6B7280]">Please wait as we process your request.</p>
    </DialogWrapper>
  );
}
