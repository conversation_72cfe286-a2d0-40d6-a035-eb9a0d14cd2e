import { RequestType, requestTypeLabels } from "~lib/api/types";
import { clsx } from "~lib/utils";
import { setAiAdjudicationSelectedBenefitType } from "../../features/ai/aiAdjudicationSlice";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import { requestTypeIcon } from "../RequestTypeIcon";

export default function AIPreAuthBenefitsFilter() {
  const dispatch = useAppDispatch();
  const selectedBenefitType = useAppSelector(
    (state) => state.aiAdjudication.aiAdjudicationSelectedBenefitType,
  );

  const handleBenefitSelection = (requestType: RequestType) => {
    if (selectedBenefitType === requestType) {
      dispatch(setAiAdjudicationSelectedBenefitType(""));
    } else {
      dispatch(setAiAdjudicationSelectedBenefitType(requestType));
    }
  };

  return (
    <div className="mb-8 flex flex-wrap justify-around gap-8">
      {Object.values(RequestType).map((requestType) => (
        <button
          className="flex flex-col items-center justify-center focus:outline-customBlue"
          key={requestType}
          onClick={() => handleBenefitSelection(requestType)}
        >
          <div
            className={clsx(
              "mb-2 flex h-28 w-28 cursor-pointer items-center justify-center rounded-lg border",
              selectedBenefitType === requestType
                ? "border-blue-900 bg-blue-900 text-white"
                : "border border-gray-200 bg-gray-50 text-blue-900",
            )}
          >
            {requestTypeIcon(requestType)}
          </div>

          <p className="text-sm font-medium">{requestTypeLabels.get(requestType)} Visit</p>
        </button>
      ))}
    </div>
  );
}
