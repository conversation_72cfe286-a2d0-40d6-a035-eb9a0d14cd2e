import { clsx } from "~lib/utils";
import { setAiAdjudicationSearchQuery } from "../../features/ai/aiAdjudicationSlice";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import { InformationCircleIcon } from "@heroicons/react/24/outline";

export default function AIAdjudicationSearchInput() {
  const dispatch = useAppDispatch();
  const aiPreAuthSearchQuery = useAppSelector(
    (state) => state.aiAdjudication.aiAdjudicationSearchQuery,
  );

  const handleSearch = (searchTerm: string) => {
    dispatch(setAiAdjudicationSearchQuery(searchTerm));
  };

  return (
    <div className="flex flex-col space-y-2">
      <label htmlFor="query" className="flex gap-1 text-sm font-medium text-midGray">
        Search
        <span className="cursor-pointer" title="Enter member number, member name or visit number.">
          <InformationCircleIcon className="h-5 w-5" />
        </span>
      </label>
      <input
        type="text"
        id="query"
        name="query"
        title="Enter member number, member name or visit number."
        placeholder="Enter member number, member name or visit number..."
        className={clsx(
          "w-[50ch] rounded-md border border-gray-300 px-2 text-sm placeholder:text-sm focus:outline-none focus:ring-0",
        )}
        value={aiPreAuthSearchQuery}
        onChange={(e) => handleSearch(e.target.value)}
      />
    </div>
  );
}
