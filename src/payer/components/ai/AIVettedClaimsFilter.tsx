import { useEffect, useState } from "react";
import { useGetAllAgeBandsQuery } from "../../api/features/claimsApi";
import {
  useGetMembershipBenefitsCatalogQuery,
  useGetMembershipProvidersQuery,
  useGetMembershipRegionsQuery,
  useGetMembershipSchemesQuery,
} from "../../api/features/membershipApi";
import {
  setAiEndDate,
  setAiProvidersSearchQuery,
  setAiSelectedAgeBandId,
  setAiSelectedProviderId,
  setAiSelectedRegion,
  setAiSelectedScheme,
  setAiSelectedServiceGroup,
  setAiStartDate,
} from "../../features/ai/aiVettingSlice";
import { useHandleFilterSelectClear } from "../../hooks/useHandleFilterSelectClear";
import { AgeBand, AgeBandType } from "../../lib/types/claims/ageBand";
import { MembershipBenefitCatalog } from "../../lib/types/membership/memberBenefit";
import {
  MembershipProvider,
  MembershipProvidersQuery,
} from "../../lib/types/membership/memberProvider";
import { MembershipRegion } from "../../lib/types/membership/memberRegion";
import { MembershipScheme } from "../../lib/types/membership/memberScheme";
import UserService from "../../services/UserService";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import { isValidId } from "../../utils/utils";
import { BatchingCriteria } from "../claimsBatching/BatchingCriteriaSelector";
import Button from "../ui/Button";
import Calendar from "../ui/input/Calender";
import FilterSelect from "../ui/input/FilterSelect";

interface AIVettedClaimsFilterSelect<OptType, ValType> {
  name: BatchingCriteria;
  options: Array<string | OptType>;
  value: ValType;
}

export default function AIVettedClaimsFilter() {
  const [payerRegions, setPayerRegions] = useState<MembershipRegion[]>([]);
  const [payerProviders, setPayerProviders] = useState<MembershipProvider[]>([]);
  const [payerBenefitCatalogs, setPayerBenefitCatalogs] = useState<MembershipBenefitCatalog[]>([]);
  const [payerSchemes, setPayerSchemes] = useState<MembershipScheme[]>([]);
  const [payerAgeBands, setPayerAgeBands] = useState<AgeBand[]>([]);
  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const { data: membershipRegionsResponse } = useGetMembershipRegionsQuery(payerId);
  const { data: membershipBenefitsResponse } = useGetMembershipBenefitsCatalogQuery(payerId);
  const { data: membershipSchemesResponse } = useGetMembershipSchemesQuery(payerId);

  const bandType: AgeBandType = "BATCHING";
  const { data: ageBandsResponse } = useGetAllAgeBandsQuery({ bandType });
  const providersSearchQuery = useAppSelector((state) => state.aiVetting.aiProvidersSearchQuery);

  /* providers filtering */
  const providersQuery: MembershipProvidersQuery = {
    payerId,
    ...(providersSearchQuery.length > 0 && { query: providersSearchQuery }),
  };

  const { data: membershipProvidersResponse } = useGetMembershipProvidersQuery(providersQuery);

  const dispatch = useAppDispatch();

  /** ai filtering state */
  const selectedBenefitId = useAppSelector((state) => state.aiVetting.aiSelectedBenefitId);
  const selectedProviderId = useAppSelector((state) => state.aiVetting.aiSelectedProviderId);
  const selectedRegion = useAppSelector((state) => state.aiVetting.aiSelectedRegion);
  const selectedScheme = useAppSelector((state) => state.aiVetting.aiSelectedScheme);
  const selectedServiceGroup = useAppSelector((state) => state.aiVetting.aiSelectedServiceGroup);
  const startDate = useAppSelector((state) => state.aiVetting.aiStartDate);
  const endDate = useAppSelector((state) => state.aiVetting.aiEndDate);

  const benefitsFilter: AIVettedClaimsFilterSelect<MembershipBenefitCatalog, string> = {
    name: BatchingCriteria.BENEFIT,
    options: ["Select benefit", ...payerBenefitCatalogs],
    value: selectedBenefitId,
  };

  const providersFilter: AIVettedClaimsFilterSelect<MembershipProvider, string> = {
    name: BatchingCriteria.PROVIDER,
    options: ["Select provider", ...payerProviders],
    value: selectedProviderId,
  };

  const regionsFilter: AIVettedClaimsFilterSelect<MembershipRegion, string> = {
    name: BatchingCriteria.REGION,
    options: ["Select region", ...payerRegions],
    value: selectedRegion,
  };

  const schemesFilter: AIVettedClaimsFilterSelect<MembershipScheme, string> = {
    name: BatchingCriteria.SCHEME,
    options: ["Select scheme", ...payerSchemes],
    value: selectedScheme?.name || "",
  };

  const aiVettedClaimsFilters = [benefitsFilter, providersFilter, regionsFilter, schemesFilter];

  const handleSelect = (
    criteria: BatchingCriteria,
    selectedOption:
      | AgeBand
      | MembershipProvider
      | MembershipRegion
      | MembershipBenefitCatalog
      | MembershipScheme
      | Option
      | null,
  ) => {
    switch (criteria) {
      case BatchingCriteria.REGION:
        dispatch(setAiSelectedRegion((selectedOption as MembershipRegion)?.name || ""));
        break;
      case BatchingCriteria.PROVIDER:
        dispatch(
          setAiSelectedProviderId(((selectedOption as MembershipProvider)?.id as string) || ""),
        );
        break;
      case BatchingCriteria.BENEFIT:
        dispatch(setAiSelectedServiceGroup(selectedOption?.name || ""));
        break;
      case BatchingCriteria.AGING:
        dispatch(setAiSelectedAgeBandId(((selectedOption as AgeBand)?.id as string) || ""));
        break;
      case BatchingCriteria.SCHEME:
        dispatch(setAiSelectedScheme(selectedOption as MembershipScheme));
        break;
    }
  };

  useEffect(() => {
    if (membershipRegionsResponse?.data) {
      setPayerRegions(membershipRegionsResponse?.data);
    }
    if (membershipProvidersResponse?.data?.content) {
      setPayerProviders(membershipProvidersResponse.data.content);
    }
    if (membershipBenefitsResponse?.data) {
      setPayerBenefitCatalogs(membershipBenefitsResponse?.data);
    }
    if (ageBandsResponse?.data) {
      setPayerAgeBands(ageBandsResponse?.data as AgeBand[]);
    }
    if (membershipSchemesResponse?.data) {
      setPayerSchemes(membershipSchemesResponse?.data as MembershipScheme[]);
    }
  }, [
    membershipRegionsResponse,
    membershipProvidersResponse,
    membershipBenefitsResponse,
    ageBandsResponse,
    membershipSchemesResponse,
  ]);

  const filterSelected =
    selectedServiceGroup ||
    selectedProviderId ||
    selectedRegion ||
    isValidId(selectedScheme?.id) ||
    startDate ||
    endDate;

  const handleFilterSelectClearAging = useHandleFilterSelectClear(
    `filter-select-${BatchingCriteria.AGING}`,
  );
  const handleFilterSelectClearBenefit = useHandleFilterSelectClear(
    `filter-select-${BatchingCriteria.BENEFIT}`,
  );
  const handleFilterSelectClearProvider = useHandleFilterSelectClear(
    `filter-select-${BatchingCriteria.PROVIDER}`,
  );
  const handleFilterSelectClearRegion = useHandleFilterSelectClear(
    `filter-select-${BatchingCriteria.REGION}`,
  );
  const handleFilterSelectClearScheme = useHandleFilterSelectClear(
    `filter-select-${BatchingCriteria.SCHEME}`,
  );

  const clearFilterSelectValues = () => {
    handleFilterSelectClearAging();
    handleFilterSelectClearBenefit();
    handleFilterSelectClearProvider();
    handleFilterSelectClearRegion();
    handleFilterSelectClearScheme();
  };

  const clearFilters = () => {
    dispatch(setAiSelectedAgeBandId(""));
    dispatch(setAiSelectedProviderId(""));
    dispatch(setAiSelectedRegion(""));
    dispatch(setAiSelectedScheme({} as MembershipScheme));
    dispatch(setAiSelectedServiceGroup(""));
    dispatch(setAiStartDate(""));
    dispatch(setAiEndDate(""));
    clearFilterSelectValues();
  };

  function convertToUniqueServiceGroups(catalogItems: Array<MembershipBenefitCatalog>): Option[] {
    const uniqueServiceGroups = new Map<string, string>();

    catalogItems.forEach((item) => {
      if (!uniqueServiceGroups.has(item.serviceGroup)) {
        uniqueServiceGroups.set(item.serviceGroup, item.serviceGroup);
      }
    });

    return Array.from(uniqueServiceGroups).map(([serviceGroup, id]) => ({
      id: id,
      name: serviceGroup,
    }));
  }

  const uniqueBenefitServiceGroups = convertToUniqueServiceGroups(payerBenefitCatalogs);
  const getSelectOptions = (criteria: BatchingCriteria) => {
    const optionsMap = {
      [BatchingCriteria.REGION]: payerRegions,
      [BatchingCriteria.PROVIDER]: payerProviders,
      [BatchingCriteria.BENEFIT]: uniqueBenefitServiceGroups,
      [BatchingCriteria.AGING]: payerAgeBands,
      [BatchingCriteria.SCHEME]: payerSchemes,
    };
    return optionsMap[criteria];
  };

  interface Option {
    id: string;
    name: string;
  }

  const handleProvidersSearch = (searchTerm: string) => {
    dispatch(setAiProvidersSearchQuery(searchTerm));
  };

  const handleStartDateChange = (value: string) => {
    dispatch(setAiStartDate(value));
  };

  const handleEndDateChange = (value: string) => {
    dispatch(setAiEndDate(value));
  };

  return (
    <section className="flex w-full items-center justify-between space-x-2 py-6">
      {aiVettedClaimsFilters.map((criteria) => (
        <div className="flex flex-col space-y-2" key={criteria.name}>
          <label htmlFor={criteria.name} className="text-sm font-medium text-midGray">
            {criteria.name}
          </label>
          <FilterSelect
            nameSelector={(option: Option) => option.name}
            idSelector={(option: Option) => option.id}
            id={`filter-select-${criteria.name}`}
            name={criteria.name}
            options={getSelectOptions(criteria.name) as Option[]}
            placeholder={criteria.options[0] as string}
            onSelect={(selected) => handleSelect(criteria.name, selected)}
            handleExternalSearch={
              criteria.name === BatchingCriteria.PROVIDER ? handleProvidersSearch : undefined
            }
          />
        </div>
      ))}
      <section className="flex w-[200px] flex-col space-y-2">
        <label htmlFor={"sign-off-date"} className="text-sm font-medium text-midGray">
          <p>Start Date</p>
        </label>
        <Calendar
          placeholder="Start Date"
          value={startDate as string}
          onChange={(value) => handleStartDateChange(value)}
        />
      </section>
      <section className="flex flex-col space-y-2">
        <label htmlFor={"sign-off-date"} className="text-sm font-medium text-midGray">
          <p>End Date</p>
        </label>
        <Calendar
          placeholder="End Date"
          value={endDate as string}
          onChange={(value) => handleEndDateChange(value)}
        />
      </section>
      <Button
        className="self-end whitespace-nowrap"
        disabled={!filterSelected}
        onClick={clearFilters}
      >
        Clear Filter
      </Button>
    </section>
  );
}
