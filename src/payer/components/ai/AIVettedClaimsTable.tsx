import { useEffect, useState } from "react";
import { Empty } from "~lib/components";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { useGetAllClaimsQuery } from "../../api/features/claimsApi";
import {
  GetAllClaimsParams,
  InvoiceBatchStatus,
  InvoiceData,
  InvoiceServiceGroup,
  InvoiceStatus,
} from "../../lib/types/claims/invoice";
import { formatDate, formatValue } from "../../lib/Utils";
import UserService from "../../services/UserService";
import { useAppSelector } from "../../store/hooks";
import { isValidFilterName } from "../../utils/utils";
import PrimaryPagination from "../ui/pagination/PrimaryPagination";
import TableDataItem from "../ui/table/TableDataItem";
import TableHeaderItem from "../ui/table/TableHeaderItem";
import AIVettedClaimsFilter from "./AIVettedClaimsFilter";

export default function AIVettedClaimsTable() {
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(10);
  const [claims, setClaims] = useState<InvoiceData[]>([]);

  const selectedProviderId = useAppSelector((state) => state.aiVetting.aiSelectedProviderId);
  const selectedRegion = useAppSelector((state) => state.aiVetting.aiSelectedRegion);
  const selectedAgeBandId = useAppSelector((state) => state.aiVetting.aiSelectedAgeBandId);
  const selectedScheme = useAppSelector((state) => state.aiVetting.aiSelectedScheme);
  const selectedServiceGroup = useAppSelector((state) => state.aiVetting.aiSelectedServiceGroup);
  const startDate = useAppSelector((state) => state.aiVetting.aiStartDate);
  const endDate = useAppSelector((state) => state.aiVetting.aiEndDate);

  const payerId: string = UserService.getPayer()?.tokenParsed?.["payerId"];
  const batchStatus: InvoiceBatchStatus = "NOT_BATCHED";
  const invoiceStatuses: InvoiceStatus[] = ["BALANCE_DEDUCTED"];

  const claimsToBeBatchedParams: GetAllClaimsParams = {
    payerId,
    providerId: Number(selectedProviderId),
    ageBandId: Number(selectedAgeBandId),
    region: selectedRegion,
    batchStatus,
    size,
    page,
    invoiceStatuses,
    ...(startDate && { startDate: (startDate as string).split("T")[0] as string }),
    ...(endDate && { endDate: (endDate as string).split("T")[0] as string }),
    ...(selectedScheme?.id && { planIds: [selectedScheme.id] }),
    ...(isValidFilterName(selectedServiceGroup) && {
      serviceGroups: [selectedServiceGroup as InvoiceServiceGroup],
    }),
  };

  const { data, isLoading, isFetching, error } = useGetAllClaimsQuery(claimsToBeBatchedParams);

  const totalElements = data?.data.totalElements as number;
  const totalPages = data?.data.totalPages as number;

  const handlePageNumberClick = (page: number) => {
    setPage(page);
  };

  const handleSizeChange = (size: number) => {
    setSize(size);
    setPage(1);
  };

  useEffect(() => {
    if (data?.data.content) {
      setClaims(data.data.content);
    }
  }, [data]);

  return (
    <div className="max-w-full overflow-x-auto">
      <h2 className="text-lg font-medium capitalize text-[#111827]">Vetted Claims List</h2>
      <AIVettedClaimsFilter />
      <section className={`${isLoading || (isFetching && "py-40")}`}>
        {isLoading || isFetching ? (
          <div className="flex items-center justify-center space-x-2 self-center py-8">
            <LoadingIcon className="h-6 w-6 text-blue-400" />
            <p className="text-blue-700">Loading vetted claims...</p>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center self-center py-8">
            <p className="text-red-700">Error loading vetted claims. Try again later.</p>
          </div>
        ) : (
          claims?.length === 0 && (
            <div className="self-center py-20">
              <Empty message="Claims not Found" />
            </div>
          )
        )}
      </section>
      {claims.length > 0 && !isLoading && !isFetching && !error && (
        <>
          <table className="mt-4 w-full">
            <thead>
              <tr className="bg-gray-50">
                <TableHeaderItem item="Member No" />
                <TableHeaderItem item="Member Name" />
                <TableHeaderItem item="Scheme" />
                <TableHeaderItem item="Provider" />
                <TableHeaderItem item="Invoice No" />
                <TableHeaderItem item="Invoice Amount" />
                <TableHeaderItem item="Invoice Date" />
                <TableHeaderItem item="Action" />
              </tr>
            </thead>
            <tbody>
              {claims.map((claim) => {
                return (
                  <tr key={claim.id}>
                    <TableDataItem item={claim?.memberNumber} />
                    <TableDataItem item={claim?.memberName} />
                    <TableDataItem item={claim?.schemeName} />
                    <TableDataItem item={claim?.providerName} />
                    <TableDataItem item={claim?.invoiceNumber} />
                    <TableDataItem item={formatValue(claim?.totalAmount)} />
                    <TableDataItem item={formatDate(claim?.invoiceDate)} />
                    <td className="cursor-pointer border-b border-[#EAECF0] px-2 py-4 text-center text-xs font-medium text-txtBlue underline underline-offset-4">
                      Preview
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
          <div className="self-start py-4">
            <PrimaryPagination
              pageSize={size}
              totalElements={totalElements ?? 0}
              totalPages={totalPages ?? 1}
              pageNumber={page}
              onPageNumberClick={handlePageNumberClick}
              onSizeChange={handleSizeChange}
            />
          </div>
        </>
      )}
    </div>
  );
}
