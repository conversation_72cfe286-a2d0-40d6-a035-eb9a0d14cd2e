import {
  setAiAdjudicationEndDate,
  setAiAdjudicationSearchQuery,
  setAiAdjudicationSelectedProviderId,
  setAiAdjudicationSelectedScheme,
  setAiAdjudicationSelectedStatus,
  setAiAdjudicationStartDate,
} from "../../features/ai/aiAdjudicationSlice";
import { AdjudicationStatus } from "../../lib/types/claims/adjudication";
import { MembershipScheme } from "../../lib/types/membership/memberScheme";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import { isValidId } from "../../utils/utils";
import Button from "../ui/Button";
import Calendar from "../ui/input/Calender";
import { Select } from "../ui/input/Select";
import AIAdjudicationSearchInput from "./AIAdjudicationSearchInput";

export default function AIAdjudicationFilters() {
  const adjudicationStatuses: Array<{ id: AdjudicationStatus; name: string }> = [
    { id: "Adjudicating", name: "Adjudicating" },
    { id: "Complete", name: "Complete" },
    { id: "Errored", name: "Errored" },
    { id: "Pending", name: "Pending" },
    { id: "AwaitingManualReview", name: "Awaiting Manual Review" },
  ];

  const dispatch = useAppDispatch();

  /** ai preauths filtering state */
  const selectedProviderId = useAppSelector(
    (state) => state.aiAdjudication.aiAdjudicationSelectedProviderId,
  );
  const selectedScheme = useAppSelector(
    (state) => state.aiAdjudication.aiAdjudicationSelectedScheme,
  );
  const status = useAppSelector((state) => state.aiAdjudication.aiAdjudicationSelectedStatus);
  const startDate = useAppSelector((state) => state.aiAdjudication.aiAdjudicationStartDate);
  const endDate = useAppSelector((state) => state.aiAdjudication.aiAdjudicationEndDate);
  const searchQuery = useAppSelector((state) => state.aiAdjudication.aiAdjudicationSearchQuery);

  const filterSelected =
    status ||
    selectedProviderId ||
    isValidId(selectedScheme?.id) ||
    startDate ||
    endDate ||
    searchQuery;

  const clearFilters = () => {
    dispatch(setAiAdjudicationSelectedProviderId(""));
    dispatch(setAiAdjudicationSelectedScheme({} as MembershipScheme));
    dispatch(setAiAdjudicationStartDate(""));
    dispatch(setAiAdjudicationEndDate(""));
    dispatch(setAiAdjudicationSelectedStatus("" as AdjudicationStatus));
    dispatch(setAiAdjudicationSearchQuery(""));
  };

  const handleStartDateChange = (value: string) => {
    dispatch(setAiAdjudicationStartDate(value));
  };

  const handleEndDateChange = (value: string) => {
    dispatch(setAiAdjudicationEndDate(value));
  };

  return (
    <section className="flex w-full items-center space-x-8 py-2">
      <AIAdjudicationSearchInput />
      <section className="flex flex-col space-y-2">
        <label htmlFor="status" className="text-sm font-medium text-midGray">
          Status
        </label>
        <Select
          value={status}
          onChange={(value) =>
            dispatch(setAiAdjudicationSelectedStatus(value as AdjudicationStatus))
          }
          options={[
            { key: "", value: "", label: "All Statuses" },
            ...adjudicationStatuses.map((status) => ({
              key: status.id,
              value: status.id,
              label: status.name,
            })),
          ]}
          placeholder="All Statuses"
          containerClassName="w-[20ch]"
        />
      </section>
      <section className="flex w-[200px] flex-col space-y-2">
        <label htmlFor={"sign-off-date"} className="text-sm font-medium text-midGray">
          Start Date
        </label>
        <Calendar
          placeholder="Start Date"
          value={startDate as string}
          onChange={(value) => handleStartDateChange(value)}
        />
      </section>
      <section className="flex flex-col space-y-2">
        <label htmlFor={"sign-off-date"} className="text-sm font-medium text-midGray">
          End Date
        </label>
        <Calendar
          placeholder="End Date"
          value={endDate as string}
          onChange={(value) => handleEndDateChange(value)}
        />
      </section>
      <Button
        className="self-end whitespace-nowrap"
        disabled={!filterSelected}
        onClick={clearFilters}
      >
        Clear Filter
      </Button>
    </section>
  );
}
