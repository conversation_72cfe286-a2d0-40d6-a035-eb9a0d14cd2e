import { useEffect, useState } from "react";
import { Empty } from "~lib/components";
import LoadingIcon from "~lib/components/icons/LoadingIcon";
import { truncate } from "~lib/utils";
import { useGetAdjudicationClaimsQuery } from "../../api/features/claimsApi";
import {
  AdjudicationStatus,
  ClaimAdjudication,
  ClaimAdjudicationSearchParams,
} from "../../lib/types/claims/adjudication";
import { formatDate } from "../../lib/Utils";
import UserService from "../../services/UserService";
import { useAppSelector } from "../../store/hooks";
import PrimaryPagination from "../ui/pagination/PrimaryPagination";
import TableDataItem from "../ui/table/TableDataItem";
import TableHeaderItem from "../ui/table/TableHeaderItem";
import AIAdjudicationFilters from "./AIAdjudicationFilters";
import Text from "../ui/typography/Text";
import { getAiAdjudicationTextAndColorFromStatus } from "../../utils/utils";
import Badge from "../ui/Badge";

export default function AIAdjudicatedClaimsTable() {
  const [adjudicatedClaims, setAdjudicatedClaims] = useState<Array<ClaimAdjudication>>([]);
  const [page, setPage] = useState(1);
  const [size, setSize] = useState<number>(10);
  const _status = useAppSelector((state) => state.aiAdjudication.aiAdjudicationSelectedStatus);
  const requestType = useAppSelector(
    (state) => state.aiAdjudication.aiAdjudicationSelectedBenefitType,
  );
  const _requestTypes: Array<string> = [requestType && requestType];
  const search = useAppSelector((state) => state.aiAdjudication.aiAdjudicationSearchQuery);
  const adjudicationSelectedStatus = useAppSelector(
    (state) => state.aiAdjudication.aiAdjudicationSelectedStatus,
  );

  const adjudicationStatuses: Array<AdjudicationStatus> = adjudicationSelectedStatus
    ? [adjudicationSelectedStatus]
    : [];

  const handlePageNumberClick = (page: number) => {
    setPage(page);
  };

  const handleSizeChange = (size: number) => {
    setSize(size);
    setPage(1);
  };

  const payerId = UserService.getPayer()?.tokenParsed?.["payerId"];
  const sortOrder = "desc";
  const sortColumn = "createdAt";
  const fromDate = useAppSelector((state) => state.aiAdjudication.aiAdjudicationStartDate).split(
    "T",
  )[0] as string;
  const toDate = useAppSelector((state) => state.aiAdjudication.aiAdjudicationEndDate).split(
    "T",
  )[0] as string;

  const adjudicatedClaimsSearchParams: ClaimAdjudicationSearchParams = {
    payerId,
    page,
    size,
    search,
    sortColumn,
    sortOrder,
    fromDate,
    toDate,
    adjudicationStatuses,
  };

  const {
    data: preauthsData,
    isLoading,
    isFetching,
    error,
  } = useGetAdjudicationClaimsQuery(adjudicatedClaimsSearchParams);

  useEffect(() => {
    if (preauthsData?.data.content) {
      setAdjudicatedClaims(preauthsData.data.content);
    }
  }, [preauthsData]);

  const isLoadingDataOrIsError = isLoading || isFetching || error;

  return (
    <div className="h-full max-w-full overflow-x-auto">
      <Text variant="heading" className="text-lg">
        Visits
      </Text>
      <AIAdjudicationFilters />
      <section className={`${isLoadingDataOrIsError && "py-40"}`}>
        {isLoading || isFetching ? (
          <div className="flex items-center justify-center space-x-2 self-center py-8">
            <LoadingIcon className="h-6 w-6 text-blue-400" />
            <p className="text-blue-700">Loading Visits...</p>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center self-center py-8">
            <p className="text-red-700">Error loading visits. Try again later.</p>
          </div>
        ) : (
          adjudicatedClaims?.length === 0 && (
            <div className="py-40">
              <Empty message="No Adjudicated Claims Found" />
            </div>
          )
        )}
      </section>
      {adjudicatedClaims.length > 0 && !isLoading && !isFetching && !error && (
        <>
          <table className="mt-4 w-full">
            <thead>
              <tr className="bg-gray-50">
                <TableHeaderItem className="text-xs" item="Visit No" />
                <TableHeaderItem className="text-xs" item="Member No" />
                <TableHeaderItem className="text-xs" item="Member Name" />
                <TableHeaderItem className="text-xs" item="Benefit" />
                <TableHeaderItem className="text-xs" item="Reference No" />
                <TableHeaderItem className="text-xs" item="Date" />
                <TableHeaderItem className="text-xs" item="Status" />
                <TableHeaderItem className="text-xs" item="Results" />
              </tr>
            </thead>
            <tbody>
              {adjudicatedClaims.map((adjudicatedClaim) => {
                const [text, color] = getAiAdjudicationTextAndColorFromStatus(
                  adjudicatedClaim.status,
                );
                return (
                  <tr key={adjudicatedClaim.id}>
                    <TableDataItem className="text-xs" item={adjudicatedClaim.visitNumber} />
                    <TableDataItem className="text-xs" item={adjudicatedClaim?.memberNumber} />
                    <TableDataItem className="text-xs" item={adjudicatedClaim?.memberName} />
                    <TableDataItem
                      className="text-xs"
                      item={truncate(adjudicatedClaim?.benefitName, 30) || "-"}
                      title={adjudicatedClaim?.benefitName || "-"}
                    />
                    {adjudicatedClaim.reference ? (
                      <TableDataItem className="text-xs" item={adjudicatedClaim.reference || "-"} />
                    ) : (
                      <td className="border-b border-[#EAECF0] text-center text-xs text-customGray">
                        {"-"}
                      </td>
                    )}
                    <TableDataItem
                      className="text-xs"
                      item={
                        adjudicatedClaim.createdAt
                          ? formatDate(new Date(adjudicatedClaim.createdAt))
                          : ""
                      }
                    />
                    <TableDataItem>
                      <Badge color={color} text={text} textClassName="text-xs" />
                    </TableDataItem>
                    {adjudicatedClaim.errorMsg ? (
                      <td
                        className="cursor-pointer border-b border-[#EAECF0] text-xs text-customRed"
                        title={adjudicatedClaim.errorMsg}
                      >
                        {adjudicatedClaim.errorMsg.length > 30
                          ? adjudicatedClaim.errorMsg.slice(0, 30) + "..."
                          : adjudicatedClaim.errorMsg}
                      </td>
                    ) : (
                      <td className="cursor-pointer border-b border-[#EAECF0] py-2 text-xs font-medium text-txtBlue underline underline-offset-4">
                        <a href={adjudicatedClaim.resultUrl} target="_blank">
                          Preview
                        </a>
                      </td>
                    )}
                  </tr>
                );
              })}
            </tbody>
          </table>
          <div className="self-start py-4">
            <PrimaryPagination
              pageSize={size}
              totalElements={preauthsData?.data?.totalElements ?? 0}
              totalPages={preauthsData?.data?.totalPages ?? 1}
              pageNumber={page}
              onPageNumberClick={handlePageNumberClick}
              onSizeChange={handleSizeChange}
            />
          </div>
        </>
      )}
    </div>
  );
}
