import { render, screen, fireEvent } from "@testing-library/react";
import Button from "../../../components/ui/Button";

describe("Button component", () => {
  it("renders children correctly", () => {
    render(<Button>Click Me</Button>);
    expect(screen.getByText("Click Me")).toBeInTheDocument();
  });

  it("calls onClick when clicked", () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Click Me</Button>);
    fireEvent.click(screen.getByText("Click Me"));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it("renders with correct variant classes", () => {
    const { rerender } = render(<Button variant="filled">Filled</Button>);
    expect(screen.getByText("Filled")).toHaveClass("bg-btnBlue", "text-white");

    rerender(<Button variant="outlined">Outlined</Button>);
    expect(screen.getByText("Outlined")).toHaveClass("border", "border-lightGray", "text-darkGray");
  });

  it("disables the button when disabled prop is true", () => {
    render(<Button disabled>Disabled</Button>);
    const button = screen.getByText("Disabled");
    expect(button).toBeDisabled();
    expect(button).toHaveClass("disabled:cursor-not-allowed", "disabled:opacity-60");
  });

  it("sets the correct type attribute", () => {
    const { rerender } = render(<Button type="submit">Submit</Button>);
    expect(screen.getByText("Submit")).toHaveAttribute("type", "submit");

    rerender(<Button type="reset">Reset</Button>);
    expect(screen.getByText("Reset")).toHaveAttribute("type", "reset");
  });
});
