import { render, screen } from "@testing-library/react";
import Badge from "../../../components/ui/Badge";

describe("Badge component", () => {
  it("renders the text correctly", () => {
    render(<Badge color="red" text="Important" />);
    expect(screen.getByText("Important")).toBeInTheDocument();
  });

  it("applies the correct background color", () => {
    const { rerender } = render(<Badge color="red" text="Red Badge" />);
    expect(screen.getByText("Red Badge").parentElement).toHaveClass("bg-faintRed");

    rerender(<Badge color="blue" text="Blue Badge" />);
    expect(screen.getByText("Blue Badge").parentElement).toHaveClass("bg-faintBlue");
  });

  it("renders a dot when hasDot is true", () => {
    render(<Badge color="green" text="With Dot" hasDot />);
    const dot = screen.getByText("With Dot").previousSibling;
    expect(dot).toBeInTheDocument();
    expect(dot).toHaveClass("bg-customGreen");
  });

  it("does not render a dot when hasDot is false", () => {
    render(<Badge color="yellow" text="No Dot" hasDot={false} />);
    const badgeContainer = screen.getByText("No Dot").parentElement;
    expect(badgeContainer?.querySelector("div.rounded-full")).not.toBeInTheDocument();
  });

  it("applies the correct text color", () => {
    const { rerender } = render(<Badge color="gray" text="Gray Badge" />);
    expect(screen.getByText("Gray Badge")).toHaveClass("text-darkGray");

    rerender(<Badge color="yellow" text="Yellow Badge" />);
    expect(screen.getByText("Yellow Badge")).toHaveClass("text-goldenBrown");
  });
});
