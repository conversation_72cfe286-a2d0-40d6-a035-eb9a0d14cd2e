import { render } from "@testing-library/react";
import { vi } from "vitest";
import useDialogModalContext from "../../../components/DialogModal/useDialogModalContext";

// Mock component to test the hook usage inside context
const MockComponent = () => {
  const context = useDialogModalContext();
  return <div>{context ? "Context Found" : "No Context"}</div>;
};

describe("useDialogModalContext", () => {
  it("should throw an error when used outside the DialogModalContext provider", () => {
    const consoleErrorMock = vi.spyOn(console, "error").mockImplementation(() => null);

    expect(() => {
      render(<MockComponent />);
    }).toThrowError(
      "useDialogModalContext can only be used inside a DialogModalContext Provider or DialogModal child component",
    );

    consoleErrorMock.mockRestore();
  });
});
