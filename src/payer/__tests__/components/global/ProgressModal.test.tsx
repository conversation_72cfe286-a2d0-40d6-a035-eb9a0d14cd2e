import { act, render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { vi } from "vitest";
import ProgressModal from "../../../components/ui/modal/ProgressModal";

global.ResizeObserver = class ResizeObserver {
  observe = () => null;
  unobserve = () => null;
  disconnect = () => null;
};

describe("ProgressModal", () => {
  it("renders when isProgressModalOpen is true", async () => {
    await act(async () => {
      // Wrapping the render call with act
      render(<ProgressModal isProgressModalOpen={true} onClose={() => null} />);
    });
    expect(screen.getByRole("dialog")).toBeInTheDocument();
  });

  it("does not render when isProgressModalOpen is false", async () => {
    await act(async () => {
      // Wrapping with act here as well
      render(<ProgressModal isProgressModalOpen={false} onClose={() => null} />);
    });
    expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
  });

  it("displays title and description if provided", async () => {
    await act(async () => {
      // Wrapping with act
      render(
        <ProgressModal
          isProgressModalOpen={true}
          onClose={() => null}
          title="Loading Data"
          description="Please wait while we fetch the data."
        />,
      );
    });

    expect(screen.getByText("Loading Data")).toBeInTheDocument();
    expect(screen.getByText("Please wait while we fetch the data.")).toBeInTheDocument();
  });

  it("calls onClose when closed", async () => {
    const onClose = vi.fn();
    render(<ProgressModal isProgressModalOpen={true} onClose={onClose} />);

    screen.getByRole("dialog");
    await userEvent.keyboard("{Escape}"); // Assuming the dialog closes on Escape key

    expect(onClose).toHaveBeenCalled();
  });
});
