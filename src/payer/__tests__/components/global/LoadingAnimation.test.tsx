import { render } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import LoadingAnimation from "../../../components/animations/LoadingAnimation/LoadingAnimation";
import styles from "../../../components/animations/LoadingAnimation/LoadingAnimation.module.css";

describe("LoadingAnimation Component", () => {
  it("renders with the default size", () => {
    const { container } = render(<LoadingAnimation />);
    const svgElement = container.querySelector("svg");
    expect(svgElement).toBeInTheDocument();
    expect(svgElement).toHaveAttribute("width", "200");
    expect(svgElement).toHaveAttribute("height", "200");
  });

  it("renders with a custom size", () => {
    const customSize = 150;
    const { container } = render(<LoadingAnimation size={customSize} />);
    const svgElement = container.querySelector("svg");
    expect(svgElement).toHaveAttribute("width", customSize.toString());
    expect(svgElement).toHaveAttribute("height", customSize.toString());
  });

  it("applies the correct class name", () => {
    const { container } = render(<LoadingAnimation />);
    const svgElement = container.querySelector("svg");

    // Assert that the class exists in the styles object
    const loadingAnimationClass = styles["loading-animation"];
    expect(loadingAnimationClass).toBeDefined();

    // Assert that the SVG element has the correct class
    expect(svgElement).toHaveClass(loadingAnimationClass as string);
  });
});
