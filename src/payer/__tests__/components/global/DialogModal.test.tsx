import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { useState } from "react";
import { vi } from "vitest";
import DialogModal, { useDialogModalContext } from "../../../components/DialogModal";

function CloseButtonTester() {
  const { closeModal } = useDialogModalContext();
  return <button onClick={closeModal}>Close modal</button>;
}

describe("DialogModal", () => {
  beforeAll(() => {
    global.ResizeObserver = class {
      observe = () => null;
      unobserve = () => null;
      disconnect = () => null;
    };
  });

  it("renders modal content when isOpen is true", () => {
    const { rerender } = render(
      <DialogModal isOpen={true} setIsOpen={vi.fn()}>
        <p>Test Content</p>
      </DialogModal>,
    );

    expect(screen.getByText("Test Content")).toBeInTheDocument();

    rerender(
      <DialogModal isOpen={false} setIsOpen={vi.fn()}>
        <p>Test Content</p>
      </DialogModal>,
    );

    expect(screen.queryByText("Test Content")).not.toBeInTheDocument();
  });

  it("closes modal when closeModal function is called", async () => {
    const TestWrapper = () => {
      const [isOpen, setIsOpen] = useState(true);

      return (
        <DialogModal isOpen={isOpen} setIsOpen={setIsOpen}>
          <p>Test Content</p>
          <CloseButtonTester />
        </DialogModal>
      );
    };

    render(<TestWrapper />);

    const closeButtonElement = screen.getByRole("button", { name: /Close modal/i });
    const user = userEvent.setup();

    await user.click(closeButtonElement);

    expect(screen.queryByText("Test Content")).not.toBeInTheDocument();
  });
});
