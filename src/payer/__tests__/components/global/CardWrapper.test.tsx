import { render, screen } from "@testing-library/react";
import Card<PERSON>rapper from "../../../components/ui/CardWrapper";

describe("CardWrapper component", () => {
  it("renders children correctly", () => {
    render(
      <CardWrapper>
        <p>Test Content</p>
      </CardWrapper>,
    );
    expect(screen.getByText("Test Content")).toBeInTheDocument();
  });

  it("applies custom className correctly", () => {
    const { container } = render(
      <CardWrapper className="custom-class">
        <p>Styled Content</p>
      </CardWrapper>,
    );
    expect(container.firstChild).toHaveClass("custom-class");
  });

  it("has default styling classes", () => {
    const { container } = render(
      <CardWrapper>
        <p>Default Styling</p>
      </CardWrapper>,
    );
    expect(container.firstChild).toHaveClass(
      "rounded-md",
      "border",
      "border-gray-200",
      "px-6",
      "py-4",
      "shadow-md",
    );
  });
});
