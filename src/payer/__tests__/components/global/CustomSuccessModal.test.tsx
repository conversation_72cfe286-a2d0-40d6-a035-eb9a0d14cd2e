import { render, screen, fireEvent } from "@testing-library/react";
import SuccessModal from "../../../components/CustomSuccessModal/CustomSuccessModal";

describe("SuccessModal", () => {
  test("renders correctly when open", () => {
    const title = "Success";
    const description = "Your action was successful.";

    render(<SuccessModal isOpen={true} title={title} description={description} />);

    // Check if title and description are rendered
    expect(screen.getByText(title)).toBeInTheDocument();
    expect(screen.getByText(description)).toBeInTheDocument();
  });

  test("does not render when closed", () => {
    const { queryByText } = render(
      <SuccessModal isOpen={false} title="Success" description="Your action was successful." />,
    );

    // Modal content should not be present when isOpen is false
    expect(queryByText("Success")).not.toBeInTheDocument();
  });

  test("closes the modal when the close button is clicked", () => {
    const setIsOpen = vi.fn();

    render(
      <SuccessModal
        isOpen={true}
        setIsOpen={setIsOpen}
        title="Success"
        description="Your action was successful."
        isShowCloseButton={true}
      />,
    );

    // Check if close button exists and is clickable
    const closeButton = screen.getByRole("button");
    fireEvent.click(closeButton);

    // Ensure the setIsOpen callback was called with false
    expect(setIsOpen).toHaveBeenCalledWith(false);
  });
});
