import { render, screen, fireEvent } from "@testing-library/react";
import { vi } from "vitest";
import ButtonGroup, { ButtonGroupOption } from "../../../components/ui/ButtonGroup";

describe("ButtonGroup component", () => {
  const options: ButtonGroupOption<string>[] = [
    { title: "Option 1" },
    { title: "Option 2" },
    { title: "Option 3" },
  ];

  it("renders all options correctly", () => {
    render(<ButtonGroup options={options} activeOption="Option 1" setActiveOption={vi.fn()} />);
    options.forEach((option) => {
      expect(screen.getByText(option.title)).toBeInTheDocument();
    });
  });

  it("highlights the active option", () => {
    render(<ButtonGroup options={options} activeOption="Option 2" setActiveOption={vi.fn()} />);
    const activeButton = screen.getByText("Option 2").parentElement;
    expect(activeButton).toHaveClass("bg-faintBlue", "text-txtBlue");
  });

  it("calls setActiveOption when an option is clicked", () => {
    const setActiveOption = vi.fn();
    render(
      <ButtonGroup options={options} activeOption="Option 1" setActiveOption={setActiveOption} />,
    );

    fireEvent.click(screen.getByText("Option 3"));
    expect(setActiveOption).toHaveBeenCalledWith("Option 3");
  });
});
