import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { vi } from "vitest";
import Calendar from "../../../components/ui/input/Calender";

describe("Calendar Component", () => {
  it("renders correctly with placeholder", () => {
    render(<Calendar placeholder="Select a date" value="" onChange={vi.fn()} />);

    // Check if the input with placeholder is rendered
    expect(screen.getByPlaceholderText("Select a date")).toBeInTheDocument();
  });

  it("renders XMark icon if a value is passed", () => {
    render(<Calendar placeholder="Select a date" value="3-3-2025" onChange={vi.fn()} />);

    const xMarkIconElement = screen.getByTestId("x-mark-icon");
    expect(xMarkIconElement).toBeInTheDocument();
  });

  it("renders Calendar icon if no value is passed", () => {
    render(<Calendar placeholder="Select a date" value="" onChange={vi.fn()} />);

    const calendarIconElement = screen.getByTestId("calender-icon");

    expect(calendarIconElement).toBeInTheDocument();
  });

  it("clears the value when the XMark icon is clicked", async () => {
    const onChangeMock = vi.fn();
    render(<Calendar placeholder="Select a date" value="3-3-2025" onChange={onChangeMock} />);

    const xMarkIconElement = screen.getByTestId("x-mark-icon");
    await userEvent.click(xMarkIconElement);

    expect(onChangeMock).toHaveBeenCalledWith("");
  });
});
