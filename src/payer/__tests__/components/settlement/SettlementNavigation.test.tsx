import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { MemoryRouter, Route, Routes, useLocation } from "react-router-dom";
import SettlementNavigation from "../../../pages/finance-and-accounting-settlement/SettlementNavigation";

// Custom hook to get the current path
const CurrentPath = () => {
  const location = useLocation();
  return <span data-testid="current-path">{location.pathname}</span>;
};

describe("SettlementNavigation", () => {
  it("renders navigation links", () => {
    render(
      <MemoryRouter>
        <SettlementNavigation />
      </MemoryRouter>,
    );

    expect(screen.getByText("Unpaid")).toBeInTheDocument();
    expect(screen.getByText("Paying")).toBeInTheDocument();
    expect(screen.getByText("Paid")).toBeInTheDocument();
  });

  it("navigates correctly and updates the current path", async () => {
    render(
      <MemoryRouter initialEntries={["/finance-and-accounting/settlement/"]}>
        <Routes>
          <Route
            path="*"
            element={
              <>
                <SettlementNavigation />
                <CurrentPath />
              </>
            }
          />
        </Routes>
      </MemoryRouter>,
    );

    const unpaidLink = screen.getByText("Unpaid");
    const payingLink = screen.getByText("Paying");
    const paidLink = screen.getByText("Paid");

    const user = userEvent.setup();

    // Check the initial path
    expect(screen.getByTestId("current-path").textContent).toBe(
      "/finance-and-accounting/settlement/",
    );

    // Click 'Paying' and check the current path
    await user.click(payingLink);
    expect(screen.getByTestId("current-path").textContent).toBe(
      "/finance-and-accounting/settlement/paying",
    );

    // Click 'Paid' and check the current path
    await user.click(paidLink);
    expect(screen.getByTestId("current-path").textContent).toBe(
      "/finance-and-accounting/settlement/paid",
    );

    // Click 'Unpaid' and check the current path
    await user.click(unpaidLink);
    expect(screen.getByTestId("current-path").textContent).toBe(
      "/finance-and-accounting/settlement/",
    );
  });
});
