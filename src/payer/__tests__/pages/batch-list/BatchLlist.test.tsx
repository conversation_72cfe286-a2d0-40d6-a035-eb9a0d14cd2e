import { render, screen } from "@testing-library/react";
import BatchList from "../../../pages/claims/claim-vetting/batch-list/BatchList";

import { KeycloakInstance } from "keycloak-js";
import { ReactNode } from "react";
import { Provider } from "react-redux";
import { MemoryRouter } from "react-router-dom";
import UserService from "../../../services/UserService";
import { store } from "../../../store";

function TestWrapper({ children }: { children: ReactNode }) {
  return (
    <Provider store={store}>
      <MemoryRouter>{children}</MemoryRouter>
    </Provider>
  );
}

describe("BatchList", () => {
  beforeEach(() => {
    // Mocking the UserService.kcObject to return a mock KeycloakInstance
    const mockKeycloakInstance: Partial<KeycloakInstance> = {
      tokenParsed: { payerId: "12345" },
      init: vi.fn(),
      login: vi.fn(),
      logout: vi.fn(),
      register: vi.fn(),
    };

    vi.spyOn(UserService, "kcObject", "get").mockReturnValue(
      mockKeycloakInstance as KeycloakInstance,
    );
  });

  it("renders BatchList page component", () => {
    render(
      <TestWrapper>
        <BatchList />
      </TestWrapper>,
    );
    const headingElement = screen.getByRole("heading", { name: "Batch List" });
    expect(headingElement).toBeInTheDocument();
  });

  it("renders button group", () => {
    render(
      <TestWrapper>
        <BatchList />
      </TestWrapper>,
    );
    const unVettedButton = screen.getByText(/UnVetted/i);
    expect(unVettedButton).toBeInTheDocument();
    const inProgressButton = screen.getByText(/InProgress/i);
    expect(inProgressButton).toBeInTheDocument();
    const completedButton = screen.getByText(/Completed/i);
    expect(completedButton).toBeInTheDocument();
  });
});
