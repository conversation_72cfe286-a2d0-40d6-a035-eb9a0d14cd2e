import { render, screen } from "@testing-library/react";
import type { KeycloakInstance } from "keycloak-js";
import { Provider } from "react-redux";
import { MemoryRouter } from "react-router-dom";
import Paid from "../../../pages/finance-and-accounting-settlement/Paid/Paid";
import UserService from "../../../services/UserService";
import { store } from "../../../store";

describe("Paid", () => {
  beforeEach(() => {
    // Mocking the UserService.kcObject to return a mock KeycloakInstance
    const mockKeycloakInstance: Partial<KeycloakInstance> = {
      tokenParsed: { payerId: "12345" },
      init: vi.fn(),
      login: vi.fn(),
      logout: vi.fn(),
      register: vi.fn(),
    };

    vi.spyOn(UserService, "kcObject", "get").mockReturnValue(
      mockKeycloakInstance as KeycloakInstance,
    );
  });

  it("should display title", () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <Paid />
        </MemoryRouter>
      </Provider>,
    );

    expect(screen.getByText(/Payment List/i)).toBeInTheDocument();
  });

  it("should not display an action button ", () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <Paid />
        </MemoryRouter>
      </Provider>,
    );

    const actionButton = screen.queryByRole("button", { name: /Actions/i });
    expect(actionButton).not.toBeInTheDocument();
  });

  it("should display reference number search input ", () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <Paid />
        </MemoryRouter>
      </Provider>,
    );

    const searchInput = screen.getByRole("textbox");
    expect(searchInput).toHaveAttribute("placeholder", "Search by reference number");
  });
});
