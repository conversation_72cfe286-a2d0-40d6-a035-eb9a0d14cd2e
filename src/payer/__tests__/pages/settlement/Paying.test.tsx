import { it, expect, describe, vi } from "vitest";
import { render, screen } from "@testing-library/react";
import Paying from "../../../pages/finance-and-accounting-settlement/Paying/Paying";
import UserService from "../../../services/UserService";
import { MemoryRouter } from "react-router-dom";
import { Provider } from "react-redux";
import { store } from "../../../store";
import type { KeycloakInstance } from "keycloak-js";

describe("Paying", () => {
  beforeEach(() => {
    // Mocking the UserService.kcObject to return a mock KeycloakInstance
    const mockKeycloakInstance: Partial<KeycloakInstance> = {
      tokenParsed: { payerId: "12345" },
      init: vi.fn(),
      login: vi.fn(),
      logout: vi.fn(),
      register: vi.fn(),
    };

    vi.spyOn(UserService, "kcObject", "get").mockReturnValue(
      mockKeycloakInstance as KeycloakInstance,
    );
  });

  it("should display title", () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <Paying />
        </MemoryRouter>
      </Provider>,
    );

    expect(screen.getByText(/Payment List/i)).toBeInTheDocument();
  });

  it("should display an action button ", () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <Paying />
        </MemoryRouter>
      </Provider>,
    );

    const actionButton = screen.getByRole("button");
    expect(actionButton).toHaveTextContent(/Actions/i);
  });

  it("should display reference number search input ", () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <Paying />
        </MemoryRouter>
      </Provider>,
    );

    const searchInput = screen.getByRole("textbox");
    expect(searchInput).toHaveAttribute("placeholder", "Search by reference number");
  });
});
