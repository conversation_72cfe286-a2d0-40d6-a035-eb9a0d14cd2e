import { render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import { store } from "../../../store";
import Vouchering from "../../../pages/vouchering/Vouchering";

const renderComponent = () => {
  return render(
    <Provider store={store}>
      <Vouchering />
    </Provider>,
  );
};

describe("Vouchering", () => {
  it(`renders the default view with "No claims" message when no vouchering filter is selected`, () => {
    renderComponent();
    const noClaimsMessage = screen.getByText(/No claims/i);
    expect(noClaimsMessage).toBeInTheDocument();
  });
});
