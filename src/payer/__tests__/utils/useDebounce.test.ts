import { renderHook, act } from "@testing-library/react";
import useDebounce from "../../utils/useDebounce";

describe("useDebounce", () => {
  vi.useFakeTimers();

  it("should return the initial value immediately", () => {
    const { result } = renderHook(() => useDebounce("initial", 500));
    expect(result.current).toBe("initial");
  });

  it("should update the debounced value after the specified delay", () => {
    const { result, rerender } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: "initial", delay: 500 },
    });

    // Change the value
    rerender({ value: "updated", delay: 500 });

    // The debounced value should still be the initial value before the delay
    expect(result.current).toBe("initial");

    // Fast-forward time by the delay
    act(() => {
      vi.advanceTimersByTime(500);
    });

    // The debounced value should now be updated
    expect(result.current).toBe("updated");
  });

  it("should reset the timer if the value changes before the delay", () => {
    const { result, rerender } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: "initial", delay: 500 },
    });

    // Change the value
    rerender({ value: "updated1", delay: 500 });

    // Fast-forward time by less than the delay
    act(() => {
      vi.advanceTimersByTime(300);
    });

    // The debounced value should still be the initial value
    expect(result.current).toBe("initial");

    // Change the value again before the delay completes
    rerender({ value: "updated2", delay: 500 });

    // Fast-forward time by the full delay
    act(() => {
      vi.advanceTimersByTime(500);
    });

    // The debounced value should now reflect the latest update
    expect(result.current).toBe("updated2");
  });

  it("should use a default delay of 500ms if none is provided", () => {
    const { result, rerender } = renderHook(({ value }) => useDebounce(value), {
      initialProps: { value: "initial" },
    });

    // Change the value
    rerender({ value: "updated" });

    // The debounced value should still be the initial value before the default delay
    expect(result.current).toBe("initial");

    // Fast-forward time by the default delay
    act(() => {
      vi.advanceTimersByTime(500);
    });

    // The debounced value should now be updated
    expect(result.current).toBe("updated");
  });

  afterEach(() => {
    vi.clearAllTimers();
  });
});
