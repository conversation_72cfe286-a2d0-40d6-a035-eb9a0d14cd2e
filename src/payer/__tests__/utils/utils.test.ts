import { describe, it, expect } from "vitest";
import {
  formatDateVerbose,
  formatDateWithSuffix,
  formatLogDateToMonthYear,
  getDifferenceInDays,
  isValidBatchingCriteria,
  isValidFilterName,
  isValidId,
} from "../../utils/utils";
import { BatchingCriteria } from "../../components/claimsBatching/BatchingCriteriaSelector";

describe("getDifferenceInDays", () => {
  it("calculates difference between two valid dates", () => {
    expect(getDifferenceInDays("01.01.2023", "03.01.2023")).toBe(2);
  });

  it("throws an error for invalid date formats", () => {
    expect(() => getDifferenceInDays("01-01-2023", "03.01.2023")).toThrow(
      "Invalid date format. Please use dd.mm.yyyy.",
    );
  });

  it("returns null if any part of the date is not a number", () => {
    expect(() => getDifferenceInDays("01.aa.2023", "03.01.2023")).toThrow(
      "Invalid date format. Please use dd.mm.yyyy.",
    );
  });
});

describe("formatLogDateToMonthYear", () => {
  it("formats a date string to 'Month, Year' format", () => {
    expect(formatLogDateToMonthYear("01.01.2023")).toBe("January, 2023");
  });
});

describe("formatDateWithSuffix", () => {
  it("formats a date with day suffix", () => {
    const date = new Date(2023, 0, 1); // January 1, 2023
    expect(formatDateWithSuffix(date)).toBe("1st January, 2023");
  });

  it("handles special suffix cases like 11th, 12th, and 13th", () => {
    const date11 = new Date(2023, 0, 11); // January 11, 2023
    expect(formatDateWithSuffix(date11)).toBe("11th January, 2023");

    const date12 = new Date(2023, 0, 12); // January 12, 2023
    expect(formatDateWithSuffix(date12)).toBe("12th January, 2023");

    const date13 = new Date(2023, 0, 13); // January 13, 2023
    expect(formatDateWithSuffix(date13)).toBe("13th January, 2023");
  });

  it("handles suffixes for 2nd, 3rd, and default cases", () => {
    const date2 = new Date(2023, 0, 2); // January 2, 2023
    expect(formatDateWithSuffix(date2)).toBe("2nd January, 2023");

    const date3 = new Date(2023, 0, 3); // January 3, 2023
    expect(formatDateWithSuffix(date3)).toBe("3rd January, 2023");

    const date4 = new Date(2023, 0, 4); // January 4, 2023
    expect(formatDateWithSuffix(date4)).toBe("4th January, 2023");
  });
});

describe("formatDateVerbose", () => {
  it("formats a date to a verbose string", () => {
    const date = new Date(2023, 0, 1); // January 1, 2023
    expect(formatDateVerbose(date)).toBe("01 January, 2023");
  });
});

describe("isValidId", () => {
  it("returns true for valid numeric strings and numbers", () => {
    expect(isValidId("123")).toBe(true);
    expect(isValidId(123)).toBe(true);
  });

  it("returns false for invalid strings", () => {
    expect(isValidId("select")).toBe(false);
    expect(isValidId("0")).toBe(false);
  });

  it("returns false for empty strings and zero", () => {
    expect(isValidId(0)).toBe(false);
    expect(isValidId("")).toBe(false);
  });
});

describe("isValidFilterName", () => {
  it("returns true for valid filter names", () => {
    expect(isValidFilterName("filter")).toBe(true);
  });

  it("returns false for invalid filter names", () => {
    expect(isValidFilterName("select")).toBe(false);
    expect(isValidFilterName("")).toBe(false);
  });
});

describe("isValidBatchingCriteria", () => {
  it("returns true for valid criteria", () => {
    expect(isValidBatchingCriteria(BatchingCriteria.AGING)).toBe(true);
  });

  it("returns false for invalid criteria", () => {
    expect(isValidBatchingCriteria("invalid" as unknown)).toBe(false);
    expect(isValidBatchingCriteria(123 as unknown)).toBe(false);
  });
});
