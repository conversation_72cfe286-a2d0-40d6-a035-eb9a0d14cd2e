import { describe, it, expect } from "vitest";
import { formatNumberToKes } from "../../utils/formatCurrency";

describe("formatNumberToKes", () => {
  it("should format a positive number correctly", () => {
    const result = formatNumberToKes(1000);
    expect(result).toBe("KES 1,000.00");
  });

  it("should format zero correctly", () => {
    const result = formatNumberToKes(0);
    expect(result).toBe("KES 0.00");
  });

  it("should format a negative number correctly", () => {
    const result = formatNumberToKes(-500);
    expect(result).toBe("-KES 500.00");
  });

  it("should handle non-integer numbers", () => {
    const result = formatNumberToKes(1234.56);
    expect(result).toBe("KES 1,234.56");
  });
});
