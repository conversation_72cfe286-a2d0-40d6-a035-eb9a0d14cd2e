import { convertDateString } from "../../utils/convertDateString";

describe("convertDateString", () => {
  it("should correctly convert a valid dd/mm/yyyy date string to the same format", () => {
    const dateString = "22/01/2025";
    const result = convertDateString(dateString);
    expect(result).toBe("22/01/2025");
  });

  it("should correctly convert a valid ISO date string to dd/mm/yyyy format", () => {
    const dateString = "2025-01-22T00:00:00Z";
    const result = convertDateString(dateString);
    expect(result).toBe("22/01/2025");
  });

  it("should allow specifying a custom separator", () => {
    const dateString = "22/01/2025";
    const result = convertDateString(dateString, "-");
    expect(result).toBe("22-01-2025");
  });

  it("should throw an error for an invalid date string", () => {
    const dateString = "invalid-date";
    expect(() => convertDateString(dateString)).toThrowError("Invalid date string provided");
  });

  it("should handle a date string with a single digit day or month", () => {
    const dateString = "5/9/2025"; // September 5, 2025
    const result = convertDateString(dateString);
    expect(result).toBe("05/09/2025");
  });
});
