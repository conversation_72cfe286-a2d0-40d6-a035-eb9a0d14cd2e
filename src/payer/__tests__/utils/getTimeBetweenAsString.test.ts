import { describe, it, expect, vi } from "vitest";
import getTimeBetweenAsString from "../../utils/getTimeBetweenAsString";

describe("getTimeBetweenAsString", () => {
  beforeAll(() => {
    // Mock current date to a fixed point in time
    const currentDate = new Date("2025-01-22T00:00:00Z");
    vi.useFakeTimers().setSystemTime(currentDate);
  });

  afterAll(() => {
    vi.useRealTimers(); // Restore the real timers after all tests
  });

  it('should return "X years ago" for a date more than a year ago', () => {
    const dateTimeString = "2023-01-01T00:00:00Z";
    const result = getTimeBetweenAsString(dateTimeString);
    expect(result).toBe("2 years ago");
  });

  it('should return "X months ago" for a date more than a month ago but less than a year ago', () => {
    const dateTimeString = "2024-12-01T00:00:00Z";
    const result = getTimeBetweenAsString(dateTimeString);
    expect(result).toBe("1 month ago");
  });

  it('should return "X weeks ago" for a date more than a day ago but less than a week ago', () => {
    const dateTimeString = "2025-01-15T00:00:00Z";
    const result = getTimeBetweenAsString(dateTimeString);
    expect(result).toBe("1 week ago");
  });

  it('should return "X hours ago" for a date more than an hour ago but less than a day ago', () => {
    const dateTimeString = new Date(
      new Date("2025-01-22T00:00:00Z").getTime() - 5 * 60 * 60 * 1000,
    ).toISOString(); // 5 hours ago
    const result = getTimeBetweenAsString(dateTimeString);
    expect(result).toBe("5 hrs ago");
  });

  it('should return "X minutes ago" for a date more than a minute ago but less than an hour ago', () => {
    const dateTimeString = new Date(
      new Date("2025-01-22T00:00:00Z").getTime() - 10 * 60 * 1000,
    ).toISOString(); // 10 minutes ago
    const result = getTimeBetweenAsString(dateTimeString);
    expect(result).toBe("10 mins ago");
  });

  it('should return "X seconds ago" for a date more than a second ago but less than a minute ago', () => {
    const dateTimeString = new Date(
      new Date("2025-01-22T00:00:00Z").getTime() - 15 * 1000,
    ).toISOString(); // 15 seconds ago
    const result = getTimeBetweenAsString(dateTimeString);
    expect(result).toBe("15 seconds ago");
  });

  it("should throw an error for an invalid dateTime string", () => {
    const dateTimeString = "invalid-date";
    expect(() => getTimeBetweenAsString(dateTimeString)).toThrowError(
      "Invalid dateTime string provided",
    );
  });

  it("should return the correct format when the difference is exactly 1 unit (e.g., 1 minute ago)", () => {
    const dateTimeString = new Date(
      new Date("2025-01-22T00:00:00Z").getTime() - 60 * 1000,
    ).toISOString(); // Exactly 1 minute ago
    const result = getTimeBetweenAsString(dateTimeString);
    expect(result).toBe("1 min ago");
  });
});
