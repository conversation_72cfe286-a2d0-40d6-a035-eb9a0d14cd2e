import { describe, it, expect } from "vitest";
import { createSearchParamsString } from "../../utils/createSearchParamsString";

describe("createSearchParamsString", () => {
  it("should convert an object with single string and number values to a query string", () => {
    const params = { search: "test", page: 2 };
    const result = createSearchParamsString(params);
    expect(result).toBe("search=test&page=2");
  });

  it("should handle arrays of strings or numbers as values", () => {
    const params = { filter: ["active", "pending"], ids: [1, 2, 3] };
    const result = createSearchParamsString(params);
    expect(result).toBe("filter=active&filter=pending&ids=1&ids=2&ids=3");
  });

  it("should ignore keys with undefined or empty string values", () => {
    const params = { search: "test", page: undefined, category: "" };
    const result = createSearchParamsString(params);
    expect(result).toBe("search=test");
  });

  it("should handle boolean values correctly", () => {
    const params = { showAll: true, featured: false };
    const result = createSearchParamsString(params);
    expect(result).toBe("showAll=true&featured=false");
  });

  it("should return an empty string if all values are undefined or empty", () => {
    const params = { search: undefined, filter: "" };
    const result = createSearchParamsString(params);
    expect(result).toBe("");
  });

  it("should append each element of the array with the same key", () => {
    const params = { filter: ["active", "pending"] };
    const result = createSearchParamsString(params);
    expect(result).toBe("filter=active&filter=pending");
  });
});
