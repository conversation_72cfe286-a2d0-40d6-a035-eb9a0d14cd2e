import { describe, it, vi, expect } from "vitest";
import { downloadFile } from "../../utils/downloadFile";

describe("downloadFile", () => {
  it("should create an anchor element, set the correct attributes, and trigger a download", () => {
    const mockUrl = "https://example.com/file.txt";
    const mockFilename = "file.txt";

    // Mock document.createElement
    const appendChildMock = vi.fn();
    const removeChildMock = vi.fn();
    const clickMock = vi.fn();
    const mockAnchor = {
      href: "",
      download: "",
      click: clickMock,
    };
    vi.spyOn(document, "createElement").mockImplementation(
      () => mockAnchor as unknown as HTMLAnchorElement,
    );
    vi.spyOn(document.body, "appendChild").mockImplementation(appendChildMock);
    vi.spyOn(document.body, "removeChild").mockImplementation(removeChildMock);

    downloadFile(mockUrl, mockFilename);

    expect(document.createElement).toHaveBeenCalledWith("a");
    expect(mockAnchor.href).toBe(mockUrl);
    expect(mockAnchor.download).toBe(mockFilename);
    expect(appendChildMock).toHaveBeenCalledWith(mockAnchor);
    expect(clickMock).toHaveBeenCalled();
    expect(removeChildMock).toHaveBeenCalledWith(mockAnchor);

    // Restore mocks
    vi.restoreAllMocks();
  });
});
