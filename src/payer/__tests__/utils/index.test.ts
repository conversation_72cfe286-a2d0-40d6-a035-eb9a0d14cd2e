import { describe, it, expect } from "vitest";
import clsx, { capitalize, dateFlatPickrOptions, toastOptions, unCamelCase } from "../../utils";

describe("clsx", () => {
  it("should concatenate string arguments", () => {
    expect(clsx("foo", "bar")).toBe("foo bar");
  });

  it("should filter out boolean values", () => {
    expect(clsx("foo", true, "bar", false)).toBe("foo bar");
  });

  it("should handle empty arguments gracefully", () => {
    expect(clsx()).toBe("");
  });

  it("should only join string values", () => {
    expect(clsx("foo", 123 as unknown as string, "bar")).toBe("foo bar");
  });
});

describe("capitalize", () => {
  it("should capitalize the first letter of a string", () => {
    expect(capitalize("hello")).toBe("Hello");
  });

  it("should handle empty strings gracefully", () => {
    expect(capitalize("")).toBe("");
  });

  it("should not modify strings that start with uppercase letters", () => {
    expect(capitalize("Hello")).toBe("Hello");
  });
});

describe("unCamelCase", () => {
  it("should insert spaces between camelCase words", () => {
    expect(unCamelCase("camelCase")).toBe("Camel Case");
  });

  it("should handle PascalCase words", () => {
    expect(unCamelCase("PascalCase")).toBe("Pascal Case");
  });

  it("should handle acronyms and camelCase together", () => {
    expect(unCamelCase("XMLHttpRequest")).toBe("XML Http Request");
  });

  it("should capitalize the first letter of the resulting string", () => {
    expect(unCamelCase("exampleString")).toBe("Example String");
  });
});

describe("Constants", () => {
  it("should match the snapshot for toastOptions", () => {
    expect(toastOptions).toMatchSnapshot();
  });

  it("should match the snapshot for dateFlatPickrOptions", () => {
    expect(dateFlatPickrOptions).toMatchSnapshot();
  });
});
