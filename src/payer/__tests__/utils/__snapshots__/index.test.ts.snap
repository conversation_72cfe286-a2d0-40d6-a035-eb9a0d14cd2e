// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Constants > should match the snapshot for dateFlatPickrOptions 1`] = `
{
  "dateFormat": "Y-m-d",
  "enableTime": false,
  "maxDate": "today",
  "mode": "single",
  "monthSelectorType": "static",
  "nextArrow": "<svg class="fill-current" width="7" height="11" viewBox="0 0 7 11"><path d="M1.4 10.8L0 9.4l4-4-4-4L1.4 0l5.4 5.4z" /></svg>",
  "prevArrow": "<svg class="fill-current" width="7" height="11" viewBox="0 0 7 11"><path d="M5.4 10.8l1.4-1.4-4-4 4-4L5.4 0 0 5.4z" /></svg>",
  "static": true,
}
`;

exports[`Constants > should match the snapshot for toastOptions 1`] = `
{
  "autoClose": 5000,
  "closeOnClick": true,
  "draggable": true,
  "hideProgressBar": false,
  "pauseOnHover": true,
  "position": "top-right",
  "progress": undefined,
}
`;
