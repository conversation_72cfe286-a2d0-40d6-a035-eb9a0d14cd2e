import { describe, expect, it } from "vitest";
import checkIfObjectsAreEqual, { PlainObject } from "../../utils/checkIfObjectsAreEqual";

describe("checkIfObjectsAreEqual", () => {
  it("should return true for identical objects", () => {
    const obj1 = { a: 1, b: 2, c: 3 };
    const obj2 = { a: 1, b: 2, c: 3 };
    expect(checkIfObjectsAreEqual(obj1, obj2)).toBe(true);
  });

  it("should return false for objects with different keys", () => {
    const obj1 = { a: 1, b: 2 };
    const obj2 = { a: 1, c: 3 };
    expect(checkIfObjectsAreEqual(obj1, obj2)).toBe(false);
  });

  it("should return false for objects with the same keys but different values", () => {
    const obj1 = { a: 1, b: 2 };
    const obj2 = { a: 1, b: 3 };
    expect(checkIfObjectsAreEqual(obj1, obj2)).toBe(false);
  });

  it("should return false when one object is null", () => {
    const obj1 = { a: 1, b: 2 };
    const obj2 = null;
    expect(checkIfObjectsAreEqual(obj1, obj2)).toBe(false);
  });

  it("should return true for null objects when both are null", () => {
    const obj1 = null;
    const obj2 = null;
    expect(checkIfObjectsAreEqual(obj1, obj2)).toBe(true);
  });

  it("should return false when one of the objects is not an object", () => {
    const obj1 = { a: 1, b: 2 };
    const obj2 = "not an object";
    expect(checkIfObjectsAreEqual(obj1, obj2 as unknown as PlainObject)).toBe(false);
  });

  it("should return true for deeply nested identical objects", () => {
    const obj1 = { a: { b: { c: 3 } } };
    const obj2 = { a: { b: { c: 3 } } };
    expect(checkIfObjectsAreEqual(obj1, obj2)).toBe(true);
  });

  it("should return false for deeply nested objects with different values", () => {
    const obj1 = { a: { b: { c: 3 } } };
    const obj2 = { a: { b: { c: 4 } } };
    expect(checkIfObjectsAreEqual(obj1, obj2)).toBe(false);
  });
});
