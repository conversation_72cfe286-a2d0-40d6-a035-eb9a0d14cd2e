import { toast } from "react-toastify";
import { describe, expect, it, vi } from "vitest";
import { handleTryCatchError } from "../../utils/handleTryCatchError";

// Mock the `toast` object from `react-toastify`
vi.mock("react-toastify", () => ({
  toast: {
    error: vi.fn(),
  },
}));

describe("handleTryCatchError", () => {
  it("should call toast.error with the error message if error is an instance of Error", () => {
    const error = new Error("Test error message");

    handleTryCatchError(error);

    expect(toast.error).toHaveBeenCalledWith("Test error message");
    expect(toast.error).toHaveBeenCalledTimes(1);
  });

  it("should call toast.error with a default message if error is not an instance of Error", () => {
    const error = "Some random error";

    handleTryCatchError(error);

    expect(toast.error).toHaveBeenCalledWith("Error occurred");
  });

  it("should log the error to the console", () => {
    const consoleErrorSpy = vi.spyOn(console, "error").mockImplementation(() => null);
    const error = new Error("Test error message");

    handleTryCatchError(error);

    expect(consoleErrorSpy).toHaveBeenCalledWith("TryCatch Error: ", error);
    consoleErrorSpy.mockRestore();
  });
});
