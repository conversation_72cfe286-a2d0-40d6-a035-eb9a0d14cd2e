import * as actionTypes from "./actionTypes";

const initialState = {
  member: [],
  success: null,
  loading: false,
  memberSearchResult: [],
  categories: [],
  policyId: null,
  biometrics: [],
  topUpSuccess: false,
  transferSuccess: false,
  memberupdated: false,
  biometricsupdated: false,
  categoryupdated: false,
  memberstatusupdated: false,
  plans: [],

  benefitlist: [],
  memberSelected: {},
  reason: "",
  startVisitSuccess: null,
  error: "",
  startVisitMsg: "",
  closeVisitSuccess: null,

  closedVisits: [],
  activeVisits: [],

  closedVisitsTotalElements: 0,
  closedVisitsTotalPages: 0,
  closedVisitsTotalPageNumber: 0,
  hospitalProvider: 0,

  memberBenefitItem: {},
  activeBillItem: {},
  finalBillItem: {},
  historyItem: {},
  msg: null,
  icd10: [],
  lineItemsList: [],
  providerName: "",
  reimbursementProviderName: "",
  reimbursementInvoiceDate: null,
  offlctInvoiceDate: null,
  pendingPreAuths: [],
  allPreAuths: [],
  refreshKey: 0,
  preAuthItem: {},
  preAuthSubmitted: false,
  preAuthApproved: false,
  preAuthApprovedError: "",
  loadingClaimsToVet: false,

  claimsToVet: [],
  claim: {},
  preauth: {},
  vettingInvoicelineItems: [],
  vettingInvoices: [],
  vettingdocuments: [],
  loadingVetInvoice: false,
  vetStatusResponse: false,
  loadingInvoices:false,
  loadingDocumentsToVet:false,
  loadingClaimsToBatch:false,
  loadingbatchInvoices:false,
  batchResultStatus:{},
  claimsToBatch: [],
  loadingbatchedInvoices:false,
  batchedInvoices:[],
  downloadedDocurl:"",
  payerRegions:[]

};

export const memberSearchReducer = (state = initialState, action) => {
  switch (action.type) {
    case actionTypes.SET_LOADING:
      return {
        ...state,
        topUpSuccess: false,
        transferSuccess: false,
        memberupdated: false,
        biometricsupdated: false,
        categoryupdated: false,
        memberstatusupdated: false,
        preAuthSubmitted: false,
        preAuthApprovedError: "",
      };
    case actionTypes.APPROVE_PREAUTH:
      return {
        ...state,
        preAuthSubmitted: true,
      };
    case actionTypes.APPROVE_PREAUTH_ERROR:
      return {
        ...state,
        preAuthApproved: false,
        preAuthApprovedError: action.payload,
      };
    case actionTypes.DECLINE_PREAUTH:
      return {
        ...state,
        preAuthSubmitted: true,
      };
    case actionTypes.SET_ACTIVE_BILL:
      return {
        ...state,
        activeBillItem: action.payload,
        finalBillItem: "",
      };
    case actionTypes.UPDATE_ACTIVE_BILL:
      return {
        ...state,
        finalBillItem: action.payload,
      };
    case actionTypes.SELECTED_MEMBER:
      return {
        ...state,
        memberSelected: action.payload,
      };
    case actionTypes.SELECTED_REASON:
      return {
        ...state,
        reason: action.payload,
      };
    case actionTypes.SELECTED_PROVIDER:
      return {
        ...state,
        hospitalProvider: action.payload,
      };
    case actionTypes.INPUT_SELECTED_PROVIDER:
      return {
        ...state,
        reimbursementProviderName: action.payload,
      };
    case actionTypes.INPUT_INVOICE_DATE:
      return {
        ...state,
        reimbursementInvoiceDate: action.payload,
      };
    case actionTypes.INPUT_OFFLCT_INVOICE_DATE:
      return {
        ...state,
        offlctInvoiceDate: action.payload,
      };
    case actionTypes.SEARCH_MEMBER_BENEFITS:
      return {
        ...state,
        loading: false,
        benefitlist: action.payload,
      };
    case actionTypes.SEARCH_MEMBER:
      return {
        ...state,
        loading: false,
        member: action.payload,
      };
    case actionTypes.GET_PLANS:
      return {
        ...state,
        loading: false,
        plans: action.payload,
      };

    case actionTypes.CLEAR_MEMBER_RESULT:
      return {
        ...state,
        loading: true,
        member: [],
      };
    case actionTypes.SEARCH_MEMBERSHIP_DIRECT: {
      return {
        ...state,
        loading: false,
        memberSearchResult: action.payload,
      };
    }
    case actionTypes.CLEAR_MEMBER_SEARCH:
      return {
        ...state,
        loading: true,
        memberSearchResult: [],
      };
    case actionTypes.GET_CATEGORIES:
      return {
        ...state,
        categories: action.payload,
      };
    case actionTypes.GET_BIOMETRICS:
      return {
        ...state,
        biometrics: action.payload,
      };
    case actionTypes.TOPUP_BENEFIT:
      return {
        ...state,
        topUpSuccess: action.payload,
      };
    case actionTypes.TRANSFER_BENEFIT:
      return {
        ...state,
        transferSuccess: action.payload,
      };
    case actionTypes.UPDATE_MEMBER:
      return {
        ...state,
        memberupdated: action.payload,
      };
    case actionTypes.UPDATE_BIOMETRICS:
      return {
        ...state,
        biometricsupdated: action.payload,
      };
    case actionTypes.CHANGE_CATEGORY:
      return {
        ...state,
        categoryupdated: action.payload,
      };
    case actionTypes.ACTIVATE_MEMBER:
      return {
        ...state,
        memberstatusupdated: action.payload,
      };
    case actionTypes.DEACTIVATE_MEMBER:
      return {
        ...state,
        memberstatusupdated: action.payload,
      };
    case actionTypes.CLEAR_ACTIVE_USER_DATA:
      return {
        ...state,
        member: [],
        success: null,
        loading: false,
        memberSearchResult: [],
        categories: [],
        biometrics: [],
        topUpSuccess: false,
        transferSuccess: false,
        memberupdated: false,
        biometricsupdated: false,
        categoryupdated: false,
        memberstatusupdated: false,
      };
    case actionTypes.CLEAR_ACTIVE_LIST:
      return {
        ...state,
        loading: true,
        activeVisits: [],
      };
    case actionTypes.CLEAR_CLOSED_LIST:
      return {
        ...state,
        loading: true,
        closedVisits: [],
      };

    case actionTypes.START_OFFLCT_VISIT: {
      return {
        ...state,
        startVisitSuccess: action.payload.success,
        startVisitMsg: action.payload.msg,
      };
    }
    case actionTypes.START_OFFLCT_VISIT_ERROR: {
      return {
        ...state,
        startVisitMsg: action.payload,
        startVisitSuccess: false,
        closeVisitSuccess: false,
      };
    }
    case actionTypes.START_REIMBURSEMENT_VISIT: {
      return {
        ...state,
        startVisitSuccess: action.payload.success,
        startVisitMsg: action.payload.msg,
      };
    }
    case actionTypes.START_REIMBURSEMENT_VISIT_ERROR: {
      return {
        ...state,
        startVisitMsg: action.payload,
        startVisitSuccess: false,
        closeVisitSuccess: false,
      };
    }
    case actionTypes.BILL_AND_CLOSE_VISIT: {
      return {
        ...state,
        startVisitSuccess: null,
        closeVisitSuccess: action.payload,
      };
    }
    case actionTypes.BILL_AND_CLOSE_VISIT_ERROR: {
      return {
        ...state,
        startVisitSuccess: null,
        closeVisitSuccess: false,
      };
    }

    case actionTypes.GET_CLOSED_VISITS:
      return {
        ...state,
        loading: false,
        closedVisits: action.payload.content,
        closedVisitsTotalElements: action.payload.totalElements,
        closedVisitsTotalPages: action.payload.totalPages,
        closedVisitsTotalPageNumber: action.payload.number,
      };
    case actionTypes.GET_ACTIVE_VISITS:
      return {
        ...state,
        loading: false,
        activeVisits: action.payload,
        closeVisitSuccess: false,
      };

    case actionTypes.GET_PENDING_PREAUTHS:
      return {
        ...state,
        loading: false,
        pendingPreAuths: action.payload,
        closeVisitSuccess: false,
      };
    case actionTypes.GET_ALL_PREAUTHS:
      return {
        ...state,
        loading: false,
        allPreAuths: action.payload,
        closeVisitSuccess: false,
      };
    case actionTypes.SET_REFRESH_HISTORY:
      return {
        ...state,
        refreshKey: action.payload,
      };
    case actionTypes.SET_SELECTED_PREAUTH_ITEM:
      return {
        ...state,
        preAuthItem: action.payload,
      };
    case actionTypes.GET_CLAIMS_TO_VET:
      return {
        ...state,
        loadingClaimsToVet: false,
        claimsToVet: action.payload,
        closeVisitSuccess: false,
      };
    case actionTypes.SET_LOADING_CLAIMS_TO_VET:
      return {
        ...state,
        loadingClaimsToVet: true,
        claimsToVet: [],
      };
    case actionTypes.GET_CLAIM_BY_ID:
      return {
        ...state,
        loading: true,
        claim: action.payload,
      };
    case actionTypes.GET_PREAUTH_BY_ID:
      return {
        ...state,
        loading: true,
        preauth: action.payload,
      };
      case actionTypes.SET_LOADING_INVOICES_BY_VISIT:
        return {
          ...state,
          loadingInvoices: true,
          vettingInvoices: [],
        };
      case actionTypes.GET_INVOICES_BY_VISIT:
        return {
          ...state,
          loadingInvoices: false,
          vettingInvoices: action.payload,
        };
    case actionTypes.SET_LOADING_LINE_ITEMS_BY_VISIT:
      return {
        ...state,
        loadingData: true,
        vettingInvoicelineItems: [],
      };
    case actionTypes.GET_LINE_ITEMS_BY_VISIT:
      return {
        ...state,
        loadingData: false,
        vettingInvoicelineItems: action.payload,
      };
    case actionTypes.SET_LOADING_VET_INVOICE:
      return {
        ...state,
        loadingVetInvoice: true,
      };
    case actionTypes.VET_INVOICE:
      return {
        ...state,
        loadingVetInvoice: false,
        vetStatusResponse: action.payload,
      };
      case actionTypes.VET_INVOICE_FAIL:
      return {
        ...state,
        loadingVetInvoice: false,
        vetStatusResponse: action.payload,
      };
      case actionTypes.SET_LOADING_DOCUMENTS_TO_VET:
        return {
          ...state,
          loadingDocumentsToVet: true,
        };
      case actionTypes.GET_DOCUMENTS_TO_VET:
        return {
          ...state,
          loadingDocumentsToVet: false,
          documents: action.payload,
        };
      case actionTypes.SET_LOADING_CLAIMS_TO_BATCH:
        return {
          ...state,
          loadingClaimsToBatch: true,
          claimsToBatch:[]
        };
      case actionTypes.GET_CLAIMS_TO_BATCH:
        return {
          ...state,
          loadingClaimsToBatch: false,
          claimsToBatch: action.payload,
        };

      case actionTypes.SET_LOADING_BATCH_INVOICES:
      return {
        ...state,
        loadingbatchInvoices: true,
        batchResultStatus:{}
      };
    case actionTypes.BATCH_INVOICES:
      return {
        ...state,
        loadingbatchInvoices: false,
        batchResultStatus: action.payload,
      };
    case actionTypes.BATCH_INVOICES_FAIL:
      return {
        ...state,
        loadingbatchInvoices: false,
      };
      ////////
    case actionTypes.SET_LOADING_GET_BATCHED_INVOICES:
      return {
        ...state,
        loadingbatchedInvoices: true,
        batchedInvoices:[]
      };
    case actionTypes.GET_BATCHED_INVOICES:
      return {
        ...state,
        loadingbatchedInvoices: false,
        batchedInvoices: action.payload,
      };
    case actionTypes.GET_BATCHED_INVOICES_FAIL:
      return {
        ...state,
        loadingbatchedInvoices: false,
      };
    case actionTypes.SET_LOADING_DOWNLOAD_DOCUMENTS_TO_VET:
      return {
        ...state,
        downloadedDocurl:""
      };
    case actionTypes.DOWNLOAD_DOCUMENTS_TO_VET:
      return {
        ...state,
        downloadedDocurl:action.payload
      }
    case actionTypes.SET_LOADING_PROVIDER:
      return {
        ...state,
        providerBranches: [],
      };
    case actionTypes.GET_PROVIDER_BRANCHES: {
      return {
        ...state,
        providerBranches: action.payload,
        loading: false,
        success: true,
      };
    }
    case actionTypes.GET_PAYER_REGIONS:
      return {
        ...state,
        loading: false,
        payerRegions: action.payload,
      };
  

    default:
      return state;
  }
};
