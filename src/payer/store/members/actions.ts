import * as actionTypes from "./actionTypes";
import axios from "axios";
import {
  api_beneficiary_url,
  api_url,
  api_url_auditlog,
  api_url_biometrics,
  api_url_membership,
  api_url_member_management_Search,
  api_url_preauth,
  api_url_visit,
  api_url_provider,
  api_url_claim_report,
} from "../../lib/Utils";
import { baseUrl } from "~lib/constants";
import { downloadFile } from "~lib/utils";
import { VisitType } from "~lib/api/types";
import { sortOrder } from "~lib/constants";

export const startReimbursement = (visit) => async (dispatch) => {
  try {
    const res = await axios.post(`${api_url_visit}startVisit`, visit);

    if (res.data.success === true) {
      //log(res.data.success);

      dispatch({
        type: actionTypes.START_REIMBURSEMENT_VISIT,
        payload: res.data,
      });
    } else {
      dispatch({
        type: actionTypes.START_REIMBURSEMENT_VISIT_ERROR,
        payload: res.data.msg,
      });
      //log(res);
    }
  } catch (error) {
  } finally {
  }
};

export const startVisit = (visit) => async (dispatch) => {
  try {
    const res = await axios.post(`${api_url_visit}startVisit`, visit);

    if (res.data.success === true) {
      //log(res.data.success);

      dispatch({
        type: actionTypes.START_OFFLCT_VISIT,
        payload: res.data,
      });
    } else {
      dispatch({
        type: actionTypes.START_OFFLCT_VISIT_ERROR,
        payload: res.data.msg,
      });
      //log(res);
    }
  } catch (error) {
  } finally {
  }
};
export const setUpdateActiveBill = (bill) => async (dispatch) => {
  dispatch({
    type: actionTypes.UPDATE_ACTIVE_BILL,
    payload: bill,
  });
};

export const setSelectedMember = (member) => async (dispatch) => {
  dispatch({
    type: actionTypes.SELECTED_MEMBER,
    payload: member,
  });
};

export const clearActiveVisitList = () => async (dispatch) => {
  dispatch({
    type: actionTypes.CLEAR_ACTIVE_LIST,
  });
};
export const clearClosedVisitList = () => async (dispatch) => {
  dispatch({
    type: actionTypes.CLEAR_CLOSED_LIST,
  });
};

//

// Get pending Preuaths
export const getPendingPreAuths =
  ({ payerId }) =>
  async (dispatch) => {
    dispatch({
      type: actionTypes.CLEAR_ACTIVE_LIST,
    });
    try {
      const res = await axios.get(`${api_url_preauth}${payerId}/pending/payer`);
      //log(res.data);

      if (res.status == 200) {
        //log(res);

        dispatch({
          type: actionTypes.GET_PENDING_PREAUTHS,
          payload: res.data.data,
        });
      } else {
        //log(res);
      }
    } catch (error) {}
  };

// Get pending Preuaths
export const getAllPreAuths =
  ({ payerId }) =>
  async (dispatch) => {
    dispatch({
      type: actionTypes.CLEAR_ACTIVE_LIST,
    });
    try {
      const res = await axios.get(`${api_url_preauth}${payerId}/payer`);
      //log(res.data);

      if (res.status == 200) {
        //log(res);

        dispatch({
          type: actionTypes.GET_ALL_PREAUTHS,
          payload: res.data.data,
        });
      } else {
        //log(res);
      }
    } catch (error) {}
  };

export const approvePreAuth = (preauth) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING,
  });
  try {
    const res = await axios.put(`${api_url_preauth}authorize`, preauth);

    if (res.data.success === true) {
      //log(res.data.success);
      const logObject = {
        action: preauth.type,
        user: preauth.user,
        organisation: preauth.organisation,
        reason: preauth.reason,
        memberNumber: preauth.memberNumber,
        type: preauth.type,
      };

      const responseresult = await axios.post(`${api_url_auditlog}/save`, logObject);

      if (responseresult.data.success === true) {
        dispatch({
          type: actionTypes.APPROVE_PREAUTH,
          payload: responseresult.data.success,
        });
      } else {
        dispatch({
          type: actionTypes.APPROVE_PREAUTH_ERROR,
          payload: res.data.msg,
        });
      }
    } else {
      dispatch({
        type: actionTypes.APPROVE_PREAUTH_ERROR,
        payload: res.data.msg,
      });
      //log(res);
    }
  } catch (error) {
  } finally {
  }
};

export const declinePreAuth = (preauth) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING,
  });
  try {
    const res = await axios.put(`${api_url_preauth}decline`, preauth);

    if (res.data.success === true) {
      //log(res.data.success);
      const logObject = {
        action: preauth.type,
        user: preauth.user,
        organisation: preauth.organisation,
        reason: preauth.reason,
        memberNumber: preauth.memberNumber,
        type: preauth.type,
      };

      const responseresult = await axios.post(`${api_url_auditlog}/save`, logObject);

      if (responseresult.data.success === true) {
        dispatch({
          type: actionTypes.DECLINE_PREAUTH,
          payload: res.data,
        });
      } else {
        dispatch({
          type: actionTypes.DECLINE_PREAUTH_ERROR,
          payload: res.data.msg,
        });
      }
    } else {
      dispatch({
        type: actionTypes.DECLINE_PREAUTH_ERROR,
        payload: res.data.msg,
      });
      //log(res);
    }
  } catch (error) {
  } finally {
  }
};
export const setSelectedReason = (reason) => async (dispatch) => {
  dispatch({
    type: actionTypes.SELECTED_REASON,
    payload: reason,
  });
};
export const setSelectedProvider = (provider) => async (dispatch) => {
  dispatch({
    type: actionTypes.SELECTED_PROVIDER,
    payload: provider,
  });
};
export const setInputProviderName = (providerName) => async (dispatch) => {
  dispatch({
    type: actionTypes.INPUT_SELECTED_PROVIDER,
    payload: providerName,
  });
};
export const setInputProviderReimbursementName = (providerName) => async (dispatch) => {
  dispatch({
    type: actionTypes.INPUT_SELECTED_PROVIDER,
    payload: providerName,
  });
};
export const setInputInvoiceDateReimbursement = (invoiceDate) => async (dispatch) => {
  dispatch({
    type: actionTypes.INPUT_INVOICE_DATE,
    payload: invoiceDate,
  });
};
export const setInputInvoiceDateOfflct = (invoiceDate) => async (dispatch) => {
  dispatch({
    type: actionTypes.INPUT_OFFLCT_INVOICE_DATE,
    payload: invoiceDate,
  });
};
export const getMemberBenefits = (searchTerm) => async (dispatch) => {
  try {
    const res = await axios.get(`${api_beneficiary_url}/${searchTerm}`);

    if (res.status == 200) {
      dispatch({
        type: actionTypes.SEARCH_MEMBER_BENEFITS,
        payload: res.data.data,
      });
    }
  } catch (error) {}
};

export const getMemberDetails = (searchTerm) => async (dispatch) => {
  try {
    const res = await axios.get(
      `${api_url_member_management_Search}?memberNumber=${searchTerm.memberNumber}&id=${searchTerm.id}&payerId=${searchTerm.payerId}`
    );
    console.log(res.data.data);
    if ((await res).status === 200) {
      console.log((await res).data.data);
      dispatch({
        type: actionTypes.SEARCH_MEMBER,
        payload: (await res).data,
      });
    } else {
      ///console.log(res);
    }
  } catch (error) {}
};
export const getPlanByPayerId = (payerId) => async (dispatch) => {
  try {
    const res = await axios.get(`${api_url}/payer/${payerId}/plans`);
    console.log(res.data);
    if (res.data.success) {
      dispatch({
        type: actionTypes.GET_PLANS,
        payload: res.data.data,
      });
    } else {
      dispatch({
        type: actionTypes.GET_PLANS,
        payload: res.data.data,
      });
    }
  } catch (error) {}
};

// Off lct member search

export const getBiometrics = (memberNumber) => async (dispatch) => {
  try {
    const res = await axios.get(`${api_url_biometrics}retrieve?memberNumber=${memberNumber}`);
    console.log(res.data.data);

    if (res.data.success) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_BIOMETRICS,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};
export const updateBiometrics = (memberObject) => async (dispatch) => {
  console.log(memberObject.memberNumber);
  try {
    const res = await axios.put(
      `${api_url_biometrics}update?memberNumber=${memberObject.memberNumber}`
    );
    console.log(res.data.data);

    if (res.data.success) {
      console.log(res);
      const logObject = {
        action: memberObject.type,
        user: memberObject.user,
        organisation: memberObject.organisation,
        reason: memberObject.reason,
        memberNumber: memberObject.memberNumber,
        type: memberObject.type,
        time: memberObject.date,
        data: JSON.stringify(memberObject.data),
        beneficiaryId: memberObject.id,
      };

      const responseresult = await axios.post(`${api_url_auditlog}/save`, logObject);

      if (responseresult.data.success === true) {
        dispatch({
          type: actionTypes.UPDATE_BIOMETRICS,
          payload: responseresult.data.success,
        });
      } else {
        console.log(responseresult);
      }
    } else {
      console.log(res);
    }
  } catch (error) {}
};

export const topUpBenefit = (topUpObj) => async (dispatch) => {
  try {
    const res = await axios.post(`${api_url_visit}topUpBenefit`, topUpObj);
    console.log(res.data.data);

    if (res.data.success) {
      console.log(res);

      const logObject = {
        action: topUpObj.type,
        user: topUpObj.user,
        organisation: topUpObj.organisation,
        reason: topUpObj.reason,
        memberNumber: topUpObj.memberNumber,
        type: topUpObj.type,
        time: topUpObj.date,
        beneficiaryId: topUpObj.beneficiaryId,
        data: JSON.stringify(topUpObj.data),
      };

      const responseresult = await axios.post(`${api_url_auditlog}/save`, logObject);

      if (responseresult.data.success === true) {
        dispatch({
          type: actionTypes.TOPUP_BENEFIT,
          payload: responseresult.data.success,
        });
      } else {
        console.log(responseresult);
      }
    } else {
      console.log(res);
    }
  } catch (error) {}
};

export const getCategoriesInPolicy = (policyId) => async (dispatch) => {
  try {
    const res = await axios.get(`${api_url}/policy/${policyId}/categories`);
    console.log(res.data.data);
    if ((await res).status === 200) {
      console.log((await res).data.data);
      dispatch({
        type: actionTypes.GET_CATEGORIES,
        payload: (await res).data.data,
      });
    } else {
      ///console.log(res);
    }
  } catch (error) {}
};

//changeCategory
export const changeCategory = (categoryObj) => async (dispatch) => {
  try {
    const res = await axios.post(`${api_url}/changeCategory`, categoryObj);
    console.log(res);
    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);
      console.log(res);
      const logObject = {
        action: categoryObj.type,
        user: categoryObj.user,
        organisation: categoryObj.organisation,
        reason: categoryObj.reason,
        memberNumber: categoryObj.memberNumber,
        type: categoryObj.type,
        time: categoryObj.date,
        beneficiaryId: categoryObj.id,
        data: JSON.stringify(categoryObj.data),
      };

      const responseresult = await axios.post(`${api_url_auditlog}/save`, logObject);

      if (responseresult.data.success === true) {
        dispatch({
          type: actionTypes.CHANGE_CATEGORY,
          payload: responseresult.data.success,
        });
      } else {
        console.log(responseresult);
      }
    }
  } catch (error) {
  } finally {
  }
};

//transferBenefit
export const transferBenefit = (transferObj) => async (dispatch) => {
  try {
    const res = await axios.post(`${api_url_visit}transferBenefit`, transferObj);
    console.log(res);
    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);

      const logObject = {
        action: transferObj.type,
        user: transferObj.user,
        organisation: transferObj.organisation,
        reason: transferObj.reason,
        memberNumber: transferObj.memberNumber,
        type: transferObj.type,
        time: transferObj.date,
        beneficiaryId: transferObj.beneficiaryId,
        data: JSON.stringify(transferObj.data),
      };

      const responseresult = await axios.post(`${api_url_auditlog}/save`, logObject);
      if (responseresult.data.success === true) {
        dispatch({
          type: actionTypes.TRANSFER_BENEFIT,
          payload: responseresult.data.success,
        });
      } else {
        console.log(responseresult);
      }
    }
  } catch (error) {
  } finally {
  }
};

// Search member directly on membership
export const searchMember = (payerId, searchTerm) => async (dispatch) => {
  try {
    const res = await axios.get(
      `${api_url}/memberSearchPayerInquiry?payerId=${payerId}&search=${searchTerm}`
    );
    console.log(res.data);
    console.log(res.data.success);

    if (res.data.success) {
      console.log(res);

      dispatch({
        type: actionTypes.SEARCH_MEMBERSHIP_DIRECT,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};
export const searchMemberWithHRRole = (payerId, searchTerm, schemeId) => async (dispatch) => {
  try {
    const res = await axios.get(
      `${api_url}/beneficiariesByPayerAndPlan?payerId=${payerId}&search=${searchTerm}&planId=${schemeId}`
    );

    if (res.data.success) {
      console.log(res);

      dispatch({
        type: actionTypes.SEARCH_MEMBERSHIP_DIRECT,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};

export const clearMemberSearch = () => async (dispatch) => {
  dispatch({
    type: actionTypes.CLEAR_MEMBER_SEARCH,
  });
};

export const clearMemberResult = () => async (dispatch) => {
  dispatch({
    type: actionTypes.CLEAR_MEMBER_RESULT,
  });
};

export const updateMember = (member) => async (dispatch) => {
  try {
    const res = await axios.post(`${api_url}/updateMember`, member);
    console.log(res);

    if (res.data.success === true) {
      console.log(res.data.success);

      const logObject = {
        action: member.type,
        user: member.user,
        organisation: member.organisation,
        reason: member.reason,
        memberNumber: member.memberNumber,
        canUseBiometrics: member.canUseBiometrics,
        type: member.type,
        time: member.date,
        beneficiaryId: member.id,
        data: JSON.stringify(member.data),
      };

      const responseresult = await axios.post(`${api_url_auditlog}/save`, logObject);

      if (responseresult.data.success === true) {
        dispatch({
          type: actionTypes.UPDATE_MEMBER,
          payload: responseresult.data.success,
        });
      } else {
        console.log(responseresult);
      }
    } else {
      console.log(res);
    }
  } catch (error) {}
};

////activate Member
export const activateMember = (member) => async (dispatch) => {
  try {
    const res = await axios.post(`${api_url}/activateBeneficiary/${member.beneficiaryId}`);
    console.log(res.data.data);

    if (res.data.success) {
      console.log(res);

      const logObject = {
        action: member.type,
        user: member.user,
        organisation: member.organisation,
        reason: member.reason,
        memberNumber: member.memberNumber,
        type: member.type,
        time: member.date,
        beneficiaryId: member.beneficiaryId,
        data: JSON.stringify(member.data),
      };

      const responseresult = await axios.post(`${api_url_auditlog}/save`, logObject);

      if (responseresult.data.success === true) {
        dispatch({
          type: actionTypes.ACTIVATE_MEMBER,
          payload: responseresult.data.success,
        });
      } else {
        console.log(responseresult);
      }
    } else {
      console.log(res);
    }
  } catch (error) {}
};

////deactivate Member
export const deactivateMember = (member) => async (dispatch) => {
  try {
    const res = await axios.post(`${api_url}/deactivateBeneficiary/${member.beneficiaryId}`);
    console.log(res.data.data);

    if (res.data.success) {
      console.log(res);

      const logObject = {
        action: member.type,
        user: member.user,
        organisation: member.organisation,
        reason: member.reason,
        memberNumber: member.memberNumber,
        type: member.type,
        time: member.date,
        beneficiaryId: member.beneficiaryId,
        data: JSON.stringify(member.data),
      };

      const responseresult = await axios.post(`${api_url_auditlog}/save`, logObject);

      if (responseresult.data.success === true) {
        dispatch({
          type: actionTypes.DEACTIVATE_MEMBER,
          payload: responseresult.data.success,
        });
      } else {
        console.log(responseresult);
      }
    } else {
      console.log(res);
    }
  } catch (error) {}
};

export const clearBillData = () => async (dispatch) => {
  dispatch({
    type: actionTypes.CLEAR_ACTIVE_USER_DATA,
  });
};

export const getClosedVisits =
  ({ payerId, offSetValue }) =>
  async (dispatch) => {
    // console.log(hospitalProviderId + "///" + staffId + "///" + offSetValue);

    dispatch({
      type: actionTypes.CLEAR_CLOSED_LIST,
    });
    const offset = offSetValue ? offSetValue : "1";
    try {
      const res = await axios.get(`${api_url_visit}offlct/${payerId}/11111/${offset}/100/closed`);
      //log(res.data);

      if (res.status == 200) {
        //log(res);

        dispatch({
          type: actionTypes.GET_CLOSED_VISITS,
          payload: res.data.data,
        });
      } else {
        //log(res);
      }
    } catch (error) {}
  };

export const getClosedReiumbursement =
  ({ payerId, offSetValue }) =>
  async (dispatch) => {
    // console.log(hospitalProviderId + "///" + staffId + "///" + offSetValue);

    dispatch({
      type: actionTypes.CLEAR_CLOSED_LIST,
    });
    const offset = offSetValue ? offSetValue : "1";
    try {
      const res = await axios.get(
        `${api_url_visit}reimbursement/${payerId}/11111/${offset}/1000/closed`
      );
      //log(res.data);

      if (res.status == 200) {
        //log(res);

        dispatch({
          type: actionTypes.GET_CLOSED_VISITS,
          payload: res.data.data,
        });
      } else {
        //log(res);
      }
    } catch (error) {}
  };

export const getActiveVisits =
  ({ payerId }) =>
  async (dispatch) => {
    dispatch({
      type: actionTypes.CLEAR_ACTIVE_LIST,
    });
    const staffid = "11111";
    try {
      const res = await axios.get(`${api_url_visit}offlct/${payerId}/${staffid}/active`);
      //log(res.data);

      if (res.status == 200) {
        //log(res);

        dispatch({
          type: actionTypes.GET_ACTIVE_VISITS,
          payload: res.data.data,
        });
      } else {
        //log(res);
      }
    } catch (error) {}
  };

export const getActiveReimbursement =
  ({ payerId }) =>
  async (dispatch) => {
    dispatch({
      type: actionTypes.CLEAR_ACTIVE_LIST,
    });
    const staffid = "11111";
    try {
      const res = await axios.get(`${api_url_visit}reimbursement/${payerId}/${staffid}/active`);
      //log(res.data);

      if (res.status == 200) {
        //log(res);

        dispatch({
          type: actionTypes.GET_ACTIVE_VISITS,
          payload: res.data.data,
        });
      } else {
        //log(res);
      }
    } catch (error) {}
  };

export const setActiveBillData = (bill) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_ACTIVE_BILL,
    payload: bill,
  });
};

// Bill and  Close Visit
export const CloseVisit = (visit) => async (dispatch) => {
  try {
    const res = await axios.post(`${api_url_visit}saveAndCloseBillItemPortal`, visit);

    if (res.data.success === true) {
      //log(res.data.success);

      dispatch({
        type: actionTypes.BILL_AND_CLOSE_VISIT,
        payload: res.data.success,
      });
    } else {
      //log(res);
    }
  } catch (error) {
    dispatch({
      type: actionTypes.BILL_AND_CLOSE_VISIT_ERROR,
      payload: error,
    });
  } finally {
  }
};

export const setSelectedPreAuthItem = (preauthItem) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_SELECTED_PREAUTH_ITEM,
    payload: preauthItem,
  });
};

// GET CLAIMS TO VET---PAYER

// export const getClaimsToVet = (payerId, providerId, query, offSetValue,planSelected,vetStatusSelected,size,startDate,endDate) => async (dispatch) => {
//   console.log(size);
//   dispatch({
//     type: actionTypes.SET_LOADING_CLAIMS_TO_VET,
//   });
//   try {
//     const res = await axios.get(`${api_url_visit}vetting/search`, {
//       params: {
//         payerId: payerId,
//         providerId: providerId?Number(providerId):undefined,
//         query: query?query:undefined,
//         page: offSetValue? offSetValue : 1,
//         planId:planSelected?Number(planSelected):undefined,
//         vetStatus:vetStatusSelected?vetStatusSelected:undefined,
//         size:size?size:10,
//         startDate:startDate,
//         endDate:endDate
//       },
//     });
//     console.log(res.data.data);

//     if (res.data.success) {
//       console.log(res);

//       dispatch({
//         type: actionTypes.GET_CLAIMS_TO_VET,
//         payload: res.data,
//       });
//     } else {
//       console.log(res);
//     }
//   } catch (error) {}
// };


//SECOND GET CLAIMS TO VET---PAYER
export const getClaimsToVet = (payerId, providerId, query, offSetValue,planSelected,vetStatusSelected,size,startDate,endDate) => async (dispatch) => {
  console.log(size);
  dispatch({
    type: actionTypes.SET_LOADING_CLAIMS_TO_VET,
  });
  try {
    const res = await axios.get(`${api_url_visit}search`, {
      params: {
        payerId: payerId,
        mainProviderIds: providerId?Number(providerId):undefined,
        query: query?query:undefined,
        page: offSetValue? offSetValue : 1,
        planId:planSelected?Number(planSelected):undefined,
        visitVettingStatuses:vetStatusSelected?vetStatusSelected:undefined,
        visitTypes: `${VisitType.ONLINE},${VisitType.OFF_LCT}`,
        invoiceStatuses:`${'BALANCE_DEDUCTED'}`,
        statuses:`${'CLOSED'},${'LINE_ITEMS_ADDED'}`,
        retrieveDocuments:true,
        size:size?size:10,
        vetStartDate:startDate,
        vetEndDate:endDate,
        sortOrder:sortOrder.ASC
      },
    });
    console.log(res.data.data);

    if (res.data.success) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_CLAIMS_TO_VET,
        payload: res.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};

export const getClaimsToVetById = (id) => async (dispatch) => {
  console.log(id);
  // dispatch({
  //   type: actionTypes.SET_LOADING_CLAIM,
  // });
  try {
    const res = await axios.get(`${api_url_visit}/${id}/visit`);
    console.log(res.data.data);

    if (res.data.success) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_CLAIM_BY_ID,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};

export const getPreAuthToVetById = (id) => async (dispatch) => {
  console.log(id);
  
  try {
    const res = await axios.get(`${api_url_preauth}/${id}/preauth`);
    console.log(res.data.data);

    if (res.data.success) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_PREAUTH_BY_ID,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};


// get line items by visit
export const getLineItemsByVisitIdAndInvoiceNumber = (visitId, invoiceNo) => async (dispatch) => {
  console.log(invoiceNo)
    dispatch({
    type: actionTypes.SET_LOADING_LINE_ITEMS_BY_VISIT,
  });

  try {
    const res = await axios.get(
      `${api_url_visit}invoice/lineItems`,
      {
        params: {
          visitNumber: visitId,
          invoiceNumber: invoiceNo,
        },
      }
    );

    if (res.status == 200) {
      //log(res);

      dispatch({
        type: actionTypes.GET_LINE_ITEMS_BY_VISIT,
        payload: res.data.data,
      });
    } else {
      //log(res);
    }
  } catch (error) {}
};


///get invoices by visit number
export const getInvoicesByVisitId = (visitId) => async (dispatch) => {
    dispatch({
    type: actionTypes.SET_LOADING_INVOICES_BY_VISIT,
  });

  try {
    const res = await axios.get(
      `${api_url_visit}/${visitId}/invoices`
    );

    if (res.status == 200) {
      //log(res);

      dispatch({
        type: actionTypes.GET_INVOICES_BY_VISIT,
        payload: res.data.data,
      });
    } else {
      //log(res);
    }
  } catch (error) {}
};

///get documents by invoice number & visit id 
export const getDocumentsByVisitId = (visitId) => async (dispatch) => {
    dispatch({
    type: actionTypes.SET_LOADING_DOCUMENTS_TO_VET,
  });

  try {
    const res = await axios.get(
      `${api_url_visit}invoice/documents`,
      {
        params: {
          visitNumber: visitId
        },
      }
    );

    if (res.status == 200) {
      //log(res);
      dispatch({
        type: actionTypes.GET_DOCUMENTS_TO_VET,
        payload: res.data.data,
      });
    } else {
      //log(res);
    }
  } catch (error) {}
};


// Update vetting Status
export const vetClaimInvoice = (invoiceVetObj) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_VET_INVOICE,
  });
  try {
    const res = await axios.post(`${api_url_visit}/claimsVetting/invoice`, invoiceVetObj);

    if (res.data.success === true) {
      console.log(res.data.success);
      console.log(res);

      dispatch({
        type: actionTypes.VET_INVOICE,
        payload: res.data.success,
      });
    }
  } catch (error) {
    //
    dispatch({
      type: actionTypes.VET_INVOICE_FAIL,
      payload: false,
    });
  } finally {
    //
  }
};


// GET CLAIMS TO BATCH---PAYER

// export const getClaimsToBatch = (payerId, providerId, query, offSetValue,planSelected,regionSelected,startDate,endDate) => async (dispatch) => {
//   console.log(planSelected);
//   dispatch({
//     type: actionTypes.SET_LOADING_CLAIMS_TO_BATCH,
//   });
//   try {
//     const res = await axios.get(`${api_url_visit}vettedClaims/search`, {
//       params: {
//         payerId: payerId,
//         providerId: providerId?Number(providerId):undefined,
//         query: query?query:undefined,
//         page: offSetValue? offSetValue : 1,
//         planId:planSelected?Number(planSelected):undefined,
//         size:0,
//         regionId:regionSelected?Number(regionSelected):undefined,
//         startDate:startDate,
//         endDate:endDate
//       },
//     });
//     console.log(res.data.data);

//     if (res.data.success) {
//       console.log(res);

//       dispatch({
//         type: actionTypes.GET_CLAIMS_TO_BATCH,
//         payload: res.data,
//       });
//     } else {
//       console.log(res);
//     }
//   } catch (error) {}
// };

//Get claims to batch

export const getClaimsToBatch = (payerId, providerId, query, offSetValue,planSelected,regionSelected,size,startDate,endDate) => async (dispatch) => {
  console.log(planSelected);
  dispatch({
    type: actionTypes.SET_LOADING_CLAIMS_TO_BATCH,
  });
  try {
    const res = await axios.get(`${api_url_visit}invoices/search`, {
      params: {
        payerIds: payerId,
        providerIds: providerId?Number(providerId):undefined,
        query: query?query:undefined,
        page: offSetValue? offSetValue : 1,
        planIds:planSelected?Number(planSelected):undefined,
        region:regionSelected?String(regionSelected):undefined,
        batchStatus:`NOT_BATCHED`,
        vettingStatuses:`${`APPROVED`},${`PARTIAL`}`,
       // size:size?size:10,
       size:100000,
        vettingStartDate:startDate,
        vettingEndDate:endDate
      },
    });
    console.log(res.data.data);

    if (res.data.success) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_CLAIMS_TO_BATCH,
        payload: res.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};

// batch invoices
export const batchInvoices = (invoices) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_BATCH_INVOICES,
  });
  try {
    const res = await axios.post(`${api_url_visit}/batchInvoices`, invoices);

    if (res.data.success === true) {

      dispatch({
        type: actionTypes.BATCH_INVOICES,
        payload: res.data,
      });
    }
  } catch (error) {
    //
    dispatch({
      type: actionTypes.BATCH_INVOICES_FAIL,
      payload: false,
    });
  } finally {
    //
  }
};

// get batched invoices
export const getBatchedInvoices = (payerId,query,page,size) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_GET_BATCHED_INVOICES,
  });
  try {
    const res = await axios.get(`${api_url_visit}/batch/list`, {
      params: {
        payerId: payerId,
        query: query?query:undefined,
        size:10
      },
    });

    if (res.data.success === true) {

      dispatch({
        type: actionTypes.GET_BATCHED_INVOICES,
        payload: res.data,
      });
    }
  } catch (error) {
    //
    dispatch({
      type: actionTypes.GET_BATCHED_INVOICES_FAIL,
      payload: false,
    });
  } finally {
    //
  }
};

// DOWNLOAD SUPPORTING DOCUMENTS PAYER

export const DownloadDocument = (url) => async (dispatch) => {
  console.log(url)
  dispatch({
    type: actionTypes.SET_LOADING_DOWNLOAD_DOCUMENTS_TO_VET,
  });
  try {
    const res = await axios.get(`${baseUrl}/api/file/download`, {
      params: {
        name: url
      },
    });
    console.log(res.data.data);

    if (res.data.success) {
      console.log(res);

      dispatch({
        type: actionTypes.DOWNLOAD_DOCUMENTS_TO_VET,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};

// get Provider Branches
export const getProviderBranches = (providerId) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_PROVIDER,
  });
  try {
    const res = await axios.get(
      `${api_url_provider}branches?providerId=${providerId}`
    );
    //console.log(res.data.data);

    if (res.data.success == true) {
      //console.log(res);

      dispatch({
        type: actionTypes.GET_PROVIDER_BRANCHES,
        payload: res.data.data,
      });
    } else {
      //console.log(res);
    }
  } catch (error) {}
};

export const getPayerRegionsByPayerId = (payerId) => async (dispatch) => {
  try {
    const res = await axios.get(`${api_url}/payer/regions/${payerId}`);
    console.log(res.data);
    if (res.data.success) {
      dispatch({
        type: actionTypes.GET_PAYER_REGIONS,
        payload: res.data.data,
      });
    } else {
      dispatch({
        type: actionTypes.GET_PAYER_REGIONS,
        payload: res.data.data,
      });
    }
  } catch (error) {}
};


