import * as actionTypes from "./actionTypes";
import axios from "axios";
import {
  api_url,
  api_url_Catalog,
  api_url_country,
  api_url_membership,
  api_url_provider,
  api_url_search,
} from "../../lib/Utils";
import { IProvider } from "./types";

// Set loading to true
export const setLoading = () => {
  return {
    type: actionTypes.SET_LOADING_PROVIDER,
  };
};
// add provider
export const addProvider = (provider) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_PROVIDER,
  });
  console.log(provider);
  try {
    const res = await axios.post(`${api_url_provider}`, provider);
    console.log(res);
    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);

      dispatch({
        type: actionTypes.ADD_PROVIDER,
        payload: res.data.data,
      });
    } else {
      dispatch({
        type: actionTypes.ADD_PROVIDER_ERROR,
        payload: res.data.msg,
      });
    }
  } catch (error) {
  } finally {
  }
};
// add provider
export const addProviderCashier = (provider) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_PROVIDER,
  });
  try {
    const res = await axios.post(`${api_url_provider}`, provider);
    console.log(res);
    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);

      dispatch({
        type: actionTypes.ADD_PROVIDER,
        payload: res.data.data,
      });
    } else {
      dispatch({
        type: actionTypes.ADD_PROVIDER_ERROR,
        payload: res.data.msg,
      });
    }
  } catch (error) {
  } finally {
  }
};
// add provider Copayment
export const addProviderCopay = (provider) => async (dispatch) => {
  setLoading();
  try {
    const res = await axios.post(`${api_url}/providers`, provider);
    console.log(res);
    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);

      dispatch({
        type: actionTypes.ADD_PROVIDER,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {
  } finally {
  }
};
// whitelist single
export const whitelistProviderBenefit = (provider) => async (dispatch) => {
  setLoading();
  try {
    const res = await axios.post(`${api_url}/providers`, provider);
    console.log(res);
    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);

      dispatch({
        type: actionTypes.ADD_PROVIDER,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {
  } finally {
  }
};
// Whitelist multiple
export const whitelistMultipleProviderBenefit = (provider) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_PROVIDER,
  });
  const provider_id = { provider };
  console.log(provider_id.provider.benefitIds);
  const obj = {};
  obj["benefitIds"] = provider_id.provider.benefitIds;
  try {
    const res = await axios.post(
      `${api_url_provider}${provider_id.provider.provider_id}/whitelist/multiple`,
      obj,
    );
    console.log(res);
    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);
      dispatch({
        type: actionTypes.WHITELIST_MULTIPLE_BENEFITS,
        payload: res.data.success,
      });
    } else {
      console.log(res);
    }
  } catch (error) {
  } finally {
  }
};

// Process Provider Exclusions
export const processProviderExclusions = (provider) => async (dispatch) => {
  setLoading();
  try {
    const res = await axios.post(`${api_url}/providers`, provider);
    console.log(res);
    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);

      dispatch({
        type: actionTypes.ADD_PROVIDER,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {
  } finally {
  }
};

// Add provider Exclusion
export const addProviderExclusion = (provider) => async (dispatch) => {
  setLoading();
  try {
    const res = await axios.post(`${api_url}/providers`, provider);
    console.log(res);
    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);

      dispatch({
        type: actionTypes.ADD_PROVIDER,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {
  } finally {
  }
};
// deactivate provider Exclusion
export const deactivateProviderExclusion = (provider) => async (dispatch) => {
  setLoading();
  try {
    const res = await axios.post(`${api_url}/providers`, provider);
    console.log(res);
    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);

      dispatch({
        type: actionTypes.ADD_PROVIDER,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {
  } finally {
  }
};
// process Provider Copayment
export const processProviderCopays = (provider) => async (dispatch) => {
  setLoading();
  try {
    const res = await axios.post(`${api_url}/providers`, provider);
    console.log(res);
    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);

      dispatch({
        type: actionTypes.ADD_PROVIDER,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {
  } finally {
  }
};

// get Providers
export const getProviders = (payerId) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_PROVIDER,
  });

  try {
    const res = await axios.get(`${api_url_search}provider/payer/${payerId}/1/100000`);
    console.log(res.data);

    dispatch({
      type: actionTypes.GET_PROVIDERS,
      payload: res.data,
    });
  } catch (error) {}
};

// get Provider exclusions
export const getProviderExclusions = (providerId) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_PROVIDER,
  });
  try {
    const res = await axios.get(`${api_url_provider}${providerId}/exclusions`);
    console.log(res.data.data);

    if (res.data.success == true) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_PROVIDER_EXCLUSIONS,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};
// get Provider copays
export const getProviderCopays = (providerId) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_PROVIDER,
  });
  try {
    const res = await axios.get(`${api_url_provider}${providerId}/copays`);
    console.log(res.data.data);

    if (res.data.success == true) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_PROVIDER_COPAYS,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};
// get Provider whitelist
export const getProviderWhitelist = (providerId, offSetValue) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_PROVIDER,
  });
  const offset = offSetValue ? offSetValue : "1";
  try {
    const res = await axios.get(
      `${api_url_provider}${providerId}/whitelist?page=${offset}&size=10`,
    );
    console.log(res.data.data);

    if (res.data.success == true) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_PROVIDER_WHITELIST,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};
// show Provider
export const showProvider = (provider) => async (dispatch) => {
  dispatch({
    type: actionTypes.SHOW_PROVIDER,
    payload: provider,
  });
};

// Get provider by tier
export const getProvidersByTier = (tier, offSetValue) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_PROVIDER,
  });
  const offset = offSetValue ? offSetValue : "1";
  try {
    const res = await axios.get(`${api_url_provider}tier?tier=${tier}&page=${offset}&size=10`);
    console.log(res.data.data);

    if (res.data.success == true) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_PROVIDERS,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};

// Get providers by Region
export const getProvidersByRegion = (regionId, offSetValue) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_PROVIDER,
  });
  const offset = offSetValue ? offSetValue : "1";
  try {
    const res = await axios.get(
      `${api_url_country}region/${regionId}/providers?regionId=${regionId}&page=${offset}&size=10`,
    );
    console.log(res.data.data);

    if (res.data.success == true) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_PROVIDERS,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};
