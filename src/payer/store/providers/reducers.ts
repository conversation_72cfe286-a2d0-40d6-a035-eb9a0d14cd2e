import * as actionTypes from "./actionTypes";
import { ProviderAction, ProviderState } from "./types";

const initialState = {
  providers: [],
  exclusions: [],
  copays: [],
  whitelist: [],
  success: null,
  loading: false,
  providerItem: {},
  selectedProvider: {},
  totalElements: null,
  totalPages: null,
  pageNumber: null,

  totalWhiteListElements: null,
  totalWhiteListPages: null,
  pageWhiteListNumber: null,

  msg: null,
};

export const providerReducer = (state = initialState, action) => {
  switch (action.type) {
    case actionTypes.SET_LOADING_PROVIDER:
      return {
        ...state,
        loading: true,
        success: null,
      };
    case actionTypes.ADD_PROVIDER: {
      const newproviders = [...state.providers, action.payload];
      const newState = {
        providers: newproviders,
        loading: false,
        success: true,
      };
      return { ...state, ...newState };
    }
    case actionTypes.ADD_PROVIDER_ERROR: {
      const newState = {
        msg: action.payload,
        loading: false,
        success: false,
      };
      return { ...state, ...newState };
    }
    case actionTypes.MAP_BENEFIT: {
      const newproviders = [...state.providers, action.payload];
      const newState = {
        providers: newproviders,
        loading: false,
        success: true,
      };
      return { ...state, ...newState };
    }
    case actionTypes.ADD_PROVIDER_SUCCESS: {
      const newState = {
        success: action.payload,
        loading: false,
      };
      return { ...state, ...newState };
    }
    case actionTypes.WHITELIST_MULTIPLE_BENEFITS: {
      const newState = {
        success: action.payload,
        loading: false,
      };
      return { ...state, ...newState };
    }
    case actionTypes.GET_PROVIDERS: {
      return {
        ...state,
        providers: action.payload,
        loading: false,
      };
    }
    case actionTypes.GET_PROVIDER_EXCLUSIONS: {
      return {
        ...state,
        exclusions: action.payload,
        loading: false,
        success: true,
      };
    }
    case actionTypes.GET_PROVIDER_COPAYS: {
      return {
        ...state,
        copays: action.payload,
        loading: false,
        success: true,
      };
    }
    case actionTypes.GET_PROVIDER_WHITELIST: {
      return {
        ...state,
        whitelist: action.payload.content,
        loading: false,
        success: true,
        totalWhiteListElements: action.payload.totalElements,
        totalWhiteListPages: action.payload.totalPages,
        pageWhiteListNumber: action.payload.pageable.pageNumber,
      };
    }
    case actionTypes.ADD_PROVIDER_LOADING: {
      const newState = {
        loading: true,
        success: false,
      };
      return { ...state, ...newState };
    }
    case actionTypes.SHOW_PROVIDER: {
      const newState = {
        providerItem: action.payload,
      };
      return { ...state, ...newState };
    }
    case actionTypes.SET_SELECTED_BENEFIT: {
      const newState = {
        selectedBenefit: action.payload,
      };
      return { ...state, ...newState };
    }
    case actionTypes.SET_SELECTED_PROVIDER: {
      const newState = {
        selectedProvider: action.payload,
      };
      return { ...state, ...newState };
    }
    default:
      return state;
  }
};
