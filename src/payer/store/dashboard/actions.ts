import * as actionTypes from "./actionTypes";
import axios from "axios";
import {
  api_beneficiary_url,
  api_url,
  api_url_auditlog,
  api_url_membership_root,
  api_url_visit,
} from "../../lib/Utils";

// Set loading to true
export const setLoading = () => {
  return {
    type: actionTypes.SET_LOADING_DASHBOARD,
  };
};

// export const getDashboardTasks = () => async (dispatch) => {
//   dispatch({
//     type: actionTypes.SET_LOADING_DASHBOARD,
//   });
//   try {
//     const res = await axios.get(`${api_url_tasks}`);
//     console.log(res.data);

//     if (res.status == 200) {
//       console.log(res);

//       dispatch({
//         type: actionTypes.GET_DASHBOARD_TASKS,
//         payload: res.data,
//       });
//     } else {
//       console.log(res);
//     }
//   } catch (error) {}
// };
export const showDashboard = (dashboard) => async (dispatch) => {
  dispatch({
    type: actionTypes.SHOW_DASHBOARD,
    payload: dashboard,
  });
};
export const setTaskId = (taskId) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_TASKID,
    payload: taskId,
  });
};
export const setTaskCategoryIdPolicyId =
  (categoryPolicyObject) => async (dispatch) => {
    dispatch({
      type: actionTypes.SET_CATEGORYIDPOLICYID,
      payload: categoryPolicyObject,
    });
  };
export const setDashboardNotifications =
  (notifications) => async (dispatch) => {
    dispatch({
      type: actionTypes.SET_DASHBOARD_NOTIFICATIONS,
      payload: notifications,
    });
  };
export const setSelectedDashboard = (dashboard) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_SELECTED_DASHBOARD,
    payload: dashboard,
  });
};

export const getProviderTransactions = (filters) => async (dispatch) => {
  const { selectedSchemeId, dateTo, dateFrom, providerId } = filters;
  dispatch({
    type: actionTypes.SET_LOADING_REPORTS,
  });
  try {
    const res = await axios.get(
      `${api_url_visit}providerClaims?planId=${selectedSchemeId}&dateFrom=${dateFrom}&dateTo=${dateTo}&hospitalProviderId=${providerId}`
    );
    dispatch({
      type: actionTypes.GET_PROVIDER_TRANSACTIONS,
      payload: res.data.data,
    });
  } catch (error) {
    console.log("ERROR", error.response);
  }
};

export const getPlanReimbursementsTransactions =
  (filters) => async (dispatch) => {
    const { selectedSchemeId, dateTo, dateFrom, providerId } = filters;
    dispatch({
      type: actionTypes.SET_LOADING_REPORTS,
    });
    try {
      const res = await axios.get(
        `${api_url_visit}planReimbursements?planId=${selectedSchemeId}&dateFrom=${dateFrom}&dateTo=${dateTo}`
      );
      dispatch({
        type: actionTypes.GET_PLAN_REIMBURSEMENTS,
        payload: res.data.data,
      });
    } catch (error) {
      console.log("ERROR", error.response);
    }
  };

export const getPlanOfflctsTransactions = (filters) => async (dispatch) => {
  const { selectedSchemeId, dateTo, dateFrom, providerId } = filters;
  dispatch({
    type: actionTypes.SET_LOADING_REPORTS,
  });
  try {
    const res = await axios.get(
      `${api_url_visit}planOfflcts?planId=${selectedSchemeId}&dateFrom=${dateFrom}&dateTo=${dateTo}&hospitalProviderId=${providerId}`
    );
    dispatch({
      type: actionTypes.GET_PLAN_OFFLCTS,
      payload: res.data.data,
    });
  } catch (error) {
    console.log("ERROR", error.response);
  }
};

export const getSchemes = (offSetValue) => async (dispatch) => {
  const offset = offSetValue ? offSetValue : "1";
  try {
    const res = await axios.get(
      `${api_url_membership_root}/plans?page=${offset}&size=100`
    );
    console.log(res.data.data);

    if (res.data.success == true) {
      console.log(res.data.data);

      dispatch({
        type: actionTypes.GET_SCHEMES,
        payload: res.data.data.content,
      });
    } else {
      console.log(res);
    }
  } catch (error) {
    console.log("ERROR", error.response);
  }
};
export const setRefreshHistory = (random) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_REFRESH_HISTORY,
    payload: random,
  });
};
export const setSelectedHistoryItem = (historyItem) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_SELECTED_HISTORY_ITEM,
    payload: historyItem,
  });
};

export const printProviderTransactionsMultiple =
  (filters) => async (dispatch) => {
    const { selectedSchemeId, dateTo, dateFrom, providerId } = filters;
    try {
      const res = await axios.get(
        `${api_url_visit}statement/provider/${providerId}?planId=${selectedSchemeId}&from=${dateFrom}&to=${dateTo}`
      );
      console.log(res.data.data);

      if (res.data.success == true) {
        console.log(res.data.data);
      } else {
        console.log(res);
      }
    } catch (error) {
      console.log("ERROR", error.response);
    }
  };

// // Search Provider
// export const searchClaim = (payerId, search) => async (dispatch) => {
//   dispatch({
//     type: actionTypes.SET_LOADING_CLAIMS,
//   });

//   try {
//     const res = await axios.get(
//       `${api_url_visit}searchByInvoiceOrMemberNo?payerId=${payerId}&search=${search}`
//     );
//     console.log(res.data.data);

//     if (res.data.success == true) {
//       console.log(res);

//       dispatch({
//         type: actionTypes.SEARCH_CLAIM,
//         payload: res.data.data,
//       });
//     } else {
//       console.log(res);
//     }
//   } catch (error) {
//     console.log(error);
//   }
// };

// Search Provider
export const searchClaim = (payerId, search) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_CLAIMS,
  });

  try {
    const res = await axios.get(
      `${api_url_visit}invoices/search?payerId=${payerId}&query=${search}`
    );
    console.log(res.data.data);

    if (res.data.success == true) {
      console.log(res);

      dispatch({
        type: actionTypes.SEARCH_CLAIM,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {
    console.log(error);
  }
};

export const getInvoiceLines =
  (invoiceNumber, visitNumber) => async (dispatch) => {
    dispatch({
      type: actionTypes.SET_LOADING_LINES,
    });
    try {
      const res = await axios.get(
        `${api_url_visit}/invoice/lineItems?invoiceNumber=${invoiceNumber}&visitNumber=${visitNumber}`
      );
      console.log(res.data.data);

      if (res.data.success == true) {
        console.log(res.data.data);

        dispatch({
          type: actionTypes.GET_INVOICE_LINES,
          payload: res.data.data,
        });
      } else {
        console.log(res);
      }
    } catch (error) {
      console.log("ERROR", error.response);
    }
  };

export const reverseClaim = (visit) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_CLAIMS,
  });
  try {
    const res = await axios.post(`${api_url_visit}reverseVisit`, visit);
    console.log(res);
    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);

      const logObject = {
        action: "REVERSAL",
        user: visit.user,
        organisation: visit.organisation,
        reason: visit.reason,
        memberNumber: visit.memberNumber,
        type: "BENEFIT_UPDATE",
      };

      const responseresult = await axios.post(
        `${api_url_auditlog}/save`,
        logObject
      );
      dispatch({
        type: actionTypes.REVERSE_CLAIM,
        payload: res.data.success,
      });
      // if (responseresult.data.success === true) {

      // }
    } else {
      dispatch({
        type: actionTypes.REVERSE_CLAIM_ERROR,
        payload: res.data.msg,
      });
    }
  } catch (error) {
    console.log(error);
  }
};

export const reverseInvoice = (invoice) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_REVERSAL,
  });
  try {
    const res = await axios.post(`${api_url_visit}InvoiceReversal`, invoice);
    console.log(res);
    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);

      const logObject = {
        action: "REVERSAL",
        user: invoice.user,
        organisation: invoice.organisation,
        reason: invoice.reason,
        memberNumber: invoice.memberNumber,
        type: "BENEFIT_UPDATE",
      };

      const responseresult = await axios.post(
        `${api_url_auditlog}/save`,
        logObject
      );
      dispatch({
        type: actionTypes.REVERSE_CLAIM,
        payload: res.data.success,
      });
    } else {
      dispatch({
        type: actionTypes.REVERSE_CLAIM_ERROR,
        payload: res.data.msg,
      });
    }
  } catch (error) {
    console.log(error);
  }
};

export const partialReversal = (lineItem) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_REVERSAL,
  });
  try {
    const res = await axios.post(`${api_url_visit}lineItemReversal`, lineItem);
    console.log(res);
    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);

      const logObject = {
        action: "REVERSAL",
        user: lineItem.user,
        organisation: lineItem.organisation,
        reason: lineItem.reason,
        memberNumber: lineItem.memberNumber,
        type: "BENEFIT_UPDATE",
      };

      const responseresult = await axios.post(
        `${api_url_auditlog}/save`,
        logObject
      );
      dispatch({
        type: actionTypes.REVERSE_CLAIM,
        payload: res.data.success,
      });
      // if (responseresult.data.success === true) {

      // }
    } else {
      dispatch({
        type: actionTypes.REVERSE_CLAIM_ERROR,
        payload: res.data.msg,
      });
    }
  } catch (error) {
    console.log(error);
  }
};
