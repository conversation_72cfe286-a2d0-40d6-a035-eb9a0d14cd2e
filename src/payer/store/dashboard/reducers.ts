import * as actionTypes from "./actionTypes";

const initialState = {
  notifications: [],
  tasks: [],
  success: null,
  loading: false,
  dashboardItem: {},
  msg: null,
  taskId: "",
  categoryId: "",
  policyId: "",
  schemes: [],
  filteredProviderTransactions: [],
  filteredReimbursementsTransactions: [],
  filteredOffLctTransactions: [],
  userObj: {},
  claims: [],
  loadingClaims: false,
  loadingReversal: false,
  claimReversed: false,
  loadingReports: false,
  invoiceLines: [],
};

export const dashboardReducer = (state = initialState, action) => {
  switch (action.type) {
    case actionTypes.SET_LOADING_DASHBOARD:
      return {
        ...state,
        loading: true,
        success: null,
      };
    case actionTypes.SET_LOADING_CLAIMS:
      return {
        ...state,
        loadingClaims: true,
        success: null,
        claimReversed: null,
      };
    case actionTypes.SET_LOADING_REVERSAL:
      return {
        ...state,
        loadingReversal: true,
        success: null,
        claimReversed: null,
      };
    case actionTypes.SET_LOADING_LINES:
      return {
        ...state,
        invoiceLines: [],
      };
    case actionTypes.SET_DASHBOARD_NOTIFICATIONS: {
      const newState = {
        loading: false,
        success: true,
        notifications: action.payload,
      };
      return { ...state, ...newState };
    }
    case actionTypes.SET_TASKID: {
      const newState = {
        taskId: action.payload,
      };
      return { ...state, ...newState };
    }
    case actionTypes.SET_CATEGORYIDPOLICYID: {
      const newState = {
        categoryId: action.payload.categoryId,
        policyId: action.payload.policyId,
      };
      return { ...state, ...newState };
    }
    case actionTypes.ADD_DASHBOARD_ERROR: {
      const newState = {
        msg: action.payload,
        loading: false,
        success: false,
      };
      return { ...state, ...newState };
    }

    case actionTypes.ADD_DASHBOARD_SUCCESS: {
      const newState = {
        success: action.payload,
        loading: false,
      };
      return { ...state, ...newState };
    }
    case actionTypes.GET_DASHBOARD_TASKS: {
      return {
        ...state,
        tasks: action.payload,
      };
    }
    case actionTypes.GET_DASHBOARDS: {
      return {
        ...state,
        payers: action.payload,
        loading: false,
        success: true,
      };
    }
    case actionTypes.GET_SCHEMES:
      return {
        ...state,
        schemes: action.payload,
      };
    case actionTypes.GET_INVOICE_LINES:
      return {
        ...state,
        invoiceLines: action.payload,
      };
    case actionTypes.GET_PROVIDER_TRANSACTIONS:
      return {
        ...state,
        filteredProviderTransactions: action.payload,
        loadingReports: false,
      };
    case actionTypes.GET_PLAN_REIMBURSEMENTS:
      return {
        ...state,
        filteredReimbursementsTransactions: action.payload,
        loadingReports: false,
      };
    case actionTypes.GET_PLAN_OFFLCTS:
      return {
        ...state,
        filteredOffLctTransactions: action.payload,
        loadingReports: false,
      };
    case actionTypes.SEARCH_CLAIM:
      return {
        ...state,
        loadingClaims: false,
        claims: action.payload,
      };
    case actionTypes.REVERSE_CLAIM:
      return {
        ...state,
        loadingReversal: false,
        claimReversed: action.payload,
      };
    case actionTypes.REVERSE_CLAIM_ERROR:
      return {
        ...state,
        loadingReversal: false,
        claimReversed: false,
      };
    case actionTypes.SET_LOADING_REPORTS:
      return {
        ...state,
        loadingReports: true,
        success: null,
      };

    default:
      return state;
  }
};
