import * as actionTypes from "./actionTypes";

const initialState = {
  payers: [],
  success: null,
  loading: false,
  payerItem: {},
  selectedBenefit: {},
  selectedPayer: {},
  payerMappedBenefits: [],
  payerMappedProviders: [],
  totalElements: null,
  totalPages: null,
  pageNumber: null,
  userObj: {},
  payer: {},
  totalPayerMappedElements: null,
  totalPayerMappedPages: null,
  pagePayerMappedNumber: null,
  msg: null,
};

export const payerReducer = (state = initialState, action) => {
  switch (action.type) {
    case actionTypes.SET_LOADING_PAYER:
      return {
        ...state,
        loading: true,
        success: null,
      };
    case actionTypes.ADD_PAYER: {
      const newState = {
        loading: false,
        success: true,
      };
      return { ...state, ...newState };
    }
    case actionTypes.ADD_PAYER_ERROR: {
      const newState = {
        msg: action.payload,
        loading: false,
        success: false,
      };
      return { ...state, ...newState };
    }
    case actionTypes.MAP_BENEFIT: {
      const newpayers = [...state.payers, action.payload];
      const newState = {
        payers: newpayers,
        loading: false,
        success: true,
      };
      return { ...state, ...newState };
    }
    case actionTypes.ADD_PAYER_SUCCESS: {
      const newState = {
        success: action.payload,
        loading: false,
      };
      return { ...state, ...newState };
    }
    case actionTypes.GET_PAYERS: {
      return {
        ...state,
        payers: action.payload,
        loading: false,
        success: true,
      };
    }
    case actionTypes.GET_PAYER: {
      return {
        ...state,
        payer: action.payload,
        loading: false,
        success: true,
      };
    }
    case actionTypes.GET_PAYER_MAPPED_BENEFITS: {
      return {
        ...state,
        loading: false,
        success: true,
        payerMappedBenefits: action.payload,
      };
    }
    case actionTypes.GET_PAYER_MAPPED_PROVIDERS: {
      return {
        ...state,
        loading: false,
        success: true,
        payerMappedProviders: action.payload.content,
        totalPayerMappedElements: action.payload.totalElements,
        totalPayerMappedPages: action.payload.totalPages,
        pagePayerMappedNumber: action.payload.pageable.pageNumber,
      };
    }
    case actionTypes.ADD_PAYER_LOADING: {
      const newState = {
        loading: true,
        success: false,
      };
      return { ...state, ...newState };
    }
    case actionTypes.SHOW_PAYER: {
      const newState = {
        payerItem: action.payload,
      };
      return { ...state, ...newState };
    }
    case actionTypes.SET_SELECTED_BENEFIT: {
      const newState = {
        selectedBenefit: action.payload,
      };
      return { ...state, ...newState };
    }
    case actionTypes.SET_SELECTED_PAYER: {
      const newState = {
        selectedPayer: action.payload,
      };
      return { ...state, ...newState };
    }
    case actionTypes.SET_USER_DETAILS:
      return {
        ...state,
        userObj: action.payload,
      };
    default:
      return state;
  }
};
