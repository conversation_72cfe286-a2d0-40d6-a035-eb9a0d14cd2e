import * as actionTypes from "./actionTypes";
import axios from "axios";
import { api_url, api_url_Catalog } from "../../lib/Utils";

// Set loading to true
export const setLoading = () => {
  return {
    type: actionTypes.SET_LOADING_PAYER,
  };
};
export const addPayer = (payer) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_PAYER,
  });
  try {
    const res = await axios.post(`${api_url}/payers`, payer);
    console.log(res);
    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);
      dispatch({
        type: actionTypes.ADD_PAYER,
        payload: res.data.data,
      });
    } else {
      dispatch({
        type: actionTypes.ADD_PAYER_ERROR,
        payload: res.data.msg,
      });
    }
  } catch (error) {
  } finally {
  }
};

export const mapBenefit = (benefit) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_PAYER,
  });
  try {
    const res = await axios.post(`${api_url}/payers/mapping/benefits`, benefit);
    console.log(res);
    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);

      // dispatch({
      //   type: actionTypes.MAP_BENEFIT,
      //   payload: res.data.data,
      // });
    } else {
      console.log(res);
    }
  } catch (error) {
  } finally {
  }
};
export const mapProvider = (provider) => async (dispatch) => {
  setLoading();
  try {
    const res = await axios.post(
      `${api_url}/payers/mapping/providers`,
      provider
    );
    console.log(res);
    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);

      // dispatch({
      //   type: actionTypes.MAP_BENEFIT,
      //   payload: res.data.data,
      // });
    } else {
      console.log(res);
    }
  } catch (error) {
  } finally {
  }
};

export const getPayerMappedBenefits = (payerID) => async (dispatch) => {
  setLoading();
  try {
    const res = await axios.get(
      `${api_url}/payers/${payerID}/mapping/benefits`
    );
    console.log(res.data.data);

    if (res.data.success == true) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_PAYER_MAPPED_BENEFITS,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};

export const getPayerMappedProviders =
  (payerID, offSetValue) => async (dispatch) => {
    dispatch({
      type: actionTypes.SET_LOADING_PAYER,
    });
    const offset = offSetValue ? offSetValue : "1";
    try {
      const res = await axios.get(
        `${api_url}/payers/${payerID}/mapping/providers?page=${offset}&size=10`
      );
      console.log(res.data.data);

      if (res.data.success == true) {
        console.log(res);

        dispatch({
          type: actionTypes.GET_PAYER_MAPPED_PROVIDERS,
          payload: res.data.data,
        });
      } else {
        console.log(res);
      }
    } catch (error) {}
  };

export const getPayers = () => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_PAYER,
  });
  try {
    const res = await axios.get(`${api_url}/payers`);
    console.log(res.data.data);

    if (res.data.success == true) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_PAYERS,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};
export const getPayer = (payerID) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_PAYER,
  });
  try {
    const res = await axios.get(`${api_url}/payers/${payerID}/payer`);
    console.log(res.data.data);

    if (res.data.success == true) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_PAYER,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};
export const getPayersByType = (type) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_PAYER,
  });
  try {
    const res = await axios.get(`${api_url}/payers/type/${type}`);
    console.log(res.data.data);

    if (res.data.success == true) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_PAYERS,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};

export const showPayer = (payer) => async (dispatch) => {
  dispatch({
    type: actionTypes.SHOW_PAYER,
    payload: payer,
  });
};
export const setSelectedBenefit = (benefit) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_SELECTED_BENEFIT,
    payload: benefit,
  });
};
export const setSelectedPayer = (payer) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_SELECTED_PAYER,
    payload: payer,
  });
};

export const setUserDetails = (user) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_USER_DETAILS,
    payload: user,
  });
};
