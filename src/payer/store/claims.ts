import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";

interface ClaimsState {
  activeMember: number | undefined;
}

const initialState = { activeMember: undefined } as ClaimsState;

const claimsSlice = createSlice({
  name: "claims",
  initialState,
  reducers: {
    selectMember(state, action: PayloadAction<number>) {
      state.activeMember = action.payload;
    },
    clearMember(state) {
      state.activeMember = undefined;
    },
  },
});

export const { selectMember, clearMember } = claimsSlice.actions;
export default claimsSlice.reducer;
