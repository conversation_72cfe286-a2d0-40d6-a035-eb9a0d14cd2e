import { configureStore } from "@reduxjs/toolkit";
import { setupListeners } from "@reduxjs/toolkit/query";
import { combineReducers } from "redux";
import { api } from "~lib/api";
import authReducer from "~lib/store/authSlice";
import { claimVettingApi } from "../api/claims-vetting-api/claimVettingApi";
import { claimsApi } from "../api/features/claimsApi";
import { membershipApi } from "../api/features/membershipApi";
import userGroupsReducer from "../api/features/role-management/user-groups-slice";
import { reconciliationApi } from "../api/reconciliation/reconciliationAPI";
import { remittanceApi } from "../api/remittance/remittanceApi";
import { settlementApi } from "../api/settlement/settlementApi";
import aiAdjudicationReducer from "../features/ai/aiAdjudicationSlice";
import aiVettingReducer from "../features/ai/aiVettingSlice";
import preAuthReducer from "../features/care/preAuthSlice";
import claimsBatchingReducer from "../features/claims/claimsBatchingSlice";
import claimsReversalReducer from "../features/claims/claimsReversalSlice";
import voucherEditingReducer from "../features/claims/voucherEditingSlice";
import voucheringAllocationReducer from "../features/claims/voucheringAllocationSlice";
import voucheringReducer from "../features/claims/voucheringSlice";
import filterSelectReducer from "../features/filters/filterSelectSlice";
import signOffReducer from "../features/finance/signoff/signOffSlice";
import { catalogReducer } from "./catalog/reducers";
import claimsReducer from "./claims";
import { countryReducer } from "./countries/reducers";
import { dashboardReducer } from "./dashboard/reducers";
import { memberSearchReducer } from "./members/reducers";
import { payerReducer } from "./payers/reducers";
import { policyCategoryReducer } from "./policies/policyCategory/reducers";
import { policyReducer } from "./policies/reducers";
import { providerReducer } from "./providers/reducers";
import schemeReducer from "./schemes/reducers";
import accessControlReducer from "../features/access-control/accessControlSlice";

export const rootReducer = combineReducers({
  payers: payerReducer,
  providers: providerReducer,
  schemes: schemeReducer,
  policy: policyReducer,
  policyCategory: policyCategoryReducer,
  location: countryReducer,
  catalog: catalogReducer,
  dashboard: dashboardReducer,
  memberInfo: memberSearchReducer,
  claims: claimsReducer,
  claimsBatching: claimsBatchingReducer,
  claimsReversal: claimsReversalReducer,
  voucheringAllocation: voucheringAllocationReducer,
  vouchering: voucheringReducer,
  filterSelect: filterSelectReducer,
  signOff: signOffReducer,
  preAuth: preAuthReducer,
  voucherEditing: voucherEditingReducer,
  aiVetting: aiVettingReducer,
  aiAdjudication: aiAdjudicationReducer,
  accessControl: accessControlReducer,
  userGroups: userGroupsReducer,
  [api.reducerPath]: api.reducer,
  [claimVettingApi.reducerPath]: claimVettingApi.reducer,
  [settlementApi.reducerPath]: settlementApi.reducer,
  auth: authReducer,
  [claimsApi.reducerPath]: claimsApi.reducer,
  [membershipApi.reducerPath]: membershipApi.reducer,
  [reconciliationApi.reducerPath]: reconciliationApi.reducer,
  [remittanceApi.reducerPath]: remittanceApi.reducer,
});

// Applies thunk middleware automatically
export const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      api.middleware,
      claimsApi.middleware,
      membershipApi.middleware,
      claimVettingApi.middleware,
      settlementApi.middleware,
      reconciliationApi.middleware,
      remittanceApi.middleware,
    ),
});

setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
