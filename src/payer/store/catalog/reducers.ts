import * as actionTypes from "./actionTypes";
import { PayerAction, PayerState } from "./types";

const initialState = {
  benefitCatalog: [],
  catalogProviders: [],
  success: null,
  loading: false,
  catalogItem: {},
  totalElements: null,
  totalPages: null,
  pageNumber: null,
};

export const catalogReducer = (state = initialState, action) => {
  switch (action.type) {
    case actionTypes.SET_LOADING_CATALOG:
      return {
        ...state,
        loading: true,
        success: null,
      };
    case actionTypes.ADD_BENEFIT_TO_CATALOG: {
      const newState = {
        loading: false,
        success: true,
      };
      return { ...state, ...newState };
    }
    case actionTypes.WHITELIST_BENEFIT_PROVIDER: {
      const newState = {
        loading: false,
        success: true,
      };
      return { ...state, ...newState };
    }
    case actionTypes.MAP_BENEFIT: {
      const newbenefitCatalog = [...state.benefitCatalog, action.payload];
      const newState = {
        benefitCatalog: newbenefitCatalog,
        loading: false,
        success: true,
      };
      return { ...state, ...newState };
    }
    case actionTypes.ADD_CATALOG_SUCCESS: {
      const newState = {
        success: action.payload,
        loading: false,
      };
      return { ...state, ...newState };
    }
    case actionTypes.GET_CATALOG: {
      return {
        ...state,
        benefitCatalog: action.payload,
        loading: false,
        success: true,
      };
    }
    case actionTypes.GET_CATALOG_PROVIDERS_LIST: {
      return {
        ...state,
        catalogProviders: action.payload,
        loading: false,
        success: true,
      };
    }
    case actionTypes.GET_CATALOG_MAPPED_BENEFITS: {
      return {
        ...state,
        loading: false,
        success: true,
        payerMappedBenefits: action.payload,
      };
    }
    case actionTypes.GET_CATALOG_MAPPED_PROVIDERS: {
      return {
        ...state,
        loading: false,
        success: true,
        payerMappedProviders: action.payload,
      };
    }
    case actionTypes.ADD_CATALOG_LOADING: {
      const newState = {
        loading: true,
        success: false,
      };
      return { ...state, ...newState };
    }
    case actionTypes.SHOW_CATALOG: {
      const newState = {
        payerItem: action.payload,
      };
      return { ...state, ...newState };
    }
    case actionTypes.SET_SELECTED_BENEFIT: {
      const newState = {
        selectedBenefit: action.payload,
      };
      return { ...state, ...newState };
    }
    case actionTypes.SET_SELECTED_CATALOG: {
      const newState = {
        selectedPayer: action.payload,
      };
      return { ...state, ...newState };
    }
    default:
      return state;
  }
};
