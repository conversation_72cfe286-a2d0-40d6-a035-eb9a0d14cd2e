import * as actionTypes from "./actionTypes";
import axios from "axios";
import { api_url, api_url_Catalog, api_url_provider } from "../../lib/Utils";

// Set loading to true
export const setLoading = () => {
  return {
    type: actionTypes.SET_LOADING_CATALOG,
  };
};
export const addBenefitToCatalog = (payer) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_CATALOG,
  });
  try {
    const res = await axios.post(`${api_url_Catalog}/benefit`, payer);
    console.log(res);
    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);
      dispatch({
        type: actionTypes.ADD_BENEFIT_TO_CATALOG,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {
  } finally {
  }
};
export const whitelistBenefitProvider =
  (benefitProviderObject) => async (dispatch) => {
    dispatch({
      type: actionTypes.SET_LOADING_CATALOG,
    });
    try {
      const res = await axios.post(
        `${api_url_provider}/whitelist`,
        benefitProviderObject
      );
      console.log(res);
      console.log(res.data.data);

      if (res.data.success === true) {
        console.log(res.data.success);
        dispatch({
          type: actionTypes.WHITELIST_BENEFIT_PROVIDER,
          payload: res.data.data,
        });
      } else {
        console.log(res);
      }
    } catch (error) {
    } finally {
    }
  };
export const getCatalogProviders = () => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_CATALOG,
  });
  try {
    const res = await axios.get(`${api_url_provider}all?page=1&size=10`);
    console.log(res.data.data);

    if (res.data.success == true) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_CATALOG_PROVIDERS_LIST,
        payload: res.data.data.content,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};

export const getBenefitCatalog = (searchTerm) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_CATALOG,
  });
  try {
    const res = await axios.get(
      `${api_url_Catalog}/benefit?search=${searchTerm}&page=1&size=10`
    );
    /////console.log(res.data.data);

    if (res.data.success == true) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_CATALOG,
        payload: res.data.data.content,
      });
    } else {
      ///console.log(res);
    }
  } catch (error) {}
};

export const getBenefitCatalog2 = () => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_CATALOG,
  });
  try {
    const res = await axios.get(`${api_url_Catalog}/all?page=1&size=10`);
    //console.log(res.data.data);

    if (res.data.success == true) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_CATALOG,
        payload: res.data.data.content,
      });
    } else {
      ////console.log(res);
    }
  } catch (error) {
    ///console.log("", error.response);
  }
};

export const showCatalog = (benefitCatalogItem) => async (dispatch) => {
  dispatch({
    type: actionTypes.SHOW_CATALOG,
    payload: benefitCatalogItem,
  });
};
// export const setSelectedBenefit = (benefit) => async (dispatch) => {
//   dispatch({
//     type: actionTypes.SET_SELECTED_BENEFIT,
//     payload: benefit,
//   });
// };
// export const setSelectedPayer = (payer) => async (dispatch) => {
//   dispatch({
//     type: actionTypes.SET_SELECTED_PAYER,
//     payload: payer,
//   });
// };
