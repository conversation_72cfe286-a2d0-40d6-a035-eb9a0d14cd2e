import * as actionTypes from "./actionTypes";

const initialState = {
  schemes: [],
  schemeAdmins: [],
  schemePolicies: [],
  success: null,
  loading: false,
  totalElements: null,
  totalPages: null,
  pageNumber: null,
  selectedScheme: {},
};

const schemeReducer = (state = initialState, action) => {
  switch (action.type) {
    case actionTypes.SET_LOADING_SCHEME:
      return {
        ...state,
        loading: true,
        success: null,
      };
    case actionTypes.SHOW_SCHEME_POLICIES:
      return {
        ...state,
        selectedScheme: action.payload,
      };
    case actionTypes.ADD_SCHEME: {
      return { ...state, success: action.payload, loading: false };
    }
    case actionTypes.GET_SCHEMES: {
      const newState = {
        schemes: action.payload.content,
        loading: false,
        totalElements: action.payload.totalElements,
        totalPages: action.payload.totalPages,
        pageNumber: action.payload.pageable.pageNumber,
      };
      return { ...state, ...newState };
    }

    case actionTypes.GET_POLICIES_IN_SCHEME: {
      const newState = {
        schemePolicies: action.payload,
        loading: false,
      };
      return { ...state, ...newState };
    }

    case actionTypes.GET_SCHEME_ADMINS: {
      const newState = {
        schemeAdmins: action.payload,
        loading: false,
      };
      return { ...state, ...newState };
    }

    default:
      return state;
  }
};

export default schemeReducer;
