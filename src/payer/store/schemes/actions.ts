import * as actionTypes from "./actionTypes";
import axios from "axios";
import { api_url, api_url_apply_task } from "../../lib/Utils";
import { IScheme } from "./types";
import UserService from "../../services/UserService";
// Set loading to true
export const setLoading = () => {
  return {
    type: actionTypes.SET_LOADING_SCHEME,
  };
};
export const addScheme = (scheme: IScheme) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_SCHEME,
  });
  try {
    const res = await axios.post(`${api_url}/plans`, scheme);
    console.log(res);
    if (res.data.success === true) {
      console.log(res.data.success);
      dispatch({
        type: actionTypes.ADD_SCHEME,
        payload: res.data.success,
      });

      const data = res.data.data;
      const { name, type, accessMode } = data;

      const schemeObject = {
        schemeName: name,
        schemeType: type,
        accessMode: accessMode,
        to: UserService.getSubject(),
        from: "backend",
        requestType: "SCHEME_UPDATE",
        linkId: "/policies/addPolicy/",
      };

      // call ticketing
      // let response = await axios.post(
      //   `${api_url_apply_task}schemeCreated/apply`,
      //   schemeObject,
      //   config
      // );
      // if (response.data.status === 200) {
      //   console.log("scheme ticket success");
      // }
    } else {
      console.log(res);
    }
  } catch (error) {
    // ;
  }
};

export const getSchemes = (offSetValue) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_SCHEME,
  });
  const offset = offSetValue ? offSetValue : "1";
  try {
    const res = await axios.get(`${api_url}/plans?page=${offset}&size=10`);
    console.log(res.data.data);

    if (res.data.success == true) {
      console.log(res.data.data);

      dispatch({
        type: actionTypes.GET_SCHEMES,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {
    console.log("ERROR", error.response);
  }
};

export const getSchemesAdmins = () => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_SCHEME,
  });

  try {
    const res = await axios.get(`${api_url}/administrators`);
    console.log(res.data.data);

    if (res.data.success == true) {
      console.log(res.data.data);

      dispatch({
        type: actionTypes.GET_SCHEME_ADMINS,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {
    console.log("ERROR", error.response);
  }
};

//
export const getPoliciesInSchemes = (planId) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_SCHEME,
  });

  try {
    const res = await axios.get(`${api_url}/plan/${planId}/policies`);
    console.log(res.data.data);

    if (res.data.success == true) {
      console.log(res.data.data);

      dispatch({
        type: actionTypes.GET_POLICIES_IN_SCHEME,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {
    console.log("ERROR", error.response);
  }
};

// show Provider
export const showSchemePolicies = (schemeObj) => async (dispatch) => {
  dispatch({
    type: actionTypes.SHOW_SCHEME_POLICIES,
    payload: schemeObj,
  });
};
