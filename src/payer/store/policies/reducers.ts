import * as actionTypes from "./actionTypes";

const initialState = {
  policies: [],
  policyCategories: [],
  success: null,
  successUpload: null,
  loading: false,
  loadingUpload: false,
  selectedPolicy: {},
  policyItem: {},
  msg: null,
};

export const policyReducer = (state = initialState, action) => {
  switch (action.type) {
    case actionTypes.SET_LOADING_POLICY:
      return {
        ...state,
        loading: true,
        success: null,
      };
    case actionTypes.SET_LOADING_MEMBERUPLOAD:
      return {
        ...state,
        loadingUpload: true,
        success: null,
      };
    case actionTypes.ADD_POLICY_SUCCESS: {
      const newpolicies = [...state.policies, ...state.policies];
      const newState = {
        success: action.payload,
        loading: false,
        policies: newpolicies,
      };
      return { ...state, ...newState };
    }
    case actionTypes.ADD_POLICY_ERROR: {
      const newState = {
        msg: action.payload,
        loading: false,
        success: false,
      };
      return { ...state, ...newState };
    }

    case actionTypes.GET_POLICIES: {
      return {
        ...state,
        policies: action.payload,
        loading: false,
        success: true,
      };
    }
    case actionTypes.FILE_MEMBERUPLOAD_SUCCESS: {
      return {
        ...state,
        loadingUpload: false,
        successUpload: true,
      };
    }
    case actionTypes.FILE_MEMBERUPLOAD_ERROR: {
      return {
        ...state,
        loadingUpload: false,
        successUpload: false,
      };
    }

    case actionTypes.GET_MAIN_POLICIES_LIST: {
      return {
        ...state,
        policies: action.payload,
        loading: false,
        success: true,
      };
    }
    case actionTypes.GET_POLICY_OVERVIEW: {
      return {
        ...state,
        policyItem: action.payload,
        loading: false,
        success: true,
      };
    }
    case actionTypes.GET_POLICY_CATEGORIES: {
      return {
        ...state,
        policyCategories: action.payload,
        loading: false,
        success: true,
      };
    }
    case actionTypes.POST_POLICY_CATEGORY_SUCCESS: {
      const newState = {
        loading: false,
        success: true,
      };
      return { ...state, ...newState };
    }
    case actionTypes.POLICY_LOADING: {
      const newState = {
        loading: true,
        success: "",
      };
      return { ...state, ...newState };
    }
    case actionTypes.POST_CATEGORY: {
      const newState = {
        loading: false,
        success: true,
      };
      return { ...state, ...newState };
    }

    case actionTypes.POST_CATEGORY_FAIL: {
      const newState = {
        loading: false,
        success: false,
        msg: action.payload,
      };
      return { ...state, ...newState };
    }
    case actionTypes.SHOW_POLICY: {
      const newState = {
        selectedPolicy: action.payload,
      };
      return { ...state, ...newState };
    }
    case actionTypes.SET_SELECTED_POLICY: {
      const newState = {
        selectedPolicy: action.payload,
      };
      return { ...state, ...newState };
    }
    default:
      return state;
  }
};
