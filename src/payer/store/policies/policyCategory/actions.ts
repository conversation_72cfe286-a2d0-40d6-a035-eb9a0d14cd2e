import * as actionTypes from "./actionTypes";
import axios from "axios";
import {
  api_url,
  api_url_apply_task,
  api_url_provider,
} from "../../../lib/Utils";
import { IPolicy } from "./types";
import UserService from "../../../services/UserService";

export const addPolicyCategoryBenefit = (benefit) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_POLICY_CATEGORY_LOADING,
  });
  try {
    const res = await axios.post(`${api_url}/benefits`, benefit);
    console.log(res);

    if (res.data.success === true) {
      console.log(res.data.success);

      dispatch({
        type: actionTypes.ADD_POLICY_CATEGORY_BENEFIT,
        payload: res.data.success,
      });

      if (benefit.ticketingObj.taskId) {
        const policyObject = {
          policyId: benefit.ticketingObj.policyId,
          categoryId: benefit.ticketingObj.categoryId,
          to: UserService.getSubject(),
          from: "backend",
          requestType: "BENEFITS_UPDATE",
          linkId: "/policies/overview/addMembers",
        };

        // // call ticketing
        // let response = await axios.post(
        //   `${api_url_apply_task}benefitsCreated/apply/${benefit.ticketingObj.taskId}`,
        //   policyObject
        // );
        // if (response.data.status === 200) {
        //   console.log("Benefit ticket success");
        // }
      }
    } else {
      console.log(res);
    }
  } catch (error) {}
};

export const addPolicyCategoryMembers = (member) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_POLICY_CATEGORY_LOADING,
  });
  try {
    const res = await axios.post(`${api_url}/beneficiaries`, member);
    console.log(res);

    if (res.data.success === true) {
      console.log(res.data.success);

      dispatch({
        type: actionTypes.ADD_POLICY_CATEGORY_MEMBERS,
        payload: res.data.success,
      });

      if (member.ticketingObj.taskId) {
        const policyObject = {
          policyId: member.ticketingObj.policyId,
          categoryId: member.ticketingObj.categoryId,
          to: UserService.getSubject(),
          from: "backend",
          requestType: "MEMBER_UPDATE",
          linkId: "/policies/overview/livenPolicy",
        };

        // // call ticketing
        // let response = await axios.post(
        //   `${api_url_apply_task}membersCreated/apply/${member.ticketingObj.taskId}`,
        //   policyObject
        // );
        // if (response.data.status === 200) {
        //   console.log("members ticket success");
        // }
      }
    } else {
      console.log(res);
    }
  } catch (error) {}
};

export const processCategoryBenefits = (obj) => async (dispatch) => {
  console.log(obj);
  dispatch({
    type: actionTypes.SET_PROCESS_BENEFIT_LOADING,
  });
  const processObj = {
    categoryId:obj.processCategoryId,
    processBeneficiaries:"ALL"
  }
  try {
    const res = await axios.post(
      `${api_url}/category/process`,processObj
    );
    console.log(res);

    if (res.data.success === true) {
      console.log(res.data.success);

      dispatch({
        type: actionTypes.PROCESS_CATEGORY_BENEFITS,
        payload: res.data.success,
      });

    } else {
      console.log(res);
    }
  } catch (error) {
    console.log(error);
    dispatch({
      type: actionTypes.PROCESS_CATEGORY_BENEFITS_FAIL,
      payload: "Processing Benefits Failed",
    });
  }
};

export const addPolicyCategoryProviderExclusion =
  (policy) => async (dispatch) => {
    dispatch({
      type: actionTypes.SET_POLICY_CATEGORY_LOADING,
    });
    try {
      const res = await axios.post(`${api_url_provider}/exclusion`, policy);
      console.log(res);

      if (res.data.success === true) {
        console.log(res.data.success);

        dispatch({
          type: actionTypes.ADD_POLICY_CATEGORY_EXCLUSION,
          payload: res.data.success,
        });
      } else {
        console.log(res);
      }
    } catch (error) {}
  };
export const addPolicyCategoryCopayment = (policy) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_POLICY_CATEGORY_LOADING,
  });
  try {
    const res = await axios.post(`${api_url_provider}/copay`, policy);
    console.log(res);

    if (res.data.success === true) {
      console.log(res.data.success);

      dispatch({
        type: actionTypes.ADD_POLICY_CATEGORY_COPAYMENT,
        payload: res.data.success,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};

export const getPolicyCategoryBenefit =
  (categoryId, offSetValue) => async (dispatch) => {
    const offset = offSetValue ? offSetValue : "1";

    try {
      const res = await axios.get(
        `${api_url}/category/${categoryId}/benefits?page=${offset}&size=10`
      );
      console.log(res.data.data);

      if (res.data.success == true) {
        console.log(res);

        dispatch({
          type: actionTypes.GET_POLICY_CATEGORY_BENEFIT,
          payload: res.data.data,
        });
      } else {
        console.log(res);
      }
    } catch (error) {
      console.log("ERROR", error.response);
    }
  };

export const getPolicyCategoryMembers =
  (categoryId, offSetValue) => async (dispatch) => {
    const offset = offSetValue ? offSetValue : "1";
    try {
      const res = await axios.get(
        `${api_url}/category/${categoryId}/beneficiaries?page=${offset}&size=10`
      );
      console.log(res.data.data);

      if (res.data.success == true) {
        console.log(res);

        dispatch({
          type: actionTypes.GET_POLICY_CATEGORY_MEMBERS,
          payload: res.data.data,
        });
      } else {
        console.log(res);
      }
    } catch (error) {
      console.log("ERROR", error.response);
    }
  };

export const getCategoryMainBenefits = (categoryId) => async (dispatch) => {
  //const offset = offSetValue ? offSetValue : "1";
  try {
    const res = await axios.get(
      `${api_url}/category/${categoryId}/benefits/main`
    );
    console.log(res.data.data);

    if (res.data.success == true) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_POLICY_CATEGORY_MAIN_BENEFITS,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {
    console.log("ERROR", error.response);
  }
};

export const getPolicyCategoryPrincipalMembers =
  (categoryId) => async (dispatch) => {
    //const offset = offSetValue ? offSetValue : "1";
    try {
      const res = await axios.get(
        `${api_url}/category/${categoryId}/principals`
      );
      console.log(res.data.data);

      if (res.data.success == true) {
        console.log(res);

        dispatch({
          type: actionTypes.GET_POLICY_CATEGORY_MEMBERS_PRINCIPALS,
          payload: res.data.data,
        });
      } else {
        console.log(res);
      }
    } catch (error) {
      console.log("ERROR", error.response);
    }
  };

export const getPolicyCategoryProviderExclusion =
  (categoryId, offSetValue) => async (dispatch) => {
    const offset = offSetValue ? offSetValue : "1";
    try {
      const res = await axios.get(
        `${api_url_provider}/category/${categoryId}/exclusions?page=${offset}&size=10`
      );
      console.log(res.data.data);

      if (res.data.success == true) {
        console.log(res);

        dispatch({
          type: actionTypes.GET_POLICY_CATEGORY_PROVIDER_EXCLUSION,
          payload: res.data.data,
        });
      } else {
        console.log(res);
      }
    } catch (error) {
      console.log("ERROR", error.response);
    }
  };

export const getPolicyCategoryCopayments =
  (categoryId, offSetValue) => async (dispatch) => {
    const offset = offSetValue ? offSetValue : "1";
    try {
      const res = await axios.get(
        `${api_url_provider}/category/${categoryId}/copays?page=${offset}&size=10`
      );
      console.log(res.data.data);

      if (res.data.success == true) {
        console.log(res);

        dispatch({
          type: actionTypes.GET_POLICY_CATEGORY_COPAYMENTS,
          payload: res.data.data,
        });
      } else {
        console.log(res);
      }
    } catch (error) {
      console.log("ERROR", error.response);
    }
  };

export const editPolicy = (policy: IPolicy) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_POLICY_CATEGORY_LOADING,
  });
  try {
    const res = await axios.put(`${api_url}/policies`, policy);
    console.log(res);

    if (res.data.success === true) {
      console.log(res.data.success);

      dispatch({
        type: actionTypes.ADD_POLICY_SUCCESS,
        payload: res.data.success,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};
export const getPolicyCategoryProviderRestriction =
  (categoryId) => async (dispatch) => {
    ///const offset = offSetValue ? offSetValue : "1";
    try {
      const res = await axios.get(
        `${api_url_provider}/category/${categoryId}/restrictions`
      );
      console.log(res.data.data);

      if (res.data.success == true) {
        console.log(res);

        dispatch({
          type: actionTypes.GET_POLICY_CATEGORY_PROVIDER_RESTRICTION,
          payload: res.data.data,
        });
      } else {
        console.log(res);
      }
    } catch (error) {
      console.log("ERROR", error.response);
    }
  };

export const showPolicyCategoryPage = (policy) => async (dispatch) => {
  dispatch({
    type: actionTypes.SHOW_POLICY_CATEGORIES,
    payload: policy,
  });
};
