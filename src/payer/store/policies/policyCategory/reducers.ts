import * as actionTypes from "./actionTypes";

const initialState = {
  categoryMembers: [],
  categoryBenefits: [],
  categoryMainBenefits: [],
  categoryProviderExclusion: [],
  categoryCopayemnt: [],
  categoryPrincipals: [],
  success: "",
  processBenefitsSuccess: "",
  loading: false,
  processBenefitsloading: false,
  msg: "",
  policyCategory: {},

  totalBenefitElements: null,
  totalBenefitPages: null,
  pageBenefitNumber: null,

  totalMembersElements: null,
  totalMembersPages: null,
  pageMembersNumber: null,

  totalProvExclusionElements: null,
  totalProvExclusionPages: null,
  pageProvExclusionNumber: null,

  totalCopaymentElements: null,
  totalCopaymentPages: null,
  pageCopaymentNumber: null,
  categoryProviderRestrictions: [],
};

export const policyCategoryReducer = (state = initialState, action) => {
  switch (action.type) {
    case actionTypes.SET_POLICY_CATEGORY_LOADING:
      return {
        ...state,
        loading: true,
        success: "",
        categoryProviderRestrictions: [],
      };

    case actionTypes.GET_POLICY_CATEGORY_PROVIDER_RESTRICTION: {
      return {
        ...state,
        categoryProviderRestrictions: action.payload,
      };
    }

    case actionTypes.ADD_POLICY_SUCCESS: {
      const newpolicies = [];
      const newState = {
        success: action.payload,
        loading: false,
        policies: newpolicies,
      };
      return { ...state, ...newState };
    }
    case actionTypes.GET_POLICIES: {
      const newState = {
        policies: action.payload,
        loading: false,
        success: true,
      };
      return { ...state, ...newState };
    }
    case actionTypes.ADD_POLICY_CATEGORY_BENEFIT: {
      return {
        ...state,
        loading: false,
        success: action.payload,
      };
    }
    case actionTypes.ADD_POLICY_CATEGORY_MEMBERS: {
      return {
        ...state,
        loading: false,
        success: action.payload,
      };
    }

    case actionTypes.SET_PROCESS_BENEFIT_LOADING:
      return {
        ...state,
        processBenefitsloading: true,
        processBenefitsSuccess: "",
      };
    case actionTypes.PROCESS_CATEGORY_BENEFITS: {
      return {
        ...state,
        processBenefitsloading: false,
        processBenefitsSuccess: action.payload,
      };
    }

    case actionTypes.PROCESS_CATEGORY_BENEFITS_FAIL: {
      return {
        ...state,
        processBenefitsloading: false,
        processBenefitsSuccess: false,
      };
    }
    case actionTypes.ADD_POLICY_CATEGORY_EXCLUSION: {
      return {
        ...state,
        loading: false,
        success: action.payload,
      };
    }
    case actionTypes.ADD_POLICY_CATEGORY_COPAYMENT: {
      return {
        ...state,
        loading: false,
        success: action.payload,
      };
    }
    case actionTypes.GET_POLICY_CATEGORY_BENEFIT: {
      return {
        ...state,
        categoryBenefits: action.payload.content,
        loading: false,
        success: true,
        totalBenefitElements: action.payload.totalElements,
        totalBenefitPages: action.payload.totalPages,
        pageBenefitNumber: action.payload.pageable.pageNumber,
      };
    }
    case actionTypes.GET_POLICY_CATEGORY_MAIN_BENEFITS: {
      return {
        ...state,
        categoryMainBenefits: action.payload,
        loading: false,
        success: true,
      };
    }
    case actionTypes.GET_POLICY_CATEGORY_MEMBERS: {
      return {
        ...state,
        categoryMembers: action.payload.content,
        loading: false,
        success: true,

        totalMembersElements: action.payload.totalElements,
        totalMembersPages: action.payload.totalPages,
        pageMembersNumber: action.payload.pageable.pageNumber,
      };
    }
    case actionTypes.GET_POLICY_CATEGORY_MEMBERS_PRINCIPALS: {
      return {
        ...state,
        categoryPrincipals: action.payload,
        loading: false,
        success: true,
      };
    }

    case actionTypes.GET_POLICY_CATEGORY_PROVIDER_EXCLUSION: {
      return {
        ...state,
        categoryProviderExclusion: action.payload.content,
        loading: false,
        success: true,
        totalProvExclusionElements: action.payload.totalElements,
        totalProvExclusionPages: action.payload.totalPages,
        pageProvExclusionNumber: action.payload.pageable.pageNumber,
      };
    }
    case actionTypes.GET_POLICY_CATEGORY_COPAYMENTS: {
      return {
        ...state,
        categoryCopayemnt: action.payload,
        loading: false,
        success: true,

        // totalCopaymentElements: action.payload.totalElements,
        // totalCopaymentPages: action.payload.totalPages,
        // pageCopaymentNumber: action.payload.pageable.pageNumber,
      };
    }
    case actionTypes.POST_POLICY_CATEGORY_SUCCESS: {
      const newState = {
        loading: false,
        success: true,
      };
      return { ...state, ...newState };
    }

    case actionTypes.POST_CATEGORY: {
      const newState = {
        loading: false,
        success: true,
      };
      return { ...state, ...newState };
    }

    case actionTypes.POST_CATEGORY_FAIL: {
      const newState = {
        loading: false,
        success: false,
        msg: action.payload,
      };
      return { ...state, ...newState };
    }

    case actionTypes.SHOW_POLICY_CATEGORIES: {
      const newState = {
        policyCategory: action.payload,
      };
      return { ...state, ...newState };
    }
    default:
      return state;
  }
};
