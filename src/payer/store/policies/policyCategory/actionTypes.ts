export const ADD_POLICY = "ADD_POLICY";
export const ADD_POLICY_CATEGORY_EXCLUSION = "ADD_POLICY_CATEGORY_EXCLUSION";
export const ADD_POLICY_CATEGORY_COPAYMENT = "ADD_POLICY_CATEGORY_COPAYMENT";
export const SET_POLICY_CATEGORY_LOADING = "SET_POLICY_CATEGORY_LOADING";
export const SET_PROCESS_BENEFIT_LOADING = "SET_PROCESS_BENEFIT_LOADING";
export const GET_POLICIES = "GET_POLICIES";

export const GET_POLICY_CATEGORIES = "GET_POLICY_CATEGORIES";
export const GET_POLICY_CATEGORY_COPAYMENTS = "GET_POLICY_CATEGORY_COPAYMENTS";
export const SHOW_POLICY_CATEGORIES = "SHOW_POLICY_CATEGORIES";
export const GET_POLICY_CATEGORY_BENEFIT = "GET_POLICY_CATEGORY_BENEFIT";
export const ADD_POLICY_CATEGORY_BENEFIT = "ADD_POLICY_CATEGORY_BENEFIT";
export const ADD_POLICY_CATEGORY_MEMBERS = "ADD_POLICY_CATEGORY_MEMBERS";
export const GET_POLICY_CATEGORY_MEMBERS = "GET_POLICY_CATEGORY_MEMBERS";
export const GET_POLICY_CATEGORY_MAIN_BENEFITS =
  "GET_POLICY_CATEGORY_MAIN_BENEFITS";
export const GET_POLICY_CATEGORY_MEMBERS_PRINCIPALS =
  "GET_POLICY_CATEGORY_MEMBERS_PRINCIPALS";
export const GET_POLICY_CATEGORY_PROVIDER_EXCLUSION =
  "GET_POLICY_CATEGORY_PROVIDER_EXCLUSION";
export const POST_POLICY_CATEGORY_SUCCESS = "POST_POLICY_CATEGORY_SUCCESS";
export const GET_POLICY_OVERVIEW = "GET_POLICY_OVERVIEW";
export const POST_CATEGORY = "POST_CATEGORY";
export const POST_CATEGORY_FAIL = "POST_CATEGORY_FAIL";
export const ADD_POLICY_SUCCESS = "ADD_POLICY_SUCCESS";
export const ADD_POLICY_LOADING = "ADD_POLICY_LOADING";
export const ADD_POLICY_ERROR = "ADD_POLICY_ERROR";
export const EDIT_POLICY = "EDIT_POLICY";
export const REMOVE_POLICY = "REMOVE_POLICY";
export const PROCESS_CATEGORY_BENEFITS = "PROCESS_CATEGORY_BENEFITS";
export const PROCESS_CATEGORY_BENEFITS_FAIL = "PROCESS_CATEGORY_BENEFITS_FAIL";
export const GET_POLICY_CATEGORY_PROVIDER_RESTRICTION =
  "GET_POLICY_CATEGORY_PROVIDER_RESTRICTION";
