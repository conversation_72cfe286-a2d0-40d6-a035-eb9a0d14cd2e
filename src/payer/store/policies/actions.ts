import * as actionTypes from "./actionTypes";
import axios from "axios";
import { api_url, api_url_apply_task } from "../../lib/Utils";
import { IPolicy } from "./types";
import UserService from "../../services/UserService";

export const uploadMembersToPolicy =
  (excelFile, policyId) => async (dispatch) => {
    dispatch({
      type: actionTypes.SET_LOADING_MEMBERUPLOAD,
    });

    try {
      const formData = new FormData();
      formData.append("file", excelFile);
      console.log(excelFile);

      const res = await axios.post(
        `${api_url}/category/massupload?policyId=${policyId}`,
        formData
      );

      if (res.data.success === true) {
        console.log(res.data.success);

        dispatch({
          type: actionTypes.FILE_MEMBERUPLOAD_SUCCESS,
          payload: res.data.success,
        });
      } else {
        dispatch({
          type: actionTypes.FILE_MEMBERUPLOAD_ERROR,
          payload: res.data.msg,
        });
      }
    } catch (error) {}
  };

export const addPolicy = (policy) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_POLICY,
  });
  const config = {
    headers: {
      "Content-Type": "application/json",
      Accept: "*/*",
    },
  };
  try {
    const res = await axios.post(`${api_url}/policies`, policy);
    console.log(res);

    if (res.data.success === true) {
      console.log(res.data.success);

      dispatch({
        type: actionTypes.ADD_POLICY_SUCCESS,
        payload: res.data.success,
      });
      console.log(res.data);
      const data = res.data.data;
      const { id, policyNumber } = data;
      // console.log(name);

      const policyObject = {
        policyId: id,
        policyNumber: policyNumber,
        to: UserService.getSubject(),
        from: "backend",
        requestType: "POLICY_UPDATE",
        linkId: "/policies/overview/addCategory",
      };

      // // call ticketing
      // let response = await axios.post(
      //   `${api_url_apply_task}policyCreated/apply/${policy.taskId}`,
      //   policyObject,
      //   config
      // );
      // if (response.data.status === 200) {
      //   console.log("policy ticket success");
      // }
    } else {
      dispatch({
        type: actionTypes.ADD_POLICY_ERROR,
        payload: res.data.msg,
      });
    }
  } catch (error) {}
};

export const editPolicy = (policy: IPolicy) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_POLICY,
  });
  try {
    const res = await axios.put(`${api_url}/policies`, policy);
    console.log(res);

    if (res.data.success === true) {
      console.log(res.data.success);

      dispatch({
        type: actionTypes.ADD_POLICY_SUCCESS,
        payload: res.data.success,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};

export const getPolicies = () => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_POLICY,
  });
  try {
    const res = await axios.get(`${api_url}/policies`);

    if (res.data.success == true) {
      console.log(res.data.data);
      console.log("here");

      dispatch({
        type: actionTypes.GET_MAIN_POLICIES_LIST,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {
    console.log("ERROR", error.response);
  }
};

export const getPolicyOverview = (policyId) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_POLICY,
  });
  try {
    const res = await axios.get(`${api_url}/policies/${policyId}/policy`);
    console.log(res.data.data);

    if (res.data.success == true) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_POLICY_OVERVIEW,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {
    console.log("ERROR", error.response);
  }
};

export const getPolicyCategories = (policyId) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_POLICY,
  });
  try {
    const res = await axios.get(`${api_url}/policy/${policyId}/categories`);
    console.log(res.data.data);

    if (res.data.success == true) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_POLICY_CATEGORIES,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {
    console.log("ERROR", error.response);
  }
};

export const postCategories = (categoryObject) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOADING_POLICY,
  });
  console.log(categoryObject);
  try {
    const res = await axios.post(`${api_url}/categories`, categoryObject);

    const data = await res.data;

    console.log(data);

    if (data.success == true) {
      dispatch({
        type: actionTypes.POST_POLICY_CATEGORY_SUCCESS,
        payload: data.data,
      });

      const categoryTicketObject = {
        policyId: data.data[0].policy.id,
        categoryId: data.data[0].id,
        to: UserService.getSubject(),
        from: "backend",
        requestType: "CATEGORIES_UPDATE",
        linkId: "/policies/overview/addBenefits",
      };

      // // call ticketing
      // let response = await axios.post(
      //   `${api_url_apply_task}categoriesCreated/apply/${categoryObject.taskId}`,
      //   categoryTicketObject
      // );
      // if (response.data.status === 200) {
      //   console.log("category ticket success");
      // }
    } else {
      dispatch({
        type: actionTypes.POST_CATEGORY_FAIL,
        payload: data.msg,
      });
      console.log(data.msg);
    }

    // console.log(data.data[0].id);
    // console.log(data.data[0].policy.id);

    // Get categories
    dispatch(getPolicyCategories(categoryObject.policyId));
  } catch (error) {
    console.log("ERROR", error.response);
  }
};

export const setSelectedPolicy = (policy) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_SELECTED_POLICY,
    payload: policy,
  });
};
export const showPolicy = (policy) => async (dispatch) => {
  dispatch({
    type: actionTypes.SHOW_POLICY,
    payload: policy,
  });
};
