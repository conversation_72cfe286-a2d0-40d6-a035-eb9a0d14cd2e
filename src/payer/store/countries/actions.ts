import * as actionTypes from "./actionTypes";
import axios from "axios";
import { api_url, api_url_Catalog, api_url_country } from "../../lib/Utils";
import { IPayer } from "./types";

export const addCountry = (country) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOCATION_LOADING,
  });
  try {
    const res = await axios.post(`${api_url_country}`, country);
    console.log(res);
    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);

      dispatch({
        type: actionTypes.ADD_COUNTRY,
        payload: res.data.success,
      });
    } else {
      console.log(res);
    }
  } catch (error) {
  } finally {
  }
};
export const addRegion = (region) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_LOCATION_LOADING,
  });
  try {
    const res = await axios.post(`${api_url_country}region`, region);

    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);
      dispatch({
        type: actionTypes.ADD_REGION,
        payload: res.data.success,
      });
    } else {
      console.log(res);
    }
  } catch (error) {
  } finally {
  }
};

export const mapBenefit = (benefit) => async (dispatch) => {
  try {
    const res = await axios.post(`${api_url_Catalog}/benefit`, benefit);
    console.log(res);
    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);

      // dispatch({
      //   type: actionTypes.MAP_BENEFIT,
      //   payload: res.data.data,
      // });
    } else {
      console.log(res);
    }
  } catch (error) {
  } finally {
  }
};
export const mapProvider = (provider) => async (dispatch) => {
  try {
    const res = await axios.post(`${api_url_Catalog}/benefit`, provider);
    console.log(res);
    console.log(res.data.data);

    if (res.data.success === true) {
      console.log(res.data.success);

      // dispatch({
      //   type: actionTypes.MAP_BENEFIT,
      //   payload: res.data.data,
      // });
    } else {
      console.log(res);
    }
  } catch (error) {
  } finally {
  }
};

export const getCountry = () => async (dispatch) => {
  dispatch({ type: actionTypes.SET_LOCATION_LOADING });
  try {
    const res = await axios.get(`${api_url_country}`);
    console.log(res.data.data);

    if (res.data.success == true) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_COUNTRY,
        payload: res.data.data,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};

export const getRegionsByCountry = (countryId) => async (dispatch) => {
  dispatch({ type: actionTypes.SET_LOCATION_LOADING });
  try {
    const res = await axios.get(
      `${api_url_country}${countryId}/region?countryId=${countryId}&page=1&size=500`
    );

    if (res.data.success == true) {
      console.log(res);

      dispatch({
        type: actionTypes.GET_REGIONSBY_COUNTRY,
        payload: res.data.data.content,
      });
    } else {
      console.log(res);
    }
  } catch (error) {}
};

export const showPayer = (payer) => async (dispatch) => {
  dispatch({
    type: actionTypes.SHOW_PAYER,
    payload: payer,
  });
};
export const setSelectedBenefit = (benefit) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_SELECTED_BENEFIT,
    payload: benefit,
  });
};
export const setSelectedPayer = (payer) => async (dispatch) => {
  dispatch({
    type: actionTypes.SET_SELECTED_PAYER,
    payload: payer,
  });
};
