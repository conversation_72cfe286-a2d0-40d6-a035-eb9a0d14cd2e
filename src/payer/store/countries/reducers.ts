import * as actionTypes from "./actionTypes";
import { PayerAction, PayerState } from "./types";

const initialState = {
  countries: [],
  regions: [],
  success: false,
  loading: false,
  payerItem: {},
  selectedBenefit: {},
  selectedPayer: {},
};

export const countryReducer = (state = initialState, action) => {
  switch (action.type) {
    case actionTypes.SET_LOCATION_LOADING:
      return {
        ...state,
        loading: true,
        success: false,
      };
    case actionTypes.ADD_COUNTRY: {
      const newState = {
        success: action.payload,
        loading: false,
      };
      return { ...state, ...newState };
    }
    case actionTypes.ADD_REGION: {
      const newState = {
        success: action.payload,
        loading: false,
      };
      return { ...state, ...newState };
    }
    case actionTypes.GET_COUNTRY: {
      return {
        ...state,
        countries: action.payload,
        loading: false,
        success: true,
      };
    }
    case actionTypes.GET_REGIONSBY_COUNTRY: {
      return {
        ...state,
        regions: action.payload,
        loading: false,
        success: true,
      };
    }

    case actionTypes.SHOW_PAYER: {
      const newState = {
        payerItem: action.payload,
      };
      return { ...state, ...newState };
    }
    case actionTypes.SET_SELECTED_BENEFIT: {
      const newState = {
        selectedBenefit: action.payload,
      };
      return { ...state, ...newState };
    }
    case actionTypes.SET_SELECTED_PAYER: {
      const newState = {
        selectedPayer: action.payload,
      };
      return { ...state, ...newState };
    }
    default:
      return state;
  }
};
