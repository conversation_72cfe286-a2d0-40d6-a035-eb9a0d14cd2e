// * Prevent insertion of scientific, addition and subtraction letters/symbols
// * if enabled by data- attributes
document.addEventListener("keydown", (e) => {
  const target = e.target as HTMLElement;
  if (!target.matches('input[type="number"]')) return;

  if (target.hasAttribute("data-nonscientific-number-input")) {
    if ((e as KeyboardEvent).key.toLowerCase() === "e") {
      e.preventDefault();
    }
  }

  if (target.hasAttribute("data-unsigned-number-input")) {
    const key = (e as KeyboardEvent).key;
    if (key === "+" || key === "-") {
      e.preventDefault();
    }
  }
});

// * Remove scrolling behavior from all number inputs
document.addEventListener(
  "wheel",
  (e) => {
    const target = e.target as HTMLElement;
    // if (!(target instanceof HTMLElement)) return;

    if (target.matches('input[type="number"]')) {
      e.preventDefault();
    }
  },
  {
    passive: false,
  },
);
