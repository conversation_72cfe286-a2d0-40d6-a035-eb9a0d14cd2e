import { api_url, api_url_Catalog, api_url_provider } from "./Utils";
import axios from "axios";
let schemeOptions = [];
let schemeObject = {};
let fetchedSchemes = false;

let providerOptions = [];
let payerOptions = [];
let benefitCatalogOptions = [];
let providerObject = {};
let payerObject = {};
let benefitCatalogObject = {};
let fetchedProviders = false;
let fetchedPayers = false;
let fetchedBenefitCatalog = false;

const fetchSchemes = async (sizeValue) => {
  const requestSize = sizeValue ? sizeValue : "10";

  const res = await axios.get(`${api_url}/plans?page=1&size=${requestSize}`);

  if (res.data.success == true) {
    schemeObject.totalElements = res.data.data.totalElements;
    schemeObject.numberOfElements = res.data.data.numberOfElements;
    schemeObject.data = res.data.data.content;
    fetchedSchemes = true;
    schemeOptions = [];

    for (let i = 0; i < schemeObject.numberOfElements; ++i) {
      schemeOptions.push({
        value: schemeObject.data[i].id,
        label: schemeObject.data[i].name,
      });
    }
  }
};
const fetchPayers = async (sizeValue) => {
  const requestSize = sizeValue ? sizeValue : "10";

  const res = await axios.get(`${api_url}/payers`);

  if (res.data.success == true) {
    payerObject.results = res.data.data.results;
    payerObject.data = res.data.data;
    fetchedPayers = true;
    payerOptions = [];
    //console.log(payerObject);

    for (let i = 0; i < payerObject.data.length; ++i) {
      payerOptions.push({
        value: payerObject.data[i].id,
        id: payerObject.data[i].id,
        label: payerObject.data[i].name,
        name: payerObject.data[i].name,
        contact: payerObject.data[i].contact,
        type: payerObject.data[i].type,
      });
    }
  }
};
const fetchBenefitCatalog = async (sizeValue) => {
  const requestSize = sizeValue ? sizeValue : "10";

  const res = await axios.get(
    `${api_url_Catalog}/all?page=1&size=${requestSize}`
  );

  if (res.data.success == true) {
    benefitCatalogObject.totalElements = res.data.data.totalElements;
    benefitCatalogObject.numberOfElements = res.data.data.numberOfElements;
    benefitCatalogObject.data = res.data.data.content;
    fetchedBenefitCatalog = true;
    benefitCatalogOptions = [];

    for (let i = 0; i < benefitCatalogObject.data.length; ++i) {
      benefitCatalogOptions.push({
        value: benefitCatalogObject.data[i].id,
        label: benefitCatalogObject.data[i].name,
      });
    }
  }
};
const fetchProviders = async (sizeValue) => {
  const requestSize = sizeValue ? sizeValue : "10";

  const res = await axios.get(
    `${api_url_provider}/all?page=1&size=${requestSize}`
  );

  if (res.data.success == true) {
    providerObject.totalElements = res.data.data.totalElements;
    providerObject.numberOfElements = res.data.data.numberOfElements;
    providerObject.data = res.data.data.content;

    for (let i = 0; i < providerObject.numberOfElements; ++i) {
      providerOptions.push({
        value: providerObject.data[i].id,
        label: providerObject.data[i].name,
      });
    }
  }
};

export const loadPayerOptions = async (search, prevOptions) => {
  let filteredOptions;
  if (!search) {
    filteredOptions = payerOptions;
  } else {
    const searchLower = search.toLowerCase();
    fetchPayers(payerObject.results);

    if (!fetchedPayers) {
      fetchedPayers(payerObject.results);
    }
    filteredOptions = payerOptions.filter(({ label }) =>
      label.toLowerCase().includes(searchLower)
    );
  }
  const hasMore = filteredOptions.length > payerObject.data.length;
  const slicedOptions = filteredOptions.slice(0, payerObject.data.length);
  return {
    options: slicedOptions,
    hasMore,
  };
};
export const loadschemeOptions = async (search, prevOptions) => {
  let filteredOptions;
  if (!search) {
    filteredOptions = schemeOptions;
  } else {
    const searchLower = search.toLowerCase();
    fetchSchemes(schemeObject.totalElements);

    if (!fetchedSchemes) {
      fetchSchemes(schemeObject.totalElements);
    }
    filteredOptions = schemeOptions.filter(({ label }) =>
      label.toLowerCase().includes(searchLower)
    );
  }
  const hasMore = filteredOptions.length > schemeObject.totalElements;
  const slicedOptions = filteredOptions.slice(0, schemeObject.totalElements);
  return {
    options: slicedOptions,
    hasMore,
  };
};

export const loadProviderOptions = async (search, prevOptions) => {
  let filteredOptions;
  if (!search) {
    filteredOptions = providerOptions;
  } else {
    const searchLower = search.toLowerCase();

    if (!fetchedProviders) {
      fetchedProviders = true;

      fetchProviders(providerObject.totalElements);
    }
    filteredOptions = providerOptions.filter(({ label }) =>
      label.toLowerCase().includes(searchLower)
    );
  }

  const hasMore = filteredOptions.length > providerObject.totalElements;
  const slicedOptions = filteredOptions.slice(0, providerObject.totalElements);
  return {
    options: slicedOptions,
    hasMore,
  };
};
export const loadBenefitCatalogOptions = async (search, prevOptions) => {
  let filteredOptions;
  if (!search) {
    filteredOptions = benefitCatalogOptions;
  } else {
    const searchLower = search.toLowerCase();
    fetchBenefitCatalog(benefitCatalogObject.totalElements);

    if (!fetchedBenefitCatalog) {
      fetchedBenefitCatalog = true;

      fetchBenefitCatalog(benefitCatalogObject.data.length);
    }
    filteredOptions = benefitCatalogOptions.filter(({ label }) =>
      label.toLowerCase().includes(searchLower)
    );
  }

  const hasMore = filteredOptions.length > benefitCatalogObject.totalElements;
  const slicedOptions = filteredOptions.slice(
    0,
    benefitCatalogObject.totalElements
  );
  return {
    options: slicedOptions,
    hasMore,
  };
};

fetchSchemes();
fetchProviders();
fetchPayers();
fetchBenefitCatalog();
