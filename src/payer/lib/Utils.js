export const baseUrl = import.meta.env.VITE_API_URL;

export const api_url_preauth = `${baseUrl}/api/v1/preauthorization/`;
export const api_url = `${baseUrl}/api/v1/membership`;
export const api_url_auditlog = `${baseUrl}/api/v1/audit`;
export const api_url_biometrics = `${baseUrl}/api/v1/biometric/`;
export const api_url_visit = `${baseUrl}/api/v1/visit/`;
export const api_url_member_management_Search = `${baseUrl}/api/v1/search/member/details/payer`;
export const api_url_Catalog = `${baseUrl}/api/v1/catalog`;
export const api_url_membership = `${baseUrl}/api/v1/visit/payer/beneficiary?`;
export const api_url_country = `${baseUrl}/api/v1/country/`;
export const api_url_provider = `${baseUrl}/api/v1/provider/`;
export const api_url_apply_task = `${baseUrl}/api/v1/ticketing/`;
export const api_url_cashier = `${baseUrl}/api/v1/cashier/`;
export const api_beneficiary_url = `${baseUrl}/api/v1/visit/benefit/search`;
export const api_url_membership_root = `${baseUrl}/api/v1/membership/`;
export const api_url_search = `${baseUrl}/api/v1/search/`;
export const api_url_claim_report = `${baseUrl}/api/v1/claim/report`;

export const hexToRGB = (h) => {
  let r = 0;
  let g = 0;
  let b = 0;
  if (h.length === 4) {
    r = `0x${h[1]}${h[1]}`;
    g = `0x${h[2]}${h[2]}`;
    b = `0x${h[3]}${h[3]}`;
  } else if (h.length === 7) {
    r = `0x${h[1]}${h[2]}`;
    g = `0x${h[3]}${h[4]}`;
    b = `0x${h[5]}${h[6]}`;
  }
  return `${+r},${+g},${+b}`;
};

export const formatValue = (value) =>
  Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "KES",
    maximumSignificantDigits: 10,
  }).format(value);

export const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-GB").replace(/\//g, ".");
};

export const formatDateSlash = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-GB").replace(/\//g, "/");
};

export const formatLogDate = (dateString) => {
  const date = new Date(dateString);
  const day = ("0" + date.getDate()).slice(-2);
  const monthAbbrev = date.toLocaleString("en-GB", { month: "short" });
  const _year = date.getFullYear();
  return `${day} ${monthAbbrev}`;
};

export function calculateDaysDifference(date1, date2) {
  const [day1, month1, year1] = date1.split(".").map(Number);
  const [day2, month2, year2] = date2.split(".").map(Number);

  const d1 = new Date(year1, month1 - 1, day1);
  const d2 = new Date(year2, month2 - 1, day2);

  if (
    d1.getFullYear() === d2.getFullYear() &&
    d1.getMonth() === d2.getMonth() &&
    d1.getDate() === d2.getDate()
  ) {
    return 1;
  }

  const differenceInMillis = Math.abs(d2 - d1);
  return differenceInMillis / (1000 * 60 * 60 * 24);
}
