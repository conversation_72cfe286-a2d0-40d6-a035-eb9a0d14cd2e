import { useState, useEffect } from "react";
import axios from "axios";
import { api_url_Catalog } from "./Utils";

const useFetch = () => {
  const [data, setData] = useState({
    slug: "",
    results: [],
  });

  useEffect(() => {
    if (data.slug !== "") {
      const timeoutId = setTimeout(() => {
        const fetch = async () => {
          try {
            const res = await axios.get(
              `${api_url_Catalog}/benefit?search=${data.slug}&page=1&size=10`
            );
            setData({ ...data, results: res.data.data.content });
          } catch (err) {
            console.error(err);
          }
        };
        fetch();
      }, 1000);
      return () => clearTimeout(timeoutId);
    }
  }, [data.slug]);

  return { data, setData };
};

export default useFetch;
