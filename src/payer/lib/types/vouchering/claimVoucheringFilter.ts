import { AgeBand } from "../claims/ageBand";
import { MembershipAccount } from "../membership/memberAccount";
import { MembershipBenefitCatalog } from "../membership/memberBenefit";
import { MembershipRegion } from "../membership/memberRegion";
import { MembershipScheme } from "../membership/memberScheme";

export enum VoucheringCriteria {
  AGING = "Aging",
  BENEFIT = "Benefit",
  ACCOUNT = "Account",
  SCHEME = "Scheme",
  REGION = "Region",
}

export type VoucheringCriteriaSelect =
  | { name: VoucheringCriteria.REGION; options: Array<MembershipRegion | string>; value: string }
  | { name: VoucheringCriteria.AGING; options: Array<AgeBand | string>; value: number }
  | { name: VoucheringCriteria.ACCOUNT; options: Array<MembershipAccount | string>; value: string }
  | { name: VoucheringCriteria.SCHEME; options: Array<MembershipScheme | string>; value: string }
  | {
      name: VoucheringCriteria.BENEFIT;
      options: Array<MembershipBenefitCatalog | string>;
      value: number;
    };
