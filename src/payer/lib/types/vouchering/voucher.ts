import { APIResponseWithContent } from "../apiResponseGeneric";

export interface Voucher {
  id: number;
  payerId: number;
  provider: {
    mainFacilityId: number;
    providerName: string;
    usesGlobalBatchInvoice: boolean | null;
    providerId: number;
  };
  providerAccount: {
    accountId: number;
    accountName: string;
    providerName: string;
    providerId: number;
    payerId: number;
    mappingId: number;
    accountNumber: string;
    bankName: string;
    bankBranch: string;
  };
  voucherNo: string;
  amount: number;
  batchCriteria: string;
  batchCriteriaId: number;
  batchCriteriaValue: string;
  discountType: string;
  discount: number;
  createdBy: {
    name: string;
    value: string;
    id: string;
    userName: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  createdOn: string;
}

export type VouchersResponse = APIResponseWithContent<Voucher>;

export type VoucherPaymentStatus = "Paying" | "UnPaid" | "Paid";
export type VoucherBatchCriteria = "Aging" | "Benefit" | "Region" | "Provider" | "Scheme";

export interface GetAllVouchersParams {
  payerId: string;
  voucherNo?: number;
  aggregatedId?: number;
  voucherIds?: Array<number>;
  providerIds?: Array<number>;
  providerAccountIds?: Array<number>;
  regionIds?: Array<number>;
  paymentIds?: Array<number>;
  voucherPaymentStatus?: VoucherPaymentStatus;
  batchCriteria?: VoucherBatchCriteria;
  startDate?: string;
  endDate?: string;
  interimSave?: boolean;
  page?: number;
  size?: number;
}

export interface DeleteVoucherPayload {
  voucherId: number;
  body: {
    actionedBy: string;
  };
}

export interface VoucherResponse {
  id: number;
  payerId: number;
  providerId: number;
  providerAccountId: number;
  voucherNo: string;
  amount: number;
  allocatedAmount: number;
  batchCriteria: VoucherBatchCriteria;
  batchCriteriaId: number;
  batchCriteriaValue: string;
  discountType: VoucherDiscount;
  discount: number;
  description: string;
  aggregateId: string;
  interimSave: boolean;
  migrated: boolean;
  createdBy: string;
  createdOn: string;
  signOff: boolean;
}

export type VoucherDiscount = "Fixed" | "Percentage";
