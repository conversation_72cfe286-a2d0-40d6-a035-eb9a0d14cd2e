export interface APIResponseWithContent<T> {
  success: boolean;
  msg: null | string;
  data: {
    content: T[];
    pageable: {
      sort: {
        empty: boolean;
        sorted: boolean;
        unsorted: boolean;
      };
      offset: number;
      pageNumber: number;
      pageSize: number;
      paged: boolean;
      unpaged: boolean;
    };
    totalPages: number;
    totalElements: number;
    last: boolean;
    number: number;
    sort: {
      empty: boolean;
      sorted: boolean;
      unsorted: boolean;
    };
    size: number;
    numberOfElements: number;
    first: boolean;
    empty: boolean;
  };
}

export interface APIResponseWithoutContent<T> {
  success: boolean;
  msg: null | string;
  data: T[];
  results: number | null;
}

export interface GeneralResponseBoolean {
  success: boolean;
  msg: null | string;
  data: boolean;
}

export interface GeneralResponseGeneric<T> {
  success: boolean;
  msg: null | string;
  data: T;
}

export interface CustomPageableResponse<T> {
  content: Array<T>;
  pageable: {
    sort: {
      empty: boolean;
      sorted: boolean;
      unsorted: boolean;
    };
    offset: number;
    pageNumber: number;
    pageSize: number;
    paged: boolean;
    unpaged: boolean;
  };
  totalPages: number;
  totalElements: number;
  last: boolean;
  number: number;
  sort: {
    empty: boolean;
    sorted: boolean;
    unsorted: boolean;
  };
  size: number;
  numberOfElements: number;
  first: boolean;
  empty: boolean;
}
