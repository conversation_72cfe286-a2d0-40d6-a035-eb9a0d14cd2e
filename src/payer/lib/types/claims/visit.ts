import {
  ClaimProcessStatus,
  Diagnosis,
  FacilityType,
  InvoiceNumberProcedureCode,
  MiddlewareStatus,
  OffSystemReason,
  Preauthorization,
  ProviderMiddleware,
  VettingStatus,
} from "../care/preAuth";
import {
  InvoiceData,
  InvoiceLine,
  InvoicePayerStatus,
  InvoiceServiceGroup,
  InvoiceVisitStatus,
  InvoiceVisitType,
} from "./invoice";

export interface Visit {
  id: number;
  memberNumber: string;
  memberName: string;
  hospitalProviderId?: number;
  staffId: string;
  staffName: string;
  aggregateId: string;
  categoryId?: string;
  benefitName?: string;
  beneficiaryId?: number;
  benefitId?: number;
  payerId: string;
  policyNumber?: string;
  balanceAmount: number;
  beneficiaryType?: string;
  totalInvoiceAmount?: number;
  providerMiddleware?: ProviderMiddleware;
  invoiceNumber?: string;
  status?: InvoiceVisitStatus;
  middlewareStatus?: MiddlewareStatus;
  claimProcessStatus?: ClaimProcessStatus;
  cancelReason?: string;
  createdAt: string;
  updatedAt?: string;
  visitEnd?: string; // date
  diagnosis: Array<Diagnosis>;
  procedures: Array<InvoiceNumberProcedureCode>;
  visitType?: InvoiceVisitType;
  offSystemReason?: OffSystemReason;
  reimbursementProvider?: string;
  reimbursementInvoiceDate?: string;
  reimbursementReason?: string;
  payerStatus?: InvoicePayerStatus;
  facilityType?: FacilityType;
  serviceGroup: InvoiceServiceGroup;
  invoices: Array<InvoiceData>;
  preAuths: Array<Preauthorization>;
  providerMapping?: string;
  benefitMapping?: string;
  payerClaimReference?: string;
  invoiceDate?: string;
  bigQueryPicked?: boolean;
  providerName?: string;
  referToProviderId?: number;
  referToProviderName?: string;
  referToDoctor?: string;
  referNarration?: string;
  vettingStatus?: VettingStatus;
  batchNo?: string;
  categoryName?: string;
  categoryDescription?: string;
  schemeName?: string;
  payerName?: string;
  otherNumber?: string;
  lineItems: Array<InvoiceLine>;
  visitReference?: string;
}
