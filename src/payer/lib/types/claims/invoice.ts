import { VoucheringCriteria } from "../vouchering/claimVoucheringFilter";
import { MiddlewareStatus } from "../care/preAuth";

export type InvoiceVisitStatus =
  | "INACTIVE"
  | "ACTIVE"
  | "CLOSED"
  | "LINE_ITEMS_ADDED"
  | "DIAGNOSIS_ADDED"
  | "PENDING"
  | "CANCELLED"
  | "TRANSMITTED"
  | "SETTLED"
  | "REJECTED"
  | "DRAFT"
  | "REFERRAL_REQUEST"
  | "REFERRED";

export type InvoicePayerStatus = "UNSENT" | "SENT" | "FAILED" | "PENDING";

export type InvoicePaymentStatus = "PAID" | "NOT_PAID";

export type InvoiceVisitType = "ONLINE" | "OFF_LCT" | "REIMBURSEMENT";

export type InvoiceServiceGroup = "DENTAL" | "OPTICAL" | "COVID" | "MATERNITY";

export type InvoiceDeductibleType = "COPAYMENT" | "EXCLUSION" | "CASHPAYMENT" | "DISCOUNT" | "NHIF";

export type InvoiceDeductible = {
  id: number;
  deductibleType: InvoiceDeductibleType;
  amount: number;
  invoice: InvoiceData;
};

export interface InvoiceLine {
  id: number;
  lineTotal: number;
  description: string;
  invoiceNumber: string;
  quantity: number;
  unitPrice: number;
  lineType: string;
  claimRef: string | null;
  lineCategory: string | null;
  createdAt: string;
}

export interface InvoiceData {
  id: string | number;
  hospitalProviderId: number;
  invoiceNumber: string;
  service: string;
  totalAmount: number;
  payableAmount: number | null;
  deductibleAmount: number | null;
  actionedBy: string;
  claimRef: string | null;
  batchInvoiceNumber: string | null;
  status: InvoiceStatus;
  transType: string;
  batchStatus: InvoiceBatchStatus;
  vettingStatus: InvoiceVettingStatus;
  vetDate: string | null;
  benefitId: number;
  invoiceLines: InvoiceLine[];
  dispatched: boolean | null;
  createdAt: string;
  payerStatus: InvoicePayerStatus;
  payerMessage: string;
  paymentStatus: InvoicePaymentStatus;
  paymentReference: string | null;
  paymentDate: string | null;
  providerName: string;
  schemeName: string;
  payerName: string;
  memberNumber: string;
  memberName: string;
  invoiceDate: string;
  categoryName: string;
  benefitName: string;
  visitType: InvoiceVisitType;
  deductibles: InvoiceDeductible[];
  middlewareStatus: MiddlewareStatus;
  visitNumber: number;
}

export interface ClaimsResponse {
  success: boolean;
  msg: null | string;
  data: {
    content: InvoiceData[];
    empty: boolean;
    first: boolean;
    last: boolean;
    number: number;
    numberOfElements: number;
    pageable: {
      sort: {
        empty: boolean;
        sorted: boolean;
        unsorted: boolean;
      };
      offset: number;
      pageNumber: number;
      pageSize: number;
      paged: boolean;
      unpaged: boolean;
    };
    size: number;
    sort: {
      empty: boolean;
      sorted: boolean;
      unsorted: boolean;
    };
    totalElements: number;
    totalPages: number;
  };
}

export interface AssignBatchToPayerPayload {
  batchId: number | string;
  assignedBy: string;
  assignedTo: Array<string>;
  vettingAssignmentMode?: VettingAssignmentMode;
  reason?: string;
}

export type VettingAssignmentMode = "SINGLE_USER" | "MULTIPLE_USERS";

export type InvoiceVettingStatus = "PENDING" | "PARTIAL" | "APPROVED" | "DECLINED" | "BATCHED";
export type InvoiceBatchStatus = "BATCHED" | "NOT_BATCHED";
export type InvoiceStatus =
  | "REJECTED"
  | "BALANCE_DEDUCTED"
  | "DIAGNOSIS_ADDED"
  | "DOCUMENTS_ADDED"
  | "PENDED"
  | "SENT"
  | "BALANCE_ADDED"
  | "REVERSED"
  | "DISPATCHED"
  | "CANCELLED"
  | "PROFORMA";

export type GetAllClaimsParams = {
  payerId?: string | number;
  providerId?: number | undefined;
  ageBandId?: number | undefined;
  region?: string | undefined;
  catalogIds?: number[];
  planIds?: (string | number)[];
  batchStatus?: InvoiceBatchStatus;
  vettingStatuses?: Array<InvoiceVettingStatus>;
  size?: number;
  page?: number;
  withVoucher?: boolean;
  onlyProviderWithAccounts?: boolean;
  invoiceStatuses?: Array<InvoiceStatus>;
  voucherIds?: Array<number>;
  providerAccountIds?: Array<number>;
  payerStatuses?: Array<InvoicePayerStatus>;
  providerIds?: Array<number>;
  payerIds?: Array<number>;
  mainProviderIds?: Array<number>;
  policyIds?: Array<number>;
  beneficiaryIds?: Array<number>;
  invoiceIds?: Array<number>;
  batchIds?: Array<number>;
  mainBenefitIds?: Array<number>;
  benefitIds?: Array<number>;
  mainCatalogIds?: Array<number>;
  categoryIds?: Array<number>;
  visitStatuses?: Array<InvoiceVisitStatus>;
  visitTypes?: Array<InvoiceVisitType>;
  serviceGroups?: Array<InvoiceServiceGroup>;
  startDate?: string;
  endDate?: string;
  vettingStartDate?: string;
  vettingEndDate?: string;
  query?: string;
};

export interface VoucherClaimsResponse {
  success: boolean;
  msg: null | string;
  data: boolean;
}

export interface VoucherClaimsPayload {
  payerId: number | string;
  providerId?: number | string;
  ageBandId?: number | string;
  planIds?: Array<number | string>;
  providerIds?: Array<number | string>;
  payerIds?: Array<number | string>;
  mainProviderIds?: Array<number | string>;
  policyIds?: Array<number | string>;
  beneficiaryIds?: Array<number | string>;
  invoiceIds?: Array<number | string>;
  batchIds?: Array<number | string>;
  voucherIds?: Array<number | string>;
  mainBenefitIds?: Array<number | string>;
  benefitIds?: Array<number | string>;
  mainCatalogIds?: Array<number | string>;
  catalogIds?: Array<number | string>;
  categoryIds?: Array<number | string>;
  providerAccountIds?: Array<number>;
  invoiceStatuses?: InvoiceStatus;
  visitStatuses?: InvoiceVisitStatus;
  batchStatus?: InvoiceBatchStatus;
  withVoucher?: boolean;
  onlyProviderWithAccounts?: boolean;
  vettingStatuses?: Array<InvoiceVettingStatus>;
  payerStatuses?: Array<InvoicePayerStatus>;
  visitTypes?: Array<InvoiceVisitType>;
  serviceGroups?: Array<InvoiceServiceGroup>;
  query?: string;
  region?: string;
  startDate?: string;
  endDate?: string;
  isSignOff?: boolean;
  vettingStartDate?: string;
  vettingEndDate?: string;
  actionedBy?: string;
  provider?: string;
  vettingAssignmentMode?: VettingAssignmentMode;
  batchCriteria?: VoucheringCriteria | null;
  allocatedAmount?: number;
  description?: string;
  interimSave?: boolean;
}

export interface CalculateInvoicesAmountResponse {
  success: boolean;
  msg: null | string;
  data: number;
}

export interface CalculateInvoicesAmountPayload {
  payerId: number | string;
  providerId?: number | string;
  ageBandId?: number | string;
  planIds?: Array<number | string>;
  providerIds?: Array<number>;
  payerIds?: Array<number>;
  mainProviderIds?: Array<number>;
  policyIds?: Array<number>;
  beneficiaryIds?: Array<number>;
  invoiceIds?: Array<number | string>;
  batchIds?: Array<number>;
  voucherIds?: Array<number | string>;
  mainBenefitIds?: Array<number>;
  benefitIds?: Array<number>;
  mainCatalogIds?: Array<number>;
  catalogIds?: Array<number | string>;
  categoryIds?: Array<number>;
  providerAccountIds?: Array<number>;
  invoiceStatuses?: Array<InvoiceStatus>;
  visitStatuses?: Array<InvoiceVisitStatus>;
  batchStatus?: InvoiceBatchStatus;
  withVoucher?: boolean;
  onlyProviderWithAccounts?: boolean;
  vettingStatuses?: Array<InvoiceVettingStatus>;
  payerStatuses?: Array<InvoicePayerStatus>;
  visitTypes?: Array<InvoiceVisitType>;
  serviceGroups?: Array<InvoiceServiceGroup>;
  query?: string;
  region?: string;
  startDate?: string;
  endDate?: string;
  vettingStartDate?: string;
  vettingEndDate?: string;
  actionedBy?: string;
  provider?: string;
  vettingAssignmentMode?: VettingAssignmentMode;
  batchCriteria?: string;
  allocatedAmount?: number;
  description?: string;
  interimSave?: boolean;
}

export interface VoucherEditingPayload {
  voucherId: number | string;
  body: {
    invoiceIds?: Array<number | string> | undefined;
    interimSave?: boolean | undefined;
    actionByUser: string;
  };
}

export interface ReverseInvoicePayload {
  invoiceId: string;
  amount: number;
  invoiceLines?: Array<InvoiceLinePayload> | undefined;
  creditNoteNumber?: string | undefined;
  reversedBy: string;
  reason: string;
  sendToPayer?: boolean;
  ignoreErrors?: boolean;
}

export interface InvoiceLinePayload {
  description?: string;
  quantity?: number;
  lineCategory?: string;
  // default amount is zero
  lineTotal?: number;
  lineType?: InvoiceLineType;
  unitPrice?: number;
}

export enum InvoiceLineType {
  PHARMACY = "PHARMACY",
  LABORATORY = "LABORATORY",
  RADIOLOGY = "RADIOLOGY",
  CONSULTATION = "CONSULTATION",
  MEDICALPROCEDURE = "MEDICALPROCEDURE",
  DIAGNOSIS = "DIAGNOSIS",
  OTHER = "OTHER",
  INPATIENT = "INPATIENT",
  PROCEDURE = "PROCEDURE",
  CONSUMABLE = "CONSUMABLE",
  PACKAGE = "PACKAGE",
  PROFESSIONAL_FEE = "PROFESSIONAL_FEE",
  ROOM_BOARD = "ROOM_BOARD",
}
