export type BatchAllocationStatus = "Allocated" | "UnAllocated";

export interface BatchedInvoicesList {
  id: string;
  batchCriteria: string | null;
  invoiceCount: number;
  totalAmount: number;
  assignedTo: Array<string>;
  batchAge: string;
  createdOn: string;
  createdBy: {
    name: string;
    value: string;
    id: string;
    firstName: string;
    lastName: string;
    userName: string;
    email: string | null;
  };
  batchStatus: string | null;
  allocationStatus: BatchAllocationStatus | null;
}

export interface BatchedInvoicesListResponse {
  success: boolean;
  msg: null | string;
  data: {
    content: BatchedInvoicesList[];
    empty: boolean;
    first: boolean;
    last: boolean;
    number: number;
    numberOfElements: number;
    pageable: {
      sort: {
        empty: boolean;
        sorted: boolean;
        unsorted: boolean;
      };
      offset: number;
      pageNumber: number;
      pageSize: number;
      paged: boolean;
      unpaged: boolean;
    };
    size: number;
    sort: {
      empty: boolean;
      sorted: boolean;
      unsorted: boolean;
    };
    totalElements: number;
    totalPages: number;
  };
}
