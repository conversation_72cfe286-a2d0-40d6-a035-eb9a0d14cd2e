import { PreAuthStatus } from "../care/preAuth";
import { InvoiceVisitStatus } from "./invoice";

export type AdjudicationClaimType = "DISCHARGE_RAISED" | "INITIAL_PRE_AUTHORIZATION_RAISED";
export type AdjudicationStatus =
  | "Pending"
  | "Errored"
  | "Complete"
  | "Adjudicating"
  | "AwaitingManualReview";

export interface ClaimAdjudication {
  reference: string | null;
  id: number;
  memberName: string;
  query: string;
  claimNumber: string;
  approvedAmount: number;
  claimDecision: string;
  rejectionReasons: string;
  authorizedAmount: number;
  benefitName: string;
  preAuthStatus: PreAuthStatus;
  visitNumber: number;
  preAuthId?: number;
  errorMsg: string;
  resultUrl: string;
  requestRefNumber: string;
  memberNumber: string;
  service: string;
  status: AdjudicationStatus;
  claimType: AdjudicationClaimType;
  requestType: string;
  createdAt: string;
  totalInvoiceAmount: number;
  requestAmount: number;
  visitStatus: InvoiceVisitStatus;
}

export interface ClaimAdjudicationSearchParams {
  payerId: string;
  visitId?: string;
  search?: string;
  claimTypes?: Array<AdjudicationClaimType>;
  adjudicationStatuses?: Array<AdjudicationStatus>;
  fromDate?: string;
  toDate?: string;
  sortColumn?: string;
  sortOrder?: string;
  page?: number;
  size?: number;
}
