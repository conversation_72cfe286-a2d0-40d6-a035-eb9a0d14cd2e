import { ClaimsUser } from "./claimsUser";

export interface GetBatchResponse {
  success: true;
  msg: null;
  data: Batch;
}

export interface Batch {
  id: number;
  payerId: number;
  batchName: string | null;
  batchCriteria: string | null;
  provider: string | null;
  region: string | null;
  totalAmount: number;
  actionedBy: string;
  vettingAssignmentMode: string | null;
  allocationStatus: string | null;
  batchStatus: string | null;
  createdOn: string;
  invoiceCount: number;
  assignedTo: Array<ClaimsUser>;
}

export interface UpdateBatchPayload {
  batchName: string;
  vettingAssignmentMode: string;
  editedBy: string;
}

export interface BatchUpdateResponse {
  success: boolean;
  msg: string;
  data: boolean;
}

export interface BatchDeleteResponse {
  success: boolean;
  msg: string;
  data: boolean;
}
