export interface AuditLog {
  id: number;
  action: string;
  eventName: string;
  previousValues: string | null;
  newValues: string | null;
  actionByUser: string;
  reason: string | null;
  createdOn: string;
}

export interface BatchAuditLogsResponse {
  success: true;
  msg: null;
  data: {
    content: Array<AuditLog>;
    pageable: {
      sort: {
        empty: true;
        sorted: false;
        unsorted: true;
      };
      offset: 0;
      pageNumber: 0;
      pageSize: 50;
      paged: true;
      unpaged: false;
    };
    totalElements: 7;
    totalPages: 1;
    last: true;
    number: 0;
    sort: {
      empty: true;
      sorted: false;
      unsorted: true;
    };
    size: 50;
    numberOfElements: 7;
    first: true;
    empty: false;
  };
}
