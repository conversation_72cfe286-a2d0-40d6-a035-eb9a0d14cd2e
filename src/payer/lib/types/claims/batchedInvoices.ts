export interface BatchedInvoices {
  payerId: number;
  providerId?: number;
  planIds?: Array<number>;
  providerIds?: Array<number>;
  payerIds?: Array<number>;
  mainProviderIds?: Array<number>;
  policyIds?: Array<number>;
  beneficiaryIds?: Array<number>;
  invoiceIds: Array<string>;
  invoiceStatuses?: Array<string>;
  visitStatuses?: Array<string>;
  batchStatus?: string;
  vettingStatuses?: Array<string>;
  payerStatuses?: Array<string>;
  visitTypes?: Array<string>;
  query?: string;
  region?: string;
  startDate?: string;
  endDate?: string;
  vettingStartDate?: string;
  vettingEndDate?: string;
  actionedBy?: string;
  provider?: string;
  vettingAssignmentMode?: string;
  batchCriteria?: string;
}

export interface BatchInvoicesResponse {
  success: boolean;
  msg: string;
  data: boolean;
}

export type InvoiceBatchStatus = "UnVetted" | "InProgress" | "Completed";
export type BatchAllocationStatus = "UnAllocated" | "Allocated";

export interface GetBatchedClaimsParams {
  payerId: string;
  batchAllocationStatus?: BatchAllocationStatus | string;
  invoiceBatchStatus?: InvoiceBatchStatus | string;
  pageNumber?: number;
  pageSize?: number;
  userId?: string;
  sortColumn?: string;
  sortOrder?: "asc" | "desc";
}
