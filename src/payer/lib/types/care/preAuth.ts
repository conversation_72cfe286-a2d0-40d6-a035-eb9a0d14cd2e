import { InvoiceVisitStatus } from "../claims/invoice";
import { Visit } from "../claims/visit";
import { RequestType } from "../../../api/types";
import { PreAuthorizationTopUp } from "~lib/api/types";

export enum PreAuthFilterCriteria {
  SCHEME = "Scheme",
  PROVIDER = "Provider",
}

export interface PreAuthCriteriaSelect<OptType, ValType> {
  name: PreAuthFilterCriteria;
  options: Array<string | OptType>;
  value: ValType;
}

export interface PreAuthSearchParams {
  payerId: number;
  providerId?: number;
  beneficiaryId?: number;
  planId?: number;
  search?: string;
  status?: PreAuthStatus;
  visitStatuses?: Array<InvoiceVisitStatus>;
  requestTypes?: Array<string>;
  startDate?: string;
  endDate?: string;
  page?: number;
  size?: number;
}

export type PreAuthStatus =
  | "ACTIVE"
  | "INACTIVE"
  | "PENDING"
  | "DECLINED"
  | "AUTHORIZED"
  | "CLAIMED"
  | "CANCELLED"
  | "EXPIRED"
  | "DRAFT"
  | "WITHDRAWN";

export type PreAuthType = "SCHEDULED_ADMISSION" | "EMERGENCY_ADMISSION";

export interface Preauthorization {
  id: number;
  time: string;
  status: PreAuthStatus;
  draft: boolean;
  preauthType: PreAuthType;
  requestAmount: number;
  initialRequestAmount?: number;
  authorizedAmount?: number;
  initialAuthorizedAmount?: number;
  balanceAmount?: number;
  reversedAmount?: number;
  requester?: string;
  authorizer?: string;
  rejectBy?: string;
  notes: string;
  initialNotes: string;
  authorizationNotes?: string;
  rejectNotes?: string;
  validForDays?: number;
  visit: Visit;
  service: string;
  requestType: RequestType;
  reference: string;
  utilization: number;
  procedureQuantity: number;
  doctorName?: string;
  supportingDocuments?: Array<string>;
  preDiagnosisCodes?: Array<string>;
  procedureCode?: string;
  toothNumber?: number;
  numberOfTeeth?: number;
  causeOfIllness?: string;
  surgeryRequired: boolean;
  lensDetails?: string;
  typeOfLens?: string;
  frameMake?: string;
  natureOfInjury?: string;
  causeOfAccident?: string;
  natureOfAccident?: string;
  dateOfAccident?: string;
  dateOfAdmission?: string;
  expedited: boolean;
  typeOfSurgery?: string;
  procedureType?: string;
  numberOfSessions?: number;
  referredFrom?: string;
  dueDate?: string;
  bedType?: string;
  bedCharges?: number;
  netOfNHIFLimit?: number;
  doctorFees?: number;
  prescriptionType?: string;
  prescriptionReason?: string;
  otherPrescriptionReason?: string;
  otherProcedure?: string;
  surgeryDetails?: string;
  underlyingCondition?: string;
  conditionCongenital: boolean;
  conditionRecurrent: boolean;
  dateOfProcedure?: string;
  previousComplication?: string;
  caesareanType?: string;
  priorCaesarean?: string;
  physiotherapySessions?: number;
  amountPerSession?: number;
  radiologyType?: string;
  dateOfFirstDiagnosis?: string;
  natureOfTreatment?: string;
  dentalConditionType?: string;
  specialistReferral?: string;
  framesCost?: number;
  lensesCost?: number;
  consultationFee?: number;
  dentalCost?: number;
  allowedDaysOfAdmission?: number;
  limit?: number;
  createdAt: string;
  updatedAt?: string;
  authorizedAt?: string;
  markAsIncomplete: boolean;
  markAsIncompleteReason?: string;
  parentPreAuth?: Preauthorization;
  sessionTrackers: Array<SessionTracker>;
  lines: Array<PreAuthorizationLine>;
  diagnosisCodes: Array<PreAuthDiagnosisCode>;
  diagnosisCodesRetrieved: boolean;
  docsRetrieved: boolean;
  guidelines: Array<PreAuthorizationGuideline>;
  providerName: string;
  payerName: string;
  schemeName: string;
  diagnosisInfo: Array<Icd10code>;
  procedureInfo: MedicalProcedure;
  topUps: Array<PreAuthorizationTopUp>;
}

export interface SessionTracker {
  id: number;
  amount: number;
  createdAt: string;
}

export interface PreAuthorizationLine {
  id: number;
  name: string;
  tag: string;
  numberOfTeeth: number;
  quantity: number;
  cost: number;
  active: boolean;
  createdAt: string;
}

export interface PreAuthDiagnosisCode {
  id: number;
  icdCode: Icd10code;
}

export interface PreAuthorizationGuideline {
  id: number;
  description: string;
  cost: number;
  createdAt: string;
  updatedAt?: string;
}

export type ProviderMiddleware =
  | "AVENUE"
  | "MATER"
  | "NAIROBIHOSPITAL"
  | "GETRUDES"
  | "MPSHAH"
  | "METROPOLITAN"
  | "AGAKHANKISUMU"
  | "AGAKHANMOMBASA"
  | "AGAKHANNAIROBI"
  | "NONE"
  | "AGAKHANNAIROBITEST"
  | "AKUH"
  | "GETRUDESTEST"
  | "OTHER"
  | "COPTIC"
  | "PANDYA"
  | "VALLEYHOSPITAL";

export interface InvoiceNumberProcedureCode {
  id: number;
  procedure_code: string;
  procedure_description: string;
  invoiceNumber: string;
  createdAt: string;
  updatedAt?: string;
}

export interface MedicalProcedure {
  id: number;
  procedure_code: string;
  procedure_description: string;
}

export interface Icd10code {
  id: number;
  code: string;
  title: string;
}

export interface Diagnosis {
  id: number;
  code?: string;
  title?: string;
  invoiceNumber?: string;
  claimRef?: string;
}

export type VettingStatus = "NEW" | "INCOMPLETE" | "VETTED";

export type MiddlewareStatus = "SENT" | "UNSENT";

export type FacilityType = "SINGLE" | "MULTIPLE";

export type OffSystemReason =
  | "SYSTEM_DOWNTIME"
  | "PROVIDER_NOT_SETUP"
  | "FAULTY_DEVICE"
  | "EMERGENCY_CASE";

export type ClaimProcessStatus = "PROCESSED" | "UNPROCESSED";
