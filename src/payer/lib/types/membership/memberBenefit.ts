export interface MembershipBenefit {
  id: number | string;
  name: string;
  benefitRef: {
    id: number;
    code: string;
    name: string;
    serviceGroup: string;
  };
  applicableGender: string;
  applicableMember: string;
  status: string;
  limit: number;
  suspensionThreshold: number;
  preAuthThreshold: any;
  thresholdAction: string;
  preAuthType: string;
  sharing: string;
  coPaymentRequired: boolean;
  coPaymentAmount: number;
  parentBenefit: any;
  waitingPeriod: string;
  processed: boolean;
  processedTime: string;
  payer: {
    id: number;
    name: string;
    contact: string;
    email: string;
    website: string | null;
    streetAddress: string | null;
    postalAddress: string | null;
    logo: string | null;
    type: string;
    mainPayer: any;
  };
  benefitType: string;
  capitationType: any;
  capitationPeriod: any;
  visitCountPeriod: any;
  capitationMaxVisitCount: number;
  capitationFacilitiesCount: number;
  requireBeneficiaryToSelectProvider: boolean;
  visitLimit: any;
  daysOfAdmissionLimit: number;
  amountPerDayLimit: number;
  applicableMinAge: number | null;
  applicableMaxAge: number | null;
  transferable: boolean;
  billable: any;
  restriction: any;
  changeLog: [];
}

export interface MembershipBenefitCatalog {
  id: number | string;
  code: string;
  name: string;
  serviceGroup: string;
  benefitName: string;
}
