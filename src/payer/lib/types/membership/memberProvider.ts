export interface MembershipProvider {
  id: string;
  name: string;
  email: string;
  latitude: number;
  longitude: number;
  tier: string;
  region: {
    id: string;
    name: string;
    country: {
      id: string;
      name: string;
    };
  };
  baseUrl: string;
  billingStation: boolean;
  billsOnPortal: boolean;
  billsOnHmis: boolean;
  billsOnDevice: boolean;
  billsOnHmisAutomaticClose: boolean;
  canUseOtpVerificationFailOver: boolean;
  usesGlobalBatchInvoice: null | string;
  verificationType: string;
  invoiceNumberType: null | string;
  providerMiddleware: null | string;
  useMainHospMiddleware: boolean;
  useBranchMiddleware: boolean;
  isIntergrated: boolean;
  createdOn: string;
}

export type ProviderType = "MAIN" | "BRANCH";
export type ProviderTier = "TIER_ONE" | "TIER_TWO" | "TIER_THREE";

export interface MembershipProvidersQuery {
  payerId: string;
  query?: string;
  countryId?: string;
  regionId?: string;
  mainFacilityId?: string;
  tier?: ProviderTier;
  providerType?: ProviderType;
  page?: number;
  size?: number;
}
