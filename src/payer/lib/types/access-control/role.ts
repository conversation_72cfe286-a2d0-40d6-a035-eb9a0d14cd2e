export interface RolePresentation {
  id: string;
  name: string;
  description: string;
  composite: boolean;
  clientRole: boolean;
  containerId: string;
}

export interface CustomRoleRequest {
  name: string;
  description: string;
  permissions: Array<string>;
  createdBy: string;
  payerId: number;
}

export interface CustomRoleUpdateRequest {
  id: number;
  payerId: number;
  body: {
    name?: string | undefined;
    description?: string | undefined;
    permissionsToAdd?: Array<string> | undefined;
    permissionsToRemove?: Array<string> | undefined;
    usersToAdd?: Array<string> | undefined;
    usersToRemove?: Array<string> | undefined;
    updatedBy: string;
  };
}

export interface CustomRoleResponse {
  id: number;
  name: string;
  description: string;
  isPredefined: boolean;
  permissions: PermissionResponse[];
  createdBy: string;
  createdAt: string;
  updatedAt?: string;
  payerId: number;
}

export interface CreateUserGroupRequest {
  name: string;
  description: string;
  customRoleIds?: Array<number> | undefined;
  predefinedRoles?: Array<string> | undefined;
  userIds?: Array<string> | undefined;
  createdBy: string;
  payerId: number;
}

export interface UserGroupResponse {
  id: number;
  keycloakGroupId: string;
  name: string;
  description: string;
  customRoles: CustomRoleResponse[];
  predefinedRoles: string[];
  createdBy: string;
  createdAt: string;
  updatedAt?: string;
  memberCount: number;
  members: UserGroupMemberDto[];
  payerId: number;
}

export interface AddUsersToGroupRequest {
  groupId: number;
  body: {
    userIds: Array<string>;
    addedBy: string;
  };
}

export interface UserGroupMemberDto {
  id: number;
  userId: string;
  userName?: string;
  userEmail?: string;
  name?: string;
  addedBy: string;
  addedAt: string;
}

export interface DenyPolicyRequest {
  name: string;
  userIds: string[];
  deniedPermissions: string[];
  reason: string;
  payerId: number;
  expiresAt?: string | null;
  createdBy: string;
}

export interface UserBasicDto {
  userId: string;
  userName?: string;
  userEmail?: string;
  name?: string;
}

export interface DenyPolicyDto {
  id: number;
  name: string;
  payerId: number;
  users: UserBasicDto[];
  deniedPermissions: string[];
  reason?: string;
  createdBy: string;
  createdAt: string;
  updatedAt?: string;
  expiresAt?: string;
}

export interface UserEffectiveRolesResponse {
  userId: string;
  userName?: string;
  userEmail?: string;
  directRoles: PermissionResponse[];
  inheritedRoles: Record<string, PermissionResponse[]>; // Group name -> List of roles
  deniedPermissions: PermissionResponse[];
  effectivePermissions: PermissionResponse[];
}

export interface UpdateDenyPolicyRequest {
  id: number;
  body: {
    name?: string | undefined;
    reason?: string | undefined;
    expiresAt?: string | undefined;
    addUsers?: string[] | undefined;
    removeUsers?: string[] | undefined;
    addPermissions?: string[] | undefined;
    removePermissions?: string[] | undefined;
    updatedBy: string;
  };
}

export interface PermissionResponse {
  name: string;
  description?: string;
}

export interface RemoveUsersFromGroupRequest {
  groupId: number;
  body: {
    userIds: Array<string>;
    actionedBy: string;
  };
}

export interface UpdateGroupRolesRequest {
  id: number;
  payerId: number;
  body: {
    customRoleIds?: Array<number> | undefined;
    predefinedRoles?: Array<string> | undefined;
    updatedBy: string;
  };
}

export interface UserStandaloneRolesResponse {
  userId: string;
  userName?: string;
  userEmail?: string;
  customRoles: CustomRoleResponse[];
  standaloneRoles: PermissionResponse[];
}

export interface CreatePayerUserRequest {
  username: string;
  email: string;
  password: string;
  payerId: number;
  firstName: string;
  lastName: string;
  groupIds?: Array<number> | undefined;
  customRoleIds?: Array<number> | undefined;
  predefinedRoles?: Array<string> | undefined;
  createdBy: string;
}

export interface GetUserGroupsQuery {
  payerId: number;
  page?: number;
  size?: number;
  name?: string;
}

export interface GetCustomRolesQuery {
  payerId: number;
  page?: number | undefined;
  size?: number | undefined;
  name?: string | undefined;
}

export interface GetUserGroupMembersQuery {
  groupId: number;
  page?: number;
  size?: number;
}

export interface GetPayerDenyPoliciesQuery {
  payerId: number;
  page?: number;
  size?: number;
}

export interface UserDirectRolesResponse {
  userId: string;
  userName?: string;
  userEmail?: string;
  directRoles: PermissionResponse[];
}

export interface UpdateUserGroupRequest {
  id: number;
  payerId: number;
  body: {
    name?: string | undefined;
    description?: string | undefined;
    customRolesToAdd?: Array<number> | undefined;
    customRolesToRemove?: Array<number> | undefined;
    predefinedRolesToAdd?: Array<string> | undefined;
    predefinedRolesToRemove?: Array<string> | undefined;
    usersToAdd?: Array<string> | undefined;
    usersToRemove?: Array<string> | undefined;
    updatedBy: string;
  };
}

export interface AssignCustomRoleToUserRequest {
  userId: string;
  roleId: number;
  assignedBy: string;
}

export interface RemoveCustomRoleFromUserRequest {
  userId: string;
  roleId: number;
  actionedBy: string;
}

export interface RoleManagementDeletionResponse {
  success: boolean;
  message: string;
}

export interface GetCustomRoleUsersQuery {
  id: number;
  params: {
    payerId: number;
    page: number;
    size: number;
  };
}

export interface GetDenyPolicyUsersQuery {
  id: number;
  params: {
    payerId: number;
    page: number;
    size: number;
  };
}
