export interface UserRepresentation {
  self: string;
  id: string;
  origin: string;
  createdTimestamp: number;
  username: string;
  enabled: boolean;
  emailVerified: boolean;
  firstName: string;
  lastName: string;
  email: string;
  federationLink: string;
  serviceAccountClientId: string;
  attributes: {
    providerId: Array<string>;
    payerId: Array<string>;
    providerName: Array<string>;
  };
  credentials: Array<CredentialRepresentation>;
  disableableCredentialTypes: Set<string>;
  requiredActions: Array<string>;
  federatedIdentities: Array<FederatedIdentityRepresentation>;
  realmRoles: Array<string>;
  clientRoles: Array<string>;
  clientConsents: Array<UserConsentRepresentation>;
  notBefore: number;
  groups: Array<string>;
  access: {
    manageGroupMembership: boolean;
    view: boolean;
    mapRoles: boolean;
    impersonate: boolean;
    manage: boolean;
  };
}

export interface CredentialRepresentation {
  id: string;
  type: string;
  userLabel: string;
  createdDate: number;
  secretData: string;
  credentialData: string;
  priority: number;
  value: string;
  temporary: boolean;
}

export interface UserConsentRepresentation {
  clientId: string;
  createdDate: number;
  lastUpdatedDate: number;
  grantedClientScopes: Array<string>;
}

export interface FederatedIdentityRepresentation {
  identityProvider: string;
  userId: string;
  userName: string;
}

export interface UpdateUserPayload {
  userId: string;
  payload: {
    addedRoles: Array<string>;
    removedRoles: Array<string>;
    actionedBy: string;
  };
}

export interface GetPayerUsersQuery {
  payerId: string;
  page: number;
  size: number;
  search?: string;
}

export interface GetPayerUserAuditLogsQuery {
  userId: string;
  page: number;
  size: number;
}

export interface UserAuditLog {
  id: number;
  userId: string;
  actionedBy: string;
  eventTimestamp: string;
  addedRoles: Array<string>;
  removedRoles: Array<string>;
  createdAt: string;
}

export interface AddPayerRequest {
  username: string;
  email: string;
  password: string;
  payerId: number;
  firstName: string;
  lastName: string;
}

export interface PayerUserUpdateRequest {
  userId: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  reason: string;
  updatedBy: string;
}

export interface UserStatusUpdateRequest {
  userId: string;
  enabled: boolean;
  reason: string;
  updatedBy: string;
}

export interface PasswordResetRequest {
  username: string;
}

export interface PasswordResetResponse {
  userId: string;
  username: string;
  email: string;
  payerId: number;
  payerName: string;
  temporaryPassword: string;
  expiryTime: string;
}
