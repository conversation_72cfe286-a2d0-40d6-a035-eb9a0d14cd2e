import React from "react";
import { Path, useFormContext, Controller } from "react-hook-form";
import Flatpickr from "react-flatpickr";
import clsx, { dateFlatPickrOptions } from "../utils";

interface Props<T extends string> extends React.InputHTMLAttributes<HTMLInputElement> {
  description?: string;
  label?: string;
  id?: string;
  className?: string;
  wrapperClassName?: string;
  required?: boolean;
  name?: T;
  disabled?: boolean;
  position?: "left" | "right" | "auto";
  /**
   * See - https://flatpickr.js.org/examples/#mindate-and-maxdate
   */
  maxDate?: string
}

const { static: _, maxDate, ...restFlatPickrOptions } = dateFlatPickrOptions

/**
 * The DateInput component is a wrapper around the Flatpickr component.
 * WARNING: Do not use a FieldWrapper around this component.
 */
export default function DateInput<T extends string>({
  name,
  type = "text",
  required,
  id = name,
  className = '',
  placeholder = '',
  wrapperClassName = '',
  description,
  disabled,
  label,
  position = "auto",
  maxDate,
  ...rest
}: Props<T>) {
  type Inputs = {
    [key in T]: string;
  };

  type Fields = Inputs;
  const { control, formState } = useFormContext<Fields>();

  const error = formState.errors?.[name];
  const message =
    error?.type === "required"
      ? "This field is required"
      : error?.message ?? "Invalid input";

  return (
    <div className={clsx("flex flex-col gap-1", wrapperClassName)}>
      {label && (
        <label htmlFor={id}>
          {label} {required && <span className="text-red-500"> *</span>}
        </label>
      )}

      {description && (
        <span className="text-gray-400 text-sm">{description}</span>
      )}

      <Controller 
        control={control}
        name={name as unknown as Path<Fields>}
        rules={{
          required: required,
        }}
        render={({ field: { onChange, ...fieldProps } }) => (
          <Flatpickr
            {...fieldProps}
            className={clsx(
              className,
              "border border-gray-300 rounded-md px-4",
            )}
            options={{
              ...restFlatPickrOptions,
              ...(maxDate && { maxDate: maxDate }),
              position: `auto ${position}`,
            }}
            placeholder={placeholder}
            onChange={(date, dateStr) => {
              onChange(dateStr);
            }}
            disabled={disabled}
          />
        )}
      />

      {error && <span className="text-xs text-red-500">{message}</span>}
    </div>
  );
}
