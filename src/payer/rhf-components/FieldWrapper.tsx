import React from "react";
import { useFormContext } from "react-hook-form";
import clsx from "../utils";
import { cn } from "~lib/utils/cn";

interface InputProps<T> {
  description?: string;
  label?: string;
  required?: boolean;
  id?: string;
  name: T;
  className?: string;
  descriptionClassName?: string;
  children: React.ReactNode;
}

export default function FieldWrapper<T extends string>({
  label,
  name,
  description,
  required,
  id = name,
  className = "",
  children,
  descriptionClassName,
}: InputProps<T>) {
  type Inputs = {
    [key in T]: string;
  };

  const { formState } = useFormContext<Inputs>();

  const childProps = {
    ...(name && { name }),
    ...(id && { id }),
    ...(required && { required: "This field is required" }),
  };

  const error = formState.errors?.[name];
  const message = error?.message ?? "Invalid input";

  // Throw error if more than one child is passed
  React.Children.only(children);

  // Inject props into child
  const child = React.isValidElement(children)
    ? React.cloneElement(children, { ...childProps })
    : undefined;

  return (
    <div className={clsx("flex flex-col gap-1", className)}>
      {label && (
        <label htmlFor={id}>
          {label} {required && <span className="text-red-500"> *</span>}
        </label>
      )}

      {description && (
        <span className={cn("text-sm text-gray-400", descriptionClassName)}>{description}</span>
      )}

      {child}

      <span className="text-xs text-red-500">{error ? message : <>&#8203;</>}</span>
    </div>
  );
}
