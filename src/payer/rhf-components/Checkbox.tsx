import React from "react";
import clsx from "../utils";

interface Props extends React.ComponentProps<"input"> {
  defaultValue?: string | number;
  className?: string;
  /**
   * Injected by FieldWrapper
   */
  name?: string;
  required?: boolean;
}

export default function Checkbox({
  defaultValue,
  name,
  required,
  className = '',
  ...rest
}: Props) {
  return (
    <input
      type="checkbox"
      {...rest}
      className={clsx(
        "rounded-lg",
        className,
      )}
    />
  );
}
