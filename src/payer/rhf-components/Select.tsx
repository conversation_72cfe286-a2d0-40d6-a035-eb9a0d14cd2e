import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import ReactSelect from 'react-select';

interface Props extends React.ComponentProps<typeof ReactSelect>{
  defaultValue?: string | number;
  className?: string;
  /**
   * Injected by FieldWrapper
   */
  name?: string;
  required?: boolean;
}

export default function Select({
  defaultValue,
  name,
  required,
  className = "",
  ...selectOptions
}: Props) {
  const { control } = useFormContext();

  const controllerProps = {
    ...(defaultValue && { defaultValue }),
    rules: required ? { required } : {},
  }

  interface Option {
    value: string | number;
    label: string;
  }

  // Remove manually configured options
  const { options, value, onChange, ...restOptions } = selectOptions;

  // TODO: Transform value to string
  // See - https://stackoverflow.com/questions/62795886/returning-correct-value-using-react-select-and-react-hook-form
  // and https://github.com/react-hook-form/react-hook-form/issues/997
  return (
    <Controller
      name={name}
      control={control}
      defaultValue={defaultValue}
      render={({ field: { onChange, value, ...restProps} }) => (
        <ReactSelect
          {...restOptions}
          value={options.find(c => (c as Option)?.value === value)}
          onChange={val => onChange((val as Option)?.value)} 
          options={options}
          isClearable={!required}
          className={className}
          {...restProps}
        />
      )}
      {...controllerProps}
    />
  );
}
