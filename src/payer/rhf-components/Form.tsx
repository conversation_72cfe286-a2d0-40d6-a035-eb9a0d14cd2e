import React, { FormEvent } from "react";
import { DeepPartial, useForm, FormProvider, UseFormReturn, UseFormHandleSubmit  } from "react-hook-form";

interface IFormProps<T> extends Omit<React.FormHTMLAttributes<HTMLFormElement>, "onSubmit"> {
  defaultValues?: DeepPartial<T>;
  onSubmit?: (data: T, e?: FormEvent<HTMLFormElement>) => void;
  children: React.ReactNode;
  className?: string;
  methods?: UseFormReturn<T>;
}

export function Form<T>({
  defaultValues,
  onSubmit = () => {},
  children,
  className = '',
  methods: _methods = {} as UseFormReturn<T>,
  ...props
}: IFormProps<T>) {
  let handleSubmit: UseFormHandleSubmit<T> | undefined

  const methods = useForm<T>({ defaultValues });

  if (typeof _methods?.handleSubmit === "function") {
    handleSubmit = _methods.handleSubmit;
  } else {
    handleSubmit = methods.handleSubmit;
  }

  return (
    // Let parent methods, if any, override current methods
    <FormProvider {...methods} {..._methods}>
      <form onSubmit={handleSubmit(onSubmit)} className={className} {...props}>
        {children}
      </form>
    </FormProvider>
  );
}
