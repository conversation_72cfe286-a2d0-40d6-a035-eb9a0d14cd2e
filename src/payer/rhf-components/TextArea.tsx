import React from "react";
import { Path, useFormContext, RegisterOptions } from "react-hook-form";
import clsx from "../utils";

interface InputProps<T extends string> extends React.InputHTMLAttributes<HTMLTextAreaElement> {
  options?: RegisterOptions
  id?: string;
  className?: string;
  wrapperClassName?: string;
  /**
   * Fields injected by FieldWrapper
   */
  required?: boolean;
  name?: T;
  rows?: number;
  cols?: number;
}

export default function TextArea<T extends string, V extends string | number>({
  name,
  options,
  type = "text",
  required: parentRequired,
  id = name,
  className = '',
  wrapperClassName = '',
  rows = 3,
  cols = 50,
  ...rest
}: InputProps<T>) {
  type Inputs = {
    [key in T]: V;
  };

  type Fields = Inputs;
  const { register } = useFormContext<Fields>();
  const required = parentRequired || options?.required
  const { pattern, ...restOptions } = options || {}

  const registerOptions = {
    ...restOptions,
    ...(required && { required }),
  }

  // const typeRegisterOptions = type === "number" ? {
  //   ...registerOptions,
  //   valueAsNumber: true as const,
  // } : registerOptions

  return (
      <textarea
        {...register(name as unknown as Path<Fields>, registerOptions)}
        {...rest}
        className={clsx(
          "w-full p-2 border border-gray-300 rounded-md",
          className,
        )}
        rows={rows}
        cols={cols}
      />
  );
}
