import React from "react";
import { Path, useFormContext, RegisterOptions } from "react-hook-form";
import clsx from "../utils";

interface InputProps<T extends string> extends React.InputHTMLAttributes<HTMLInputElement> {
  options?: RegisterOptions
  id?: string;
  className?: string;
  wrapperClassName?: string;
  type?: "text" | "number" | "email" | "password" | "tel" | "url" | "search" | "hidden";
  /**
   * Fields injected by FieldWrapper
   */
  required?: boolean;
  name?: T;
}

export default function Input<T extends string, V extends string | number>({
  name,
  options,
  type = "text",
  required: parentRequired,
  id = name,
  className = '',
  wrapperClassName = '',
  ...rest
}: InputProps<T>) {
  type Inputs = {
    [key in T]: V;
  };

  type Fields = Inputs;
  const { register } = useFormContext<Fields>();
  const required = parentRequired || options?.required
  const { pattern, ...restOptions } = options || {}

  const registerOptions = {
    ...restOptions,
    valueAsDate: false as const,
    ...(required && { required: "This field is required" }),
  }

  const registerOptionsWithType = type === "number" ? {
    ...registerOptions,
    valueAsNumber: true as const,
  } : registerOptions


  return (
      <input
        {...register(name as unknown as Path<Fields>, registerOptionsWithType)}
        {...rest}
        className={clsx(
          "border border-gray-300 rounded-md p-2",
          className,
        )}
        type={type}
      />
  );
}
