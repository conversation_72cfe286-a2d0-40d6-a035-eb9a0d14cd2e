import { describe, it, expect } from "vitest";
import {
  nameValidation,
  descriptionValidation,
  userGroupSchema,
  customRoleSchema,
  denyPolicySchema,
  nameSchema,
  descriptionSchema,
  reasonSchema,
  NAME_MIN_LENGTH,
  NAME_MAX_LENGTH,
  DESCRIPTION_MIN_LENGTH,
  DESCRIPTION_MAX_LENGTH,
} from "./validation-schemas";

describe("Validation Schemas", () => {
  describe("nameValidation", () => {
    it("should accept valid names", () => {
      expect(() => nameValidation.parse("Valid Name")).not.toThrow();
      expect(() => nameValidation.parse("Test")).not.toThrow();
      expect(() => nameValidation.parse("A".repeat(50))).not.toThrow();
    });

    it("should reject names that are too short", () => {
      expect(() => nameValidation.parse("")).toThrow();
      expect(() => nameValidation.parse("AB")).toThrow();
    });

    it("should reject names that are too long", () => {
      expect(() => nameValidation.parse("A".repeat(101))).toThrow();
    });

    it("should trim whitespace", () => {
      const result = nameValidation.parse("  Test Name  ");
      expect(result).toBe("Test Name");
    });
  });

  describe("descriptionValidation", () => {
    it("should accept valid descriptions", () => {
      expect(() => descriptionValidation.parse("Valid description")).not.toThrow();
      expect(() => descriptionValidation.parse("Test")).not.toThrow();
      expect(() => descriptionValidation.parse("A".repeat(250))).not.toThrow();
    });

    it("should reject descriptions that are too short", () => {
      expect(() => descriptionValidation.parse("")).toThrow();
      expect(() => descriptionValidation.parse("AB")).toThrow();
    });

    it("should reject descriptions that are too long", () => {
      expect(() => descriptionValidation.parse("A".repeat(501))).toThrow();
    });

    it("should trim whitespace", () => {
      const result = descriptionValidation.parse("  Test description  ");
      expect(result).toBe("Test description");
    });
  });

  describe("Consolidated schemas", () => {
    it("should validate user group schema", () => {
      expect(() =>
        userGroupSchema.parse({ name: "Valid Group", description: "Valid description" }),
      ).not.toThrow();
      expect(() =>
        userGroupSchema.parse({ name: "AB", description: "Valid description" }),
      ).toThrow();
      expect(() => userGroupSchema.parse({ name: "Valid Group", description: "AB" })).toThrow();
    });

    it("should validate custom role schema", () => {
      expect(() =>
        customRoleSchema.parse({ name: "Valid Role", description: "Valid description" }),
      ).not.toThrow();
      expect(() =>
        customRoleSchema.parse({ name: "AB", description: "Valid description" }),
      ).toThrow();
      expect(() => customRoleSchema.parse({ name: "Valid Role", description: "AB" })).toThrow();
    });

    it("should validate deny policy schema", () => {
      expect(() =>
        denyPolicySchema.parse({ name: "Valid Policy", reason: "Valid reason" }),
      ).not.toThrow();
      expect(() => denyPolicySchema.parse({ name: "AB", reason: "Valid reason" })).toThrow();
      expect(() => denyPolicySchema.parse({ name: "Valid Policy", reason: "AB" })).toThrow();
    });
  });

  describe("Individual field schemas", () => {
    it("should validate name schema", () => {
      expect(() => nameSchema.parse({ name: "Valid Name" })).not.toThrow();
      expect(() => nameSchema.parse({ name: "AB" })).toThrow();
    });

    it("should validate description schema", () => {
      expect(() => descriptionSchema.parse({ description: "Valid description" })).not.toThrow();
      expect(() => descriptionSchema.parse({ description: "AB" })).toThrow();
    });

    it("should validate reason schema", () => {
      expect(() => reasonSchema.parse({ reason: "Valid reason" })).not.toThrow();
      expect(() => reasonSchema.parse({ reason: "AB" })).toThrow();
    });
  });

  describe("Constants", () => {
    it("should have correct validation limits", () => {
      expect(NAME_MIN_LENGTH).toBe(3);
      expect(NAME_MAX_LENGTH).toBe(100);
      expect(DESCRIPTION_MIN_LENGTH).toBe(3);
      expect(DESCRIPTION_MAX_LENGTH).toBe(500);
    });
  });
});
