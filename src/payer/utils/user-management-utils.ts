import { useGetUserEffectiveRolesQuery } from "../api/features/membershipApi";
import {
  ALL_CARE_ROLES,
  ALL_CLAIMS_ADJUDICATION_ROLES,
  ALL_CLAIMS_ROLES,
  ALL_FINANCE_AND_ACCOUNTING_ROLES,
  ALL_MEMBERSHIP_ROLES,
  ALL_PROVIDERS_ROLES,
  CompositeRole,
  DEFAULT_ROLES,
  FULL_ACCESS_ROLES,
  LEGACY_MODULE_ACTIVATION_ROLES,
  NON_PAYER_PORTAL_ROLES,
  PAYER_SUPER_ADMIN_EFFECTIVE_ROLES,
  PREAUTH_ROLES_NOT_DENIABLE_VIA_POLICY,
  PREAUTH_ROLES_TO_NOT_ASSIGN_DIRECTLY,
} from "../components/access-management/data";
import { PermissionResponse } from "../lib/types/access-control/role";
import UserService from "../services/UserService";

export interface DenyPolicyPermission {
  name: string;
  description: string;
  roleName: string;
}

export interface CustomRolePermission {
  name: string;
  description: string;
  roleName: string;
}

/**
 * Hook to get the current user's effective permissions
 * @returns An object containing the user's effective permissions and a function to check if the user has specific roles
 */
export function useUserEffectivePermissions() {
  const userId = UserService.getSubject();
  const {
    data: userEffectiveRoles,
    isLoading,
    error,
  } = useGetUserEffectiveRolesQuery(userId || "", {
    skip: !userId,
  });

  const effectivePermissionNames =
    userEffectiveRoles?.effectivePermissions?.map((permission) => permission.name) || [];

  const hasPermission = (roles: string[]) => {
    if (!userEffectiveRoles || isLoading) {
      return roles.some((role) => UserService.hasRole([role]));
    }

    return roles.some((role) => effectivePermissionNames.includes(role));
  };

  return {
    effectivePermissions: userEffectiveRoles?.effectivePermissions || [],
    effectivePermissionNames,
    hasPermission,
    isLoading,
    error,
  };
}

export function mapPermissionsToDenyPolicyPermissions(
  permissions: Array<PermissionResponse>,
): Array<DenyPolicyPermission> {
  const result: Array<DenyPolicyPermission> = [];

  for (const permission of permissions) {
    // skip full access roles and default system roles
    // deny policies should not contain composite roles
    if (
      FULL_ACCESS_ROLES.includes(permission.name) ||
      DEFAULT_ROLES.includes(permission.name) ||
      PAYER_SUPER_ADMIN_EFFECTIVE_ROLES.includes(permission.name) ||
      LEGACY_MODULE_ACTIVATION_ROLES.includes(permission.name) ||
      NON_PAYER_PORTAL_ROLES.includes(permission.name) ||
      PREAUTH_ROLES_NOT_DENIABLE_VIA_POLICY.includes(permission.name)
    ) {
      continue;
    }

    const descriptiveName = mapRoleNameToDescriptiveName(permission.name);

    result.push({
      name: descriptiveName,
      description: permission.description || "",
      roleName: permission.name,
    });
  }

  return arrangePermissionsAlphabetically(result);
}

export function mapPermissionsToCustomRolePermissions(
  permissions: Array<PermissionResponse>,
): Array<DenyPolicyPermission> {
  const result: Array<DenyPolicyPermission> = [];

  for (const permission of permissions) {
    // skip default system roles
    if (
      FULL_ACCESS_ROLES.includes(permission.name) ||
      DEFAULT_ROLES.includes(permission.name) ||
      PAYER_SUPER_ADMIN_EFFECTIVE_ROLES.includes(permission.name) ||
      LEGACY_MODULE_ACTIVATION_ROLES.includes(permission.name) ||
      NON_PAYER_PORTAL_ROLES.includes(permission.name) ||
      PREAUTH_ROLES_TO_NOT_ASSIGN_DIRECTLY.includes(permission.name)
    ) {
      continue;
    }

    const descriptiveName = mapRoleNameToDescriptiveName(permission.name);

    result.push({
      name: descriptiveName,
      description: permission.description || "",
      roleName: permission.name,
    });
  }

  return arrangePermissionsAlphabetically(result);
}

export function mapPermissionsToValidDisplayPermissions(
  permissions: Array<PermissionResponse>,
): Array<PermissionResponse> {
  const result: Array<PermissionResponse> = [];

  for (const permission of permissions) {
    // skip default system roles
    if (
      DEFAULT_ROLES.includes(permission.name) ||
      PAYER_SUPER_ADMIN_EFFECTIVE_ROLES.includes(permission.name) ||
      LEGACY_MODULE_ACTIVATION_ROLES.includes(permission.name) ||
      NON_PAYER_PORTAL_ROLES.includes(permission.name)
    ) {
      continue;
    }

    const descriptiveName = mapRoleNameToDescriptiveName(permission.name);

    result.push({
      name: descriptiveName,
      description: permission.description || "",
    });
  }

  return arrangePermissionsAlphabetically(result);
}

export function mapRoleNameToDescriptiveName(roleName: string): string {
  const roleNameMap: Record<string, string> = {
    // Scheme Module
    SCHEME_OVERVIEW_ROLE: "Scheme Overview",

    // HR Roles
    HR: "HR",
    HR_LVL_1: "Level 1 HR",

    // Membership Module
    MEMBERSHIP_ROLE: "Membership Module Access",
    MEMBERSHIP_ACTIVATE_ROLE: "Member Activation",
    MEMBERSHIP_DEACTIVATE_ROLE: "Member Deactivation",
    MEMBERSHIP_SUSPEND_ROLE: "Member Suspension",
    MEMBERSHIP_BENEFIT_TOPUP_ROLE: "Benefit Top-up",
    MEMBERSHIP_BENEFIT_TRANSFER_ROLE: "Benefit Transfer",
    MEMBERSHIP_BENEFIT_SUSPEND_ROLE: "Benefit Suspension",
    MEMBERSHIP_DETACH_BIOMETRICS_ROLE: "Biometrics Detachment",
    MEMBERSHIP_INQUIRY_ROLE: "Member Inquiry",
    MEMBERSHIP_EDIT_ROLE: "Member Editing",
    MEMBERSHIP_MASS_ACTIVATION_ROLE: "Mass Activation",
    MEMBERSHIP_MASS_DEACTIVATION_ROLE: "Mass Deactivation",
    MEMBERSHIP_MASS_EDIT_ROLE: "Mass Edit",
    MEMBERSHIP_REPORTS_ROLE: "Membership Reports",

    // Provider Module
    PROVIDER_ROLE: "Provider Module Access",
    PROVIDER_VIEW_ROLE: "Provider View",
    PROVIDER_RESTRICTIONS_ROLE: "Provider Restrictions",

    // Care Module
    CARE_ROLE: "Care Module Access",
    CARE_PREAUTH_REVIEW_ROLE: "Pre-authorization Review",
    CARE_VISIT_REACTIVATION_ROLE: "Visit Reactivation",

    // Claims Module
    CLAIMS_ROLE: "Claims Module Access",
    CLAIMS_REIMBURSEMENT_ROLE: "Reimbursement",
    CLAIMS_OFFLCT_ROLE: "Offline Visits",
    CLAIMS_VETTING_ROLE: "Claims Vetting",
    CLAIMS_BATCHING_ROLE: "Claims Batching",
    CLAIMS_REVERSAL_ROLE: "Claims Reversals",
    CLAIMS_REPORTS_ROLE: "Claims Reports",

    // Claims Adjudication Module
    CLAIMS_ADJUDICATION_ROLE: "Claims Adjudication Module Access",
    CLAIMS_ADJUDICATION_BATCHING_ROLE: "Claim Adjudication Batching",
    CLAIMS_ADJUDICATION_VETTING_ROLE: "Claim Adjudication Vetting",
    CLAIMS_ADJUDICATION_AI_ROLE: "AI Adjudication",

    // Finance & Accounting Module
    FINANCE_AND_ACCOUNTING_ROLE: "Finance & Accounting Module Access",
    FINANCE_AND_ACCOUNTING_VOUCHERING_ROLE: "Vouchering",
    FINANCE_AND_ACCOUNTING_SETTLEMENT_ROLE: "Settlement",
    FINANCE_AND_ACCOUNTING_REMITTANCE_ROLE: "Remittance",
    FINANCE_AND_ACCOUNTING_CREDIT_ANALYSIS_ROLE: "Credit Analysis",

    // Composite roles
    ALL_MEMBERSHIP_ROLE: "All Membership Roles",
    ALL_PROVIDERS_ROLE: "All Provider Roles",
    ALL_CARE_ROLE: "All Care Roles",
    ALL_CLAIMS_ROLE: "All Claims Roles",
    ALL_CLAIMS_ADJUDICATION_ROLE: "All Claims Adjudication Roles",
    ALL_FINANCE_AND_ACCOUNTING_ROLE: "All Finance & Accounting Roles",

    // Payer super admin roles
    PAYER_SUPER_ADMIN: "Payer Super Admin",
    USER_MANAGEMENT_ROLES_ASSIGNMENT_ROLE: "User Management Roles Assignment",
    USER_MANAGEMENT_AUDIT_LOGS_ROLE: "User Management Audit Logs",

    // Atomic Approval Roles
    CARE_PREAUTH_APPROVE_ALL_ROLE: "Pre-Auth: Approve All Benefits",
    CARE_PREAUTH_APPROVE_DENTAL_ROLE: "Pre-Auth: Approve Dental",
    CARE_PREAUTH_APPROVE_OPTICAL_ROLE: "Pre-Auth: Approve Optical",
    CARE_PREAUTH_APPROVE_OUTPATIENT_ROLE: "Pre-Auth: Approve Outpatient",
    CARE_PREAUTH_APPROVE_INPATIENT_ROLE: "Pre-Auth: Approve Inpatient",
    CARE_PREAUTH_APPROVE_MATERNITY_ROLE: "Pre-Auth: Approve Maternity",

    // Atomic Top-Up Roles
    CARE_PREAUTH_TOPUP_ALL_ROLE: "Pre-Auth: Top-Up All Benefits",
    CARE_PREAUTH_TOPUP_DENTAL_ROLE: "Pre-Auth: Top-Up Dental",
    CARE_PREAUTH_TOPUP_OPTICAL_ROLE: "Pre-Auth: Top-Up Optical",
    CARE_PREAUTH_TOPUP_OUTPATIENT_ROLE: "Pre-Auth: Top-Up Outpatient",
    CARE_PREAUTH_TOPUP_INPATIENT_ROLE: "Pre-Auth: Top-Up Inpatient",
    CARE_PREAUTH_TOPUP_MATERNITY_ROLE: "Pre-Auth: Top-Up Maternity",

    // Atomic View-Only Roles
    CARE_PREAUTH_VIEW_ALL_ROLE: "Pre-Auth: View All Benefits",
    CARE_PREAUTH_VIEW_DENTAL_ROLE: "Pre-Auth: View Dental",
    CARE_PREAUTH_VIEW_OPTICAL_ROLE: "Pre-Auth: View Optical",
    CARE_PREAUTH_VIEW_OUTPATIENT_ROLE: "Pre-Auth: View Outpatient",
    CARE_PREAUTH_VIEW_INPATIENT_ROLE: "Pre-Auth: View Inpatient",
    CARE_PREAUTH_VIEW_MATERNITY_ROLE: "Pre-Auth: View Maternity",

    // Composite Approval & View Roles
    CARE_PREAUTH_APPROVE_AND_VIEW_ALL_ROLE: "Pre-Auth: Approve & View All Benefits",
    CARE_PREAUTH_APPROVE_AND_VIEW_DENTAL_ROLE: "Pre-Auth: Approve & View Dental",
    CARE_PREAUTH_APPROVE_AND_VIEW_OPTICAL_ROLE: "Pre-Auth: Approve & View Optical",
    CARE_PREAUTH_APPROVE_AND_VIEW_OUTPATIENT_ROLE: "Pre-Auth: Approve & View Outpatient",
    CARE_PREAUTH_APPROVE_AND_VIEW_INPATIENT_ROLE: "Pre-Auth: Approve & View Inpatient",
    CARE_PREAUTH_APPROVE_AND_VIEW_MATERNITY_ROLE: "Pre-Auth: Approve & View Maternity",

    // Composite Top-Up & View Roles
    CARE_PREAUTH_TOPUP_AND_VIEW_ALL_ROLE: "Pre-Auth: Top-Up & View All Benefits",
    CARE_PREAUTH_TOPUP_AND_VIEW_DENTAL_ROLE: "Pre-Auth: Top-Up & View Dental",
    CARE_PREAUTH_TOPUP_AND_VIEW_OPTICAL_ROLE: "Pre-Auth: Top-Up & View Optical",
    CARE_PREAUTH_TOPUP_AND_VIEW_OUTPATIENT_ROLE: "Pre-Auth: Top-Up & View Outpatient",
    CARE_PREAUTH_TOPUP_AND_VIEW_INPATIENT_ROLE: "Pre-Auth: Top-Up & View Inpatient",
    CARE_PREAUTH_TOPUP_AND_VIEW_MATERNITY_ROLE: "Pre-Auth: Top-Up & View Maternity",

    // Reports
    CARE_PREAUTH_GET_DIAGNOSIS_REPORTS_ROLE: "Pre-Auth: Get Diagnosis Reports",
    CARE_REPORTS_RETRIEVAL_ROLE: "Pre-Auth: Get Care Reports",
  };

  return roleNameMap[roleName] || roleName;
}

export function mapCompositeRoleToConstituentRoles(roleName: CompositeRole): Set<string> {
  const compositeRoleMap: Record<string, Array<string>> = {
    ALL_MEMBERSHIP_ROLE: ALL_MEMBERSHIP_ROLES,
    ALL_PROVIDERS_ROLE: ALL_PROVIDERS_ROLES,
    ALL_CARE_ROLE: ALL_CARE_ROLES,
    ALL_CLAIMS_ROLE: ALL_CLAIMS_ROLES,
    ALL_CLAIMS_ADJUDICATION_ROLE: ALL_CLAIMS_ADJUDICATION_ROLES,
    ALL_FINANCE_AND_ACCOUNTING_ROLE: ALL_FINANCE_AND_ACCOUNTING_ROLES,
  };

  return new Set(compositeRoleMap[roleName] || []);
}

export function arrangePermissionsAlphabetically<T extends { name: string }>(
  permissions: Array<T>,
): Array<T> {
  return [...permissions].sort((a, b) => a.name.localeCompare(b.name));
}
