/**
 * Downloads a file from a given URL and assigns a filename to it.
 *
 * @param {string} url - The URL of the file to be downloaded.
 * @param {string} filename - The desired name for the downloaded file.
 */
export function downloadFile(url: string, filename: string) {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename; // This sets the name of the file to be downloaded
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
  