import { AccessModuleName, RealmRole } from "../components/access-management/AccessModuleCard";
import {
  accessModuleDetails,
  ALL_CARE_ROLES,
  ALL_CLAIMS_ADJUDICATION_ROLES,
  ALL_CLAIMS_ROLES,
  ALL_FINANCE_AND_ACCOUNTING_ROLES,
  ALL_MEMBERSHIP_ROLES,
  ALL_PROVIDERS_ROLES,
  ALL_SCHEME_ROLES,
  DEFAULT_ROLES,
  PAYER_SUPER_ADMIN_EFFECTIVE_ROLES,
} from "../components/access-management/data";
import { BatchingCriteria } from "../components/claimsBatching/BatchingCriteriaSelector";
import { BadgeColor } from "../components/ui/Badge";
import { PreAuthStatus } from "../lib/types/care/preAuth";
import { AdjudicationStatus } from "../lib/types/claims/adjudication";
import { BatchAllocationStatus } from "../lib/types/claims/batchedInvoices";
import {
  InvoiceBatchStatus,
  InvoiceStatus,
  InvoiceVettingStatus,
} from "../lib/types/claims/invoice";

export function getDifferenceInDays(date1: string, date2: string): number {
  function parseDate(date: string): Date | null {
    const parts = date.split(".");
    if (parts.length !== 3) return null;

    const [dayStr, monthStr, yearStr] = parts;
    const day = Number(dayStr);
    const month = Number(monthStr);
    const year = Number(yearStr);

    if (isNaN(day) || isNaN(month) || isNaN(year)) return null;

    return new Date(year, month - 1, day);
  }

  const parsedDate1 = parseDate(date1);
  const parsedDate2 = parseDate(date2);

  if (!parsedDate1 || !parsedDate2) {
    throw new Error("Invalid date format. Please use dd.mm.yyyy.");
  }

  const differenceInTime = Math.abs(parsedDate2.getTime() - parsedDate1.getTime());
  const differenceInDays = Math.ceil(differenceInTime / (1000 * 60 * 60 * 24));
  return differenceInDays;
}

export function getDifferenceInDaysFinance(date1: string, date2: string): number {
  function parseDate(date: string): Date | null {
    const parts = date.split(".");
    if (parts.length !== 3) return null;

    const [dayStr, monthStr, yearStr] = parts;
    const day = Number(dayStr);
    const month = Number(monthStr);
    const year = Number(yearStr);

    if (isNaN(day) || isNaN(month) || isNaN(year)) return null;

    return new Date(year, month - 1, day);
  }

  const parsedDate1 = parseDate(date1);
  const parsedDate2 = parseDate(date2);

  if (!parsedDate1 || !parsedDate2) {
    throw new Error("Invalid date format. Please use dd.mm.yyyy.");
  }

  const differenceInTime = parsedDate2.getTime() - parsedDate1.getTime();
  const differenceInDays = Math.ceil(differenceInTime / (1000 * 60 * 60 * 24));
  const financeAge = differenceInDays - 30;
  return financeAge;
}

export function formatLogDateToMonthYear(dateString: string): string {
  const [_day, month, year] = dateString.split(".");
  const monthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  const monthName = monthNames[parseInt(month as string, 10) - 1];
  return `${monthName}, ${year}`;
}

function getDayWithSuffix(day: number): string {
  // Special case for 11th, 12th, 13th
  if (day >= 11 && day <= 13) return `${day}th`;
  switch (day % 10) {
    case 1:
      return `${day}st`;
    case 2:
      return `${day}nd`;
    case 3:
      return `${day}rd`;
    default:
      return `${day}th`;
  }
}

export function formatDateWithSuffix(date: Date): string {
  const day = date.getDate();
  const month = date.toLocaleString("en-GB", { month: "long" });
  const year = date.getFullYear();

  const dayWithSuffix = getDayWithSuffix(day);

  return `${dayWithSuffix} ${month}, ${year}`;
}

export function formatDateVerbose(date: Date): string {
  const options: Intl.DateTimeFormatOptions = { day: "2-digit", month: "long", year: "numeric" };

  const dateParts = new Intl.DateTimeFormat("en-GB", options).formatToParts(date).reduce(
    (acc, part) => {
      acc[part.type] = part.value;
      return acc;
    },
    {} as Record<string, string>,
  );
  return `${dateParts["day"]} ${dateParts["month"]}, ${dateParts["year"]}`;
}

export const isValidId = (id: number | string) => {
  if (typeof id === "string") {
    if (id === "") {
      return false;
    }
    return !id.toLocaleLowerCase().includes("select") && !isNaN(Number(id)) && Number(id) !== 0;
  }
  return !isNaN(Number(id)) && id !== 0;
};

export const isValidFilterName = (filter: string) =>
  filter !== "" && !filter.toLocaleLowerCase().includes("select");

export function isValidBatchingCriteria(criteria: unknown): criteria is BatchingCriteria {
  if (typeof criteria !== "string") return false;
  return Object.values(BatchingCriteria).includes(criteria as BatchingCriteria);
}

export function isValidInvoiceBatchStatus(status: unknown): status is InvoiceBatchStatus {
  if (typeof status !== "string") return false;
  return ["UnVetted", "InProgress", "Completed", "Deleted"].includes(status);
}

export function isValidBatchAllocationStatus(status: unknown): status is BatchAllocationStatus {
  if (typeof status !== "string") return false;
  return ["UnAllocated", "Allocated"].includes(status);
}

export function isValidBatchStatus(status: unknown): status is InvoiceBatchStatus {
  if (typeof status !== "string") return false;
  return ["BATCHED", "NOT_BATCHED"].includes(status);
}

export function isValidInvoiceStatus(status: unknown): status is InvoiceStatus {
  if (typeof status !== "string") return false;
  return [
    "REJECTED",
    "BALANCE_DEDUCTED",
    "DIAGNOSIS_ADDED",
    "DOCUMENTS_ADDED",
    "PENDED",
    "SENT",
    "BALANCE_ADDED",
    "REVERSED",
    "DISPATCHED",
    "CANCELLED",
    "PROFORMA",
  ].includes(status);
}

export function isValidQueryId(id: unknown) {
  if (typeof id === "string") {
    return !id.toLocaleLowerCase().includes("select");
  }
  return true;
}

export function getClaimTextAndColorFromVettingStatus(
  vettingStatus: InvoiceVettingStatus,
): [string, BadgeColor] {
  switch (vettingStatus) {
    case "PARTIAL":
      return ["Partial", "green"];
    case "APPROVED":
      return ["Approved", "green"];
    case "PENDING":
      return ["Pending", "yellow"];
    case "DECLINED":
      return ["Declined", "red"];
    case "BATCHED":
      return ["Batched", "blue"];
    default:
      return ["Unknown", "gray"];
  }
}

export function getPreauthTextAndColorFromStatus(
  preauthStatus: PreAuthStatus,
): [string, BadgeColor] {
  switch (preauthStatus) {
    case "ACTIVE":
      return ["Active", "green"];
    case "INACTIVE":
      return ["Incomplete", "gray"];
    case "PENDING":
      return ["Pending", "yellow"];
    case "DECLINED":
      return ["Declined", "red"];
    case "AUTHORIZED":
      return ["Authorized", "green"];
    case "CLAIMED":
      return ["Claimed", "green"];
    case "CANCELLED":
      return ["Cancelled", "gray"];
    case "DRAFT":
      return ["Draft", "blue"];
    case "EXPIRED":
      return ["Expired", "gray"];
    case "WITHDRAWN":
      return ["Withdrawn", "gray"];
    default:
      return ["Unknown", "gray"];
  }
}

interface ModifiedRealmRoles {
  addedRoles: Array<RealmRole>;
  removedRoles: Array<RealmRole>;
}

export function getModuleRoleNames(moduleName: AccessModuleName): Array<string> {
  switch (moduleName) {
    case "Schemes Module":
      return ALL_SCHEME_ROLES;
    case "Membership Module":
      return ALL_MEMBERSHIP_ROLES;
    case "Provider Module":
      return ALL_PROVIDERS_ROLES;
    case "Care Module":
      return ALL_CARE_ROLES;
    case "Claims Module":
      return ALL_CLAIMS_ROLES;
    case "Claims Adjudication Module":
      return ALL_CLAIMS_ADJUDICATION_ROLES;
    case "Finance & Accounting Module":
      return ALL_FINANCE_AND_ACCOUNTING_ROLES;
    default:
      return [];
  }
}

export function findModifiedRealmRoles(
  addedRoles: Array<string>,
  removedRoles: Array<string>,
): ModifiedRealmRoles {
  // filter out default roles as they are common to all users and not meaningful in audit logs
  const filteredAddedRoles = addedRoles.filter((role) => !DEFAULT_ROLES.includes(role));
  const filteredRemovedRoles = removedRoles.filter((role) => !DEFAULT_ROLES.includes(role));

  const allRealmRoles: Array<RealmRole> = [];

  accessModuleDetails.forEach((module) => {
    allRealmRoles.push(...module.realmRoles);
  });

  allRealmRoles.push({
    name: "Payer Super Admin",
    description: "Full administrative access to all payer portal features and user management",
    roleNames: PAYER_SUPER_ADMIN_EFFECTIVE_ROLES,
  });

  const isExactMatch = (selectedRoles: string[], roleNames: string[]) =>
    selectedRoles.length === roleNames.length &&
    selectedRoles.every((role) => roleNames.includes(role));

  const isPartialMatch = (selectedRoles: string[], roleNames: string[]) =>
    roleNames.some((roleName) => selectedRoles.includes(roleName));

  const added = allRealmRoles.filter(
    (role) =>
      isExactMatch(filteredAddedRoles, role.roleNames) ||
      (filteredAddedRoles.length > 0 && isPartialMatch(filteredAddedRoles, role.roleNames)),
  );

  const removed = allRealmRoles.filter(
    (role) =>
      isExactMatch(filteredRemovedRoles, role.roleNames) ||
      (filteredRemovedRoles.length > 0 && isPartialMatch(filteredRemovedRoles, role.roleNames)),
  );

  return {
    addedRoles: added,
    removedRoles: removed,
  };
}

export function findModuleNameByRoles(roles: Array<string>): AccessModuleName {
  if (ALL_MEMBERSHIP_ROLES.some((role) => roles.includes(role))) {
    return "Membership Module";
  } else if (ALL_PROVIDERS_ROLES.some((role) => roles.includes(role))) {
    return "Provider Module";
  } else if (ALL_CARE_ROLES.some((role) => roles.includes(role))) {
    return "Care Module";
  } else if (ALL_CLAIMS_ROLES.some((role) => roles.includes(role))) {
    return "Claims Module";
  } else if (ALL_SCHEME_ROLES.some((role) => roles.includes(role))) {
    return "Schemes Module";
  } else if (ALL_FINANCE_AND_ACCOUNTING_ROLES.some((role) => roles.includes(role))) {
    return "Finance & Accounting Module";
  } else if (ALL_CLAIMS_ADJUDICATION_ROLES.some((role) => roles.includes(role))) {
    return "Claims Adjudication Module";
  } else {
    return "Unknown Module" as AccessModuleName;
  }
}

export function getAiAdjudicationTextAndColorFromStatus(
  adjudicationStatus: AdjudicationStatus,
): [string, BadgeColor] {
  switch (adjudicationStatus) {
    case "Adjudicating":
      return ["Adjudicating", "blue"];
    case "Complete":
      return ["Complete", "green"];
    case "Pending":
      return ["Pending", "yellow"];
    case "Errored":
      return ["Errored", "red"];
    case "AwaitingManualReview":
      return ["Awaiting Manual Review", "gray"];
    default:
      return ["Unknown", "gray"];
  }
}

export function getClaimsReversalTextAndColorFromStatus(
  adjudicationStatus: InvoiceStatus,
): [string, BadgeColor] {
  switch (adjudicationStatus) {
    case "REVERSED":
      return ["Reversed", "red"];
    case "BALANCE_DEDUCTED":
      return ["Balance Deducted", "green"];
    case "BALANCE_ADDED":
      return ["Balance Added", "green"];
    case "SENT":
      return ["Sent", "blue"];
    case "PENDED":
      return ["Pended", "yellow"];
    case "CANCELLED":
      return ["Cancelled", "red"];
    case "DIAGNOSIS_ADDED":
      return ["Diagnosis Added", "blue"];
    case "DISPATCHED":
      return ["Dispatched", "blue"];
    case "DOCUMENTS_ADDED":
      return ["Documents Added", "blue"];
    case "PROFORMA":
      return ["Proforma", "yellow"];
    case "REJECTED":
      return ["Rejected", "red"];
    default:
      return ["Unknown", "gray"];
  }
}
