export function convertDateString(dateString: string, separator = `/`): string {
  let date: Date | null = null;

  // Check if the input is in dd/mm/yyyy format
  if (dateString.includes("/")) {
    const [day, month, year] = dateString.split("/").map(Number);
    if (year && month && day) {
      date = new Date(year, month - 1, day); // Month is 0-indexed
    }
  } else {
    // Assume the input is an ISO string
    date = new Date(dateString);
  }

  // Handle cases where the date could be invalid
  if (!date || isNaN(date.getTime())) {
    throw new Error("Invalid date string provided");
  }

  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0"); // Months are 0-indexed
  const year = date.getFullYear();

  return `${day}${separator}${month}${separator}${year}`;
}
