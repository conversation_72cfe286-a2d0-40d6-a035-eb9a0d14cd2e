import { z } from "zod";

export const NAME_MIN_LENGTH = 3;
export const NAME_MAX_LENGTH = 100;
export const DESCRIPTION_MIN_LENGTH = 3;
export const DESCRIPTION_MAX_LENGTH = 500;
export const REASON_MIN_LENGTH = 5;

export const nameValidation = z
  .string()
  .trim()
  .min(NAME_MIN_LENGTH, `Name must be at least ${NAME_MIN_LENGTH} characters`)
  .max(NAME_MAX_LENGTH, `Name must not exceed ${NAME_MAX_LENGTH} characters`)
  .transform((val) => val.trim());

export const descriptionValidation = z
  .string()
  .trim()
  .min(DESCRIPTION_MIN_LENGTH, `Description must be at least ${DESCRIPTION_MIN_LENGTH} characters`)
  .max(DESCRIPTION_MAX_LENGTH, `Description must not exceed ${DESCRIPTION_MAX_LENGTH} characters`)
  .transform((val) => val.trim());

export const reasonValidation = z
  .string()
  .trim()
  .min(REASON_MIN_LENGTH, `Reason must be at least ${REASON_MIN_LENGTH} characters`)
  .max(DESCRIPTION_MAX_LENGTH, `Reason must not exceed ${DESCRIPTION_MAX_LENGTH} characters`)
  .transform((val) => val.trim());

export const userGroupSchema = z.object({
  name: nameValidation,
  description: descriptionValidation,
});

export const customRoleSchema = z.object({
  name: nameValidation,
  description: descriptionValidation,
});

export const denyPolicySchema = z.object({
  name: nameValidation,
  reason: reasonValidation,
});

export const nameSchema = z.object({
  name: nameValidation,
});

export const descriptionSchema = z.object({
  description: descriptionValidation,
});

export const reasonSchema = z.object({
  reason: reasonValidation,
});

export type UserGroupInput = z.infer<typeof userGroupSchema>;
export type CustomRoleInput = z.infer<typeof customRoleSchema>;
export type DenyPolicyInput = z.infer<typeof denyPolicySchema>;
export type NameInput = z.infer<typeof nameSchema>;
export type DescriptionInput = z.infer<typeof descriptionSchema>;
export type ReasonInput = z.infer<typeof reasonSchema>;
