/**
 * Converts an object of key-value pairs into a query string format.
 *
 * This function takes an object where the keys are strings and the values are either strings, numbers, or arrays of strings/numbers.
 * It creates a `URLSearchParams` instance, appends each key-value pair to it, and returns the resulting
 * query string. Keys with `undefined` or empty values are skipped.
 *
 * @param {Record<string, number | string | string[] | undefined>} params - An object representing the query parameters.
 * @returns {string} The query string representation of the provided parameters.
 *
 * @example
 * ```typescript
 * const params = { search: 'test', page: 2, filter: ['active', 'pending'] };
 * const queryString = createSearchParamsString(params);
 * console.log(queryString); // Output: 'search=test&page=2&filter=active&filter=pending'
 * ```
 */
export function createSearchParamsString(params: Record<string, any>): string {
  const searchParams: URLSearchParams = new URLSearchParams();

  for (const [key, value] of Object.entries(params)) {
    if (Array.isArray(value)) {
      // Append each element of the array with the same key
      if (value.length > 0) {
        for (const item of value) {
          searchParams.append(key, item.toString());
        }
      }
    } else if (value !== undefined && value !== "" && value !== 0) {
      // Append single values
      searchParams.append(key, value.toString());
    }
  }

  return searchParams.toString();
}
