export type PlainObject = { [key: string]: unknown } | null;

export default function checkIfObjectsAreEqual(obj1: PlainObject, obj2: PlainObject): boolean {
  if (obj1 === obj2) return true; // Handles identical references

  if (typeof obj1 !== "object" || obj1 === null || typeof obj2 !== "object" || obj2 === null) {
    return false; // If either is not an object or is null, they are not equal
  }

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) return false;

  for (const key of keys1) {
    if (!checkIfObjectsAreEqual(obj1[key] as PlainObject, obj2[key] as PlainObject)) {
      return false;
    }
  }

  return true;
}
