import { createContext, useContext } from "react";
import { UserNotification } from "~lib/api/types";

export type ActiveNotification = UserNotification;

type NotificationsState = {
  isNotificationsPanelOpen: boolean;
  setIsNotificationsPanelOpen: React.Dispatch<React.SetStateAction<boolean>>;
  closeNotificationsPanel(): void;
  notificationsCount: number | null;
  activeNotification: UserNotification | null | undefined;
  setActiveNotification: React.Dispatch<React.SetStateAction<UserNotification | null | undefined>>;
  handleMarkNotificationsAsRead(notificationIds: number[], markAsRead?: boolean): void;
};

const NotificationsContext = createContext<NotificationsState | null>(null);

export function useNotifications() {
  const context = useContext(NotificationsContext);

  if (!context)
    throw new Error("useNotification can only be used in a NotificationContext provider");

  return context;
}

export default NotificationsContext;
