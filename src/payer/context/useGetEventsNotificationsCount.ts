import { useEffect, useState } from "react";
import { baseUrl } from "~lib/constants";
import { useAppSelector } from "../store/hooks";

// Custom hook to subscribe to notification count updates via SSE (Server-Sent Events)
export default function useGetEventsNotificationsCount() {
  // Get the auth token from Redux state
  const authToken = useAppSelector((state) => state.auth.token);

  // Local state to store the total notification count
  const [notificationsCount, setNotificationsCount] = useState<number | null>(null);

  useEffect(() => {
    // Don't initiate SSE if there's no token
    if (!authToken) return;

    // Create an AbortController to allow cleanup of the fetch request
    const controller = new AbortController();

    // Immediately invoke an async function to handle the SSE connection
    (async () => {
      try {
        // Connect to the SSE endpoint using fetch
        const response = await fetch(`${baseUrl}/api/v1/events/notifications/subscribe`, {
          headers: {
            Authorization: `Bearer ${authToken}`, // Pass auth token
            Accept: "text/event-stream", // Tell server we expect SSE
          },
          signal: controller.signal, // Attach abort controller to support cleanup
        });

        // If the response is not OK, throw an error
        if (!response.ok) throw new Error("Failed to connect SSE stream");

        // Ensure response has a readable body
        if (!response.body) throw new Error("Received response has no body");

        // Get a reader from the response body stream
        const reader = response.body.getReader();
        const decoder = new TextDecoder("utf-8"); // Used to decode chunks of bytes into strings
        let buffer = ""; // Holds incomplete chunks across iterations

        // Continuously read from the stream
        // eslint-disable-next-line no-constant-condition
        while (true) {
          const { done, value } = await reader.read(); // Read next chunk from stream
          if (done) break; // Exit loop if stream is closed

          // Decode the chunk and add to buffer
          buffer += decoder.decode(value, { stream: true });

          // Split the buffer into lines; keep the last partial line in buffer
          const lines = buffer.split("\n");
          buffer = lines.pop() as string;

          // Parse each complete line of the stream
          for (const line of lines) {
            // 1) Match both “data:” and “data::”
            if (line.startsWith("data:") || line.startsWith("data::")) {
              // Strip off any "data:" or "data::" prefix and whitespace.
              const payload = line.replace(/^data:{1,2}\s*/, "");

              // If it’s a ping/keep-alive, skip it
              if (/^keep-?alive$/i.test(payload)) {
                continue;
              }

              // Only parse if it starts with an object or array marker
              if (!/^[{[]/.test(payload)) {
                console.warn("Skipping non-JSON SSE payload:", payload);
                continue;
              }

              try {
                const data = JSON.parse(payload);
                console.log("Incoming SSE:", data.newNotificationCount);
                setNotificationsCount(data.newNotificationCount);
              } catch (err) {
                console.warn("Failed to parse SSE JSON:", payload, err);
              }
            }
          }
        }
      } catch (error) {
        // Log any error related to connecting/reading/parsing the stream
        console.error("Stream error:", error);
      }
    })();

    // Cleanup function to abort the fetch request when component unmounts or token changes
    return () => controller.abort();
  }, [authToken]); // Re-run effect if authToken changes

  // Return the current notifications count to consuming components
  return { notificationsCount };
}
