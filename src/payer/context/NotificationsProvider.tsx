import { ReactNode, useState } from "react";
import { useMarkNotificationsAsReadMutation } from "~lib/api";
import { UserNotification } from "~lib/api/types";
import { handleTryCatchError } from "../utils/handleTryCatchError";
import NotificationsContext from "./NotificationsContext";
import useGetEventsNotificationsCount from "./useGetEventsNotificationsCount";

type Props = {
  children: ReactNode;
};

export default function NotificationsProvider({ children }: Props) {
  const [isNotificationsPanelOpen, setIsNotificationsPanelOpen] = useState(false);

  const [activeNotification, setActiveNotification] = useState<UserNotification | null>();

  // rtk query hooks
  const { notificationsCount } = useGetEventsNotificationsCount();
  const [markNotificationsAsRead] = useMarkNotificationsAsReadMutation();

  console.log("Notification count: ", notificationsCount);

  // functions
  function closeNotificationsPanel() {
    setIsNotificationsPanelOpen(false);
  }

  async function handleMarkNotificationsAsRead(notificationIds: number[], markAsRead = true) {
    try {
      await markNotificationsAsRead({ body: { notificationIds, markAsRead } }).unwrap();
    } catch (error) {
      handleTryCatchError(error);
    }
  }

  return (
    <NotificationsContext.Provider
      value={{
        isNotificationsPanelOpen,
        setIsNotificationsPanelOpen,
        closeNotificationsPanel,
        notificationsCount,
        activeNotification,
        setActiveNotification,
        handleMarkNotificationsAsRead,
      }}
    >
      {children}
    </NotificationsContext.Provider>
  );
}
