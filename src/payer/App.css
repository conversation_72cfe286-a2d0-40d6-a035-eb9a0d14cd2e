.stepper-item::after {
  content: "";
  width: 100%;
  border-bottom: 1px solid rgba(180, 180, 180, 0.3);
  height: 1px;
  margin: 0 1rem;
}

input[id^="react-select"]:focus {
  box-shadow: none !important;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield; /*Firefox */
  appearance: textfield; /* standard */
}

.flatpickr-wrapper {
  width: 100%;
}

select,
input[type="text"] {
  background-color: white;
}
