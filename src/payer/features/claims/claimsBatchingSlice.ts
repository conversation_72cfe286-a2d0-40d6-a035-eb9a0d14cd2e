import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { BatchingCriteria } from "../../components/claimsBatching/BatchingCriteriaSelector";
import { ClaimsMenuView } from "../../components/claimsBatching/ClaimsViewSelector";
import { SettingItems } from "../../components/claimsBatching/modals/batchSettings/BatchSettingsItem";
import { BatchAllocationStatus, InvoiceBatchStatus } from "../../lib/types/claims/batchedInvoices";
import { InvoiceData } from "../../lib/types/claims/invoice";
import { MembershipScheme } from "../../lib/types/membership/memberScheme";

interface ClaimsBatchingState {
  selectedProviderId: string;
  selectedRegion: string;
  selectedBenefitId: string;
  selectedAgeBandId: string;
  selectedBatchingOption: BatchingCriteria | null | string;
  catalogIds: number[];
  isSelectAllActive: boolean;
  checkedClaimsIds: string[];
  selectedClaimsSum: number;
  allClaimsToBeBatched: InvoiceData[];
  isClaimsBatchedSuccessfullyModalOpen: boolean;
  isBatchClaimsModalOpen: boolean;
  activeClaimsMenu: ClaimsMenuView;
  batchAllocationStatus: BatchAllocationStatus | string;
  batchVettingStatus: InvoiceBatchStatus | string;
  reassignSingleUserReason: string;
  reassignMultipleUsersReason: string;
  isUsersAssignedSuccessfullyModalOpen: boolean;
  isSingleUserReassignedSuccessfullyModalOpen: boolean;
  isMultipleUsersReassignedSuccessfullyModalOpen: boolean;
  selectedBatchId: number;
  activeBatchSettingsItem: SettingItems;
  selectedScheme: MembershipScheme;
  providersSearchQuery: string;
  assignedBatchId: number;
  totalSystemClaims: InvoiceData[];
  selectedBatchingServiceGroup: string;
  adjudicationBatchingStartDate: string;
  adjudicationBatchingEndDate: string;
}

const initialState: ClaimsBatchingState = {
  selectedProviderId: "",
  selectedRegion: "",
  selectedBenefitId: "",
  selectedAgeBandId: "",
  selectedBatchingOption: null,
  catalogIds: [],
  isSelectAllActive: false,
  checkedClaimsIds: [],
  selectedClaimsSum: 0,
  allClaimsToBeBatched: [],
  isClaimsBatchedSuccessfullyModalOpen: false,
  isBatchClaimsModalOpen: false,
  activeClaimsMenu: ClaimsMenuView.IndividualClaims,
  batchAllocationStatus: "",
  batchVettingStatus: "",
  reassignSingleUserReason: "",
  reassignMultipleUsersReason: "",
  isUsersAssignedSuccessfullyModalOpen: false,
  isSingleUserReassignedSuccessfullyModalOpen: false,
  isMultipleUsersReassignedSuccessfullyModalOpen: false,
  selectedBatchId: 0,
  activeBatchSettingsItem: SettingItems.BatchDetails,
  selectedScheme: {} as MembershipScheme,
  providersSearchQuery: "",
  assignedBatchId: 0,
  totalSystemClaims: [],
  selectedBatchingServiceGroup: "",
  adjudicationBatchingStartDate: "",
  adjudicationBatchingEndDate: "",
};

const claimsBatchingSlice = createSlice({
  name: "claimsBatching",
  initialState,
  reducers: {
    setSelectedProviderId(state, action: PayloadAction<string>) {
      state.selectedProviderId = action.payload;
    },
    setSelectedRegion(state, action: PayloadAction<string>) {
      state.selectedRegion = action.payload;
    },
    setSelectedBenefitId(state, action: PayloadAction<string>) {
      state.selectedBenefitId = action.payload;
    },
    setSelectedAgeBandId(state, action: PayloadAction<string>) {
      state.selectedAgeBandId = action.payload;
    },
    setSelectedBatchingOption(state, action: PayloadAction<BatchingCriteria | null | string>) {
      state.selectedBatchingOption = action.payload;
    },
    setCatalogIds(state, action: PayloadAction<number[]>) {
      state.catalogIds = action.payload;
    },
    setIsSelectAllActive(state, action: PayloadAction<boolean>) {
      state.isSelectAllActive = action.payload;
    },
    setCheckedClaimsIds(state, action: PayloadAction<string[]>) {
      state.checkedClaimsIds = action.payload;
    },
    setSelectedClaimsSum(state, action: PayloadAction<number>) {
      state.selectedClaimsSum = action.payload;
    },
    setAllClaimsToBeBatched(state, action: PayloadAction<InvoiceData[]>) {
      state.allClaimsToBeBatched = action.payload;
    },
    setIsClaimsBatchedSuccessfullyModalOpen(state, action: PayloadAction<boolean>) {
      state.isClaimsBatchedSuccessfullyModalOpen = action.payload;
    },
    setIsBatchClaimsModalOpen(state, action: PayloadAction<boolean>) {
      state.isBatchClaimsModalOpen = action.payload;
    },
    setActiveClaimsMenu(state, action: PayloadAction<ClaimsMenuView>) {
      state.activeClaimsMenu = action.payload;
    },
    setBatchAllocationStatus(state, action: PayloadAction<BatchAllocationStatus | string>) {
      state.batchAllocationStatus = action.payload;
    },
    setBatchVettingStatus(state, action: PayloadAction<InvoiceBatchStatus | string>) {
      state.batchVettingStatus = action.payload;
    },
    setReassignSingleUserReason(state, action: PayloadAction<string>) {
      state.reassignSingleUserReason = action.payload;
    },
    setReassignMultipleUsersReason(state, action: PayloadAction<string>) {
      state.reassignMultipleUsersReason = action.payload;
    },
    setIsUsersAssignedSuccessfullyModalOpen(state, action: PayloadAction<boolean>) {
      state.isUsersAssignedSuccessfullyModalOpen = action.payload;
    },
    setIsSingleUserReassignedSuccessfullyModalOpen(state, action: PayloadAction<boolean>) {
      state.isSingleUserReassignedSuccessfullyModalOpen = action.payload;
    },
    setIsMultipleUsersReassignedSuccessfullyModalOpen(state, action: PayloadAction<boolean>) {
      state.isMultipleUsersReassignedSuccessfullyModalOpen = action.payload;
    },
    setSelectedBatchId(state, action: PayloadAction<number>) {
      state.selectedBatchId = action.payload;
    },
    setActiveBatchSettingsItem(state, action: PayloadAction<SettingItems>) {
      state.activeBatchSettingsItem = action.payload;
    },
    setSelectedScheme(state, action: PayloadAction<MembershipScheme>) {
      state.selectedScheme = action.payload;
    },
    setProvidersSearchQuery(state, action: PayloadAction<string>) {
      state.providersSearchQuery = action.payload;
    },
    setAssignedBatchId(state, action: PayloadAction<number>) {
      state.assignedBatchId = action.payload;
    },
    setTotalSystemClaims(state, action: PayloadAction<InvoiceData[]>) {
      state.totalSystemClaims = action.payload;
    },
    setSelectedBatchingServiceGroup(state, action: PayloadAction<string>) {
      state.selectedBatchingServiceGroup = action.payload;
    },
    setAdjudicationBatchingStartDate(state, action: PayloadAction<string>) {
      state.adjudicationBatchingStartDate = action.payload;
    },
    setAdjudicationBatchingEndDate(state, action: PayloadAction<string>) {
      state.adjudicationBatchingEndDate = action.payload;
    },
  },
});

export const {
  setCatalogIds,
  setSelectedBatchingOption,
  setSelectedAgeBandId,
  setSelectedBenefitId,
  setSelectedProviderId,
  setSelectedRegion,
  setIsSelectAllActive,
  setCheckedClaimsIds,
  setSelectedClaimsSum,
  setAllClaimsToBeBatched,
  setIsClaimsBatchedSuccessfullyModalOpen,
  setIsBatchClaimsModalOpen,
  setActiveClaimsMenu,
  setBatchAllocationStatus,
  setBatchVettingStatus,
  setReassignMultipleUsersReason,
  setReassignSingleUserReason,
  setIsUsersAssignedSuccessfullyModalOpen,
  setIsMultipleUsersReassignedSuccessfullyModalOpen,
  setIsSingleUserReassignedSuccessfullyModalOpen,
  setSelectedBatchId,
  setActiveBatchSettingsItem,
  setSelectedScheme,
  setProvidersSearchQuery,
  setAssignedBatchId,
  setTotalSystemClaims,
  setSelectedBatchingServiceGroup,
  setAdjudicationBatchingEndDate,
  setAdjudicationBatchingStartDate,
} = claimsBatchingSlice.actions;

export default claimsBatchingSlice.reducer;
