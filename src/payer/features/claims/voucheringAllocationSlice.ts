import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  AllocationMethod,
  AllocationSelectionMethod,
} from "../../components/vouchering/modals/ui/DetailedCheckbox";

interface VoucheringAllocationState {
  selectedAllocationMethod: AllocationMethod | null;
  selectedAllocationSelectionMethod: AllocationSelectionMethod | null;
  isPartialAllocation: boolean;
  amountNotAllocated: boolean;
  isVoucherCreatedSuccessfullyModalOpen: boolean;
}

const initialState: VoucheringAllocationState = {
  selectedAllocationMethod: null,
  selectedAllocationSelectionMethod: null,
  isPartialAllocation: false,
  amountNotAllocated: true,
  isVoucherCreatedSuccessfullyModalOpen: false,
};

const voucheringAllocationSlice = createSlice({
  name: "voucheringAllocation",
  initialState,
  reducers: {
    setSelectedAllocationMethod: (state, action: PayloadAction<AllocationMethod | null>) => {
      state.selectedAllocationMethod = action.payload;
    },
    setSelectedAllocationSelectionMethod: (
      state,
      action: PayloadAction<AllocationSelectionMethod | null>,
    ) => {
      state.selectedAllocationSelectionMethod = action.payload;
    },
    setIsPartialAllocation: (state, action: PayloadAction<boolean>) => {
      state.isPartialAllocation = action.payload;
    },
    setAmountNotAllocated: (state, action: PayloadAction<boolean>) => {
      state.amountNotAllocated = action.payload;
    },
    setVoucherCreatedSuccessfullyModalOpen: (state, action: PayloadAction<boolean>) => {
      state.isVoucherCreatedSuccessfullyModalOpen = action.payload;
    },
  },
});

export const {
  setSelectedAllocationMethod,
  setSelectedAllocationSelectionMethod,
  setIsPartialAllocation,
  setVoucherCreatedSuccessfullyModalOpen,
  setAmountNotAllocated,
} = voucheringAllocationSlice.actions;

export default voucheringAllocationSlice.reducer;
