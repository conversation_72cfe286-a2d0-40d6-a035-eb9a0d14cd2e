import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { VoucheringProgress } from "../../components/vouchering/VoucheringProgressBar";
import { InvoiceData } from "../../lib/types/claims/invoice";
import { VoucheringCriteria } from "../../lib/types/vouchering/claimVoucheringFilter";

export interface VoucheringState {
  catalogIds: number[];
  selectedAgeBandId: number;
  selectedBenefitId: number;
  selectedRegion: string;
  selectedAccount: string;
  selectedSchemeIds: (string | number)[];
  vettedClaims: InvoiceData[];
  isVoucheringSelectAllActive: boolean;
  voucheringCheckedClaimsIds: string[];
  voucheringPayableAmount: number;
  selectedScheme: string;
  isAllocationConfirmationModalOpen: boolean;
  isVoucheringAllocationModalOpen: boolean;
  isVoucherConfirmationModalOpen: boolean;
  isAllocationExceededWarningModalOpen: boolean;
  uniqueCheckedClaimsIds: Array<string | number>;
  allocatedAmount: number;
  selectedSchemeName: string;
  selectedBenefitName: string;
  selectedAgeBandName: string;
  selectedAccountName: string;
  voucherAmount: number;
  voucheringProgress: VoucheringProgress;
  mainVoucheringCriteria: VoucheringCriteria | null;
  isCalculatingVoucherAmount: boolean;
  isVoucheringClaimsSuccess: boolean;
  isInterimSaveSuccessfulModalOpen: boolean;
  isVoucheringAgingCriteriaSuccessfulModalOpen: boolean;
  isVoucheringEntireInvoicesSuccessfulModalOpen: boolean;
  isBackButtonVisible: boolean;
  totalSystemClaimsVouchering: Array<InvoiceData>;
}

const initialState: VoucheringState = {
  catalogIds: [],
  selectedAgeBandId: 0,
  selectedBenefitId: 0,
  selectedRegion: "",
  selectedAccount: "",
  selectedSchemeIds: [],
  vettedClaims: [],
  isVoucheringSelectAllActive: false,
  voucheringCheckedClaimsIds: [],
  voucheringPayableAmount: 0,
  selectedScheme: "",
  isAllocationConfirmationModalOpen: false,
  isVoucheringAllocationModalOpen: false,
  isVoucherConfirmationModalOpen: false,
  isAllocationExceededWarningModalOpen: false,
  uniqueCheckedClaimsIds: [],
  allocatedAmount: 0,
  selectedSchemeName: "",
  selectedBenefitName: "",
  selectedAgeBandName: "",
  selectedAccountName: "",
  voucherAmount: 0,
  voucheringProgress: VoucheringProgress.Unallocated,
  mainVoucheringCriteria: null,
  isCalculatingVoucherAmount: false,
  isVoucheringClaimsSuccess: false,
  isInterimSaveSuccessfulModalOpen: false,
  isVoucheringAgingCriteriaSuccessfulModalOpen: false,
  isVoucheringEntireInvoicesSuccessfulModalOpen: false,
  isBackButtonVisible: false,
  totalSystemClaimsVouchering: [],
};

const voucheringSlice = createSlice({
  name: "vouchering",
  initialState,
  reducers: {
    setCatalogIds(state, action: PayloadAction<number[]>) {
      state.catalogIds = action.payload;
    },
    setSelectedAgeBandId(state, action: PayloadAction<number>) {
      state.selectedAgeBandId = action.payload;
    },
    setSelectedBenefitId(state, action: PayloadAction<number>) {
      state.selectedBenefitId = action.payload;
    },
    setSelectedRegion(state, action: PayloadAction<string>) {
      state.selectedRegion = action.payload;
    },
    setSelectedAccount(state, action: PayloadAction<string>) {
      state.selectedAccount = action.payload;
    },
    setSelectedSchemeIds(state, action: PayloadAction<(string | number)[]>) {
      state.selectedSchemeIds = action.payload;
    },
    setVettedClaims(state, action: PayloadAction<InvoiceData[]>) {
      state.vettedClaims = action.payload;
    },
    setIsVoucheringSelectAllActive(state, action: PayloadAction<boolean>) {
      state.isVoucheringSelectAllActive = action.payload;
    },
    setVoucheringCheckedClaimsIds(state, action: PayloadAction<string[]>) {
      state.voucheringCheckedClaimsIds = action.payload;
    },
    setVoucheringPayableAmount(state, action: PayloadAction<number>) {
      state.voucheringPayableAmount = action.payload;
    },
    setSelectedScheme(state, action: PayloadAction<string>) {
      state.selectedScheme = action.payload;
    },
    setIsAllocationConfirmationModalOpen(state, action: PayloadAction<boolean>) {
      state.isAllocationConfirmationModalOpen = action.payload;
    },
    setIsVoucheringAllocationModalOpen(state, action: PayloadAction<boolean>) {
      state.isVoucheringAllocationModalOpen = action.payload;
    },
    setIsVoucherConfirmationModalOpen(state, action: PayloadAction<boolean>) {
      state.isVoucherConfirmationModalOpen = action.payload;
    },
    setIsAllocationExceededWarningModalOpen(state, action: PayloadAction<boolean>) {
      state.isAllocationExceededWarningModalOpen = action.payload;
    },
    setUniqueCheckedClaimsIds(state, action: PayloadAction<Array<string | number>>) {
      state.uniqueCheckedClaimsIds = action.payload;
    },
    setAllocatedAmount(state, action: PayloadAction<number>) {
      state.allocatedAmount = action.payload;
    },
    setSelectedSchemeName(state, action: PayloadAction<string>) {
      state.selectedSchemeName = action.payload;
    },
    setSelectedBenefitName(state, action: PayloadAction<string>) {
      state.selectedBenefitName = action.payload;
    },
    setSelectedAgeBandName(state, action: PayloadAction<string>) {
      state.selectedAgeBandName = action.payload;
    },
    setVoucherAmount(state, action: PayloadAction<number>) {
      state.voucherAmount = action.payload;
    },
    setVoucheringProgress(state, action: PayloadAction<VoucheringProgress>) {
      state.voucheringProgress = action.payload;
    },
    setMainVoucheringCriteria(state, action: PayloadAction<VoucheringCriteria | null>) {
      state.mainVoucheringCriteria = action.payload;
    },
    setIsCalculatingVoucherAmount(state, action: PayloadAction<boolean>) {
      state.isCalculatingVoucherAmount = action.payload;
    },
    setSelectedAccountName(state, action: PayloadAction<string>) {
      state.selectedAccountName = action.payload;
    },
    setIsVoucheringClaimsSuccess(state, action: PayloadAction<boolean>) {
      state.isVoucheringClaimsSuccess = action.payload;
    },
    setIsInterimSaveSuccessfulModalOpen(state, action: PayloadAction<boolean>) {
      state.isInterimSaveSuccessfulModalOpen = action.payload;
    },
    setIsVoucheringAgingCriteriaSuccessfulModalOpen(state, action: PayloadAction<boolean>) {
      state.isVoucheringAgingCriteriaSuccessfulModalOpen = action.payload;
    },
    setIsVoucheringEntireInvoicesSuccessfulModalOpen(state, action: PayloadAction<boolean>) {
      state.isVoucheringEntireInvoicesSuccessfulModalOpen = action.payload;
    },
    setIsBackButtonVisible(state, action: PayloadAction<boolean>) {
      state.isBackButtonVisible = action.payload;
    },
    setTotalSystemClaimsVouchering(state, action: PayloadAction<Array<InvoiceData>>) {
      state.totalSystemClaimsVouchering = action.payload;
    },
  },
});

export const {
  setCatalogIds,
  setSelectedAgeBandId,
  setSelectedBenefitId,
  setSelectedRegion,
  setSelectedAccount,
  setSelectedSchemeIds,
  setVettedClaims,
  setIsVoucheringSelectAllActive,
  setVoucheringCheckedClaimsIds,
  setVoucheringPayableAmount,
  setSelectedScheme,
  setIsAllocationConfirmationModalOpen,
  setIsVoucheringAllocationModalOpen,
  setIsVoucherConfirmationModalOpen,
  setIsAllocationExceededWarningModalOpen,
  setUniqueCheckedClaimsIds,
  setAllocatedAmount,
  setSelectedSchemeName,
  setSelectedBenefitName,
  setSelectedAgeBandName,
  setVoucherAmount,
  setVoucheringProgress,
  setMainVoucheringCriteria,
  setIsCalculatingVoucherAmount,
  setSelectedAccountName,
  setIsVoucheringClaimsSuccess,
  setIsInterimSaveSuccessfulModalOpen,
  setIsVoucheringAgingCriteriaSuccessfulModalOpen,
  setIsVoucheringEntireInvoicesSuccessfulModalOpen,
  setIsBackButtonVisible,
  setTotalSystemClaimsVouchering,
} = voucheringSlice.actions;

export default voucheringSlice.reducer;
