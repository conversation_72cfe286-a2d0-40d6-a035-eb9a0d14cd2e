import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { VoucherEditingMethod } from "../../components/vouchering/modals/ui/DetailedCheckbox";

interface VoucherEditingState {
  voucherId: number;
  isVoucherEditingModalOpen: boolean;
  invoiceIds: Array<number | string>;
  voucherAccountId: string;
  voucherAccountName: string;
  currentVoucherAmount: number;
  isUpdatingVoucherSuccess: boolean;
  voucherReversalWarningModalOpen: boolean;
  selectedEditingMethod: VoucherEditingMethod | null;
}

const initialState: VoucherEditingState = {
  voucherId: 0,
  isVoucherEditingModalOpen: false,
  invoiceIds: [],
  voucherAccountId: "",
  voucherAccountName: "",
  currentVoucherAmount: 0,
  isUpdatingVoucherSuccess: false,
  voucherReversalWarningModalOpen: false,
  selectedEditingMethod: null,
};

const voucherEditingSlice = createSlice({
  name: "voucherEditing",
  initialState,
  reducers: {
    setVoucherId(state, action: PayloadAction<number>) {
      state.voucherId = action.payload;
    },
    setIsVoucherEditingModalOpen(state, action: PayloadAction<boolean>) {
      state.isVoucherEditingModalOpen = action.payload;
    },
    setInvoiceIds(state, action: PayloadAction<Array<number | string>>) {
      state.invoiceIds = action.payload;
    },
    setVoucherAccountId(state, action: PayloadAction<string>) {
      state.voucherAccountId = action.payload;
    },
    setVoucherAccountName(state, action: PayloadAction<string>) {
      state.voucherAccountName = action.payload;
    },
    setIsUpdatingVoucherSuccess(state, action: PayloadAction<boolean>) {
      state.isUpdatingVoucherSuccess = action.payload;
    },
    setVoucherReversalWarningModalOpen(state, action: PayloadAction<boolean>) {
      state.voucherReversalWarningModalOpen = action.payload;
    },
    setCurrentVoucherAmount(state, action: PayloadAction<number>) {
      state.currentVoucherAmount = action.payload;
    },
    setSelectedEditingMethod(state, action: PayloadAction<VoucherEditingMethod | null>) {
      state.selectedEditingMethod = action.payload;
    },
  },
});

export const {
  setVoucherId,
  setIsVoucherEditingModalOpen,
  setInvoiceIds,
  setVoucherAccountId,
  setVoucherAccountName,
  setIsUpdatingVoucherSuccess,
  setVoucherReversalWarningModalOpen,
  setCurrentVoucherAmount,
  setSelectedEditingMethod,
} = voucherEditingSlice.actions;

export default voucherEditingSlice.reducer;
