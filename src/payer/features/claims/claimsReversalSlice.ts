import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { InvoiceData } from "../../lib/types/claims/invoice";

export enum InvoiceReversalType {
  FULL = "Full Reversal",
  PARTIAL = "Partial Reversal",
}

interface ClaimsReversalState {
  isFullReversalModalOpen: boolean;
  isPartialReversalModalOpen: boolean;
  selectedInvoice: InvoiceData | null;
  isReverseSentClaimsWarningModalOpen: boolean;
  reverseSentClaimsReversalType: InvoiceReversalType | null;
  shouldIgnoreReversalErrorsForPayerSentClaims: boolean;
}

const initialState: ClaimsReversalState = {
  isFullReversalModalOpen: false,
  isPartialReversalModalOpen: false,
  selectedInvoice: null,
  isReverseSentClaimsWarningModalOpen: false,
  reverseSentClaimsReversalType: null,
  shouldIgnoreReversalErrorsForPayerSentClaims: false,
};

const claimsReversalSlice = createSlice({
  name: "claimsReversal",
  initialState,
  reducers: {
    setIsFullReversalModalOpen: (state, action: PayloadAction<boolean>) => {
      state.isFullReversalModalOpen = action.payload;
    },
    setIsPartialReversalModalOpen: (state, action: PayloadAction<boolean>) => {
      state.isPartialReversalModalOpen = action.payload;
    },
    setSelectedInvoice: (state, action: PayloadAction<InvoiceData | null>) => {
      state.selectedInvoice = action.payload;
    },
    setIsReverseSentClaimsWarningModalOpen: (state, action: PayloadAction<boolean>) => {
      state.isReverseSentClaimsWarningModalOpen = action.payload;
    },
    setReverseSentClaimsReversalType(state, action: PayloadAction<InvoiceReversalType | null>) {
      state.reverseSentClaimsReversalType = action.payload;
    },
    resetReversalState: () => {
      return initialState;
    },
    setShouldIgnoreReversalErrorsForPayerSentClaims(state, action: PayloadAction<boolean>) {
      state.shouldIgnoreReversalErrorsForPayerSentClaims = action.payload;
    },
  },
});

export const {
  setIsFullReversalModalOpen,
  setIsPartialReversalModalOpen,
  setSelectedInvoice,
  setIsReverseSentClaimsWarningModalOpen,
  setReverseSentClaimsReversalType,
  resetReversalState,
  setShouldIgnoreReversalErrorsForPayerSentClaims,
} = claimsReversalSlice.actions;

export default claimsReversalSlice.reducer;
