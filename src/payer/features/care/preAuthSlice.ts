import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { MembershipProvider } from "../../lib/types/membership/memberProvider";
import { MembershipScheme } from "../../lib/types/membership/memberScheme";

interface PreAuthState {
  selectedPreAuthScheme: MembershipScheme;
  selectedPreAuthProvider: MembershipProvider;
  preAuthProvidersSearchQuery: string;
}

const initialState: PreAuthState = {
  selectedPreAuthScheme: {} as MembershipScheme,
  selectedPreAuthProvider: {} as MembershipProvider,
  preAuthProvidersSearchQuery: "",
};

const preAuthSlice = createSlice({
  name: "preAuth",
  initialState,
  reducers: {
    setSelectedPreAuthScheme: (state, action: PayloadAction<MembershipScheme>) => {
      state.selectedPreAuthScheme = action.payload;
    },
    setSelectedPreAuthProvider: (state, action: PayloadAction<MembershipProvider>) => {
      state.selectedPreAuthProvider = action.payload;
    },
    setPreAuthProvidersSearchQuery: (state, action: PayloadAction<string>) => {
      state.preAuthProvidersSearchQuery = action.payload;
    },
  },
});

export const {
  setSelectedPreAuthScheme,
  setSelectedPreAuthProvider,
  setPreAuthProvidersSearchQuery,
} = preAuthSlice.actions;

export default preAuthSlice.reducer;
