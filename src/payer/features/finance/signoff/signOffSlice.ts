import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { DateRange } from "../../../components/signoff/DurationButton";
import { InvoiceData } from "../../../lib/types/claims/invoice";

type ValuePiece = Date | null;

type DateValue = ValuePiece | [ValuePiece, ValuePiece];

interface SignOffState {
  isFiltersViewOpen: boolean;
  selectedSignOffAccount: string;
  selectedSignOffScheme: string;
  isStartDatePickerOpen: boolean;
  isEndDatePickerOpen: boolean;
  fromDate: DateValue | string;
  toDate: DateValue | string;
  activeDateRange: DateRange | string;
  signOffCheckedClaimsIds: string[];
  isSignOffSelectAllActive: boolean;
  signOffSelectedClaimsSum: number;
  allClaimsToBeSignedOff: Array<InvoiceData>;
  signOffSelectedAccountName: string;
  signOffSelectedSchemeName: string;
  totalSystemSignOffClaims: Array<InvoiceData>;
}

const initialState: SignOffState = {
  isFiltersViewOpen: false,
  selectedSignOffAccount: "",
  selectedSignOffScheme: "",
  isStartDatePickerOpen: false,
  isEndDatePickerOpen: false,
  fromDate: "",
  toDate: "",
  activeDateRange: "",
  signOffCheckedClaimsIds: [],
  signOffSelectedClaimsSum: 0,
  isSignOffSelectAllActive: false,
  allClaimsToBeSignedOff: [],
  signOffSelectedAccountName: "",
  signOffSelectedSchemeName: "",
  totalSystemSignOffClaims: [],
};

const signOffSlice = createSlice({
  name: "signOff",
  initialState,
  reducers: {
    setIsFiltersViewOpen: (state, action: PayloadAction<boolean>) => {
      state.isFiltersViewOpen = action.payload;
    },
    setSelectedSignOffAccount: (state, action: PayloadAction<string>) => {
      state.selectedSignOffAccount = action.payload;
    },
    setSelectedSignOffScheme: (state, action: PayloadAction<string>) => {
      state.selectedSignOffScheme = action.payload;
    },
    setIsStartDatePickerOpen: (state, action: PayloadAction<boolean>) => {
      state.isStartDatePickerOpen = action.payload;
    },
    setFromDate: (state, action: PayloadAction<DateValue | string>) => {
      state.fromDate = action.payload;
    },
    setToDate: (state, action: PayloadAction<DateValue | string>) => {
      state.toDate = action.payload;
    },
    setActiveDateRange: (state, action: PayloadAction<DateRange | string>) => {
      state.activeDateRange = action.payload;
    },
    setIsEndDatePickerOpen: (state, action: PayloadAction<boolean>) => {
      state.isEndDatePickerOpen = action.payload;
    },
    setSignOffCheckedClaimsIds: (state, action: PayloadAction<string[]>) => {
      state.signOffCheckedClaimsIds = action.payload;
    },
    setIsSignOffSelectAllActive: (state, action: PayloadAction<boolean>) => {
      state.isSignOffSelectAllActive = action.payload;
    },
    setSignOffSelectedClaimsSum: (state, action: PayloadAction<number>) => {
      state.signOffSelectedClaimsSum = action.payload;
    },
    setAllClaimsToBeSignedOff: (state, action: PayloadAction<InvoiceData[]>) => {
      state.allClaimsToBeSignedOff = action.payload;
    },
    setSelectedSignOffAccountName: (state, action: PayloadAction<string>) => {
      state.signOffSelectedAccountName = action.payload;
    },
    setSelectedSignOffSchemeName: (state, action: PayloadAction<string>) => {
      state.signOffSelectedSchemeName = action.payload;
    },
    setTotalSystemSignOffClaims: (state, action: PayloadAction<InvoiceData[]>) => {
      state.totalSystemSignOffClaims = action.payload;
    },
  },
});

export const {
  setIsFiltersViewOpen,
  setSelectedSignOffAccount,
  setSelectedSignOffScheme,
  setIsStartDatePickerOpen,
  setFromDate,
  setToDate,
  setActiveDateRange,
  setIsEndDatePickerOpen,
  setSignOffCheckedClaimsIds,
  setIsSignOffSelectAllActive,
  setSignOffSelectedClaimsSum,
  setAllClaimsToBeSignedOff,
  setSelectedSignOffAccountName,
  setSelectedSignOffSchemeName,
  setTotalSystemSignOffClaims,
} = signOffSlice.actions;

export default signOffSlice.reducer;
