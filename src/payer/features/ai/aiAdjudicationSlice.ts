import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { MembershipScheme } from "../../lib/types/membership/memberScheme";
import { AdjudicationStatus } from "../../lib/types/claims/adjudication";

interface AIPreauthState {
  aiAdjudicationSelectedProviderId: string;
  aiAdjudicationStartDate: string;
  aiAdjudicationEndDate: string;
  aiAdjudicationProvidersSearchQuery: string;
  aiAdjudicationSelectedScheme: MembershipScheme;
  aiAdjudicationSelectedStatus: AdjudicationStatus;
  aiAdjudicationSearchQuery: string;
  aiAdjudicationSelectedBenefitType: string;
}

const initialState: AIPreauthState = {
  aiAdjudicationSelectedProviderId: "",
  aiAdjudicationStartDate: "",
  aiAdjudicationEndDate: "",
  aiAdjudicationProvidersSearchQuery: "",
  aiAdjudicationSelectedScheme: {} as MembershipScheme,
  aiAdjudicationSelectedStatus: "" as AdjudicationStatus,
  aiAdjudicationSearchQuery: "",
  aiAdjudicationSelectedBenefitType: "",
};

const aiAdjudicationSlice = createSlice({
  name: "aiPreauth",
  initialState,
  reducers: {
    setAiAdjudicationSelectedProviderId(state, action: PayloadAction<string>) {
      state.aiAdjudicationSelectedProviderId = action.payload;
    },
    setAiAdjudicationStartDate(state, action: PayloadAction<string>) {
      state.aiAdjudicationStartDate = action.payload;
    },
    setAiAdjudicationEndDate(state, action: PayloadAction<string>) {
      state.aiAdjudicationEndDate = action.payload;
    },
    setAiAdjudicationProvidersSearchQuery(state, action: PayloadAction<string>) {
      state.aiAdjudicationProvidersSearchQuery = action.payload;
    },
    setAiAdjudicationSelectedScheme(state, action: PayloadAction<MembershipScheme>) {
      state.aiAdjudicationSelectedScheme = action.payload;
    },
    setAiAdjudicationSelectedStatus(state, action: PayloadAction<AdjudicationStatus>) {
      state.aiAdjudicationSelectedStatus = action.payload;
    },
    setAiAdjudicationSearchQuery(state, action: PayloadAction<string>) {
      state.aiAdjudicationSearchQuery = action.payload;
    },
    setAiAdjudicationSelectedBenefitType(state, action: PayloadAction<string>) {
      state.aiAdjudicationSelectedBenefitType = action.payload;
    },
  },
});

export const {
  setAiAdjudicationSelectedProviderId,
  setAiAdjudicationStartDate,
  setAiAdjudicationEndDate,
  setAiAdjudicationProvidersSearchQuery,
  setAiAdjudicationSelectedScheme,
  setAiAdjudicationSelectedStatus,
  setAiAdjudicationSearchQuery,
  setAiAdjudicationSelectedBenefitType,
} = aiAdjudicationSlice.actions;

export default aiAdjudicationSlice.reducer;
