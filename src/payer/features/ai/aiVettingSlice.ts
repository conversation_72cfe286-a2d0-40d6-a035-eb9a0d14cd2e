import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { MembershipScheme } from "../../lib/types/membership/memberScheme";

interface AiVettingState {
  aiSelectedProviderId: string;
  aiSelectedRegion: string;
  aiSelectedBenefitId: string;
  aiSelectedAgeBandId: string;
  aiCatalogIds: number[];
  aiSelectedServiceGroup: string;
  aiStartDate: string;
  aiEndDate: string;
  aiSelectedScheme: MembershipScheme;
  aiProvidersSearchQuery: string;
}

const initialState: AiVettingState = {
  aiSelectedProviderId: "",
  aiSelectedRegion: "",
  aiSelectedBenefitId: "",
  aiSelectedAgeBandId: "",
  aiCatalogIds: [],
  aiSelectedServiceGroup: "",
  aiStartDate: "",
  aiEndDate: "",
  aiSelectedScheme: {} as MembershipScheme,
  aiProvidersSearchQuery: "",
};

const aiVettingSlice = createSlice({
  name: "aiVetting",
  initialState,
  reducers: {
    setAiSelectedProviderId(state, action: PayloadAction<string>) {
      state.aiSelectedProviderId = action.payload;
    },
    setAiSelectedRegion(state, action: PayloadAction<string>) {
      state.aiSelectedRegion = action.payload;
    },
    setAiSelectedBenefitId(state, action: PayloadAction<string>) {
      state.aiSelectedBenefitId = action.payload;
    },
    setAiSelectedAgeBandId(state, action: PayloadAction<string>) {
      state.aiSelectedAgeBandId = action.payload;
    },
    setAiCatalogIds(state, action: PayloadAction<number[]>) {
      state.aiCatalogIds = action.payload;
    },
    setAiSelectedServiceGroup(state, action: PayloadAction<string>) {
      state.aiSelectedServiceGroup = action.payload;
    },
    setAiStartDate(state, action: PayloadAction<string>) {
      state.aiStartDate = action.payload;
    },
    setAiEndDate(state, action: PayloadAction<string>) {
      state.aiEndDate = action.payload;
    },
    setAiSelectedScheme(state, action: PayloadAction<MembershipScheme>) {
      state.aiSelectedScheme = action.payload;
    },
    setAiProvidersSearchQuery(state, action: PayloadAction<string>) {
      state.aiProvidersSearchQuery = action.payload;
    },
  },
});

export const {
  setAiSelectedProviderId,
  setAiSelectedRegion,
  setAiSelectedBenefitId,
  setAiSelectedAgeBandId,
  setAiCatalogIds,
  setAiSelectedServiceGroup,
  setAiStartDate,
  setAiEndDate,
  setAiSelectedScheme,
  setAiProvidersSearchQuery,
} = aiVettingSlice.actions;

export default aiVettingSlice.reducer;
