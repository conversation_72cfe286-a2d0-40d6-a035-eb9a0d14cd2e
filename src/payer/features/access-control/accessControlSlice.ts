import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { AccessModuleName } from "../../components/access-management/AccessModuleCard";
import { RolePresentation } from "../../lib/types/access-control/role";
import { UserRepresentation } from "../../lib/types/access-control/user";

interface AccessControlState {
  selectedAccessControlUserId: string;
  selectedAccessControlUsername: string;
  selectedUserRoles: Array<RolePresentation>;
  selectedModule: AccessModuleName | null;
  userAddedRoles: Array<string>;
  userRemovedRoles: Array<string>;
  isConfirmRolesModalOpen: boolean;
  isUserRolesUpdatedSuccessfullyModalOpen: boolean;
  isUpdatingUserRoles: boolean;
  temporaryUserRoles: Array<string>;
  selectedAuditLogsUser: UserRepresentation;
}

const initialState: AccessControlState = {
  selectedAccessControlUserId: "",
  selectedAccessControlUsername: "",
  selectedUserRoles: [],
  selectedModule: null,
  userAddedRoles: [],
  userRemovedRoles: [],
  isConfirmRolesModalOpen: false,
  isUserRolesUpdatedSuccessfullyModalOpen: false,
  isUpdatingUserRoles: false,
  temporaryUserRoles: [],
  selectedAuditLogsUser: {} as UserRepresentation,
};

export const accessControlSlice = createSlice({
  name: "accessControl",
  initialState,
  reducers: {
    setSelectedAccessControlUserId(state, action: PayloadAction<string>) {
      state.selectedAccessControlUserId = action.payload;
    },
    setSelectedModule(state, action: PayloadAction<AccessModuleName | null>) {
      state.selectedModule = action.payload;
    },
    setSelectedUserRoles(state, action: PayloadAction<Array<RolePresentation>>) {
      state.selectedUserRoles = action.payload;
    },
    setUserAddedRoles(state, action: PayloadAction<Array<string>>) {
      state.userAddedRoles = action.payload;
    },
    setUserRemovedRoles(state, action: PayloadAction<Array<string>>) {
      state.userRemovedRoles = action.payload;
    },
    setIsConfirmRolesModalOpen(state, action: PayloadAction<boolean>) {
      state.isConfirmRolesModalOpen = action.payload;
    },
    setIsUserRolesUpdatedSuccessfullyModalOpen(state, action: PayloadAction<boolean>) {
      state.isUserRolesUpdatedSuccessfullyModalOpen = action.payload;
    },
    toggleUserSwitchRole(
      state,
      action: PayloadAction<{ roleNames: Array<string>; isAdding: boolean }>,
    ) {
      const { roleNames, isAdding } = action.payload;

      roleNames.forEach((roleName) => {
        if (isAdding) {
          state.userRemovedRoles = state.userRemovedRoles.filter((role) => role !== roleName);
          if (!state.userAddedRoles.includes(roleName)) {
            state.userAddedRoles.push(roleName);
          }
        } else {
          state.userAddedRoles = state.userAddedRoles.filter((role) => role !== roleName);
          if (!state.userRemovedRoles.includes(roleName)) {
            state.userRemovedRoles.push(roleName);
          }
        }
      });
    },
    setIsUpdatingUserRoles(state, action: PayloadAction<boolean>) {
      state.isUpdatingUserRoles = action.payload;
    },
    setTemporaryUserRoles(state, action: PayloadAction<Array<string>>) {
      state.temporaryUserRoles = action.payload;
    },
    setSelectedAccessControlUsername(state, action: PayloadAction<string>) {
      state.selectedAccessControlUsername = action.payload;
    },
    setSelectedAuditLogsUser(state, action: PayloadAction<UserRepresentation>) {
      state.selectedAuditLogsUser = action.payload;
    },
  },
});

export const {
  setSelectedAccessControlUserId,
  setSelectedUserRoles,
  setSelectedModule,
  setUserAddedRoles,
  setUserRemovedRoles,
  setIsConfirmRolesModalOpen,
  setIsUserRolesUpdatedSuccessfullyModalOpen,
  toggleUserSwitchRole,
  setIsUpdatingUserRoles,
  setTemporaryUserRoles,
  setSelectedAccessControlUsername,
  setSelectedAuditLogsUser,
} = accessControlSlice.actions;
export default accessControlSlice.reducer;
