import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface FilterSelectState {
  isOptionsListOpen: boolean;
  searchTerm: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  selectedOption: any;
  highlightedIndex: number;
}

interface FilterSelectsState {
  [id: string]: FilterSelectState;
}

const initialState: FilterSelectsState = {};

const filterSelectSlice = createSlice({
  name: "filterSelect",
  initialState,
  reducers: {
    initializeSelect: (state, action: PayloadAction<{ id: string }>) => {
      const { id } = action.payload;
      state[id] = {
        isOptionsListOpen: false,
        searchTerm: "",
        selectedOption: null,
        highlightedIndex: -1,
      };
    },
    setIsOptionsListOpen: (
      state,
      action: PayloadAction<{ id: string; isOptionsListOpen: boolean }>,
    ) => {
      const { id, isOptionsListOpen } = action.payload;
      if (state[id]) {
        state[id].isOptionsListOpen = isOptionsListOpen;
      }
    },
    setSearchTerm: (state, action: PayloadAction<{ id: string; searchTerm: string }>) => {
      const { id, searchTerm } = action.payload;
      if (state[id]) {
        state[id].searchTerm = searchTerm;
      }
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    setSelectedOption: (state, action: PayloadAction<{ id: string; selectedOption: any }>) => {
      const { id, selectedOption } = action.payload;
      if (state[id]) {
        state[id].selectedOption = selectedOption;
      }
    },
    setHighlightedIndex: (state, action: PayloadAction<{ id: string; index: number }>) => {
      const { id, index } = action.payload;
      if (state[id]) {
        state[id].highlightedIndex = index;
      }
    },
    resetSelect: (state, action: PayloadAction<{ id: string }>) => {
      const { id } = action.payload;
      delete state[id];
    },
  },
});

export const {
  initializeSelect,
  setIsOptionsListOpen,
  setSearchTerm,
  setSelectedOption,
  setHighlightedIndex,
  resetSelect,
} = filterSelectSlice.actions;
export default filterSelectSlice.reducer;
