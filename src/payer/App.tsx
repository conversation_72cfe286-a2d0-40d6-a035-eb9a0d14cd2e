import { ErrorInfo, useEffect } from "react";
import { ErrorBoundary } from "react-error-boundary";
import { <PERSON><PERSON><PERSON><PERSON>outer } from "react-router-dom";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Fallback from "~lib/components/Fallback";
import Welcome from "~lib/components/Welcome";
import "./App.css";
import MenuBox from "./components/MenuBox";
import NotificationsProvider from "./context/NotificationsProvider";
import "./css/drawer.css";
import "./css/style.scss";
import { auth, keycloak } from "./services/UserService";
import { useAppSelector } from "./store/hooks";

toast.configure();

const logError = (error: Error, info: ErrorInfo) => {
  console.error(error);
  console.error("Component stack:", info.componentStack);
};

const App = () => {
  const isAuthenticated = useAppSelector((state) => state.auth.isAuthenticated);
  const isLoading = useAppSelector((state) => state.auth.isLoading);
  const error = useAppSelector((state) => state.auth.error);

  useEffect(() => {
    async function main() {
      await auth.init();
    }

    main();
  }, []);

  return (
    <ErrorBoundary
      fallbackRender={(props) => <Fallback {...props} keycloak={keycloak} />}
      onError={logError}
    >
      <ToastContainer
        position="top-center"
        autoClose={5000}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        hideProgressBar
        draggable
        pauseOnHover
      />
      {/* NOTE: The welcome page will be shown immediately the session expires, 
        which might be jarring for users. */}
      {isAuthenticated ? (
        <BrowserRouter>
          <NotificationsProvider>
            <MenuBox />
          </NotificationsProvider>
        </BrowserRouter>
      ) : (
        <Welcome
          isLoading={isLoading}
          isAuthenticated={isAuthenticated}
          error={error}
          keycloak={keycloak}
          portalType="payer"
        />
      )}
    </ErrorBoundary>
  );
};

export default App;
