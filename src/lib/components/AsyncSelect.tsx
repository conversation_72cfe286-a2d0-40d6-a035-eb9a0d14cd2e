import React from "react";
import { useForm<PERSON>ontex<PERSON>, Controller, Path, PathValue, ControllerProps } from "react-hook-form";
import ReactSelectAsync from "react-select/async";

// TODO: Make this generic
interface Option {
  label: string;
  value: string;
}

/**
 * TODO: Add controller props
 */
interface Props<N extends string> extends React.ComponentProps<typeof ReactSelectAsync> {
  getOptions: (query: any) => Promise<Option[]>;
  placeholder?: string;
  valueOnly?: boolean;
  /* ----- */
  // Injected by FieldWrapper?
  name?: N;
  required?: boolean;
  validate?: ControllerProps<{
    [key in N]: string;
  }>["rules"]["validate"];
}

/**
 * valueOnly - save only the value, not the whole option. Assumes label and
 * value are the same.
 */
export default function AsyncSelect<N extends string>({
  getOptions,
  name,
  required = false,
  valueOnly = false,
  placeholder,
  validate,
  ...selectOptions
}: Props<N>) {
  type Inputs = {
    [key in N]: string;
  };

  const { control } = useFormContext<Inputs>();

  const controllerProps = {
    rules: {
      ...(required && { required: "This field is required" }),
      ...(validate && { validate }),
    },
  };

  // Remove manually configured options
  const { options, value, onChange, ...restOptions } = selectOptions;

  return (
    <Controller
      name={name as unknown as Path<Inputs>}
      control={control}
      render={({ field: { onChange, value, ...restProps } }) => (
        <ReactSelectAsync
          loadOptions={getOptions}
          isClearable={true}
          isSearchable={true}
          onChange={(option) => {
            if (!option) {
              onChange(null);
              return;
            }

            if (valueOnly) {
              onChange((option as Option).value as PathValue<Inputs, Path<Inputs>>);
            } else {
              onChange(option as PathValue<Inputs, Path<Inputs>>);
            }
          }}
          value={value == null ? undefined : valueOnly ? { label: value, value } : value}
          {...restProps}
          {...restOptions}
          {...(placeholder && { placeholder })}
          key={(value || "") as string}
        />
      )}
      {...controllerProps}
    />
  );
}
