import { clsx } from "~lib/utils";
import { cn } from "~lib/utils/cn";

interface Section {
  title: string;
  description?: string;
}

interface Props {
  sections: Array<Section>;
  active: number;
  containerClassName?: string;
}

export default function VerticalStepper({ sections, active, containerClassName = "" }: Props) {
  return (
    <ol className={cn("flex h-full flex-col ", containerClassName)}>
      {sections.map((section, index) => (
        <li className="flex grow gap-4" key={index}>
          {/* bg-blue-100 ring-white */}
          {/* ring-8 ring-white dark:ring-gray-900 */}
          <div className="flex flex-col items-center">
            <span
              className={clsx(
                "flex h-10 w-10 items-center justify-center rounded-full border font-medium",
                index === active
                  ? "border-none bg-blue-900 text-white"
                  : "border-gray-300 bg-white text-gray-400",
              )}
            >
              {index + 1}
            </span>
            {index !== sections.length - 1 && (
              <span
                className={cn(
                  "grow border border-blue-900 ",
                  index === active ? "border-blue-900" : "border-gray-400",
                )}
              ></span>
            )}
          </div>

          <div className="">
            <h3
              className={clsx(
                "mb-1 flex items-center pt-1 font-semibold",
                index === active ? "text-blue-900" : "text-gray-400",
              )}
            >
              {section.title}
            </h3>

            {section.description && (
              <p
                className={clsx(
                  "mb-4 text-sm font-normal",
                  index === active ? "text-gray-500" : "text-gray-400",
                )}
              >
                {section.description}
              </p>
            )}
          </div>
        </li>
      ))}
    </ol>
  );
}
