import { clsx } from "~lib/utils";

interface Props {
  sections: Array<string>;
  active: number;
}

export default function Stepper({ sections, active }: Props) {
  return (
    <ol className="flex mb-4 items-center w-full text-sm font-medium text-center text-gray-500 dark:text-gray-400 sm:text-base">
      {sections.map((section, index) => {
        return (
          <li
            className={clsx(
              "stepper-item flex items-center",
              index !== sections.length - 1 && "md:w-full items-center after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 sm:after:inline-block after:mx-6 xl:after:mx-10 dark:after:border-gray-700",
              index <= active && "text-blue-600 dark:text-blue-500 sm:after:content-['']"
            )}
            key={index}
          >
            <span className="flex items-center after:content-['/'] sm:after:hidden after:mx-2 after:text-gray-200 dark:after:text-gray-500">
              {index < active ? (
                // prettier-ignore
                <svg aria-hidden="true" className="w-4 h-4 mr-2 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" >
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                </svg>
              ) : (
                <span className="mr-2">{index + 1}</span>
              )}
              {section}
            </span>
          </li>
        );
      })}
    </ol>
  );
}
