import { clsx } from "~lib/utils";

interface Props {
  className?: string;
  circleClassName?: string;
  children?: React.ReactNode;
}

const Badge = ({ className, circleClassName, children }: Props) => {
  return (
    <div
      className={clsx(
        "flex gap-1 items-center px-2 py-1 rounded-full text-xs font-bold border",
        className
      )}
      style={{ width: "fit-content" }}
    >
      <span
        className={clsx("inline-block rounded-full h-3 w-3", circleClassName)}
      ></span>
      <span className="whitespace-nowrap">{children}</span>
    </div>
  );
};

export default Badge;
