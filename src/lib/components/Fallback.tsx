import { Disclosure } from "@headlessui/react";
import Keycloak from "keycloak-js";
import ErrorMessage from "./Error";

interface Props {
  keycloak: Keycloak.KeycloakInstance;
  error: Error;
  resetErrorBoundary?: () => void;
}

const Fallback = ({ keycloak, error }: Props) => {
  function refreshPage() {
    window.location.reload();
  }

  return (
    <main className="bg-gray-50 flex justify-center items-center w-full min-h-screen">
      <div className="bg-white shadow-sm p-8 rounded-lg w-full max-w-md">
        <ErrorMessage />

        <div className="w-full px-2 mb-4">
          <div className="rounded-2xl bg-white p-2">
            <Disclosure>
              {({ open }) => (
                <>
                  <Disclosure.Button className="flex w-full justify-between rounded-lg bg-red-50 px-4 py-2 text-left text-sm font-medium text-gray-900 hover:bg-red-100 focus:outline-none focus-visible:ring focus-visible:ring-gray-500 focus-visible:ring-opacity-75">
                    <span className="text-red-400">Error Details</span>

                    <>
                      {/* prettier-ignore */}
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={`${
                          open ? "rotate-180 transform" : ""
                        } h-5 w-5 text-red-500`}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
                      </svg>
                    </>
                  </Disclosure.Button>
                  <Disclosure.Panel className="px-4 pt-4 pb-2 text-sm text-gray-500">
                    {error.message}
                  </Disclosure.Panel>
                </>
              )}
            </Disclosure>
          </div>
        </div>

        <div className="flex gap-4 justify-center">
          <button
            className="flex gap-2 py-2 px-4 font-medium rounded border border-red-500 text-red-500 enabled:hover:text-red-600 enabled:hover:border-red-600"
            onClick={() => keycloak.logout()}
          >
            Log Out
          </button>

          <button
            className="flex gap-2 py-2 px-4 font-medium rounded border border-blue-500 text-blue-500 enabled:hover:text-blue-600 enabled:hover:border-blue-600"
            onClick={() => refreshPage()}
          >
            Refresh the Page
          </button>
        </div>
      </div>
    </main>
  );
};

export default Fallback;
