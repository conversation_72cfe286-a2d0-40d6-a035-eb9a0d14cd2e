import React from "react";
import { useFormContext } from "react-hook-form";
import { clsx, isFalsyOrEmpty } from "~lib/utils";

interface InputProps<T> {
  description?: string;
  label?: string;
  required?: boolean;
  id?: string;
  name: T;
  className?: string;
  labelClassName?: string;
  hideErrorMessage?: boolean;
  children: React.ReactNode;
  fieldError?: string | undefined;
}

export default function FieldWrapper<T extends string>({
  label,
  name,
  description,
  required,
  id = name,
  className = "",
  labelClassName,
  hideErrorMessage = false,
  children,
  fieldError,
}: InputProps<T>) {
  type Inputs = {
    [key in T]: string;
  };

  const { formState } = useFormContext<Inputs>();

  const childProps = {
    ...(name && { name }),
    ...(id && { id }),
    ...(required && {
      required: typeof required == "string" ? required : "This field is required",
    }),
  };

  const selfError = formState.errors?.[name];

  // Get error, or error root if field array
  const selfErrorObject = selfError || selfError?.root;

  const selfErrorMessage = !isFalsyOrEmpty(selfErrorObject) ? selfErrorObject.message : "Invalid input";

  // Get error, use root error if field array
  const message = !isFalsyOrEmpty(selfErrorObject) ? selfErrorMessage : fieldError;

  // Throw error if more than one child is passed
  React.Children.only(children);

  // Inject props into child
  const child = React.isValidElement(children)
    ? React.cloneElement(children, { ...childProps })
    : undefined;

  return (
    <div className={clsx("flex flex-col gap-1", className)}>
      {label && (
        <label htmlFor={id} className={labelClassName}>
          {label} {required && <span className="text-red-500"> *</span>}
        </label>
      )}

      {description && <span className="text-sm text-gray-400">{description}</span>}

      {child}

      {!hideErrorMessage && (
        <span className="text-xs text-red-500">{message ? message : <>&#8203;</>}</span>
      )}
    </div>
  );
}
