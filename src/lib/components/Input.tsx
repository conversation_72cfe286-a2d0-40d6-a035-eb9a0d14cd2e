import React from "react";
import { Path, useFormContext, RegisterOptions, ValidationRule } from "react-hook-form";
import { clsx } from "~lib/utils";

interface InputProps<T extends string> extends React.InputHTMLAttributes<HTMLInputElement> {
  options?: RegisterOptions;
  id?: string;
  className?: string;
  wrapperClassName?: string;
  type?: "text" | "number" | "email" | "password" | "tel" | "url" | "search" | "hidden";
  /**
   * Fields injected by FieldWrapper
   */
  required?: boolean;
  name?: T;
  step?: "any" | number;
}

export default function Input<T extends string, V extends string | number>({
  name,
  options,
  type = "text",
  required: parentRequired,
  id = name,
  className = "",
  wrapperClassName = "",
  readOnly = false,
  step = "any",
  ...rest
}: InputProps<T>) {
  type Inputs = {
    [key in T]: V;
  };

  type Fields = Inputs;
  const { register } = useFormContext<Fields>();
  const required = parentRequired || options?.required;
  const { pattern, min, max, minLength, maxLength, ...restOptions } = options || {};

  const registerOptions = {
    ...restOptions,
    valueAsDate: false as const,
    ...(required && { required }),
  };

  const validation = (value: ValidationRule<number | string>, message?: string | undefined) =>
    typeof value === "object"
      ? value
      : {
          value: value,
          message: message || `Invalid input`,
        };

  /**
   * WARN: validation rules must be passed as simple primitives
   * TODO: Fix types
   */
  const registerOptionsWithType =
    type === "number"
      ? {
          ...registerOptions,
          valueAsNumber: true as const,
          ...(min && {
            min: validation(min, `Minimum value is ${min}`),
          }),
          ...(max && {
            max: validation(max, `Maximum value is ${max}`),
          }),
        }
      : type === "text"
        ? {
            ...(minLength && {
              minLength: validation(minLength, `Minimum length is ${minLength}`),
            }),
            ...(maxLength && {
              maxLength: validation(maxLength, `Maximum length is ${maxLength}`),
            }),
            ...registerOptions,
          }
        : {
            ...registerOptions,
          };

  return (
    <input
      {...register(name as unknown as Path<Fields>, registerOptionsWithType)}
      {...rest}
      className={clsx(
        "rounded-md border border-gray-300 p-2 read-only:opacity-60 disabled:opacity-60",
        readOnly && "opacity-60",
        className,
      )}
      type={type}
      readOnly={readOnly}
      {...(type == "number" && {
        step,
      })}
    />
  );
}
