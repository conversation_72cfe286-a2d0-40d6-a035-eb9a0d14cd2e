import { useState } from "react";
import { Controller, Path, PathValue, useFormContext } from "react-hook-form";
import { useSearchProvidersByTypeQuery } from "~lib/api";
import FieldSet from "~lib/components/FieldSet";
import { useDebounce } from "~lib/hooks/useDebounce";
import { clsx, pluralize } from "~lib/utils";
import Empty from "./Empty";
import { Option } from "~lib/types";
import { SearchProviderFilter } from "~lib/api/types";

const PROVIDERS_SIZE = 10; // Max items to fetch from the API
const PROVIDERS_PAGE = 1; // Page is always 1 for now
const MAX_ITEMS = 10; // Max items shown in the UI

interface Props<T extends string> {
  name: T;
  label?: string;
  fieldsetClassName?: string;
  type?: SearchProviderFilter;
  mainProvider?: number;
}

type StringOption = Option<string>;

/**
 * TODO: Make name generic
 */
const SelectProviders = <T extends string>({
  fieldsetClassName,
  name,
  label,
  type = SearchProviderFilter.ALL,
  mainProvider,
}: Props<T>) => {
  type Inputs = {
    [key in T]: StringOption[];
  };

  const [query, setQuery] = useState("");
  const debouncedQuery = useDebounce(query, 500);

  const methods = useFormContext<Inputs>();
  const { watch, resetField, control } = methods;

  const form = watch();

  const selected = form[name] || [];
  const selectedCount = selected.length;

  const {
    data: providersResponse,
    isLoading: isProvidersLoading,
    isFetching: isProvidersFetching,
    error: providersError,
  } = useSearchProvidersByTypeQuery({
    ...(debouncedQuery && { query: debouncedQuery }),
    ...(mainProvider && { mainFacilityId: mainProvider }),
    page: PROVIDERS_PAGE,
    size: PROVIDERS_SIZE,
    type,
  });

  const providers = providersResponse?.data?.content || [];

  const providerOptions = providers.map((provider) => ({
    label: provider.name,
    value: provider.id.toString(),
  }));

  const optionChecked = (option: StringOption) =>
    selected.some((value) => value.value === option.value);

  const options = !debouncedQuery
    ? selected.concat(
        providerOptions
          .filter((option) => !optionChecked(option))
          .slice(0, MAX_ITEMS - selectedCount)
      )
    : providerOptions;

  return (
    <div>
      <div className="mb-2">
        <label
          htmlFor={`${name}-query`}
          className="flex gap-2 px-2 items-center"
        >
          <div className="flex relative flex-grow">
            <input
              type="text"
              id={`${name}-query`}
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="border border-gray-300 rounded-md text-sm pl-2 pr-8 py-1 flex-grow"
              placeholder="Search Providers"
            />

            <button
              type="button"
              onClick={() => setQuery("")}
              className="text-gray-400 hover:text-gray-500 absolute right-2 top-1/2 transform -translate-y-1/2"
            >
              {/* prettier-ignore */}
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <span>
            {/* prettier-ignore */}
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"
              className={clsx(
                "text-gray-400 w-6 h-6 opacity-0",
                isProvidersFetching && !isProvidersLoading && "opacity-100"
              )}
            >
              <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z">
                <animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/>
              </path>
            </svg>
          </span>
        </label>
      </div>

      <div className="flex justify-between gap-4 flex-wrap text-gray-400 text-sm mb-2">
        <p>
          Selected {selectedCount} {pluralize(selectedCount, "item")}
        </p>

        <button
          type="button"
          onClick={() => {
            resetField(name as unknown as Path<Inputs>);
            setQuery("");
          }}
          className="text-red-500"
        >
          Clear All
        </button>
      </div>

      {isProvidersLoading ? (
        <div className="flex justify-center items-center py-8">
          {/* prettier-ignore */}
          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-blue-400 w-8 h-8">
            <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"><animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/></path>
          </svg>
        </div>
      ) : providersError ? (
        <div className="py-8 px-2 text-red-500">
          Something went wrong while fetching providers.
        </div>
      ) : !providers?.length ? (
        <Empty message="No providers found" />
      ) : (
        <FieldSet {...{ label }} className={fieldsetClassName}>
          {options.map((option) => (
            <label key={option.value} className="flex items-center gap-2">
              {/* TODO: Use register options */}
              <Controller
                name={name as unknown as Path<Inputs>}
                control={control}
                render={({ field }) => (
                  <input
                    type="checkbox"
                    {...field}
                    value={option.value}
                    checked={optionChecked(option)}
                    onChange={() => {
                      if (selected.includes(option)) {
                        field.onChange(
                          selected.filter(
                            (formOption) => formOption.value !== option.value
                          ) as PathValue<Inputs, Path<Inputs>>
                        );
                      } else {
                        field.onChange([...selected, option] as PathValue<
                          Inputs,
                          Path<Inputs>
                        >);
                      }
                    }}
                  />
                )}
              />
              {option.label}
            </label>
          ))}
        </FieldSet>
      )}
    </div>
  );
};

export default SelectProviders;
