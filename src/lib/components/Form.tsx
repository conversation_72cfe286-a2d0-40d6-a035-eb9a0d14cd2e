import React, { FormEvent } from "react";
import {
  DeepPartial,
  useForm,
  FormProvider,
  UseFormReturn,
  UseFormHandleSubmit,
} from "react-hook-form";

interface IFormProps<T> extends Omit<React.InputHTMLAttributes<HTMLFormElement>, "onSubmit"> {
  defaultValues?: DeepPartial<T>;
  onSubmit?: (data: T, e?: FormEvent<HTMLFormElement>) => void;
  children: React.ReactNode;
  className?: string;
  methods?: UseFormReturn<T>;
  asFragment?: boolean;
  noValidate?: boolean;
}

export function Form<T>({
  defaultValues,
  onSubmit,
  children,
  className = "",
  methods: _methods = {} as UseFormReturn<T>,
  asFragment = false,
  noValidate,
  ...formAttrs
}: IFormProps<T>) {
  let handleSubmit: UseFormHandleSubmit<T> | undefined;

  const methods = useForm<T>({ defaultValues });

  if (typeof _methods?.handleSubmit === "function") {
    handleSubmit = _methods.handleSubmit;
  } else {
    handleSubmit = methods.handleSubmit;
  }

  return (
    // Let parent methods, if any, override current methods
    <FormProvider {...methods} {..._methods}>
      {asFragment ? (
        <>{children}</>
      ) : (
        <form
          {...(onSubmit && { onSubmit: handleSubmit(onSubmit) })}
          className={className}
          noValidate={noValidate}
          {...formAttrs}
        >
          {children}
        </form>
      )}
    </FormProvider>
  );
}
