import { clsx } from "~lib/utils";

interface Props extends React.ComponentProps<"fieldset"> {
  className?: string;
  label?: string;
  children?: React.ReactNode;
  key?: string;
  required?: boolean;
}

const FieldSet = ({ className, label, children, key, required, ...rest }: Props) => {
  return (
    // Passing undefined to a prop is the same as omitting it.
    <fieldset
      className={clsx("border border-gray-300 flex flex-col gap-2 px-4 py-2 rounded", className)}
      key={key}
      {...rest}
    >
      {label && (
        <legend className="text-sm px-2 text-gray-400">
          {label} {required && <span className="text-red-500">*</span>}
        </legend>
      )}

      {children}
    </fieldset>
  );
};

export default FieldSet;
