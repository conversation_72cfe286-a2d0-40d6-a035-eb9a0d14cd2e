import { BeneficiaryBenefit } from "~lib/api/types";
import { clsx, formatMoney } from "~lib/utils";
import { ChevronDownIcon } from "@heroicons/react/24/outline";
import ReactTooltip from "react-tooltip";
import { BenefitNode } from "~lib/types";

interface Props {
  beneficiaryBenefits: BenefitNode[];
  startVisit: (benefit: BeneficiaryBenefit) => Promise<void>;
}

export default function BenefitTreeView({ beneficiaryBenefits, startVisit }: Props) {
  return (
    <ul className="pl-4 space-y-4 text-sm">
      {beneficiaryBenefits.map((beneficiaryBenefit) => (
        <li key={beneficiaryBenefit.id}>
          <details className="rounded cursor-pointer border border-light-blue-700 border-dashed pr-4 pb-4 [&>summary>svg]:open:-rotate-180">
            <summary className="flex gap-2 p-4 items-center">
              <ChevronDownIcon
                className={clsx(
                  "w-6 h-6 shrink-0 rotate-0 transform transition-all duration-300 opacity-0",
                  <PERSON><PERSON><PERSON>(beneficiaryBenefit.children.length) && "opacity-100"
                )}
                stroke="currentColor"
              />
              <span className="flex-grow font-medium">{beneficiaryBenefit.benefitName}</span>
              <p>
                <span>Balance: </span>
                <span className="font-medium">{formatMoney(beneficiaryBenefit.balance)}</span>
              </p>

              <ReactTooltip id="unbillable-benefit" type="error">
                <span>You cannot bill this benefit</span>
              </ReactTooltip>

              <button
                className="flex gap-2 py-2 px-4 font-medium rounded bg-light-blue-700 enabled:hover:bg-light-blue-500 disabled:opacity-60 disabled:cursor-not-allowed text-white"
                onClick={(e: React.MouseEvent) => {
                  e.preventDefault();
                  e.stopPropagation();

                  startVisit(beneficiaryBenefit);
                }}
                disabled={beneficiaryBenefit.billable === false}
                data-tip={beneficiaryBenefit.billable === false}
                {...(beneficiaryBenefit.billable === false && {
                  "data-for": "unbillable-benefit",
                })}
              >
                Start&nbsp;Visit
              </button>
            </summary>

            {Boolean(beneficiaryBenefit.children.length) && (
              <BenefitTreeView
                beneficiaryBenefits={beneficiaryBenefit.children}
                startVisit={startVisit}
              />
            )}
          </details>
        </li>
      ))}
    </ul>
  );
}
