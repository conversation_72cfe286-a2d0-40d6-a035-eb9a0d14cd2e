/**
 * @file UploadFile.tsx
 * @description UploadFile component. We use axios to be able to track the
 * upload progress.
 */

import { ChangeEvent, DragEvent, useState } from "react";
import { toast } from "react-toastify";
import { baseUrl, MAX_FILE_SIZE } from "~lib/constants";
import { clsx, formatBytes } from "~lib/utils";
import { upload } from "~lib/utils/uploadFile";

interface Props {
  path: string;
  /**
   * @example
   * { "image/jpeg": "JPEG" }
   */
  allowedMimeTypes: Map<string, string>;
  /**
   * Maximum file size in bytes
   * @default MAX_FILE_SIZE in constants
   */
  maxFileSize?: number;
  /**
   * TODO: Fix types
   */
  onCompleted?: (...args: unknown[]) => void;
  payload?: Record<string, string>;
  showToastNotification?: boolean;
}

export default function UploadFile({
  path,
  allowedMimeTypes,
  onCompleted,
  payload,
  maxFileSize = MAX_FILE_SIZE,
  showToastNotification = true,
}: Props) {
  const [dragActive, setDragActive] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<Error | undefined>(undefined);
  const [file, setFile] = useState<File | null>(null);
  const [abortController, setAbortController] = useState(new AbortController());

  const isUploading = file != null && progress < 100; // File is selected and upload is not complete

  const handleUploadFile = async (file: File) => {
    const abortController = new AbortController();
    setAbortController(abortController);

    try {
      setFile(file);
      setProgress(0);
      setError(undefined);

      const response = await upload({
        url: baseUrl + path,
        file,
        signal: abortController.signal,
        progressCallBack(progressEvent: ProgressEvent) {
          setProgress(Math.floor((progressEvent.loaded / progressEvent.total) * 100));
        },
        payload,
      });

      if (response.data.success != true) {
        throw new Error(response.data.msg || "Upload error");
      } else {
        if (showToastNotification) toast.success(response.data.msg);
      }

      setProgress(100);
      onCompleted?.(response.data);
    } catch (error) {
      if (process.env.NODE_ENV === "development") console.error(error);
      setError(error);
    }
  };

  const handleDrag = async function (e) {
    e.preventDefault();
    e.stopPropagation();

    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  /**
   * Returns an error message if the file is invalid
   * @param file {File[]}
   * @returns {string|undefined} Error message
   */
  const validateFile = (file: File): string | undefined => {
    switch (true) {
      case !file:
        return "No file selected";
      case !allowedMimeTypes.has(file.type):
        return "Invalid file type";
      case file.size > maxFileSize:
        return `Only files below ${formatBytes(maxFileSize)} are allowed`;
      default:
        return undefined;
    }
  };

  const handleDrop = async function (ev: DragEvent<HTMLDivElement>) {
    ev.preventDefault();
    ev.stopPropagation();

    if (isUploading) {
      toast.error("File is already uploading");
      return;
    }

    setDragActive(false);

    const files = ev.dataTransfer.items
      ? Array.from(ev.dataTransfer.items)
          .filter((item) => item.kind === "file")
          .map((item) => item.getAsFile())
      : Array.from(ev.dataTransfer.files);

    if (files.length > 1) {
      toast.error("Only one file is allowed");
      return;
    }

    const file = files?.[0];
    const error = validateFile(file);

    if (error) toast.error(error);
    if (error != undefined) return;

    await handleUploadFile(file);
  };

  const handleChange = async (e: ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    e.stopPropagation();

    const files = Array.from(e.target.files || []);

    const error = validateFile(files?.[0]);

    if (error) toast.error(error);
    if (error != undefined) return;

    for (const file of files) {
      await handleUploadFile(file);
    }
  };

  return (
    <div>
      <div className="px-2 py-4">
        <div
          className={clsx(
            "flex items-center justify-center w-full h-56 border-2 border-dashed border-blue-300 rounded-lg",
            dragActive && isUploading ? "bg-gray-50" : dragActive ? "bg-blue-50" : "bg-white"
          )}
          onDragEnter={handleDrag}
        >
          <div className="flex flex-col items-center">
            {/* prettier-ignore */}
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-24 h-24 text-blue-400">
              <path fillRule="evenodd" d="M10.5 3.75a6 6 0 00-5.98 6.496A5.25 5.25 0 006.75 20.25H18a4.5 4.5 0 002.206-8.423 3.75 3.75 0 00-4.133-4.303A6.001 6.001 0 0010.5 3.75zm2.03 5.47a.75.75 0 00-1.06 0l-3 3a.75.75 0 101.06 1.06l1.72-1.72v4.94a.75.75 0 001.5 0v-4.94l1.72 1.72a.75.75 0 101.06-1.06l-3-3z" clipRule="evenodd" />
            </svg>

            <div>
              <p className="text-center">
                Drag and drop here or
                <label className="px-2 underline cursor-pointer text-blue-400 hover:text-blue-500">
                  browse
                  <input
                    type="file"
                    name="file"
                    className="hidden"
                    multiple={false}
                    accept={Array.from(allowedMimeTypes.keys()).join(",")}
                    onChange={handleChange}
                    disabled={isUploading}
                  />
                </label>
              </p>

              <p className="text-center text-xs text-gray-400 font-medium">
                {Array.from(allowedMimeTypes.values()).join(", ")} files only. Max file size is{" "}
                {formatBytes(maxFileSize)}.
              </p>
            </div>
          </div>

          {dragActive && (
            <div
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
              className="absolute inset-0 z-10 top-0 left-0 right-0 bottom-0 w-full h-full opacity-0"
            />
          )}
        </div>
      </div>

      <div className="px-4 mb-4">
        {file && (
          <div className="flex gap-4 mb-2">
            <div className="break-all flex-grow">{file.name}</div>

            {error ? (
              <div className="flex gap-2 items-center">
                <p className="text-red-500 text-sm">{error?.message || "Upload error"}</p>

                {/* prettier-ignore */}
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-6 h-6 text-red-500">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                </svg>
              </div>
            ) : (
              <div className="flex items-center justify-start">
                <div className="w-32 bg-gray-200 rounded-full h-1.5">
                  <div
                    className="bg-blue-600 h-1.5 rounded-full dark:bg-blue-500"
                    style={{ width: `${progress}%` }}
                  />
                </div>
              </div>
            )}

            <div className="flex items-center">
              {progress === 100 && !error ? (
                <>
                  {/* prettier-ignore */}
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-6 h-6 text-green-500">
                <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
                </>
              ) : (
                <button
                  className={clsx("text-red-500 hover:text-red-600")}
                  type="button"
                  title={error ? "Remove" : "Cancel"}
                  onClick={() => {
                    if (error) {
                      // Remove file with network error from the UI
                      setFile(null);
                    } else {
                      // Cancel upload
                      abortController?.abort();
                    }
                  }}
                >
                  {/* prettier-ignore */}
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                </svg>
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
