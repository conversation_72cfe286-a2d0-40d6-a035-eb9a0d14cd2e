import { useState } from "react";
import { Path, PathValue, RegisterOptions, useFormContext } from "react-hook-form";
import FieldSet from "~lib/components/FieldSet";
import { clsx, pluralize } from "~lib/utils";

interface Option {
  label: string;
  value: string;
}

interface Props<N extends string> {
  registerOptions?: RegisterOptions;
  options: Option[];
  fieldSetClassName?: string;
  /**
   * @default true
   */
  showFilter?: boolean;
  /**
   * @default true
   */
  showHeader?: boolean;
  /**
   * Values protentially from parent component
   */
  name?: N;
  label?: string;
  disabled?: boolean;
}

/**
 * WARNING: Values must be strings.
 * See - https://github.com/react-hook-form/react-hook-form/issues/8499#issuecomment-1155776337
 */
const CheckboxGroup = <N extends string>({
  registerOptions,
  fieldSetClassName,
  options,
  name,
  label,
  showFilter = true,
  showHeader = true,
  disabled,
}: Props<N>) => {
  type Inputs = {
    [key in N]: string[];
  };

  const [filter, setFilter] = useState("");
  const methods = useFormContext<Inputs>();
  const { register, watch, setValue, resetField } = methods;

  const form = watch();
  const selected = form[name] || [];

  const checked = (option: Option) => selected.includes(option.value);
  const filtered = (option: Option) => option.label.toLowerCase().includes(filter.toLowerCase());

  const filteredOptions = options.slice().sort(
    // Sort by whether the option is already selected or is filtered
    // See - https://stackoverflow.com/a/49385215/6567303
    (a, b) => Number(filtered(b)) - Number(filtered(a)) || Number(checked(b)) - Number(checked(a)),
  );

  const nameTyped = name as unknown as Path<Inputs>;

  const values = options.map((option) => option.value);

  return (
    <div>
      {showFilter && (
        <div className="mb-2">
          <label htmlFor={`${name}-filter`} className="flex flex-wrap items-center gap-2">
            <div className="relative flex flex-grow">
              <input
                type="text"
                id={`${name}-filter`}
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                className="flex-grow rounded-md border border-gray-300 py-1 pl-2 pr-8 text-sm"
                placeholder="Filter..."
              />

              <button
                type="button"
                onClick={() => setFilter("")}
                className="absolute right-2 top-1/2 -translate-y-1/2 transform text-gray-400 hover:text-gray-500"
              >
                {/* prettier-ignore */}
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
              </svg>
              </button>
            </div>
          </label>
        </div>
      )}

      {showHeader && (
        <div className="mb-2 flex flex-wrap justify-between gap-4 text-sm text-gray-400">
          <button
            type="button"
            onClick={() => {
              setValue(
                nameTyped,
                values.map((v) => v.toString()) as PathValue<Inputs, Path<Inputs>>,
              );
            }}
            className="text-blue-500"
          >
            Select All
          </button>

          <p>
            Selected {selected.length} {pluralize(selected.length, "item")}
          </p>

          {selected.length > 0 && (
            <button
              type="button"
              onClick={() => {
                resetField(nameTyped);
                setFilter("");
              }}
              className="text-red-500"
            >
              Clear All
            </button>
          )}
        </div>
      )}

      <FieldSet
        {...{ label }}
        className={clsx(disabled && "cursor-not-allowed opacity-60", fieldSetClassName)}
      >
        {filteredOptions.map((option) => (
          <label key={option.value} className="flex items-center gap-2">
            <input
              type="checkbox"
              value={option.value}
              key={option.value}
              {...register(nameTyped, registerOptions)}
              className="rounded border-gray-300"
              disabled={disabled}
            />
            {option.label}
          </label>
        ))}
      </FieldSet>
    </div>
  );
};

export default CheckboxGroup;
