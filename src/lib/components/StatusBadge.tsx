import { Status } from "~lib/types";
import { clsx } from "~lib/utils";

interface Props {
  className?: string;
  children?: React.ReactNode;
  status: Status;
}

const statusBadgeColors = new Map([
  [Status.OTHER, ["border-gray-400 text-gray-600", "bg-gray-400"]],
  [Status.SUCCESS, ["border-green-400 text-green-600", "bg-green-400"]],
  [Status.FAILURE, ["border-red-400 text-red-600", "bg-red-400"]],
  [Status.PENDING, ["border-yellow-400 text-yellow-600", "bg-yellow-400"]],
]);

const StatusBadge = ({ className, status, children }: Props) => {
  return (
    <div
      className={clsx(
        "flex gap-1 items-center px-2 py-1 rounded-full text-xs font-bold border",
        statusBadgeColors.get(status)?.[0] || "",
        className
      )}
      style={{ width: "fit-content" }}
    >
      <span
        className={clsx("inline-block rounded-full h-3 w-3", statusBadgeColors.get(status)?.[1] || "")}
      ></span>
      <span className="whitespace-nowrap">{children}</span>
    </div>
  );
};

export default StatusBadge;
