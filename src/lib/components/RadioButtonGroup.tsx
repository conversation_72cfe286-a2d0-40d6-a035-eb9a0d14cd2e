import React from "react";
import { clsx } from "~lib/utils";
import { useFormContext, Path } from "react-hook-form";

interface Props<V, T> {
  className?: string;
  options: Array<{ label: JSX.Element | string; value: V }>;
  required?: boolean;
  /**
   * Fields injected by `FieldWrapper`
   */
  name?: T;
  isDisabled?: boolean;
}

// TODO: Test number values
// TODO: Test default values
export default function RadioButtonGroup<V extends string | number, T extends string>({
  name: inputName,
  options,
  required,
  className = "",
  isDisabled,
}: Props<V, T>) {
  type Inputs = {
    [key in T]: V;
  };

  const name = inputName as unknown as Path<Inputs>;

  const { register, watch } = useFormContext<Inputs>();
  const value = watch(name);
  const registerOptions = {
    ...(required && { required }),
  };

  return (
    <div className={clsx("inline-flex rounded-md shadow-sm", className)}>
      {options.map((option, index) => (
        <label
          key={option.value}
          className={clsx(
            "flex items-center gap-1 rounded-md border border-transparent",
            value === option.value && "border-blue-500 bg-blue-50",
          )}
        >
          <input
            type="radio"
            value={option.value}
            className="hidden"
            {...register(name as unknown as Path<Inputs>, registerOptions)}
            disabled={isDisabled}
          />
          <span
            className={clsx(
              "bg-white px-4 py-2 font-medium",
              index == 0 && "rounded-s-lg",
              index == options.length - 1 && "rounded-e-lg",
              value === option.value
                ? "border border-gray-200 text-blue-700"
                : "border-b border-t border-gray-200 text-gray-900",
              value === option.value &&
                !isDisabled &&
                "hover:bg-gray-100 focus:z-10 focus:text-blue-700 focus:ring-2 focus:ring-blue-700",
              value != option.value &&
                !isDisabled &&
                "hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:text-blue-700 focus:ring-2 focus:ring-blue-700",
              isDisabled && "cursor-not-allowed opacity-60",
            )}
          >
            {option.label}
          </span>
        </label>
      ))}
    </div>
  );
}
