import { useLazySearchProvidersByTypeQuery } from "~lib/api";
import { SearchProviderFilter } from "~lib/api/types";
import AsyncSelect from "./AsyncSelect";
import { clsx } from "~lib/utils";

const PROVIDERS_SIZE = 10; // Max items to fetch from the API
const PROVIDERS_PAGE = 1; // Page is always 1 for now

interface Props {
  name: string;
  label?: string;
  type?: SearchProviderFilter;
  mainProvider?: number;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

/**
 * TODO: Make name generic
 */
const SelectProvider = ({
  name,
  type = SearchProviderFilter.ALL,
  mainProvider,
  placeholder,
  disabled,
  className,
}: Props) => {
  const [getProviders] = useLazySearchProvidersByTypeQuery();

  const getProviderOptions = async (query: string) => {
    const response = await getProviders({
      // TODO: Debounce query?
      ...(query && { query }),
      ...(mainProvider && { mainFacilityId: mainProvider }),
      page: PROVIDERS_PAGE,
      size: PROVIDERS_SIZE,
      type,
    }).unwrap();

    const providers = response?.data?.content || [];

    return providers.map((provider) => ({
      label: provider.name,
      value: provider.id.toString(),
    }));
  };

  return (
    <div>
      <div className="mb-2">
        <label htmlFor={name} className="flex gap-2 py-1 items-center">
          <AsyncSelect
            name={name}
            getOptions={getProviderOptions}
            placeholder={placeholder}
            className={clsx("flex-grow max-w-full", className)}
            styles={{
              menuList: (provided) => ({
                ...provided,
                height: "150px",
              }),
              input: (base) => ({
                ...base,
              }),
            }}
            isDisabled={disabled}
          />
        </label>
      </div>
    </div>
  );
};

export default SelectProvider;
