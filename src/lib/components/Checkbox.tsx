import React from "react";
import { Path, RegisterOptions, useFormContext } from "react-hook-form";
import { clsx } from "~lib/utils";

interface Props<T extends string> extends React.ComponentProps<"input">{
  className?: string;
  options?: RegisterOptions;
  /**
   * Injected by FieldWrapper
   */
  name?: T;
}

/**
 * TODO: Handle required checkbox
 * WARNING: This component has not been tested
 */
export default function Checkbox<T extends string>({
  name,
  options,
  className = '',
  ...rest
}: Props<T>) {
  type Inputs = {
    [key in T]: boolean;
  };

  const { register } = useFormContext<Inputs>();

  return (
    <input
      type="checkbox"
      className={clsx(
        "rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50",
        className,
      )}
      {...register(name as unknown as Path<Inputs>, options)}
      {...rest}
    />
  );
}
