import React from "react";
import { RegisterOptions,  Path, useFormContext, } from "react-hook-form";
import { clsx } from "~lib/utils";

interface Props<T extends string> {
  options?: RegisterOptions
  className?: string;
  id?: string;
  /**
   * Fields injected by FieldWrapper
   */
  required?: boolean;
  name?: T;
}

export default function Switch<T extends string>({
  name,
  options,
  required: parentRequired,
  id = name,
  className = '',
  ...rest
}: Props<T>) {
  type Inputs = {
    [key in T]: boolean;
  };

  type Fields = Inputs;
  const { register } = useFormContext<Fields>();
  const required = parentRequired || options?.required
  const { pattern, ...restOptions } = options || {}

  const registerOptions = {
    ...restOptions,
    ...(required && { required }),
  }

  return (
    <div className={clsx(
      "form-switch",
      className,
    )}>
      <input
        {...register(name as unknown as Path<Fields>, registerOptions)}
        {...rest}
        type="checkbox"
        id={name}
        className="sr-only"
      />
      <label className="bg-gray-400" htmlFor={name}>
        <span className="bg-white shadow-sm" aria-hidden="true"></span>
        <span className="sr-only">Switch label</span>
      </label>
    </div>
  );
}
