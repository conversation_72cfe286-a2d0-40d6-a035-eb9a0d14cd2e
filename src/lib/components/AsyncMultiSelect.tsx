import React from "react";
import { Controller, ControllerProps, Path, useFormContext } from "react-hook-form";
import ReactSelectAsync from "react-select/async";

// TODO: Make this generic
interface Option {
  label: string;
  value: string;
}

/**
 * TODO: Add controller props
 */
interface Props<N extends string> extends React.ComponentProps<typeof ReactSelectAsync> {
  getOptions: (query: string) => Promise<Option[]>;
  placeholder?: string;
  valueOnly?: boolean;
  isMulti?: boolean;
  /* ----- */
  // Injected by FieldWrapper?
  name?: N;
  required?: boolean;
  validate?: ControllerProps<{
    [key in N]: string;
  }>["rules"]["validate"];
}

/**
 * TODO: Save options. Use saved options to display field value.
 * valueOnly - save only the value, not the whole option. Assumes label and
 * value are the same.
 */
export default function AsyncMultiSelect<N extends string>({
  getOptions,
  name,
  required,
  valueOnly = false,
  isMulti = false,
  placeholder,
  validate,
  ...reactSelectProps
}: Props<N>) {
  type T = Option[] | Option | string[] | string | undefined;

  const isOptionArray = (value: T): value is Option[] => isMulti && !valueOnly;
  const isOption = (value: T): value is Option => !isMulti && !valueOnly;
  const isValueArray = (value: T): value is string[] => isMulti && valueOnly;
  const isValue = (value: T): value is string => !isMulti && valueOnly;
  const isNullable = (value: T | null): value is undefined | null => value == undefined;

  type Inputs = {
    [key in N]: T;
  };

  const { control } = useFormContext<Inputs>();

  const controllerProps = {
    rules: {
      ...(required && { required: "This field is required" }),
      ...(validate && { validate }),
    },
  };

  // Remove manually configured options
  const {
    options: _options,
    value: _value,
    onChange: _onChange,
    ...restAttributes
  } = reactSelectProps;

  /**
   * Convert react-select value to react-hook-form value
   * @param newValueRaw Value received from react-select
   * @param onChange react-hook-form onChange hook
   */
  const handleChange = (newValueRaw: Option | Option[] | null, onChange: (value: T) => void) => {
    let newValue: T;

    if (isNullable(newValueRaw)) {
      // null or undefined - field cleared
      newValue = undefined;
    } else if (Array.isArray(newValueRaw)) {
      // isMulti - react-select returns array
      newValue = valueOnly ? newValueRaw.map((v) => v.value) : newValueRaw;
    } else {
      // Regular option
      newValue = valueOnly ? newValueRaw.value : newValueRaw;
    }

    onChange(newValue);
  };

  /**
   * Convert react-hook-form value to react-select value of type Option | null
   */
  const getValue = (value: T): Option | Option[] | null => {
    if (isNullable(value)) {
      return null;
    } else if (isValueArray(value)) {
      // Use value as label
      return value.map((v) => ({ value: v, label: v }));
    } else if (isValue(value)) {
      return { value, label: value };
    }

    // Option[] and Option can be returned as is
    return value;
  };

  const getKey = (value: T): string => {
    if (isOptionArray(value)) {
      return value.map((v) => v.value).join(",");
    } else if (isValueArray(value)) {
      // Use value as label
      return value.join(",");
    } else if (isOption(value)) {
      return value?.value;
    } else if (isValue(value)) {
      return value;
    }

    return "";
  };

  return (
    <Controller
      name={name as unknown as Path<Inputs>}
      control={control}
      render={({ field: { onChange, value, ...restFieldProps } }) => (
        <ReactSelectAsync
          loadOptions={getOptions}
          onChange={(newValue) => {
            handleChange(newValue as Option | Option[] | null, onChange);
          }}
          isMulti={isMulti}
          key={getKey(value)}
          value={getValue(value)}
          {...restFieldProps}
          {...restAttributes}
          placeholder={placeholder}
          isClearable
          isSearchable
        />
      )}
      {...controllerProps}
    />
  );
}
