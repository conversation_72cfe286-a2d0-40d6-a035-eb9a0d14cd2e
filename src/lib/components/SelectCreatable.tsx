import React, { useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import ReactSelectCreatable from "react-select/creatable";

interface Option {
  value: string | number;
  label: string;
}

interface Props extends React.ComponentProps<typeof ReactSelectCreatable> {
  defaultValue?: string | number;
  className?: string;
  addOption?: (value: string) => Promise<Option>;
  /**
   * Injected by FieldWrapper
   */
  name?: string;
  required?: boolean;
}

export default function SelectCreatable({
  defaultValue,
  name,
  required,
  className = "",
  addOption,
  ...selectOptions
}: Props) {
  const { control } = useFormContext();
  const [isLoading, setIsLoading] = useState(false);

  const controllerProps = {
    ...(defaultValue && { defaultValue }),
    rules: required ? { required } : {},
  };

  // Remove manually configured options
  const { options, value, onChange, ...restOptions } = selectOptions;

  // TODO: Transform value to string
  // See - https://stackoverflow.com/questions/62795886/returning-correct-value-using-react-select-and-react-hook-form
  // and https://github.com/react-hook-form/react-hook-form/issues/997
  return (
    <Controller
      name={name}
      control={control}
      defaultValue={defaultValue}
      render={({ field: { onChange, value, ...restProps } }) => (
        <ReactSelectCreatable
          isDisabled={isLoading}
          isLoading={isLoading}
          {...(addOption && {
            async onCreateOption(newOption: string) {
              setIsLoading(true);
              
              await addOption?.(newOption); // Assume error handling is done in addOption
              
              // TODO: Select new option
              // if (option) {
              //   onChange(option.value);
              // }
              setIsLoading(false);
            },
          })}
          {...restOptions}
          value={options.find((c) => (c as Option)?.value === value)}
          onChange={(val) => onChange((val as Option)?.value)}
          options={options}
          isClearable={!required}
          className={className}
          {...restProps}
        />
      )}
      {...controllerProps}
    />
  );
}
