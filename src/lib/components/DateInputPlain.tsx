import React from "react";
import { Path, useForm<PERSON>ontext, Controller, RegisterOptions, PathValue } from "react-hook-form";
import Flatpickr, { DateTimePickerProps } from "react-flatpickr";
import { clsx } from "~lib/utils";
import { dateFlatPickrOptions } from "~lib/constants";

interface Props<T extends string> extends React.InputHTMLAttributes<HTMLInputElement> {
  id?: string;
  className?: string;
  wrapperClassName?: string;
  required?: boolean;
  name?: T;
  options?: RegisterOptions;
  position?: DateTimePickerProps["options"]["position"];
  /**
   * See - https://flatpickr.js.org/examples/#mindate-and-maxdate
   */
  max?: string;
  min?: string;
}

const { static: _, maxDate: _maxDate, ...restFlatPickrOptions } = dateFlatPickrOptions;

/**
 * The DateInput component is a wrapper around the Flatpickr component.
 * TODO: Add all flatPickr options
 */
export default function DateInputPlain<T extends string>({
  name,
  options: _options,
  required: _parentRequired,
  id: _id = name,
  className = "",
  required,
  placeholder = "",
  wrapperClassName = "",
  disabled,
  position = "auto",
  max,
  min,
  ..._rest
}: Props<T>) {
  type Inputs = {
    [key in T]: string;
  };

  type Fields = Inputs;
  const { control } = useFormContext<Fields>();

  const controllerProps = {
    rules: required ? { required } : {},
  };

  return (
    <Controller
      control={control}
      name={name as unknown as Path<Fields>}
      rules={{
        ...(required && { required: true }),
      }}
      render={({ field: { onChange, value, ...restProps } }) => (
        <div className={wrapperClassName}>
          <Flatpickr
            {...restProps}
            value={value as string}
            className={clsx(className, "border border-gray-300 rounded-md px-4")}
            options={{
              ...restFlatPickrOptions,
              ...(max && { maxDate: max }),
              ...(min && { minDate: min }),
              position,
            }}
            disabled={disabled}
            onChange={(_: Date[], dateStr: string) => {
              onChange(dateStr as PathValue<Inputs, Path<Inputs>>);
            }}
            {...(placeholder && { placeholder })}
          />
        </div>
      )}
      {...controllerProps}
    />
  );
}
