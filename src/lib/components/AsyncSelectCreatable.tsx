import React from "react";
import { useF<PERSON><PERSON><PERSON><PERSON><PERSON>, Controller, Path, PathValue, ControllerProps } from "react-hook-form";
import ReactSelectAsyncCreatable from "react-select/async-creatable";

// TODO: Make this generic
interface Option {
  label: string;
  value: string;
}

/**
 * TODO: Add controller props
 */
interface Props<N extends string> extends React.ComponentProps<typeof ReactSelectAsyncCreatable> {
  getOptions: (query: string) => Promise<Option[]>;
  placeholder?: string;
  valueOnly?: boolean;
  // TODO: Fix type
  validate?: ControllerProps<{
    [key in N]: string;
  }>["rules"]["validate"];
  /* -------------- Injected by parent <FieldWrapper /> --------------------- */
  name?: N;
  required?: boolean;
}

/**
 * valueOnly - save only the value, not the whole option. Assumes label and
 * value are the same.
 */
export default function AsyncSelectCreatable<N extends string>({
  getOptions,
  name,
  required = false,
  valueOnly = false,
  placeholder,
  validate,
  ...selectOptions
}: Props<N>) {
  type Inputs = {
    [key in N]: string;
  };

  const { control } = useFormContext<Inputs>();

  const controllerProps = {
    rules: {
      ...(required && { required: "This field is required" }),
      ...(validate && { validate }),
    },
  };

  // Remove manually configured options
  const { options: _options, value: _value, onChange: _onChange, ...restOptions } = selectOptions;

  return (
    <Controller
      name={name as unknown as Path<Inputs>}
      control={control}
      render={({ field: { onChange, value, ...restProps } }) => (
        <ReactSelectAsyncCreatable
          loadOptions={getOptions}
          isClearable={true}
          isSearchable={true}
          onChange={(option) => {
            if (!option) {
              onChange(null);
              return;
            }

            if (valueOnly) {
              onChange((option as Option).value as PathValue<Inputs, Path<Inputs>>);
            } else {
              onChange(option as PathValue<Inputs, Path<Inputs>>);
            }
          }}
          value={value == null ? undefined : valueOnly ? { label: value, value } : value}
          {...restProps}
          {...restOptions}
          {...(placeholder && { placeholder })}
          key={(value || "") as string}
        />
      )}
      {...controllerProps}
    />
  );
}
