import { produce } from "immer";
import { <PERSON><PERSON><PERSON>, Drag<PERSON><PERSON>, useEffect, useState } from "react";
import { ArrayPath, FieldArray, Path, useFieldArray, useFormContext } from "react-hook-form";
import { toast } from "react-toastify";
import { MAX_FILE_SIZE, baseUrl } from "~lib/constants";
import { clsx, formatBytes } from "~lib/utils";
import { upload } from "~lib/utils/uploadFile";

interface OngoingFileUpload {
  file: File;
  progress: number;
  error?: string;
  abortController: AbortController;
}

interface FileUpload {
  name: string;
  url: string;
}

type Props<N extends string> = {
  className?: string;
  wrapperClassName?: string;
  /**
   * Path to be appended to the base URL
   */
  path: string;
  /**
   * @example
   * { "image/jpeg": "JPEG" }
   */
  allowedMimeTypes: Map<string, string>;
  /**
   * Maximum file size in bytes
   * @default MAX_FILE_SIZE in constants
   */
  maxFileSize?: number;
  /**
   * Payload to prepend to form data
   */
  payload?: Record<string, string>;
  /**
   * Lift isUploading state to parent component
   */
  isUploading?: boolean;
  setIsUploading?: (isUploading: boolean) => void;
  /**
   * Fields potentially injected by FieldWrapper
   */
  name?: N;
  required?: boolean;
};

/**
 * TODO: Error on uploading duplicates
 */
function UploadFiles<const N extends string>({
  setIsUploading,
  isUploading,
  name,
  path,
  required,
  payload,
  maxFileSize = MAX_FILE_SIZE,
  allowedMimeTypes,
}: Props<N>) {
  type Inputs = {
    [key in N]: Array<FileUpload>;
  };

  const [dragActive, setDragActive] = useState(false);
  const [incompleteUploads, setIncompleteUploads] = useState<OngoingFileUpload[]>([]);
  const { control, watch, formState } = useFormContext<Inputs>();

  const requiredMessage = "At least one supporting document is required";

  const { append, remove } = useFieldArray({
    control,
    name: name as unknown as ArrayPath<Inputs>,
    rules: {
      ...(required && {
        minLength: {
          value: 1,
          message: requiredMessage,
        },
        // Constraint validation is only applied when the value is changed by the user.
        // See - https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/minlength
        required: "At least one supporting document is required",
      }),
    },
  });

  // TODO: Fix types
  const documents = watch(name as unknown as Path<Inputs>) as unknown as Array<FileUpload>;

  const error = formState.errors?.[name]?.root;
  const errorMessage = error?.message;

  const handleUploadFile = async (file: File) => {
    const abortController = new AbortController();

    setIncompleteUploads(
      produce((draft) => {
        draft.push({ file, progress: 0, abortController });
      })
    );

    try {
      setIsUploading?.(true);

      const response = await upload({
        url: baseUrl + path,
        file,
        ...(payload && { payload }),
        signal: abortController.signal,
        progressCallBack(progressEvent: ProgressEvent) {
          setIncompleteUploads(
            produce((draft) => {
              const fileUpload = draft.find((fileUpload) => fileUpload.file === file);
              if (fileUpload) {
                fileUpload.progress = Math.floor(
                  (progressEvent.loaded / progressEvent.total) * 100
                );
              }
            })
          );
        },
      });

      const url = response.data?.data || "";

      if (!url) {
        // No URL returned by the server
        throw new Error("Invalid response");
      }

      append({
        url: url,
        name: file.name,
      } as unknown as FieldArray<Inputs, ArrayPath<Inputs>>[]);

      setIncompleteUploads(
        produce((draft) => {
          draft.splice(
            draft.findIndex((fileUpload) => fileUpload.file === file),
            1
          );
        })
      );
    } catch (error) {
      setIncompleteUploads(
        produce((draft) => {
          const fileUpload = draft.find((fileUpload) => fileUpload.file === file);
          if (fileUpload) {
            fileUpload.error = typeof error === "string" ? error : error?.message || "Error";
          }
        })
      );
    } finally {
      setIsUploading?.(false);
    }
  };

  const handleDrag = async function (e: DragEvent) {
    e.preventDefault();
    e.stopPropagation();

    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  /**
   * Returns true if all files are valid, false otherwise
   * @param files {File[]}
   * @returns {[boolean, string]} [isValid, errorMessage]
   */
  const validateFiles = (files: File[]): [boolean, string] => {
    const ongoingUploads = incompleteUploads.filter((upload) => !upload.error);

    switch (true) {
      case !files.length:
        return [false, ""];
      case files.some((file: File) => !Array.from(allowedMimeTypes.keys()).includes(file.type)):
        return [false, `Only ${Array.from(allowedMimeTypes.values()).join(", ")} allowed`];
      case files.some((file: File) => file.size > maxFileSize):
        return [false, `Only files below ${formatBytes(maxFileSize)} are allowed`];
      case files.some((file: File) =>
        ongoingUploads.some(
          (upload) => upload.file.name === file.name && upload.file.size === file.size
        )
      ):
        return [false, "A similar file is already being uploaded"];
      default:
        return [true, ""];
    }
  };

  const handleDrop = async function (ev: DragEvent<HTMLDivElement>) {
    ev.preventDefault();
    ev.stopPropagation();

    setDragActive(false);

    const isNotNull = (item: File | null): item is File => item !== null && item !== undefined;

    const files = ev.dataTransfer.items
      ? Array.from(ev.dataTransfer.items)
          .filter((item) => item.kind === "file")
          .map((item) => item.getAsFile())
          .filter(isNotNull)
      : Array.from(ev.dataTransfer.files).filter(isNotNull);

    const [valid, error] = validateFiles(files);

    if (error && !valid) toast.error(error);
    if (error && valid) toast.warning(error);
    if (!valid) return;

    for (const file of files) {
      await handleUploadFile(file);
    }
  };

  const handleChange = async (e: ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    e.stopPropagation();

    const files = Array.from(e.target.files || []);

    const [valid, error] = validateFiles(files);

    if (error) toast.error(error);
    if (!valid) return;

    for (const file of files) {
      await handleUploadFile(file);
    }
  };

  const uploadLoading = incompleteUploads.some((upload) => upload.progress < 100 && !upload.error);

  useEffect(() => {
    setIsUploading?.(uploadLoading);
  }, [uploadLoading, setIsUploading]);

  return (
    <div>
      <div className="px-2 py-4">
        <div
          className={clsx(
            "flex items-center justify-center w-full h-56 border-2 border-dashed border-blue-300 rounded-lg",
            dragActive ? "bg-blue-50" : "bg-white"
          )}
          onDragEnter={handleDrag}
        >
          <div className="flex flex-col items-center">
            {/* prettier-ignore */}
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-24 h-24 text-blue-400">
              <path fillRule="evenodd" d="M10.5 3.75a6 6 0 00-5.98 6.496A5.25 5.25 0 006.75 20.25H18a4.5 4.5 0 002.206-8.423 3.75 3.75 0 00-4.133-4.303A6.001 6.001 0 0010.5 3.75zm2.03 5.47a.75.75 0 00-1.06 0l-3 3a.75.75 0 101.06 1.06l1.72-1.72v4.94a.75.75 0 001.5 0v-4.94l1.72 1.72a.75.75 0 101.06-1.06l-3-3z" clipRule="evenodd" />
            </svg>

            <div>
              <p className="text-center">
                Drag and drop here or
                <label className="px-2 underline cursor-pointer text-blue-400 hover:text-blue-500">
                  browse
                  <input
                    type="file"
                    name="documents"
                    id="documents"
                    className="hidden"
                    multiple={true}
                    accept={Array.from(allowedMimeTypes.keys()).join(",")}
                    onChange={handleChange}
                  />
                </label>
              </p>

              <p className="text-center text-xs text-gray-400 font-medium">
                {Array.from(allowedMimeTypes.values()).join(", ")} files only. Max file size is{" "}
                {formatBytes(maxFileSize)}.
              </p>
            </div>
          </div>

          {dragActive && (
            <div
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
              className="absolute inset-0 z-10 top-0 left-0 right-0 bottom-0 w-full h-full opacity-0"
            />
          )}
        </div>

        {/* {<span className="text-xs text-red-500">{error ? errorMessage : ""}</span>} */}
      </div>

      <div className="px-4 mb-4">
        {/* Show in progress uploads */}
        {incompleteUploads.map((upload, index) => (
          <div key={index} className="flex gap-4 mb-2">
            <div className="break-all flex-grow">{upload.file.name}</div>

            {upload.error ? (
              <div className="flex gap-2 items-center">
                <p className="text-red-500 text-sm">{upload.error}</p>

                {/* prettier-ignore */}
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-6 h-6 text-red-500">
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
              </svg>
              </div>
            ) : (
              <div className="flex items-center justify-start">
                <div className="w-32 bg-gray-200 rounded-full h-1.5">
                  <div
                    className="bg-blue-600 h-1.5 rounded-full dark:bg-blue-500"
                    style={{ width: `${upload.progress}%` }}
                  />
                </div>
              </div>
            )}

            <div className="flex items-center">
              <button
                className={clsx("text-red-500 hover:text-red-600")}
                type="button"
                title={upload.error ? "Remove" : "Cancel"}
                onClick={() => {
                  if (upload.error) {
                    // Remove file with network error from the UI
                    setIncompleteUploads(
                      produce(incompleteUploads, (draft) => {
                        draft.splice(index, 1);
                      })
                    );
                  } else {
                    // Cancel upload
                    upload.abortController?.abort();
                  }
                }}
              >
                {/* prettier-ignore */}
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                </svg>
              </button>
            </div>
          </div>
        ))}

        {documents.map((doc, index) => (
          <div key={index} className="flex gap-4 mb-2">
            <div className="break-all flex-grow">{doc.name}</div>

            <div className="flex gap-2 items-center justify-center">
              {/* prettier-ignore */}
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-6 h-6 text-green-500">
                <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>

            <div className="flex items-center">
              <button
                className="text-red-500 hover:text-red-600"
                title="Remove"
                onClick={() => {
                  remove(index);
                }}
                type="button"
              >
                {/* prettier-ignore */}
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                </svg>
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default UploadFiles;
