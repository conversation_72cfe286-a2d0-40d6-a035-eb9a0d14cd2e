import React, { useRef, useEffect } from "react";
import Transition from "./Transition";
import { clsx } from "~lib/utils";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { cn } from "~lib/utils/cn";

interface Props {
  modalOpen: boolean;
  onClose: () => void;
  title: string;
  id: string;
  size?: "xs" | "sm" | "md" | "lg" | "xl" | "xxl";
  children: React.ReactNode;
  headingClassName?: string;
}
export const Modal: React.FC<Props> = ({ size = "xl", headingClassName = "", ...props }: Props) => {
  // function ModalBasic({ children, id, title, modalOpen, setModalOpen }) {
  const modalContent = useRef(null);

  // close if the esc key is pressed
  useEffect(() => {
    const keyHandler = ({ keyCode }) => {
      if (!props.modalOpen || keyCode !== 27) return;
      props.modalOpen = false;
    };
    document.addEventListener("keydown", keyHandler);
    return () => document.removeEventListener("keydown", keyHandler);
  });

  const maxWidth = {
    xs: "max-w-xl",
    sm: "max-w-2xl",
    md: "max-w-3xl",
    lg: "max-w-4xl",
    xl: "max-w-5xl",
    xxl: "max-w-6xl",
  };

  return (
    <>
      {/* Modal backdrop */}
      <Transition
        className="fixed inset-0 z-50 bg-gray-900 bg-opacity-30 transition-opacity"
        show={props.modalOpen}
        enter="transition ease-out duration-200"
        enterStart="opacity-0"
        enterEnd="opacity-100"
        leave="transition ease-out duration-100"
        leaveStart="opacity-100"
        leaveEnd="opacity-0"
        aria-hidden="true"
        appear={true}
      />
      {/* Modal dialog */}
      <Transition
        id={props.id}
        className="fixed inset-0 z-50 my-4 flex transform items-center justify-center overflow-hidden px-4 sm:px-6"
        role="dialog"
        aria-modal="true"
        show={props.modalOpen}
        enter="transition ease-in-out duration-200"
        enterStart="opacity-0 translate-y-4"
        enterEnd="opacity-100 translate-y-0"
        leave="transition ease-in-out duration-200"
        leaveStart="opacity-100 translate-y-0"
        leaveEnd="opacity-0 translate-y-4"
        appear={true}
      >
        <div
          ref={modalContent}
          className={clsx(
            "max-h-full w-full overflow-auto rounded bg-white shadow-lg",
            maxWidth[size],
          )}
        >
          {/* Modal header */}
          <div className="px-5 py-4 ">
            <div className="flex items-center justify-between">
              <h3 className={cn("my-2 grow text-2xl font-medium text-[#1A2853]", headingClassName)}>
                {props.title}
              </h3>
              <button
                className="text-gray-400 hover:text-gray-500"
                onClick={(e) => {
                  e.stopPropagation();
                  props.onClose();
                }}
              >
                <div className="sr-only">Close</div>
                <XMarkIcon className="w-6 text-[#374151]" strokeWidth={2} />
              </button>
            </div>
          </div>
          {props.children}
        </div>
      </Transition>
    </>
  );
};

export default Modal;
