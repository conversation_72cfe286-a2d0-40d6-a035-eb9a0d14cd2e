import { useRef } from "react";
import { produce } from "immer";

const LENGTH = 4;
const BACKSPACE_KEY = "Backspace";
const TAB_KEY = "Tab";

type OTP = [string, string, string, string];

interface Props {
  setValue: React.Dispatch<React.SetStateAction<OTP>>;
  value: OTP;
}

/**
 * TODO: Handle paste
 * TODO: Handle Shift+Tab
 * TODO: Take in default value
 */
const OTPInput = ({ setValue, value }: Props) => {
  const inputRefs = useRef<Array<HTMLInputElement>>([]);

  return (
    <div className="flex gap-2">
      {new Array(LENGTH).fill(0).map((_, i) => (
        <div>
          <input
            type="text"
            className="w-12 h-12 p-1 rounded border-gray-300 align-middle text-center"
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              if (e.target.value && i < LENGTH - 1) {
                // Move to the next input on entering a value in the current input
                inputRefs.current[i + 1]?.focus();
              }

              // Update the state
              setValue(produce((draft) => {
                draft[i] = e.target.value || "";
              }))
            }}
            onKeyDown={(e: React.KeyboardEvent) => {
              if (e.key == BACKSPACE_KEY && !(e.currentTarget as HTMLInputElement).value && i > 0) {
                // Move to the previous input on hitting backspace with an empty code input
                e.preventDefault();
                inputRefs.current[i - 1]?.focus();
              }

              if (e.key != BACKSPACE_KEY && (e.currentTarget as HTMLInputElement).value) {
                // Move to the next input if someone attempts to input in a code input with an existing value
                if (i < LENGTH - 1) {
                  inputRefs.current[i + 1]?.focus();
                } else {
                  e.preventDefault();
                  (e.currentTarget as HTMLInputElement).blur();
                }
              }

              if (
                ![
                  BACKSPACE_KEY,
                  TAB_KEY,
                  ...Array.from({ length: 10 })
                    .map((_, i) => i)
                    .map(String),
                ].includes(e.key)
              ) {
                // Prevent inputting non-digit charactes
                e.preventDefault();
              }
            }}
            ref={(el) => {
              inputRefs.current[i] = el;
            }}
            value={value?.[i]}
          />
        </div>
      ))}
    </div>
  );
};

export default OTPInput;
