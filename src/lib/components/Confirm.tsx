import { Modal } from ".";

interface Props {
  children: React.ReactNode;
  handleConfirm: (answer: boolean) => void;
  modalOpen: boolean;
}

const Confirm = ({ children, handleConfirm, modalOpen }: Props) => {
  return (
    <Modal
      id="confirm-modal"
      modalOpen={modalOpen}
      onClose={() => {
        handleConfirm(false);
      }}
      title="Confirm"
      size="sm"
    >
      <div className="p-4">
        <div className="mb-4">{children}</div>

        <div className="flex gap-4 justify-end">
          <button
            onClick={() => {
              handleConfirm(false);
            }}
            className="flex gap-2 py-2 px-4 font-medium rounded border border-red-500 text-red-500 enabled:hover:text-red-600 enabled:hover:border-red-600"
          >
            Cancel
          </button>

          <button
            onClick={() => {
              handleConfirm(true);
            }}
            className="flex gap-2 py-2 px-4 font-medium rounded border border-blue-500 text-blue-500 enabled:hover:text-blue-600 enabled:hover:border-blue-600"
          >
            OK
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default Confirm;
