import { Disclosure } from "@headlessui/react";
import Keycloak from "keycloak-js";

interface Props {
  keycloak: Keycloak.KeycloakInstance;
  isLoading: boolean;
  error?: string;
  isAuthenticated: boolean;
  portalType: "payer" | "provider" | "admin"| "onboard";
}

const Welcome = ({ isAuthenticated, isLoading, keycloak, error, portalType }: Props) => {
  return (
    <main className="bg-gray-50 flex justify-center items-center w-full min-h-screen">
      <div className="bg-white shadow-sm p-8 rounded-lg w-full max-w-md">
        <div className="flex gap-2 items-center justify-center">
          {/* Logo */}
          {/* prettier-ignore */}
          <svg fill="none" viewBox="0 0 70 65" version="1.1" xmlns="http://www.w3.org/2000/svg" className="w-8 h-8">
            <g><path fill="#449fda" d="M 67.3,25.1 57,7.5 A 14.4,14.4 0 0 0 44.7,0.3 H 34.1 a 14.3,14.3 0 0 1 12.4,7.2 l 2.4,4.2 0.3,0.4 10.1,17.7 a 5.2,5.2 0 0 1 0,5.2 l -10.1,17.6 -0.3,0.5 -2.4,4.1 A 14.4,14.4 0 0 1 34,64.4 H 44.6 A 14.3,14.3 0 0 0 57,57.2 L 67.2,39.6 a 14.6,14.6 0 0 0 0,-14.5 z" /><path fill="#304254" d="m 22.6,7.5 -2.4,4.2 a 5.2,5.2 0 0 1 4.2,-2.2 h 30.8 a 5.2,5.2 0 0 1 4.2,2.2 L 57,7.5 A 14.4,14.4 0 0 0 44.6,0.3 H 35 A 14.3,14.3 0 0 0 22.6,7.5 Z"/><path fill="#449fda" d="M 22.6,57.2 20.3,53.1 20,52.6 9.8,35 a 5.2,5.2 0 0 1 0,-5.1 L 20,12 c 0,-0.2 0.2,-0.3 0.3,-0.5 L 22.6,7.4 A 14.4,14.4 0 0 1 35.1,0.3 H 24.4 A 14.3,14.3 0 0 0 12.1,7.5 L 1.9,25.2 a 14.5,14.5 0 0 0 0,14.4 l 10.2,17.6 a 14.4,14.4 0 0 0 12.3,7.3 H 35.1 A 14.3,14.3 0 0 1 22.6,57.2 Z" /><path fill="#304254" d="m 46.5,57.2 2.4,-4.1 a 5.2,5.2 0 0 1 -4.2,2.1 H 13.8 A 5.2,5.2 0 0 1 9.6,53.1 l 2.4,4.1 a 14.4,14.4 0 0 0 12.4,7.2 h 9.7 a 14.3,14.3 0 0 0 12.4,-7.2 z" /></g>
          </svg>

          <h1 className="text-4xl font-mono text-blue-400 font-bold border-r-2 border-gray-300 pr-3">
            LCT
          </h1>

          <h1 className="text-2xl text-gray-500 font-semibold pl-2 font-sans capitalize">
            {portalType} Portal
          </h1>
        </div>

        {isAuthenticated ? (
          // This part should never be seen since the main page should be shown if authenticated
          <div>
            <div className="p-8 flex justify-center items-center">
              <div className="flex flex-col items-center">
                {/* prettier-ignore */}
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1} stroke="currentColor" className="w-12 h-12 text-green-600">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
                </svg>

                <h3 className="font-medium text-lg text-center text-gray-500">
                  Authentication Successful
                </h3>

                <p className="text-sm text-center text-gray-400">
                  Login completed successfully. You should be directed to home page automatically...
                </p>
              </div>
            </div>

            <div className="flex gap-4 justify-center">
              <button
                className="flex gap-2 py-2 px-4 font-medium rounded border border-red-500 text-red-500 enabled:hover:text-red-600 enabled:hover:border-red-600"
                onClick={() => keycloak.logout()}
              >
                Log Out
              </button>
            </div>
          </div>
        ) : isLoading ? (
          <div>
            <div className="py-4 flex justify-center items-center">
              {/* prettier-ignore */}
              <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-blue-400 w-8 h-8">
                <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z">
                  <animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite" />
                </path>
              </svg>
            </div>

            <p className="text-sm text-center text-gray-400">Checking authentication status...</p>
          </div>
        ) : error ? (
          <div>
            <div className="p-8 flex justify-center items-center">
              <div className="flex flex-col items-center">
                {/* prettier-ignore */}
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1} stroke="currentColor" className="w-12 h-12 text-red-500">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                </svg>

                <h3 className="font-medium text-lg text-center text-gray-500">
                  Authentication Error
                </h3>

                <p className="text-sm text-center text-gray-400">
                  Something went wrong when trying to log you in...
                </p>

                <div className="w-full px-2">
                  <div className="rounded-2xl bg-white p-2">
                    <Disclosure>
                      {({ open }) => (
                        <>
                          <Disclosure.Button className="flex w-full justify-between rounded-lg bg-red-50 px-4 py-2 text-left text-sm font-medium text-gray-900 hover:bg-red-100 focus:outline-none focus-visible:ring focus-visible:ring-gray-500 focus-visible:ring-opacity-75">
                            <span className="text-red-400">Error Details</span>

                            <>
                              {/* prettier-ignore */}
                              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={`${
                                  open ? "rotate-180 transform" : ""
                                } h-5 w-5 text-red-500`}>
                                <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
                              </svg>
                            </>
                          </Disclosure.Button>
                          <Disclosure.Panel className="px-4 pt-4 pb-2 text-sm text-gray-500">
                            {error}
                          </Disclosure.Panel>
                        </>
                      )}
                    </Disclosure>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex gap-4 justify-center">
              <button
                className="flex gap-2 py-2 px-4 font-medium rounded border border-red-500 text-red-500 enabled:hover:text-red-600 enabled:hover:border-red-600"
                onClick={() => keycloak.logout()}
              >
                Log Out
              </button>

              <button
                className="flex gap-2 py-2 px-4 font-medium rounded border border-blue-500 text-blue-500 enabled:hover:text-blue-600 enabled:hover:border-blue-600"
                onClick={() => keycloak.login()}
              >
                Retry
              </button>
            </div>
          </div>
        ) : (
          <div>
            {/* This is not supposed to be seen. The user is authenticated but the home page has not been loaded. */}
            <div className="p-8 flex justify-center items-center">
              <div className="flex flex-col items-center">
                {/* prettier-ignore */}
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1} stroke="currentColor" className="w-12 h-12 text-red-500">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                </svg>


                <h3 className="font-medium text-lg text-center text-gray-500">
                  Authentication Error
                </h3>

                <p className="text-sm text-center text-gray-400">
                  Something went woefully wrong...
                </p>
              </div>
            </div>

            <div className="flex gap-4 justify-center">
              <button
                className="flex gap-2 py-2 px-4 font-medium rounded border border-blue-500 text-blue-500 enabled:hover:text-blue-600 enabled:hover:border-blue-600"
                onClick={() => keycloak.login()}
              >
                Retry
              </button>
            </div>
          </div>
        )}
      </div>
    </main>
  );
};

export default Welcome;
