import React, { useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import ReactSelectCreatable from "react-select/creatable";

interface Option {
  value: string;
  label: string;
}

interface Props extends React.ComponentProps<typeof ReactSelectCreatable> {
  className?: string;
  /**
   * Injected by FieldWrapper
   */
  name?: string;
  required?: boolean;
  defaultOptions?: Array<Option>;
}

/**
 * Chips created by the user
 */
export default function Chips({
  name,
  required,
  className = "",
  defaultOptions = [],
  ...selectOptions
}: Props) {
  const { control } = useFormContext();
  const [options, setOptions] = useState<Array<Option>>([...defaultOptions]);

  const controllerProps = {
    rules: required ? { required } : {},
  };

  // Remove manually configured options
  const { value: _value, onChange: _onChange, ...restOptions } = selectOptions;

  // TODO: Transform value to string
  // See - https://stackoverflow.com/questions/62795886/returning-correct-value-using-react-select-and-react-hook-form
  // and https://github.com/react-hook-form/react-hook-form/issues/997
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value: rhfValue, ...restProps } }) => (
        <ReactSelectCreatable
          isMulti
          onCreateOption={(input: string) => {
            const option = { value: input, label: input };
            setOptions((prevOptions) => [...prevOptions, option]);
            onChange([...(rhfValue || []), input]);
          }}
          {...restOptions}
          value={options.filter((c) => rhfValue?.includes((c as Option)?.value))}
          onChange={(selectedOptions: Array<Option>) => {
            onChange(selectedOptions.map((o) => o.value));
            setOptions((options) => [
              ...options.filter((o) => selectedOptions.includes(o)),
              ...defaultOptions,
            ]);
          }}
          options={options}
          isClearable={!required}
          className={className}
          placeholder="Type to add"
          {...restProps}
        />
      )}
      {...controllerProps}
    />
  );
}
