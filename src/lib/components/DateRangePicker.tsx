import { Fragment, useState } from "react";
import Flatpickr, { DateTimePickerProps } from "react-flatpickr";
import { Transition, Popover } from "@headlessui/react";
import { clsx, formatDateISO } from "~lib/utils";
import {
  DateRange,
  OptionalDateRange,
  dateDayEquals,
  friendlyDateRange,
  getLastXDays,
  getLastXMonths,
  getMonthToDate,
  getNextDay,
  getPreviousDay,
  getQuarterToDate,
  getToday,
  getYearToDate,
} from "~lib/utils/dates";
import { dropdownTransitions } from "~lib/constants";
import { usePopper } from "react-popper";

interface Props {
  value: OptionalDateRange;
  onChange: React.Dispatch<React.SetStateAction<OptionalDateRange>>;
  footer?: React.ReactNode;
  options?: DateTimePickerProps["options"];
}

const flatPickrOptions = {
  mode: "single",
  inline: true,
  position: "auto center",
  monthSelectorType: "static",
  dateFormat: "Y-m-d",
  // defaultDate: "today",
  prevArrow:
    '<svg class="fill-current" width="7" height="11" viewBox="0 0 7 11"><path d="M5.4 10.8l1.4-1.4-4-4 4-4L5.4 0 0 5.4z" /></svg>',
  nextArrow:
    '<svg class="fill-current" width="7" height="11" viewBox="0 0 7 11"><path d="M1.4 10.8L0 9.4l4-4-4-4L1.4 0l5.4 5.4z" /></svg>',
} as const;

const DateRangePicker = ({ value, onChange, footer, options }: Props) => {
  const dateRangeDefaults: [string, () => DateRange | [undefined, undefined]][] = [
    ["Today", () => getToday()],
    ["Last 7 days", () => getLastXDays(7)],
    ["Month to date", () => getMonthToDate()],
    ["Last 30 days", () => getLastXDays(30)],
    ["Quarter to date", () => getQuarterToDate()],
    ["Last 3 months", () => getLastXMonths(3)],
    ["Last 12 months", () => getLastXMonths(12)],
    ["Year to date", () => getYearToDate()],
    ["Reset", () => [undefined, undefined]],
  ];

  const dateArrowsDisabled =
    value.every((date) => date === undefined) || !dateDayEquals(value[0], value[1]);

  const [buttonRef, setButtonRef] = useState<HTMLButtonElement | null>(null);
  const [panelRef, setPanelRef] = useState<HTMLDivElement | null>(null);
  const { styles, attributes } = usePopper(buttonRef, panelRef);

  return (
    <Popover className="flex rounded-md shadow-sm" role="group">
      <button
        type="button"
        className={clsx(
          "px-4 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-200 rounded-l-lg hover:bg-gray-100 hover:text-blue-700 focus:ring-2 focus:ring-blue-700 focus:text-blue-700 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-blue-500 dark:focus:text-white",
          dateArrowsDisabled && "opacity-50 cursor-not-allowed"
        )}
        disabled={dateArrowsDisabled}
        onClick={() => {
          onChange((dateRange) =>
            dateRange[0] && dateRange[1]
              ? [getPreviousDay(dateRange[0]), getPreviousDay(dateRange[1])]
              : [undefined, undefined]
          );
        }}
      >
        {/* prettier-ignore */}
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
          <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
        </svg>
      </button>

      <Popover.Button
        className={clsx(
          "inline-flex gap-2 items-end text-gray-600 hover:text-blue-700 justify-center bg-white border-t border-b border-gray-200 hover:bg-gray-100 px-4 py-2 text-sm font-medium focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700 whitespace-nowrap"
        )}
        title="Select date range"
        ref={setButtonRef}
      >
        {/* prettier-ignore */}
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
          <path strokeLinecap="round" strokeLinejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5m-9-6h.008v.008H12v-.008zM12 15h.008v.008H12V15zm0 2.25h.008v.008H12v-.008zM9.75 15h.008v.008H9.75V15zm0 2.25h.008v.008H9.75v-.008zM7.5 15h.008v.008H7.5V15zm0 2.25h.008v.008H7.5v-.008zm6.75-4.5h.008v.008h-.008v-.008zm0 2.25h.008v.008h-.008V15zm0 2.25h.008v.008h-.008v-.008zm2.25-4.5h.008v.008H16.5v-.008zm0 2.25h.008v.008H16.5V15z" />
        </svg>
        {friendlyDateRange(value)}
      </Popover.Button>

      <button
        type="button"
        className={clsx(
          "px-4 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-200 rounded-r-md hover:bg-gray-100 hover:text-blue-700 focus:ring-2 focus:ring-blue-700 focus:text-blue-700 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-blue-500 dark:focus:text-white",
          dateArrowsDisabled && "opacity-50 cursor-not-allowed"
        )}
        disabled={dateArrowsDisabled}
        onClick={() => {
          onChange((dateRange) =>
            dateRange[0] && dateRange[1]
              ? [getNextDay(dateRange[0]), getNextDay(dateRange[1])]
              : [undefined, undefined]
          );
        }}
      >
        {/* prettier-ignore */}
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
          <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
        </svg>
      </button>

      <Transition as={Fragment} {...dropdownTransitions}>
        <Popover.Panel
          // className="flex gap-8 absolute z-10 mt-12 origin-top-right focus:outline-none bg-gray-50 rounded-lg shadow p-4 overflow-y-auto border border-gray-300"
          className="flex gap-8 mt-2 z-10 focus:outline-none bg-gray-50 rounded-lg shadow p-4 overflow-y-auto border border-gray-300"
          ref={setPanelRef}
          style={styles["popper"]}
          {...attributes["popper"]}
        >
          <div className="flex flex-col gap-2 lg:whitespace-nowrap">
            {dateRangeDefaults.map(([label, dateRange]) => (
              <button
                key={label}
                type="button"
                className="px-4 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-200 rounded-md hover:bg-gray-100 hover:text-blue-700 focus:ring-2 focus:ring-blue-700 focus:text-blue-700 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-blue-500 dark:focus:text-white"
                onClick={() => {
                  onChange(dateRange);
                }}
              >
                {label}
              </button>
            ))}
          </div>

          <div>
            <div className="flex gap-4 mb-4 flex-wrap lg:flex-nowrap">
              <div className="flex flex-col gap-4">
                <Flatpickr
                  className="border border-gray-300 rounded-md px-4"
                  options={{
                    ...flatPickrOptions,
                    ...options,
                  }}
                  onChange={(date) => {
                    onChange((dateRange) => [date[0], dateRange[1]]);
                  }}
                  value={formatDateISO(value[0])}
                  placeholder="From"
                />
              </div>

              <div className="flex flex-col gap-4">
                <Flatpickr
                  className="border border-gray-300 rounded-md px-4"
                  options={{
                    ...flatPickrOptions,
                  }}
                  onChange={(date) => {
                    onChange((dateRange) => [dateRange[0], date[0]]);
                  }}
                  value={formatDateISO(value[1])}
                  placeholder="To"
                />
              </div>
            </div>

            {footer ? (
              footer
            ) : (
              <div className="flex justify-end">
                <Popover.Button className="px-4 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-200 rounded-md hover:bg-gray-100 hover:text-blue-700 focus:ring-2 focus:ring-blue-700 focus:text-blue-700 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-blue-500 dark:focus:text-white">
                  Close
                </Popover.Button>
              </div>
            )}
          </div>
        </Popover.Panel>
      </Transition>
    </Popover>
  );
};

export default DateRangePicker;
