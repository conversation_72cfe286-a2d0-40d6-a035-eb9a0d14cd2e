import { Fragment } from "react";
import { clsx, pluralize } from "~lib/utils";
import { narthurPaginate } from "~lib/utils/pagination";

interface Props {
  totalElements: number;
  totalPages: number;
  page: number;
  size: number;
  setPage: (page: number) => void;
  setSize: (size: number) => void;
  isLoading?: boolean;
}

const PAGE_SIZES = [10, 20, 50, 100];

export default function Pagination({
  totalElements,
  page,
  setPage,
  setSize,
  totalPages,
  isLoading,
}: Props) {
  function handleSizeChange(e) {
    setSize(Number(e.target.value) || 0);
  }

  return (
    <div className="flex justify-center px-4">
      <nav
        className="flex flex-wrap items-center gap-2 text-sm"
        role="navigation"
        aria-label="Navigation"
      >
        <p className="text-gray-400">
          {totalElements || 0} {pluralize(totalElements || 0, "item")}
        </p>

        <button
          onClick={(e) => {
            e.stopPropagation();
            setPage(page - 1);
          }}
          className={clsx(
            "inline-flex items-center justify-center rounded p-3 border border-gray-200 text-gray-600",
            page == 1 && "opacity-60 cursor-not-allowed"
          )}
          disabled={page == 1}
        >
          <svg className="h-4 w-4 fill-current" viewBox="0 0 16 16">
            <path d="M9.4 13.4l1.4-1.4-4-4 4-4-1.4-1.4L4 8z" />
          </svg>
        </button>

        <ul className="flex gap-2 items-center">
          {narthurPaginate(page, totalPages).map((item, index) => (
            <Fragment key={index}>
              {item === "..." ? (
                <span className="text-gray-300 font-bold px-2">...</span>
              ) : (
                <button
                  onClick={() => {
                    setPage(item);
                  }}
                  className={clsx(
                    "rounded px-4 py-2 font-medium text-gray-600",
                    page == item ? "border-2 border-blue-500" : "border border-gray-200"
                  )}
                >
                  {item}
                </button>
              )}
            </Fragment>
          ))}
        </ul>

        <button
          onClick={(e) => {
            e.stopPropagation();
            setPage(page + 1);
          }}
          className={clsx(
            "inline-flex items-center justify-center rounded p-3 border border-gray-200 text-gray-600",
            page == totalPages && "opacity-60 cursor-not-allowed"
          )}
          disabled={page == totalPages}
        >
          <svg className="h-4 w-4 fill-current" viewBox="0 0 16 16">
            <path d="M6.6 13.4L5.2 12l4-4-4-4 1.4-1.4L12 8z" />
          </svg>
        </button>

        <div className="flex gap-2">
          <select
            id="size"
            name="size"
            onChange={handleSizeChange}
            className="rounded border-gray-200 text-gray-500 text-sm"
            defaultValue={PAGE_SIZES[0]}
          >
            {PAGE_SIZES.map((size, index) => (
              <option value={size} key={index}>
                {size} per page
              </option>
            ))}
          </select>
        </div>

        <p className="text-gray-400">
          Page {page} of {totalPages}
        </p>
      </nav>

      <div className="flex items-center ml-2">
        {isLoading && (
          <>
            {/* prettier-ignore */}
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" className="text-gray-400 w-6 h-6">
              <path fill="currentColor" d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z">
                <animateTransform attributeName="transform" type="rotate" dur="0.75s" values="0 12 12;360 12 12" repeatCount="indefinite"/>
              </path>
            </svg>
          </>
        )}
      </div>
    </div>
  );
}
