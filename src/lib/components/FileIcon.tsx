type Props = {
  extension: "csv" | "xls" | "xlsx" | "jpg" | "jpeg" | "png" | "pdf" | "doc" | "docx" | string;
};

export default function FileIcon({ extension }: Props) {
  switch (extension) {
    case "csv":
      return (
        // prettier-ignore
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" fillRule="evenodd" clipRule="evenodd" viewBox="0 0 512 512" className="w-12 h-12 text-green-500">
          <path d="M83.5-.5h253a146.342 146.342 0 0 1 10.5 10 7256.284 7256.284 0 0 1 94 109c.5 21.331.667 42.664.5 64 8.516-.615 16.849.218 25 2.5 8.144 4.143 13.311 10.643 15.5 19.5l.5 81.5a1815.01 1815.01 0 0 1-1.5 84.5c-3.167 8.5-9 14.333-17.5 17.5a90.896 90.896 0 0 1-22 1.5c.167 35.668 0 71.335-.5 107-2.29 7.117-6.79 12.117-13.5 15h-344c-6.71-2.883-11.21-7.883-13.5-15-.5-35.665-.667-71.332-.5-107-13.374 2.714-24.874-.619-34.5-10a59.026 59.026 0 0 1-5-9 1816.117 1816.117 0 0 1-1.5-84.5l.5-81.5c3.167-11.167 10.333-18.333 21.5-21.5 6.325-.5 12.658-.666 19-.5-.167-56.001 0-112.001.5-168 2.29-7.117 6.79-12.117 13.5-15Zm7 21h231c-.474 35.715.026 71.382 1.5 107a17.842 17.842 0 0 0 5.5 4.5c30.665.5 61.332.667 92 .5v50h-330v-162Zm42 183c16.723-1.322 33.057.511 49 5.5.457.414.791.914 1 1.5a231.843 231.843 0 0 1-7.5 25 51.92 51.92 0 0 0-15.5-4.5c-17.016-2.912-32.349.755-46 11-9.68 10.529-14.346 23.029-14 37.5.262 32.762 16.762 49.429 49.5 50a130.4 130.4 0 0 0 27.5-4 173.69 173.69 0 0 0 4 23l-1 2c-23.258 7.774-46.592 7.941-70 .5-23.11-8.779-37.276-25.279-42.5-49.5-5.4-25.962-.4-49.629 15-71 13.741-14.935 30.574-23.935 50.5-27Zm112 0c13.529-.801 26.862.365 40 3.5a33.27 33.27 0 0 1 8.5 3.5 123.755 123.755 0 0 1-6.5 26c-9.885-4.244-20.218-6.577-31-7-8.205.008-15.038 3.008-20.5 9-2.978 7.555-1.145 13.722 5.5 18.5a385.08 385.08 0 0 0 34 15c22.853 12.214 30.353 30.714 22.5 55.5-4.833 10.167-12.333 17.667-22.5 22.5-21.395 7.923-43.062 8.589-65 2a34.399 34.399 0 0 1-10.5-4.5 19955.86 19955.86 0 0 1 5.5-26 501.66 501.66 0 0 1 17 5.5c12.045 3.204 24.045 3.204 36 0 8.622-4.86 11.122-12.026 7.5-21.5a45.255 45.255 0 0 0-8.5-7.5 266.655 266.655 0 0 0-24-10c-10.596-4.601-19.429-11.434-26.5-20.5-10.495-23.515-4.995-42.348 16.5-56.5 7.132-3.44 14.465-5.94 22-7.5Zm68 2a320.436 320.436 0 0 1 35.5 1 3760.158 3760.158 0 0 1 32.5 116 1913.538 1913.538 0 0 1 33-116 204.664 204.664 0 0 1 35-.5 28013.102 28013.102 0 0 0-50 147.5c-13 1.333-26 1.333-39 0a9510.57 9510.57 0 0 1-47-148Zm-222 184h330v96h-330v-96Z" />
        </svg>
      );
    case "xls":
    case "xlsx":
      return (
        // prettier-ignore
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" fillRule="evenodd" clipRule="evenodd" viewBox="0 0 512 512" className="w-12 h-12 text-green-500">
          <path d="M511.5 89.5v332c-2.147 4.311-5.48 7.478-10 9.5-71.333.5-142.666.667-214 .5.167 18.003 0 36.003-.5 54-3.281 6.639-8.614 9.973-16 10A23178.143 23178.143 0 0 1 13.5 448c-6.382-1.343-11.048-4.843-14-10.5v-364c2.952-5.657 7.618-9.157 14-10.5A23185.08 23185.08 0 0 1 271 15.5c7.724-.472 13.057 2.861 16 10 .5 17.997.667 35.997.5 54 71.334-.167 142.667 0 214 .5 4.52 2.022 7.853 5.189 10 9.5Zm-224 22h192v288h-192v-32c18.003.167 36.003 0 54-.5 6.987-2.807 10.32-7.973 10-15.5.32-7.527-3.013-12.693-10-15.5-17.997-.5-35.997-.667-54-.5v-32c18.003.167 36.003 0 54-.5 6.987-2.807 10.32-7.973 10-15.5.32-7.527-3.013-12.693-10-15.5-17.997-.5-35.997-.667-54-.5v-32c18.003.167 36.003 0 54-.5 6.987-2.807 10.32-7.973 10-15.5.32-7.527-3.013-12.693-10-15.5-17.997-.5-35.997-.667-54-.5v-32c18.003.167 36.003 0 54-.5 6.987-2.807 10.32-7.973 10-15.5.32-7.527-3.013-12.693-10-15.5-17.997-.5-35.997-.667-54-.5v-32Zm-85 49c14.483-1.018 20.983 5.649 19.5 20a36.16 36.16 0 0 1-4 7 3614.57 3614.57 0 0 0-49.5 64 4298.192 4298.192 0 0 1 51.5 59c5.614 16.384-.219 24.217-17.5 23.5l-4-2-49.5-55.5a1863.427 1863.427 0 0 1-44.5 55.5c-16.384 5.614-24.217-.219-23.5-17.5l2-4a2016.806 2016.806 0 0 0 44.5-58.5A1614.417 1614.417 0 0 0 83 200.5c-5.614-16.384.22-24.217 17.5-23.5l4 2a4659.393 4659.393 0 0 0 43 48.5 4025.688 4025.688 0 0 0 46.5-60c2.441-2.954 5.275-5.287 8.5-7Z" />
          <path d="M393.5 143.5c14.67-.167 29.337 0 44 .5 8.982 4.473 12.149 11.64 9.5 21.5-1.833 4.5-5 7.667-9.5 9.5a484.008 484.008 0 0 1-44 0c-6.987-2.807-10.32-7.973-10-15.5-.198-7.615 3.135-12.949 10-16Zm0 64c14.67-.167 29.337 0 44 .5 8.982 4.473 12.149 11.64 9.5 21.5-1.833 4.5-5 7.667-9.5 9.5a484.008 484.008 0 0 1-44 0c-6.987-2.807-10.32-7.973-10-15.5-.198-7.615 3.135-12.949 10-16Zm0 64c14.67-.167 29.337 0 44 .5 8.982 4.473 12.149 11.64 9.5 21.5-1.833 4.5-5 7.667-9.5 9.5a484.008 484.008 0 0 1-44 0c-6.987-2.807-10.32-7.973-10-15.5-.198-7.615 3.135-12.949 10-16Zm0 64c14.67-.167 29.337 0 44 .5 8.982 4.473 12.149 11.64 9.5 21.5-1.833 4.5-5 7.667-9.5 9.5a484.008 484.008 0 0 1-44 0c-6.987-2.807-10.32-7.973-10-15.5-.198-7.615 3.135-12.949 10-16Z" />
        </svg>
      );
    case "jpg":
    case "jpeg":
      return (
        // prettier-ignore
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" fillRule="evenodd" clipRule="evenodd" viewBox="0 0 512 512" className="w-12 h-12 text-yellow-500">
          <path d="M83.5-.5h253a146.342 146.342 0 0 1 10.5 10 7256.284 7256.284 0 0 1 94 109c.5 21.331.667 42.664.5 64 8.516-.615 16.849.218 25 2.5 8.144 4.143 13.311 10.643 15.5 19.5l.5 81.5a1815.01 1815.01 0 0 1-1.5 84.5c-3.167 8.5-9 14.333-17.5 17.5a90.896 90.896 0 0 1-22 1.5c.167 35.668 0 71.335-.5 107-2.29 7.117-6.79 12.117-13.5 15h-344c-6.71-2.883-11.21-7.883-13.5-15-.5-35.665-.667-71.332-.5-107-13.963 2.844-25.796-.823-35.5-11l-4-8a1816.117 1816.117 0 0 1-1.5-84.5l.5-81.5c3.167-11.167 10.333-18.333 21.5-21.5 6.325-.5 12.658-.666 19-.5-.167-56.001 0-112.001.5-168 2.29-7.117 6.79-12.117 13.5-15Zm7 21h231c-.474 35.715.026 71.382 1.5 107a17.842 17.842 0 0 0 5.5 4.5c30.665.5 61.332.667 92 .5v50h-330v-162Zm301 186c15.056.103 29.723 2.436 44 7a559.123 559.123 0 0 0-6 26 148.062 148.062 0 0 0-35-6c-37.453 2.625-53.953 22.625-49.5 60 5.559 26.048 21.725 39.048 48.5 39a66.582 66.582 0 0 0 16-2c.167-10.672 0-21.339-.5-32a141.755 141.755 0 0 0-23.5-1v-25h55c.328 26.241-.005 52.408-1 78.5-28.516 9.092-57.183 9.759-86 2-29.101-13.14-43.768-35.806-44-68 1.108-37.578 19.442-62.244 55-74 9.007-2.373 18.007-3.873 27-4.5Zm-181 1c11.671-.167 23.338 0 35 .5 39.921 5.336 54.754 27.502 44.5 66.5-7.618 15.073-19.785 24.24-36.5 27.5a109.978 109.978 0 0 1-34 1.5v54h-32v-147c7.809-.939 15.476-1.939 23-3Zm-85 1h32c.167 36.335 0 72.668-.5 109-3.063 24.062-16.563 37.896-40.5 41.5-11.506 1.129-22.839.296-34-2.5l-1-3.5c.96-7.516 1.96-15.016 3-22.5a77.392 77.392 0 0 0 23 1.5c6.729-.942 11.562-4.442 14.5-10.5a54.631 54.631 0 0 0 3-16c.5-32.332.667-64.665.5-97Zm-35 181h330v96h-330v-96Z" />
          <path d="M231.5 231.5c26.297.954 34.797 13.621 25.5 38-5.243 5.642-11.743 8.809-19.5 9.5a67.189 67.189 0 0 1-18-.5 507.128 507.128 0 0 1 1-45c3.803-.604 7.469-1.27 11-2Z" />
        </svg>
      );
    case "png":
      return (
        // prettier-ignore
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" fillRule="evenodd" clipRule="evenodd" viewBox="0 0 512 512" className="w-12 h-12 text-yellow-500">
          <path d="M83.5-.5h253a146.342 146.342 0 0 1 10.5 10 7256.284 7256.284 0 0 1 94 109c.5 21.331.667 42.664.5 64 8.516-.615 16.849.218 25 2.5 8.144 4.143 13.311 10.643 15.5 19.5l.5 81.5a1815.01 1815.01 0 0 1-1.5 84.5c-3.167 8.5-9 14.333-17.5 17.5a90.896 90.896 0 0 1-22 1.5c.167 35.668 0 71.335-.5 107-2.29 7.117-6.79 12.117-13.5 15h-344c-6.71-2.883-11.21-7.883-13.5-15-.5-35.665-.667-71.332-.5-107-13.963 2.844-25.796-.823-35.5-11l-4-8a1816.117 1816.117 0 0 1-1.5-84.5l.5-81.5c3.167-11.167 10.333-18.333 21.5-21.5 6.325-.5 12.658-.666 19-.5-.167-56.001 0-112.001.5-168 2.29-7.117 6.79-12.117 13.5-15Zm7 21h231c-.474 35.715.026 71.382 1.5 107a17.842 17.842 0 0 0 5.5 4.5c30.665.5 61.332.667 92 .5v50h-330v-162Zm313 191c14.128-.173 27.794 2.16 41 7-.993 8.327-2.826 16.66-5.5 25-15.013-5.987-30.513-7.487-46.5-4.5-19.255 4.68-30.088 16.847-32.5 36.5-3.021 19.287 2.813 35.121 17.5 47.5 13.606 7.49 27.94 9.323 43 5.5v-31h-22v-24h52c.333 24.676 0 49.342-1 74-27.564 9.547-55.231 10.047-83 1.5-26.268-12.332-39.601-33.166-40-62.5.92-37.4 19.253-61.234 55-71.5a196.037 196.037 0 0 1 22-3.5Zm-315 1a233.711 233.711 0 0 1 45 2.5c20.949 5.447 32.282 18.947 34 40.5-1.704 25.536-15.038 41.036-40 46.5a180.951 180.951 0 0 1-30 1.5v50h-31c-.333-46.005 0-92.005 1-138a495.605 495.605 0 0 1 21-3Zm100 1a343.225 343.225 0 0 1 37 1 1697.118 1697.118 0 0 0 27.5 48 600.999 600.999 0 0 1 25 51 2144.61 2144.61 0 0 1-1.5-100h28v140a285.561 285.561 0 0 1-33.5-1 1896.973 1896.973 0 0 1-54.5-103c-.104 34.636-.104 69.303 0 104h-28v-140Zm-98 176h330v96h-330v-96Z" />
          <path d="M101.5 235.5c9.478-1.018 18.478.482 27 4.5 8.817 8.641 10.317 18.474 4.5 29.5-4.789 6.481-11.289 9.981-19.5 10.5a55.328 55.328 0 0 1-16-.5v-43c1.599.268 2.932-.066 4-1Z" />
        </svg>
      );
    case "pdf":
      return (
        // prettier-ignore
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" fillRule="evenodd" clipRule="evenodd" viewBox="0 0 512 512" className="w-12 h-12 text-red-500" >
          <path d="M118.5-.5h217a23232.886 23232.886 0 0 1 128.5 128c.667 112 .667 224 0 336-4.339 25.835-19.172 41.835-44.5 48h-301c-22.914-5.727-37.414-20.06-43.5-43a266.363 266.363 0 0 1-1.5-35c-12.018.749-20.518-4.251-25.5-15a1426.759 1426.759 0 0 1-1.5-74.5 1426.54 1426.54 0 0 1 1.5-74.5c4.895-11.034 13.395-15.701 25.5-14a7795.06 7795.06 0 0 1 1.5-213c6.13-22.95 20.63-37.284 43.5-43Zm6 34h197c-.167 25.336 0 50.669.5 76 3.624 18.624 14.791 29.457 33.5 32.5 24.664.5 49.331.667 74 .5-.317 102.507.017 205.007 1 307.5.693 8.915-1.973 16.581-8 23-2.626 2.146-5.626 3.479-9 4-96.333.667-192.667.667-289 0-6.324-1.995-11.157-5.828-14.5-11.5a189.33 189.33 0 0 1-1.5-32c89.364.485 178.697-.015 268-1.5 7.258-2.924 12.092-8.091 14.5-15.5.667-48.333.667-96.667 0-145-2.5-7.833-7.667-13-15.5-15.5a35626.8 35626.8 0 0 0-267-.5c-.167-68.667 0-137.334.5-206 2.685-7.85 7.852-13.184 15.5-16Zm-11 267c10.339-.166 20.672 0 31 .5 21.552 4.932 29.386 18.098 23.5 39.5-4.398 9.786-11.898 15.62-22.5 17.5a93.752 93.752 0 0 1-21 1.5v33h-19v-91c2.885.306 5.552-.027 8-1Zm80 0c11.005-.167 22.005 0 33 .5 28.373 4.891 41.207 21.725 38.5 50.5-3.7 24.364-17.866 37.864-42.5 40.5a158.905 158.905 0 0 1-38-.5v-90c3.213.31 6.213-.023 9-1Zm87 0h55v16h-35v22h33v16h-33v38h-20v-92Z" />
          <path d="M130.5 314.5c17.387-.194 23.22 7.806 17.5 24-6.561 5.677-14.061 7.344-22.5 5a196.869 196.869 0 0 1-1-28c2.235.295 4.235-.039 6-1Z" />
          <path d="M204.5 315.5c6.342-.166 12.675 0 19 .5 14.291 4.207 21.291 13.874 21 29-1.398 25.214-14.731 36.047-40 32.5v-62Z" />
        </svg>
      );
    case "doc":
    case "docx":
      return (
        // prettier-ignore
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" fillRule="evenodd" clipRule="evenodd" viewBox="0 0 512 512" className="w-12 h-12 text-blue-500">
          <path d="M145.1 43.5C17.8 73 5.5 76 3.1 78.4L.5 81 .2 265.1C0 463.6-.3 453.5 4.9 456.7c2.7 1.7 281.9 44.6 285.7 43.8 6.4-1.1 6.8-2.7 7.4-23.4l.5-18.6 103.6-.3 103.6-.2 2.6-2.6c1.5-1.5 2.9-4 3.2-5.8.3-1.7.4-90.1.3-196.3L511.5 60l-2.8-2.7-2.7-2.8-103.8-.5-103.7-.5-.6-19c-.3-10.5-.6-19.1-.7-19.3-1.2-1.5-7.4-4.2-9.7-4.1-1.6.1-65.7 14.6-142.4 32.4zm345.7 212.8L491 438l-96.2-.2-96.3-.3v-43l82.6-.5c79.3-.5 82.8-.6 84.8-2.4 4-3.6 3.6-12.2-.7-15.8-1.2-1-18.9-1.4-84.1-1.8l-82.6-.5v-43l82.6-.5c79.3-.5 82.8-.6 84.8-2.4 4-3.6 3.6-12.2-.7-15.8-1.2-1-18.9-1.4-84.1-1.8l-82.6-.5v-43l82.6-.5c79.3-.5 82.8-.6 84.8-2.4 4-3.6 3.6-12.2-.7-15.8-1.2-1-18.9-1.4-84.1-1.8l-82.6-.5v-43l82.6-.5c79.3-.5 82.8-.6 84.8-2.4 4-3.6 3.6-12.2-.7-15.8-1.2-1-18.9-1.4-84.1-1.8l-82.6-.5v-43l82.6-.5c79.3-.5 82.8-.6 84.8-2.4 4-3.6 3.6-12.2-.7-15.8-1.2-1-18.9-1.4-84.1-1.8l-82.6-.5-.3-20.5c-.1-11.2 0-21 .2-21.7.4-1.1 19.2-1.3 96.3-1.1l95.8.3.3 181.8zM60.4 184.2c2.9 3 4.4 7.9 19.1 59.5 8.8 31 16.2 56.3 16.5 56.3.3 0 7.7-25.3 16.5-56.1 15.3-53.7 16.1-56.2 19.3-59 5.5-4.8 14.4-2.9 17 3.7.6 1.6 7.9 26.7 16.2 55.9 8.3 29.1 15.4 53.8 15.8 54.8.5 1.2 6-16.6 16.6-54 8.8-30.7 16.5-57 17.3-58.5 1.5-3 6-5.8 9.3-5.8 5.2 0 11 5.7 11 10.7 0 2.2-42.5 151-44.2 154.8-1.5 3.3-5.6 5.5-10.3 5.5-7.6 0-7.4.4-25.5-62.9l-16.4-57.3-16.2 56.8c-15.1 52.9-16.5 57.1-19.3 59.6-5 4.5-13.1 3.5-16.3-2-.9-1.5-11.3-36.8-23.2-78.6-20-69.8-21.6-76.1-20.5-79.3 1.3-4.1 6-7.3 10.8-7.3 2.4 0 4.1.9 6.5 3.2z" />
        </svg>
      );
    default:
      return (
        // prettier-ignore
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-12 h-12 text-gray-400">
          <path d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0016.5 9h-1.875a1.875 1.875 0 01-1.875-1.875V5.25A3.75 3.75 0 009 1.5H5.625z" />
          <path d="M12.971 1.816A5.23 5.23 0 0114.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 013.434 1.279 9.768 9.768 0 00-6.963-6.963z" />
        </svg>
      );
  }
}
