import React from "react";
import { Path, RegisterOptions, useFormContext } from "react-hook-form";
import { clsx, validationValue } from "~lib/utils";

interface Props<T extends string>
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "type" | "required"> {
  options?: RegisterOptions;
  id?: string;
  className?: string;
  /* --------Inject by FieldWrapper ------- */
  required?: boolean | string;
  name?: T;
}

/**
 * Date input using native `<input type="date" />`.
 * TODO: Format output value?
 * TODO: Validate min/max date with RHF.
 * TODO: Sync RHF default date.
 * WARN: Always add validation register options with messages.
 * See https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/date
 */
export default function Input<T extends string, V extends string | number>({
  name,
  options: registerOptions,
  required: isParentRequired,
  className = "",
  readOnly = false,
  ...rest
}: Props<T>) {
  type Inputs = { [key in T]: V };
  type Fields = Inputs;

  const { register } = useFormContext<Fields>();

  const isRequiredValue = validationValue(registerOptions?.required) || isParentRequired;

  const isRequired = Boolean(isRequiredValue);

  const isRequiredMessage =
    typeof isRequiredValue == "string" ? isRequiredValue : "This field is required";

  const requiredValidation = {
    required: {
      value: isRequired,
      message: isRequiredMessage,
    },
  };

  return (
    <input
      {...register(name as unknown as Path<Fields>, {
        ...(isRequired && typeof isRequired == "boolean" && requiredValidation), // Merge required property
        ...registerOptions,
        valueAsDate: true as const,
        valueAsNumber: false as const,
        pattern: undefined,
      })}
      {...rest}
      className={clsx(
        "border border-gray-300 rounded-md p-2 disabled:opacity-60 read-only:opacity-60",
        readOnly && "opacity-60",
        className
      )}
      type="date"
      readOnly={readOnly}
    />
  );
}
