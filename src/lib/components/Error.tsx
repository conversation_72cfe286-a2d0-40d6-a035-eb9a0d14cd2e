interface Props {
  /**
   * @default "Something went wrong"
   */
  title?: string;
  message?: string;
}

export default function ErrorMessage({ title = "Something went wrong", message }: Props) {
  return (
    <div className="p-8 flex justify-center items-center">
      <div className="flex flex-col items-center">
        {/* prettier-ignore */}
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-12 h-12 text-red-400">
          <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
        </svg>

        <h3 className="font-medium text-center text-red-400">{title}</h3>
        {message && <p className="text-sm text-center text-red-400">{message}</p>}
      </div>
    </div>
  );
}
