import { clsx } from "~lib/utils";

interface Section {
  title: string;
  description?: string;
}

interface Props {
  sections: Array<Section>;
  active: number;
  className?: string;
}

export default function HorizontalStepper({ sections, active, className }: Props) {
  return (
    <div className={clsx("px-2", className)}>
      <ol className="flex items-center w-full text-sm font-medium text-center text-gray-500">
        {sections.map((section, index) => (
          <li
            className={clsx(
              "flex flex-grow w-full items-center",
              index == active ? "text-blue-900" : "text-gray-400"
            )}
            key={index}
          >
            <span className="flex items-center">
              <span
                className={clsx(
                  "w-8 h-8 mr-2 text-white  font-semibold text-sm flex justify-center items-center rounded-full",
                  index === active ? "bg-blue-900" : "bg-gray-400"
                )}
              >
                {index < active ? (
                  <>
                    {/* prettier-ignore */}
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-4 h-4 text-white">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                  </svg>
                  </>
                ) : (
                  <span>{index + 1}</span>
                )}
              </span>
              <span>{section.title}</span>
            </span>

            {index != sections.length - 1 && (
              <hr className="mx-2 text-gray-200 border flex-grow"></hr>
            )}
          </li>
        ))}
      </ol>
    </div>
  );
}
