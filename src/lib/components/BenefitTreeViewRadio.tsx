import { Path, useFormContext } from "react-hook-form";
import { BeneficiaryProviderBenefit } from "~lib/api/types";
import { BeneficiaryProviderBenefitNode, BenefitNode } from "~lib/types";
import { clsx, formatMoney } from "~lib/utils";

interface Props<T> {
  beneficiaryBenefits: BeneficiaryProviderBenefitNode[] | BenefitNode[];
  className?: string;
  required?: boolean;
  name?: T; // injected by FieldWrapper
}

/**
 * TODO: Fix field name undefined
 */
export default function BenefitTreeViewRadio<T extends string>({
  name: inputName = "benefit" as T,
  beneficiaryBenefits,
  className = "",
  required,
}: Props<T>) {
  type V = string; // benefit id

  type Inputs = {
    [key in T]: V;
  };

  const name = inputName as unknown as Path<Inputs>;

  const { register, watch } = useFormContext<Inputs>();
  const value = watch(name);

  const registerOptions = {
    ...(required && { required: "This field is required" }),
  };

  const beneficiaryBenefitDisabled = (b: BeneficiaryProviderBenefit) =>
    b.restriction?.restricted || b.billable === false;

  const providerBeneficiaryBenefitTitle = (b: BeneficiaryProviderBenefit) => {
    switch (true) {
      case b.restriction?.restricted === true:
        return b.restriction?.message || "Benefit is retricted in facility";
      case b.billable === false:
        return "Benefit is not billable";
      default:
        return "Select benefit";
    }
  };

  return (
    <ul className={clsx("space-y-2 pl-2 text-sm", className)}>
      {beneficiaryBenefits.map((beneficiaryProviderBenefit) => (
        <li key={beneficiaryProviderBenefit.benefitId}>
          <details className="rounded pb-2 pr-2 [&>summary>.chevron]:open:rotate-90">
            <summary
              className={clsx(
                "flex items-center gap-2 rounded p-2 hover:bg-gray-100",
                value == beneficiaryProviderBenefit.benefitId.toString() &&
                  "border-blue-500 bg-blue-50",
              )}
            >
              <span
                className={clsx(
                  "chevron shrink-0 origin-center rotate-0 transform opacity-0 transition-all duration-300",
                  Boolean(beneficiaryProviderBenefit.children.length) && "opacity-100",
                )}
              >
                ▶
              </span>
              <label
                className="flex-grow font-medium"
                htmlFor={beneficiaryProviderBenefit.benefitId.toString()}
                title={
                  "restriction" in beneficiaryProviderBenefit
                    ? providerBeneficiaryBenefitTitle(beneficiaryProviderBenefit)
                    : "Select benefit"
                }
              >
                {beneficiaryProviderBenefit.benefitName}
              </label>
              <p>
                <span>Balance: </span>
                <span className="font-medium">
                  {formatMoney(beneficiaryProviderBenefit.balance)}
                </span>
              </p>

              <input
                type="radio"
                value={beneficiaryProviderBenefit.benefitId}
                {...register(name, registerOptions)}
                disabled={
                  "restriction" in beneficiaryProviderBenefit
                    ? beneficiaryBenefitDisabled(beneficiaryProviderBenefit)
                    : false
                }
                id={beneficiaryProviderBenefit.benefitId.toString()}
              />
            </summary>

            {Boolean(beneficiaryProviderBenefit.children.length) && (
              <BenefitTreeViewRadio beneficiaryBenefits={beneficiaryProviderBenefit.children} />
            )}
          </details>
        </li>
      ))}
    </ul>
  );
}
