import React from "react";
import { clsx } from "~lib/utils";
import { useFormContext, Path } from "react-hook-form";


interface Props<V, T> {
  className?: string;
  orientation?: "horizontal" | "vertical";
  solid?: boolean;
  options: Array<{ label: string; value: V }>;
  required?: boolean;
  isDisabled?: boolean;
  /**
   * Fields injected by `FieldWrapper`
   */
  name?: T;
}

// TODO: Test number values
// TODO: Test default values
export default function RadioGroup<V extends string | number, T extends string>({
  name: inputName,
  options,
  required,
  solid = false,
  orientation = "vertical",
  className = "",
  isDisabled,
}: Props<V, T>) {
  type Inputs = {
    [key in T]: V;
  };

  const name = inputName as unknown as Path<Inputs>;
  
  const { register, watch } = useFormContext<Inputs>();
  const value = watch(name);
  // const value = getValues(name);
  const registerOptions = {
    ...(required && { required }),
  };

  return (
    <div className={clsx(
      "flex",
      orientation === "horizontal" && "flex-row gap-4",
      orientation === "vertical" && "flex-col",
      className
    )}>
      {options.map((option) => (
        <label key={option.value} className={clsx(
          "flex items-center rounded-md  border border-transparent gap-1",
          orientation === "horizontal" ? "p-2" : "py-1 px-2",
          solid && (value === option.value) && "bg-blue-50 border-blue-500",
          isDisabled && "opacity-80 cursor-not-allowed"
        )}>
          <input
            type="radio"
            value={option.value}
            {...register(name as unknown as Path<Inputs>, registerOptions)}
            disabled={isDisabled}
          />
          {option.label}
        </label>
      ))}
    </div>
  );
}
