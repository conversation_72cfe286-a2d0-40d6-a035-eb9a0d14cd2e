export default function MinetLogo(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg viewBox="0 0 135.47 135.47" fill="currentColor" {...props}>
      <path d="M8.2 103.47a7.28 7.28 0 0 1-4.82-4.28c-.69-1.64-.6-67.62.09-69.14a7.5 7.5 0 0 1 3.38-3.56c2.02-1.03 120.92-1.03 122.95 0a7.48 7.48 0 0 1 3.37 3.56c.76 1.68.76 67.68 0 69.36a7.48 7.48 0 0 1-3.37 3.56c-1.28.65-1.28.65-60.95.7-34 .02-60.09-.07-60.65-.2zm119.84-4.73c1.43-.87 1.4-.11 1.4-34.01s.03-33.14-1.4-34.01c-1.33-.81-118.1-.81-119.43 0-1.43.87-1.4.11-1.4 34.01s-.03 33.14 1.4 34.01c1.33.81 118.1.81 119.43 0zm-56.67-15.8c-6.75-1.03-11.02-4.73-18.25-15.83-7.78-11.94-11.31-13.95-15.93-9.06-3.29 3.47-6.31 9.9-8.84 18.78-1.72 6.03-1.7 6.02-6.41 6.02-4.96 0-5.12-.24-3.63-5.48 6.13-21.6 15-32.4 25.3-30.76 6 .95 10.2 5 19.2 18.52C70 75.9 74.27 79.9 79.43 80.7c1.46.22 1.46.22.14.8-2.46 1.1-6.16 1.75-8.2 1.44zm39.15-.85c-1.54-.7-1.38-.43-8.34-13.92a498.69 498.69 0 0 0-7.27-13.76c-2.2-3.44-5.25-5.69-7.71-5.69-1.87 0-1.32-.44 1.3-1.06 10.29-2.43 13.82.61 24.76 21.35 6.24 11.81 6.61 12.78 5.15 13.34-1.22.46-6.71.28-7.89-.26zm-32.18-3.56a14.57 14.57 0 0 1-4.71-2.8c-.92-.76-.92-.76-.08-1.46 1.32-1.1 2.29-3.47 4.3-10.47 1.98-6.9 3.1-9.62 4.63-11.19 2.7-2.78 6.13-2.48 9.14.8 1.27 1.4 1.27 1.4-.24 2.92-2.06 2.09-2.63 3.43-5.08 11.88-2.9 10.05-3.96 11.42-7.96 10.32z"></path>
    </svg>
  );
}
