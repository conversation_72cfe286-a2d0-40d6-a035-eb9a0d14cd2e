import { BeneficiaryBenefit, BeneficiaryProviderBenefit } from "~lib/api/types";

export type AllowedFieldsWithType<Obj, Type> = {
  [K in keyof Obj]: Obj[K] extends Type ? K : never;
};

export type ExtractFieldsOfType<Obj, Type> = AllowedFieldsWithType<Obj, Type>[keyof Obj];

export type PickByType<T, Value> = {
  [P in keyof T as T[P] extends Value | undefined ? P : never]: T[P];
};

export type Entries<T> = {
  [K in keyof T]: [K, T[K]];
}[keyof T][];

export type StringLiteral<T> = T extends string ? (string extends T ? never : T) : never;

export type Option<T> = { label: string; value: T };
export type StringOption = Option<string>;
export type NumberOption = Option<number>;

export enum PreAuthMode {
  ADD = "ADD",
  EDIT = "EDIT",
  ADMIT = "ADMIT",
}

export enum Status {
  PENDING,
  SUCCESS,
  FAILURE,
  OTHER,
}

export type ArrayKeys<T> = {
  [K in keyof T]: T[K] extends unknown[] ? K : never;
}[keyof T];

export type PartialOrUndefined<T> = {
  [P in keyof T]?: T[P] | undefined;
};

export type Nullish<T> = T | null | undefined

export type BenefitNode = BeneficiaryBenefit & {
  children: BenefitNode[];
};

export type BeneficiaryProviderBenefitNode = BeneficiaryProviderBenefit & {
  children: BeneficiaryProviderBenefitNode[];
};
