import { useLazySearchProvidersByTypeQuery } from "~lib/api";
import { SearchProviderFilter } from "~lib/api/types";

const PROVIDERS_SIZE = 10; // Max items to fetch from the API
const PROVIDERS_PAGE = 1; // Page is always 1 for now
const DEBOUNCE_DELAY = 300;

/**
 * Hook that returns a debounced function to search providers and return provider options
 */
export default function useSearchMainProviderOptions() {
  const [getProviders] = useLazySearchProvidersByTypeQuery();

  const getProviderOptions = async (query: string) => {
    const response = await getProviders({
      ...(query && { query }),
      page: PROVIDERS_PAGE,
      size: PROVIDERS_SIZE,
      type: SearchProviderFilter.MAIN,
    }).unwrap();

    const providers = response?.data?.content || [];

    return providers.map((provider) => ({
      label: provider.name,
      value: provider.id.toString(),
    }));
  };

  type GetProviderOptionsReturnType = Awaited<ReturnType<typeof getProviderOptions>>;

  const getProviderOptionsDebouncedFactory = (delay: number) => {
    let timeoutId: ReturnType<typeof setTimeout>;

    return (query: string) =>
      new Promise<GetProviderOptionsReturnType>((resolve, reject) => {
        clearTimeout(timeoutId);

        timeoutId = setTimeout(async () => {
          try {
            const options = await getProviderOptions(query);
            resolve(options);
          } catch (error) {
            reject(error);
          }
        }, delay);
      });
  };

  return { getProviderOptions: getProviderOptionsDebouncedFactory(DEBOUNCE_DELAY) };
}
