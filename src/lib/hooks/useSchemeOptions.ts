import { useLazySearchProvidersByTypeQuery,useGetPayerPlansQuery, useLazyGetPayerPlansQuery } from "~lib/api";
import { SearchProviderFilter } from "~lib/api/types";

const PROVIDERS_SIZE = 10; // Max items to fetch from the API
const PROVIDERS_PAGE = 1; // Page is always 1 for now
const DEBOUNCE_DELAY = 300;

/**
 * Hook that returns a debounced function to search providers and return provider options
 */
export default function useSearchPlanOptions({payerId}) {
  const [getPayerPlans] = useLazyGetPayerPlansQuery();

  const getPlanOptions = async (payerId: number) => {
    const response = await getPayerPlans(
      { payerId: payerId }
    );

    const plans = response?.data || [];
    console.log(plans)

    return plans.map((provider) => ({
      label: provider.name,
      value: provider.id.toString(),
    }));
  };

  type GetProviderOptionsReturnType = Awaited<ReturnType<typeof getPlanOptions>>;

  const getProviderOptionsDebouncedFactory = (delay: number) => {
    let timeoutId: ReturnType<typeof setTimeout>;

    return (query: number) =>
      new Promise<GetProviderOptionsReturnType>((resolve, reject) => {
        clearTimeout(timeoutId);

        timeoutId = setTimeout(async () => {
          try {
            const options = await getPlanOptions(query);
            resolve(options);
          } catch (error) {
            reject(error);
          }
        }, delay);
      });
  };

  return { getPlanOptions: getProviderOptionsDebouncedFactory(DEBOUNCE_DELAY) };
}
