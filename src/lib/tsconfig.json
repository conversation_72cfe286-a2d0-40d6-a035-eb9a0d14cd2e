{
    "extends": "../../tsconfig.json",
    "compilerOptions": {
        "rootDir": ".",
        "outDir": "../../dist/lib",
        "paths": {
            "~lib/*": [
                "./*"
            ]
        },
        "declarationMap": true, // Allow editor jump to reference
        "noEmit": false, // .d.ts emits are required for referenced project—override parent config
        "composite": true // Allow project to be referenced
    },
    "include": [
        "."
    ]
}
