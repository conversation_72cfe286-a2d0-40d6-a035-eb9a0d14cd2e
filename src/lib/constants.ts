import {
  BeneficiaryStatus,
  ClaimStatus,
  DeviceAllocationStatus,
  DeviceStatus,
  InvoiceStatus,
  PayerStatus,
  PreAuth,
  PreAuthStatus,
  ServiceGroup,
  VisitStatus,
} from "./api/types";
import { Status } from "./types";

export const baseUrl = import.meta.env.VITE_API_URL;

export const toastOptions = {
  position: "top-right",
  autoClose: 5000,
  hideProgressBar: false,
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true,
  progress: undefined,
} as const;

export const dateFlatPickrOptions = {
  mode: "single",
  static: true,
  monthSelectorType: "static",
  dateFormat: "Y-m-d",
  altFormat: "d/m/Y",
  altInput: true,
  defaultDate: new Date().toISOString().split("T")[0],
  enableTime: false,
  maxDate: "today",
  prevArrow:
    '<svg class="fill-current" width="7" height="11" viewBox="0 0 7 11"><path d="M5.4 10.8l1.4-1.4-4-4 4-4L5.4 0 0 5.4z" /></svg>',
  nextArrow:
    '<svg class="fill-current" width="7" height="11" viewBox="0 0 7 11"><path d="M1.4 10.8L0 9.4l4-4-4-4L1.4 0l5.4 5.4z" /></svg>',
} as const;

export const beneficiaryStatusBadgeColors = new Map([
  [BeneficiaryStatus.ACTIVE, ["border-green-400 text-green-600", "bg-green-400"]],
  [BeneficiaryStatus.DEACTIVATED, ["border-red-400 text-red-600", "bg-red-400"]],
]);

export const getClaimStatusBadgeColors = (status: ClaimStatus) => {
  switch (status) {
    case ClaimStatus.ACTIVE:
    case ClaimStatus.PENDING:
    case ClaimStatus.TRANSMITTED:
    case ClaimStatus.LINE_ITEMS_ADDED:
    case ClaimStatus.DIAGNOSIS_ADDED:
    case ClaimStatus.CLOSED:
      return ["border-yellow-400 text-yellow-600", "bg-yellow-400"];
    case ClaimStatus.SETTLED:
    case ClaimStatus.BALANCE_ADDED:
      return ["border-green-400 text-green-600", "bg-green-400"];
    case ClaimStatus.REJECTED:
      return ["border-red-400 text-red-600", "bg-red-400"];
    default:
      return ["border-gray-400 text-gray-500", "bg-gray-400"];
  }
};

export const getInvoiceStatusBadgeColors = (status: InvoiceStatus) => {
  switch (status) {
    case InvoiceStatus.BALANCE_DEDUCTED:
    case InvoiceStatus.DIAGNOSIS_ADDED:
    case InvoiceStatus.DOCUMENTS_ADDED:
      return ["border-yellow-400 text-yellow-600", "bg-yellow-400"];
    case InvoiceStatus.SENT:
      return ["border-green-400 text-green-600", "bg-green-400"];
    case InvoiceStatus.REJECTED:
      return ["border-red-400 text-red-600", "bg-red-400"];
    default:
      return ["border-gray-400 text-gray-500", "bg-gray-400"];
  }
};

export const getDispatchStatusBadgeColors = (status: boolean | null) => {
  switch (status) {
    case true:
      return ["border-green-400 text-green-600", "bg-green-400"];
    case false:
      return ["border-red-400 text-red-600", "bg-red-400"];
    case null:
      return ["border-yellow-400 text-yellow-600", "bg-yellow-400"];
    default:
      return ["border-gray-400 text-gray-500", "bg-gray-400"];
  }
};

export const getPayerStatusBadgeColors = (status: PayerStatus | null) => {
  switch (status) {
    case PayerStatus.UNSENT:
      return ["border-yellow-400 text-yellow-600", "bg-yellow-400"];
    case PayerStatus.SENT:
      return ["border-green-400 text-green-600", "bg-green-400"];
    case PayerStatus.FAILED:
      return ["border-red-400 text-red-600", "bg-red-400"];
    default:
      return ["border-gray-400 text-gray-500", "bg-gray-400"];
  }
};

export const deviceAllocationStatusBadgeColors = new Map([
  [DeviceAllocationStatus.ALLOCATED, ["border-green-400 text-green-600", "bg-green-400"]],
  [DeviceAllocationStatus.DEALLOCATED, ["border-red-400 text-red-600", "bg-red-400"]],
]);

export const deviceStatusBadgeColors = new Map([
  [DeviceStatus.AVAILABLE, ["border-gray-400 text-gray-600", "bg-gray-400"]],
  [DeviceStatus.ALLOCATED, ["border-green-400 text-green-600", "bg-green-400"]],
  [DeviceStatus.REPAIR, ["border-red-400 text-red-600", "bg-red-400"]],
  [DeviceStatus.DECOMMISSIONED, ["border-red-400 text-red-600", "bg-red-400"]],
]);

export const dropdownTransitions = {
  enter: "transition ease-out duration-100",
  enterFrom: "transform opacity-0 scale-95",
  enterTo: "transform opacity-100 scale-100",
  leave: "transition ease-in duration-75",
  leaveFrom: "transform opacity-100 scale-100",
  leaveTo: "transform opacity-0 scale-95",
};

export const RADIOLOGY_OPTIONS = [
  {
    label: "X-Ray",
    value: "X-Ray",
  },
  {
    label: "MRI",
    value: "MRI",
  },
  {
    label: "CT Scan",
    value: "CT Scan",
  },
  {
    label: "Ultrasound",
    value: "Ultrasound",
  },
  {
    label: "Mammogram",
    value: "Mammogram",
  },
  {
    label: "PET Scan",
    value: "PET Scan",
  },
];

export const preAuthStatusColors = new Map<PreAuthStatus, [string, string]>([
  [PreAuthStatus.DRAFT, ["border-yellow-400 text-yellow-600", "bg-yellow-400"]],
  [PreAuthStatus.PENDING, ["border-yellow-400 text-yellow-600", "bg-yellow-400"]],
  [PreAuthStatus.INACTIVE, ["border-gray-300 text-gray-500", "bg-gray-400"]],
  [PreAuthStatus.EXPIRED, ["border-gray-300 text-gray-500", "bg-gray-400"]],
  [PreAuthStatus.AUTHORIZED, ["border-green-400 text-green-600", "bg-green-400"]],
  [PreAuthStatus.CLAIMED, ["border-green-400 text-green-600", "bg-green-400"]],
  [PreAuthStatus.ACTIVE, ["border-green-400 text-green-600", "bg-green-400"]],
  [PreAuthStatus.DECLINED, ["border-red-400 text-red-600", "bg-red-400"]],
  [PreAuthStatus.CANCELLED, ["border-red-400 text-red-600", "bg-red-400"]],
]);

export const EMPTY_LINE = "\u00A0";

export const headlessUiMenuTransitionAttrs = {
  enter: "transition ease-out duration-100",
  enterFrom: "transform opacity-0 scale-95",
  enterTo: "transform opacity-100 scale-100",
  leave: "transition ease-in duration-75",
  leaveFrom: "transform opacity-100 scale-100",
  leaveTo: "transform opacity-0 scale-95",
} as const;

export const PAGE_SIZES = [10, 20, 50, 100] as const;

/**
 * Useful for finding possibly inpatient service groups
 */
export const OUTPATIENT_SERVICE_GROUPS = [
  ServiceGroup.DENTAL,
  ServiceGroup.OPTICAL,
  ServiceGroup.OUTPATIENT,
];

/**
 * Global default max file size for uploads.
 */
export const MAX_FILE_SIZE = 30 * 1024 * 1024; // 30MB

export const PROVIDER_EXCLUDE_VISIT_STATUSES = [
  VisitStatus.DRAFT,
  VisitStatus.ACTIVE,
  VisitStatus.DIAGNOSIS_ADDED,
  VisitStatus.PENDING,
  VisitStatus.TRANSMITTED,
  VisitStatus.SETTLED,
];

export const PAYER_CLAIMS_EXCLUDE_VISIT_STATUSES = [
  /* --------pended visits------------ */
  VisitStatus.PENDING,
  /* --------incomplete from provider------------ */
  VisitStatus.DRAFT,
  VisitStatus.ACTIVE,
  VisitStatus.DIAGNOSIS_ADDED,
];

export const beneficiaryStatusMap: Record<BeneficiaryStatus, Status> = {
  [BeneficiaryStatus.ACTIVE]: Status.SUCCESS,
  [BeneficiaryStatus.SUSPENDED]: Status.PENDING,
  [BeneficiaryStatus.DEACTIVATED]: Status.FAILURE,
};

export const REIMBURSEMENT_PROVIDER_ID = 646;

export const MAX_INVOICES_PER_VISIT = 30; // Arbitrary max size for now
export const MAX_INVOICE_LINES_PER_INVOICE = 30; // Arbitrary max size for now

export const preauthStatusValue = (preauth: PreAuth): Status => {
  if (preauth.markAsIncomplete) {
    return Status.FAILURE;
  }

  switch (preauth.status) {
    case PreAuthStatus.DRAFT:
    case PreAuthStatus.PENDING:
      return Status.PENDING;
    case PreAuthStatus.AUTHORIZED:
    case PreAuthStatus.CLAIMED:
    case PreAuthStatus.ACTIVE:
      return Status.SUCCESS;
    case PreAuthStatus.DECLINED:
    case PreAuthStatus.CANCELLED:
      return Status.FAILURE;
    default:
      return Status.OTHER;
  }
};

export const PROVIDER_PREAUTH_UPDATE_REASON = "PROVIDER-PREAUTH-UPDATE";

export enum sortOrder {
  ASC = "asc",
  DESC = "desc",
}
