import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";

export type Attributes = {
  providerId?: number;
  payerId?: number;
  providerName?: number;
  scheme?: string;
  schemeId?: number;
  inpatient?: string;
};

export type AuthState = Attributes & {
  isLoading: boolean;
  error: string;
  isAuthenticated: boolean;
  token: string;
  userId: string;
  username: string;
  roles: Array<string>;
};

const initialState: AuthState = {
  error: "",
  isLoading: false,
  isAuthenticated: false,
  token: "",
  username: "",
  userId: "",
  roles: [],
};

export const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setAuth: (state, { payload }: PayloadAction<Omit<AuthState, "isLoading" | "error">>) => {
      return { ...state, ...payload };
    },
    setAttibutes: (state, { payload: attributes }: PayloadAction<Attributes>) => {
      return { ...state, ...attributes };
    },
    setAuthenticated: (state, { payload: isAuthenticated }: PayloadAction<boolean>) => {
      state.isAuthenticated = isAuthenticated;
    },
    setLoading: (state, { payload: isLoading }: PayloadAction<boolean>) => {
      state.isLoading = isLoading;
    },
    setError: (state, { payload: error }: PayloadAction<string>) => {
      state.error = error || "";
    },
  },
});

export const { setAuth, setAttibutes, setLoading, setError, setAuthenticated } = authSlice.actions;

export default authSlice.reducer;
