import { SerializedError } from "@reduxjs/toolkit";
import { FetchBaseQueryError } from "@reduxjs/toolkit/query/react";
import currency, { Any } from "currency.js";
import { ValidationRule } from "react-hook-form";
import {
  BeneficiaryBenefit,
  BeneficiaryProviderBenefit,
  ErrorResponse,
  PayerStatus,
  Provider,
  ServiceGroup,
  SimpleResponse,
  VisitStatus,
} from "~lib/api/types";
import { OUTPATIENT_SERVICE_GROUPS } from "~lib/constants";
import { Line } from "~lib/service-fields";
import { BeneficiaryProviderBenefitNode, BenefitNode, Nullish, Option, Status } from "~lib/types";

/**
 * The name of the line items field in preauth form
 * WARNING: This must be kept in sync with the name of the field in service-fields
 */
const LINES = "lines";

/**
 * Concatenates two strings with a space in between, removing non-string values.
 */
export function clsx(...args: Array<unknown>): string {
  return args
    .filter(Boolean)
    .filter((x) => typeof x == "string")
    .join(" ");
}

/**
 * Makes the first letter of a string uppercase
 */
export function capitalize(value: string): string {
  return value ? value.charAt(0).toUpperCase() + value.slice(1) : "";
}

/**
 * Convert camelCase to Regular Case
 */
export function unCamelCase(str: string) {
  return (
    str
      // Insert a space between lower & upper
      .replace(/([a-z])([A-Z])/g, "$1 $2")
      // Space before last upper in a sequence followed by lower
      .replace(/\b([A-Z]+)([A-Z])([a-z])/, "$1 $2$3")
      // Uppercase the first character
      .replace(/^./, function (str) {
        return str.toUpperCase();
      })
  );
}

/**
 * Formats a number as money, e.g. 1000 -> KES 1,000.00
 */
export const formatMoney = (value: number | undefined) => {
  const parsedValue = Number.isNaN(Number(value)) ? 0 : Number(value);

  return Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "KES",
    maximumSignificantDigits: 10,
  }).format(parsedValue);
};

/**
 * Formats a date string as dd/MM/yy e.g. 2021-01-01 -> 01/01/2021
 * TODO: Handle invalid dates.
 */
export const formatDateStringGB = (value: string | number) => {
  const date = new Date(value);
  return !value || window.isNaN(date.getTime())
    ? ""
    : Intl.DateTimeFormat("en-GB").format(new Date(value));
};

/**
 * Format date as dd/MM/yyy
 */
export const formatDateGB = (value?: Date) => {
  return !value || window.isNaN(value.getTime()) ? "" : Intl.DateTimeFormat("en-GB").format(value);
};

/**
 * Format date as yyyy-MM-dd
 */
export const formatDateISO = (date?: Date) => {
  if (!date || window.isNaN(date.getTime())) {
    return "";
  }

  const offset = date.getTimezoneOffset();
  const offsetDate = new Date(date.getTime() - offset * 60 * 1000);

  return offsetDate.toISOString().split("T")[0] ?? "";
};

export const isoDateStringToLocal = (value: string | number) => {
  const date = new Date(value);
  return !value || window.isNaN(date.getTime()) ? "" : date.toLocaleDateString();
};

/**
 * Returns the intersection of two arrays
 */
export function intersection<T>(a: T[], b: T[]) {
  return a.filter((value) => b.includes(value));
}

/**
 * Returns the difference of two arrays, a-b
 */
export function difference<T>(a: T[], b: T[]) {
  return a.filter((value) => !b.includes(value));
}

/**
 * Returns the union of two arrays
 */
export function union<T>(a: T[], b: T[]) {
  return a.concat(difference(b, a));
}

/**
 * Returns the symmetric difference of two arrays
 */
export function symmetricDifference<T>(a: T[], b: T[]) {
  return difference(union(a, b), intersection(a, b));
}

/**
 * Returns true is b is a subset of a
 */
export function isSubset<T>(a: T[], b: T[]) {
  return difference(b, a).length === 0;
}

/**
 * Returns true if a is a strict subset of b
 */
export function isStrictSubset<T>(a: T[], b: T[]) {
  return isSubset(a, b) && a.length < b.length;
}

/**
 * Removes a value from an array
 */
export function exclude<T>(array: T[], value: T) {
  return array.filter((item) => item !== value);
}

/**
 * Adds a value to an array if it doesn't exist
 */
export function include<T>(array: T[], value: T) {
  return array.includes(value) ? array.slice() : array.concat(value);
}

export function pluralize(count: number, noun: string, suffix = "s") {
  return `${noun}${count !== 1 ? suffix : ""}`;
}

export function formatCalculation({ years, months }: { years: number; months: number }): string {
  const yearLabel = years === 1 ? "year" : "years";
  const monthLabel = months === 1 ? "month" : "months";

  return `${years} ${yearLabel} and ${months} ${monthLabel}`;
}

/**
 * Picks a random item from an array
 */
export function pickRandom<T>(array: T[]) {
  return array[Math.floor(Math.random() * array.length)];
}

export interface PaginationResult<T> {
  content: T[];
  empty: boolean;
  first: boolean;
  last: boolean;
  number: number;
  numberOfElements: number;
  size: number;
  totalElements: number;
  totalPages: number;
}

export function paginate<T>(data: T[], page: number, size: number): PaginationResult<T> {
  const start = (page - 1) * size;
  const end = start + size;
  const content = data.slice(start, end);

  return {
    content,
    empty: data.length === 0,
    first: page === 0,
    last: end >= data.length,
    number: page,
    numberOfElements: content.length,
    size,
    totalElements: data.length,
    totalPages: Math.ceil(data.length / size),
  };
}

/**
 * Mutates a URLSearchParams object by appending an array of values with a shared key
 */
export function urlSearchParamsAppend<T extends string | number>(
  queryParams: URLSearchParams,
  key: string,
  value?: T[],
) {
  if (value) {
    value.forEach((v) => queryParams.append(key, String(v)));
  }
}

/**
 * Helper function to create DOM element, and recursively create children
 * TODO: Add support for style attribute
 */
export function h(
  tagName: string,
  attributes: Record<string, string | number | boolean | null>,
  ...children: Array<string | HTMLElement>
) {
  const elem = document.createElement(tagName);

  Object.entries(attributes).forEach(([key, value]) => {
    if (value) {
      elem.setAttribute(key, String(value));
    }
  });

  children.forEach((child) => {
    if (typeof child === "string") {
      elem.appendChild(document.createTextNode(child));
    } else {
      elem.appendChild(child);
    }
  });

  return elem;
}

export function downloadFile(blob: Blob, filename: string) {
  const url = window.URL.createObjectURL(blob);

  const linkElem = h("a", {
    href: url,
    download: filename,
    target: "_blank",
  });

  linkElem.style.display = "none";

  document.body.appendChild(linkElem);
  linkElem.click();

  setTimeout(() => {
    document.body.removeChild(linkElem);
    window.URL.revokeObjectURL(url);
  }, 100);
}

/**
 * Join an array of values into a string
 * @param array The array to join
 * @param getLabel A function that returns the label of a value
 * @param separator The separator to use, defaults to comma
 * @returns A string of the array values joined by a comma
 */
export const arrayJoin = <T>(
  array: T[],
  getLabel: (value: T) => unknown = (v) => v,
  separator = ", ",
) => array.map(getLabel).join(separator);

/**
 * Formats a date into a string that can be used as a filename
 * @param {Date} date
 * @returns {string}
 *
 * @example
 * dateFileName(new Date()) // 2023-08-04-12-34-04-898
 */
export const dateFileName = (date?: Date): string => {
  if (!date || window.isNaN(date.getTime())) {
    return "";
  }

  const offset = date.getTimezoneOffset();
  const offsetDate = new Date(date.getTime() - offset * 60 * 1000);
  const isoString = offsetDate.toISOString();

  return isoString.replace(/[T:.]/g, "-").replace("Z", "");
};

export const arrayOrUndefined = <T>(value: T[]): T[] | undefined =>
  value?.length ? value : undefined;

/**
 * Converts an array of strings to an array of numbers,
 * or undefined if the array is empty.
 */
export const numberArrayOrUndefined = (value: string[]): number[] | undefined =>
  value?.length ? value.map(Number) : undefined;

/**
 * Converts an array of options to an array of numbers,
 * or undefined if the array is empty.
 */
export const optionNumberArrayOrUndefined = (value: Option<string>[]): number[] | undefined =>
  value?.length ? value.map((option) => option.value).map(Number) : undefined;

export const formValueTruthy = (value: unknown): unknown =>
  Array.isArray(value) ? value.length > 0 : Boolean(value);

/**
 * Formats a date into a string representation
 * @param date Date to format
 */
export const formatDateTime = (date: Date) =>
  date.toLocaleDateString() +
  " " +
  date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });

/**
 * Trims a substring from the start or end of a string
 * @param {string} value The string to trim
 * @param {string} substring The substring to trim
 */
export const trimSubstring = (value: string, substring: string): string => {
  if (value.startsWith(substring)) {
    return value.slice(substring.length);
  }

  if (value.endsWith(substring)) {
    return value.slice(0, -substring.length);
  }

  return value;
};

/**
 * A variadic function to sum up numbers,
 * using 0 as the default value if the value is undefined
 */
export const sum = (...values: (number | undefined)[]) =>
  values.reduce((acc, value) => (acc ?? 0) + (value || 0), 0);

/**
 * A variadic function to multiply numbers,
 * using 0 as the default value if the value is undefined
 */
export const product = (...values: (number | undefined)[]) =>
  values.reduce((acc, value) => (acc ?? 0) * (value || 0), 1);

const reduceLineItems = <O>(
  array: Line[],
  reducer: (acc: O, v: Line) => O,
  identityValue: O = 0 as O,
) => array?.reduce(reducer, identityValue);

/**
 * toFixed only if necessary
 * @param value The number
 * @param dp The number of decimal places
 * @returns The number with the specified number of decimal places
 */
const toFixed = (value: string, dp = 2) => +parseFloat(value).toFixed(dp);

/**
 * Creates a function that evaluate an expression with a given state
 * @example
 * const state = { foo: "bar" };
 * const expression = "foo === 'bar'";
 * const result = createExpressionEvaluator(state)(expression); // true
 * @param defaultState The default state to use
 * @param functions A record of functions to use in the expression
 * @returns A function that evaluates an expression with a given state
 */
export const createExpressionEvaluator =
  <T extends { [LINES]: [] }>(
    defaultState: T = { [LINES]: [] } as T,
    // eslint-disable-next-line @typescript-eslint/ban-types
    functions: Record<string, Function> = {
      formatDate: formatDateGB,
      formatMoney,
      sum,
      product,
      reduceLineItems,
      toFixed,
    },
  ) =>
  (expression: string, state: T = defaultState) => {
    functions["reduceLineItems"] = reduceLineItems.bind(null, state[LINES]);

    const fn = new Function(
      ...Object.keys(functions),
      ...Object.keys(state),
      `return ${expression}`,
    );

    try {
      return fn(...Object.values(functions), ...Object.values(state));
    } catch (error) {
      if (import.meta.env.DEV && !(error instanceof ReferenceError)) {
        console.error(error);
      }
      return "";
    }
  };

/**
 * Removes circular references from an object
 */
export function decycle(obj: unknown, stack: unknown[] = []): unknown {
  if (!obj || typeof obj !== "object") return obj;

  if (stack.includes(obj)) return null;

  const s = stack.concat([obj]);

  return Array.isArray(obj)
    ? obj.map((x) => decycle(x, s))
    : Object.fromEntries(Object.entries(obj).map(([k, v]) => [k, decycle(v, s)]));
}

/**
 * Truncates a string
 */
export function truncate(value: string | undefined, length: number) {
  return (value?.length ?? 0) > length ? `${value?.slice(0, length)}...` : value;
}

/**
 * Converts bytes to a human readable string
 */
export function formatBytes(bytes: number) {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const dm = 2;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
}

/**
 * Type predicate to narrow an unknown error to `FetchBaseQueryError`
 */
export function isFetchBaseQueryError(error: unknown): error is FetchBaseQueryError {
  return typeof error === "object" && error != null && "status" in error;
}

/**
 * Type predicate to narrow an unknown error to an object with a string 'message' property
 */
export function isErrorWithMessage(error: unknown): error is { message: string } {
  return (
    typeof error === "object" &&
    error != null &&
    "message" in error &&
    typeof (error as { message: string }).message === "string"
  );
}

export type RTKResult<T> =
  | {
      data: SimpleResponse<T | null>;
    }
  | {
      error: FetchBaseQueryError | SerializedError;
    };

/**
 * Extracts the error message, if any, from an rtk lazy query/mutation result
 * T - Success response $.data type
 * E - Error response
 */
export function responseError<T = unknown, E = ErrorResponse>(
  result: RTKResult<T>,
  errorField = "error" as keyof E,
): undefined | string {
  if ("data" in result && result.data?.success) {
    /**
     * HTTP Success, and API Success
     */
    return;
  } else if ("data" in result) {
    /**
     * HTTP Success, but API Error
     */
    return result.data?.msg;
  } else if ("error" in result) {
    if ("data" in result.error) {
      if (result.error.data == null) {
        return "Something went wrong. Empty response body";
      }

      /**
       * API Error
       */
      return (result.error.data as E)[errorField]?.toString();
    } else if ("message" in result.error) {
      /**
       * rtk-query error
       */
      return result.error.message;
    }
  }
}

export const queryError = (error: FetchBaseQueryError | SerializedError | undefined) => {
  if (error == undefined) {
    return undefined;
  }

  if ("error" in error) {
    return error.error;
  } else if ("data" in error) {
    return (error.data as ErrorResponse).error;
  } else if ("message" in error) {
    return error.message;
  } else {
    return undefined;
  }
};

/**
 * Returns true if the value is an object
 */
export const isObject = (value: unknown) =>
  typeof value === "object" && value !== null && !Array.isArray(value);

export const padArray = (array: Array<unknown>, length: number, filler: unknown) =>
  array.concat(Array(length).fill(filler)).slice(0, length);

export type OTP = [string, string, string, string];

/**
 * Splits a member number into discrete parts
 * @example
 * splitMemberNumber("JDC123456-00") // ["JDC", "123456", "-00"]
 */
export const splitMemberNumber = (memberNumber: string) =>
  memberNumber.match(/([a-z]+)?(\d+)(-\d+)?/i)?.slice(1);

export const branchName = (providerName: string) => {
  return providerName.split(" - ").at(-1);
};

export const truncateDecimals = (value: number, digits = 2) => {
  const multiplier = Math.pow(10, Math.abs(digits));
  const adjustedNum = value * multiplier;
  const truncatedNum = adjustedNum < 0 ? Math.ceil(adjustedNum) : Math.floor(adjustedNum);

  return truncatedNum / multiplier;
};

export function getVisitStatusBadge(status: VisitStatus) {
  switch (status) {
    case VisitStatus.CLOSED:
    case VisitStatus.DRAFT:
    case VisitStatus.ACTIVE:
    case VisitStatus.PENDING:
    case VisitStatus.DIAGNOSIS_ADDED:
      return Status.PENDING;
    case VisitStatus.LINE_ITEMS_ADDED:
    case VisitStatus.TRANSMITTED:
    case VisitStatus.SETTLED:
      return Status.SUCCESS;
    case VisitStatus.CANCELLED:
    case VisitStatus.REJECTED:
      return Status.FAILURE;
    case VisitStatus.INACTIVE:
      return Status.OTHER;
    default:
      return Status.OTHER;
  }
}

export const payerStatusBadge = (status: Nullish<PayerStatus>) => {
  switch (status) {
    case PayerStatus.FAILED:
      return Status.FAILURE;
    case PayerStatus.SENT:
      return Status.SUCCESS;
    default:
      return Status.OTHER;
  }
};

/**
 * Returns true if a and b are within `delta` of each other
 */
export const almostEqual = (a: number, b: number, delta = 0.01) => Math.abs(a - b) < delta;

export const formatKes = (a: currency) =>
  a.format({
    symbol: "KES",
    precision: 2,
  });

export const KES = (v: Any) =>
  currency(v, {
    symbol: "KES",
  });

export const isIntegratedProvider = (provider: Provider) =>
  Boolean(provider.providerMiddleware) && provider.providerMiddleware !== "NONE";

/**
 * Converts a list of benefits into a tree
 */
export function treeify(benefits: BeneficiaryBenefit[]) {
  /**
   * Map a benefit id to the BenefitNode
   */
  const dictionary = new Map<number, BenefitNode>(
    benefits.map((benefit) => [benefit.id, { ...benefit, children: [] }]),
  );

  /**
   * Result list of BenefitNodes
   */
  const dataTree: BenefitNode[] = [];

  benefits.forEach((benefit) => {
    if (benefit.parent) {
      dictionary.get(benefit.parent.id)!.children.push(dictionary.get(benefit.id)!);
    } else {
      dataTree.push(dictionary.get(benefit.id)!);
    }
  });

  return dataTree;
}

/**
 * Converts a list of benefits into a tree
 */
export function treeifyBeneficiaryProviderBenefit(benefits: BeneficiaryProviderBenefit[]) {
  /**
   * Map a benefit id to the BenefitNode
   */
  const dictionary = new Map<number, BeneficiaryProviderBenefitNode>(
    benefits.map((benefit) => [benefit.id, { ...benefit, children: [] }]),
  );

  /**
   * Result list of BenefitNodes
   */
  const dataTree: BeneficiaryProviderBenefitNode[] = [];

  benefits.forEach((benefit) => {
    if (benefit.parent) {
      dictionary.get(benefit.parent.id)!.children.push(dictionary.get(benefit.id)!);
    } else {
      dataTree.push(dictionary.get(benefit.id)!);
    }
  });

  return dataTree;
}

export const isInpatientServiceGroup = (serviceGroup: ServiceGroup) =>
  !OUTPATIENT_SERVICE_GROUPS.includes(serviceGroup);

export const validationValue = <T extends string | number | boolean = string | number | boolean>(
  validation: ValidationRule<T> | undefined,
) =>
  validation && typeof validation == "object" && "value" in validation
    ? validation.value
    : validation;

export const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

/**
 * Replaces substring of range with a replacement string
 */
export const replaceRange = (
  value: string,
  start: number,
  end: number,
  replacement: string,
): string => {
  return value.substring(0, start) + replacement + value.substring(end);
};

/**
 * Masks a phone number
 */
export const maskPhoneNumber = (phoneNumber: string) => replaceRange(phoneNumber, 3, 7, "****");

export const isFalsyOrEmpty = (value: unknown) => {
  if (!value) {
    return true;
  }

  if (Array.isArray(value) && value.length === 0) {
    return true;
  }

  if (typeof value === "object" && Object.keys(value).length === 0) {
    return true;
  }

  return false;
};

/**
 * Given a Cloud Storage URL, extract the file name
 */
export const documentFileName = (url: string) =>
  decodeURIComponent(url).split("/").pop()?.slice(27) ?? "";
