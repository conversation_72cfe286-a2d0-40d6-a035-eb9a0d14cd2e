import { formatDateGB, pluralize } from ".";

export type DateRange = [Date, Date];
export type OptionalDateRange = [Date | undefined, Date | undefined];

const ZERO_HOURS = [0, 0, 0, 0] as const;

export function getToday(): DateRange {
  const today = new Date();
  return [today, today];
}

export function getLastXDays(x: number): DateRange {
  const today = new Date();
  const startDate = new Date(today);
  startDate.setDate(today.getDate() - x);
  return [startDate, today];
}

export function getLastXMonths(x: number): DateRange {
  const today = new Date();
  const startDate = new Date(today);
  startDate.setMonth(today.getMonth() - x);
  return [startDate, today];
}

export function getMonthToDate(): DateRange {
  const today = new Date();
  const startDate = new Date(today.getFullYear(), today.getMonth(), 1);
  return [startDate, today];
}

export function getQuarterToDate(): DateRange {
  const today = new Date();
  const quarterStartMonth = Math.floor(today.getMonth() / 3) * 3;
  const startDate = new Date(today.getFullYear(), quarterStartMonth, 1);
  return [startDate, today];
}

export function getYearToDate(): DateRange {
  const today = new Date();
  const startDate = new Date(today.getFullYear(), 0, 1);
  return [startDate, today];
}

export function dateDayEquals(date1?: Date, date2?: Date): boolean {
  if (!date1 || !date2) {
    return false;
  }

  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
}

export function getNextDay(date: Date): Date {
  const nextDay = new Date(date.getTime());
  nextDay.setDate(date.getDate() + 1);
  return nextDay;
}

export function getPreviousDay(date: Date): Date {
  const previousDay = new Date(date.getTime());
  previousDay.setDate(date.getDate() - 1);
  return previousDay;
}

export function friendlyDateRange([startDate, endDate]: [
  Date | undefined,
  Date | undefined,
]): string {
  if (!startDate && !endDate) {
    return "";
  }

  if (dateDayEquals(startDate, endDate)) {
    const todayMidnightTimestamp = new Date().setHours(...ZERO_HOURS);
    const startDateMidnightTimestamp = new Date(startDate?.getTime() ?? 0).setHours(...ZERO_HOURS);

    if (todayMidnightTimestamp === startDateMidnightTimestamp) {
      return "Today";
    } else {
      return formatDateGB(startDate);
    }
  }

  return `${formatDateGB(startDate)} - ${formatDateGB(endDate)}`;
}

/**
 * Calculates the age of a person given their date of birth as a Date object
 */
export function calculateAge(dateOfBirth: Date): number {
  const today = new Date();
  const birthDate = new Date(dateOfBirth);

  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDifference = today.getMonth() - birthDate.getMonth();

  if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }

  return age;
}

export function calculateAgeYearsMonth(dob: Date) {
  const dobDate = new Date(dob);
  const today = new Date();

  let years = today.getFullYear() - dobDate.getFullYear();
  let months = today.getMonth() - dobDate.getMonth();

  if (months < 0) {
    years--;
    months += 12;
  }

  if (today.getDate() < dobDate.getDate()) {
    months--;
    if (months < 0) {
      months = 11;
      years--;
    }
  }

  return { years, months };
}

export const MS_IN_SEC = 1000;
export const MS_IN_MIN = MS_IN_SEC * 60;
export const MS_IN_HR = MS_IN_MIN * 60;

/**
 * Returns the hours and minutes left to a target date
 * TODO: Countdown backwards
 */
export function countdown(target: Date): [number, number, number] {
  const delta = target.getTime() - Date.now();

  if (delta <= 0) {
    return [0, 0, 0];
  }

  const hours = Math.floor(delta / MS_IN_HR);
  const minutes = Math.floor((delta % MS_IN_HR) / MS_IN_MIN);
  const seconds = Math.floor((delta % MS_IN_MIN) / MS_IN_SEC);

  return [hours, minutes, seconds];
}

/**
 * @example
 * ```
 * 23 hours
 * 1 hour
 * 12 minutes
 * a few seconds
 * ```
 * TODO: Humanize negative countdown
 */
export function humanizeCountdown([hours, minutes]: [number, number, number]) {
  if (hours >= 24) {
    /**
     * TODO: Add special case for visits, which should expire after 24 hours
     */
    return `${hours} hours`;
  } else if (hours >= 1) {
    return `${hours} ${pluralize(hours, "hour")}`;
  } else if (minutes >= 1) {
    return `${minutes} ${pluralize(minutes, "minute")}`;
  } else if (minutes > 0) {
    return `a few seconds`;
  } else {
    return ``;
  }
}

export const addHours = (date: Date, delta: number) => {
  const sumDate = new Date(date.getTime());
  sumDate.setHours(sumDate.getHours() + delta);
  return sumDate;
};
