import { ToolkitStore } from "@reduxjs/toolkit/dist/configureStore";
import _Keycloak from "keycloak-js";
import {
  Attributes,
  setAttibutes,
  setAuth,
  setAuthenticated,
  setError,
  setLoading,
} from "~lib/store/authSlice";

const DEFAULT_ERROR_MESSAGE = "Something went wrong";
const LCT_REALM = "LCT";

interface KeycloakConfig extends Omit<_Keycloak.KeycloakConfig, "realm" | "url"> {
  realm?: string;
  url?: string;
}

interface ConstructorArgs<S extends ToolkitStore> extends KeycloakConfig {
  store: S;
  requiredAttributes?: Array<keyof Attributes>;
  onTokenUpdated?: () => void;
}

const errorMessage = (error: unknown) => {
  if (error instanceof Error) {
    return error.message;
  } else if (
    error &&
    typeof error == "object" &&
    "error" in error &&
    typeof error["error"] == "string"
  ) {
    return error.error;
  }
  return DEFAULT_ERROR_MESSAGE;
};

/**
 * Wraps Keycloak to provide extra functionality
 */
export default class Auth<S extends ToolkitStore> {
  readonly keycloak: _Keycloak.KeycloakInstance;
  readonly requiredAttributes: Array<keyof Attributes>;
  readonly onTokenUpdated?: () => void;
  readonly store: S;

  /**
   * @param config Kecloak configuration
   * @param requiredAttributes Attributes that must be in the parsed token
   * @param onTokenUpdated Callback function triggered when the token is updated
   */
  constructor({
    requiredAttributes = [],
    store,
    onTokenUpdated,
    ...keycloakConfig
  }: ConstructorArgs<S>) {
    this.keycloak = _Keycloak({
      realm: LCT_REALM,
      url: import.meta.env.VITE_KEYCLOAK_URL,
      ...keycloakConfig,
    });
    this.requiredAttributes = requiredAttributes;

    if (onTokenUpdated) {
      this.onTokenUpdated = onTokenUpdated;
    }

    this.store = store;

    const dispatch = store.dispatch;

    /**
     * WARNING: keycloak.onTokenExpired() must be set before keycloak.init()
     */
    this.keycloak.onTokenExpired = async () => {
      try {
        dispatch(setLoading(true));
        await this.keycloak.updateToken(30);
        this.tokenUpdated();
      } catch (error) {
        console.error("Refreshing token", error);

        dispatch(setError(errorMessage(error)));

        // Immediately show the welcome screen
        dispatch(setAuthenticated(false));
      } finally {
        dispatch(setLoading(false));
      }
    };

    return this;
  }

  async init() {
    const keycloak = this.keycloak;

    try {
      this.store.dispatch(setLoading(true));
      await keycloak.init({
        /**
         * Display login page if user is not authenticated
         */
        onLoad: "login-required",
        silentCheckSsoRedirectUri: window.location.origin + "/silent-check-sso.html",
        pkceMethod: "S256",
      });

      this.tokenUpdated();
    } catch (error) {
      console.error("Initializing keycloak", error);
      this.store.dispatch(setError(errorMessage(error)));
    } finally {
      this.store.dispatch(setLoading(false));
    }
  }

  /**
   * @throws {Error} Error thrown when required attributes are missing
   */
  tokenUpdated() {
    const {
      authenticated: isAuthenticated,
      token,
      subject: userId,
      idTokenParsed,
      realmAccess: { roles },
    } = this.keycloak as Required<_Keycloak.KeycloakInstance>;

    // TODO: Verify correct token to use
    const {
      providerId,
      payerId,
      providerName,
      scheme,
      schemeId,
      inpatient,
      preferred_username: username,
    } = idTokenParsed;

    const authState = {
      isAuthenticated,
      token,
      userId,
      username,
      roles,
    };

    const attributes: Attributes = {
      providerId,
      payerId,
      providerName,
      scheme,
      schemeId,
      inpatient,
    };

    const missingAttributes = this.requiredAttributes.filter((attribute) => !attributes[attribute]);

    // Check required attributes on login
    if (missingAttributes.length) {
      // TODO: Add missing attributes to error
      throw new Error(`Missing required attributes: ${missingAttributes.join(", ")}`);
    }

    this.store.dispatch(setAuth(authState));
    this.store.dispatch(setAttibutes(attributes));
    this.onTokenUpdated?.();
  }
}
