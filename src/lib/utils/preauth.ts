import { PreAuth, PreAuthStatus, PreAuthType } from "~lib/api/types";

export enum Stage {
  /** Emergency */
  EMERGENCY_NOTIFICATION, // Has diagnosis information
  EMERGENCY_NOTIFICATION_APPROVED,
  EMERGENCY_FILLED, // Filled, no request amount
  EMERGENCY_FILLED_APPROVED, // Has benefit
  EMERGENCY_DISCHARGE, // Has amount

  /** Admission */
  ADMISSION_NOTIFICATION, // Optionally has initial request amount
  ADMISSION_APPROVED, // Has payer undertaking
  ADMISSION_DISCHARGE, // Has request amount, documents

  UNKNOWN,
}

export const getPreauthStage = (preauth: PreAuth | undefined) => {
  if (!preauth) return Stage.UNKNOWN;

  const { status } = preauth;
  const { DRAFT, ACTIVE, PENDING } = PreAuthStatus;

  switch (preauth.preauthType) {
    case PreAuthType.EMERGENCY_ADMISSION:
      if (status == DRAFT && preauth.draft) {
        return Stage.EMERGENCY_NOTIFICATION;
      } else if (status == ACTIVE && preauth.draft) {
        return Stage.EMERGENCY_NOTIFICATION_APPROVED;
      } else if (status == DRAFT && !preauth.draft) {
        return Stage.EMERGENCY_FILLED;
      } else if (status == ACTIVE && !preauth.draft) {
        return Stage.EMERGENCY_FILLED_APPROVED;
      } else if (status == PENDING) {
        return Stage.ADMISSION_DISCHARGE;
      }
      break;
    case PreAuthType.SCHEDULED_ADMISSION:
      if (status == DRAFT) {
        return Stage.ADMISSION_NOTIFICATION;
      } else if (status == ACTIVE) {
        return Stage.ADMISSION_APPROVED;
      } else if (status == PENDING) {
        return Stage.ADMISSION_DISCHARGE;
      }
  }

  return Stage.UNKNOWN;
};
