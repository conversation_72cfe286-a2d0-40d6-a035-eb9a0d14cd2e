export type Result = Array<number | '...'>;

/**
 * O(1) Pagination algorithm by narthur.
 * Unfortunately the number of items changes.
 * @see https://gist.github.com/kottenator/9d936eb3e4e3c3e02598?permalink_comment_id=3413141#gistcomment-3413141
 * @param current Current page
 * @param total Total number of pages
 */
export function narthurPaginate(current: number, total: number): Result {
  if (total <= 1) return [1];

  const center = [current - 1, current, current + 1]; // ... 3 4 5 ...
  const filteredCenter: Result = center.filter((p) => p > 1 && p < total);

  const includeTwoLeft = current === 4; // 1 [2 3] 4
  const includeTwoRight = current === total - 3; // 5 [6 7] 8
  const includeLeftDots = current > 4; // 1 ... 4 5 6
  const includeRightDots = current < total - 3; // 3 4 5 ... 8

  if (includeTwoLeft) filteredCenter.unshift(2);
  if (includeTwoRight) filteredCenter.push(total - 1);

  if (includeLeftDots) filteredCenter.unshift('...');
  if (includeRightDots) filteredCenter.push('...');

  return [1, ...filteredCenter, total];
}
