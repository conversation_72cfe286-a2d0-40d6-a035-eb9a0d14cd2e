import axios, { AxiosResponse } from "axios";

export interface UploadResponse<T = string> {
  success: boolean;
  msg: string;
  data?: T;
  results?: number;
}

export interface UploadArgs {
  progressCallBack?: (progressEvent: ProgressEvent) => void;
  file: File;
  signal?: AbortSignal;
  url: string;
  payload?: Record<string, string>;
}

export interface Upload {
  name: string;
  url: string;
  size: number;
}

export async function upload({ signal, file, progressCallBack, url, payload }: UploadArgs) {
  const fd = new FormData();

  for (const [key, value] of Object.entries(payload || {})) {
    fd.append(key, value as string);
  }

  fd.append("file", file); // WARNING: Add file last

  const axiosResponse = await axios.post<UploadResponse, AxiosResponse<UploadResponse>, FormData>(
    url,
    fd,
    {
      headers: {
        "Content-Type": "multipart/form-data",
        Accept: "*/*",
      },
      onUploadProgress: (progressEvent: ProgressEvent) => {
        progressCallBack && progressCallBack(progressEvent);
      },
      ...(signal && { signal }),
    }
  );

  return axiosResponse;
}
