import { Aggregation, ArrayField, ComputedPrimitive } from ".";
import { createExpressionEvaluator } from "~lib/utils";

/**
 * Aggregate the fields of an array field
 * Assumes only one aggregate field
 */
export const aggregateArrayField = <T extends object[]>(
  formItems: T,
  field: ArrayField
) => {
  const aggregateField = field.fields.find(
    (nestedField) => nestedField.name === field?.aggregate.field
  );

  if (!aggregateField) {
    return;
  }

  const evaluateExpression = createExpressionEvaluator();

  // TODO: Show non-NaN negative values
  const getValue = (item: (typeof formItems)[number]) =>
    aggregateField.type === ComputedPrimitive
      ? evaluateExpression(aggregateField.expression, item) || 0
      : item[field.aggregate.field];

  /**
   * NOTE: TypeScript burps on (A[] | B[]).map() but not on (A|B)[].map()
   * See - https://github.com/microsoft/TypeScript/issues/33591
   */
  switch (field.aggregate.aggregation) {
    case Aggregation.Sum:
      return (formItems as Array<(typeof formItems)[number]>)?.reduce(
        (acc: number, curr) => acc + getValue(curr),
        0
      );
    case Aggregation.Average:
      return (
        (formItems as Array<(typeof formItems)[number]>)?.reduce(
          (acc: number, curr) => acc + getValue(curr),
          0
        ) / formItems?.length
      );
    case Aggregation.Count:
      return (formItems as Array<(typeof formItems)[number]>)?.length;
    case Aggregation.Max:
      return (formItems as Array<(typeof formItems)[number]>)?.reduce(
        (acc: number, curr) => Math.max(acc, getValue(curr)),
        0
      );
    case Aggregation.Min:
      return (formItems as Array<(typeof formItems)[number]>)?.reduce(
        (acc: number, curr) => Math.min(acc, getValue(curr)),
        0
      );
  }
};
