# Service Fields

Service fields are an abstraction for dynamic fields for different preauth services. 

The base idea is to eventually have a configurable/dynamic list of fields fetched from the backend, currently for preauth requests for each service's fields, and later for other modules.

We then render the fields, and register `react-hook-form` components based on the type of the fields, field rules, et cetera.

## Assumptions/implementation details

We assume all `ComplexPrimitive.Array` fields are line items with the name `lines`, and render line items in a new section.

## Notes/warnings

- The implementation of service fields is not fully fleshed out, and edge cases may not have been accounted for or tested.
- Complex fields beyond two-levels deep are not accounted for.
- Unmounting fields will unregister them. We use this property to unregister fields when the service changes.
- `react-select` `async` does not persist the label of the selection option after a rerender when saving the value only. This means you can't simply save the value of an option, as the label will be blank when moving to a previous section.

## TODO

- Fetch request types and services from the network. Add request type and service API types.
- Create array of primitive values.
