import {
  Async<PERSON>elect,
  DateInputPlain,
  Input,
  Select,
  SelectCreatable,
  Switch,
  TextArea,
} from "~lib/components";
import { clsx, createExpressionEvaluator } from "~lib/utils";
import {
  ArrayField,
  BasicField,
  BasicNestedField,
  ComplexPrimitive,
  ComputedField,
  ComputedNestedField,
  ComputedPrimitive,
  DateRules,
  NetworkField,
  NetworkNestedField,
  NetworkPrimitive,
  NumberRules,
  OptionField,
  OptionNestedField,
  Primitive,
  TextRules,
  service,
} from ".";
import useNetworkPrimitiveOptions from "./useNetworkPrimitiveOptions";
import { Path, useFormContext } from "react-hook-form";
import { useEffect } from "react";

type Field =
  | BasicField
  | BasicNestedField
  | OptionField
  | OptionNestedField
  | NetworkField
  | NetworkNestedField
  | ComputedField
  | ComputedNestedField;

interface Props<D> {
  key?: string;
  className?: string;
  defaultValue?: D;
  parent?: ArrayField;
  index?: number;
  service?: service;
  field: Field;
  /**
   * Values implicitly passed by FieldWrapper
   */
  required?: boolean;
  id?: string;
  name?: string;
}

/**
 * Renders a *simple* field based on its type.
 * Does not handle Array or Object fields.
 */
const RenderField = <I extends object>({
  field,
  key,
  className,
  defaultValue,
  parent,
  index,
  name,
  id = name,
  required,
}: Props<(typeof field)["defaultValue"]>) => {
  const { watch, setValue } = useFormContext<I>();
  const form = watch();

  const childProps = {
    ...(name && { name }),
    ...(id && { id }),
    ...(required && { required }),
  };

  const networkPrimitiveOptions = useNetworkPrimitiveOptions();
  const evaluateExpression = createExpressionEvaluator(form);

  const computedValue =
    "isRegistered" in field && field.isRegistered
      ? evaluateExpression(field.expression, form)
      : undefined;

  useEffect(() => {
    if (computedValue !== undefined) {
      setValue(name as Path<I>, computedValue);
    }
  }, [computedValue, name, setValue]);

  switch (field.type) {
    case Primitive.Text:
      return (
        <Input
          className={clsx(className, field.hidden && "hidden")}
          {...(defaultValue && { defaultValue: defaultValue as string })}
          {...(key && { name: key })}
          {...(field.placeholder && {
            placeholder: String(field.placeholder),
          })}
          options={{
            ...((field.rules as TextRules)?.minLength && {
              minLength: (field.rules as TextRules).minLength,
            }),
            ...((field.rules as TextRules)?.maxLength && {
              maxLength: (field.rules as TextRules).maxLength,
            }),
          }}
          {...childProps}
        />
      );
    case Primitive.Number:
      return (
        <Input
          className={clsx(className, field.hidden && "hidden")}
          {...(defaultValue && { defaultValue: defaultValue as number })}
          {...(key && { name: key })}
          type="number"
          {...(field.placeholder && { placeholder: String(field.placeholder) })}
          options={{
            ...((field.rules as NumberRules)?.min && {
              min: (field.rules as NumberRules).min,
            }),
            ...((field.rules as NumberRules)?.max && {
              max: (field.rules as NumberRules).max,
            }),
          }}
          {...childProps}
        />
      );
    case Primitive.Boolean:
      return (
        <Switch
          className={clsx(className, field.hidden && "hidden")}
          {...(defaultValue && { defaultValue: defaultValue as boolean })}
          {...childProps}
        />
      );
    case Primitive.Date:
      return (
        <DateInputPlain
          className={clsx(className, field.hidden && "hidden")}
          {...(defaultValue && { defaultValue: defaultValue as string })}
          {...(key && { name: key })}
          {...(field.placeholder && { placeholder: String(field.placeholder) })}
          {...((field.rules as DateRules)?.min && {
            min: (field.rules as DateRules).min,
          })}
          {...((field.rules as DateRules)?.max && {
            max: (field.rules as DateRules).max,
          })}
          {...childProps}
        />
      );
    case Primitive.Textbox:
      return (
        <TextArea
          className={clsx(className, field.hidden && "hidden")}
          {...(defaultValue && { defaultValue: defaultValue as string })}
          {...(key && { name: key })}
          {...(field.placeholder && { placeholder: String(field.placeholder) })}
          options={{
            ...((field.rules as TextRules)?.minLength && {
              minLength: (field.rules as TextRules).minLength,
            }),
            ...((field.rules as TextRules)?.maxLength && {
              maxLength: (field.rules as TextRules).maxLength,
            }),
          }}
          {...childProps}
        />
      );
    case ComplexPrimitive.Options:
      return field.rules?.allowsOther ? (
        <SelectCreatable
          {...(defaultValue && { defaultValue: defaultValue as string })}
          className={clsx(className, field.hidden && "hidden")}
          {...(key && { name: key })}
          options={field.options}
          {...(field.placeholder && { placeholder: field.placeholder })}
          {...childProps}
        />
      ) : (
        <Select
          {...(defaultValue && { defaultValue: defaultValue as string })}
          className={clsx(className, field.hidden && "hidden")}
          {...(key && { name: key })}
          options={field.options}
          {...(field.placeholder && { placeholder: field.placeholder })}
          {...childProps}
        />
      );
    case NetworkPrimitive.Disease:
    case NetworkPrimitive.Drug:
    case NetworkPrimitive.LabTest:
    case NetworkPrimitive.Procedure:
      return (
        <AsyncSelect
          className={clsx(className, field.hidden && "hidden")}
          {...(key && { name: key })}
          getOptions={networkPrimitiveOptions.get(field.type as NetworkPrimitive)}
          placeholder={field.placeholder || "Search..."}
          {...childProps}
          valueOnly
        />
      );
    case ComputedPrimitive:
      return (
        <div className={clsx(className, field.hidden && "hidden")}>
          {/* TODO: Show non-Nan negative values */}
          <div className="text-gray-400 py-1 px-2">
            {evaluateExpression(
              field.expression,
              // Use current item as context for nested computed fields
              parent && (index && index >= 0) ? form[parent?.name]?.[index] : form
            ) ||
              defaultValue ||
              0}
          </div>
        </div>
      );
    default:
      return null;
  }
};

export default RenderField;
