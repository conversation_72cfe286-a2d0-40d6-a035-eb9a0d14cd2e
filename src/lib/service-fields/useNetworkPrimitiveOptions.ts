import {
  useLazySearchDiagnosisQuery,
  useLazySearchDrugQuery,
  useLazySearchLabTestQuery,
  useLazySearchProcedureQuery,
} from "~lib/api";
import { NetworkPrimitive } from ".";

/**
 * @returns A map of network primitive to a function that returns the options for that primitive
 * TODO: Return an object
 */
export default function useNetworkPrimitiveOptions(labelAsValue = true) {
  const [getProcedure] = useLazySearchProcedureQuery();
  const [getDiagnosis] = useLazySearchDiagnosisQuery();
  const [getDrug] = useLazySearchDrugQuery();
  const [getLabTest] = useLazySearchLabTestQuery();

  const getProcedureOptions = async (query: string) => {
    if (!query) return [];

    const procedures = await getProcedure({
      query: query,
    }).unwrap();

    return procedures.map((procedure) => ({
      value: labelAsValue ? procedure.procedure_description : procedure.procedure_code,
      label: procedure.procedure_description,
    }));
  };

  const getDiagnosisOptions = async (query: string) => {
    if (!query) return [];

    const diagnoses = await getDiagnosis({
      query: query,
    }).unwrap();

    return diagnoses.map((diagnosis) => ({
      value: labelAsValue ? diagnosis.title : diagnosis.code,
      label: diagnosis.title,
    }));
  };

  const getDrugOptions = async (query: string) => {
    if (!query) return [];

    const drugs = await getDrug({
      query: query,
    }).unwrap();

    return drugs.map((drug) => ({
      value: labelAsValue ? drug.name : drug.id.toString(),
      label: drug.name,
    }));
  };

  const getLabTestOptions = async (query: string) => {
    if (!query) return [];

    const labTests = await getLabTest({
      query: query,
    }).unwrap();

    return labTests.map((labTest) => ({
      value: labelAsValue ? labTest.name : labTest.id.toString(),
      label: labTest.name,
    }));
  };

  return new Map([
    [NetworkPrimitive.Procedure, getProcedureOptions],
    [NetworkPrimitive.Disease, getDiagnosisOptions],
    [NetworkPrimitive.Drug, getDrugOptions],
    [NetworkPrimitive.LabTest, getLabTestOptions],
  ]);
}
