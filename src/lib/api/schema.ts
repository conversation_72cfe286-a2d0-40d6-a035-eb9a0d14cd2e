import { z } from "zod";
import {
  BeneficiaryStatus,
  BenefitStatus,
  BillingStation,
  BiometricStatus,
  DeductibleType,
  FacilityType,
  Finger,
  Gender,
  Hand,
  InvoiceStatus,
  MemberPrivilege,
  OfflineVisitReason,
  PayerStatus,
  VisitStatus,
  VisitType,
  ProviderTier,
  BeneficiaryType,
  SearchProviderFilter,
  ListResponse,
  InvoiceLineType,
} from "./types";

/**
 * TODO: Validate BE responses
 * nullish() - optional, undefined, nullable
 */

const SearchVisitsRequestSchema = z.object({
  providerIds: z.array(z.number()).nullish(),
  payerIds: z.array(z.number()).nullish(),
  policyIds: z.array(z.number()).nullish(),
  visitTypes: z.array(z.nativeEnum(VisitType)).nullish(),
  statuses: z.array(z.nativeEnum(VisitStatus)).nullish(),
  query: z.string().nullish(),
  // TODO: Validate dates
  startDate: z.string().nullish(),
  endDate: z.string().nullish(),
});

export type SearchVisitsRequest = z.infer<typeof SearchVisitsRequestSchema>;

const SearchProviderClaimsRequestSchema = z.object({
  providerIds: z.array(z.number()).nullish(),
  payerIds: z.array(z.number()).nullish(),
  policyIds: z.array(z.number()).nullish(),
  beneficiaryIds: z.array(z.number()).nullish(),
  invoicesStatuses: z.array(z.nativeEnum(InvoiceStatus)).nullish(),
  visitStatuses: z.array(z.nativeEnum(VisitStatus)).nullish(),
  visitTypes: z.array(z.nativeEnum(VisitType)).nullish(),
  query: z.string().nullish(),
  // TODO: Validate dates
  startDate: z.string().nullish(),
  endDate: z.string().nullish(),
});

export type SearchProviderClaimsRequest = z.infer<typeof SearchProviderClaimsRequestSchema>;

/**
 * A provider claim is an _invoice_
 */
const ProviderClaimSchema = z.object({
  visitNumber: z.number(),
  memberName: z.string().optional(),
  memberNumber: z.string().optional(),
  benefitName: z.string().optional(),
  schemeName: z.string().optional(),
  payerName: z.string().optional(),
  invoiceAmount: z.number(),
  invoiceNumber: z.string(),
  batchInvoiceNumber: z.string().optional(),
  createdAt: z.string().optional(),
  providerName: z.string().optional(),
  status: z.nativeEnum(VisitStatus),
  payerStatus: z.nativeEnum(PayerStatus).optional(),
  hospitalProviderId: z.number().optional(),
  invoiceId: z.number(),
});

export type ProviderClaim = z.infer<typeof ProviderClaimSchema>;

const StartVisitRequestSchema = z.object({
  memberNumber: z.string(),
  memberName: z.string(),
  hospitalProviderId: z.number(),
  staffId: z.string(),
  staffName: z.string(),
  benefitName: z.string(),
  categoryId: z.string(),
  payerId: z.string(),
  balanceAmount: z.number(),
  benefitId: z.number(),
  providerMiddleware: z.string(),
  aggregateId: z.string(),
  beneficiaryId: z.number().nullish(),
  beneficiaryType: z.string().nullish(),
  payerName: z.string().nullish(),
  policyNumber: z.string().nullish(),
  billingStation: z.nativeEnum(BillingStation).nullish(),
  visitType: z.nativeEnum(VisitType).nullish(),
  /* -------------------- Required if visit type is offlct -------------------- */
  offlctInvoiceDate: z.string().nullish(),
  offSystemReason: z.nativeEnum(OfflineVisitReason).nullish(),
  facilityType: z.nativeEnum(FacilityType).nullish(),
  /* ---------------- Required? if visit type is reimbursement ---------------- */
  reimbursementProvider: z.string().nullish(),
  reimbursementInvoiceDate: z.string().nullish(),
  reimbursementReason: z.string().nullish(),
});

export type StartVisitRequest = z.infer<typeof StartVisitRequestSchema>;

const LineItemSchema = z.object({
  type: z.string(),
  code: z.string().nullish(),
  description: z.string().nullish(),
  unitPrice: z.number(),
  quantity: z.number(),
});

export type LineItem = z.infer<typeof LineItemSchema>;

const SearchClaimVettingRequestSchema = z.object({
  providerIds: z.array(z.number()).nullish(),
  payerIds: z.array(z.number()).nullish(),
  planIds: z.array(z.number()).nullish(),
  policyIds: z.array(z.number()).nullish(),
  visitTypes: z.array(z.nativeEnum(VisitType)).nullish(),
  statuses: z.array(z.nativeEnum(VisitStatus)).nullish(),
  query: z.string().nullish(),
  // TODO: Validate dates
  startDate: z.string().nullish(),
  endDate: z.string().nullish(),
});

export type SearchClaimVettingRequest = z.infer<typeof SearchClaimVettingRequestSchema>;

const GetMemberBiometricsRequestSchema = z.object({
  memberNumber: z.string(),
  biometricStatus: z.nativeEnum(BiometricStatus).optional(),
  page: z.number(),
  size: z.number(),
});

export type GetMemberBiometricsRequest = z.infer<typeof GetMemberBiometricsRequestSchema>;

export const FingerPrintSchema = z.object({
  biometricId: z.number(),
  base64: z.string(),
  base64ISO: z.string(),
  hand: z.nativeEnum(Hand),
  finger: z.nativeEnum(Finger),
  beneficiaryId: z.number(),
  memberNumber: z.string(),
  captureDate: z.string(),
  biometricStatus: z.nativeEnum(BiometricStatus),
});

export type BeneficiaryFingerPrint = z.infer<typeof FingerPrintSchema>;

export type GetMemberBiometricsResponse = ListResponse<BeneficiaryFingerPrint>;

export const TopUpBenefitRequestSchema = z.object({
  id: z.number(),
  //benefitId: z.number(),
  // beneficiaryId: z.number(),
  //aggregateId: z.string(),
  //memberNumber: z.string(),
  topUpReason: z.string(),
  topUpBy: z.string(),
  topUpAmount: z.number(),
});

export type TopUpBenefitRequest = z.infer<typeof TopUpBenefitRequestSchema>;

export const TransferBenefitBalanceRequestSchema = z.object({
  benefitId: z.number(),
  beneficiaryId: z.number(),
  aggregateId: z.string(),
  memberNumber: z.string(),
  reason: z.string(),
  username: z.string(),
  amount: z.number(),
  fromBenefitBeneficiaryId: z.number(),
  fromAggregate: z.string(),
  toBenefitBeneficiaryId: z.number(),
  toAggregate: z.string(),
});

export type TransferBenefitBalanceRequest = z.infer<typeof TransferBenefitBalanceRequestSchema>;

export const TopUpBenefitRequestV2Schema = z.object({
  reason: z.string(),
  username: z.string(),
  amount: z.number(),
});

export type TopUpBenefitV2Request = z.infer<typeof TopUpBenefitRequestV2Schema>;

export const SuspendBenefitRequestSchema = z.object({
  reason: z.string(),
  username: z.string(),
  scope: z.enum(["INDIVIDUAL", "FAMILY"]).nullish(),
});

export type SuspendBenefitRequest = z.infer<typeof SuspendBenefitRequestSchema>;

export const ActivateBenefitRequestSchema = SuspendBenefitRequestSchema.extend({});
export type ActivateBenefitRequest = z.infer<typeof ActivateBenefitRequestSchema>;

export const TransferBenefitBalanceRequestV2Schema = z.object({
  sourceBenefitId: z.number(),
  destinationBenefitId: z.number(),
  amount: z.number(),
  reason: z.string(),
  username: z.string(),
});

export type TransferBenefitBalanceV2Request = z.infer<typeof TransferBenefitBalanceRequestV2Schema>;

export const UpdateMemberStatusRequestSchema = z.object({
  reason: z.string(),
  username: z.string(),
  startDate: z.string(),
});

export type UpdateMemberStatusRequest = z.infer<typeof UpdateMemberStatusRequestSchema>;

export const UpdateMemberRequestSchema = z.object({
  canUseBiometrics: z.boolean().nullish(),
  nhifNumber: z.string().nullish(),
  dob: z.string().nullish(),
  gender: z.nativeEnum(Gender).nullish(),
  beneficiaryType: z.nativeEnum(BeneficiaryType).nullish(),
  phoneNumber: z.string().nullish(),
  email: z.string().nullish(),
  status: z.nativeEnum(BeneficiaryStatus).nullish(),
  joinDate: z.string().nullish(),
  name: z.string().nullish(),
  privilege: z.nativeEnum(MemberPrivilege).nullish(),
  reason: z.string().nullish(),
  updateBy: z.string().nullish(),
  otherNumber: z.string().nullish(),
  updateType: z.enum(["INDIVIDUAL", "FAMILY"]).optional(),
});

export type UpdateMemberRequest = z.infer<typeof UpdateMemberRequestSchema>;

export const CategoryChangeRequestSchema = z.object({
  beneficiaryIds: z.array(z.number()),
  categoryId: z.number(),
  updateBy: z.string(),
  reason: z.string(),
  transferUtilization: z.boolean(),
});

export type CategoryChangeRequest = z.infer<typeof CategoryChangeRequestSchema>;

export const BeneficiaryStatusUpdateRequestSchema = z.object({
  beneficiaryIds: z.array(z.number()),
  status: z.nativeEnum(BeneficiaryStatus),
  updateType: z.enum(["INDIVIDUAL", "FAMILY"]),
  updateBy: z.string(),
  reason: z.string(),
});

export type BeneficiaryStatusUpdateRequest = z.infer<typeof BeneficiaryStatusUpdateRequestSchema>;

export const UpdateBenefitStatusRequestSchema = z.object({
  benefitStatus: z.nativeEnum(BenefitStatus),
  updateBy: z.string(),
  reason: z.string(),
  benefitIds: z.array(z.number()),
});

export type UpdateBenefitStatusRequest = z.infer<typeof UpdateBenefitStatusRequestSchema>;

export const BeneficiaryBenefitStatusUpdateSchema = z.object({
  beneficiaryId: z.number(),
  updateType: z.enum(["INDIVIDUAL", "FAMILY"]),
  benefitIds: z.array(z.number()),
  benefitStatus: z.nativeEnum(BenefitStatus),
});

export const BeneficiaryBenefitStatusUpdateRequestSchema = z.object({
  updateBy: z.string(),
  reason: z.string(),
  updateList: z.array(BeneficiaryBenefitStatusUpdateSchema),
});

export type BeneficiaryBenefitStatusUpdateRequest = z.infer<
  typeof BeneficiaryBenefitStatusUpdateRequestSchema
>;

const BillingDeductibleSchema = z.object({
  deductibleType: z.nativeEnum(DeductibleType),
  amount: z.number(),
});

const BillingLineItemSchema = z.object({
  description: z.string(),
  quantity: z.number(),
  lineCategory: z.string().optional(), // TODO: Validate is optional
  lineTotal: z.number(),
  lineType: z.nativeEnum(InvoiceLineType),
  unitPrice: z.number(),
});

const BillingInvoiceSchema = z.object({
  invoiceNumber: z.string(),
  amount: z.number(),
  service: z.string().optional(), // TODO: Validate is optional
  deductibles: z.array(BillingDeductibleSchema).optional(),
  lineItems: z.array(BillingLineItemSchema).optional(),
});

export type BillingInvoice = z.infer<typeof BillingInvoiceSchema>;

const BillingDiagnosisSchema = z.object({
  id: z.number(),
  code: z.string(),
  title: z.string(),
  invoiceNumber: z.string(),
  claimRef: z.string(),
});

export const BillVisitSchema = z.object({
  id: z.number(),
  totalInvoiceAmount: z.number(),
  invoiceNumber: z.string().optional(),
  diagnosis: z.array(BillingDiagnosisSchema).optional(),
  hospitalProviderId: z.number(),
  singleInvoiceDeductibles: z.array(BillingDeductibleSchema).optional(),
  invoices: z.array(BillingInvoiceSchema).optional(),
  staffId: z.string().optional(),
  staffName: z.string(),
});

export type BillVisitRequest = z.infer<typeof BillVisitSchema>;

export const FilterPayerProvidersSchema = z.object({
  payerId: z.number(),
  query: z.string().optional(),
  countryId: z.number().optional(),
  regionId: z.number().optional(),
  providerType: z.nativeEnum(SearchProviderFilter).optional(),
  tier: z.nativeEnum(ProviderTier).optional(),
  mainFacilityId: z.number().optional(),
});

export type FilterPayerProviders = z.infer<typeof FilterPayerProvidersSchema>;

const PatchInvoiceLineItemSchema = z.object({
  itemDescription: z.string(),
  itemAmount: z.number(),
  invoiceNumber: z.string().optional(),
  lineType: z.nativeEnum(InvoiceLineType),
  itemCode: z.string().optional(),
  itemQuantity: z.number(),
  itemUnitPrice: z.number(),
});

export const PatchInvoiceSchema = z.object({
  invoiceNumber: z.string(),
  totalAmount: z.number(),
  providerId: z.number(),
  visitId: z.number(),
  lineItems: z.array(PatchInvoiceLineItemSchema).optional(),
});

export type PatchInvoiceRequest = z.infer<typeof PatchInvoiceSchema>;

export const ReactivatePreauthSchema = z.object({
  reactivatedBy: z.string(),
  reason: z.string(),
});

export type ReactivatePreauthRequest = z.infer<typeof ReactivatePreauthSchema>;
