import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { AuthState } from "~lib/store/authSlice";
import { PartialOrUndefined } from "~lib/types";
import { dateFileName, downloadFile, sleep } from "~lib/utils";
import { baseUrl } from "../constants";
import {
  ActivateBenefitRequest,
  BeneficiaryBenefitStatusUpdateRequest,
  BeneficiaryStatusUpdateRequest,
  BillVisitRequest,
  CategoryChangeRequest,
  FilterPayerProviders,
  GetMemberBiometricsRequest,
  GetMemberBiometricsResponse,
  PatchInvoiceRequest,
  ReactivatePreauthRequest,
  SearchClaimVettingRequest,
  SearchProviderClaimsRequest,
  SearchVisitsRequest,
  StartVisitRequest,
  TopUpBenefitRequest,
  TransferBenefitBalanceRequest,
  UpdateBenefitStatusRequest,
  UpdateMemberRequest,
} from "./schema";
import {
  AddRestrictionProvidersResponse,
  AdmissionRequest,
  AllocateDeviceRequest,
  AllocateDeviceResponse,
  ApproveTopUpRequest,
  AuthorizePreauthRequest,
  AuthorizePreauthResponse,
  BaseDiagnosis,
  BatchUpdateBeneficiariesQuery,
  BatchUpdateBeneficiariesResponse,
  BatchUpdateBeneficiaryStatusQuery,
  BatchUpdateBeneficiaryStatusResponse,
  BeneficiariesResponse,
  Beneficiary,
  BeneficiaryBasicsResponse,
  BeneficiaryBenefit,
  BeneficiaryBenefitProviderResponse,
  BeneficiaryBenefitsResponse,
  BeneficiaryFamilyResponse,
  BeneficiaryListResponse,
  BeneficiaryProviderBenefitsResponse,
  BeneficiaryResponse,
  BenefitProvidersResponse,
  CategoriesResponse,
  Category,
  ClaimsReportType,
  ClaimsResponse,
  Country,
  CountryResponse,
  CreateRestrictionResponse,
  DeclinePreAuthResponse,
  DeclinePreauthRequest,
  DeletePreAuthResponse,
  DeleteRestrictionResponse,
  DeviceAllocationResponse,
  DeviceModelResponse,
  DeviceSimStatus,
  DeviceStatus,
  DevicesResponse,
  DiagnosisResponse,
  DispatchVisitRequest,
  DispatchVisitResponse,
  DownloadFileResponse,
  DraftVisitRequest,
  DraftVisitResponse,
  Drug,
  DrugsResponse,
  EditPreAuthResponse,
  EditPreauthRequest,
  EnhancedPoliciesResponse,
  EnhancedPolicy,
  ExportFileType,
  FilterBeneficiaries,
  FilterInvoices,
  FilterVisits,
  GetPaginatedEventsNotification,
  HttpMethod,
  LabTest,
  LabTestsResponse,
  ListResponse,
  MappedProvider,
  MutationResponse,
  NewPreAuthResponse,
  OTPRequest,
  PagedVisitsResponse,
  Pagination,
  PatchDeviceRequest,
  Payer,
  PayerProviderMappingResponse,
  PayerResponse,
  PlainPreAuthsResponse,
  Plan,
  PlansResponse,
  PoliciesResponse,
  Policy,
  PreAuth,
  PreAuthRePortRequest,
  PreAuthResponse,
  PreAuthStatus,
  PreAuthTopUpByPayerRequest,
  PreAuthsResponse,
  PreauthBase,
  Procedure,
  ProcedureResponse,
  Provider,
  ProviderBranchesResponse,
  ProviderClaimsResponse,
  ProviderResponse,
  ProvidersResponse,
  Region,
  RegionResponse,
  RejectTopUpRequest,
  RemoveRestrictionProvidersResponse,
  ReportVisit,
  Restriction,
  RestrictionProvidersResponse,
  RestrictionType,
  RestrictionsResponse,
  SaveBillAndCloseVisitResponse,
  SearchBeneficiariesResponse,
  SearchProviderFilter,
  SimpleResponse,
  SimsResponse,
  StatementType,
  UserNotification,
  UsersResponse,
  VerifyOTPRequest,
  Visit,
  VisitResponse,
  VisitsResponse,
} from "./types";

const REFRESH_DELAY = 1000;

/**
 * TODO: Refactor invalidateTags
 */
export const api = createApi({
  reducerPath: "api",
  baseQuery: fetchBaseQuery({
    baseUrl: baseUrl,
    prepareHeaders(headers, { getState }) {
      const { isAuthenticated, token } = (getState() as { auth?: AuthState })?.auth || {};

      if (isAuthenticated) {
        headers.set("Authorization", `Bearer ${token}`);
      }

      return headers;
    },
  }),
  tagTypes: [
    "Beneficiaries",
    "BeneficiaryBenefitProvider",
    "BeneficiaryBenefits",
    "Biometrics",
    "Categories",
    "Claims",
    "DeviceAllocations",
    "DeviceModels",
    "Devices",
    "MappedProviders",
    "PayerProviders",
    "BeneficiaryFamily",
    "Plans",
    "Policies",
    "PreAuths",
    "ProviderBranches",
    "Providers",
    "RestrictionProviders",
    "Restrictions",
    "Sims",
    "Users",
    "Visits",
    "VisitsToVet",
    "AuditLogs",
    "PreAuthReports",
    "Notifications",
  ],
  endpoints: (builder) => ({
    // Returns a list of countries
    getCountries: builder.query<Country[], Record<string, never>>({
      query: () => `/api/v1/country/`, // WARN: Trailing slash is required
      transformResponse: (response: CountryResponse) => response.data,
    }),

    // Returns a list of regions
    getRegions: builder.query<Region[], { countryId: number }>({
      query: ({ countryId }) => {
        const url = `/api/v1/country/${countryId}/region/`;

        const urlSearchParams = new URLSearchParams();
        urlSearchParams.append("page", "1");
        urlSearchParams.append("size", "500"); // WARN: Assumes there are less than 500 regions per country

        return url + "?" + urlSearchParams.toString();
      },
      transformResponse: (response: RegionResponse) => response.data.content,
    }),

    // Returns a list of providers mapped to the benefit
    // plus list of unmapped providers from the Payer Providers
    getBenefitMappedProviders: builder.query<
      ListResponse<MappedProvider>,
      {
        benefitId: number;
        page: number;
        size: number;
      }
    >({
      query: ({ benefitId, page, size }) =>
        `/api/v1/membership/payer/benefit/${benefitId}/provider/mapping/${page}/${size}`,
      providesTags: (result, error, { benefitId }) => [
        { type: "MappedProviders" as const, id: benefitId },
      ],
    }),

    // Maps a Benefit to a batch of Providers/Facilities
    addBenefitMappedProviders: builder.mutation<
      MutationResponse,
      {
        benefitId: number;
        providerIds: number[];
        payerId: number;
      }
    >({
      query: ({ benefitId, payerId, providerIds }) => ({
        url: `/api/v1/membership/benefit/mapping/provider/batch`,
        method: HttpMethod.POST,
        body: {
          payerId: payerId,
          benefitId: benefitId,
          providerIds: providerIds,
        },
      }),
      invalidatesTags: (result, error, { benefitId }) => [
        { type: "MappedProviders", id: benefitId },
      ],
    }),

    // Unmaps a batch of Providers from a Benefit
    unmapBenefitMappedProviders: builder.mutation<
      MutationResponse,
      {
        benefitId: number;
        providerIds: number[];
      }
    >({
      query: ({ benefitId, providerIds }) => ({
        url: `/api/v1/membership/benefit/${benefitId}/unmap/provider/batch`,
        method: HttpMethod.PUT, // Why oh, why!?
        body: {
          providerIds: providerIds,
        },
      }),
      invalidatesTags: (result, error, { benefitId }) => [
        { type: "MappedProviders", id: benefitId },
      ],
    }),

    // Retuns a list of benefits available to a beneficiary
    getBeneficiaryBenefits: builder.query<
      BeneficiaryBenefit[],
      {
        beneficiaryId: number;
      }
    >({
      query: ({ beneficiaryId }) =>
        `/api/v1/visit/benefit/search/withoutStatus/all/${beneficiaryId}`,
      providesTags: (result, error, { beneficiaryId }) => [
        { type: "BeneficiaryBenefits", id: beneficiaryId },
      ],
      transformResponse: (response: BeneficiaryBenefitsResponse) => response.data,
    }),

    // Retuns a list of benefits available to a beneficiary
    getActiveBeneficiaryBenefits: builder.query<
      BeneficiaryBenefit[],
      {
        beneficiaryId: number;
      }
    >({
      query: ({ beneficiaryId }) =>
        `/api/v1/visit/benefit/searc/api/v1/visit/benefit/search/${beneficiaryId}`,
      providesTags: (result, error, { beneficiaryId }) => [
        { type: "BeneficiaryBenefits", id: beneficiaryId },
      ],
      transformResponse: (response: BeneficiaryBenefitsResponse) => response.data,
    }),

    // Returns a list of mapped providers for the given benefit and region matching the query
    searchBenefitProviders: builder.query<
      Provider[],
      {
        benefitId: number;
        regionId: number;
        page?: number;
        size?: number;
        query?: string;
      }
    >({
      query: ({ benefitId, regionId, query = "", page = 1, size = 50 }) => {
        const url = `/api/v1/membership/payer/benefit/${benefitId}/provider/region/${regionId}`;

        const urlSearchParams = new URLSearchParams();

        if (query.length > 0) {
          urlSearchParams.append("facilityName", query);
        }
        urlSearchParams.append("page", page.toString());
        urlSearchParams.append("size", size.toString());

        return url + "?" + urlSearchParams.toString();
      },
      transformResponse: (response: BenefitProvidersResponse) =>
        response.data.content.map((mappedProvider) => mappedProvider.provider),
    }),

    // Maps a member benefit to a provider
    addBenefitProvider: builder.mutation<
      MutationResponse,
      {
        benefitId: number;
        providerId: number;
        beneficiaryId: number;
      }
    >({
      query: ({ benefitId, beneficiaryId, providerId }) => ({
        url: `/api/v1/membership/beneficiary/mapping/provider`,
        method: HttpMethod.POST,
        body: {
          beneficiaryId: beneficiaryId,
          benefitId: benefitId,
          providerId: providerId,
        },
      }),
      invalidatesTags: (result, error, arg) => [
        {
          type: "BeneficiaryBenefitProvider",
          id: `${arg.beneficiaryId}-${arg.benefitId}`,
        },
      ],
    }),

    // Gets the provider mapped to a beneficiary for a given benefit
    getBenefitProvider: builder.query<
      Provider | undefined,
      {
        benefitId: number;
        beneficiaryId: number;
      }
    >({
      query: ({ benefitId, beneficiaryId }) => {
        const url = `/api/v1/membership/beneficiary/benefit/mapping/provider`;

        const urlSearchParams = new URLSearchParams();
        urlSearchParams.append("beneficiaryId", beneficiaryId.toString());
        urlSearchParams.append("benefitId", benefitId.toString());

        return url + "?" + urlSearchParams.toString();
      },
      transformResponse: (response: BeneficiaryBenefitProviderResponse) =>
        response?.data?.[0]?.provider,
      providesTags: (_result, _error, arg) => [
        {
          type: "BeneficiaryBenefitProvider",
          id: `${arg.beneficiaryId}-${arg.benefitId}`,
        },
      ],
    }),

    // Returns a list of preauths for a given provider
    getPreauths: builder.query<
      PreAuthsResponse["data"],
      {
        providerId: number;
        page?: number;
        size?: number;
      }
    >({
      query: ({ providerId, page = 1, size = 10 }) => {
        return `/api/v1/preauthorization/${providerId}/provider/?page=${page}&size=${size}`;
      },
      transformResponse: (response: PreAuthsResponse) => response.data,
      providesTags: (result, _error, _arg) =>
        result?.content
          ? [...result.content.map(({ id }) => ({ type: "PreAuths" as const, id })), "PreAuths"]
          : ["PreAuths"],
    }),

    getProviderBranches: builder.query<
      ProviderBranchesResponse,
      {
        providerId: number;
      }
    >({
      query: ({ providerId }) => {
        return {
          url: `/api/v1/provider/branches`,
          params: {
            providerId,
          },
        };
      },
      providesTags: (_result, _error, arg) => [{ type: "ProviderBranches", id: arg.providerId }],
    }),

    /**
     * Returns a list of active visits for a given branch
     * TODO: Verify response types
     */
    getActiveVisits: builder.query<Visit[], { providerId: number; staffId: string }>({
      query: ({ providerId, staffId }) => `/api/v1/visit/${providerId}/${staffId}/active`,
      transformResponse: (response: VisitsResponse) => response.data,
      providesTags: (result, _error, _arg) =>
        result
          ? [...result.map(({ id }) => ({ type: "Visits" as const, id })), "Visits"]
          : ["Visits"],
    }),

    /**
     * Returns a list of active visits for the main facility and satellite branches
     */
    getMainFacilityActiveVisits: builder.query<Visit[], { providerId: number; staffId: string }>({
      query: ({ providerId, staffId }) => `/api/v1/visit/main/${providerId}/${staffId}/active`,
      transformResponse: (response: VisitsResponse) => response.data,
      providesTags: (result, _error, _arg) =>
        result
          ? [...result.map(({ id }) => ({ type: "Visits" as const, id })), "Visits"]
          : ["Visits"],
    }),

    // Returns a list of active visits for a given provider and staff
    getActiveAndDraftVisits: builder.query<Visit[], { providerId: number; staffId: string }>({
      query: ({ providerId, staffId }) =>
        `/api/v1/visit/main/${providerId}/${staffId}/activeAndDraft`,
      transformResponse: (response: VisitsResponse) => response.data,
      providesTags: (result, _error, _arg) =>
        result
          ? [...result.map(({ id }) => ({ type: "Visits" as const, id })), "Visits"]
          : ["Visits"],
    }),

    // Currently returns error 500
    getClosedVisits: builder.query<Visit[], { providerId: number; staffId: string }>({
      query: ({ providerId, staffId }) => `/api/v1/visit/${providerId}/${staffId}/closed`,
      transformResponse: (response: VisitsResponse) => response.data,
    }),

    getPagedClosedVisits: builder.query<
      PagedVisitsResponse,
      { providerId: number; staffId: string; page: number; size: number }
    >({
      query: ({ providerId, staffId, page, size }) =>
        `/api/v1/visit/${providerId}/${staffId}/${page}/${size}/closed`,
    }),

    // Get a visit - GET /api/v1/visit/{id}/visit
    getVisit: builder.query<Visit | undefined, { id: number }>({
      query: ({ id }) => `/api/v1/visit/${id}/visit`,
      transformResponse: (response: VisitResponse) => response.data,
      providesTags: (result, error, { id }) => [{ type: "Visits", id }],
    }),

    // Search for a diagnosis (disease) by title - /visit/searchICD10ByTitle/icd10code?title=ba&page=1&size=50
    searchDiagnosis: builder.query<BaseDiagnosis[], { query: string }>({
      query: ({ query }) =>
        `/api/v1/visit/searchICD10ByTitle/icd10code?title=${query}&page=1&size=50`,
      transformResponse: (response: DiagnosisResponse) => response.data.content,
    }),

    // Search for a procedure by title - /visit/searchMedicalProcedure
    searchProcedure: builder.query<Procedure[], { query: string }>({
      query: ({ query }) => `/api/v1/visit/searchMedicalProcedure?title=${query}&page=1&size=50`,
      transformResponse: (response: ProcedureResponse) => response.data.content,
    }),

    // Search for a drug - /api/v1/visit/searchMedicalDrugs
    searchDrug: builder.query<Drug[], { query: string }>({
      query: ({ query }) => `/api/v1/visit/searchMedicalDrugs?title=${query}&page=1&size=50`,
      transformResponse: (response: DrugsResponse) => response.data.content,
    }),

    // Search for a lab test - /api/v1/visit/searchLaboratory
    searchLabTest: builder.query<LabTest[], { query: string }>({
      query: ({ query }) => `/api/v1/visit/searchLaboratory?title=${query}&page=1&size=50`,
      transformResponse: (response: LabTestsResponse) => response.data.content,
    }),

    // Get payers
    getPayers: builder.query<Payer[], Record<string, never>>({
      query: () => `/api/v1/membership/payers/`, // WARN: Trailing slash is required
      transformResponse: (response: PayerResponse) => response.data,
    }),

    // Adds a new preauthorization - /preauthorization/new
    addPreauth: builder.mutation<NewPreAuthResponse, PreauthBase | AdmissionRequest>({
      query: (body) => ({
        url: `/api/v1/preauthorization/new`,
        method: HttpMethod.POST,
        body,
      }),
      // Fetch the new list of preauths after a delay
      onQueryStarted: async (data, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        setTimeout(() => {
          dispatch(api.util.invalidateTags(["PreAuths"]));
        }, 1000);
      },
    }),

    // Edit a preauthorization - /api/v1/preauthorization/edit/{preAuthId}
    editPreauth: builder.mutation<EditPreAuthResponse, EditPreauthRequest>({
      query: ({ id, body }) => ({
        url: `/api/v1/preauthorization/edit/${id}`,
        method: HttpMethod.PUT,
        body,
      }),
      // Fetch the new list of preauths after a delay
      onQueryStarted: async ({ id }, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        setTimeout(() => {
          dispatch(api.util.invalidateTags([{ type: "PreAuths", id }]));
        }, 1000);
      },
      invalidatesTags: ["AuditLogs"],
    }),

    // Deletes a preauthorization - PUT /api/v1/preauthorization/cancel/{id} - { "reason": "string" }
    deletePreauth: builder.mutation<
      DeletePreAuthResponse,
      { id: number; reason: string; cancelledBy: string }
    >({
      query: ({ id, reason, cancelledBy }) => ({
        url: `/api/v1/preauthorization/cancel/${id}/payer`,
        method: HttpMethod.PUT,
        body: {
          reason,
          cancelledBy,
        },
      }),
      // Fetch the new list of preauths after a delay
      onQueryStarted: async (data, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        setTimeout(() => {
          dispatch(api.util.invalidateTags([{ type: "PreAuths", id: data.id }]));
        }, 1000);
      },
    }),

    // Searches a preauthorization - GET /api/v1/preauthorization/search?search=XJDC8024&page=1&size=10
    // Either providerId or payerId must be provided
    searchPreauth: builder.query<
      PreAuthsResponse["data"],
      {
        page: number;
        size: number;
      } & PartialOrUndefined<{
        requestTypes: Array<string>;
        providerId: number;
        beneficiaryId: number;
        search: string;
        payerId: number;
        startDate: string;
        endDate: string;
        status: Array<PreAuthStatus>;
        planId: number;
      }>
    >({
      query: ({
        search,
        providerId,
        payerId,
        beneficiaryId,
        status,
        requestTypes,
        startDate,
        endDate,
        page = 1,
        size = 10,
        planId,
      }) => ({
        url: `/api/v1/preauthorization/search`,
        params: {
          page,
          size,
          requestTypes,
          ...(search && { search }),
          ...(providerId && { providerId }),
          ...(payerId && { payerId }),
          ...(startDate && { startDate }),
          ...(endDate && { endDate }),
          ...(status && { status }),
          ...(beneficiaryId && { beneficiaryId }),
          ...(planId && { planId }),
        },
      }),
      transformResponse: (response: PreAuthsResponse) => response.data,
      providesTags: (result, _error, _arg) =>
        result
          ? [...result.content.map(({ id }) => ({ type: "PreAuths" as const, id })), "PreAuths"]
          : ["PreAuths"],
    }),

    // Counts pending preauthorizations - GET /api/v1/preauthorization/search
    countPendingPreAuths: builder.query<
      number | undefined,
      { payerId?: number; providerId?: number }
    >({
      query: ({ payerId, providerId }) => {
        const queryParams = new URLSearchParams();

        queryParams.append("page", "1");
        queryParams.append("size", "10");

        if (providerId) {
          queryParams.append("providerId", providerId.toString());
        } else if (payerId) {
          queryParams.append("payerId", payerId.toString());
        } else {
          // TODO: Throw error
        }

        queryParams.append("status", PreAuthStatus.PENDING);

        return `/api/v1/preauthorization/search?${queryParams.toString()}`;
      },
      transformResponse: (response: PreAuthsResponse) => response.data?.totalElements,
      providesTags: ["PreAuths"],
    }),

    // Gets a preauthorization - GET /api/v1/preauthorization/id/{id}/preauth
    getPreauth: builder.query<PreAuth | undefined, { id: number }>({
      query: ({ id }) => `/api/v1/preauthorization/id/${id}/preauth`,
      transformResponse: (response: PreAuthResponse) => response.data,
      providesTags: (result, error, { id }) => [{ type: "PreAuths", id }],
    }),

    // Get pending preauthorizations - GET /api/v1/preauthorization/{{payerId}}/pending/payer
    getPendingPreauthsByPayer: builder.query<
      PreAuth[],
      {
        payerId: number;
      }
    >({
      query: ({ payerId }) => `/api/v1/preauthorization/${payerId}/pending/payer`,
      transformResponse: (response: PlainPreAuthsResponse) => response.data,
      providesTags: ["PreAuths"],
    }),

    // Get preauthorizations by payer - GET /api/v1/preauthorization/{{payerId}}/payer
    getPreauthsByPayer: builder.query<
      PreAuth[],
      {
        payerId: number;
      }
    >({
      query: ({ payerId }) => `/api/v1/preauthorization/${payerId}/payer`,
      transformResponse: (response: PlainPreAuthsResponse) => response.data,
    }),

    // Approve a preauthorization - PUT /api/v1/preauthorization/authorize
    approvePreauth: builder.mutation<
      AuthorizePreauthResponse,
      AuthorizePreauthRequest & { id: number }
    >({
      query: ({ id, ...body }) => ({
        url: `/api/v1/preauthorization/authorize/${id}`,
        method: HttpMethod.PUT,
        body,
      }),
      onQueryStarted: async (data, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        setTimeout(() => {
          dispatch(api.util.invalidateTags(["PreAuths"]));
        }, 1000);
      },
      invalidatesTags: (result, error, { id }) => [{ type: "PreAuths", id }],
    }),

    // Decline a preauthorization - PUT /api/v1/preauthorization/decline
    declinePreauth: builder.mutation<
      DeclinePreAuthResponse,
      DeclinePreauthRequest & {
        id: number;
      }
    >({
      query: ({ id, ...body }) => ({
        url: `/api/v1/preauthorization/decline/${id}`,
        method: HttpMethod.PUT,
        body,
      }),
      onQueryStarted: async (data, { dispatch, queryFulfilled }) => {
        try {
          await queryFulfilled;
          setTimeout(() => {
            dispatch(api.util.invalidateTags(["PreAuths"]));
          }, 1000);
        } catch (error) {
          console.error(error);
        }
      },
      invalidatesTags: (result, error, { id }) => [{ type: "PreAuths", id }],
    }),

    // Download a file /api/file/download?name={name}
    downloadFile: builder.query<DownloadFileResponse, { name: string }>({
      query: ({ name }) => `/api/file/download?name=${name}`,
    }),

    // Search for a provider by name - GET /api/v1/provider/name
    searchProvider: builder.query<
      ProvidersResponse,
      { name: string; page?: number; size?: number }
    >({
      query: ({ name, page = 1, size = 10 }) => {
        const queryParams = new URLSearchParams();

        queryParams.append("page", page.toString());
        queryParams.append("size", size.toString());
        queryParams.append("name", name);

        return `/api/v1/provider/name?${queryParams.toString()}`;
      },
    }),

    // Get all providers - GET /api/v1/provider/all
    getProviders: builder.query<ProvidersResponse, { page?: number; size?: number }>({
      query: ({ page = 1, size = 10 }) => {
        const queryParams = new URLSearchParams();

        queryParams.append("page", page.toString());
        queryParams.append("size", size.toString());

        return `/api/v1/provider/all?${queryParams.toString()}`;
      },
      providesTags: (result, _error, _arg) =>
        result?.data?.content
          ? [
              ...result.data.content.map(({ id }) => ({ type: "Providers" as const, id })),
              "Providers",
            ]
          : ["Providers"],
    }),

    // Get provider - GET /api/v1/provider/
    getProvider: builder.query<ProviderResponse, { providerId: number }>({
      query: ({ providerId }) => ({
        url: `/api/v1/provider`,
        params: { providerId },
      }),
      providesTags: (result, _error, _arg) =>
        result?.data?.id ? [{ type: "Providers" as const, id: result.data.id }] : [],
    }),

    // Add a restriction set - POST /api/v1/restriction/create
    addRestriction: builder.mutation<
      CreateRestrictionResponse,
      {
        name: string;
        payerId: number;
        restrictionType: RestrictionType;
      }
    >({
      query: ({ name, payerId, restrictionType }) => ({
        url: "/api/v1/restriction/create",
        method: HttpMethod.POST,
        body: { name, payerId, restrictionType },
      }),
      invalidatesTags: (result, error, { payerId }) => [{ type: "Restrictions", id: payerId }],
      onQueryStarted: async (data, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        setTimeout(() => {
          dispatch(api.util.invalidateTags(["Restrictions"]));
        }, REFRESH_DELAY);
      },
    }),

    // Get all restriction by payer - GET /api/v1/restriction
    getRestrictions: builder.query<Restriction[], { payerId: number }>({
      query: ({ payerId }) => `/api/v1/restriction?payerId=${payerId}`,
      transformResponse: (response: RestrictionsResponse) => response.data,
      providesTags: (result, _error, _arg) =>
        result
          ? [...result.map(({ id }) => ({ type: "Restrictions" as const, id })), "Restrictions"]
          : ["Restrictions"],
    }),

    // Delete a restriction by id - PUT /api/v1/restriction/remove/{id}
    deleteRestriction: builder.mutation<DeleteRestrictionResponse, { id: number }>({
      query: ({ id }) => ({
        url: `/api/v1/restriction/remove/${id}`,
        method: HttpMethod.PUT,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "Restrictions", id: id }],
      onQueryStarted: async (data, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        setTimeout(() => {
          dispatch(api.util.invalidateTags(["Restrictions"]));
        }, REFRESH_DELAY);
      },
    }),

    // Get all active providers in a restriction - GET api/v1/restriction/providers
    getRestrictionProviders: builder.query<
      RestrictionProvidersResponse,
      { restrictionId: number; page: number; size: number }
    >({
      query: ({ restrictionId, page, size }) => {
        const queryParams = new URLSearchParams();

        queryParams.append("restrictionId", restrictionId.toString());
        queryParams.append("page", page.toString());
        queryParams.append("size", size.toString());

        return `/api/v1/restriction/providers?${queryParams.toString()}`;
      },
      providesTags: (result, error, { restrictionId }) => [
        { type: "RestrictionProviders", id: restrictionId },
      ],
    }),

    // Remove providers from a restriction - PUT /api/v1/restriction/remove/{restrictionId}/providers
    removeRestrictionProviders: builder.mutation<
      RemoveRestrictionProvidersResponse,
      { restrictionId: number; providerIds: number[] }
    >({
      query: ({ restrictionId, providerIds }) => ({
        url: `/api/v1/restriction/remove/${restrictionId}/providers`,
        method: HttpMethod.PUT,
        body: { providerIds },
      }),
      onQueryStarted: async ({ restrictionId }, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        setTimeout(() => {
          dispatch(api.util.invalidateTags([{ type: "RestrictionProviders", id: restrictionId }]));
        }, REFRESH_DELAY);
      },
    }),

    // Add providers to a restriction - POST /api/v1/restriction/add/providers
    addRestrictionProviders: builder.mutation<
      AddRestrictionProvidersResponse,
      { restrictionId: number; providerIds: number[] }
    >({
      query: ({ restrictionId, providerIds }) => ({
        url: `/api/v1/restriction/add/providers`,
        method: HttpMethod.POST,
        body: { restrictionId, providerIds },
      }),
      onQueryStarted: async ({ restrictionId }, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        setTimeout(() => {
          dispatch(api.util.invalidateTags([{ type: "RestrictionProviders", id: restrictionId }]));
        }, REFRESH_DELAY);
      },
    }),

    // Get users by payer - GET /api/v1/membership/user/payer/{payerId}
    getUsersByPayer: builder.query<UsersResponse, { payerId: number }>({
      query: ({ payerId }) => `/api/v1/membership/user/payer/${payerId}`,
      providesTags: ["Users"],
    }),

    // Get beneficiaries by various filters - GET /api/v1/membership/report/beneficiaries/{page}/{size}
    searchPayerBeneficiaries: builder.query<
      BeneficiariesResponse,
      FilterBeneficiaries & Pagination
    >({
      query: ({ page = 1, size = 10, ...rest }) => {
        return {
          url: `/api/v1/membership/report/beneficiaries/${page}/${size}`,
          method: HttpMethod.POST,
          body: rest,
        };
      },
      providesTags: ["Beneficiaries"],
    }),

    // Export beneficiaries by various filters - POST /api/v1/membership/report/beneficiaries/{page}/{size}/export/{fileType}
    exportBeneficiaries: builder.mutation<
      Record<string, never> | null,
      FilterBeneficiaries & Pagination & { fileType: ExportFileType }
    >({
      queryFn: async ({ fileType, page = 1, size = 10, ...rest }, api, extraOptions, baseQuery) => {
        const result = await baseQuery({
          url: `/api/v1/membership/report/beneficiaries/${page}/${size}/export/${fileType}`,
          method: HttpMethod.POST,
          body: rest,
          responseHandler: async (response) => {
            return await response.blob();
          },
        });

        const fileName = `membership-${dateFileName(new Date())}.${fileType.toLowerCase()}`;

        downloadFile(result.data as Blob, fileName);

        return { data: null };
      },
    }),

    // Get a payer's plans - GET {{baseUrl}}/api/v1/membership/payer/{{payerId}}/plans
    getPayerPlans: builder.query<Plan[], { payerId: number }>({
      query: ({ payerId }) => `/api/v1/membership/payer/${payerId}/plans`,
      providesTags: ["Plans"],
      transformResponse: (response: PlansResponse) => response.data,
    }),

    // Get a plans's policies - GET {{baseUrl}}/api/v1/membership/plan/{{planId}}/policies
    getPlanPolicies: builder.query<Policy[], { planId: number }>({
      query: ({ planId }) => `/api/v1/membership/plan/${planId}/policies`,
      providesTags: ["Policies"],
      transformResponse: (response: PoliciesResponse) => response.data,
    }),

    // Get a payer's policies - GET {{baseUrl}}/api/v1/membership/payer/{{payerId}}/policies
    getPayerPolicies: builder.query<EnhancedPolicy[], { payerId: number }>({
      query: ({ payerId }) => `/api/v1/membership/payer/${payerId}/policies`,
      providesTags: ["Policies"],
      transformResponse: (response: EnhancedPoliciesResponse) => response.data,
    }),

    // Get all categories for a policy - GET {{baseUrl}}/api/v1/membership/policy/{{policyId}}/categories
    getPolicyCategories: builder.query<Category[], { policyId: number }>({
      query: ({ policyId }) => `/api/v1/membership/policy/${policyId}/categories`,
      providesTags: ["Categories"],
      transformResponse: (response: CategoriesResponse) => response.data,
    }),

    // Get all categories for the given policies - GET /api/v1/membership/policy/categories/findByPolicyIds
    getPoliciesCategories: builder.query<Category[], { policyIds: number[] }>({
      query: ({ policyIds }) => ({
        url: `/api/v1/membership/policy/categories/findByPolicyIds`,
        method: HttpMethod.POST,
        body: { ids: policyIds },
      }),
      providesTags: ["Categories"],
      transformResponse: (response: CategoriesResponse) => response.data,
    }),

    getPoliciesCategoriesByPlanId: builder.query<Category[], { policyIds: number[] }>({
      query: ({ policyIds }) => ({
        url: `/api/v1/membership/policy/categories/findByPlanIds`,
        method: HttpMethod.POST,
        body: { ids: policyIds },
      }),
      providesTags: ["Categories"],
      transformResponse: (response: CategoriesResponse) => response.data,
    }),

    // Get all beneficiaries for a category - GET {{baseUrl}}/api/v1/membership/category/{{categoryId}}/beneficiaries?page=1&size=100
    getCategoryBeneficiaries: builder.query<
      BeneficiariesResponse,
      { categoryId: number; page: number; size: number }
    >({
      query: ({ categoryId, page, size }) => {
        const queryParams = new URLSearchParams();

        queryParams.append("page", page.toString());
        queryParams.append("size", size.toString());

        return `/api/v1/membership/category/${categoryId}/beneficiaries?${queryParams.toString()}`;
      },
      providesTags: ["Beneficiaries"],
    }),

    // Get and filter claims - POST /api/v1/claim/report/filter/{page}/{size}
    getClaims: builder.query<ClaimsResponse, FilterVisits & { page: number; size: number }>({
      query: ({ page = 1, size = 10, ...rest }) => {
        return {
          url: `/api/v1/claim/report/filter/${page}/${size}`,
          method: HttpMethod.POST,
          body: rest,
        };
      },
      providesTags: ["Claims"],
    }),

    //Get invoices
    getInvoices: builder.query<ClaimsResponse, FilterInvoices & { page: number; size: number }>({
      query: ({ page = 1, size = 10, ...rest }) => {
        return {
          url: `/api/v1/visit/invoices/search/${page}/${size}`,
          method: HttpMethod.POST,
          body: rest,
        };
      },
      providesTags: ["Claims"],
    }),

    // Filter and export claims - POST /api/v1/claim/report/export/{fileType}/{page}/{size}
    exportClaims: builder.mutation<
      Record<string, never> | null,
      FilterVisits & {
        fileType: ExportFileType;
        reportType: ClaimsReportType;
        page: number;
        size: number;
      }
    >({
      queryFn: async ({ fileType, page = 1, size = 10, ...rest }, api, extraOptions, baseQuery) => {
        const result = await baseQuery({
          url: `/api/v1/claim/report/export/${fileType}/${page}/${size}`,
          method: HttpMethod.POST,
          body: rest,
          responseHandler: async (response) => {
            return await response.blob();
          },
        });

        let fileName = ``;

        if (!rest.fromDate && !rest.toDate) {
          fileName = `${String(rest.reportType)}-${dateFileName(new Date())}.${fileType.toLowerCase()}`;
        } else {
          fileName = `${String(rest.reportType)} from ${String(rest.fromDate)} to ${String(rest.toDate)}`;
        }

        downloadFile(result.data as Blob, fileName);

        return { data: null };
      },
    }),

    exportInvoices: builder.mutation<
      Record<string, never> | null,
      FilterVisits & {
        fileType: ExportFileType;
        reportType: ClaimsReportType;
        page: number;
        size: number;
      }
    >({
      queryFn: async ({ fileType, page = 1, size = 10, ...rest }, api, extraOptions, baseQuery) => {
        const result = await baseQuery({
          url: `/api/v1/visit/invoices/export/${fileType}`,
          method: HttpMethod.POST,
          body: rest,
          responseHandler: async (response) => {
            return await response.blob();
          },
        });

        let fileName = ``;

        if (!rest.startDate && !rest.endDate) {
          fileName = `${String(rest.reportType)}-${dateFileName(new Date())}.${fileType.toLowerCase()}`;
        } else {
          fileName = `${String(rest.reportType)} from ${String(rest.startDate)} to ${String(rest.endDate)}.${fileType.toLowerCase()}`;
        }

        downloadFile(result.data as Blob, fileName);

        return { data: null };
      },
    }),

    // Mark claims as sent by visit number - POST /api/v1/visit/sendKengenUnattached
    sendKengenClaims: builder.mutation<
      Record<string, never>,
      { invoiceIds: number[]; actionedBy: string }
    >({
      query: ({ invoiceIds, actionedBy }) => ({
        url: `/api/v1/visit/sendKengenUnattached/v2`,
        method: HttpMethod.POST,
        body: {
          invoiceIds,
          actionedBy,
        },
      }),
      onQueryStarted: async (_, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        setTimeout(() => {
          dispatch(api.util.invalidateTags(["Claims"]));
        }, REFRESH_DELAY);
      },
    }),

    // GET /api/v1/provider/search
    searchProvidersByType: builder.query<
      ProvidersResponse,
      {
        type?: SearchProviderFilter;
        query?: string;
        mainFacilityId?: number;
      } & Pagination
    >({
      query: ({ type = SearchProviderFilter.ALL, query, mainFacilityId, page = 1, size = 10 }) => ({
        url: `/api/v1/provider/search`,
        params: {
          searchType: type,
          ...(query && { providerName: query }),
          ...(mainFacilityId && { mainFacilityId }),
          page,
          size,
        },
      }),
      providesTags: ["Providers"],
    }),

    // Retuns a single beneficiary - GET /api/v1/membership/beneficiaries/{beneficiaryId}
    getBeneficiary: builder.query<
      Beneficiary | undefined,
      {
        beneficiaryId: number;
      }
    >({
      query: ({ beneficiaryId }) => `/api/v1/membership/beneficiaries/${beneficiaryId}`,
      transformResponse: (response: BeneficiaryListResponse) => response.data[0],
      providesTags: (result, error, { beneficiaryId }) => [
        { type: "Beneficiaries" as const, id: beneficiaryId },
      ],
    }),

    // Register a device - POST /api/v1/device/register
    registerDevice: builder.mutation<
      SimpleResponse<true>,
      {
        deviceId: string;
        imei: string[];
        description: string;
        registeredByUser: string;
        modelId: number;
        accessories: Array<{
          accessory: string;
          note: string;
        }>;
      }
    >({
      query: (body) => ({
        url: `/api/v1/device/register`,
        method: HttpMethod.POST,
        body,
      }),
      onQueryStarted: async (_, { dispatch, queryFulfilled }) => {
        try {
          await queryFulfilled;
          setTimeout(() => {
            dispatch(api.util.invalidateTags(["Devices"]));
          }, REFRESH_DELAY);
        } catch (error) {
          if (import.meta.env.DEV) {
            console.error(error);
          }
        }
      },
    }),

    // Get unallocated devices - GET /api/v1/device/unallocated
    getUnallocatedDevices: builder.query<
      DevicesResponse,
      {
        page?: number;
        size?: number;
      }
    >({
      query: ({ page = 1, size = 10 }) => ({
        url: `/api/v1/device/unallocated`,
        params: { page, size },
      }),
      providesTags: ["Devices"],
    }),

    // Get all devices - GET /api/v1/device/catalog
    getDevices: builder.query<
      DevicesResponse,
      {
        page?: number;
        size?: number;
      }
    >({
      query: ({ page = 1, size = 10 }) => ({
        url: `/api/v1/device/catalog`,
        params: { page, size },
      }),
      providesTags: ["Devices"],
    }),

    // Get device allocation - GET /api/v1/device/allocation
    getDeviceAllocation: builder.query<
      DeviceAllocationResponse,
      {
        providerId: number;
        deviceId: string;
      }
    >({
      query: ({ providerId, deviceId }) => ({
        url: `/api/v1/device/allocation`,
        params: { providerId, deviceId },
      }),
      providesTags: ["DeviceAllocations"],
    }),

    // Get device models - GET /api/v1/device/models/all
    getDeviceModels: builder.query<
      DeviceModelResponse,
      {
        page?: number;
        size?: number;
      }
    >({
      query: ({ page = 1, size = 100 }) => ({
        url: `/api/v1/device/models/all`,
        params: { page, size },
      }),
      providesTags: ["DeviceModels"],
    }),

    // Add a model - POST /api/v1/device/model
    addDeviceModel: builder.mutation<
      SimpleResponse<true>,
      {
        model: string;
        description: string;
      }
    >({
      query: (body) => ({
        url: `/api/v1/device/model`,
        method: HttpMethod.POST,
        body,
      }),
      onQueryStarted: async (_, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        setTimeout(() => {
          dispatch(api.util.invalidateTags(["DeviceModels"]));
        }, REFRESH_DELAY);
      },
    }),

    // Get all SIMs - GET /api/v1/device/sim/all
    getSims: builder.query<
      SimsResponse,
      {
        page?: number;
        size?: number;
      }
    >({
      query: ({ page = 1, size = 100 }) => ({
        url: `/api/v1/device/sim/all`,
        params: { page, size },
      }),
      providesTags: ["Sims"],
    }),

    // Add a SIM - POST /api/v1/device/sim
    addSim: builder.mutation<
      SimpleResponse<true>,
      {
        simNumber: string;
        registeredByUser: string;
      }
    >({
      query: (body) => ({
        url: `/api/v1/device/sim`,
        method: HttpMethod.POST,
        body,
      }),
      onQueryStarted: async (_arg, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        setTimeout(() => {
          dispatch(api.util.invalidateTags(["Sims"]));
        }, REFRESH_DELAY);
      },
    }),

    // Search for a SIM - GET /api/v1/device/sim/search
    searchSim: builder.query<
      SimsResponse,
      {
        simStatus: DeviceSimStatus;
        simNumber: string;
        page?: number;
        size?: number;
      }
    >({
      query: ({ simNumber, simStatus, page = 1, size = 100 }) => ({
        url: `/api/v1/device/sim/search`,
        params: { simNumber, page, size, simStatus },
      }),
      // providesTags: ["Sims"],
    }),

    // Allocate a device to a provider - POST /api/v1/device/allocate
    allocateDevice: builder.mutation<AllocateDeviceResponse, AllocateDeviceRequest>({
      query: (body) => ({
        url: `/api/v1/device/allocate`,
        method: HttpMethod.POST,
        body,
      }),
      onQueryStarted: async (_arg, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        setTimeout(() => {
          dispatch(api.util.invalidateTags(["Devices"]));
        }, REFRESH_DELAY);
      },
    }),

    // Add allocation documents urls - POST /api/v1/device/allocation/uploadDocuments
    addAllocationDocuments: builder.mutation<
      SimpleResponse<true>,
      {
        allocationId: number;
        uploadedBy: string;
        supportingDocuments: Array<string>;
      }
    >({
      query: (body) => ({
        url: `/api/v1/device/allocation/uploadDocuments`,
        method: HttpMethod.POST,
        body,
      }),
      onQueryStarted: async (_arg, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        setTimeout(() => {
          dispatch(api.util.invalidateTags(["Devices"]));
        }, REFRESH_DELAY);
      },
    }),

    // Edit device status - PATCH /api/v1/device/{deviceCatalogId}
    patchDevice: builder.mutation<SimpleResponse<true>, PatchDeviceRequest>({
      query: ({ deviceStatus, updatedBy, deviceCatalogId }) => ({
        url: `/api/v1/device/${deviceCatalogId}`,
        method: HttpMethod.PATCH,
        body: { deviceStatus, updatedBy },
      }),
      onQueryStarted: async (_arg, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        setTimeout(() => {
          dispatch(api.util.invalidateTags(["Devices"]));
        }, REFRESH_DELAY);
      },
    }),

    // Search devices - GET /api/v1/device/search
    searchDevice: builder.query<
      DevicesResponse,
      {
        deviceId?: string;
        deviceStatus?: DeviceStatus;
        imei?: string;
      } & Pagination
    >({
      query: (params) => ({
        url: `/api/v1/device/search`,
        params,
      }),
      providesTags: ["Devices"],
    }),

    // Export member statement - GET /api/v1/visit/statement/member
    exportMemberStatement: builder.mutation<
      Record<string, never> | null,
      {
        statementType: StatementType;
        payerId: number;
        beneficiaryId: number;
        fileType: "PDF" | "XLSX";
      }
    >({
      queryFn: async (
        { payerId, beneficiaryId, statementType, fileType },
        api,
        extraOptions,
        baseQuery,
      ) => {
        const result = await baseQuery({
          url: `/api/v1/visit/statement/member`,
          params: {
            payerId,
            beneficiaryId,
            statementType,
            fileType,
          },
          responseHandler: async (response) => {
            return await response.blob();
          },
        });

        const fileName = `member-statement-${statementType.toLowerCase()}-${dateFileName(
          new Date(),
        )}`;

        downloadFile(result.data as Blob, fileName);

        return { data: null };
      },
    }),

    // Bill a visit - POST /api/v1/visit/saveBillAndCloseVisitPortal
    billVisit: builder.mutation<SaveBillAndCloseVisitResponse, BillVisitRequest>({
      query: (body) => ({
        url: `/api/v1/visit/saveBillAndCloseVisitPortal`,
        method: HttpMethod.POST,
        body,
      }),
      onQueryStarted: async ({ id }, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        await sleep(REFRESH_DELAY);
        dispatch(api.util.invalidateTags([{ type: "Visits", id }]));
      },
    }),

    // Dispatch a visit - POST /api/v1/visit/saveLineItem
    dispatchVisit: builder.mutation<DispatchVisitResponse, DispatchVisitRequest & { id: number }>({
      query: ({ id: _id, ...body }) => ({
        url: `/api/v1/visit/saveLineItem`,
        method: HttpMethod.POST,
        body,
      }),
      onQueryStarted: async ({ id }, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        await sleep(REFRESH_DELAY);
        dispatch(api.util.invalidateTags([{ type: "Visits", id }])); // Invoice is fetched through visit
      },
    }),

    // Start a draft visit - POST /api/v1/visit/startDraftVisit
    addDraftVisit: builder.mutation<DraftVisitResponse, DraftVisitRequest>({
      query: (body) => ({
        url: `/api/v1/visit/startDraftVisit`,
        method: HttpMethod.POST,
        body,
      }),
    }),

    // Get all beneficiary benefits with provider restriction checks - GET /api/v1/visit/benefits/all/{beneficiaryId}/{providerId}
    getBeneficiaryProviderBenefits: builder.query<
      BeneficiaryProviderBenefitsResponse,
      { beneficiaryId: number; providerId: number }
    >({
      query: ({ beneficiaryId, providerId }) =>
        `/api/v1/visit/benefits/all/${beneficiaryId}/${providerId}`,
      providesTags: (result, error, { beneficiaryId }) => [
        { type: "BeneficiaryBenefits", id: beneficiaryId },
      ],
    }),

    // Search for beneficiaries - GET /api/v1/membership/beneficiaries/v2
    searchBeneficiaries: builder.query<
      SearchBeneficiariesResponse,
      { query: string; providerId: number }
    >({
      query: ({ query, providerId }) => ({
        url: `/api/v1/membership/beneficiaries/v2`,
        params: { search: query, providerId },
      }),
      providesTags: ["Beneficiaries"],
    }),

    // Search for payer beneficiaries - GET /api/v1/membership/beneficiaries/v2
    searchBeneficiariesByPayer: builder.query<
      SearchBeneficiariesResponse,
      { query: string; payerId: number }
    >({
      query: ({ query, payerId }) => ({
        url: `/api/v1/membership/memberSearchPayerInquiry`,
        params: { search: query, payerId },
      }),
      providesTags: ["Beneficiaries"],
    }),

    // Get beneficiary - GET /api/v1/membership/beneficiary/beneficiaryData/{beneficiaryId}
    getBeneficiaryBasics: builder.query<
      BeneficiaryBasicsResponse,
      {
        beneficiaryId: number;
      }
    >({
      query: ({ beneficiaryId }) => ({
        url: `/api/v1/membership/beneficiary/beneficiaryData/${beneficiaryId}`,
      }),
      providesTags: (result, error, { beneficiaryId }) => [
        { type: "Beneficiaries", id: beneficiaryId },
      ],
    }),

    // Request OTP - /api/v1/notification/otp
    requestOtp: builder.mutation<true, OTPRequest>({
      query: (body) => ({
        url: `/api/v1/notification/otpPortal`,
        method: HttpMethod.POST,
        body,
      }),
    }),

    // Verify OTP - /api/v1/notification/verify
    verifyOtp: builder.mutation<SimpleResponse<string>, VerifyOTPRequest>({
      query: (body) => ({
        url: `/api/v1/notification/otp`,
        method: HttpMethod.PUT,
        body,
      }),
    }),

    // Reactivate a visit - /api/v1/visit/activate/{visitId}
    activateVisit: builder.mutation<
      SimpleResponse<Visit>,
      {
        visitId: number;
      }
    >({
      query: ({ visitId }) => ({
        url: `/api/v1/visit/activate/${visitId}`,
        method: HttpMethod.PATCH,
      }),
      invalidatesTags: (result, error, { visitId }) => [{ type: "Visits", id: visitId }],
    }),

    // Search visits - /api/v1/visit/search
    searchVisits: builder.query<PagedVisitsResponse, SearchVisitsRequest & Pagination>({
      query: (params) => ({
        url: `/api/v1/visit/search`,
        params,
      }),
      providesTags: (result, _error, _arg) =>
        result?.data.content
          ? [...result.data.content.map(({ id }) => ({ type: "Visits" as const, id })), "Visits"]
          : ["Visits"],
    }),

    // Search provider claims - /api/v1/visit/claims/search
    searchClaims: builder.query<ProviderClaimsResponse, SearchProviderClaimsRequest & Pagination>({
      query: (params) => ({
        url: `/api/v1/visit/claims/search`,
        params,
      }),
      providesTags: (result, _error, _arg) =>
        result?.data.content
          ? [
              ...result.data.content.map(({ invoiceId }) => ({
                type: "Claims" as const,
                id: invoiceId || 0,
              })),
              "Claims",
            ]
          : ["Claims"],
    }),

    // Batch update beneficiaries - /api/v1/membership/beneficiaries/edit
    batchUpdateBeneficiaries: builder.mutation<
      BatchUpdateBeneficiariesResponse,
      BatchUpdateBeneficiariesQuery & { file: Blob }
    >({
      query: ({ file, ...params }) => {
        const body = new FormData();
        body.append("file", file);

        return {
          url: `/api/v1/membership/beneficiaries/edit`,
          params,
          body,
          method: HttpMethod.POST,
        };
      },
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        setTimeout(() => {
          dispatch(api.util.invalidateTags(["Beneficiaries"]));
        }, REFRESH_DELAY);
      },
    }),

    // Batch update beneficiary status - /api/v1/membership/beneficiaries/status/updateByTemplate
    batchUpdateBeneficiaryStatus: builder.mutation<
      BatchUpdateBeneficiaryStatusResponse,
      BatchUpdateBeneficiaryStatusQuery & { file: Blob }
    >({
      query: ({ file, ...params }) => {
        const body = new FormData();
        body.append("file", file);

        return {
          url: `/api/v1/membership/beneficiaries/status/updateByTemplate`,
          params,
          body,
          method: HttpMethod.POST,
        };
      },
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        setTimeout(() => {
          dispatch(api.util.invalidateTags(["Beneficiaries"]));
        }, REFRESH_DELAY);
      },
    }),

    // PUT /api/v1/membership/beneficiary/update/{beneficiaryId} - Update beneficiary
    updateMember: builder.mutation<
      SimpleResponse<true>,
      UpdateMemberRequest & {
        id: number;
      }
    >({
      query: ({ id, ...body }) => ({
        url: `/api/v1/membership/beneficiary/update/${id}`,
        method: HttpMethod.PUT,
        body,
      }),
      onQueryStarted: async ({ id }, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        setTimeout(() => {
          dispatch(api.util.invalidateTags([{ type: "Beneficiaries", id }]));
        }, REFRESH_DELAY);
      },
    }),

    // POST - /visit/startVisit
    startVisit: builder.mutation<MutationResponse, StartVisitRequest>({
      query: (body) => ({
        url: `/api/v1/visit/startVisit`,
        method: HttpMethod.POST,
        body,
      }),
      invalidatesTags: ["Visits"],
    }),

    // Get member registered biometrics - GET /api/v1/biometric/retrieveByMemberNumberAndBioStatus
    getBeneficiaryBiometrics: builder.query<
      GetMemberBiometricsResponse,
      GetMemberBiometricsRequest
    >({
      query: (params) => ({
        url: `/api/v1/biometric/retrieveByMemberNumberAndBioStatus`,
        params,
      }),
      providesTags: (result, _error, arg) =>
        result?.data ? [{ type: "Biometrics" as const, id: arg.memberNumber }] : ["Biometrics"],
    }),

    getBeneficiaryFamily: builder.query<BeneficiaryFamilyResponse, { id: number }>({
      query: ({ id }) => ({
        url: `/api/v1/membership/family`,
        params: {
          beneficiaryId: id,
        },
      }),
      providesTags: (result, _error, arg) =>
        result?.data
          ? [
              ...result.data.map(({ id }) => ({ type: "Beneficiaries" as const, id })),
              { type: "BeneficiaryFamily", id: arg.id },
            ]
          : [{ type: "BeneficiaryFamily", id: arg.id }],
    }),

    // POST /api/v1/membership/deactivateBeneficiary/{beneficiaryId} - deactivate beneficiary
    deactivateBeneficiary: builder.mutation<BeneficiaryResponse, { id: number }>({
      query: ({ id }) => ({
        url: `/api/v1/membership/deactivateBeneficiary/${id}`,
        method: HttpMethod.POST,
      }),
      onQueryStarted: async ({ id }, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        await sleep(REFRESH_DELAY);
        dispatch(api.util.invalidateTags([{ type: "Beneficiaries", id }]));
      },
    }),

    // PATCH /api/v1/membership/suspend/{beneficiaryId} - deactivate beneficiary
    suspendBeneficiary: builder.mutation<BeneficiaryResponse, { id: number }>({
      query: ({ id }) => ({
        url: `/api/v1/membership/suspend/${id}`,
        method: HttpMethod.PATCH,
      }),
      onQueryStarted: async ({ id }, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        await sleep(REFRESH_DELAY);
        dispatch(api.util.invalidateTags([{ type: "Beneficiaries", id }]));
      },
    }),

    // POST /api/v1/membership/activateBeneficiary/{beneficiaryId} - activate beneficiary
    activateBeneficiary: builder.mutation<BeneficiaryResponse, { id: number }>({
      query: ({ id }) => ({
        url: `/api/v1/membership/activateBeneficiary/${id}`,
        method: HttpMethod.POST,
      }),
      onQueryStarted: async ({ id }, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        await sleep(REFRESH_DELAY);
        dispatch(api.util.invalidateTags([{ type: "Beneficiaries", id }]));
      },
    }),

    // PUT /api/v1/biometric/update - detach biometrics
    detachBiometrics: builder.mutation<SimpleResponse<true>, { memberNumber: string }>({
      query: (params) => ({
        url: `/api/v1/biometric/update`,
        method: HttpMethod.PUT,
        params,
      }),
      onQueryStarted: async ({ memberNumber }, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        await sleep(REFRESH_DELAY);
        dispatch(api.util.invalidateTags([{ type: "Biometrics", id: memberNumber }]));
      },
    }),

    // PATCH /api/v1/visit/benefits/:id/activate - activate benefit
    activateBenefit: builder.mutation<
      SimpleResponse<true>,
      ActivateBenefitRequest & { benefitId: number; beneficiaryId: number }
    >({
      query: ({ benefitId: id, beneficiaryId: _beneficiaryId, ...body }) => ({
        url: `/api/v1/visit/benefits/${id}/activate`,
        method: HttpMethod.PATCH,
        body,
      }),
      onQueryStarted: async ({ beneficiaryId: id }, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        await sleep(REFRESH_DELAY);
        dispatch(api.util.invalidateTags([{ type: "BeneficiaryBenefits", id }]));
      },
    }),

    // PATCH /api/v1/visit/benefits/:id/activate - activate benefit
    suspendBenefit: builder.mutation<
      SimpleResponse<true>,
      ActivateBenefitRequest & { benefitId: number; beneficiaryId: number }
    >({
      query: ({ benefitId: id, beneficiaryId: _beneficiaryId, ...body }) => ({
        url: `/api/v1/visit/benefits/${id}/suspend`,
        method: HttpMethod.PATCH,
        body,
      }),
      onQueryStarted: async ({ beneficiaryId: id }, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        await sleep(REFRESH_DELAY);
        dispatch(api.util.invalidateTags([{ type: "BeneficiaryBenefits", id }]));
      },
    }),

    // POST /api/v1/visit/topUpBenefit - top up benefit
    topUpBenefit: builder.mutation<SimpleResponse<true>, TopUpBenefitRequest>({
      query: (body) => ({
        url: `/api/v1/benefit/topUpBenefit`,
        method: HttpMethod.POST,
        body,
      }),
      onQueryStarted: async ({ beneficiaryId: id }, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        await sleep(REFRESH_DELAY);
        dispatch(api.util.invalidateTags([{ type: "BeneficiaryBenefits", id }]));
      },
    }),

    // POST /api/v1/visit/transferBenefit - top up benefit
    transferBenefitBalance: builder.mutation<SimpleResponse<true>, TransferBenefitBalanceRequest>({
      query: (body) => ({
        url: `/api/v1/benefit/transferBenefit`,
        method: HttpMethod.POST,
        body,
      }),
      onQueryStarted: async ({ beneficiaryId: id }, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        await sleep(REFRESH_DELAY);
        dispatch(api.util.invalidateTags([{ type: "BeneficiaryBenefits", id }]));
      },
    }),

    // GET /api/v1/membership/payers/{payerId}/{page}/{size}/mapping/providers
    getPayerMappedProviders: builder.query<
      PayerProviderMappingResponse,
      { payerId: number; page: number; size: number }
    >({
      query: ({ payerId, page, size }) => ({
        url: `/api/v1/membership/payers/${payerId}/${page}/${size}/mapping/providers`,
      }),
      providesTags: (_result, _error, { payerId }) => [{ type: "PayerProviders", id: payerId }],
    }),

    // GET /api/v1/membership/payers/{payerId}/providers
    filterPayerProviders: builder.query<ProvidersResponse, FilterPayerProviders & Pagination>({
      query: ({ payerId, ...params }) => ({
        url: `/api/v1/membership/payers/${payerId}/providers`,
        params,
      }),
      providesTags: (_result, _error, { payerId }) => [{ type: "PayerProviders", id: payerId }],
    }),

    // Search visits - /api/v1/visit/search
    searchClaimVetting: builder.query<PagedVisitsResponse, SearchClaimVettingRequest & Pagination>({
      query: (params) => ({
        url: `/api/v1/visit/search`,
        params,
      }),
      providesTags: (result, _error, _arg) =>
        result?.data.content
          ? [
              ...result.data.content.map(({ id }) => ({ type: "VisitsToVet" as const, id })),
              "VisitsToVet",
            ]
          : ["VisitsToVet"],
    }),

    // Get beneficiary - GET /api/v1/membership/beneficiary/beneficiaryData/{beneficiaryId}
    getPlansByPayer: builder.query<
      PlansResponse,
      {
        payerId: number;
      }
    >({
      query: ({ payerId }) => ({
        url: `/api/v1/membership/payer/${payerId}/plans`,
      }),
      providesTags: (result, error, { payerId }) => [{ type: "Plans", id: payerId }],
    }),

    // Change beneficiaries' categories - POST /api/v1/membership/category/change
    changeBeneficiaryCategory: builder.mutation<SimpleResponse<true>, CategoryChangeRequest>({
      query: (body) => ({
        url: `/api/v1/membership/category/change`,
        method: HttpMethod.POST,
        body,
      }),
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        await sleep(REFRESH_DELAY);
        dispatch(
          api.util.invalidateTags(
            arg.beneficiaryIds.map((id) => ({ type: "Beneficiaries" as const, id })),
          ),
        );
      },
    }),

    // Update beneficiaries' statuses - POST /api/v1/membership/beneficiaries/status/update
    updateBeneficiaryStatus: builder.mutation<SimpleResponse<true>, BeneficiaryStatusUpdateRequest>(
      {
        query: (body) => ({
          url: `/api/v1/membership/beneficiaries/status/update`,
          method: HttpMethod.POST,
          body,
        }),
        onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
          await queryFulfilled;
          await sleep(REFRESH_DELAY);
          dispatch(
            api.util.invalidateTags([
              ...arg.beneficiaryIds.map((id) => ({ type: "Beneficiaries" as const, id })),
              ...arg.beneficiaryIds.map((id) => ({ type: "BeneficiaryFamily" as const, id })),
            ]),
          );
        },
      },
    ),

    // Update benefit status - POST /api/v1/membership/benefits/status/update
    updateBenefitStatus: builder.mutation<SimpleResponse<true>, UpdateBenefitStatusRequest>({
      query: (body) => ({
        url: `/api/v1/membership/benefits/status/update`,
        method: HttpMethod.POST,
        body,
      }),
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        await sleep(REFRESH_DELAY);
        dispatch(
          api.util.invalidateTags(
            arg.benefitIds.map((id) => ({ type: "BeneficiaryBenefits" as const, id })),
          ),
        );
      },
    }),

    // Update beneficiary benefit status - POST /api/v1/membership/benefits/status/update
    updateBeneficiaryBenefitStatus: builder.mutation<
      SimpleResponse<true>,
      BeneficiaryBenefitStatusUpdateRequest
    >({
      query: (body) => ({
        url: `/api/v1/benefit/status/update`,
        method: HttpMethod.POST,
        body,
      }),
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        await sleep(REFRESH_DELAY);
        dispatch(
          api.util.invalidateTags(
            arg.updateList.map(({ beneficiaryId }) => ({
              type: "BeneficiaryBenefits" as const,
              id: beneficiaryId,
            })),
          ),
        );
      },
    }),

    // POST /api/v1/visit/saveMultipleStationsLineItemsPortal
    patchInvoice: builder.mutation<SimpleResponse<true>, PatchInvoiceRequest>({
      query: (body) => ({
        url: `/api/v1/visit/saveMultipleStationsLineItemsPortal`,
        method: HttpMethod.POST,
        body,
      }),
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        await sleep(REFRESH_DELAY);
        // Invoice is fetched through visit
        dispatch(api.util.invalidateTags([{ type: "Visits", id: arg.visitId }]));
      },
    }),

    //return memeber cover periods//<CoverResponse,{planId:number,memberNo:string}>
    getCoverPeriods: builder.query({
      query: ({ planId, memberNo }) =>
        `/api/v1/membership/cover/periods?planId=${planId}&memberNo=${memberNo}`,
    }),

    // PUT - /api/v1/preauthorization/reactivate/{preAuthId}
    reactivatePreauth: builder.mutation<PreAuthResponse, ReactivatePreauthRequest & { id: number }>(
      {
        query: ({ id, ...body }) => ({
          url: `/api/v1/preauthorization/reactivate/${id}`,
          method: HttpMethod.PUT,
          body,
        }),
        onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
          await queryFulfilled;
          await sleep(REFRESH_DELAY);
          dispatch(api.util.invalidateTags([{ type: "PreAuths", id: arg.id }]));
        },
      },
    ),
    // PUT - /api/v1/preauthorization/withdraw/{preAuthId}
    withdrawPreauth: builder.mutation<
      PreAuthResponse,
      {
        actionBy: string;
        reason: string;
      } & { id: number }
    >({
      query: ({ id, ...body }) => ({
        url: `/api/v1/preauthorization/withdraw/${id}`,
        method: HttpMethod.PUT,
        body,
      }),
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        await sleep(REFRESH_DELAY);
        dispatch(api.util.invalidateTags([{ type: "PreAuths", id: arg.id }]));
      },
    }),

    //download documents
    documentDownload: builder.query({
      query: (documentId) => `/api/v1/visit/documentDownload?id=${documentId}`,
    }),

    //PUT update invoice number
    updateInvoice: builder.mutation({
      query: ({ id, invoiceNumber, reason, username }) => ({
        url: `/api/v1/visit/invoice/${id}`,
        method: "PUT",
        body: { invoiceNumber, reason, updatedBy: username },
      }),
    }),

    saveLineItem: builder.mutation({
      query: (payload) => ({
        url: "/api/v1/visit/saveLineItem",
        method: "POST",
        headers: {
          accept: "application/json",
          "Content-Type": "application/json",
        },
        body: payload,
      }),
    }),

    getPoliciesByPlanId: builder.query({
      query: (id) => `/api/v1/membership/plan/${id}/policies`,
    }),

    reverseInvoice: builder.mutation({
      query: (payload) => ({
        url: "/api/v1/visit/invoice/reversal",
        method: "POST",
        headers: {
          accept: "application/json",
          "Content-Type": "application/json",
        },
        body: payload,
      }),
    }),

    batchInvoices: builder.mutation({
      query: (payload) => ({
        url: "/api/v1/visit/batchClaimsWithPayableAmount/invoices",
        method: "POST",
        headers: {
          accept: "application/json",
          "Content-Type": "application/json",
        },
        body: payload,
      }),
    }),

    ///api/v1/visit/audit/logs
    auditLogs: builder.query({
      query: (params) => ({
        url: `/api/v1/visit/audit/logs`,
        params: { ...params }, // change body to params if it's a GET request
      }),
      providesTags: ["AuditLogs"],
    }),

    getLineItemsByInvoiceId: builder.query({
      query: (invoiceId) => ({
        url: `/api/v1/visit/lineItems/${invoiceId}`,
      }),
    }),

    preAuthorizationTopUpByPayer: builder.mutation<
      SimpleResponse<boolean>,
      PreAuthTopUpByPayerRequest
    >({
      query: ({ pathParameters, body }) => ({
        url: `/api/v1/preauthorization/topUpByPayer/${pathParameters.preAuthId}`,
        body,
        method: "PUT",
      }),
      invalidatesTags: ["PreAuths", "AuditLogs"],
    }),
    approvePreAuthTopUpMutation: builder.mutation<SimpleResponse<boolean>, ApproveTopUpRequest>({
      query: ({ pathParameters: { preAuthTopUpId }, body }) => ({
        url: `/api/v1/preauthorization/topup/authorization/${preAuthTopUpId}`,
        body,
        method: "PUT",
      }),
      invalidatesTags: ["PreAuths", "AuditLogs"],
    }),
    rejectPreAuthTopUp: builder.mutation<SimpleResponse<boolean>, RejectTopUpRequest>({
      query: ({ pathParameters: { preAuthTopUpId }, body }) => ({
        url: `/api/v1/preauthorization/topup/reject/${preAuthTopUpId}`,
        body,
        method: "PUT",
      }),
      invalidatesTags: ["PreAuths", "AuditLogs"],
    }),
    getPreAuthorizationReports: builder.mutation<ListResponse<ReportVisit>, PreAuthRePortRequest>({
      query: ({ body }) => ({
        url: "/api/v1/preauthorization/time/search",
        method: "POST",
        body,
      }),
    }),
    getEventsNotifications: builder.query<
      ListResponse<UserNotification>,
      GetPaginatedEventsNotification
    >({
      query: ({ pathParameters: { page, size }, queryParameters: { markAsRead } }) =>
        `/api/v1/events/notifications/${page}/${size}${markAsRead == false ? `?markAsRead=${markAsRead}` : ""}`,
      providesTags: ["Notifications"],
    }),
    markNotificationsAsRead: builder.mutation<
      unknown,
      {
        body: { notificationIds: number[]; markAsRead: boolean };
      }
    >({
      query: ({ body }) => ({
        url: "/api/v1/events/markAsRead",
        method: "PUT",
        body,
      }),
      invalidatesTags: ["Notifications"],
    }),
    markAllNotificationsAsRead: builder.mutation<unknown, void>({
      query: () => ({
        url: "/api/v1/events/markAllAsRead",
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
      }),
      invalidatesTags: ["Notifications"],
    }),
  }),
});

export const {
  useMarkAllNotificationsAsReadMutation,
  useMarkNotificationsAsReadMutation,
  useGetEventsNotificationsQuery,
  useGetPreAuthorizationReportsMutation,
  usePreAuthorizationTopUpByPayerMutation,
  useRejectPreAuthTopUpMutation,
  useApprovePreAuthTopUpMutationMutation,
  useGetCountriesQuery,
  useGetRegionsQuery,
  useGetBenefitMappedProvidersQuery,
  useAddBenefitMappedProvidersMutation,
  useUnmapBenefitMappedProvidersMutation,
  useGetBeneficiaryBenefitsQuery,
  useSearchBenefitProvidersQuery,
  useLazySearchBenefitProvidersQuery,
  useAddBenefitProviderMutation,
  useGetBenefitProviderQuery,
  useGetPreauthsQuery,
  useGetActiveVisitsQuery,
  useSearchDiagnosisQuery,
  useLazySearchDiagnosisQuery,
  useGetPayersQuery,
  useSearchProcedureQuery,
  useLazySearchProcedureQuery,
  useAddPreauthMutation,
  useDeletePreauthMutation,
  useSearchPreauthQuery,
  useLazySearchPreauthQuery,
  useGetPreauthQuery,
  useGetVisitQuery,
  useGetPendingPreauthsByPayerQuery,
  useApprovePreauthMutation,
  useDeclinePreauthMutation,
  useLazyDownloadFileQuery,
  useLazySearchProviderQuery,
  useCountPendingPreAuthsQuery,
  useSearchDrugQuery,
  useLazySearchDrugQuery,
  useSearchLabTestQuery,
  useLazySearchLabTestQuery,
  useDeleteRestrictionMutation,
  useGetRestrictionsQuery,
  useAddRestrictionMutation,
  useSearchProviderQuery,
  useGetRestrictionProvidersQuery,
  useRemoveRestrictionProvidersMutation,
  useAddRestrictionProvidersMutation,
  useGetProvidersQuery,
  useGetUsersByPayerQuery,
  useGetPayerPoliciesQuery,
  useGetPoliciesCategoriesQuery,
  useSearchPayerBeneficiariesQuery,
  useGetPayerPlansQuery,
  useLazySearchPayerBeneficiariesQuery,
  useExportBeneficiariesMutation,
  useGetClaimsQuery,
  useLazyGetClaimsQuery,
  useExportClaimsMutation,
  useSendKengenClaimsMutation,
  useSearchProvidersByTypeQuery,
  useLazySearchProvidersByTypeQuery,
  useGetClosedVisitsQuery,
  useGetPagedClosedVisitsQuery,
  useGetBeneficiaryQuery,
  useGetDevicesQuery,
  useRegisterDeviceMutation,
  useLazyGetDeviceModelsQuery,
  useGetDeviceModelsQuery,
  useAddDeviceModelMutation,
  useGetSimsQuery,
  useAddSimMutation,
  useLazySearchSimQuery,
  useAllocateDeviceMutation,
  useAddAllocationDocumentsMutation,
  usePatchDeviceMutation,
  useSearchDeviceQuery,
  useExportMemberStatementMutation,
  useBillVisitMutation,
  useDispatchVisitMutation,
  useAddDraftVisitMutation,
  useGetActiveAndDraftVisitsQuery,
  useGetBeneficiaryProviderBenefitsQuery,
  useSearchBeneficiariesQuery,
  useGetBeneficiaryBasicsQuery,
  useRequestOtpMutation,
  useVerifyOtpMutation,
  useEditPreauthMutation,
  useActivateVisitMutation,
  useSearchVisitsQuery,
  useBatchUpdateBeneficiariesMutation,
  useBatchUpdateBeneficiaryStatusMutation,
  useGetProviderBranchesQuery,
  useSearchClaimsQuery,
  useGetProviderQuery,
  useSearchBeneficiariesByPayerQuery,
  useStartVisitMutation,
  useGetBeneficiaryBiometricsQuery,
  useGetBeneficiaryFamilyQuery,
  useGetPolicyCategoriesQuery,
  useActivateBeneficiaryMutation,
  useDeactivateBeneficiaryMutation,
  useDetachBiometricsMutation,
  useActivateBenefitMutation,
  useSuspendBenefitMutation,
  useTopUpBenefitMutation,
  useTransferBenefitBalanceMutation,
  useSuspendBeneficiaryMutation,
  useUpdateMemberMutation,
  useGetPayerMappedProvidersQuery,
  useSearchClaimVettingQuery,
  useLazyGetPayerPlansQuery,
  useChangeBeneficiaryCategoryMutation,
  useUpdateBeneficiaryStatusMutation,
  useUpdateBeneficiaryBenefitStatusMutation,
  useFilterPayerProvidersQuery,
  usePatchInvoiceMutation,
  useGetCoverPeriodsQuery,
  useReactivatePreauthMutation,
  useWithdrawPreauthMutation,
  useDocumentDownloadQuery,
  useUpdateInvoiceMutation,
  useSaveLineItemMutation,
  useGetPoliciesCategoriesByPlanIdQuery,
  useGetPoliciesByPlanIdQuery,
  useReverseInvoiceMutation,
  useBatchInvoicesMutation,
  useAuditLogsQuery,
  useGetLineItemsByInvoiceIdQuery,
  useLazyGetInvoicesQuery,
  useGetInvoicesQuery,
  useExportInvoicesMutation,
} = api;
