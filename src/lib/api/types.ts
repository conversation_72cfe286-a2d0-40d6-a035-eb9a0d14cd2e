/**
 * WARN: Beware of unintended declaration merging
 */

import { PartialOrUndefined, Status } from "~lib/types";
import { Fields as ServiceFields } from "../service-fields";
import { ProviderClaim } from "./schema";

export type GetPaginatedEventsNotification = {
  pathParameters: { page: number; size: number };
  queryParameters: {
    markAsRead?: boolean | null;
  };
};

export enum UserNotificationCategory {
  TOP_UP = "TOP_UP",
  PRE_AUTH_REQUEST = "PRE_AUTH_REQUEST",
}

export type UserNotification = {
  id: number; //($int64)
  refId: number; //($int64)
  refStatus: "PENDING" | "REJECTED" | "AUTHORIZED" | "DRAFT" | undefined; //* update accordingly
  category: UserNotificationCategory;
  heading: string;
  body: string;
  markAsRead: boolean;
  triggeredBy: string;
  createdAt: string;
};

export enum HttpMethod {
  GET = "GET",
  POST = "POST",
  PUT = "PUT",
  DELETE = "DELETE",
  PATCH = "PATCH",
}

export enum HttpHeader {
  ContentType = "Content-Type",
  ContentDisposition = "Content-Disposition",
}

export interface Pagination {
  page: number;
  size: number;
}

export interface MutationResponse {
  success: boolean;
  msg: string;
  data: boolean;
  results: number;
}

export interface ListResponse<T> {
  success: boolean;
  msg: string | null;
  data: {
    content: Array<T>;
    empty: boolean;
    first: boolean;
    last: boolean;
    number: number;
    numberOfElements: number;
    pageable: Record<string, never>;
    size: number;
    sort: {
      empty: boolean;
      sorted: boolean;
      unsorted: boolean;
    };
    totalElements: number;
    totalPages: number;
  };
  results: unknown;
}

export interface SimpleListResponse<T> {
  success: boolean;
  msg: unknown;
  data: T[];
  results?: unknown; // null
}

export interface SimpleResponse<T> {
  success: boolean;
  msg: string;
  data?: T;
  results?: unknown;
}

export interface MappedProvider {
  id: number;
  code: string;
  providerId: number;
  providerName: string;
  tier: string;
  region: string;
  country: string;
  mapped: boolean;
}

export enum ChangeLogAction {
  CATEGORY_UPDATE = "CATEGORY_UPDATE",
  MEMBER_UPDATE = "MEMBER_UPDATE",
  MEMBERSTATUS_UPDATE = "MEMBERSTATUS_UPDATE",
  BIOMETRICS_UPDATE = "BIOMETRICS_UPDATE",
  BENEFIT_UPDATE = "BENEFIT_UPDATE",
  PREAUTH_UPDATE = "PREAUTH_UPDATE",
  NONE = "NONE",
}

/**
 * Change log actions that are relevant to the beneficiaries report
 * Excludes preauth updates
 */
export const beneficiariesReportChangeLogActions = [
  ChangeLogAction.CATEGORY_UPDATE,
  ChangeLogAction.MEMBER_UPDATE,
  ChangeLogAction.MEMBERSTATUS_UPDATE,
  ChangeLogAction.BIOMETRICS_UPDATE,
  ChangeLogAction.BENEFIT_UPDATE,
];

export const changeLogActionLabels = new Map([
  [ChangeLogAction.CATEGORY_UPDATE, "Category Update"],
  [ChangeLogAction.MEMBER_UPDATE, "Member Update"],
  [ChangeLogAction.MEMBERSTATUS_UPDATE, "Member Status Update"],
  [ChangeLogAction.BIOMETRICS_UPDATE, "Biometrics Update"],
  [ChangeLogAction.BENEFIT_UPDATE, "Benefit Update"],
  [ChangeLogAction.PREAUTH_UPDATE, "PreAuth Update"],
  [ChangeLogAction.NONE, "None"],
]);

export interface ChangeLog {
  id: number;
  /**
   * Typically the same as the action
   */
  action: ChangeLogAction;
  user: string;
  /**
   * ISO date string
   */
  time: string;
  /**
   * @example "[]"
   */
  data: string;
  organisation: string;
  /**
   * @example "CHANGE OF MEMBER NUMBER"
   */
  reason: string;
  memberNumber: string;
  type: ChangeLogAction;
}

export enum AccessMode {
  CARD = "CARD",
  CARDLESS = "CARDLESS",
  HYBRID = "HYBRID",
}

export const accessModeLabels: Record<AccessMode, string> = {
  CARD: "Card",
  CARDLESS: "Cardless",
  HYBRID: "Hybrid",
};

export enum PlanType {
  RETAIL = "RETAIL",
  SCHEME = "SCHEME",
}

export enum PlanDate {
  POLICY_DATE = "POLICY_DATE",
  MEMBER_JOIN_DATE = "MEMBER_JOIN_DATE",
}

/**
 * Also known as a Scheme
 */
export interface Plan {
  id: number;
  name: string;
  type: PlanType;
  accessMode: AccessMode;
  planDate?: PlanDate | null | undefined;
}

/**
 * A specific instance of a scheme with a definitive start and end date
 */
export interface Policy {
  id: number;
  plan: Plan;
  startDate: string;
  endDate: string;
  /**
   * @example "INACTIVE-190000048419/5"
   * @example "190000048419/5"
   */
  policyNumber: string;
}

export enum PolicyStatus {
  ACTIVE = "ACTIVE",
  EXPIRED = "EXPIRED",
}

export const policyStatusLabels = new Map([
  [PolicyStatus.ACTIVE, "Active"],
  [PolicyStatus.EXPIRED, "Expired"],
]);

export interface EnhancedPolicy extends Policy {
  status: PolicyStatus;
}

export enum CategoryStatus {
  PROCESSED = "PROCESSED",
  UNPROCESSED = "UNPROCESSED",
}

export interface Category {
  id: number;
  name: string;
  description: string;
  agakhanInsuranceCode: string | null;
  agakhanSchemeCode: string | null;
  jicSchemeCode: number | null;
  apaSchemeCode: number | null;
  policyPayerCode: number | null;
  policy: Policy;
  status: CategoryStatus;
  allowOtpVerificationFailOver: boolean;
  restrictionType: RestrictionType | null;
}

export enum BeneficiaryType {
  PRINCIPAL = "PRINCIPAL",
  SPOUSE = "SPOUSE",
  CHILD = "CHILD",
  PARENT = "PARENT",
}

export const beneficiaryTypeLabels = new Map([
  [BeneficiaryType.PRINCIPAL, "Principal"],
  [BeneficiaryType.SPOUSE, "Spouse"],
  [BeneficiaryType.CHILD, "Child"],
  [BeneficiaryType.PARENT, "Parent"],
]);

export enum BeneficiaryStatus {
  ACTIVE = "ACTIVE",
  DEACTIVATED = "DEACTIVATED",
  SUSPENDED = "SUSPENDED",
}

export const beneficiaryStatusLabels = new Map([
  [BeneficiaryStatus.ACTIVE, "Active"],
  [BeneficiaryStatus.DEACTIVATED, "Deactivated"],
  [BeneficiaryStatus.SUSPENDED, "Suspended"],
]);

export enum Gender {
  MALE = "MALE",
  FEMALE = "FEMALE",
}

export const genderLabels = new Map([
  [Gender.MALE, "Male"],
  [Gender.FEMALE, "Female"],
]);

export enum BiometricStatus {
  ACTIVE = "ACTIVE",
  DELETED = "DELETED",
}

export const biometricStatusLabels = new Map([
  [BiometricStatus.ACTIVE, "Active"],
  [BiometricStatus.DELETED, "Deleted"],
]);

export enum MemberPrivilege {
  NORMAL = "NORMAL",
  VIP = "VIP",
  VVIP = "VVIP",
}

export const memberPrivilegeLabels: Record<MemberPrivilege, string> = {
  NORMAL: "Normal",
  VIP: "VIP",
  VVIP: "VVIP",
};

export interface Beneficiary {
  id: number;
  name: string;
  memberNumber: string;
  jicEntityId: number | null;
  apaEntityId: number | null;
  nhifNumber: string;
  dob: string;
  privilege: MemberPrivilege | null;
  gender: Gender;
  /**
   * @example "0722000000"
   * @example "254722000000"
   * @example ""
   */
  phoneNumber: string;
  email: string | null;
  beneficiaryType: BeneficiaryType;
  category: Category;
  /**
   * TODO: Verify type
   */
  principal: Beneficiary | null;
  canUseBiometrics: boolean | null;
  processed: boolean;
  changeLog: Array<ChangeLog>;
  processedTime: string;
  status: BeneficiaryStatus;
  joinDate: string;
  familySize: number;
  /**
   * @example "2023-07-25T09:36:21.511Z"
   */
  biometricCaptureDate: string;
  biometricStatus: BiometricStatus | null;
  restriction?: boolean;
  restrictionMsg?: string;
  otherNumber?: string;
}

export const beneficiaryFieldLabels: Record<keyof Beneficiary, string> = {
  id: "ID",
  name: "Name",
  memberNumber: "Member Number",
  jicEntityId: "JIC Entity ID",
  apaEntityId: "APA Entity ID",
  nhifNumber: "SHIF Number",
  dob: "DOB",
  gender: "Gender",
  phoneNumber: "Phone Number",
  privilege: "Privilege",
  email: "Email",
  beneficiaryType: "Beneficiary Type",
  category: "Category",
  principal: "Principal",
  canUseBiometrics: "Can Use Biometrics",
  processed: "Processed",
  changeLog: "Change Log",
  processedTime: "Processed Time",
  status: "Status",
  joinDate: "Join Date",
  familySize: "Family Size",
  biometricCaptureDate: "Biometric Capture Date",
  biometricStatus: "Biometric Status",
  restriction: "Restriction",
  restrictionMsg: "Restriction Message",
};

export interface BeneficiaryBasics {
  id: number;
  memberNumber: string;
  memberName: string;
  gender: Gender;
  beneficiaryType: BeneficiaryType;
  phoneNumber: string;
  status: BeneficiaryStatus;
  categoryId: number;
}

export enum ExportFileType {
  PDF = "PDF",
  XLS = "XLS",
  XLSX = "XLSX",
  CSV = "CSV",
}

export enum ExportVisitsFileType {
  PDF = "PDF",
  XLSX = "XLSX",
}

export enum BenefitStatus {
  ACTIVE = "ACTIVE",
  SUSPENDED = "SUSPENDED",
  CANCELED = "CANCELED",
  DEPLETED = "DEPLETED",
  THRESHOLD_HIT = "THRESHOLD_HIT",
}

export const benefitStatusLabels: Record<BenefitStatus, string> = {
  [BenefitStatus.ACTIVE]: "Active",
  [BenefitStatus.SUSPENDED]: "Suspended",
  [BenefitStatus.CANCELED]: "Canceled",
  [BenefitStatus.DEPLETED]: "Depleted",
  [BenefitStatus.THRESHOLD_HIT]: "Threshold Hit",
};

export interface BeneficiaryBenefit {
  id: number;
  aggregateId: string;
  benefitId: number;
  beneficiaryId: number;
  memberName: string;
  memberNumber: string;
  benefitName: string;
  status: BenefitStatus;
  balance: number;
  suspensionThreshold: number;
  initialLimit: number;
  categoryId: number;
  payerId: number;
  utilization: number;
  parent: BeneficiaryBenefit | null;
  sharing: BenefitSharing;
  startDate: string;
  endDate: string;
  gender: string;
  memberType: string;
  catalogId: number;
  jicEntityId: unknown;
  apaEntityId: unknown;
  benefitType: unknown;
  capitationType: unknown;
  capitationPeriod: unknown;
  capitationMaxVisitCount: number;
  requireBeneficiaryToSelectProvider: unknown;
  daysOfAdmissionLimit: number;
  amountPerDayLimit: unknown;
  billable: boolean | null;
  transferable: boolean;
}

export interface BenefitProviders {
  provider: Provider;
  id: number;
}

export enum ProviderTier {
  TIER_ONE = "TIER_ONE",
  TIER_TWO = "TIER_TWO",
  TIER_THREE = "TIER_THREE",
}

export const providerTierLabels: Record<ProviderTier, string> = {
  TIER_ONE: "Tier One",
  TIER_TWO: "Tier Two",
  TIER_THREE: "Tier Three",
};

export enum InvoiceNumberType {
  SAME = "SAME",
  VARIED = "VARIED",
}

export interface Provider {
  id: number;
  name: string;
  latitude: number;
  longitude: number;
  tier: ProviderTier;
  mainFacility: Provider | null;
  region: Region;
  baseUrl: string;
  billingStation: boolean;
  billsOnPortal: boolean;
  billsOnHmis: boolean;
  billsOnDevice: boolean;
  billsOnHmisAutomaticClose: boolean;
  canUseOtpVerificationFailOver: boolean;
  verificationType: string;
  invoiceNumberType: InvoiceNumberType;
  providerMiddleware: string;
  createdOn: string;
}

export enum VerificationMode {
  BIOMETRIC = "BIOMETRIC",
  OTP = "OTP",
}

export const verificationModeLabels = new Map<VerificationMode, string>([
  [VerificationMode.BIOMETRIC, "Biometric"],
  [VerificationMode.OTP, "OTP"],
]);

export interface Country {
  id: number;
  name: string;
}

export interface Region {
  id: number;
  name: string;
  country: Country;
}

export enum BenefitSharing {
  INDIVIDUAL = "INDIVIDUAL",
  FAMILY = "FAMILY",
}

export const benefitSharingLabels: Record<BenefitSharing, string> = {
  INDIVIDUAL: "Individual",
  FAMILY: "Family",
};

export interface Benefit {
  id: number;
  name: string;
  benefitRef: BenefitRef;
  applicableGender: string;
  applicableMember: string;
  limit: number;
  suspensionThreshold: number;
  preAuthType: string;
  sharing: BenefitSharing;
  coPaymentRequired: boolean;
  coPaymentAmount: number;
  parentBenefit: string;
  waitingPeriod: string;
  processed: boolean;
  processedTime: string;
  parent: Benefit | null;
  payer: Payer;
  benefitType: string;
  capitationType: string;
  capitationPeriod: string;
  capitationMaxVisitCount: number;
  requireBeneficiaryToSelectProvider: boolean;
  daysOfAdmissionLimit: number;
  amountPerDayLimit: number;
  restriction: Restriction;
}

/**
 * Benefit with provider restriction checks
 */
export interface BeneficiaryProviderBenefit {
  /**
   * WARN: This is not the benefit id
   */
  id: number;
  aggregateId: string;
  benefitId: number;
  beneficiaryId: number;
  memberName: string;
  memberNumber: string;
  benefitName: string;
  status: BenefitStatus;
  balance: number;
  suspensionThreshold: number;
  initialLimit: number;
  categoryId: number;
  payerId: number;
  utilization: number;
  parent: BeneficiaryProviderBenefit;
  startDate: string;
  endDate: string;
  joinDate: string;
  gender: string;
  memberType: string;
  catalogId: number;
  jicEntityId: number;
  apaEntityId: number;
  benefitType: string;
  capitationType: string;
  capitationPeriod: string;
  visitCountPeriod: string;
  capitationMaxVisitCount: number;
  capitationFacilitiesCount: number;
  requireBeneficiaryToSelectProvider: boolean;
  daysOfAdmissionLimit: number;
  amountPerDayLimit: number;
  applicableMinAge: number;
  applicableMaxAge: number;
  transferable: boolean;
  billable: boolean | null;
  dob: string;
  restriction: {
    restricted: boolean;
    message: string;
  };
  benefit: Benefit;
}

export interface BenefitRef {
  id: number;
  code: string;
  name: string;
  serviceGroup: ServiceGroup;
}

export interface Payer {
  id: number;
  name: string;
  contact: string;
  type: string;
}

export interface Restriction {
  id: number;
  name: string;
  payer: Payer;
  restrictionType: string;
  active: boolean;
  createDate: string;
}

export interface BeneficiaryBenefitProvider {
  provider: Provider;
  id: number;
  benefit: Benefit;
}

export interface Validity {
  years: number;
  months: number;
  days: number;
  negative: boolean;
  zero: boolean;
  chronology: Chronology;
  units: Unit[];
}

export interface Chronology {
  calendarType: string;
  id: string;
}

export interface Unit {
  dateBased: boolean;
  timeBased: boolean;
  duration: Duration;
  durationEstimated: boolean;
}

export interface Duration {
  seconds: number;
  nano: number;
  negative: boolean;
  zero: boolean;
}

export enum VisitStatus {
  CLOSED = "CLOSED",
  INACTIVE = "INACTIVE",
  ACTIVE = "ACTIVE",
  LINE_ITEMS_ADDED = "LINE_ITEMS_ADDED",
  DIAGNOSIS_ADDED = "DIAGNOSIS_ADDED",
  PENDING = "PENDING",
  CANCELLED = "CANCELLED",
  TRANSMITTED = "TRANSMITTED",
  SETTLED = "SETTLED",
  REJECTED = "REJECTED",
  DRAFT = "DRAFT",
}

export const visitStatusLabels = new Map<VisitStatus, string>([
  [VisitStatus.CLOSED, "Invoiced"],
  [VisitStatus.INACTIVE, "Inactive"],
  [VisitStatus.ACTIVE, "Active"],
  [VisitStatus.LINE_ITEMS_ADDED, "Complete"],
  [VisitStatus.DIAGNOSIS_ADDED, "Diagnosis Added"],
  [VisitStatus.PENDING, "Pended"],
  [VisitStatus.CANCELLED, "Cancelled"],
  [VisitStatus.TRANSMITTED, "Transmitted"],
  [VisitStatus.SETTLED, "Settled"],
  [VisitStatus.REJECTED, "Rejected"],
  [VisitStatus.DRAFT, "Draft"],
]);

export function getProviderVisitStatusLabel(status: VisitStatus) {
  switch (status) {
    case VisitStatus.CANCELLED:
      return "Cancelled";
    case VisitStatus.LINE_ITEMS_ADDED:
    case VisitStatus.TRANSMITTED:
    case VisitStatus.SETTLED:
    case VisitStatus.REJECTED:
      return "Dispatched";
    case VisitStatus.CLOSED:
      // Visit available for dispatch in centralized billing
      return "Closed";
    case VisitStatus.DRAFT:
      return "Draft";
    default:
      return visitStatusLabels.get(status);
  }
}

export interface Visit {
  id: number;
  memberNumber: string;
  memberName: string;
  hospitalProviderId: number;
  staffId: string;
  staffName: string;
  aggregateID: string;
  totalInvoiceAmount: number;
  status: VisitStatus;
  claimProcessStatus: string;
  invoiceLines: InvoiceLine[];
  diagnosis: Diagnosis[];
  beneficiaryType: BeneficiaryType;
  benefitName: string;
  categoryId: string;
  payerId: string;
  payerName: string;
  policyNumber: string;
  beneficiaryId: number;
  benefitId: number;
  visitType: VisitType;
  offSystemReason: string;
  reimbursementProvider: string;
  reimbursementInvoiceDate: string;
  reimbursementReason: string;
  balanceAmount: number;
  invoiceNumber: string;
  aggregateId: string;
  preAuths: Array<PreAuth>;
  invoices: Invoice[];
  scheme: Scheme;
  schemeName?: string;
  createdAt: string;
  serviceGroup?: ServiceGroup;
  providerName?: string;
}

export interface DraftVisit extends Partial<Visit> {
  id: number;
  memberNumber: string;
  memberName: string;
  hospitalProviderId: number;
  staffId: string;
  staffName: string;
  status: VisitStatus.DRAFT;
  totalInvoiceAmount: 0;
  middleWareStatus: MiddlewareStatus.UNSENT;
  claimProcessStatus: ClaimProcessStatus.UNPROCESSED;
  beneficiaryType: BeneficiaryType;
  payerId: string;
  providerMiddleware: string;
  visitType: VisitType;
  createdAt: string;
}

export interface InvoiceLine {
  id: number;
  lineTotal: number;
  description: string;
  invoiceNumber: string;
  quantity: number;
  unitPrice: number;
  lineType: InvoiceLineType;
  claimRef: string;
  lineCategory: string;
  invoice: Invoice;
}

export interface InvoiceVisit {
  id: number;
  memberNumber: string;
  memberName: string;
  hospitalProviderId: number;
  staffId: string;
  staffName: string;
  aggregateId: string;
  categoryId: string;
  benefitName: string;
  beneficiaryId: number;
  benefitId: number;
  payerId: string;
  policyNumber: string;
  balanceAmount: number;
  beneficiaryType: string;
  totalInvoiceAmount: number;
  providerMiddleware: string;
  invoiceNumber: string;
  status: string;
  middlewareStatus: string;
  claimProcessStatus: string;
  cancelReason: string;
  createdAt: string;
  updatedAt: string;
  visitEnd: string;
  diagnosis: Diagnosis[];
  visitType: string;
  offSystemReason: string;
  reimbursementProvider: string;
  reimbursementInvoiceDate: string;
  reimbursementReason: string;
  payerStatus: string;
  facilityType: string;
  providerMapping: string;
  benefitMapping: string;
  payerClaimReference: string;
  invoiceDate: string;
}

export interface Diagnosis {
  id: number;
  code: string;
  title: string;
  invoiceNumber: string;
  claimRef: string;
}

/**
 * TODO: Verify type
 */
export interface VisitPreAuth {
  id: number;
  status: string;
  requestAmount: number;
  authorizedAmount: number;
  memberNumber: string;
  memberName: string;
  benefitName: string;
  service: string;
  reference: string;
}

export interface Scheme {
  id: string;
  name: string;
}

// ICD10
export interface BaseDiagnosis {
  id: number;
  code: string;
  title: string;
}

export interface Procedure {
  id: number;
  procedure_code: string;
  procedure_description: string;
}

export enum Service {
  DENTAL = "DENTAL",
  OPTICAL = "OPTICAL",
  MRI_SCAN = "MRI_SCAN",
  SURGERY = "SURGERY",
}

export enum PreAuthStatus {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  AUTHORIZED = "AUTHORIZED",
  DECLINED = "DECLINED",
  CLAIMED = "CLAIMED",
  CANCELLED = "CANCELLED",
  REVOKED = "REVOKED",
  PENDING = "PENDING",
  DRAFT = "DRAFT",
  EXPIRED = "EXPIRED",
  WITHDRAWN = "WITHDRAWN",
  INCOMPLETE = "INCOMPLETE",
}

export const preauthStatusLabels = new Map<PreAuthStatus, string>([
  [PreAuthStatus.ACTIVE, "Active Admission"],
  [PreAuthStatus.DRAFT, "Notification"],
  [PreAuthStatus.PENDING, "Pending"],
  [PreAuthStatus.INACTIVE, "Inactive"],
  [PreAuthStatus.CLAIMED, "Claimed"],
  [PreAuthStatus.AUTHORIZED, "Approved"],
  [PreAuthStatus.DECLINED, "Declined"],
  [PreAuthStatus.CANCELLED, "Cancelled"],
  [PreAuthStatus.REVOKED, "Revoked"],
  [PreAuthStatus.EXPIRED, "Expired"],
  [PreAuthStatus.WITHDRAWN, "Withdrawn"],
]);

export interface PreauthDiagnosisInformation {
  requestType: string;
  service: string;
  prediagnosisCodes: string[];
  initialNotes?: string | undefined;
}

interface PreauthMetadata {
  requester: string;
  draft: boolean;
  visitNumber: number;
  preauthType?: PreAuthType;
}

export interface AdmissionRequest extends PreauthDiagnosisInformation, PreauthMetadata {}

export interface PreauthBase extends AdmissionRequest, ServiceFields {
  initialRequestAmount?: number;
  requestAmount: number;
  notes: string;
  supportingDocuments: string[];
  markAsIncomplete: boolean;
}

export interface AuthorizePreauthRequest {
  authorizer: string;
  amount?: number;
  notes?: string;
  numberOfDaysValid?: number;
  allowedDaysOfAdmission?: number;
  limit?: number;
  bedType?: string;
  doctorFees?: number;
  benefitId?: number;
  guidelines?: CreateGuideline[] | undefined;
  ignoreErrors?: boolean;
  reason: string;
}

export interface DeclinePreauthRequest {
  declinedBy: string;
  reason: string;
}

export interface PayerPreauthBase {
  initialAuthorizedAmount?: number;
  authorizedAmount?: number;
  authorizer?: string;
  authorizationNotes?: string;
  validForDays?: number;
  netOfNHIFLimit?: number;
  doctorFees?: number;
  rejectNotes?: string;
  rejectBy?: string;
  limit: number | null;
}

export interface EditPreauthMetadata {
  editor: PreauthEditor;
  editReason: string;
  editedByUser: string;
  markAsIncomplete: boolean;
  markAsIncompleteReason: string;
}

export interface EditPreauthRequest {
  id: number;
  body: Partial<PreauthBase> &
    Partial<PayerPreauthBase> &
    PartialOrUndefined<EditPreauthMetadata> & {
      status?: PreAuthStatus;
    };
}

/**
 * TODO: Merge with PreAuth type
 */
export interface PreAuth extends PreauthBase, PayerPreauthBase, EditPreauthMetadata {
  id: number;
  time: string;
  status: PreAuthStatus;
  schemeName: string;
  payerName: string;
  providerName: string;
  diagnosisInfo: BaseDiagnosis[]; // Derived from prediagnosisCodes
  reference: string;
  validity: Validity;
  utilization: number;
  createdAt: string;
  updatedAt: string;
  authorizedAt: string;
  visit: Visit;
  balanceAmount: number;
  reversedAmount: number;
  sessionTrackers: SessionTracker[];
  lines: PreAuthLine[];
  procedureInfo: ProcedureInfo;
  guidelines?: Guideline[];
  allowedDaysOfAdmission?: number;
  topUps: TopUps;
}

export type PreAuthorizationTopUpLine = {
  id: number;
  name: string;
  quantity: number;
  cost: number;
  tag: string;
  numberOfTeeth: number;
};

export type PreAuthorizationTopUp = {
  id: number;
  requestedAmount: number;
  authorizedAmount: number;
  status: "PENDING" | "AUTHORIZED" | "REJECTED";
  lineItems: PreAuthorizationTopUpLine[];
  documents: {
    id: string;
    fileUrl: string;
  }[];
  createdAt: string;
};

export type TopUps = PreAuthorizationTopUp[];

export interface PreAuthLine {
  id: number;
  preAuthorization: string;
  name: string;
  tag: string;
  numberOfTeeth: number;
  quantity: number;
  cost: number;
  active: boolean;
  createdAt: string;
}

export interface DiagnosisInfo {
  id: number;
  code: string;
  title: string;
}

export interface ProcedureInfo {
  id: number;
  procedure_code: string;
  procedure_description: string;
}

export interface Drug {
  id: number;
  name: string;
}

export interface LabTest {
  id: number;
  name: string;
}

export enum RequestType {
  DENTAL = "Dental",
  OPTICAL = "Optical",
  OUTPATIENT = "Outpatient",
  INPATIENT = "Inpatient",
  MATERNITY = "Maternity",
}

export const requestTypeLabels = new Map<RequestType, string>([
  [RequestType.INPATIENT, "Inpatient"],
  [RequestType.OUTPATIENT, "Outpatient"],
  [RequestType.MATERNITY, "Maternity"],
  [RequestType.DENTAL, "Dental"],
  [RequestType.OPTICAL, "Optical"],
]);

export enum RestrictionType {
  INCLUSIVE = "INCLUSIVE",
  EXCLUSIVE = "EXCLUSIVE",
}

export interface RestrictionProvider {
  provider: Provider;
  id: number;
  createDate: string;
}

export interface AddRestrictionProvidersResponse {
  restrictionId: number;
  providerIds: number[];
}

export interface RemoveRestrictionProvidersResponse {
  providerIds: number[];
}

export interface User {
  self: string;
  id: string;
  origin: string;
  createdTimestamp: number;
  username: string;
  enabled: boolean;
  emailVerified: boolean;
  firstName: string;
  lastName: string;
  email: string;
  totp?: boolean;
  federationLink: string;
  serviceAccountClientId: string;
  attributes: Attributes;
  credentials: Credential[];
  disableableCredentialTypes: string[];
  requiredActions: string[];
  federatedIdentities: FederatedIdentity[];
  realmRoles: string[];
  clientRoles: ClientRoles;
  clientConsents: ClientConsent[];
  notBefore: number;
  groups: string[];
  access: Access;
}

export interface Attributes {
  additionalProp1: string[];
  additionalProp2: string[];
  additionalProp3: string[];
}

export interface Credential {
  id: string;
  type: string;
  userLabel: string;
  createdDate: number;
  secretData: string;
  credentialData: string;
  priority: number;
  value: string;
  temporary: boolean;
}

export interface FederatedIdentity {
  identityProvider: string;
  userId: string;
  userName: string;
}

export interface ClientRoles {
  additionalProp1: string[];
  additionalProp2: string[];
  additionalProp3: string[];
}

export interface ClientConsent {
  clientId: string;
  grantedClientScopes: string[];
  createdDate: number;
  lastUpdatedDate: number;
}

export interface Access {
  additionalProp1: boolean;
  additionalProp2: boolean;
  additionalProp3: boolean;
}

export enum ClaimStatus {
  CLOSED = "CLOSED",
  REJECTED = "REJECTED",
  PENDING = "PENDING",
  ABANDONED = "ABANDONED",
  TRANSMITTED = "TRANSMITTED",
  SETTLED = "SETTLED",
  INACTIVE = "INACTIVE",
  ACTIVE = "ACTIVE",
  LINE_ITEMS_ADDED = "LINE_ITEMS_ADDED",
  DIAGNOSIS_ADDED = "DIAGNOSIS_ADDED",
  CANCELLED = "CANCELLED",
  BALANCE_ADDED = "BALANCE_ADDED",
}

export const claimReportsStatuses = [ClaimStatus.CLOSED, ClaimStatus.REJECTED] as const;

export const claimStatusLabels = new Map<ClaimStatus, string>([
  [ClaimStatus.PENDING, "Pending"],
  [ClaimStatus.ABANDONED, "Abandoned"],
  [ClaimStatus.TRANSMITTED, "Transmitted"],
  [ClaimStatus.SETTLED, "Settled"],
  [ClaimStatus.REJECTED, "Rejected"],
  [ClaimStatus.INACTIVE, "Inactive"],
  [ClaimStatus.ACTIVE, "Active"],
  [ClaimStatus.CLOSED, "Closed"],
  [ClaimStatus.LINE_ITEMS_ADDED, "Items Added"],
  [ClaimStatus.DIAGNOSIS_ADDED, "Diagnosis Added"],
  [ClaimStatus.CANCELLED, "Cancelled"],
  [ClaimStatus.BALANCE_ADDED, "Balance Added"],
]);

export enum ClaimProcessStatus {
  PROCESSED = "PROCESSED",
  UNPROCESSED = "UNPROCESSED",
}

export const claimProcessStatusLabels = new Map<ClaimProcessStatus, string>([
  [ClaimProcessStatus.PROCESSED, "Processed"],
  [ClaimProcessStatus.UNPROCESSED, "Unprocessed"],
]);

export enum MiddlewareStatus {
  SENT = "SENT",
  UNSENT = "UNSENT",
}

export const middlewareStatusLabels = new Map<MiddlewareStatus, string>([
  [MiddlewareStatus.SENT, "Sent"],
  [MiddlewareStatus.UNSENT, "Unsent"],
]);

export enum VisitType {
  ONLINE = "ONLINE",
  OFF_LCT = "OFF_LCT",
  REIMBURSEMENT = "REIMBURSEMENT",
}

export const visitTypeLabels = new Map<VisitType, string>([
  [VisitType.ONLINE, "Online"],
  [VisitType.OFF_LCT, "Offline Visit (Off-LCT)"],
  [VisitType.REIMBURSEMENT, "Reimbursement"],
]);

export const normalizedVisitTypeLabels = new Map<VisitType, string>([
  [VisitType.ONLINE, "online"],
  [VisitType.OFF_LCT, "offline visit"],
  [VisitType.REIMBURSEMENT, "reimbursement"],
]);

export enum PayerStatus {
  UNSENT = "UNSENT",
  SENT = "SENT",
  FAILED = "FAILED",
}

export const payerStatusLabels = new Map<PayerStatus, string>([
  [PayerStatus.UNSENT, "Unsent"],
  [PayerStatus.SENT, "Sent"],
  [PayerStatus.FAILED, "Failed"],
]);

export enum InvoiceStatus {
  REJECTED = "REJECTED",
  BALANCE_DEDUCTED = "BALANCE_DEDUCTED",
  BALANCE_ADDED = "BALANCE_ADDED",
  /**
   * Unused statuses
   */
  REVERSED = "REVERSED",
  SENT = "SENT",
  PENDED = "PENDED",
  DIAGNOSIS_ADDED = "DIAGNOSIS_ADDED",
  DOCUMENTS_ADDED = "DOCUMENTS_ADDED",
}

export const dispatchStatusLabels = new Map<boolean | null, string>([
  [true, "Line Items Added"],
  [false, "Unknown"],
  [null, "Invoiced"],
]);

export const invoiceStatusLabels: Record<InvoiceStatus, string> = {
  REJECTED: "Rejected",
  BALANCE_DEDUCTED: "Balance Deducted",
  DIAGNOSIS_ADDED: "Diagnosis Added",
  DOCUMENTS_ADDED: "Documents Added",
  PENDED: "Pended",
  SENT: "Sent",
  BALANCE_ADDED: "Balance Added",
  REVERSED: "Reversed",
};

export const invoiceStatusLabelsArr = new Map<InvoiceStatus, string>([
  [InvoiceStatus.REJECTED, "Rejected"],
  [InvoiceStatus.BALANCE_DEDUCTED, "Balance Deducted"],
  [InvoiceStatus.DIAGNOSIS_ADDED, "Diagnosis Added"],
  [InvoiceStatus.DOCUMENTS_ADDED, "Documents Added"],
  [InvoiceStatus.PENDED, "Pended"],
  [InvoiceStatus.SENT, "Sent"],
  [InvoiceStatus.BALANCE_ADDED, "Balance Added"],
  [InvoiceStatus.REVERSED, "Reversed"],
]);

export const invoiceStatusMapping: Record<InvoiceStatus, Status> = {
  REJECTED: Status.FAILURE,
  BALANCE_DEDUCTED: Status.PENDING,
  DIAGNOSIS_ADDED: Status.PENDING,
  DOCUMENTS_ADDED: Status.PENDING,
  PENDED: Status.PENDING,
  SENT: Status.SUCCESS,
  BALANCE_ADDED: Status.SUCCESS,
  REVERSED: Status.SUCCESS,
};

export const invoiceStatusUserLabels: Record<InvoiceStatus, string> = {
  REJECTED: "Rejected",
  BALANCE_DEDUCTED: "Invoiced",
  DIAGNOSIS_ADDED: "Complete",
  DOCUMENTS_ADDED: "Complete",
  PENDED: "Pended",
  SENT: "Sent",
  BALANCE_ADDED: "Reversed",
  REVERSED: "Reversed",
};

export enum FacilityType {
  SINGLE = "SINGLE",
  MULTIPLE = "MULTIPLE",
}

export const facilityTypeLabels = new Map<FacilityType, string>([
  [FacilityType.SINGLE, "Single"],
  [FacilityType.MULTIPLE, "Multiple"],
]);

export enum ServiceGroup {
  INPATIENT = "INPATIENT",
  OUTPATIENT = "OUTPATIENT",
  DENTAL = "DENTAL",
  OPTICAL = "OPTICAL",
  MATERNITY = "MATERNITY",
  COVID = "COVID",
}

export const serviceGroupLabels = new Map<ServiceGroup, string>([
  [ServiceGroup.INPATIENT, "Inpatient"],
  [ServiceGroup.OUTPATIENT, "Outpatient"],
  [ServiceGroup.DENTAL, "Dental"],
  [ServiceGroup.OPTICAL, "Optical"],
  [ServiceGroup.MATERNITY, "Maternity"],
  [ServiceGroup.COVID, "Covid"],
]);

export interface Invoice {
  id: number;
  hospitalProviderId: number;
  invoiceNumber: string;
  service: string | null;
  totalAmount: number;
  claimRef: string | null;
  status: InvoiceStatus;
  createdAt: string | null;
  invoiceLines: InvoiceLine[];
  dispatched: boolean;
}

export interface Claim {
  /**
   * This is actually the visit number
   */
  id: number;
  visitNumber: number;
  memberNumber: string;
  memberName: string;
  hospitalProviderId: number;
  staffId: string;
  staffName: string;
  aggregateId: string;
  categoryId: string;
  benefitName: string;
  beneficiaryId: number;
  benefitId: number;
  payerId: string;
  policyNumber: string;
  balanceAmount: number;
  totalAmount: number;
  beneficiaryType: string;
  totalInvoiceAmount: number;
  providerMiddleware: string;
  /**
   * @deprecated
   */
  invoiceNumber: string;
  /**
   * Equivalent to the visit status
   */
  status?: ClaimStatus | InvoiceStatus;
  middlewareStatus: MiddlewareStatus;
  claimProcessStatus: ClaimProcessStatus;
  cancelReason: string;
  createdAt: string | number;
  updatedAt: string;
  visitEnd: string;
  diagnosis: Diagnosis[];
  visitType: VisitType;
  offSystemReason: string;
  reimbursementProvider: string;
  reimbursementInvoiceDate: string;
  reimbursementReason: string;
  payerStatus: PayerStatus;
  facilityType: FacilityType;
  serviceGroup: ServiceGroup;
  preAuths: PreAuth[];
  providerMapping: string;
  benefitMapping: string;
  payerClaimReference: string;
  /**
   * @deprecated
   */
  invoiceDate: string;
  providerName: string;
  categoryName: string;
  schemeName: string;
  invoices: Invoice[];
}

export interface SessionTracker {
  id: number;
  preAuthorization: string;
  amount: number;
  createdAt: string;
}

export interface FilterBeneficiaries {
  payerId: number;
  planIds?: number[];
  policyIds?: number[];
  categoryIds?: number[];
  statuses?: BeneficiaryStatus[];
  beneficiaryTypes?: BeneficiaryType[];
  verificationMode?: VerificationMode;
  changeLogType?: ChangeLogAction;
  fromDate?: string; // Filter by processed date
  toDate?: string;
}

export type FilterVisits = {
  payerId?: number | undefined;
  payerIds?: number[] | undefined;
  planIds?: number[] | undefined;
  policyIds?: number[] | undefined;
  categoryIds?: number[] | undefined;
  providerId?: number | undefined;
  providerIds?: number[] | undefined;
  serviceGroups?: ServiceGroup[] | undefined;
  // TODO: Unify visit and claim statuses
  statuses?: VisitStatus[] | ClaimStatus[] | undefined;
  visitTypes?: VisitType[] | undefined;
  fromDate?: string | undefined;
  toDate?: string | undefined;
  /**
   * providerIds for main providers
   * Fetches claims for all providers under the tier 1 provider
   */
  mainFacilities?: number[] | undefined;
  middlewareStatuses?: MiddlewareStatus[] | undefined;
  payerStatuses?: PayerStatus[] | undefined;
  visitNumbers?: number[] | undefined;
  invoiceNumbers?: string[] | undefined;
};
export type FilterInvoices = {
  payerId?: number | undefined;
  payerIds?: number[] | undefined;
  planIds?: number[] | undefined;
  policyIds?: number[] | undefined;
  categoryIds?: number[] | undefined;
  providerId?: number | undefined;
  providerIds?: number[] | undefined;
  serviceGroups?: ServiceGroup[] | undefined;
  // TODO: Unify visit and claim statuses
  statuses?: VisitStatus[] | ClaimStatus[] | undefined;
  visitTypes?: VisitType[] | undefined;
  fromDate?: string | undefined;
  toDate?: string | undefined;
  /**
   * providerIds for main providers
   * Fetches claims for all providers under the tier 1 provider
   */
  mainFacilities?: number[] | undefined;
  middlewareStatuses?: MiddlewareStatus[] | undefined;
  payerStatuses?: PayerStatus[] | undefined;
  visitNumbers?: number[] | undefined;
  invoiceNumbers?: string[] | undefined;
  page?: number | undefined;
  size?: number | undefined;
};

export enum ClaimsReportType {
  /**
   * @deprecated
   */
  SCHEME_UTILIZATION = "SCHEME_UTILIZATION",
  PROVIDER_UTILIZATION = "PROVIDER_UTILIZATION",
  DELIVERY_STATUS = "DELIVERY_STATUS",
  SCHEME_UTILIZATION_CLINICAL = "SCHEME_UTILIZATION_CLINICAL",
  SCHEME_UTILIZATION_FINANCIAL = "SCHEME_UTILIZATION_FINANCIAL",
}

export const claimsReportsTypes = [
  ClaimsReportType.SCHEME_UTILIZATION,
  ClaimsReportType.PROVIDER_UTILIZATION,
  ClaimsReportType.DELIVERY_STATUS,
];

export enum SearchProviderFilter {
  ALL = "ALL",
  MAIN = "MAIN",
  BRANCH = "BRANCH",
}

export const searchProviderFilterLabels: Record<SearchProviderFilter, string> = {
  ALL: "All",
  MAIN: "Main",
  BRANCH: "Branch",
};

export enum DeviceStatus {
  AVAILABLE = "AVAILABLE",
  ALLOCATED = "ALLOCATED",
  REPAIR = "REPAIR",
  DECOMMISSIONED = "DECOMMISSIONED",
}

export const deviceStatusLabels = new Map<DeviceStatus, string>([
  [DeviceStatus.AVAILABLE, "Available"],
  [DeviceStatus.ALLOCATED, "Allocated"],
  [DeviceStatus.REPAIR, "Repair"],
  [DeviceStatus.DECOMMISSIONED, "Decommissioned"],
]);

export const patchDeviceStatusValues = [
  DeviceStatus.AVAILABLE,
  DeviceStatus.DECOMMISSIONED,
  DeviceStatus.ALLOCATED,
  DeviceStatus.REPAIR,
];

export enum DeviceSimStatus {
  ASSIGNED = "ASSIGNED",
  UNASSIGNED = "UNASSIGNED",
}

export const deviceSimStatusLabels = new Map<DeviceSimStatus, string>([
  [DeviceSimStatus.ASSIGNED, "Assigned"],
  [DeviceSimStatus.UNASSIGNED, "Unassigned"],
]);

export interface DeviceSim {
  id: number;
  simNumber: string;
  status: DeviceSimStatus;
  addedOn: string;
  updatedAt: string;
  logs: DeviceLog[];
}

export enum DeviceAllocationStatus {
  ALLOCATED = "ALLOCATED",
  DEALLOCATED = "DEALLOCATED",
}

export const deviceAllocationStatusLabels = new Map<DeviceAllocationStatus, string>([
  [DeviceAllocationStatus.ALLOCATED, "Allocated"],
  [DeviceAllocationStatus.DEALLOCATED, "Deallocated"],
]);

export interface Accessory {
  id: number;
  accessory: string;
  note: string;
  deviceAllocation: DeviceAllocation;
  deviceCatalog: Array<Device>;
  addedDate: string;
  updatedAt: string;
}

export enum CheckerStatus {}

export interface DeviceAllocation {
  id: number;
  deviceCatalog: Device; // string?
  provider: Provider;
  status: DeviceAllocationStatus;
  note: string;
  dateAllocated: string;
  dateDeallocated: string;
  accessories: Array<Accessory>;
  deviceSim: DeviceSim;
  enableGeofence: boolean;
  radius: number;
  createdBy: string;
  allocatedBy: string;
  checkedBy: string;
  checkerStatus: CheckerStatus;
  dateCreated: string;
  dateUpdated: string;
  documents: DeviceDocument[];
}

export interface Device {
  id: number;
  deviceId: string;
  status: DeviceStatus;
  description: string;
  registeredOn: string;
  /**
   * @deprecated?
   */
  registeredByUser: string;
  deviceModel: DeviceModel;
  updatedAt: string;
  imei: Imei[];
  allocations: DeviceAllocation[];
  accessories: Accessory[];
  logs: DeviceLog[];
}

export interface DeviceModel {
  id: number;
  model: string;
  description: string;
  createdAt: string;
  deviceCatalog: Array<Device>;
  logs: DeviceLog[];
}

export interface Imei {
  id: number;
  imei: string;
  description: string;
  updatedAt: string;
  registeredOn: string;
  deviceCatalog: Array<Device>;
}

export interface DeviceDocument {
  id: number;
  documentUrl: string;
  addedOn: string;
  deviceAllocation: DeviceAllocation;
  logs: string[];
}

export enum DeviceLogAction {
  CREATED_AND_ALLOCATED_DEVICE = "CREATED_AND_ALLOCATED_DEVICE",
  ADDED_DEVICE = "ADDED_DEVICE",
  ADDED_MODEL = "ADDED_MODEL",
  UPDATED_DEVICE = "UPDATED_DEVICE",
  ADDED_SIM = "ADDED_SIM",
}

export const deviceLogActionLabels = new Map<DeviceLogAction, string>([
  [DeviceLogAction.CREATED_AND_ALLOCATED_DEVICE, "Added device"],
  [DeviceLogAction.ADDED_DEVICE, "Added device"],
  [DeviceLogAction.ADDED_MODEL, "Added model"],
  [DeviceLogAction.UPDATED_DEVICE, "Updated device"],
  [DeviceLogAction.ADDED_SIM, "Added sim"],
]);

export interface DeviceLog {
  id: number;
  catalog: Device;
  sim: string;
  allocation: string;
  model: DeviceModel;
  document: DeviceDocument;
  action: DeviceLogAction;
  previousValues: string;
  payload: string;
  actionByUser: string;
  note: string;
  reason: string;
  createdOn: string;
}

export interface AllocateDeviceRequest {
  providerId: number;
  deviceCatalogId: number;
  simId: number;
  enableGeofence: boolean;
  radius: number;
  accessories: Array<{
    accessory: string;
    note: string;
  }>;
  createdBy: string;
}

/**
 * TODO: Fetch device models from API.
 * WARN: Update this when the list of models changes
 */
export enum DeviceModels {
  FP08 = "FP08",
  Saral = "Saral",
}

export interface ErrorResponse {
  timestamp: string;
  code: number;
  error: string;
}

export interface PatchDeviceRequest {
  deviceCatalogId: number;
  deviceStatus: DeviceStatus;
  updatedBy: string;
}

export enum StatementType {
  INDIVIDUAL = "INDIVIDUAL",
  FAMILY = "FAMILY",
}

export enum BillingStation {
  MULTIPLE = "MULTIPLE",
  SINGLE = "SINGLE",
}

export enum DeductibleType {
  COPAYMENT = "COPAYMENT",
  EXCLUSION = "EXCLUSION",
  CASHPAYMENT = "CASHPAYMENT",
  DISCOUNT = "DISCOUNT",
  NHIF = "NHIF",
}

export const deductibleTypeLabels = new Map<DeductibleType, string>([
  [DeductibleType.COPAYMENT, "Co-payment"],
  [DeductibleType.EXCLUSION, "Exclusion"],
  [DeductibleType.CASHPAYMENT, "Cash Payment"],
  [DeductibleType.DISCOUNT, "Discount"],
  [DeductibleType.NHIF, "SHIF"],
]);

export interface SingleInvoiceDeductible {
  deductibleType: DeductibleType;
  amount: number;
}

export interface SaveBillAndCloseVisitRequest {
  id: number;
  totalInvoiceAmount: number;
  invoiceNumber: string;
  diagnosis?: Diagnosis[];
  hospitalProviderId: number;
  invoices?: Invoice[];
  singleInvoiceDeductibles?: SingleInvoiceDeductible[];
  staffId?: string;
  /* ---------------------------- Redundant fields? ---------------------------- */
  billingStation?: BillingStation;
  balanceAmount?: number;
  aggregateId?: string;
  claimProcessStatus?: string;
  memberNumber?: string;
  staffName?: string;
  status?: string;
  middlewareStatus?: string;
  benefitName?: string;
  beneficiaryType?: string;
  categoryId?: string;
  payerId?: string;
  payerName?: string;
  policyNumber?: string;
  benefitId?: number;
}

export enum Consultation {
  GENERAL_PRACTITIONER = "GENERAL_PRACTITIONER",
  PHYSICIAN = "PHYSICIAN",
  DENTAL = "DENTAL",
  CARDIOLOGIST = "CARDIOLOGIST",
  DERMATOLOGY = "DERMATOLOGY",
  EAR_NOSE_AND_THROAT = "EAR_NOSE_AND_THROAT",
  GYNAECOLOGIST = "GYNAECOLOGIST",
  NEUROSURGEON = "NEUROSURGEON",
  NUTRITIONIST = "NUTRITIONIST",
  ORTHOPAEDIC = "ORTHOPAEDIC",
  PAEDIATRIC_NEUROLOGIST = "PAEDIATRIC_NEUROLOGIST",
  PEDIATRICIAN = "PEDIATRICIAN",
  PSYCHIATRIST = "PSYCHIATRIST",
  RADIOLOGIST = "RADIOLOGIST",
  RADIOTHERAPY = "RADIOTHERAPY",
  RHEUMATOLOGIST = "RHEUMATOLOGIST",
  UROLOGY = "UROLOGY",
  SPECIALIST = "SPECIALIST",
}

export const consultationLabels = new Map<Consultation, string>([
  [Consultation.CARDIOLOGIST, "Cardiologist"],
  [Consultation.GENERAL_PRACTITIONER, "General Practitioner"],
  [Consultation.DENTAL, "Dental"],
  [Consultation.DERMATOLOGY, "Dermatology"],
  [Consultation.EAR_NOSE_AND_THROAT, "Ear Nose & Throat (ENT)"],
  [Consultation.GYNAECOLOGIST, "Gynaecologist"],
  [Consultation.NEUROSURGEON, "Neurosurgeon"],
  [Consultation.NUTRITIONIST, "Nutritionist"],
  [Consultation.ORTHOPAEDIC, "Orthopaedic"],
  [Consultation.PAEDIATRIC_NEUROLOGIST, "Paediatric Neurologist"],
  [Consultation.PEDIATRICIAN, "Pediatrician"],
  [Consultation.PHYSICIAN, "Physician"],
  [Consultation.PSYCHIATRIST, "Psychiatrist"],
  [Consultation.RADIOLOGIST, "Radiologist"],
  [Consultation.RADIOTHERAPY, "Radiotherapy"],
  [Consultation.RHEUMATOLOGIST, "Rheumatologist"],
  [Consultation.SPECIALIST, "Specialist"],
  [Consultation.UROLOGY, "Urology"],
]);

export enum InvoiceLineType {
  CONSULTATION = "CONSULTATION",
  PHARMACY = "PHARMACY",
  LABORATORY = "LABORATORY",
  RADIOLOGY = "RADIOLOGY",
  OTHER = "OTHER",
  PROCEDURE = "MEDICALPROCEDURE",
  DIAGNOSIS = "DIAGNOSIS",
  INPATIENT = "INPATIENT",
}

export enum DocumentType {
  CLAIM = "CLAIM",
  INVOICE = "INVOICE",
  OTHER = "OTHER",
}

export const documentTypeLabels = new Map<DocumentType, string>([
  [DocumentType.CLAIM, "Claim Form"],
  [DocumentType.INVOICE, "Invoice"],
  [DocumentType.OTHER, "Supporting Document"],
]);

export const invoiceLineOptionValues = [
  InvoiceLineType.CONSULTATION,
  InvoiceLineType.PHARMACY,
  InvoiceLineType.LABORATORY,
  InvoiceLineType.RADIOLOGY,
  InvoiceLineType.OTHER,
];

export const invoiceLineTypeLabels: Record<InvoiceLineType, string> = {
  CONSULTATION: "Consultation",
  PHARMACY: "Pharmacy",
  LABORATORY: "Laboratory",
  RADIOLOGY: "Radiology",
  OTHER: "Other",
  DIAGNOSIS: "Diagnosis",
  INPATIENT: "Inpatient",
  MEDICALPROCEDURE: "Procedure",
};

export interface DispatchVisitRequest {
  invoiceNumber: string;
  providerId: number;
  lineItems: CreateLineItem[];
  documents: Document[];
}

export interface CreateLineItem {
  itemDescription?: string | undefined;
  itemAmount?: number | undefined;
  invoiceNumber: string | undefined;
  lineType?: string | undefined;
  visitId?: number | undefined;
  providerId?: number | undefined;
  itemCode?: string | undefined;
  itemQuantity?: number | undefined;
  itemUnitPrice?: string | undefined;
  providerName?: string | undefined;
  type?: string | undefined;
  fileUrl?: string | undefined;
}

export interface Document {
  providerName: string;
  providerId: number;
  type: string;
  fileUrl: string;
  invoiceNumber: string;
}

export interface DraftVisitRequest {
  beneficiaryId: number;
  hospitalProviderId: number;
  staffId: string;
  staffName: string;
}

export interface OTPRequest {
  phoneNumber: string;
  facilityId: string;
  facilityName: string;
  source: "PORTAL";
  beneficiaryId: number;
}

export enum SMSType {
  B2B_BIOMETRIC_ON_BOARDING = "B2B_BIOMETRIC_ON_BOARDING",
  B2B_VERIFICATION = "B2B_VERIFICATION",
  PASSWORD_RECOVERY = "PASSWORD_RECOVERY",
  USER_REGISTRATION = "USER_REGISTRATION",
  LOGIN_ACC_VERIFICATION = "LOGIN_ACC_VERIFICATION",
}

export interface VerifyOTPRequest {
  phone: string;
  otp: string;
}

export enum PreAuthType {
  SCHEDULED_ADMISSION = "SCHEDULED_ADMISSION",
  EMERGENCY_ADMISSION = "EMERGENCY_ADMISSION",
}

export const preauthTypeLabels = new Map<PreAuthType, string>([
  [PreAuthType.SCHEDULED_ADMISSION, "Scheduled Admission"],
  [PreAuthType.EMERGENCY_ADMISSION, "Emergency Admission"],
]);

export interface CreateGuideline {
  description: string;
  cost: number;
}

export interface Guideline extends CreateGuideline {
  id: number;
  createdAt: string;
  updatedAt: string;
}

export interface BatchUpdateBeneficiariesQuery {
  policyId: number;
  updateBy: string;
  reason?: string;
  emailCallback?: string;
}

export interface BatchUpdateBeneficiaryStatusQuery {
  policyId: number;
  status: BeneficiaryStatus;
  updateBy: string;
  reason?: string;
  emailCallback?: string;
}

export enum TemplateType {
  MEMBER_ACTIVATION_OR_DEACTIVATION = "MEMBER_ACTIVATION_OR_DEACTIVATION",
  MEMBER_EDIT = "MEMBER_EDIT",
  DEVICE_UPLOAD = "DEVICE_UPLOAD",
  SIM_UPLOAD = "SIM_UPLOAD",
  MEMBER_ADDITION = "MEMBER_ADDITION",
}

export interface ProviderBranch {
  providerId: number;
  providerName: string;
}

export enum VisitExportFileType {
  PDF = "PDF",
  XLSX = "XLSX",
}

export enum OfflineVisitReason {
  SYSTEM_DOWNTIME = "SYSTEM_DOWNTIME",
  PROVIDER_NOT_SETUP = "PROVIDER_NOT_SETUP",
  FAULTY_DEVICE = "FAULTY_DEVICE",
  EMERGENCY_CASE = "EMERGENCY_CASE",
}

export const offlineVisitReasonLabels: Record<OfflineVisitReason, string> = {
  EMERGENCY_CASE: "Emergency",
  FAULTY_DEVICE: "Faulty Device",
  PROVIDER_NOT_SETUP: "Provider not setup",
  SYSTEM_DOWNTIME: "System downtime",
};

export enum Hand {
  RIGHT = "RIGHT",
  LEFT = "LEFT",
}

export const handLabels: Record<Hand, string> = {
  RIGHT: "Right",
  LEFT: "Left",
};

export enum Finger {
  THUMB = "THUMB",
  INDEX = "INDEX",
  // TODO: Add rest of fingers
}

export const fingerLabels: Record<Finger, string> = {
  THUMB: "Thumb",
  INDEX: "Index",
};

export enum PayerProviderMappingStatus {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
}

export interface PayerProviderMapping {
  id: number;
  status: PayerProviderMappingStatus;
  code: string;
  payer: Payer;
  provider: Provider;
}

export enum PreauthEditor {
  PROVIDER = "PROVIDER",
  PAYER = "PAYER",
}

export enum ActionLog {
  BENEFIT_TOP_UP = "BENEFIT_TOP_UP",
  BENEFIT_TRANSFER = "BENEFIT_TRANSFER",
  BENEFIT_STATUS_UPDATE = "BENEFIT_STATUS_UPDATE",
  PRE_AUTHORIZATION_UPDATE = "PRE_AUTHORIZATION_UPDATE",
  INVOICE_REVERSAL = "INVOICE_REVERSAL",
  CLAIM_VETTING = "CLAIM_VETTING",
  INVOICE_UPDATE = "INVOICE_UPDATE",
  INVOICE_BATCH_CREATED = "INVOICE_BATCH_CREATED",
  INVOICE_BATCH_ALLOCATED = "INVOICE_BATCH_ALLOCATED",
  CREATED = "CREATED",
  UPDATED = "UPDATED",
}

export type PreAuthTopUpByPayerRequest = {
  pathParameters: {
    preAuthId: number;
  };
  body: {
    amount: number;
    actionBy: string;
    reason: string;
  };
};

export type ApproveTopUpRequest = {
  pathParameters: {
    preAuthTopUpId: number;
  };
  body: {
    amount: number;
    actionBy: string;
    reason: string;
  };
};
export type RejectTopUpRequest = {
  pathParameters: {
    preAuthTopUpId: number;
  };
  body: {
    actionBy: string;
    reason: string;
  };
};

export type PreAuthRePortRequest = {
  body: {
    providerId?: number; // integer($int64)
    payerId?: number; // integer($int64)
    beneficiaryId?: number; // integer($int64)
    benefitId?: number; // integer($int64)
    catalogId?: number; // integer($int64)
    planId?: number; // integer($int64)
    search?: string;
    statuses?: Array<
      | "ACTIVE"
      | "INACTIVE"
      | "PENDING"
      | "DECLINED"
      | "AUTHORIZED"
      | "CLAIMED"
      | "CANCELLED"
      | "EXPIRED"
      | "DRAFT"
      | "WITHDRAWN"
    >;
    visitStatuses?: Array<
      | "INACTIVE"
      | "ACTIVE"
      | "CLOSED"
      | "LINE_ITEMS_ADDED"
      | "DIAGNOSIS_ADDED"
      | "PENDING"
      | "CANCELLED"
      | "TRANSMITTED"
      | "SETTLED"
      | "REJECTED"
      | "DRAFT"
      | "REFERRAL_REQUEST"
      | "REFERRED"
    >;
    serviceGroups?: Array<
      "CONSOLIDATED" | "OUTPATIENT" | "INPATIENT" | "DENTAL" | "OPTICAL" | "COVID" | "MATERNITY"
    >;
    requestTypes?: string[]; // unique items enforced at runtime if needed
    startDate?: string;
    endDate?: string;
    page?: number; // integer($int32)
    size?: number; // integer($int32)
    preAuthTypes?: Array<"SCHEDULED_ADMISSION" | "EMERGENCY_ADMISSION">;
  };
};

export type ReportVisit = {
  /** Required */
  id: number; // integer($int64)
  /** Required */
  visitNumber: number; // integer($int64)
  /** Required */
  memberName: string;
  /** Required */
  memberNumber: string;

  requestTime?: string;
  approvalTime?: string;
  declinedTime?: string;
  reference?: string;
  tat?: string;
  tatInMinutes?: string;
  providerName?: string;
  benefitName?: string;
  schemeName?: string;
  requestAmount?: number;
  approvedAmount?: number;
  authorizer?: string;
  authorizationNotes?: string;
  requester?: string;
  approvedBy?: string;
  declinedBy?: string;
  declinedReason?: string;
  withdrawnBy?: string;
  withdrawalReason?: string;
  withdrawalDate?: string;

  status?:
    | "ACTIVE"
    | "INACTIVE"
    | "PENDING"
    | "DECLINED"
    | "AUTHORIZED"
    | "CLAIMED"
    | "CANCELLED"
    | "EXPIRED"
    | "DRAFT"
    | "WITHDRAWN";
};

export type PayerProviderMappingResponse = ListResponse<PayerProviderMapping>;
export type ProviderBranchesResponse = SimpleListResponse<ProviderBranch>;
export type BatchUpdateBeneficiariesResponse = SimpleResponse<true>;
export type BatchUpdateBeneficiaryStatusResponse = SimpleResponse<true>;
export type AllocateDeviceResponse = SimpleResponse<true>;
export type SimsResponse = ListResponse<DeviceSim>;
export type DeviceModelResponse = ListResponse<DeviceModel>;
export type DeviceAllocationResponse = SimpleResponse<DeviceAllocation>;
export type DevicesResponse = ListResponse<Device>;
export type UsersResponse = Array<User>;
export type PagedVisitsResponse = ListResponse<Visit>;
export type ProviderClaimsResponse = ListResponse<ProviderClaim>;
export type BenefitProvidersResponse = ListResponse<BenefitProviders>;
export type BeneficiaryBenefitsResponse = SimpleListResponse<BeneficiaryBenefit>;
export type CountryResponse = SimpleListResponse<Country>;
export type RegionResponse = ListResponse<Region>;
export type BeneficiaryBenefitProviderResponse = SimpleResponse<BeneficiaryBenefitProvider[]>;
export type PreAuthsResponse = ListResponse<PreAuth>;
export type SearchPreAuthsResponse = SimpleListResponse<PreAuth>;
export type VisitsResponse = SimpleListResponse<Visit>;
export type DiagnosisResponse = ListResponse<BaseDiagnosis>;
export type PayerResponse = SimpleListResponse<Payer>;
export type ProcedureResponse = ListResponse<Procedure>;
export type FileUploadResponse = SimpleResponse<string>;
export type NewPreAuthResponse = SimpleResponse<PreAuth>;
export type EditPreAuthResponse = SimpleResponse<PreAuth>;
export type DeletePreAuthResponse = SimpleResponse<undefined>;
export type PreAuthResponse = SimpleResponse<PreAuth>;
export type VisitResponse = SimpleResponse<Visit>;
export type DrugsResponse = ListResponse<Drug>;
export type LabTestsResponse = ListResponse<LabTest>;
export type DownloadFileResponse = SimpleResponse<string>;
export type AuthorizePreauthResponse = SimpleResponse<PreAuth>;
export type DeclinePreAuthResponse = SimpleResponse<PreAuth>;
export type PlainPreAuthsResponse = SimpleListResponse<PreAuth>;
export type ProvidersResponse = ListResponse<Provider>;
export type ProviderResponse = SimpleResponse<Provider>;
export type DeleteRestrictionResponse = SimpleResponse<boolean>;
export type CreateRestrictionResponse = SimpleResponse<boolean>;
export type RestrictionsResponse = SimpleListResponse<Restriction>;
export type RestrictionProvidersResponse = ListResponse<RestrictionProvider>;
export type PlansResponse = SimpleListResponse<Plan>;
export type PoliciesResponse = SimpleListResponse<Policy>;
export type EnhancedPoliciesResponse = SimpleListResponse<EnhancedPolicy>;
export type CategoriesResponse = SimpleListResponse<Category>;
export type BeneficiariesResponse = ListResponse<Beneficiary>;
export type ClaimsResponse = ListResponse<Claim>;
export type BeneficiaryListResponse = SimpleListResponse<Beneficiary>;
export type BeneficiaryResponse = SimpleResponse<Beneficiary>;
export type SaveBillAndCloseVisitResponse = SimpleResponse<Visit>; // TODO: Verify response type
export type DispatchVisitResponse = SimpleResponse<string>;
export type DraftVisitResponse = SimpleResponse<DraftVisit>;
export type BeneficiaryProviderBenefitsResponse = SimpleListResponse<BeneficiaryProviderBenefit>;
export type SearchBeneficiariesResponse = SimpleListResponse<Beneficiary>;
export type BeneficiaryBasicsResponse = SimpleResponse<BeneficiaryBasics>;
export type BeneficiaryFamilyResponse = SimpleListResponse<Beneficiary>;
