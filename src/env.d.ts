/// <reference types="vite/client" />

/**
 * WARN: Must be kept upto date with env files
 */
interface ImportMetaEnv {
  readonly VITE_SAMPLE_ENV: string;
  readonly VITE_KEYCLOAK_URL: string;
  readonly VITE_API_URL: string;
  readonly VITE_KENGEN_PAYER_ID: string;
  readonly VITE_AGA_KHAN_NAIROBI_PROVIDER_ID: string;
  readonly VITE_KRA_PAYER_ID: string;
  readonly VITE_SIM_UPLOAD_TEMPLATE: string;
  readonly VITE_DEVICE_UPLOAD_TEMPLATE: string;
  readonly VITE_STAGING: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
