apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: "ingress-onboard-portal"
  namespace: frontend
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
    - hosts:
        - "onboard-uat.lctafrica.net"
      # Secret where TLS certificate and key are stored
      # Potentially populated by cert-manager
      # To prevent naming conflicts, we use the following convention: <client>-<env>-<issuer>
      secretName: "onboard-uat-letsencrypt-prod"
  rules:
    - host: "onboard-uat.lctafrica.net"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: "onboard-portal-service"
                port:
                  number: 3000
  ingressClassName: nginx
