apiVersion: apps/v1
kind: Deployment
metadata:
  name: "{{ .Values.client }}-portal-deployment"
  namespace: frontend
  labels:
    app: "{{ .Values.client }}-portal"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: "{{ .Values.client }}-portal"
  template:
    metadata:
      labels:
        app: "{{ .Values.client }}-portal"
    spec:
      containers:
        - name: "{{ .Values.client }}-portal"
          image: "registry.digitalocean.com/lct-registry/{{ .Values.client }}-portal@{{ .Values.tag }}"
          ports:
            - containerPort: 80

---
apiVersion: v1
kind: Service
metadata:
  name: "{{ .Values.client }}-portal-service"
  namespace: frontend
spec:
  selector:
    app: "{{ .Values.client }}-portal"
  ports:
    # Each pod has it's own IP, so port conflicts are not expected.
    - port: 3000
      targetPort: 80
  type: ClusterIP
