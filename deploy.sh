#! /bin/bash

# Builds and pushes the Docker image for a client portal 
# and then upgrades the Helm chart to deploy it to the Kubernetes cluster.
# Requires docker, kubectl and helm to be installed.
# Usage example:
# ./$0 provider
# Use -d to deploy to dev instead of uat

set -e # Exit immediately if a command exits with a non-zero status.
set -u # Treat unset variables as an error when substituting.
set -o pipefail # Return value of a pipeline is the status of the last command to exit with a non-zero status, or zero if no command exited with a non-zero status.
set -o nounset # Treat unset variables as an error when substituting.
set -o errexit # Exit immediately if a command exits with a non-zero status.

KUBE_NAMESPACE="frontend"
KUBE_CONTEXT="do-nyc1-lct-k8-uat"
ENV="uat"

## Parse options ##

LONGOPTS="dev,help"
OPTIONS="dh"

# -regarding ! and PIPESTATUS see above
# -temporarily store output to be able to check for errors
PARSED=$(getopt --options=$OPTIONS --longoptions=$LONGOPTS --name "$0" -- "$@")
if [[ ${PIPESTATUS[0]} -ne 0 ]]; then
    # Wrong arguments
    exit 2
fi

# read getopt’s output
eval set -- "$PARSED"

d=n h=n
while true; do
    case "$1" in
        -d|--dev)
            d=y
            shift
            ;;
        -h|--help)
            h=y
            shift
            ;;
        --)
            shift
            break
            ;;
        *)
            echo "Programming error"
            exit 3
            ;;
    esac
done

## End parse options ##

usage () {
    cat << END
Builds and pushes the Docker image for a client portal and then upgrades the Helm chart to deploy it to the Kubernetes cluster.
Requires docker, kubectl and helm to be installed.

    Usage: $0 [client] [-d]

    Example:
    $0 provider

    Options:
    -h      Show this help message
    -d      Deploy to dev instead of uat

    Arguments:
    client  One of: provider, admin, payer, onboard
END
}

if [[ $h == y ]]; then
    usage
    exit 0
fi

if [[ $d == y ]]; then
    KUBE_CONTEXT="do-nyc1-lct-k8-dev"
    ENV="dev"
fi

CLIENT=${1:-"provider"} # One of: provider, admin, payer

ENV=${2:-$ENV} # One of: dev, uat
TIMESTAMP=$(date -u +%Y%m%dT%H%M)
IMAGE="registry.digitalocean.com/lct-registry/$CLIENT-portal"

echo "Deploying $CLIENT-$ENV to $KUBE_CONTEXT:$KUBE_NAMESPACE"
read -r -p "Are you sure? [y/N] " response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]
    then
        echo "Deploying..."
    else
        exit 0
fi

set -x # Print commands and their arguments as they are executed.

# Build and tag the Docker image.
docker build --build-arg client="$CLIENT" -t "$IMAGE:$TIMESTAMP" -t "$IMAGE:latest" .

# Push the Docker image to the registry.
docker push "$IMAGE:$TIMESTAMP"
docker push "$IMAGE:latest"

IMAGE_AND_HASH=$(docker inspect --format='{{index .RepoDigests 0}}' "$IMAGE:$TIMESTAMP")
SHA=$(sed -e 's/^.*@//' <<< "$IMAGE_AND_HASH")

# Set the Kubernetes context and namespace.
kubectl config use-context "$KUBE_CONTEXT"
kubectl config set-context --current --namespace="$KUBE_NAMESPACE"

# Deploy the Docker image to the Kubernetes cluster.
helm upgrade "$CLIENT-$ENV" --install --set client="$CLIENT" --set env="$ENV" --set tag="$SHA" chart/
