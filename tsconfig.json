{
  "$schema": "https://json.schemastore.org/tsconfig",
  "compilerOptions": {
    /* Bundler mode - https://github.com/tsconfig/bases/blob/main/bases/vite-react.json*/
    "target": "ESNext",
    "module": "ESNext",
    "lib": ["ESNext", "DOM", "DOM.Iterable"],
    "moduleResolution": "bundler",
    "esModuleInterop": false,
    "allowSyntheticDefaultImports": true,
    "useDefineForClassFields": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "react-jsx",
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    /* Misc */
    "incremental": true,
    "allowJs": true,
    "types": ["vite/client", "vitest/globals"],
    /* Strict */
    "strict": true,
    // "checkJs": true,
    /* Linting */
    "allowUnusedLabels": false,
    "allowUnreachableCode": false,
    "exactOptionalPropertyTypes": true,
    // "noFallthroughCasesInSwitch": true,
    "noImplicitOverride": true,
    "noImplicitReturns": true,
    "noPropertyAccessFromIndexSignature": true,
    "noUncheckedIndexedAccess": true
    /* Linting - use eslint validation */
    // "noUnusedLocals": true,
    // "noUnusedParameters": true
  },
  "include": [], // Exclude all files, type check project references only
  "references": [
    {
      "path": "./src/payer"
    },
    {
      "path": "./src/lib"
    }
  ]
}
