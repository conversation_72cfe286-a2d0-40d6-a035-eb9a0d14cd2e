/* eslint-disable no-console */

/**
 * This file is used to run vite and tsc programmatically.
 *
 * ```sh
 * # Run the dev server with node
 * node main.js dev <client>
 *
 * # Run the dev server using npm scripts
 * npm run dev -- <client>
 * ```
 */

import { spawn } from "child_process";
import { pathToFileURL } from "url";
import { build, createServer, preview } from "vite";

if (import.meta.url === pathToFileURL(process.argv[1]).href) {
  // module was not imported but called directly

  const client = "payer";
  const port = 3001;

  process.env.VITE_CLIENT = client;
  process.env.VITE_PORT = port?.toString();

  switch (process.argv[2]) {
    case "dev":
      runServer();
      break;
    case "build":
      runBuild();
      break;
    case "build:staging":
      runBuild(true);
      break;
    case "preview":
      runPreview();
      break;
    case "check":
      runTypeCheck(client);
      break;
  }
}

async function runServer() {
  // any valid user config options, plus `mode` and `configFile`
  const server = await createServer({
    configFile: undefined, // Automatically load vite.config.js
  });
  await server.listen();

  server.printUrls();
}

async function runBuild(staging = false) {
  await build({
    configFile: undefined,
    mode: staging ? "staging" : "production",
  });
}

async function runPreview() {
  const previewServer = await preview({
    configFile: undefined,
  });

  previewServer.printUrls();
}

/**
 * Type-check a client. Similar to `npx tsc --noEmit -p ./src/payer/`.
 * TODO: Switch to programmatic ts runner - see https://github.com/Microsoft/TypeScript/wiki/Using-the-Compiler-API
 * @param {string} client
 * @param {string[]} fileNames
 * @param {ts.CompilerOptions} options
 */
function runTypeCheck(client) {
  const rootDir = `./src/${client}`;
  const command = ["npx", "tsc", "--noEmit", "-p", rootDir];
  spawn(command[0], command.slice(1), { stdio: "inherit" });
}
