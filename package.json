{"name": "lctafrica", "version": "0.2.0", "private": true, "type": "module", "scripts": {"dev": "node main.js dev", "build": "node main.js build", "build:staging": "node main.js build:staging", "preview": "node main.js preview", "check": "node main.js check", "sync": "tsc --emitDeclarationOnly -p ./src/lib/", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "test": "vitest", "test:once": "vitest run --coverage=false ", "test:ui": "vitest --ui", "coverage": "vitest run --coverage", "prepare": "husky"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@headlessui/react": "^1.7.15", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^5.0.1", "@popperjs/core": "^2.11.8", "@radix-ui/react-avatar": "^1.1.10", "@reduxjs/toolkit": "^2.2.7", "axios": "^1.7.7", "currency.js": "^2.0.4", "date-fns": "^4.1.0", "immer": "^10.0.2", "keycloak-js": "^15.0.1", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.34", "react": "^18.2.0", "react-calendar": "^5.1.0", "react-country-region-selector": "^3.6.1", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.11", "react-flatpickr": "^3.10.13", "react-hook-form": "^7.45.0", "react-loader-spinner": "^5.3.4", "react-paginate": "^8.1.3", "react-popper": "^2.3.0", "react-redux": "^9.1.2", "react-router-dom": "^6.2.1", "react-select": "^5.4.0", "react-select-async-paginate": "^0.6.2", "react-toastify": "^8.2.0", "react-tooltip": "^4.2.21", "react-transition-group": "^4.4.5", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "styled-components": "^6.1.8", "sweetalert2": "^11.4.37", "tailwind-merge": "^3.0.2", "uuid": "^11.0.3", "web-vitals": "^0.2.4", "xlsx": "^0.18.5", "zod": "^3.22.4"}, "devDependencies": {"@faker-js/faker": "^8.2.0", "@tailwindcss/forms": "^0.5.6", "@tailwindcss/line-clamp": "^0.4.4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/lodash": "^4.17.13", "@types/node": "^18.16.1", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-flatpickr": "^3.8.8", "@types/react-redux": "^7.1.33", "@types/react-router-dom": "^5.3.3", "@types/redux": "^3.6.0", "@types/redux-thunk": "^2.1.0", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "@vitejs/plugin-react-swc": "^3.7.2", "@vitest/coverage-istanbul": "^3.0.2", "@vitest/coverage-v8": "^3.0.2", "@vitest/ui": "^3.0.2", "autoprefixer": "^10.4.15", "copy-webpack-plugin": "^6.4.1", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "husky": "^9.1.6", "jsdom": "^25.0.1", "lint-staged": "^15.2.10", "msw": "^2.0.11", "postcss": "^8.4.29", "postcss-import": "^15.1.0", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.5.9", "react-error-overlay": "6.0.9", "sass": "^1.66.1", "tailwindcss": "^3.3.3", "typescript": "^5.2.2", "vite": "^6.0.10", "vitest": "^3.0.2"}, "msw": {"workerDirectory": "public"}, "resolutions": {"styled-components": "^5"}}