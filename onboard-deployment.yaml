apiVersion: apps/v1
kind: Deployment
metadata:
  name: onboard-portal-deployment
  labels:
    app: onboard-portal
spec:
  replicas: 1
  selector:
    matchLabels:
      app: onboard-portal
  template:
    metadata:
      labels:
        app: onboard-portal
    spec:
      containers:
        - name: onboard-portal
          image: europe-west3-docker.pkg.dev/lctafrica/lct-v2/onboard-portal:202425101642
          ports:
            - containerPort: 3000

---
apiVersion: v1
kind: Service
metadata:
  name: onboard-portal-service
spec:
  selector:
    app: onboard-portal
  ports:
    - port: 3000
      targetPort: 80
  type: ClusterIP
