## Getting started

```sh
# Config local git repo
git config user.name "<PERSON>"
git config user.email "<EMAIL>"

# Set up node - nvm, rtx, asdf, etc
rtx install nodejs@18.16.1
rtx local nodejs@18.16.1

# Install node modules
npm install

# Start dev server for the provider portal
npm run dev -- provider

# Build for staging
npm run build:staging -- provider

# Build for production
npm run build -- provider

# Update type definitions for shared src/lib (also type checks src/lib)
npm run sync

# Type-check client
npm run check -- provider
```

> **Warning**
> Do not add secrets into `.env` files (except `.env*.local` files)

## Deploy

Requires: docker, doctl, kubectl, helm, kubens, kubectx

```sh
# Set up
doctl registry login
doctl kubernetes cluster kubeconfig save lct-k8-uat

# Switch context/namespace
kubectx do-nyc1-lct-k8-uat
kubens frontend

# Build image
TIMESTAMP=$(date -u +%Y%m%dT%H%M)
docker build --build-arg client=provider \
  -t registry.digitalocean.com/lct-registry/provider-portal:$TIMESTAMP \
  -t registry.digitalocean.com/lct-registry/provider-portal:latest \
  .

# Push image
docker push registry.digitalocean.com/lct-registry/provider-portal:$TIMESTAMP

# Upgrade app
helm upgrade provider-uat --install --set client=provider --set env=dev --set tag=latest chart/
```

## Directory structure

```
src
  provider
  admin
  payer
  lib
    utils
    api
    components
    service-fields
```

## TODO

- [ ] add unit tests - partially done
- [ ] add e2e test with playwright
- [ ] add error logging/session replays
- [ ] serialize/deserialize with zod - partially done
- [ ] extract inline svg to separate files
- [ ] create storybook/ladle for shared components
- [x] add react error boundary
- [ ] set up bitbucket ci/cd

## Running tests

```sh
# Run all tests
npm run test

# Run all tests in a specific project
npm run test:payer
npm run test:provider
npm run test:admin
npm run test:onboard

# Run all tests in a specific project with UI
npm run test:payer:ui
npm run test:provider:ui
npm run test:admin:ui
npm run test:onboard:ui
```

## Resources

- [keycloak-js docs](https://keycloak.org/docs/latest/securing_apps/index.html#_javascript_adapter)
- [tsconfig-bases](https://github.com/tsconfig/bases/blob/main/bases/)

## Notes

- Imports auto-suggest may not work properly when importing from a referenced project (i.e. ~lib). See [microsoft/TypeScript#39778](https://github.com/microsoft/TypeScript/issues/39778).
